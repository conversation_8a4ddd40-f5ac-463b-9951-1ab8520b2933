#!/usr/bin/python3

import sys
import re
import os
import requests
import json
from git import Repo

regex = "^[\w!#\$%&'+\-=?`{|}~^]+(?:\.[\w!#\$%&'+\-=?`{|}~^]+)*@infoblox.com$"

def check_author_email(auth_token, user_name):
    
    headers = {
        "Authorization": "Bearer "+auth_token,
        "Accept": "application/vnd.github+json"
    }
    
    try:
        url = 'https://api.github.com/users/'+user_name
        response = requests.request("GET", url,  headers=headers)
        if response.status_code == 200:
            jd = response.json()
            primary_email = jd["email"]
            if primary_email is None:
                print("Please set your infoblox email as your Primary email ID. Follow the path: GitHub UI -> Settings -> Emails -> Add Primary email address")
                print("#Note: If the option 'Keep my email addresses private' is checked in, Make sure to Uncheck it. Follow the path: GitHub UI -> Settings -> Emails -> undo 'Keep my email addresses private'")
                print("And please update this primary email in your Public Profile and Re-run your Actions with 'Enable debug logging'.")
                sys.exit(1)
            else:
                print(primary_email)

                filepath = os.path.realpath("../../nios/nios")
                repo = Repo(filepath, search_parent_directories=True)

                commits_list = list(repo.iter_commits(max_count=1))
                commit = commits_list[0]

                # This is to get the Author email from CLI 'git config --global user.email'
                config_email = commit.author.email.strip()

                # This check is to match if both the Author and Primary Email ID's same or not
                # And print a Warning message if both doesn't match
                if not config_email == primary_email:
                    print("#Warning: The Email ID of Author(In git config CLI) '%s' is not matching with the Primary Email ID in GitHub UI '%s'.Both should be same and with domain name '<>@infoblox.com'." % (config_email, primary_email))

                if not (re.fullmatch(regex, primary_email)):
                    print("Primary Email ID format should be of '[a-zA-Z0-9.]*@infoblox.com' with domain name as '@infoblox.com' but the given Email is '%s'." % primary_email)
                    sys.exit(1)

                else:
                    print("VALID INFOBLOX EMAIL ADDRESS OF THE COMMITTER")
                    sys.exit(0)
        else:
            print(f"Error {response.status_code}: {response.text}")
            return False

    except json.decoder.JSONDecodeError as e:
        print(f"Error while decoding json: {e}") 


if __name__ == '__main__':
    if len(sys.argv) < 3:
        print("Usage: title_check <Auth Token> <User Name>")
        sys.exit(1)

    # This is to get the Primary email from GitHub UI
    auth_token = sys.argv[1].strip()
    user_name = sys.argv[2].strip()
    check_author_email(auth_token, user_name)
