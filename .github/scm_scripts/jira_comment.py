#!/usr/bin/python3

import sys
import subprocess
import requests

JIRA_URL_RW = 'https://infoblox.atlassian.net/rest/api/2/issue/'

def jira_comment(ticket_id, auth_token, user, event, branch, repo, merge_commit_id, pr):

    headers = {
        "Authorization": "Basic "+auth_token,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    try:
        url = JIRA_URL_RW+ticket_id+"/comment"
        if event == 'creating PR':
            data = '{"body":"user: '+user+' completed '+event+' to the branch ['+branch+'] on repo: '+repo+' using PR :'+pr+'"}'
        else:
            try:
                git_rev_list = subprocess.check_output("git rev-list --count %s" % branch, shell=True)
                git_rev_list = git_rev_list.decode().strip()
            except subprocess.CalledProcessError as e:
                print(f"Error running git_rev_list command: {e}")
                return False
            data = '{"body":"user: '+user+' completed '+event+' to the branch ['+branch+'] on repo: '+repo+' with merge commit hash ['+merge_commit_id+'] and rev-list count ['+git_rev_list+'] using PR :'+pr+'"}'
        #data = '{"body":"user: Testing inserting a comment into JIRA"}'
        response = requests.post(url, headers=headers, data=data)
        if response.status_code in [200, 201, 202]:
            print("Posted comment onto JIRA")
            return True
        else:
            print("Failed to post comment: ", response.text)
            return False
    except Exception as e:
        print("JIRA Exception occurred ", str(e))
        return False

if __name__ == '__main__':
    if len(sys.argv) < 9:
        print("Usage: jira_comment <JIRA TICKET ID> <JIRA TOKEN> <User> <Event> <Branch> <Repo> <MERGE COMMIT ID> <PR Number>")
        sys.exit(1)

    ticket = sys.argv[1]
    jira_token = sys.argv[2]
    user = sys.argv[3]
    event = sys.argv[4]
    branch = sys.argv[5]
    repo = sys.argv[6]
    merge_commit_id = sys.argv[7]
    pr = sys.argv[8]

    ret = False
    ret = jira_comment(ticket, jira_token, user, event, branch, repo, merge_commit_id, pr)

    if ret:
        sys.exit(0)
    else:
        sys.exit(1)
