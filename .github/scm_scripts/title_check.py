#!/usr/bin/python3

import sys
import string
import re

# Function to check for non-UTF-8 characters
def check_non_utf8_characters(text):
    try:
        text.encode('utf-8')
    except UnicodeEncodeError:
        print("Commit title contains non-UTF-8 characters.")
        sys.exit(1)

# Function to check for non-printable ASCII characters
def check_printable_ascii(text):
    if not all(char in string.printable for char in text):
        print(f"Commit title contains non-printable ASCII characters: {text}")
        sys.exit(1)

def check_title(title):
    # Trim leading and trailing spaces
    title = title.strip()

    # Check for non-UTF-8 characters
    check_non_utf8_characters(title)

    # Check for non-printable ASCII characters
    check_printable_ascii(title)

    new_title = ''

    # Regex pattern for Revert titles with optional quotes and additional content
    r = r'^Revert\s+(?:"?([^"]+)"?)\s*(\(#\d+\))?(\s+#\d+)?$'
    match = re.match(r, title)
    if match:
        new_title = match.group(1)
    else:
        new_title = title

    branch = ''

    r = "^Merge .+ into .+$"
    if re.match(r, new_title):
        # Handle merge commit messages
        branch = new_title.split()[-1]
    elif new_title.startswith("Merge pull request"):
        # Handle pull request merge messages
        branch = new_title.split()[-1][13:]
    else:
        # Check Commit Title Format
        ts = new_title.split(':')
        if len(ts) < 2:
            print("Commit Title Message format: <ticket-id>:<short description>")
            sys.exit(1)
        else:
            branch = ts[0].strip()

    if branch.startswith('user'):
        head = branch.split('/')[-1]
    else:
        if len(branch.split('/')) == 2:
            head = branch.split('/')[1]
        else:
            head = branch

    # Check JIRA and Version One
    cats = head.split('-')
    if len(cats) != 2:
        print("INVALID TICKET ID: ", head, "Ticket ID should be of the format 'ABC-12345'")
        sys.exit(1)

    print(head)
    sys.exit(0)

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("Usage: title_check <Commit Title>")
        sys.exit(1)

    title = ' '.join(sys.argv[1:])
    if "Merge" in title:
        title = title.split('\\n')[0]

    check_title(title)

