import requests
import sys
import re


regex = "^((develop)\/([0-9]\.[0-9]))|((release)\/([0-9]\.[0-9]\.[0-9]))|((bugfix|hotfix|project|feature)\/([.a-zA-Z0-9_-]+))|((user)\/([.a-zA-Z0-9_-]+)\/([.a-zA-Z0-9_-]+))$"

def check_branch_name(branch, email):
    # for example revert-1234-bugfix/BLDTLS-123
    if 'revert' in branch:
        branch =  branch[7:]       # 1234-bugfix/BLDTLS-123
        index = branch.index("-")  
        branch = branch[index+1:]  # bugfix/BLDTLS-123

    elif not (re.fullmatch(regex, branch)):
        print("There is something wrong with your branch name. Branch names should follow proper NAMING CONVENTIONS(case sensitive) such as: ['develop/**','release/**','bugfix/**','hotfix/**','user/**/**','project/**','feature/**']. You should rename your branch to a valid name and try again.")
        sys.exit(1)
    
    else:
        if 'user' in branch:
            user_name = branch.split('/')[1]
            email_name = email.split('@')[0]
            if not user_name == email_name:
                print("User name in your user branch is not matching with username of your email id, for example your user branch should be: user/%s/**" % email_name)
                sys.exit(1)


if __name__ == '__main__':
    if len(sys.argv) < 3:
        print("Usage: branch_name_check <Branch Name> <Primary Email>")
        sys.exit(1)

    # branch will start with "refs/heads/bugfix/**" so removed "refs/heads/"
    branch = sys.argv[1].strip()[11:]
    email = sys.argv[2].strip()
    check_branch_name(branch, email)
