#!/usr/bin/python3

import sys
import requests
import re

JIRA_URL_RO = 'https://infoblox.atlassian.net/rest/api/3/issue/'

def jira(ticket_id, auth_token, branch):

    headers = {
        "Authorization": "Basic "+auth_token,
        "Accept": "application/json"
    }

    try:
        blist = branch.split('/')
        base_branch = blist[0]

        if base_branch != 'develop' and base_branch != 'release':
            print("Target branch format is not matching with develop or release so skipping the jira fix version check:", branch)
            # Ture because PR should not fail for other branches which are allowed for PR Creation
            return True

        gitbranch_tree = blist[0]
        gitbranch_version = blist[1]
        
        url = JIRA_URL_RO+ticket_id
        response = requests.request("GET", url, headers=headers)
        if response.status_code == 200:
            jd = response.json()
            fix_version = ""
            issuetype = jd['fields']['issuetype']['name']
            if issuetype == 'Bug':
                ownfixVersion = jd['fields']['customfield_11315']
                if ownfixVersion != None:
                    if len(ownfixVersion) == 1:
                        fix_version = ownfixVersion[0]
                    elif len(ownfixVersion) > 1:
                        print("JIRA OwnFixVersion should contain only one Fixversion per Jira ticket")
                        return False
            if issuetype != 'Bug' or ownfixVersion == None:
                fixVersions = [d['name'] for d in jd['fields']['fixVersions']]
                if len(fixVersions) == 0:
                    print("JIRA FixVersion should contain atleast one Fixversions per Jira ticket")
                    return False
                else:
                    r = "^(NIOS.[0-9]\.[0-9]\.[0-9]|[0-9]\.[0-9]\.[0-9]|CHF.[0-9]\.[0-9]\.[0-9]\.[0-9])$"
                    found_version = None
                    for version in fixVersions:
                        if re.match(r, version) and (gitbranch_version in version):
                            found_version = version
                            break
                    if found_version == None:
                        print("JIRA Ticket %s should contain a valid FixVersion with format NIOS-<VERSION> or <VERSION> or CHF-<VERSION> (Eg: NIOS-9.0.0 or 9.0.0 or CHF-9.0.0.1)" % ticket_id)
                        return False
                    fix_version = found_version 
            
            gbv_list = gitbranch_version.split('.')
            
            print(fix_version)
            # check for fixversions with 'NIOS 8.6.3' or 'NIOS-8.6.3'
            if len(fix_version) == 10:
                fv = fix_version[-5:]
                ofv_list = fv.split('.') 
            # check for fixversions with CHF-8.6.3.2
            elif 'CHF' in fix_version:
                fv = fix_version.split('-')
                fv = fv[1][:-2]
                ofv_list = fv.split('.')
            else:
                ofv_list = fix_version.split('.')

            match_till = 0
                
            if gitbranch_tree == 'develop':
                match_till = 2
            elif gitbranch_tree == 'release':
                match_till = 3
            for i in range(match_till):
                if gbv_list[i] != ofv_list[i]:
                    print("Jira OwnFixVersion/FixVersion :", fix_version, "does not match with Github branch  Version :", branch)
                    return False
            return True
        else:
            print("JIRA error occurred: ", response.text)
            return False
    except Exception as e:
        print("JIRA Exception occurred ", str(e))
        return False
    return False


if __name__ == '__main__':
    if len(sys.argv) < 4:
        print("Usage: jira_fix_version <JIRA TICKET ID> <JIRA TOKEN> <Branch>")
        sys.exit(1)

    ticket = sys.argv[1]
    jira_token = sys.argv[2]
    branch = sys.argv[3]

    ret = False
    ret = jira(ticket, jira_token, branch)

    if ret:
        sys.exit(0)
    else:
        sys.exit(1)
