#!/usr/bin/python3

import sys

def check_desc(mesg):
    # Check if the PR description can be encoded in UTF-8
    try:
        mesg.encode('utf-8')
    except UnicodeEncodeError:
        print("PR Description contains invalid UTF-8 characters")
        sys.exit(1)

    print(mesg)
    sys.exit(0)


if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("Usage: pr_desc_check <PR Description>")
        sys.exit(1)

    desc = ' '.join(sys.argv[1:])
    check_desc(desc)
