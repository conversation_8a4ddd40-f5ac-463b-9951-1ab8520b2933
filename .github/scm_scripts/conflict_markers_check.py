import requests
import sys
import re
import base64


def check_conflict_markers(auth_token, commit_id):

    headers = {
        "Authorization": "Bearer " + auth_token,
        "Accept": "application/vnd.github+json",
    }

    regex = r"<{7} HEAD(?:(?!={7})[\s\S])*={7}(?:(?!>{7} \w+)[\s\S])*>{7} \w+"

    try:
        url = "https://api.github.com/repos/Infoblox-CTO/nios/commits/" + commit_id
        response = requests.request("GET", url, headers=headers)
        if response.status_code == 200:
            jd = response.json()
            files = jd.get("files")

            file_list = [(x.get("filename"), x.get("contents_url")) for x in files if (x.get("status"))!="removed"]
            
            try:
                for filename, file_url in file_list:
                    response = requests.request("GET", file_url, headers=headers)
                    if response.status_code == 200:
                        file_contents = response.json()["content"]
                        decoded_file_contents = base64.b64decode(file_contents).decode("utf-8")
                        conflict_markers = re.findall(regex, decoded_file_contents, re.DOTALL)
                        for conflict in conflict_markers:
                            print("Commit file '%s' contains conflict markers, Please check for conflict markers in all the committed files and resolve them. This is the block of conflict \n " % filename, conflict, sep=("\n"))
                            print("")
                            sys.exit(1)
                    else:
                        raise Exception("Failed to retrieve file contents, status code: {}".format(response.status_code))
            except Exception as e:
                print("Error: {}".format(e))

    except Exception as e:
        print(f"An error occurred while getting the commit files: {e}")


if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: check_conflict <Auth Token> <Commit ID>")
        sys.exit(1)

    auth_token = sys.argv[1].strip()
    commit_id = sys.argv[2].strip()
    check_conflict_markers(auth_token, commit_id)
