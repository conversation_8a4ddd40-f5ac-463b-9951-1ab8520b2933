name: PR Close Actions
on:
   pull_request:
       types: [closed]
       branches: ['develop/**', 'release/**', 'feature/**', 'hotfix/**',  'project/**', 'bugfix/**', 'user/**/**']      

jobs:
  post-processing-flow:
    if: github.event.pull_request.merged == true
    name: "PR merge processing"
    runs-on: self-hosted
    steps:
    - name: Check out code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Set up python
      uses: actions/setup-python@v4
      with:
         python-version: '3.8.10'
    - name: Install python dependencies
      run: |
        pip install requests
    - name: PR Description check
      run: python .github/scm_scripts/pr_desc_check.py ${{ toJSON(github.event.pull_request.body) }}
    - name: Fetch Commit Title
      run: |
        export TICKET_ID=$(python .github/scm_scripts/title_check.py ${{ toJSON(github.event.pull_request.title) }})
        echo "TICKET_ID=$TICKET_ID" >> $GITHUB_ENV
    - name: JIRA Comment
      env:
        JIRA_TOKEN: ${{ secrets.JIRA_TOKEN }}
      if: success() && (!startsWith(env.TICKET_ID, 'TK-') && !startsWith(env.TICKET_ID, 'B-'))
      run: |
        echo "JIRA TICKE ID = ${{ env.TICKET_ID }}"
        python .github/scm_scripts/jira_comment.py "${{ env.TICKET_ID }}" "${{ env.JIRA_TOKEN }}" "${{ github.event.pull_request.user.login }}" "merging PR" "${{ github.event.pull_request.base.ref }}" "${{ github.event.repository.full_name }}" "${{ github.sha }}" "${{ github.event.pull_request.html_url }}"
