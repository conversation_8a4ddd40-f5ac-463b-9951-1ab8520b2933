# .github/workflows/logger.yaml
name: Code Commit PreProcessing
on:
  push:
    branches-ignore:
      - main

jobs:
  github-preprocessing:
    name: "Code Commit Compliance Check"
    runs-on: self-hosted
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Set up python
        uses: actions/setup-python@v4
        with:
           python-version: '3.8.10'
      - name: Install python dependencies
        run: |
          pip install requests
          pip3 install GitPython
      - name: User email check
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
        run: |
          python .github/scm_scripts/committer_email_check.py '${{ env.GITHUB_TOKEN }}' '${{ github.actor }}' | tee >(grep -m 1 -E "@infoblox.com" > output.txt)
          echo "EMAIL_ID=$(cat output.txt)" >> $GITHUB_ENV
      - name: Branch Name check
        run: |
          python .github/scm_scripts/branch_name_check.py "${{ github.ref }}" "${{ env.EMAIL_ID }}"
      - name: Commit Title Check
        run: |
          export TICKET_ID=$(python .github/scm_scripts/title_check.py ${{ toJSON(github.event.head_commit.message) }})
          echo "TICKET_ID=$TICKET_ID" >> $GITHUB_ENV
      - name: Conflict Markers Check
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          SHA: ${{ github.sha }}
        run: python .github/scm_scripts/conflict_markers_check.py '${{ env.GITHUB_TOKEN }}' '${{ env.SHA }}'
      - name: Version1 Check
        env:
          V1_TOKEN: ${{ secrets.V1_TOKEN }}
        if: success() && (startsWith( env.TICKET_ID, 'TK-') || startsWith(env.TICKET_ID, 'B-'))
        run: |
          echo "V1 TICKE ID = ${{ env.TICKET_ID }}"
          python .github/scm_scripts/version_one_check.py "${{ env.TICKET_ID }}" "${{ env.V1_TOKEN }}"
      - name: JIRA Check
        env:
          JIRA_TOKEN: ${{ secrets.JIRA_TOKEN }}
        if: success() && (!startsWith(env.TICKET_ID, 'TK-') && !startsWith(env.TICKET_ID, 'B-'))
        run: |
          echo "JIRA TICKE ID = ${{ env.TICKET_ID }}"
          python .github/scm_scripts/jira_check.py "${{ env.TICKET_ID }}" "${{ env.JIRA_TOKEN }}"
