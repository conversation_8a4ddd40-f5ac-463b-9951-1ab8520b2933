// Inspired by examples in chapter 3 of the BIND ARM

// Allow queries from local net only
acl "infobloxnet" { 10.32.0.0/24; };
acl "loopbacknet" { *********/24; };

options {
	directory "/etc/namedb";	// Working directory
	pid-file "named.pid";		// Put pid file in working dir
//	allow-query { "infobloxnet"; "loopbacknet"; };
	recursion no;			// Do not provide recursive service
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

// Root server hints
zone "." { type hint; file "root.hint"; };

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	file "localhost.rev";
	notify no;
};

zone "test.infoblox.com" {
	type master;
	file "test.infoblox.com.db";
	allow-update { "infobloxnet"; "loopbacknet"; };
};

zone "1.168.192.in-addr.arpa" {
	type master;
	file "1.168.192.in-addr.arpa";
	allow-update { "infobloxnet"; "loopbacknet"; };
};

zone "b0003.grainger.com" {
	type master;
// Don't notify; the NS records are probably bogus for this test zone
	notify no;
	file "b0003.grainger.com";
};

// zone "bigzone.test.infoblox.com" {
//	type master;
//	notify no;
//	file "bigzone";
// };
