<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000-2012, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>nsupdate</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.nsupdate"></a><div class="titlepage"></div>
  
  

  
  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">nsupdate</span>
     &#8212; Dynamic DNS update utility
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">nsupdate</code> 
       [<code class="option">-d</code>]
       [<code class="option">-D</code>]
       [<code class="option">-i</code>]
       [<code class="option">-L <em class="replaceable"><code>level</code></em></code>]
       [
	[<code class="option">-g</code>]
	 |  [<code class="option">-o</code>]
	 |  [<code class="option">-l</code>]
	 |  [<code class="option">-y <em class="replaceable"><code>[<span class="optional">hmac:</span>]keyname:secret</code></em></code>]
	 |  [<code class="option">-k <em class="replaceable"><code>keyfile</code></em></code>]
      ]
       [<code class="option">-t <em class="replaceable"><code>timeout</code></em></code>]
       [<code class="option">-u <em class="replaceable"><code>udptimeout</code></em></code>]
       [<code class="option">-r <em class="replaceable"><code>udpretries</code></em></code>]
       [<code class="option">-R <em class="replaceable"><code>randomdev</code></em></code>]
       [<code class="option">-v</code>]
       [<code class="option">-T</code>]
       [<code class="option">-P</code>]
       [<code class="option">-V</code>]
       [filename]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>nsupdate</strong></span>
      is used to submit Dynamic DNS Update requests as defined in RFC 2136
      to a name server.
      This allows resource records to be added or removed from a zone
      without manually editing the zone file.
      A single update request can contain requests to add or remove more than
      one
      resource record.
    </p>
    <p>
      Zones that are under dynamic control via
      <span class="command"><strong>nsupdate</strong></span>
      or a DHCP server should not be edited by hand.
      Manual edits could
      conflict with dynamic updates and cause data to be lost.
    </p>
    <p>
      The resource records that are dynamically added or removed with
      <span class="command"><strong>nsupdate</strong></span>
      have to be in the same zone.
      Requests are sent to the zone's master server.
      This is identified by the MNAME field of the zone's SOA record.
    </p>
    <p>
      Transaction signatures can be used to authenticate the Dynamic
      DNS updates.  These use the TSIG resource record type described
      in RFC 2845 or the SIG(0) record described in RFC 2535 and
      RFC 2931 or GSS-TSIG as described in RFC 3645.
    </p>
    <p>
      TSIG relies on
      a shared secret that should only be known to
      <span class="command"><strong>nsupdate</strong></span> and the name server.
      For instance, suitable <span class="type">key</span> and
      <span class="type">server</span> statements would be added to
      <code class="filename">/etc/named.conf</code> so that the name server
      can associate the appropriate secret key and algorithm with
      the IP address of the client application that will be using
      TSIG authentication. You can use <span class="command"><strong>ddns-confgen</strong></span>
      to generate suitable configuration fragments.
      <span class="command"><strong>nsupdate</strong></span>
      uses the <code class="option">-y</code> or <code class="option">-k</code> options
      to provide the TSIG shared secret.  These options are mutually exclusive.
    </p>
    <p>
      SIG(0) uses public key cryptography.
      To use a SIG(0) key, the public key must be stored in a KEY
      record in a zone served by the name server.
    </p>
    <p>
      GSS-TSIG uses Kerberos credentials.  Standard GSS-TSIG mode
      is switched on with the <code class="option">-g</code> flag.  A
      non-standards-compliant variant of GSS-TSIG used by Windows
      2000 can be switched on with the <code class="option">-o</code> flag.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-d</span></dt>
<dd>
	  <p>
	    Debug mode. This provides tracing information about the
	    update requests that are made and the replies received
	    from the name server.
	  </p>
	</dd>
<dt><span class="term">-D</span></dt>
<dd>
	  <p>
	    Extra debug mode.
	  </p>
	</dd>
<dt><span class="term">-i</span></dt>
<dd>
	  <p>
	    Force interactive mode, even when standard input is not a terminal.
	  </p>
	</dd>
<dt><span class="term">-k <em class="replaceable"><code>keyfile</code></em></span></dt>
<dd>
	  <p>
	    The file containing the TSIG authentication key.
	    Keyfiles may be in two formats: a single file containing
	    a <code class="filename">named.conf</code>-format <span class="command"><strong>key</strong></span>
	    statement, which may be generated automatically by
	    <span class="command"><strong>ddns-confgen</strong></span>, or a pair of files whose names are
	    of the format <code class="filename">K{name}.+157.+{random}.key</code> and
	    <code class="filename">K{name}.+157.+{random}.private</code>, which can be
	    generated by <span class="command"><strong>dnssec-keygen</strong></span>.
	    The <code class="option">-k</code> may also be used to specify a SIG(0) key used
	    to authenticate Dynamic DNS update requests.  In this case, the key
	    specified is not an HMAC-MD5 key.
	  </p>
	</dd>
<dt><span class="term">-l</span></dt>
<dd>
	  <p>
	    Local-host only mode. This sets the server address to
	    localhost (disabling the <span class="command"><strong>server</strong></span> so that the server
	    address cannot be overridden).  Connections to the local server will
	    use a TSIG key found in <code class="filename">/var/run/named/session.key</code>,
	    which is automatically generated by <span class="command"><strong>named</strong></span> if any
	    local master zone has set <span class="command"><strong>update-policy</strong></span> to
	    <span class="command"><strong>local</strong></span>.  The location of this key file can be
	    overridden with the <code class="option">-k</code> option.
	  </p>
	</dd>
<dt><span class="term">-L <em class="replaceable"><code>level</code></em></span></dt>
<dd>
	  <p>
	    Set the logging debug level.  If zero, logging is disabled.
	  </p>
	</dd>
<dt><span class="term">-p <em class="replaceable"><code>port</code></em></span></dt>
<dd>
	  <p>
	    Set the port to use for connections to a name server. The
	    default is 53.
	  </p>
	</dd>
<dt><span class="term">-P</span></dt>
<dd>
	  <p>
	    Print the list of private BIND-specific resource record
	    types whose format is understood
	    by <span class="command"><strong>nsupdate</strong></span>. See also
	    the <code class="option">-T</code> option.
	  </p>
	</dd>
<dt><span class="term">-r <em class="replaceable"><code>udpretries</code></em></span></dt>
<dd>
	  <p>
	    The number of UDP retries. The default is 3. If zero, only
	    one update request will be made.
	  </p>
	</dd>
<dt><span class="term">-R <em class="replaceable"><code>randomdev</code></em></span></dt>
<dd>
	<p>
	  Where to obtain randomness. If the operating system
	  does not provide a <code class="filename">/dev/random</code> or
	  equivalent device, the default source of randomness is keyboard
	  input.  <code class="filename">randomdev</code> specifies the name of
	  a character device or file containing random data to be used
	  instead of the default.  The special value
	  <code class="filename">keyboard</code> indicates that keyboard input
	  should be used.  This option may be specified multiple times.
	</p>
	</dd>
<dt><span class="term">-t <em class="replaceable"><code>timeout</code></em></span></dt>
<dd>
	  <p>
	    The maximum time an update request can take before it is
	    aborted. The default is 300 seconds. Zero can be used to
	    disable the timeout.
	  </p>
	</dd>
<dt><span class="term">-T</span></dt>
<dd>
	  <p>
	    Print the list of IANA standard resource record types
	    whose format is understood by <span class="command"><strong>nsupdate</strong></span>.
	    <span class="command"><strong>nsupdate</strong></span> will exit after the lists are
	    printed. The <code class="option">-T</code> option can be combined
	    with the <code class="option">-P</code> option.
	  </p>
	  <p>
	    Other types can be entered using "TYPEXXXXX" where "XXXXX" is the
	    decimal value of the type with no leading zeros.  The rdata,
	    if present, will be parsed using the UNKNOWN rdata format,
	    (&lt;backslash&gt; &lt;hash&gt; &lt;space&gt; &lt;length&gt;
	    &lt;space&gt; &lt;hexstring&gt;).
	  </p>
	</dd>
<dt><span class="term">-u <em class="replaceable"><code>udptimeout</code></em></span></dt>
<dd>
	  <p>
	    The UDP retry interval. The default is 3 seconds. If zero,
	    the interval will be computed from the timeout interval and
	    number of UDP retries.
	  </p>
	</dd>
<dt><span class="term">-v</span></dt>
<dd>
	  <p>
	    Use TCP even for small update requests.
	    By default, <span class="command"><strong>nsupdate</strong></span>
	    uses UDP to send update requests to the name server unless they are too
	    large to fit in a UDP request in which case TCP will be used.
	    TCP may be preferable when a batch of update requests is made.
	  </p>
	</dd>
<dt><span class="term">-V</span></dt>
<dd>
	  <p>
	    Print the version number and exit.
	  </p>
	</dd>
<dt><span class="term">-y <em class="replaceable"><code>[<span class="optional">hmac:</span>]keyname:secret</code></em></span></dt>
<dd>
	  <p>
	    Literal TSIG authentication key.
	    <em class="parameter"><code>keyname</code></em> is the name of the key, and
	    <em class="parameter"><code>secret</code></em> is the base64 encoded shared secret.
	    <em class="parameter"><code>hmac</code></em> is the name of the key algorithm;
	    valid choices are <code class="literal">hmac-md5</code>,
	    <code class="literal">hmac-sha1</code>, <code class="literal">hmac-sha224</code>,
	    <code class="literal">hmac-sha256</code>, <code class="literal">hmac-sha384</code>, or
	    <code class="literal">hmac-sha512</code>.  If <em class="parameter"><code>hmac</code></em>
	    is not specified, the default is <code class="literal">hmac-md5</code>
	    or if MD5 was disabled <code class="literal">hmac-sha256</code>.
	  </p>
	  <p>
	    NOTE: Use of the <code class="option">-y</code> option is discouraged because the
	    shared secret is supplied as a command line argument in clear text.
	    This may be visible in the output from
	    <span class="citerefentry">
	      <span class="refentrytitle">ps</span>(1)
	    </span>
	    or in a history file maintained by the user's shell.
	  </p>
	</dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>INPUT FORMAT</h2>

    <p><span class="command"><strong>nsupdate</strong></span>
      reads input from
      <em class="parameter"><code>filename</code></em>
      or standard input.
      Each command is supplied on exactly one line of input.
      Some commands are for administrative purposes.
      The others are either update instructions or prerequisite checks on the
      contents of the zone.
      These checks set conditions that some name or set of
      resource records (RRset) either exists or is absent from the zone.
      These conditions must be met if the entire update request is to succeed.
      Updates will be rejected if the tests for the prerequisite conditions
      fail.
    </p>
    <p>
      Every update request consists of zero or more prerequisites
      and zero or more updates.
      This allows a suitably authenticated update request to proceed if some
      specified resource records are present or missing from the zone.
      A blank input line (or the <span class="command"><strong>send</strong></span> command)
      causes the
      accumulated commands to be sent as one Dynamic DNS update request to the
      name server.
    </p>
    <p>
      The command formats and their meaning are as follows:
      </p>
<div class="variablelist"><dl class="variablelist">
<dt><span class="term">
	      <span class="command"><strong>server</strong></span>
	       {servername}
	       [port]
	    </span></dt>
<dd>
	    <p>
	      Sends all dynamic update requests to the name server
	      <em class="parameter"><code>servername</code></em>.
	      When no server statement is provided,
	      <span class="command"><strong>nsupdate</strong></span>
	      will send updates to the master server of the correct zone.
	      The MNAME field of that zone's SOA record will identify the
	      master
	      server for that zone.
	      <em class="parameter"><code>port</code></em>
	      is the port number on
	      <em class="parameter"><code>servername</code></em>
	      where the dynamic update requests get sent.
	      If no port number is specified, the default DNS port number of
	      53 is
	      used.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>local</strong></span>
	       {address}
	       [port]
	    </span></dt>
<dd>
	    <p>
	      Sends all dynamic update requests using the local
	      <em class="parameter"><code>address</code></em>.

	      When no local statement is provided,
	      <span class="command"><strong>nsupdate</strong></span>
	      will send updates using an address and port chosen by the
	      system.
	      <em class="parameter"><code>port</code></em>
	      can additionally be used to make requests come from a specific
	      port.
	      If no port number is specified, the system will assign one.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>zone</strong></span>
	       {zonename}
	    </span></dt>
<dd>
	    <p>
	      Specifies that all updates are to be made to the zone
	      <em class="parameter"><code>zonename</code></em>.
	      If no
	      <em class="parameter"><code>zone</code></em>
	      statement is provided,
	      <span class="command"><strong>nsupdate</strong></span>
	      will attempt determine the correct zone to update based on the
	      rest of the input.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>class</strong></span>
	       {classname}
	    </span></dt>
<dd>
	    <p>
	      Specify the default class.
	      If no <em class="parameter"><code>class</code></em> is specified, the
	      default class is
	      <em class="parameter"><code>IN</code></em>.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>ttl</strong></span>
	       {seconds}
	    </span></dt>
<dd>
	    <p>
	      Specify the default time to live for records to be added.
	      The value <em class="parameter"><code>none</code></em> will clear the default
	      ttl.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>key</strong></span>
	       [hmac:] {keyname}
	       {secret}
	    </span></dt>
<dd>
	    <p>
	      Specifies that all updates are to be TSIG-signed using the
	      <em class="parameter"><code>keyname</code></em> <em class="parameter"><code>secret</code></em> pair.
	      If <em class="parameter"><code>hmac</code></em> is specified, then it sets the
	      signing algorithm in use; the default is
	      <code class="literal">hmac-md5</code> or if MD5 was disabled
	      <code class="literal">hmac-sha256</code>.  The <span class="command"><strong>key</strong></span>
	      command overrides any key specified on the command line via
	      <code class="option">-y</code> or <code class="option">-k</code>.
	    </p>
	  </dd>
<dt><span class="term">
	    <span class="command"><strong>gsstsig</strong></span>
	  </span></dt>
<dd>
	    <p>
	      Use GSS-TSIG to sign the updated.  This is equivalent to
	      specifying <code class="option">-g</code> on the command line.
	    </p>
	  </dd>
<dt><span class="term">
	    <span class="command"><strong>oldgsstsig</strong></span>
	  </span></dt>
<dd>
	    <p>
	      Use the Windows 2000 version of GSS-TSIG to sign the updated.
	      This is equivalent to specifying <code class="option">-o</code> on the
	      command line.
	    </p>
	  </dd>
<dt><span class="term">
	    <span class="command"><strong>realm</strong></span>
	     {[<span class="optional">realm_name</span>]}
	  </span></dt>
<dd>
	    <p>
	      When using GSS-TSIG use <em class="parameter"><code>realm_name</code></em> rather
	      than the default realm in <code class="filename">krb5.conf</code>.  If no
	      realm is specified the saved realm is cleared.
	    </p>
	  </dd>
<dt><span class="term">
	    <span class="command"><strong>check-names</strong></span>
	     {[<span class="optional">yes_or_no</span>]}
	  </span></dt>
<dd>
	    <p>
	      Turn on or off check-names processing on records to
	      be added.  Check-names has no effect on prerequisites
	      or records to be deleted.  By default check-names
	      processing is on.  If check-names processing fails
	      the record will not be added to the UPDATE message.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>[<span class="optional">prereq</span>] nxdomain</strong></span>
	       {domain-name}
	    </span></dt>
<dd>
	    <p>
	      Requires that no resource record of any type exists with name
	      <em class="parameter"><code>domain-name</code></em>.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>[<span class="optional">prereq</span>] yxdomain</strong></span>
	       {domain-name}
	    </span></dt>
<dd>
	    <p>
	      Requires that
	      <em class="parameter"><code>domain-name</code></em>
	      exists (has as at least one resource record, of any type).
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>[<span class="optional">prereq</span>] nxrrset</strong></span>
	       {domain-name}
	       [class]
	       {type}
	    </span></dt>
<dd>
	    <p>
	      Requires that no resource record exists of the specified
	      <em class="parameter"><code>type</code></em>,
	      <em class="parameter"><code>class</code></em>
	      and
	      <em class="parameter"><code>domain-name</code></em>.
	      If
	      <em class="parameter"><code>class</code></em>
	      is omitted, IN (internet) is assumed.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>[<span class="optional">prereq</span>] yxrrset</strong></span>
	       {domain-name}
	       [class]
	       {type}
	    </span></dt>
<dd>
	    <p>
	      This requires that a resource record of the specified
	      <em class="parameter"><code>type</code></em>,
	      <em class="parameter"><code>class</code></em>
	      and
	      <em class="parameter"><code>domain-name</code></em>
	      must exist.
	      If
	      <em class="parameter"><code>class</code></em>
	      is omitted, IN (internet) is assumed.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>[<span class="optional">prereq</span>] yxrrset</strong></span>
	       {domain-name}
	       [class]
	       {type}
	       {data...}
	    </span></dt>
<dd>
	    <p>
	      The
	      <em class="parameter"><code>data</code></em>
	      from each set of prerequisites of this form
	      sharing a common
	      <em class="parameter"><code>type</code></em>,
	      <em class="parameter"><code>class</code></em>,
	      and
	      <em class="parameter"><code>domain-name</code></em>
	      are combined to form a set of RRs.  This set of RRs must
	      exactly match the set of RRs existing in the zone at the
	      given
	      <em class="parameter"><code>type</code></em>,
	      <em class="parameter"><code>class</code></em>,
	      and
	      <em class="parameter"><code>domain-name</code></em>.
	      The
	      <em class="parameter"><code>data</code></em>
	      are written in the standard text representation of the resource
	      record's
	      RDATA.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>[<span class="optional">update</span>] del[<span class="optional">ete</span>]</strong></span>
	       {domain-name}
	       [ttl]
	       [class]
	       [type [data...]]
	    </span></dt>
<dd>
	    <p>
	      Deletes any resource records named
	      <em class="parameter"><code>domain-name</code></em>.
	      If
	      <em class="parameter"><code>type</code></em>
	      and
	      <em class="parameter"><code>data</code></em>
	      is provided, only matching resource records will be removed.
	      The internet class is assumed if
	      <em class="parameter"><code>class</code></em>
	      is not supplied.  The
	      <em class="parameter"><code>ttl</code></em>
	      is ignored, and is only allowed for compatibility.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>[<span class="optional">update</span>] add</strong></span>
	       {domain-name}
	       {ttl}
	       [class]
	       {type}
	       {data...}
	    </span></dt>
<dd>
	    <p>
	      Adds a new resource record with the specified
	      <em class="parameter"><code>ttl</code></em>,
	      <em class="parameter"><code>class</code></em>
	      and
	      <em class="parameter"><code>data</code></em>.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>show</strong></span>
	    </span></dt>
<dd>
	    <p>
	      Displays the current message, containing all of the
	      prerequisites and
	      updates specified since the last send.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>send</strong></span>
	    </span></dt>
<dd>
	    <p>
	      Sends the current message.  This is equivalent to entering a
	      blank line.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>answer</strong></span>
	    </span></dt>
<dd>
	    <p>
	      Displays the answer.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>debug</strong></span>
	    </span></dt>
<dd>
	    <p>
	      Turn on debugging.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>version</strong></span>
	    </span></dt>
<dd>
	    <p>
	      Print version number.
	    </p>
	  </dd>
<dt><span class="term">
	      <span class="command"><strong>help</strong></span>
	    </span></dt>
<dd>
	    <p>
	      Print a list of commands.
	    </p>
	  </dd>
</dl></div>
<p>
    </p>

    <p>
      Lines beginning with a semicolon are comments and are ignored.
    </p>

  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>EXAMPLES</h2>

    <p>
      The examples below show how
      <span class="command"><strong>nsupdate</strong></span>
      could be used to insert and delete resource records from the
      <span class="type">example.com</span>
      zone.
      Notice that the input in each example contains a trailing blank line so
      that
      a group of commands are sent as one dynamic update request to the
      master name server for
      <span class="type">example.com</span>.

      </p>
<pre class="programlisting">
# nsupdate
&gt; update delete oldhost.example.com A
&gt; update add newhost.example.com 86400 A **********
&gt; send
</pre>
<p>
    </p>
    <p>
      Any A records for
      <span class="type">oldhost.example.com</span>
      are deleted.
      And an A record for
      <span class="type">newhost.example.com</span>
      with IP address ********** is added.
      The newly-added record has a 1 day TTL (86400 seconds).
      </p>
<pre class="programlisting">
# nsupdate
&gt; prereq nxdomain nickname.example.com
&gt; update add nickname.example.com 86400 CNAME somehost.example.com
&gt; send
</pre>
<p>
    </p>
    <p>
      The prerequisite condition gets the name server to check that there
      are no resource records of any type for
      <span class="type">nickname.example.com</span>.

      If there are, the update request fails.
      If this name does not exist, a CNAME for it is added.
      This ensures that when the CNAME is added, it cannot conflict with the
      long-standing rule in RFC 1034 that a name must not exist as any other
      record type if it exists as a CNAME.
      (The rule has been updated for DNSSEC in RFC 2535 to allow CNAMEs to have
      RRSIG, DNSKEY and NSEC records.)
    </p>
  </div>

  <div class="refsection">
<a name="id-1.11"></a><h2>FILES</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term"><code class="constant">/etc/resolv.conf</code></span></dt>
<dd>
	  <p>
	    used to identify default name server
	  </p>
	</dd>
<dt><span class="term"><code class="constant">/var/run/named/session.key</code></span></dt>
<dd>
	  <p>
	    sets the default TSIG key for use in local-only mode
	  </p>
	</dd>
<dt><span class="term"><code class="constant">K{name}.+157.+{random}.key</code></span></dt>
<dd>
	  <p>
	    base-64 encoding of HMAC-MD5 key created by
	    <span class="citerefentry">
	      <span class="refentrytitle">dnssec-keygen</span>(8)
	    </span>.
	  </p>
	</dd>
<dt><span class="term"><code class="constant">K{name}.+157.+{random}.private</code></span></dt>
<dd>
	  <p>
	    base-64 encoding of HMAC-MD5 key created by
	    <span class="citerefentry">
	      <span class="refentrytitle">dnssec-keygen</span>(8)
	    </span>.
	  </p>
	</dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.12"></a><h2>SEE ALSO</h2>

    <p>
      <em class="citetitle">RFC 2136</em>,
      <em class="citetitle">RFC 3007</em>,
      <em class="citetitle">RFC 2104</em>,
      <em class="citetitle">RFC 2845</em>,
      <em class="citetitle">RFC 1034</em>,
      <em class="citetitle">RFC 2535</em>,
      <em class="citetitle">RFC 2931</em>,
      <span class="citerefentry">
	<span class="refentrytitle">named</span>(8)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">ddns-confgen</span>(8)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">dnssec-keygen</span>(8)
      </span>.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.13"></a><h2>BUGS</h2>

    <p>
      The TSIG key is redundantly stored in two separate files.
      This is a consequence of nsupdate using the DST library
      for its cryptographic operations, and may change in future
      releases.
    </p>
  </div>

</div></body>
</html>
