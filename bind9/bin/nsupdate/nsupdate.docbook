<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.nsupdate">
  <info>
    <date>2014-04-18</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>nsupdate</application></refentrytitle>
    <manvolnum>1</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>
  <refnamediv>
    <refname><application>nsupdate</application></refname>
    <refpurpose>Dynamic DNS update utility</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2000</year>
      <year>2001</year>
      <year>2002</year>
      <year>2003</year>
      <year>2004</year>
      <year>2005</year>
      <year>2006</year>
      <year>2007</year>
      <year>2008</year>
      <year>2009</year>
      <year>2010</year>
      <year>2011</year>
      <year>2012</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2017</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>nsupdate</command>
      <arg choice="opt" rep="norepeat"><option>-d</option></arg>
      <arg choice="opt" rep="norepeat"><option>-D</option></arg>
      <arg choice="opt" rep="norepeat"><option>-i</option></arg>
      <arg choice="opt" rep="norepeat"><option>-L <replaceable class="parameter">level</replaceable></option></arg>
      <group choice="opt" rep="norepeat">
	<arg choice="opt" rep="norepeat"><option>-g</option></arg>
	<arg choice="opt" rep="norepeat"><option>-o</option></arg>
	<arg choice="opt" rep="norepeat"><option>-l</option></arg>
	<arg choice="opt" rep="norepeat"><option>-y <replaceable class="parameter"><optional>hmac:</optional>keyname:secret</replaceable></option></arg>
	<arg choice="opt" rep="norepeat"><option>-k <replaceable class="parameter">keyfile</replaceable></option></arg>
      </group>
      <arg choice="opt" rep="norepeat"><option>-t <replaceable class="parameter">timeout</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-u <replaceable class="parameter">udptimeout</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-r <replaceable class="parameter">udpretries</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-R <replaceable class="parameter">randomdev</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-v</option></arg>
      <arg choice="opt" rep="norepeat"><option>-T</option></arg>
      <arg choice="opt" rep="norepeat"><option>-P</option></arg>
      <arg choice="opt" rep="norepeat"><option>-V</option></arg>
      <arg choice="opt" rep="norepeat">filename</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>nsupdate</command>
      is used to submit Dynamic DNS Update requests as defined in RFC 2136
      to a name server.
      This allows resource records to be added or removed from a zone
      without manually editing the zone file.
      A single update request can contain requests to add or remove more than
      one
      resource record.
    </para>
    <para>
      Zones that are under dynamic control via
      <command>nsupdate</command>
      or a DHCP server should not be edited by hand.
      Manual edits could
      conflict with dynamic updates and cause data to be lost.
    </para>
    <para>
      The resource records that are dynamically added or removed with
      <command>nsupdate</command>
      have to be in the same zone.
      Requests are sent to the zone's master server.
      This is identified by the MNAME field of the zone's SOA record.
    </para>
    <para>
      Transaction signatures can be used to authenticate the Dynamic
      DNS updates.  These use the TSIG resource record type described
      in RFC 2845 or the SIG(0) record described in RFC 2535 and
      RFC 2931 or GSS-TSIG as described in RFC 3645.
    </para>
    <para>
      TSIG relies on
      a shared secret that should only be known to
      <command>nsupdate</command> and the name server.
      For instance, suitable <type>key</type> and
      <type>server</type> statements would be added to
      <filename>/etc/named.conf</filename> so that the name server
      can associate the appropriate secret key and algorithm with
      the IP address of the client application that will be using
      TSIG authentication. You can use <command>ddns-confgen</command>
      to generate suitable configuration fragments.
      <command>nsupdate</command>
      uses the <option>-y</option> or <option>-k</option> options
      to provide the TSIG shared secret.  These options are mutually exclusive.
    </para>
    <para>
      SIG(0) uses public key cryptography.
      To use a SIG(0) key, the public key must be stored in a KEY
      record in a zone served by the name server.
    </para>
    <para>
      GSS-TSIG uses Kerberos credentials.  Standard GSS-TSIG mode
      is switched on with the <option>-g</option> flag.  A
      non-standards-compliant variant of GSS-TSIG used by Windows
      2000 can be switched on with the <option>-o</option> flag.
    </para>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
	<term>-d</term>
	<listitem>
	  <para>
	    Debug mode. This provides tracing information about the
	    update requests that are made and the replies received
	    from the name server.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-D</term>
	<listitem>
	  <para>
	    Extra debug mode.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-i</term>
	<listitem>
	  <para>
	    Force interactive mode, even when standard input is not a terminal.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-k <replaceable class="parameter">keyfile</replaceable></term>
	<listitem>
	  <para>
	    The file containing the TSIG authentication key.
	    Keyfiles may be in two formats: a single file containing
	    a <filename>named.conf</filename>-format <command>key</command>
	    statement, which may be generated automatically by
	    <command>ddns-confgen</command>, or a pair of files whose names are
	    of the format <filename>K{name}.+157.+{random}.key</filename> and
	    <filename>K{name}.+157.+{random}.private</filename>, which can be
	    generated by <command>dnssec-keygen</command>.
	    The <option>-k</option> may also be used to specify a SIG(0) key used
	    to authenticate Dynamic DNS update requests.  In this case, the key
	    specified is not an HMAC-MD5 key.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-l</term>
	<listitem>
	  <para>
	    Local-host only mode. This sets the server address to
	    localhost (disabling the <command>server</command> so that the server
	    address cannot be overridden).  Connections to the local server will
	    use a TSIG key found in <filename>/var/run/named/session.key</filename>,
	    which is automatically generated by <command>named</command> if any
	    local master zone has set <command>update-policy</command> to
	    <command>local</command>.  The location of this key file can be
	    overridden with the <option>-k</option> option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-L <replaceable class="parameter">level</replaceable></term>
	<listitem>
	  <para>
	    Set the logging debug level.  If zero, logging is disabled.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-p <replaceable class="parameter">port</replaceable></term>
	<listitem>
	  <para>
	    Set the port to use for connections to a name server. The
	    default is 53.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-P</term>
	<listitem>
	  <para>
	    Print the list of private BIND-specific resource record
	    types whose format is understood
	    by <command>nsupdate</command>. See also
	    the <option>-T</option> option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-r <replaceable class="parameter">udpretries</replaceable></term>
	<listitem>
	  <para>
	    The number of UDP retries. The default is 3. If zero, only
	    one update request will be made.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-R <replaceable class="parameter">randomdev</replaceable></term>
	<listitem>
	<para>
	  Where to obtain randomness. If the operating system
	  does not provide a <filename>/dev/random</filename> or
	  equivalent device, the default source of randomness is keyboard
	  input.  <filename>randomdev</filename> specifies the name of
	  a character device or file containing random data to be used
	  instead of the default.  The special value
	  <filename>keyboard</filename> indicates that keyboard input
	  should be used.  This option may be specified multiple times.
	</para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-t <replaceable class="parameter">timeout</replaceable></term>
	<listitem>
	  <para>
	    The maximum time an update request can take before it is
	    aborted. The default is 300 seconds. Zero can be used to
	    disable the timeout.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-T</term>
	<listitem>
	  <para>
	    Print the list of IANA standard resource record types
	    whose format is understood by <command>nsupdate</command>.
	    <command>nsupdate</command> will exit after the lists are
	    printed. The <option>-T</option> option can be combined
	    with the <option>-P</option> option.
	  </para>
	  <para>
	    Other types can be entered using "TYPEXXXXX" where "XXXXX" is the
	    decimal value of the type with no leading zeros.  The rdata,
	    if present, will be parsed using the UNKNOWN rdata format,
	    (&lt;backslash&gt; &lt;hash&gt; &lt;space&gt; &lt;length&gt;
	    &lt;space&gt; &lt;hexstring&gt;).
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-u <replaceable class="parameter">udptimeout</replaceable></term>
	<listitem>
	  <para>
	    The UDP retry interval. The default is 3 seconds. If zero,
	    the interval will be computed from the timeout interval and
	    number of UDP retries.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-v</term>
	<listitem>
	  <para>
	    Use TCP even for small update requests.
	    By default, <command>nsupdate</command>
	    uses UDP to send update requests to the name server unless they are too
	    large to fit in a UDP request in which case TCP will be used.
	    TCP may be preferable when a batch of update requests is made.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-V</term>
	<listitem>
	  <para>
	    Print the version number and exit.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-y <replaceable class="parameter"><optional>hmac:</optional>keyname:secret</replaceable></term>
	<listitem>
	  <para>
	    Literal TSIG authentication key.
	    <parameter>keyname</parameter> is the name of the key, and
	    <parameter>secret</parameter> is the base64 encoded shared secret.
	    <parameter>hmac</parameter> is the name of the key algorithm;
	    valid choices are <literal>hmac-md5</literal>,
	    <literal>hmac-sha1</literal>, <literal>hmac-sha224</literal>,
	    <literal>hmac-sha256</literal>, <literal>hmac-sha384</literal>, or
	    <literal>hmac-sha512</literal>.  If <parameter>hmac</parameter>
	    is not specified, the default is <literal>hmac-md5</literal>
	    or if MD5 was disabled <literal>hmac-sha256</literal>.
	  </para>
	  <para>
	    NOTE: Use of the <option>-y</option> option is discouraged because the
	    shared secret is supplied as a command line argument in clear text.
	    This may be visible in the output from
	    <citerefentry>
	      <refentrytitle>ps</refentrytitle><manvolnum>1</manvolnum>
	    </citerefentry>
	    or in a history file maintained by the user's shell.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>INPUT FORMAT</title></info>

    <para><command>nsupdate</command>
      reads input from
      <parameter>filename</parameter>
      or standard input.
      Each command is supplied on exactly one line of input.
      Some commands are for administrative purposes.
      The others are either update instructions or prerequisite checks on the
      contents of the zone.
      These checks set conditions that some name or set of
      resource records (RRset) either exists or is absent from the zone.
      These conditions must be met if the entire update request is to succeed.
      Updates will be rejected if the tests for the prerequisite conditions
      fail.
    </para>
    <para>
      Every update request consists of zero or more prerequisites
      and zero or more updates.
      This allows a suitably authenticated update request to proceed if some
      specified resource records are present or missing from the zone.
      A blank input line (or the <command>send</command> command)
      causes the
      accumulated commands to be sent as one Dynamic DNS update request to the
      name server.
    </para>
    <para>
      The command formats and their meaning are as follows:
      <variablelist>

	<varlistentry>
	  <term>
	      <command>server</command>
	      <arg choice="req" rep="norepeat">servername</arg>
	      <arg choice="opt" rep="norepeat">port</arg>
	    </term>
	  <listitem>
	    <para>
	      Sends all dynamic update requests to the name server
	      <parameter>servername</parameter>.
	      When no server statement is provided,
	      <command>nsupdate</command>
	      will send updates to the master server of the correct zone.
	      The MNAME field of that zone's SOA record will identify the
	      master
	      server for that zone.
	      <parameter>port</parameter>
	      is the port number on
	      <parameter>servername</parameter>
	      where the dynamic update requests get sent.
	      If no port number is specified, the default DNS port number of
	      53 is
	      used.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>local</command>
	      <arg choice="req" rep="norepeat">address</arg>
	      <arg choice="opt" rep="norepeat">port</arg>
	    </term>
	  <listitem>
	    <para>
	      Sends all dynamic update requests using the local
	      <parameter>address</parameter>.

	      When no local statement is provided,
	      <command>nsupdate</command>
	      will send updates using an address and port chosen by the
	      system.
	      <parameter>port</parameter>
	      can additionally be used to make requests come from a specific
	      port.
	      If no port number is specified, the system will assign one.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>zone</command>
	      <arg choice="req" rep="norepeat">zonename</arg>
	    </term>
	  <listitem>
	    <para>
	      Specifies that all updates are to be made to the zone
	      <parameter>zonename</parameter>.
	      If no
	      <parameter>zone</parameter>
	      statement is provided,
	      <command>nsupdate</command>
	      will attempt determine the correct zone to update based on the
	      rest of the input.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>class</command>
	      <arg choice="req" rep="norepeat">classname</arg>
	    </term>
	  <listitem>
	    <para>
	      Specify the default class.
	      If no <parameter>class</parameter> is specified, the
	      default class is
	      <parameter>IN</parameter>.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>ttl</command>
	      <arg choice="req" rep="norepeat">seconds</arg>
	    </term>
	  <listitem>
	    <para>
	      Specify the default time to live for records to be added.
	      The value <parameter>none</parameter> will clear the default
	      ttl.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>key</command>
	      <arg choice="opt" rep="norepeat">hmac:</arg><arg choice="req" rep="norepeat">keyname</arg>
	      <arg choice="req" rep="norepeat">secret</arg>
	    </term>
	  <listitem>
	    <para>
	      Specifies that all updates are to be TSIG-signed using the
	      <parameter>keyname</parameter> <parameter>secret</parameter> pair.
	      If <parameter>hmac</parameter> is specified, then it sets the
	      signing algorithm in use; the default is
	      <literal>hmac-md5</literal> or if MD5 was disabled
	      <literal>hmac-sha256</literal>.  The <command>key</command>
	      command overrides any key specified on the command line via
	      <option>-y</option> or <option>-k</option>.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <command>gsstsig</command>
	  </term>
	  <listitem>
	    <para>
	      Use GSS-TSIG to sign the updated.  This is equivalent to
	      specifying <option>-g</option> on the command line.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <command>oldgsstsig</command>
	  </term>
	  <listitem>
	    <para>
	      Use the Windows 2000 version of GSS-TSIG to sign the updated.
	      This is equivalent to specifying <option>-o</option> on the
	      command line.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <command>realm</command>
	    <arg choice="req" rep="norepeat"><optional>realm_name</optional></arg>
	  </term>
	  <listitem>
	    <para>
	      When using GSS-TSIG use <parameter>realm_name</parameter> rather
	      than the default realm in <filename>krb5.conf</filename>.  If no
	      realm is specified the saved realm is cleared.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	    <command>check-names</command>
	    <arg choice="req" rep="norepeat"><optional>yes_or_no</optional></arg>
	  </term>
	  <listitem>
	    <para>
	      Turn on or off check-names processing on records to
	      be added.  Check-names has no effect on prerequisites
	      or records to be deleted.  By default check-names
	      processing is on.  If check-names processing fails
	      the record will not be added to the UPDATE message.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command><optional>prereq</optional> nxdomain</command>
	      <arg choice="req" rep="norepeat">domain-name</arg>
	    </term>
	  <listitem>
	    <para>
	      Requires that no resource record of any type exists with name
	      <parameter>domain-name</parameter>.
	    </para>
	  </listitem>
	</varlistentry>


	<varlistentry>
	  <term>
	      <command><optional>prereq</optional> yxdomain</command>
	      <arg choice="req" rep="norepeat">domain-name</arg>
	    </term>
	  <listitem>
	    <para>
	      Requires that
	      <parameter>domain-name</parameter>
	      exists (has as at least one resource record, of any type).
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command><optional>prereq</optional> nxrrset</command>
	      <arg choice="req" rep="norepeat">domain-name</arg>
	      <arg choice="opt" rep="norepeat">class</arg>
	      <arg choice="req" rep="norepeat">type</arg>
	    </term>
	  <listitem>
	    <para>
	      Requires that no resource record exists of the specified
	      <parameter>type</parameter>,
	      <parameter>class</parameter>
	      and
	      <parameter>domain-name</parameter>.
	      If
	      <parameter>class</parameter>
	      is omitted, IN (internet) is assumed.
	    </para>
	  </listitem>
	</varlistentry>


	<varlistentry>
	  <term>
	      <command><optional>prereq</optional> yxrrset</command>
	      <arg choice="req" rep="norepeat">domain-name</arg>
	      <arg choice="opt" rep="norepeat">class</arg>
	      <arg choice="req" rep="norepeat">type</arg>
	    </term>
	  <listitem>
	    <para>
	      This requires that a resource record of the specified
	      <parameter>type</parameter>,
	      <parameter>class</parameter>
	      and
	      <parameter>domain-name</parameter>
	      must exist.
	      If
	      <parameter>class</parameter>
	      is omitted, IN (internet) is assumed.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command><optional>prereq</optional> yxrrset</command>
	      <arg choice="req" rep="norepeat">domain-name</arg>
	      <arg choice="opt" rep="norepeat">class</arg>
	      <arg choice="req" rep="norepeat">type</arg>
	      <arg choice="req" rep="repeat">data</arg>
	    </term>
	  <listitem>
	    <para>
	      The
	      <parameter>data</parameter>
	      from each set of prerequisites of this form
	      sharing a common
	      <parameter>type</parameter>,
	      <parameter>class</parameter>,
	      and
	      <parameter>domain-name</parameter>
	      are combined to form a set of RRs.  This set of RRs must
	      exactly match the set of RRs existing in the zone at the
	      given
	      <parameter>type</parameter>,
	      <parameter>class</parameter>,
	      and
	      <parameter>domain-name</parameter>.
	      The
	      <parameter>data</parameter>
	      are written in the standard text representation of the resource
	      record's
	      RDATA.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command><optional>update</optional> del<optional>ete</optional></command>
	      <arg choice="req" rep="norepeat">domain-name</arg>
	      <arg choice="opt" rep="norepeat">ttl</arg>
	      <arg choice="opt" rep="norepeat">class</arg>
	      <arg choice="opt" rep="norepeat">type <arg choice="opt" rep="repeat">data</arg></arg>
	    </term>
	  <listitem>
	    <para>
	      Deletes any resource records named
	      <parameter>domain-name</parameter>.
	      If
	      <parameter>type</parameter>
	      and
	      <parameter>data</parameter>
	      is provided, only matching resource records will be removed.
	      The internet class is assumed if
	      <parameter>class</parameter>
	      is not supplied.  The
	      <parameter>ttl</parameter>
	      is ignored, and is only allowed for compatibility.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command><optional>update</optional> add</command>
	      <arg choice="req" rep="norepeat">domain-name</arg>
	      <arg choice="req" rep="norepeat">ttl</arg>
	      <arg choice="opt" rep="norepeat">class</arg>
	      <arg choice="req" rep="norepeat">type</arg>
	      <arg choice="req" rep="repeat">data</arg>
	    </term>
	  <listitem>
	    <para>
	      Adds a new resource record with the specified
	      <parameter>ttl</parameter>,
	      <parameter>class</parameter>
	      and
	      <parameter>data</parameter>.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>show</command>
	    </term>
	  <listitem>
	    <para>
	      Displays the current message, containing all of the
	      prerequisites and
	      updates specified since the last send.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>send</command>
	    </term>
	  <listitem>
	    <para>
	      Sends the current message.  This is equivalent to entering a
	      blank line.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>answer</command>
	    </term>
	  <listitem>
	    <para>
	      Displays the answer.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>debug</command>
	    </term>
	  <listitem>
	    <para>
	      Turn on debugging.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>version</command>
	    </term>
	  <listitem>
	    <para>
	      Print version number.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term>
	      <command>help</command>
	    </term>
	  <listitem>
	    <para>
	      Print a list of commands.
	    </para>
	  </listitem>
	</varlistentry>

      </variablelist>
    </para>

    <para>
      Lines beginning with a semicolon are comments and are ignored.
    </para>

  </refsection>

  <refsection><info><title>EXAMPLES</title></info>

    <para>
      The examples below show how
      <command>nsupdate</command>
      could be used to insert and delete resource records from the
      <type>example.com</type>
      zone.
      Notice that the input in each example contains a trailing blank line so
      that
      a group of commands are sent as one dynamic update request to the
      master name server for
      <type>example.com</type>.

      <programlisting>
# nsupdate
&gt; update delete oldhost.example.com A
&gt; update add newhost.example.com 86400 A **********
&gt; send
</programlisting>
    </para>
    <para>
      Any A records for
      <type>oldhost.example.com</type>
      are deleted.
      And an A record for
      <type>newhost.example.com</type>
      with IP address ********** is added.
      The newly-added record has a 1 day TTL (86400 seconds).
      <programlisting>
# nsupdate
&gt; prereq nxdomain nickname.example.com
&gt; update add nickname.example.com 86400 CNAME somehost.example.com
&gt; send
</programlisting>
    </para>
    <para>
      The prerequisite condition gets the name server to check that there
      are no resource records of any type for
      <type>nickname.example.com</type>.

      If there are, the update request fails.
      If this name does not exist, a CNAME for it is added.
      This ensures that when the CNAME is added, it cannot conflict with the
      long-standing rule in RFC 1034 that a name must not exist as any other
      record type if it exists as a CNAME.
      (The rule has been updated for DNSSEC in RFC 2535 to allow CNAMEs to have
      RRSIG, DNSKEY and NSEC records.)
    </para>
  </refsection>

  <refsection><info><title>FILES</title></info>


    <variablelist>
      <varlistentry>
	<term><constant>/etc/resolv.conf</constant></term>
	<listitem>
	  <para>
	    used to identify default name server
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term><constant>/var/run/named/session.key</constant></term>
	<listitem>
	  <para>
	    sets the default TSIG key for use in local-only mode
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term><constant>K{name}.+157.+{random}.key</constant></term>
	<listitem>
	  <para>
	    base-64 encoding of HMAC-MD5 key created by
	    <citerefentry>
	      <refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>
	    </citerefentry>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term><constant>K{name}.+157.+{random}.private</constant></term>
	<listitem>
	  <para>
	    base-64 encoding of HMAC-MD5 key created by
	    <citerefentry>
	      <refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>
	    </citerefentry>.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para>
      <citetitle>RFC 2136</citetitle>,
      <citetitle>RFC 3007</citetitle>,
      <citetitle>RFC 2104</citetitle>,
      <citetitle>RFC 2845</citetitle>,
      <citetitle>RFC 1034</citetitle>,
      <citetitle>RFC 2535</citetitle>,
      <citetitle>RFC 2931</citetitle>,
      <citerefentry>
	<refentrytitle>named</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>ddns-confgen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>.
    </para>
  </refsection>

  <refsection><info><title>BUGS</title></info>

    <para>
      The TSIG key is redundantly stored in two separate files.
      This is a consequence of nsupdate using the DST library
      for its cryptographic operations, and may change in future
      releases.
    </para>
  </refsection>

</refentry>
