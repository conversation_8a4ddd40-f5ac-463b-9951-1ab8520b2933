.\" Copyright (C) 2000-2012, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: nsupdate
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-04-18
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "NSUPDATE" "1" "2014\-04\-18" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
nsupdate \- Dynamic DNS update utility
.SH "SYNOPSIS"
.HP \w'\fBnsupdate\fR\ 'u
\fBnsupdate\fR [\fB\-d\fR] [\fB\-D\fR] [\fB\-i\fR] [\fB\-L\ \fR\fB\fIlevel\fR\fR] [[\fB\-g\fR] | [\fB\-o\fR] | [\fB\-l\fR] | [\fB\-y\ \fR\fB\fI[hmac:]\fR\fIkeyname:secret\fR\fR] | [\fB\-k\ \fR\fB\fIkeyfile\fR\fR]] [\fB\-t\ \fR\fB\fItimeout\fR\fR] [\fB\-u\ \fR\fB\fIudptimeout\fR\fR] [\fB\-r\ \fR\fB\fIudpretries\fR\fR] [\fB\-R\ \fR\fB\fIrandomdev\fR\fR] [\fB\-v\fR] [\fB\-T\fR] [\fB\-P\fR] [\fB\-V\fR] [filename]
.SH "DESCRIPTION"
.PP
\fBnsupdate\fR
is used to submit Dynamic DNS Update requests as defined in RFC 2136 to a name server\&. This allows resource records to be added or removed from a zone without manually editing the zone file\&. A single update request can contain requests to add or remove more than one resource record\&.
.PP
Zones that are under dynamic control via
\fBnsupdate\fR
or a DHCP server should not be edited by hand\&. Manual edits could conflict with dynamic updates and cause data to be lost\&.
.PP
The resource records that are dynamically added or removed with
\fBnsupdate\fR
have to be in the same zone\&. Requests are sent to the zone\*(Aqs master server\&. This is identified by the MNAME field of the zone\*(Aqs SOA record\&.
.PP
Transaction signatures can be used to authenticate the Dynamic DNS updates\&. These use the TSIG resource record type described in RFC 2845 or the SIG(0) record described in RFC 2535 and RFC 2931 or GSS\-TSIG as described in RFC 3645\&.
.PP
TSIG relies on a shared secret that should only be known to
\fBnsupdate\fR
and the name server\&. For instance, suitable
\fBkey\fR
and
\fBserver\fR
statements would be added to
/etc/named\&.conf
so that the name server can associate the appropriate secret key and algorithm with the IP address of the client application that will be using TSIG authentication\&. You can use
\fBddns\-confgen\fR
to generate suitable configuration fragments\&.
\fBnsupdate\fR
uses the
\fB\-y\fR
or
\fB\-k\fR
options to provide the TSIG shared secret\&. These options are mutually exclusive\&.
.PP
SIG(0) uses public key cryptography\&. To use a SIG(0) key, the public key must be stored in a KEY record in a zone served by the name server\&.
.PP
GSS\-TSIG uses Kerberos credentials\&. Standard GSS\-TSIG mode is switched on with the
\fB\-g\fR
flag\&. A non\-standards\-compliant variant of GSS\-TSIG used by Windows 2000 can be switched on with the
\fB\-o\fR
flag\&.
.SH "OPTIONS"
.PP
\-d
.RS 4
Debug mode\&. This provides tracing information about the update requests that are made and the replies received from the name server\&.
.RE
.PP
\-D
.RS 4
Extra debug mode\&.
.RE
.PP
\-i
.RS 4
Force interactive mode, even when standard input is not a terminal\&.
.RE
.PP
\-k \fIkeyfile\fR
.RS 4
The file containing the TSIG authentication key\&. Keyfiles may be in two formats: a single file containing a
named\&.conf\-format
\fBkey\fR
statement, which may be generated automatically by
\fBddns\-confgen\fR, or a pair of files whose names are of the format
K{name}\&.+157\&.+{random}\&.key
and
K{name}\&.+157\&.+{random}\&.private, which can be generated by
\fBdnssec\-keygen\fR\&. The
\fB\-k\fR
may also be used to specify a SIG(0) key used to authenticate Dynamic DNS update requests\&. In this case, the key specified is not an HMAC\-MD5 key\&.
.RE
.PP
\-l
.RS 4
Local\-host only mode\&. This sets the server address to localhost (disabling the
\fBserver\fR
so that the server address cannot be overridden)\&. Connections to the local server will use a TSIG key found in
/var/run/named/session\&.key, which is automatically generated by
\fBnamed\fR
if any local master zone has set
\fBupdate\-policy\fR
to
\fBlocal\fR\&. The location of this key file can be overridden with the
\fB\-k\fR
option\&.
.RE
.PP
\-L \fIlevel\fR
.RS 4
Set the logging debug level\&. If zero, logging is disabled\&.
.RE
.PP
\-p \fIport\fR
.RS 4
Set the port to use for connections to a name server\&. The default is 53\&.
.RE
.PP
\-P
.RS 4
Print the list of private BIND\-specific resource record types whose format is understood by
\fBnsupdate\fR\&. See also the
\fB\-T\fR
option\&.
.RE
.PP
\-r \fIudpretries\fR
.RS 4
The number of UDP retries\&. The default is 3\&. If zero, only one update request will be made\&.
.RE
.PP
\-R \fIrandomdev\fR
.RS 4
Where to obtain randomness\&. If the operating system does not provide a
/dev/random
or equivalent device, the default source of randomness is keyboard input\&.
randomdev
specifies the name of a character device or file containing random data to be used instead of the default\&. The special value
keyboard
indicates that keyboard input should be used\&. This option may be specified multiple times\&.
.RE
.PP
\-t \fItimeout\fR
.RS 4
The maximum time an update request can take before it is aborted\&. The default is 300 seconds\&. Zero can be used to disable the timeout\&.
.RE
.PP
\-T
.RS 4
Print the list of IANA standard resource record types whose format is understood by
\fBnsupdate\fR\&.
\fBnsupdate\fR
will exit after the lists are printed\&. The
\fB\-T\fR
option can be combined with the
\fB\-P\fR
option\&.
.sp
Other types can be entered using "TYPEXXXXX" where "XXXXX" is the decimal value of the type with no leading zeros\&. The rdata, if present, will be parsed using the UNKNOWN rdata format, (<backslash> <hash> <space> <length> <space> <hexstring>)\&.
.RE
.PP
\-u \fIudptimeout\fR
.RS 4
The UDP retry interval\&. The default is 3 seconds\&. If zero, the interval will be computed from the timeout interval and number of UDP retries\&.
.RE
.PP
\-v
.RS 4
Use TCP even for small update requests\&. By default,
\fBnsupdate\fR
uses UDP to send update requests to the name server unless they are too large to fit in a UDP request in which case TCP will be used\&. TCP may be preferable when a batch of update requests is made\&.
.RE
.PP
\-V
.RS 4
Print the version number and exit\&.
.RE
.PP
\-y \fI[hmac:]\fR\fIkeyname:secret\fR
.RS 4
Literal TSIG authentication key\&.
\fIkeyname\fR
is the name of the key, and
\fIsecret\fR
is the base64 encoded shared secret\&.
\fIhmac\fR
is the name of the key algorithm; valid choices are
hmac\-md5,
hmac\-sha1,
hmac\-sha224,
hmac\-sha256,
hmac\-sha384, or
hmac\-sha512\&. If
\fIhmac\fR
is not specified, the default is
hmac\-md5
or if MD5 was disabled
hmac\-sha256\&.
.sp
NOTE: Use of the
\fB\-y\fR
option is discouraged because the shared secret is supplied as a command line argument in clear text\&. This may be visible in the output from
\fBps\fR(1)
or in a history file maintained by the user\*(Aqs shell\&.
.RE
.SH "INPUT FORMAT"
.PP
\fBnsupdate\fR
reads input from
\fIfilename\fR
or standard input\&. Each command is supplied on exactly one line of input\&. Some commands are for administrative purposes\&. The others are either update instructions or prerequisite checks on the contents of the zone\&. These checks set conditions that some name or set of resource records (RRset) either exists or is absent from the zone\&. These conditions must be met if the entire update request is to succeed\&. Updates will be rejected if the tests for the prerequisite conditions fail\&.
.PP
Every update request consists of zero or more prerequisites and zero or more updates\&. This allows a suitably authenticated update request to proceed if some specified resource records are present or missing from the zone\&. A blank input line (or the
\fBsend\fR
command) causes the accumulated commands to be sent as one Dynamic DNS update request to the name server\&.
.PP
The command formats and their meaning are as follows:
.PP
\fBserver\fR {servername} [port]
.RS 4
Sends all dynamic update requests to the name server
\fIservername\fR\&. When no server statement is provided,
\fBnsupdate\fR
will send updates to the master server of the correct zone\&. The MNAME field of that zone\*(Aqs SOA record will identify the master server for that zone\&.
\fIport\fR
is the port number on
\fIservername\fR
where the dynamic update requests get sent\&. If no port number is specified, the default DNS port number of 53 is used\&.
.RE
.PP
\fBlocal\fR {address} [port]
.RS 4
Sends all dynamic update requests using the local
\fIaddress\fR\&. When no local statement is provided,
\fBnsupdate\fR
will send updates using an address and port chosen by the system\&.
\fIport\fR
can additionally be used to make requests come from a specific port\&. If no port number is specified, the system will assign one\&.
.RE
.PP
\fBzone\fR {zonename}
.RS 4
Specifies that all updates are to be made to the zone
\fIzonename\fR\&. If no
\fIzone\fR
statement is provided,
\fBnsupdate\fR
will attempt determine the correct zone to update based on the rest of the input\&.
.RE
.PP
\fBclass\fR {classname}
.RS 4
Specify the default class\&. If no
\fIclass\fR
is specified, the default class is
\fIIN\fR\&.
.RE
.PP
\fBttl\fR {seconds}
.RS 4
Specify the default time to live for records to be added\&. The value
\fInone\fR
will clear the default ttl\&.
.RE
.PP
\fBkey\fR [hmac:] {keyname} {secret}
.RS 4
Specifies that all updates are to be TSIG\-signed using the
\fIkeyname\fR\fIsecret\fR
pair\&. If
\fIhmac\fR
is specified, then it sets the signing algorithm in use; the default is
hmac\-md5
or if MD5 was disabled
hmac\-sha256\&. The
\fBkey\fR
command overrides any key specified on the command line via
\fB\-y\fR
or
\fB\-k\fR\&.
.RE
.PP
\fBgsstsig\fR
.RS 4
Use GSS\-TSIG to sign the updated\&. This is equivalent to specifying
\fB\-g\fR
on the command line\&.
.RE
.PP
\fBoldgsstsig\fR
.RS 4
Use the Windows 2000 version of GSS\-TSIG to sign the updated\&. This is equivalent to specifying
\fB\-o\fR
on the command line\&.
.RE
.PP
\fBrealm\fR {[realm_name]}
.RS 4
When using GSS\-TSIG use
\fIrealm_name\fR
rather than the default realm in
krb5\&.conf\&. If no realm is specified the saved realm is cleared\&.
.RE
.PP
\fBcheck\-names\fR {[yes_or_no]}
.RS 4
Turn on or off check\-names processing on records to be added\&. Check\-names has no effect on prerequisites or records to be deleted\&. By default check\-names processing is on\&. If check\-names processing fails the record will not be added to the UPDATE message\&.
.RE
.PP
\fB[prereq]\fR\fB nxdomain\fR {domain\-name}
.RS 4
Requires that no resource record of any type exists with name
\fIdomain\-name\fR\&.
.RE
.PP
\fB[prereq]\fR\fB yxdomain\fR {domain\-name}
.RS 4
Requires that
\fIdomain\-name\fR
exists (has as at least one resource record, of any type)\&.
.RE
.PP
\fB[prereq]\fR\fB nxrrset\fR {domain\-name} [class] {type}
.RS 4
Requires that no resource record exists of the specified
\fItype\fR,
\fIclass\fR
and
\fIdomain\-name\fR\&. If
\fIclass\fR
is omitted, IN (internet) is assumed\&.
.RE
.PP
\fB[prereq]\fR\fB yxrrset\fR {domain\-name} [class] {type}
.RS 4
This requires that a resource record of the specified
\fItype\fR,
\fIclass\fR
and
\fIdomain\-name\fR
must exist\&. If
\fIclass\fR
is omitted, IN (internet) is assumed\&.
.RE
.PP
\fB[prereq]\fR\fB yxrrset\fR {domain\-name} [class] {type} {data...}
.RS 4
The
\fIdata\fR
from each set of prerequisites of this form sharing a common
\fItype\fR,
\fIclass\fR, and
\fIdomain\-name\fR
are combined to form a set of RRs\&. This set of RRs must exactly match the set of RRs existing in the zone at the given
\fItype\fR,
\fIclass\fR, and
\fIdomain\-name\fR\&. The
\fIdata\fR
are written in the standard text representation of the resource record\*(Aqs RDATA\&.
.RE
.PP
\fB[update]\fR\fB del\fR\fB[ete]\fR {domain\-name} [ttl] [class] [type\ [data...]]
.RS 4
Deletes any resource records named
\fIdomain\-name\fR\&. If
\fItype\fR
and
\fIdata\fR
is provided, only matching resource records will be removed\&. The internet class is assumed if
\fIclass\fR
is not supplied\&. The
\fIttl\fR
is ignored, and is only allowed for compatibility\&.
.RE
.PP
\fB[update]\fR\fB add\fR {domain\-name} {ttl} [class] {type} {data...}
.RS 4
Adds a new resource record with the specified
\fIttl\fR,
\fIclass\fR
and
\fIdata\fR\&.
.RE
.PP
\fBshow\fR
.RS 4
Displays the current message, containing all of the prerequisites and updates specified since the last send\&.
.RE
.PP
\fBsend\fR
.RS 4
Sends the current message\&. This is equivalent to entering a blank line\&.
.RE
.PP
\fBanswer\fR
.RS 4
Displays the answer\&.
.RE
.PP
\fBdebug\fR
.RS 4
Turn on debugging\&.
.RE
.PP
\fBversion\fR
.RS 4
Print version number\&.
.RE
.PP
\fBhelp\fR
.RS 4
Print a list of commands\&.
.RE
.PP
Lines beginning with a semicolon are comments and are ignored\&.
.SH "EXAMPLES"
.PP
The examples below show how
\fBnsupdate\fR
could be used to insert and delete resource records from the
\fBexample\&.com\fR
zone\&. Notice that the input in each example contains a trailing blank line so that a group of commands are sent as one dynamic update request to the master name server for
\fBexample\&.com\fR\&.
.sp
.if n \{\
.RS 4
.\}
.nf
# nsupdate
> update delete oldhost\&.example\&.com A
> update add newhost\&.example\&.com 86400 A 172\&.16\&.1\&.1
> send
.fi
.if n \{\
.RE
.\}
.PP
Any A records for
\fBoldhost\&.example\&.com\fR
are deleted\&. And an A record for
\fBnewhost\&.example\&.com\fR
with IP address 172\&.16\&.1\&.1 is added\&. The newly\-added record has a 1 day TTL (86400 seconds)\&.
.sp
.if n \{\
.RS 4
.\}
.nf
# nsupdate
> prereq nxdomain nickname\&.example\&.com
> update add nickname\&.example\&.com 86400 CNAME somehost\&.example\&.com
> send
.fi
.if n \{\
.RE
.\}
.PP
The prerequisite condition gets the name server to check that there are no resource records of any type for
\fBnickname\&.example\&.com\fR\&. If there are, the update request fails\&. If this name does not exist, a CNAME for it is added\&. This ensures that when the CNAME is added, it cannot conflict with the long\-standing rule in RFC 1034 that a name must not exist as any other record type if it exists as a CNAME\&. (The rule has been updated for DNSSEC in RFC 2535 to allow CNAMEs to have RRSIG, DNSKEY and NSEC records\&.)
.SH "FILES"
.PP
\fB/etc/resolv\&.conf\fR
.RS 4
used to identify default name server
.RE
.PP
\fB/var/run/named/session\&.key\fR
.RS 4
sets the default TSIG key for use in local\-only mode
.RE
.PP
\fBK{name}\&.+157\&.+{random}\&.key\fR
.RS 4
base\-64 encoding of HMAC\-MD5 key created by
\fBdnssec-keygen\fR(8)\&.
.RE
.PP
\fBK{name}\&.+157\&.+{random}\&.private\fR
.RS 4
base\-64 encoding of HMAC\-MD5 key created by
\fBdnssec-keygen\fR(8)\&.
.RE
.SH "SEE ALSO"
.PP
RFC 2136,
RFC 3007,
RFC 2104,
RFC 2845,
RFC 1034,
RFC 2535,
RFC 2931,
\fBnamed\fR(8),
\fBddns-confgen\fR(8),
\fBdnssec-keygen\fR(8)\&.
.SH "BUGS"
.PP
The TSIG key is redundantly stored in two separate files\&. This is a consequence of nsupdate using the DST library for its cryptographic operations, and may change in future releases\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2000-2012, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
.br
