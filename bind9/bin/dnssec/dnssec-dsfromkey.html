<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2008-2012, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>dnssec-dsfromkey</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.dnssec-dsfromkey"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">dnssec-dsfromkey</span>
     &#8212; DNSSEC DS RR generation tool
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">dnssec-dsfromkey</code> 
       [<code class="option">-v <em class="replaceable"><code>level</code></em></code>]
       [<code class="option">-1</code>]
       [<code class="option">-2</code>]
       [<code class="option">-a <em class="replaceable"><code>alg</code></em></code>]
       [<code class="option">-C</code>]
       [<code class="option">-l <em class="replaceable"><code>domain</code></em></code>]
       [<code class="option">-T <em class="replaceable"><code>TTL</code></em></code>]
       {keyfile}
    </p></div>
    <div class="cmdsynopsis"><p>
      <code class="command">dnssec-dsfromkey</code> 
       {-s}
       [<code class="option">-1</code>]
       [<code class="option">-2</code>]
       [<code class="option">-a <em class="replaceable"><code>alg</code></em></code>]
       [<code class="option">-K <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-l <em class="replaceable"><code>domain</code></em></code>]
       [<code class="option">-s</code>]
       [<code class="option">-c <em class="replaceable"><code>class</code></em></code>]
       [<code class="option">-T <em class="replaceable"><code>TTL</code></em></code>]
       [<code class="option">-f <em class="replaceable"><code>file</code></em></code>]
       [<code class="option">-A</code>]
       [<code class="option">-v <em class="replaceable"><code>level</code></em></code>]
       {dnsname}
   </p></div>
    <div class="cmdsynopsis"><p>
      <code class="command">dnssec-dsfromkey</code> 
       [<code class="option">-h</code>]
       [<code class="option">-V</code>]
   </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>dnssec-dsfromkey</strong></span>
      outputs the Delegation Signer (DS) resource record (RR), as defined in
      RFC 3658 and RFC 4509, for the given key(s).
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-1</span></dt>
<dd>
	  <p>
	    Use SHA-1 as the digest algorithm (the default is to use
	    both SHA-1 and SHA-256).
	  </p>
	</dd>
<dt><span class="term">-2</span></dt>
<dd>
	  <p>
	    Use SHA-256 as the digest algorithm.
	  </p>
	</dd>
<dt><span class="term">-a <em class="replaceable"><code>algorithm</code></em></span></dt>
<dd>
	  <p>
	    Select the digest algorithm. The value of
	    <code class="option">algorithm</code> must be one of SHA-1 (SHA1),
	    SHA-256 (SHA256), GOST or SHA-384 (SHA384).
	    These values are case insensitive.
	  </p>
	</dd>
<dt><span class="term">-C</span></dt>
<dd>
	  <p>
	    Generate CDS records rather than DS records.  This is mutually
	    exclusive with generating lookaside records.
	  </p>
	</dd>
<dt><span class="term">-T <em class="replaceable"><code>TTL</code></em></span></dt>
<dd>
	  <p>
	    Specifies the TTL of the DS records.
	  </p>
	  </dd>
<dt><span class="term">-K <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
	  <p>
	    Look for key files (or, in keyset mode,
	    <code class="filename">keyset-</code> files) in
	    <code class="option">directory</code>.
	  </p>
	</dd>
<dt><span class="term">-f <em class="replaceable"><code>file</code></em></span></dt>
<dd>
	  <p>
	    Zone file mode: in place of the keyfile name, the argument is
	    the DNS domain name of a zone master file, which can be read
	    from <code class="option">file</code>.  If the zone name is the same as
	    <code class="option">file</code>, then it may be omitted.
	  </p>
	  <p>
	    If <code class="option">file</code> is set to <code class="literal">"-"</code>, then
	    the zone data is read from the standard input.  This makes it
	    possible to use the output of the <span class="command"><strong>dig</strong></span>
	    command as input, as in:
	  </p>
	  <p>
	    <strong class="userinput"><code>dig dnskey example.com | dnssec-dsfromkey -f - example.com</code></strong>
	  </p>
	</dd>
<dt><span class="term">-A</span></dt>
<dd>
          <p>
            Include ZSKs when generating DS records.  Without this option,
            only keys which have the KSK flag set will be converted to DS
            records and printed.  Useful only in zone file mode.
          </p>
        </dd>
<dt><span class="term">-l <em class="replaceable"><code>domain</code></em></span></dt>
<dd>
	  <p>
	    Generate a DLV set instead of a DS set.  The specified
	    <code class="option">domain</code> is appended to the name for each
	    record in the set.
	    The DNSSEC Lookaside Validation (DLV) RR is described
	    in RFC 4431.  This is mutually exclusive with generating
	    CDS records.
	  </p>
	</dd>
<dt><span class="term">-s</span></dt>
<dd>
	  <p>
	    Keyset mode: in place of the keyfile name, the argument is
	    the DNS domain name of a keyset file.
	  </p>
	</dd>
<dt><span class="term">-c <em class="replaceable"><code>class</code></em></span></dt>
<dd>
	  <p>
	    Specifies the DNS class (default is IN).  Useful only
	    in keyset or zone file mode.
	  </p>
	  </dd>
<dt><span class="term">-v <em class="replaceable"><code>level</code></em></span></dt>
<dd>
	  <p>
	    Sets the debugging level.
	  </p>
	</dd>
<dt><span class="term">-h</span></dt>
<dd>
	  <p>
	    Prints usage information.
	  </p>
	</dd>
<dt><span class="term">-V</span></dt>
<dd>
	  <p>
	    Prints version information.
	  </p>
	</dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>EXAMPLE</h2>

    <p>
      To build the SHA-256 DS RR from the
      <strong class="userinput"><code>Kexample.com.+003+26160</code></strong>
      keyfile name, the following command would be issued:
    </p>
    <p><strong class="userinput"><code>dnssec-dsfromkey -2 Kexample.com.+003+26160</code></strong>
    </p>
    <p>
      The command would print something like:
    </p>
    <p><strong class="userinput"><code>example.com. IN DS 26160 5 2 3A1EADA7A74B8D0BA86726B0C227AA85AB8BBD2B2004F41A868A54F0 C5EA0B94</code></strong>
    </p>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>FILES</h2>

    <p>
      The keyfile can be designed by the key identification
      <code class="filename">Knnnn.+aaa+iiiii</code> or the full file name
      <code class="filename">Knnnn.+aaa+iiiii.key</code> as generated by
      <span class="refentrytitle">dnssec-keygen</span>(8).
    </p>
    <p>
      The keyset file name is built from the <code class="option">directory</code>,
      the string <code class="filename">keyset-</code> and the
      <code class="option">dnsname</code>.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.11"></a><h2>CAVEAT</h2>

    <p>
      A keyfile error can give a "file not found" even if the file exists.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.12"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
	<span class="refentrytitle">dnssec-keygen</span>(8)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">dnssec-signzone</span>(8)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>,
      <em class="citetitle">RFC 3658</em>,
      <em class="citetitle">RFC 4431</em>.
      <em class="citetitle">RFC 4509</em>.
    </p>
  </div>

</div></body>
</html>
