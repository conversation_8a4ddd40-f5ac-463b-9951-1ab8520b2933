<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000-2005, 2007-2012, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>dnssec-keygen</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.dnssec-keygen"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">dnssec-keygen</span>
     &#8212; DNSSEC key generation tool
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">dnssec-keygen</code> 
       [<code class="option">-a <em class="replaceable"><code>algorithm</code></em></code>]
       [<code class="option">-b <em class="replaceable"><code>keysize</code></em></code>]
       [<code class="option">-n <em class="replaceable"><code>nametype</code></em></code>]
       [<code class="option">-3</code>]
       [<code class="option">-A <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-C</code>]
       [<code class="option">-c <em class="replaceable"><code>class</code></em></code>]
       [<code class="option">-D <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-D sync <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-E <em class="replaceable"><code>engine</code></em></code>]
       [<code class="option">-f <em class="replaceable"><code>flag</code></em></code>]
       [<code class="option">-G</code>]
       [<code class="option">-g <em class="replaceable"><code>generator</code></em></code>]
       [<code class="option">-h</code>]
       [<code class="option">-I <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-i <em class="replaceable"><code>interval</code></em></code>]
       [<code class="option">-K <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-k</code>]
       [<code class="option">-L <em class="replaceable"><code>ttl</code></em></code>]
       [<code class="option">-P <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-P sync <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-p <em class="replaceable"><code>protocol</code></em></code>]
       [<code class="option">-q</code>]
       [<code class="option">-R <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-r <em class="replaceable"><code>randomdev</code></em></code>]
       [<code class="option">-S <em class="replaceable"><code>key</code></em></code>]
       [<code class="option">-s <em class="replaceable"><code>strength</code></em></code>]
       [<code class="option">-t <em class="replaceable"><code>type</code></em></code>]
       [<code class="option">-V</code>]
       [<code class="option">-v <em class="replaceable"><code>level</code></em></code>]
       [<code class="option">-z</code>]
       {name}
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>dnssec-keygen</strong></span>
      generates keys for DNSSEC (Secure DNS), as defined in RFC 2535
      and RFC 4034.  It can also generate keys for use with
      TSIG (Transaction Signatures) as defined in RFC 2845, or TKEY
      (Transaction Key) as defined in RFC 2930.
    </p>
    <p>
      The <code class="option">name</code> of the key is specified on the command
      line.  For DNSSEC keys, this must match the name of the zone for
      which the key is being generated.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-a <em class="replaceable"><code>algorithm</code></em></span></dt>
<dd>
	  <p>
	    Selects the cryptographic algorithm.  For DNSSEC keys, the value
	    of <code class="option">algorithm</code> must be one of RSAMD5, RSASHA1,
	    DSA, NSEC3RSASHA1, NSEC3DSA, RSASHA256, RSASHA512, ECCGOST,
	    ECDSAP256SHA256, ECDSAP384SHA384, ED25519 or ED448.
	    For TSIG/TKEY, the value must
	    be DH (Diffie Hellman), HMAC-MD5, HMAC-SHA1, HMAC-SHA224,
	    HMAC-SHA256, HMAC-SHA384, or HMAC-SHA512.  These values are
	    case insensitive.
	  </p>
	  <p>
	    If no algorithm is specified, then RSASHA1 will be used by
	    default, unless the <code class="option">-3</code> option is specified,
	    in which case NSEC3RSASHA1 will be used instead.  (If
	    <code class="option">-3</code> is used and an algorithm is specified,
	    that algorithm will be checked for compatibility with NSEC3.)
	  </p>
	  <p>
	    Note 1: that for DNSSEC, RSASHA1 is a mandatory to implement
	    algorithm, and DSA is recommended.  For TSIG, HMAC-MD5 is
	    mandatory.
	  </p>
	  <p>
	    Note 2: DH, HMAC-MD5, and HMAC-SHA1 through HMAC-SHA512
	    automatically set the -T KEY option.
	  </p>
	</dd>
<dt><span class="term">-b <em class="replaceable"><code>keysize</code></em></span></dt>
<dd>
	  <p>
	    Specifies the number of bits in the key.  The choice of key
	    size depends on the algorithm used.  RSA keys must be
	    between 512 and 2048 bits.  Diffie Hellman keys must be between
	    128 and 4096 bits.  DSA keys must be between 512 and 1024
	    bits and an exact multiple of 64.  HMAC keys must be
	    between 1 and 512 bits. Elliptic curve algorithms don't need
	    this parameter.
	  </p>
	  <p>
	    The key size does not need to be specified if using a default
	    algorithm.  The default key size is 1024 bits for zone signing
	    keys (ZSKs) and 2048 bits for key signing keys (KSKs,
	    generated with <code class="option">-f KSK</code>).  However, if an
	    algorithm is explicitly specified with the <code class="option">-a</code>,
	    then there is no default key size, and the <code class="option">-b</code>
	    must be used.
	  </p>
	</dd>
<dt><span class="term">-n <em class="replaceable"><code>nametype</code></em></span></dt>
<dd>
	  <p>
	    Specifies the owner type of the key.  The value of
	    <code class="option">nametype</code> must either be ZONE (for a DNSSEC
	    zone key (KEY/DNSKEY)), HOST or ENTITY (for a key associated with
	    a host (KEY)),
	    USER (for a key associated with a user(KEY)) or OTHER (DNSKEY).
	    These values are case insensitive.  Defaults to ZONE for DNSKEY
	    generation.
	  </p>
	</dd>
<dt><span class="term">-3</span></dt>
<dd>
	  <p>
	    Use an NSEC3-capable algorithm to generate a DNSSEC key.
	    If this option is used and no algorithm is explicitly
	    set on the command line, NSEC3RSASHA1 will be used by
	    default. Note that RSASHA256, RSASHA512, ECCGOST,
	    ECDSAP256SHA256, ECDSAP384SHA384, ED25519 and ED448
	    algorithms are NSEC3-capable.
	  </p>
	</dd>
<dt><span class="term">-C</span></dt>
<dd>
	  <p>
	    Compatibility mode:  generates an old-style key, without
	    any metadata.  By default, <span class="command"><strong>dnssec-keygen</strong></span>
	    will include the key's creation date in the metadata stored
	    with the private key, and other dates may be set there as well
	    (publication date, activation date, etc).  Keys that include
	    this data may be incompatible with older versions of BIND; the
	    <code class="option">-C</code> option suppresses them.
	  </p>
	</dd>
<dt><span class="term">-c <em class="replaceable"><code>class</code></em></span></dt>
<dd>
	  <p>
	    Indicates that the DNS record containing the key should have
	    the specified class.  If not specified, class IN is used.
	  </p>
	</dd>
<dt><span class="term">-E <em class="replaceable"><code>engine</code></em></span></dt>
<dd>
	  <p>
	    Specifies the cryptographic hardware to use, when applicable.
	  </p>
	  <p>
	    When BIND is built with OpenSSL PKCS#11 support, this defaults
	    to the string "pkcs11", which identifies an OpenSSL engine
	    that can drive a cryptographic accelerator or hardware service
	    module.  When BIND is built with native PKCS#11 cryptography
	    (--enable-native-pkcs11), it defaults to the path of the PKCS#11
	    provider library specified via "--with-pkcs11".
	  </p>
	</dd>
<dt><span class="term">-f <em class="replaceable"><code>flag</code></em></span></dt>
<dd>
	  <p>
	    Set the specified flag in the flag field of the KEY/DNSKEY record.
	    The only recognized flags are KSK (Key Signing Key) and REVOKE.
	  </p>
	</dd>
<dt><span class="term">-G</span></dt>
<dd>
	  <p>
	    Generate a key, but do not publish it or sign with it.  This
	    option is incompatible with -P and -A.
	  </p>
	</dd>
<dt><span class="term">-g <em class="replaceable"><code>generator</code></em></span></dt>
<dd>
	  <p>
	    If generating a Diffie Hellman key, use this generator.
	    Allowed values are 2 and 5.  If no generator
	    is specified, a known prime from RFC 2539 will be used
	    if possible; otherwise the default is 2.
	  </p>
	</dd>
<dt><span class="term">-h</span></dt>
<dd>
	  <p>
	    Prints a short summary of the options and arguments to
	    <span class="command"><strong>dnssec-keygen</strong></span>.
	  </p>
	</dd>
<dt><span class="term">-K <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
	  <p>
	    Sets the directory in which the key files are to be written.
	  </p>
	</dd>
<dt><span class="term">-k</span></dt>
<dd>
	  <p>
	    Deprecated in favor of -T KEY.
	  </p>
	</dd>
<dt><span class="term">-L <em class="replaceable"><code>ttl</code></em></span></dt>
<dd>
	  <p>
	    Sets the default TTL to use for this key when it is converted
	    into a DNSKEY RR.  If the key is imported into a zone,
	    this is the TTL that will be used for it, unless there was
	    already a DNSKEY RRset in place, in which case the existing TTL
	    would take precedence.  If this value is not set and there
	    is no existing DNSKEY RRset, the TTL will default to the
	    SOA TTL. Setting the default TTL to <code class="literal">0</code>
	    or <code class="literal">none</code> is the same as leaving it unset.
	  </p>
	</dd>
<dt><span class="term">-p <em class="replaceable"><code>protocol</code></em></span></dt>
<dd>
	  <p>
	    Sets the protocol value for the generated key.  The protocol
	    is a number between 0 and 255.  The default is 3 (DNSSEC).
	    Other possible values for this argument are listed in
	    RFC 2535 and its successors.
	  </p>
	</dd>
<dt><span class="term">-q</span></dt>
<dd>
	  <p>
	    Quiet mode: Suppresses unnecessary output, including
	    progress indication.  Without this option, when
	    <span class="command"><strong>dnssec-keygen</strong></span> is run interactively
	    to generate an RSA or DSA key pair, it will print a string
	    of symbols to <code class="filename">stderr</code> indicating the
	    progress of the key generation.  A '.' indicates that a
	    random number has been found which passed an initial
	    sieve test; '+' means a number has passed a single
	    round of the Miller-Rabin primality test; a space
	    means that the number has passed all the tests and is
	    a satisfactory key.
	  </p>
	</dd>
<dt><span class="term">-r <em class="replaceable"><code>randomdev</code></em></span></dt>
<dd>
	  <p>
	    Specifies the source of randomness.  If the operating
	    system does not provide a <code class="filename">/dev/random</code>
	    or equivalent device, the default source of randomness
	    is keyboard input.  <code class="filename">randomdev</code>
	    specifies
	    the name of a character device or file containing random
	    data to be used instead of the default.  The special value
	    <code class="filename">keyboard</code> indicates that keyboard
	    input should be used.
	  </p>
	</dd>
<dt><span class="term">-S <em class="replaceable"><code>key</code></em></span></dt>
<dd>
	  <p>
	    Create a new key which is an explicit successor to an
	    existing key.  The name, algorithm, size, and type of the
	    key will be set to match the existing key.  The activation
	    date of the new key will be set to the inactivation date of
	    the existing one.  The publication date will be set to the
	    activation date minus the prepublication interval, which
	    defaults to 30 days.
	  </p>
	</dd>
<dt><span class="term">-s <em class="replaceable"><code>strength</code></em></span></dt>
<dd>
	  <p>
	    Specifies the strength value of the key.  The strength is
	    a number between 0 and 15, and currently has no defined
	    purpose in DNSSEC.
	  </p>
	</dd>
<dt><span class="term">-T <em class="replaceable"><code>rrtype</code></em></span></dt>
<dd>
	  <p>
	    Specifies the resource record type to use for the key.
	    <code class="option">rrtype</code> must be either DNSKEY or KEY.  The
	    default is DNSKEY when using a DNSSEC algorithm, but it can be
	    overridden to KEY for use with SIG(0).
	  </p>
<p>
	  </p>
<p>
	    Using any TSIG algorithm (HMAC-* or DH) forces this option
	    to KEY.
	  </p>
	</dd>
<dt><span class="term">-t <em class="replaceable"><code>type</code></em></span></dt>
<dd>
	  <p>
	    Indicates the use of the key.  <code class="option">type</code> must be
	    one of AUTHCONF, NOAUTHCONF, NOAUTH, or NOCONF.  The default
	    is AUTHCONF.  AUTH refers to the ability to authenticate
	    data, and CONF the ability to encrypt data.
	  </p>
	</dd>
<dt><span class="term">-v <em class="replaceable"><code>level</code></em></span></dt>
<dd>
	  <p>
	    Sets the debugging level.
	  </p>
	</dd>
<dt><span class="term">-V</span></dt>
<dd>
	  <p>
	    Prints version information.
	  </p>
	</dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>TIMING OPTIONS</h2>


    <p>
      Dates can be expressed in the format YYYYMMDD or YYYYMMDDHHMMSS.
      If the argument begins with a '+' or '-', it is interpreted as
      an offset from the present time.  For convenience, if such an offset
      is followed by one of the suffixes 'y', 'mo', 'w', 'd', 'h', or 'mi',
      then the offset is computed in years (defined as 365 24-hour days,
      ignoring leap years), months (defined as 30 24-hour days), weeks,
      days, hours, or minutes, respectively.  Without a suffix, the offset
      is computed in seconds.  To explicitly prevent a date from being
      set, use 'none' or 'never'.
    </p>

    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-P <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
	  <p>
	    Sets the date on which a key is to be published to the zone.
	    After that date, the key will be included in the zone but will
	    not be used to sign it.  If not set, and if the -G option has
	    not been used, the default is "now".
	  </p>
	</dd>
<dt><span class="term">-P sync <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
	  <p>
	    Sets the date on which CDS and CDNSKEY records that match this
	    key are to be published to the zone.
	  </p>
	</dd>
<dt><span class="term">-A <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
	  <p>
	    Sets the date on which the key is to be activated.  After that
	    date, the key will be included in the zone and used to sign
	    it.  If not set, and if the -G option has not been used, the
	    default is "now".  If set, if and -P is not set, then
	    the publication date will be set to the activation date
	    minus the prepublication interval.
	  </p>
	</dd>
<dt><span class="term">-R <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
	  <p>
	    Sets the date on which the key is to be revoked.  After that
	    date, the key will be flagged as revoked.  It will be included
	    in the zone and will be used to sign it.
	  </p>
	</dd>
<dt><span class="term">-I <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
	  <p>
	    Sets the date on which the key is to be retired.  After that
	    date, the key will still be included in the zone, but it
	    will not be used to sign it.
	  </p>
	</dd>
<dt><span class="term">-D <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
	  <p>
	    Sets the date on which the key is to be deleted.  After that
	    date, the key will no longer be included in the zone.  (It
	    may remain in the key repository, however.)
	  </p>
	</dd>
<dt><span class="term">-D sync <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
	  <p>
	    Sets the date on which the CDS and CDNSKEY records that match this
	    key are to be deleted.
	  </p>
	</dd>
<dt><span class="term">-i <em class="replaceable"><code>interval</code></em></span></dt>
<dd>
          <p>
            Sets the prepublication interval for a key.  If set, then
            the publication and activation dates must be separated by at least
            this much time.  If the activation date is specified but the
            publication date isn't, then the publication date will default
            to this much time before the activation date; conversely, if
            the publication date is specified but activation date isn't,
            then activation will be set to this much time after publication.
          </p>
          <p>
            If the key is being created as an explicit successor to another
            key, then the default prepublication interval is 30 days;
            otherwise it is zero.
          </p>
          <p>
            As with date offsets, if the argument is followed by one of
            the suffixes 'y', 'mo', 'w', 'd', 'h', or 'mi', then the
            interval is measured in years, months, weeks, days, hours,
            or minutes, respectively.  Without a suffix, the interval is
            measured in seconds.
          </p>
        </dd>
</dl></div>
  </div>


  <div class="refsection">
<a name="id-1.10"></a><h2>GENERATED KEYS</h2>

    <p>
      When <span class="command"><strong>dnssec-keygen</strong></span> completes
      successfully,
      it prints a string of the form <code class="filename">Knnnn.+aaa+iiiii</code>
      to the standard output.  This is an identification string for
      the key it has generated.
    </p>
    <div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem">
	<p><code class="filename">nnnn</code> is the key name.
	</p>
      </li>
<li class="listitem">
	<p><code class="filename">aaa</code> is the numeric representation
	  of the
	  algorithm.
	</p>
      </li>
<li class="listitem">
	<p><code class="filename">iiiii</code> is the key identifier (or
	  footprint).
	</p>
      </li>
</ul></div>
    <p><span class="command"><strong>dnssec-keygen</strong></span>
      creates two files, with names based
      on the printed string.  <code class="filename">Knnnn.+aaa+iiiii.key</code>
      contains the public key, and
      <code class="filename">Knnnn.+aaa+iiiii.private</code> contains the
      private
      key.
    </p>
    <p>
      The <code class="filename">.key</code> file contains a DNS KEY record
      that
      can be inserted into a zone file (directly or with a $INCLUDE
      statement).
    </p>
    <p>
      The <code class="filename">.private</code> file contains
      algorithm-specific
      fields.  For obvious security reasons, this file does not have
      general read permission.
    </p>
    <p>
      Both <code class="filename">.key</code> and <code class="filename">.private</code>
      files are generated for symmetric cryptography algorithms such as
      HMAC-MD5, even though the public and private key are equivalent.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.11"></a><h2>EXAMPLE</h2>

    <p>
      To generate a 768-bit DSA key for the domain
      <strong class="userinput"><code>example.com</code></strong>, the following command would be
      issued:
    </p>
    <p><strong class="userinput"><code>dnssec-keygen -a DSA -b 768 -n ZONE example.com</code></strong>
    </p>
    <p>
      The command would print a string of the form:
    </p>
    <p><strong class="userinput"><code>Kexample.com.+003+26160</code></strong>
    </p>
    <p>
      In this example, <span class="command"><strong>dnssec-keygen</strong></span> creates
      the files <code class="filename">Kexample.com.+003+26160.key</code>
      and
      <code class="filename">Kexample.com.+003+26160.private</code>.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.12"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
	<span class="refentrytitle">dnssec-signzone</span>(8)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>,
      <em class="citetitle">RFC 2539</em>,
      <em class="citetitle">RFC 2845</em>,
      <em class="citetitle">RFC 4034</em>.
    </p>
  </div>

</div></body>
</html>
