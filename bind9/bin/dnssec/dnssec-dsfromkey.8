.\" Copyright (C) 2008-2012, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: dnssec-dsfromkey
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2012-05-02
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DNSSEC\-DSFROMKEY" "8" "2012\-05\-02" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dnssec-dsfromkey \- DNSSEC DS RR generation tool
.SH "SYNOPSIS"
.HP \w'\fBdnssec\-dsfromkey\fR\ 'u
\fBdnssec\-dsfromkey\fR [\fB\-v\ \fR\fB\fIlevel\fR\fR] [\fB\-1\fR] [\fB\-2\fR] [\fB\-a\ \fR\fB\fIalg\fR\fR] [\fB\-C\fR] [\fB\-l\ \fR\fB\fIdomain\fR\fR] [\fB\-T\ \fR\fB\fITTL\fR\fR] {keyfile}
.HP \w'\fBdnssec\-dsfromkey\fR\ 'u
\fBdnssec\-dsfromkey\fR {\-s} [\fB\-1\fR] [\fB\-2\fR] [\fB\-a\ \fR\fB\fIalg\fR\fR] [\fB\-K\ \fR\fB\fIdirectory\fR\fR] [\fB\-l\ \fR\fB\fIdomain\fR\fR] [\fB\-s\fR] [\fB\-c\ \fR\fB\fIclass\fR\fR] [\fB\-T\ \fR\fB\fITTL\fR\fR] [\fB\-f\ \fR\fB\fIfile\fR\fR] [\fB\-A\fR] [\fB\-v\ \fR\fB\fIlevel\fR\fR] {dnsname}
.HP \w'\fBdnssec\-dsfromkey\fR\ 'u
\fBdnssec\-dsfromkey\fR [\fB\-h\fR] [\fB\-V\fR]
.SH "DESCRIPTION"
.PP
\fBdnssec\-dsfromkey\fR
outputs the Delegation Signer (DS) resource record (RR), as defined in RFC 3658 and RFC 4509, for the given key(s)\&.
.SH "OPTIONS"
.PP
\-1
.RS 4
Use SHA\-1 as the digest algorithm (the default is to use both SHA\-1 and SHA\-256)\&.
.RE
.PP
\-2
.RS 4
Use SHA\-256 as the digest algorithm\&.
.RE
.PP
\-a \fIalgorithm\fR
.RS 4
Select the digest algorithm\&. The value of
\fBalgorithm\fR
must be one of SHA\-1 (SHA1), SHA\-256 (SHA256), GOST or SHA\-384 (SHA384)\&. These values are case insensitive\&.
.RE
.PP
\-C
.RS 4
Generate CDS records rather than DS records\&. This is mutually exclusive with generating lookaside records\&.
.RE
.PP
\-T \fITTL\fR
.RS 4
Specifies the TTL of the DS records\&.
.RE
.PP
\-K \fIdirectory\fR
.RS 4
Look for key files (or, in keyset mode,
keyset\-
files) in
\fBdirectory\fR\&.
.RE
.PP
\-f \fIfile\fR
.RS 4
Zone file mode: in place of the keyfile name, the argument is the DNS domain name of a zone master file, which can be read from
\fBfile\fR\&. If the zone name is the same as
\fBfile\fR, then it may be omitted\&.
.sp
If
\fBfile\fR
is set to
"\-", then the zone data is read from the standard input\&. This makes it possible to use the output of the
\fBdig\fR
command as input, as in:
.sp
\fBdig dnskey example\&.com | dnssec\-dsfromkey \-f \- example\&.com\fR
.RE
.PP
\-A
.RS 4
Include ZSKs when generating DS records\&. Without this option, only keys which have the KSK flag set will be converted to DS records and printed\&. Useful only in zone file mode\&.
.RE
.PP
\-l \fIdomain\fR
.RS 4
Generate a DLV set instead of a DS set\&. The specified
\fBdomain\fR
is appended to the name for each record in the set\&. The DNSSEC Lookaside Validation (DLV) RR is described in RFC 4431\&. This is mutually exclusive with generating CDS records\&.
.RE
.PP
\-s
.RS 4
Keyset mode: in place of the keyfile name, the argument is the DNS domain name of a keyset file\&.
.RE
.PP
\-c \fIclass\fR
.RS 4
Specifies the DNS class (default is IN)\&. Useful only in keyset or zone file mode\&.
.RE
.PP
\-v \fIlevel\fR
.RS 4
Sets the debugging level\&.
.RE
.PP
\-h
.RS 4
Prints usage information\&.
.RE
.PP
\-V
.RS 4
Prints version information\&.
.RE
.SH "EXAMPLE"
.PP
To build the SHA\-256 DS RR from the
\fBKexample\&.com\&.+003+26160\fR
keyfile name, the following command would be issued:
.PP
\fBdnssec\-dsfromkey \-2 Kexample\&.com\&.+003+26160\fR
.PP
The command would print something like:
.PP
\fBexample\&.com\&. IN DS 26160 5 2 3A1EADA7A74B8D0BA86726B0C227AA85AB8BBD2B2004F41A868A54F0 C5EA0B94\fR
.SH "FILES"
.PP
The keyfile can be designed by the key identification
Knnnn\&.+aaa+iiiii
or the full file name
Knnnn\&.+aaa+iiiii\&.key
as generated by
dnssec\-keygen(8)\&.
.PP
The keyset file name is built from the
\fBdirectory\fR, the string
keyset\-
and the
\fBdnsname\fR\&.
.SH "CAVEAT"
.PP
A keyfile error can give a "file not found" even if the file exists\&.
.SH "SEE ALSO"
.PP
\fBdnssec-keygen\fR(8),
\fBdnssec-signzone\fR(8),
BIND 9 Administrator Reference Manual,
RFC 3658,
RFC 4431\&.
RFC 4509\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2008-2012, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
