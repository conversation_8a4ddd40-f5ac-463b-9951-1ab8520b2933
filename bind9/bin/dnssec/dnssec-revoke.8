.\" Copyright (C) 2009, 2011, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: dnssec-revoke
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-01-15
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DNSSEC\-REVOKE" "8" "2014\-01\-15" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dnssec-revoke \- set the REVOKED bit on a DNSSEC key
.SH "SYNOPSIS"
.HP \w'\fBdnssec\-revoke\fR\ 'u
\fBdnssec\-revoke\fR [\fB\-hr\fR] [\fB\-v\ \fR\fB\fIlevel\fR\fR] [\fB\-V\fR] [\fB\-K\ \fR\fB\fIdirectory\fR\fR] [\fB\-E\ \fR\fB\fIengine\fR\fR] [\fB\-f\fR] [\fB\-R\fR] {keyfile}
.SH "DESCRIPTION"
.PP
\fBdnssec\-revoke\fR
reads a DNSSEC key file, sets the REVOKED bit on the key as defined in RFC 5011, and creates a new pair of key files containing the now\-revoked key\&.
.SH "OPTIONS"
.PP
\-h
.RS 4
Emit usage message and exit\&.
.RE
.PP
\-K \fIdirectory\fR
.RS 4
Sets the directory in which the key files are to reside\&.
.RE
.PP
\-r
.RS 4
After writing the new keyset files remove the original keyset files\&.
.RE
.PP
\-v \fIlevel\fR
.RS 4
Sets the debugging level\&.
.RE
.PP
\-V
.RS 4
Prints version information\&.
.RE
.PP
\-E \fIengine\fR
.RS 4
Specifies the cryptographic hardware to use, when applicable\&.
.sp
When BIND is built with OpenSSL PKCS#11 support, this defaults to the string "pkcs11", which identifies an OpenSSL engine that can drive a cryptographic accelerator or hardware service module\&. When BIND is built with native PKCS#11 cryptography (\-\-enable\-native\-pkcs11), it defaults to the path of the PKCS#11 provider library specified via "\-\-with\-pkcs11"\&.
.RE
.PP
\-f
.RS 4
Force overwrite: Causes
\fBdnssec\-revoke\fR
to write the new key pair even if a file already exists matching the algorithm and key ID of the revoked key\&.
.RE
.PP
\-R
.RS 4
Print the key tag of the key with the REVOKE bit set but do not revoke the key\&.
.RE
.SH "SEE ALSO"
.PP
\fBdnssec-keygen\fR(8),
BIND 9 Administrator Reference Manual,
RFC 5011\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2009, 2011, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
