<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.dnssec-dsfromkey">
  <info>
    <date>2012-05-02</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>dnssec-dsfromkey</application></refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>dnssec-dsfromkey</application></refname>
    <refpurpose>DNSSEC DS RR generation tool</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2008</year>
      <year>2009</year>
      <year>2010</year>
      <year>2011</year>
      <year>2012</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>dnssec-dsfromkey</command>
      <arg choice="opt" rep="norepeat"><option>-v <replaceable class="parameter">level</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-1</option></arg>
      <arg choice="opt" rep="norepeat"><option>-2</option></arg>
      <arg choice="opt" rep="norepeat"><option>-a <replaceable class="parameter">alg</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-C</option></arg>
      <arg choice="opt" rep="norepeat"><option>-l <replaceable class="parameter">domain</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-T <replaceable class="parameter">TTL</replaceable></option></arg>
      <arg choice="req" rep="norepeat">keyfile</arg>
    </cmdsynopsis>
    <cmdsynopsis sepchar=" ">
      <command>dnssec-dsfromkey</command>
      <arg choice="req" rep="norepeat">-s</arg>
      <arg choice="opt" rep="norepeat"><option>-1</option></arg>
      <arg choice="opt" rep="norepeat"><option>-2</option></arg>
      <arg choice="opt" rep="norepeat"><option>-a <replaceable class="parameter">alg</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-K <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-l <replaceable class="parameter">domain</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-s</option></arg>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">class</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-T <replaceable class="parameter">TTL</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-f <replaceable class="parameter">file</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-A</option></arg>
      <arg choice="opt" rep="norepeat"><option>-v <replaceable class="parameter">level</replaceable></option></arg>
      <arg choice="req" rep="norepeat">dnsname</arg>
   </cmdsynopsis>
    <cmdsynopsis sepchar=" ">
      <command>dnssec-dsfromkey</command>
      <arg choice="opt" rep="norepeat"><option>-h</option></arg>
      <arg choice="opt" rep="norepeat"><option>-V</option></arg>
   </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>dnssec-dsfromkey</command>
      outputs the Delegation Signer (DS) resource record (RR), as defined in
      RFC 3658 and RFC 4509, for the given key(s).
    </para>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
	<term>-1</term>
	<listitem>
	  <para>
	    Use SHA-1 as the digest algorithm (the default is to use
	    both SHA-1 and SHA-256).
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-2</term>
	<listitem>
	  <para>
	    Use SHA-256 as the digest algorithm.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-a <replaceable class="parameter">algorithm</replaceable></term>
	<listitem>
	  <para>
	    Select the digest algorithm. The value of
	    <option>algorithm</option> must be one of SHA-1 (SHA1),
	    SHA-256 (SHA256), GOST or SHA-384 (SHA384).
	    These values are case insensitive.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-C</term>
	<listitem>
	  <para>
	    Generate CDS records rather than DS records.  This is mutually
	    exclusive with generating lookaside records.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-T <replaceable class="parameter">TTL</replaceable></term>
	<listitem>
	  <para>
	    Specifies the TTL of the DS records.
	  </para>
	  </listitem>
      </varlistentry>

      <varlistentry>
	<term>-K <replaceable class="parameter">directory</replaceable></term>
	<listitem>
	  <para>
	    Look for key files (or, in keyset mode,
	    <filename>keyset-</filename> files) in
	    <option>directory</option>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-f <replaceable class="parameter">file</replaceable></term>
	<listitem>
	  <para>
	    Zone file mode: in place of the keyfile name, the argument is
	    the DNS domain name of a zone master file, which can be read
	    from <option>file</option>.  If the zone name is the same as
	    <option>file</option>, then it may be omitted.
	  </para>
	  <para>
	    If <option>file</option> is set to <literal>"-"</literal>, then
	    the zone data is read from the standard input.  This makes it
	    possible to use the output of the <command>dig</command>
	    command as input, as in:
	  </para>
	  <para>
	    <userinput>dig dnskey example.com | dnssec-dsfromkey -f - example.com</userinput>
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
        <term>-A</term>
        <listitem>
          <para>
            Include ZSKs when generating DS records.  Without this option,
            only keys which have the KSK flag set will be converted to DS
            records and printed.  Useful only in zone file mode.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>-l <replaceable class="parameter">domain</replaceable></term>
	<listitem>
	  <para>
	    Generate a DLV set instead of a DS set.  The specified
	    <option>domain</option> is appended to the name for each
	    record in the set.
	    The DNSSEC Lookaside Validation (DLV) RR is described
	    in RFC 4431.  This is mutually exclusive with generating
	    CDS records.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-s</term>
	<listitem>
	  <para>
	    Keyset mode: in place of the keyfile name, the argument is
	    the DNS domain name of a keyset file.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-c <replaceable class="parameter">class</replaceable></term>
	<listitem>
	  <para>
	    Specifies the DNS class (default is IN).  Useful only
	    in keyset or zone file mode.
	  </para>
	  </listitem>
      </varlistentry>

      <varlistentry>
	<term>-v <replaceable class="parameter">level</replaceable></term>
	<listitem>
	  <para>
	    Sets the debugging level.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-h</term>
	<listitem>
	  <para>
	    Prints usage information.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-V</term>
	<listitem>
	  <para>
	    Prints version information.
	  </para>
	</listitem>
      </varlistentry>
    </variablelist>
  </refsection>

  <refsection><info><title>EXAMPLE</title></info>

    <para>
      To build the SHA-256 DS RR from the
      <userinput>Kexample.com.+003+26160</userinput>
      keyfile name, the following command would be issued:
    </para>
    <para><userinput>dnssec-dsfromkey -2 Kexample.com.+003+26160</userinput>
    </para>
    <para>
      The command would print something like:
    </para>
    <para><userinput>example.com. IN DS 26160 5 2 3A1EADA7A74B8D0BA86726B0C227AA85AB8BBD2B2004F41A868A54F0 C5EA0B94</userinput>
    </para>
  </refsection>

  <refsection><info><title>FILES</title></info>

    <para>
      The keyfile can be designed by the key identification
      <filename>Knnnn.+aaa+iiiii</filename> or the full file name
      <filename>Knnnn.+aaa+iiiii.key</filename> as generated by
      <refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>.
    </para>
    <para>
      The keyset file name is built from the <option>directory</option>,
      the string <filename>keyset-</filename> and the
      <option>dnsname</option>.
    </para>
  </refsection>

  <refsection><info><title>CAVEAT</title></info>

    <para>
      A keyfile error can give a "file not found" even if the file exists.
    </para>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
	<refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>dnssec-signzone</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>,
      <citetitle>RFC 3658</citetitle>,
      <citetitle>RFC 4431</citetitle>.
      <citetitle>RFC 4509</citetitle>.
    </para>
  </refsection>

</refentry>
