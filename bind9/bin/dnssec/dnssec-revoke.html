<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2009, 2011, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>dnssec-revoke</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.dnssec-revoke"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">dnssec-revoke</span>
     &#8212; set the REVOKED bit on a DNSSEC key
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">dnssec-revoke</code> 
       [<code class="option">-hr</code>]
       [<code class="option">-v <em class="replaceable"><code>level</code></em></code>]
       [<code class="option">-V</code>]
       [<code class="option">-K <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-E <em class="replaceable"><code>engine</code></em></code>]
       [<code class="option">-f</code>]
       [<code class="option">-R</code>]
       {keyfile}
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>dnssec-revoke</strong></span>
      reads a DNSSEC key file, sets the REVOKED bit on the key as defined
      in RFC 5011, and creates a new pair of key files containing the
      now-revoked key.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-h</span></dt>
<dd>
	  <p>
	    Emit usage message and exit.
	  </p>
        </dd>
<dt><span class="term">-K <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
          <p>
            Sets the directory in which the key files are to reside.
          </p>
        </dd>
<dt><span class="term">-r</span></dt>
<dd>
	  <p>
	    After writing the new keyset files remove the original keyset
	    files.
	  </p>
        </dd>
<dt><span class="term">-v <em class="replaceable"><code>level</code></em></span></dt>
<dd>
          <p>
            Sets the debugging level.
          </p>
        </dd>
<dt><span class="term">-V</span></dt>
<dd>
	  <p>
	    Prints version information.
	  </p>
        </dd>
<dt><span class="term">-E <em class="replaceable"><code>engine</code></em></span></dt>
<dd>
          <p>
            Specifies the cryptographic hardware to use, when applicable.
          </p>
          <p>
            When BIND is built with OpenSSL PKCS#11 support, this defaults
            to the string "pkcs11", which identifies an OpenSSL engine
            that can drive a cryptographic accelerator or hardware service
            module.  When BIND is built with native PKCS#11 cryptography
            (--enable-native-pkcs11), it defaults to the path of the PKCS#11
            provider library specified via "--with-pkcs11".
          </p>
        </dd>
<dt><span class="term">-f</span></dt>
<dd>
          <p>
            Force overwrite: Causes <span class="command"><strong>dnssec-revoke</strong></span> to
            write the new key pair even if a file already exists matching
            the algorithm and key ID of the revoked key.
          </p>
        </dd>
<dt><span class="term">-R</span></dt>
<dd>
          <p>
	    Print the key tag of the key with the REVOKE bit set but do
	    not revoke the key.
          </p>
        </dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
        <span class="refentrytitle">dnssec-keygen</span>(8)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>,
      <em class="citetitle">RFC 5011</em>.
    </p>
  </div>

</div></body>
</html>
