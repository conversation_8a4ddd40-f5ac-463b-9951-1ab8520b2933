<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.dnssec-keyfromlabel">
  <info>
    <date>2014-02-27</date>
  </info>
  <refentryinfo>
    <date>August 27, 2015</date>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>dnssec-keyfromlabel</application></refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>dnssec-keyfromlabel</application></refname>
    <refpurpose>DNSSEC key generation tool</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2008</year>
      <year>2009</year>
      <year>2010</year>
      <year>2011</year>
      <year>2012</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2017</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>dnssec-keyfromlabel</command>
      <arg choice="req" rep="norepeat">-l <replaceable class="parameter">label</replaceable></arg>
      <arg choice="opt" rep="norepeat"><option>-3</option></arg>
      <arg choice="opt" rep="norepeat"><option>-a <replaceable class="parameter">algorithm</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-A <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">class</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-D <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-D sync <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-E <replaceable class="parameter">engine</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-f <replaceable class="parameter">flag</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-G</option></arg>
      <arg choice="opt" rep="norepeat"><option>-I <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-i <replaceable class="parameter">interval</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-k</option></arg>
      <arg choice="opt" rep="norepeat"><option>-K <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-L <replaceable class="parameter">ttl</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-n <replaceable class="parameter">nametype</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-P <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-P sync <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-p <replaceable class="parameter">protocol</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-R <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-S <replaceable class="parameter">key</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-t <replaceable class="parameter">type</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-v <replaceable class="parameter">level</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-V</option></arg>
      <arg choice="opt" rep="norepeat"><option>-y</option></arg>
      <arg choice="req" rep="norepeat">name</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>dnssec-keyfromlabel</command>
      generates a key pair of files that referencing a key object stored
      in a cryptographic hardware service module (HSM).  The private key
      file can be used for DNSSEC signing of zone data as if it were a
      conventional signing key created by <command>dnssec-keygen</command>,
      but the key material is stored within the HSM, and the actual signing
      takes place there.
    </para>
    <para>
      The <option>name</option> of the key is specified on the command
      line.  This must match the name of the zone for which the key is
      being generated.
    </para>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
	<term>-a <replaceable class="parameter">algorithm</replaceable></term>
	<listitem>
	  <para>
	    Selects the cryptographic algorithm.  The value of
	    <option>algorithm</option> must be one of RSAMD5, RSASHA1,
	    DSA, NSEC3RSASHA1, NSEC3DSA, RSASHA256, RSASHA512, ECCGOST,
	    ECDSAP256SHA256, ECDSAP384SHA384, ED25519 or ED448.
	    These values are case insensitive.
	  </para>
	  <para>
	    If no algorithm is specified, then RSASHA1 will be used by
	    default, unless the <option>-3</option> option is specified,
	    in which case NSEC3RSASHA1 will be used instead.  (If
	    <option>-3</option> is used and an algorithm is specified,
	    that algorithm will be checked for compatibility with NSEC3.)
	  </para>
	  <para>
	    Note 1: that for DNSSEC, RSASHA1 is a mandatory to implement
	    algorithm, and DSA is recommended.
	  </para>
	  <para>
	    Note 2: DH automatically sets the -k flag.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-3</term>
	<listitem>
	  <para>
	    Use an NSEC3-capable algorithm to generate a DNSSEC key.
	    If this option is used and no algorithm is explicitly
	    set on the command line, NSEC3RSASHA1 will be used by
	    default.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-E <replaceable class="parameter">engine</replaceable></term>
	<listitem>
	  <para>
	    Specifies the cryptographic hardware to use.
	  </para>
	  <para>
	    When BIND is built with OpenSSL PKCS#11 support, this defaults
	    to the string "pkcs11", which identifies an OpenSSL engine
	    that can drive a cryptographic accelerator or hardware service
	    module.  When BIND is built with native PKCS#11 cryptography
	    (--enable-native-pkcs11), it defaults to the path of the PKCS#11
	    provider library specified via "--with-pkcs11".
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-l <replaceable class="parameter">label</replaceable></term>
	<listitem>
	  <para>
	    Specifies the label for a key pair in the crypto hardware.
	  </para>
	  <para>
	    When <acronym>BIND</acronym> 9 is built with OpenSSL-based
	    PKCS#11 support, the label is an arbitrary string that
	    identifies a particular key.  It may be preceded by an
	    optional OpenSSL engine name, followed by a colon, as in
	    "pkcs11:<replaceable>keylabel</replaceable>".
	  </para>
	  <para>
	    When <acronym>BIND</acronym> 9 is built with native PKCS#11
	    support, the label is a PKCS#11 URI string in the format
	    "pkcs11:<option>keyword</option>=<replaceable>value</replaceable><optional>;<option>keyword</option>=<replaceable>value</replaceable>;...</optional>"
	    Keywords include "token", which identifies the HSM; "object", which
	    identifies the key; and "pin-source", which identifies a file from
	    which the HSM's PIN code can be obtained.  The label will be
	    stored in the on-disk "private" file.
	  </para>
	  <para>
	    If the label contains a
	    <option>pin-source</option> field, tools using the generated
	    key files will be able to use the HSM for signing and other
	    operations without any need for an operator to manually enter
	    a PIN.  Note: Making the HSM's PIN accessible in this manner
	    may reduce the security advantage of using an HSM; be sure
	    this is what you want to do before making use of this feature.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-n <replaceable class="parameter">nametype</replaceable></term>
	<listitem>
	  <para>
	    Specifies the owner type of the key.  The value of
	    <option>nametype</option> must either be ZONE (for a DNSSEC
	    zone key (KEY/DNSKEY)), HOST or ENTITY (for a key associated with
	    a host (KEY)),
	    USER (for a key associated with a user(KEY)) or OTHER (DNSKEY).
	    These values are case insensitive.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-C</term>
	<listitem>
	  <para>
	    Compatibility mode:  generates an old-style key, without
	    any metadata.  By default, <command>dnssec-keyfromlabel</command>
	    will include the key's creation date in the metadata stored
	    with the private key, and other dates may be set there as well
	    (publication date, activation date, etc).  Keys that include
	    this data may be incompatible with older versions of BIND; the
	    <option>-C</option> option suppresses them.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-c <replaceable class="parameter">class</replaceable></term>
	<listitem>
	  <para>
	    Indicates that the DNS record containing the key should have
	    the specified class.  If not specified, class IN is used.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-f <replaceable class="parameter">flag</replaceable></term>
	<listitem>
	  <para>
	    Set the specified flag in the flag field of the KEY/DNSKEY record.
	    The only recognized flags are KSK (Key Signing Key) and REVOKE.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-G</term>
	<listitem>
	  <para>
	    Generate a key, but do not publish it or sign with it.  This
	    option is incompatible with -P and -A.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-h</term>
	<listitem>
	  <para>
	    Prints a short summary of the options and arguments to
	    <command>dnssec-keyfromlabel</command>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-K <replaceable class="parameter">directory</replaceable></term>
	<listitem>
	  <para>
	    Sets the directory in which the key files are to be written.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-k</term>
	<listitem>
	  <para>
	    Generate KEY records rather than DNSKEY records.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-L <replaceable class="parameter">ttl</replaceable></term>
	<listitem>
	  <para>
	    Sets the default TTL to use for this key when it is converted
	    into a DNSKEY RR.  If the key is imported into a zone,
	    this is the TTL that will be used for it, unless there was
	    already a DNSKEY RRset in place, in which case the existing TTL
	    would take precedence.  Setting the default TTL to
	    <literal>0</literal> or <literal>none</literal> removes it.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-p <replaceable class="parameter">protocol</replaceable></term>
	<listitem>
	  <para>
	    Sets the protocol value for the key.  The protocol
	    is a number between 0 and 255.  The default is 3 (DNSSEC).
	    Other possible values for this argument are listed in
	    RFC 2535 and its successors.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-S <replaceable class="parameter">key</replaceable></term>
	<listitem>
	  <para>
	    Generate a key as an explicit successor to an existing key.
	    The name, algorithm, size, and type of the key will be set
	    to match the predecessor. The activation date of the new
	    key will be set to the inactivation date of the existing
	    one. The publication date will be set to the activation
	    date minus the prepublication interval, which defaults to
	    30 days.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-t <replaceable class="parameter">type</replaceable></term>
	<listitem>
	  <para>
	    Indicates the use of the key.  <option>type</option> must be
	    one of AUTHCONF, NOAUTHCONF, NOAUTH, or NOCONF.  The default
	    is AUTHCONF.  AUTH refers to the ability to authenticate
	    data, and CONF the ability to encrypt data.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-v <replaceable class="parameter">level</replaceable></term>
	<listitem>
	  <para>
	    Sets the debugging level.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-V</term>
	<listitem>
	  <para>
	    Prints version information.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-y</term>
	<listitem>
	  <para>
	    Allows DNSSEC key files to be generated even if the key ID
	    would collide with that of an existing key, in the event of
	    either key being revoked.  (This is only safe to use if you
	    are sure you won't be using RFC 5011 trust anchor maintenance
	    with either of the keys involved.)
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>TIMING OPTIONS</title></info>


    <para>
      Dates can be expressed in the format YYYYMMDD or YYYYMMDDHHMMSS.
      If the argument begins with a '+' or '-', it is interpreted as
      an offset from the present time.  For convenience, if such an offset
      is followed by one of the suffixes 'y', 'mo', 'w', 'd', 'h', or 'mi',
      then the offset is computed in years (defined as 365 24-hour days,
      ignoring leap years), months (defined as 30 24-hour days), weeks,
      days, hours, or minutes, respectively.  Without a suffix, the offset
      is computed in seconds.  To explicitly prevent a date from being
      set, use 'none' or 'never'.
    </para>

    <variablelist>
      <varlistentry>
	<term>-P <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which a key is to be published to the zone.
	    After that date, the key will be included in the zone but will
	    not be used to sign it.  If not set, and if the -G option has
	    not been used, the default is "now".
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-P sync <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the CDS and CDNSKEY records which match
	    this key are to be published to the zone.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-A <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the key is to be activated.  After that
	    date, the key will be included in the zone and used to sign
	    it.  If not set, and if the -G option has not been used, the
	    default is "now".
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-R <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the key is to be revoked.  After that
	    date, the key will be flagged as revoked.  It will be included
	    in the zone and will be used to sign it.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-I <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the key is to be retired.  After that
	    date, the key will still be included in the zone, but it
	    will not be used to sign it.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-D <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the key is to be deleted.  After that
	    date, the key will no longer be included in the zone.  (It
	    may remain in the key repository, however.)
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-D sync <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the CDS and CDNSKEY records which match
	    this key are to be deleted.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
        <term>-i <replaceable class="parameter">interval</replaceable></term>
        <listitem>
          <para>
            Sets the prepublication interval for a key.  If set, then
            the publication and activation dates must be separated by at least
            this much time.  If the activation date is specified but the
            publication date isn't, then the publication date will default
            to this much time before the activation date; conversely, if
            the publication date is specified but activation date isn't,
            then activation will be set to this much time after publication.
          </para>
          <para>
            If the key is being created as an explicit successor to another
            key, then the default prepublication interval is 30 days;
            otherwise it is zero.
          </para>
          <para>
            As with date offsets, if the argument is followed by one of
            the suffixes 'y', 'mo', 'w', 'd', 'h', or 'mi', then the
            interval is measured in years, months, weeks, days, hours,
            or minutes, respectively.  Without a suffix, the interval is
            measured in seconds.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>GENERATED KEY FILES</title></info>

    <para>
      When <command>dnssec-keyfromlabel</command> completes
      successfully,
      it prints a string of the form <filename>Knnnn.+aaa+iiiii</filename>
      to the standard output.  This is an identification string for
      the key files it has generated.
    </para>
    <itemizedlist>
      <listitem>
	<para><filename>nnnn</filename> is the key name.
	</para>
      </listitem>
      <listitem>
	<para><filename>aaa</filename> is the numeric representation
	  of the algorithm.
	</para>
      </listitem>
      <listitem>
	<para><filename>iiiii</filename> is the key identifier (or
	  footprint).
	</para>
      </listitem>
    </itemizedlist>
    <para><command>dnssec-keyfromlabel</command>
      creates two files, with names based
      on the printed string.  <filename>Knnnn.+aaa+iiiii.key</filename>
      contains the public key, and
      <filename>Knnnn.+aaa+iiiii.private</filename> contains the
      private key.
    </para>
    <para>
      The <filename>.key</filename> file contains a DNS KEY record
      that
      can be inserted into a zone file (directly or with a $INCLUDE
      statement).
    </para>
    <para>
      The <filename>.private</filename> file contains
      algorithm-specific
      fields.  For obvious security reasons, this file does not have
      general read permission.
    </para>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
	<refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>dnssec-signzone</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>,
      <citetitle>RFC 4034</citetitle>,
      <citetitle>The PKCS#11 URI Scheme (draft-pechanec-pkcs11uri-13)</citetitle>.
    </para>
  </refsection>

</refentry>
