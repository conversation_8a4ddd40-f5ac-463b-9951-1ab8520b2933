# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

# $Id$

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

VERSION=@BIND9_VERSION@

@BIND9_MAKE_INCLUDES@

INFOBLOX_SOURCE_ROOT	=	${top_srcdir}/..
INFOBLOX_PRODUCT_ROOT   =   $(INFOBLOX_SOURCE_ROOT)/products/dns
INFOBLOX_INCLUDES	=	-I$(INFOBLOX_SOURCE_ROOT)/common/server/include \
                                -I$(INFOBLOX_PRODUCT_ROOT)/server/include \
				-I/opt/bloxdb/include \
				-I$(INFOBLOX_SOURCE_ROOT)/products/one/server/include


CINCLUDES =	${DNS_INCLUDES} ${ISC_INCLUDES} @DST_OPENSSL_INC@ ${INFOBLOX_INCLUDES}

CDEFINES =	-DVERSION=\"${VERSION}\" @USE_PKCS11@ @PKCS11_ENGINE@ \
		@CRYPTO@ -DPK11_LIB_LOCATION=\"@PKCS11_PROVIDER@\"
CWARNINGS =

DNSLIBS =	../../lib/dns/libdns.@A@ @DNS_CRYPTO_LIBS@
ISCLIBS =	../../lib/isc/libisc.@A@
ISCNOSYMLIBS =	../../lib/isc/libisc-nosymtbl.@A@

DNSDEPLIBS =	../../lib/dns/libdns.@A@
ISCDEPLIBS =	../../lib/isc/libisc.@A@

DEPLIBS =	${DNSDEPLIBS} ${ISCDEPLIBS}

LIBS =		${DNSLIBS} ${ISCLIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}

NOSYMLIBS =	${DNSLIBS} ${ISCNOSYMLIBS} @LIBS@

# Alphabetically
TARGETS =	dnssec-keygen@EXEEXT@ dnssec-signzone@EXEEXT@ \
		dnssec-keyfromlabel@EXEEXT@ dnssec-dsfromkey@EXEEXT@ \
		dnssec-revoke@EXEEXT@ dnssec-settime@EXEEXT@ \
		dnssec-verify@EXEEXT@ dnssec-importkey@EXEEXT@

include ../../Makefile-infoblox.inc

OBJS =		dnssectool.@O@

SRCS =		dnssec-dsfromkey.c dnssec-keyfromlabel.c dnssec-keygen.c \
		dnssec-revoke.c dnssec-settime.c dnssec-signzone.c \
		dnssec-verify.c dnssec-importkey.c dnssectool.c

MANPAGES =	dnssec-dsfromkey.8 dnssec-keyfromlabel.8 dnssec-keygen.8 \
		dnssec-revoke.8 dnssec-settime.8 dnssec-signzone.8 \
		dnssec-verify.8 dnssec-importkey.8

HTMLPAGES =	dnssec-dsfromkey.html dnssec-keyfromlabel.html \
		dnssec-keygen.html dnssec-revoke.html \
		dnssec-settime.html dnssec-signzone.html \
		dnssec-verify.html dnssec-importkey.html

MANOBJS =	${MANPAGES} ${HTMLPAGES}

@BIND9_MAKE_RULES@

dnssec-dsfromkey@EXEEXT@: dnssec-dsfromkey.@O@ ${OBJS} ${DEPLIBS}
	export BASEOBJS="dnssec-dsfromkey.@O@ ${OBJS}"; \
	${FINALBUILDCMD}

dnssec-keyfromlabel@EXEEXT@: dnssec-keyfromlabel.@O@ ${OBJS} ${DEPLIBS}
	export BASEOBJS="dnssec-keyfromlabel.@O@ ${OBJS}"; \
	${FINALBUILDCMD}

dnssec-keygen@EXEEXT@: dnssec-keygen.@O@ ${OBJS} ${DEPLIBS}
	export BASEOBJS="dnssec-keygen.@O@ ${OBJS}"; \
	${FINALBUILDCMD}

dnssec-signzone.@O@: dnssec-signzone.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} -DVERSION=\"${VERSION}\" \
		-c ${srcdir}/dnssec-signzone.c

dnssec-signzone@EXEEXT@: dnssec-signzone.@O@ ${OBJS} ${DEPLIBS}
	export BASEOBJS="dnssec-signzone.@O@ ${OBJS}"; \
	${FINALBUILDCMD}

dnssec-verify.@O@: dnssec-verify.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} -DVERSION=\"${VERSION}\" \
		-c ${srcdir}/dnssec-verify.c

dnssec-verify@EXEEXT@: dnssec-verify.@O@ ${OBJS} ${DEPLIBS}
	export BASEOBJS="dnssec-verify.@O@ ${OBJS}"; \
	${FINALBUILDCMD}

dnssec-revoke@EXEEXT@: dnssec-revoke.@O@ ${OBJS} ${DEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ \
	dnssec-revoke.@O@ ${OBJS} ${LIBS}

dnssec-settime@EXEEXT@: dnssec-settime.@O@ ${OBJS} ${DEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ \
	dnssec-settime.@O@ ${OBJS} ${LIBS}

dnssec-importkey@EXEEXT@: dnssec-importkey.@O@ ${OBJS} ${DEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ \
	dnssec-importkey.@O@ ${OBJS} ${LIBS}

doc man:: ${MANOBJS}

docclean manclean maintainer-clean::
	rm -f ${MANOBJS}

installdirs:
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${sbindir}
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${mandir}/man8

install:: ${TARGETS} installdirs
	for t in ${TARGETS}; do ${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} $$t ${DESTDIR}${sbindir}; done
	for m in ${MANPAGES}; do ${INSTALL_DATA} ${srcdir}/$$m ${DESTDIR}${mandir}/man8; done

uninstall::
	for m in ${MANPAGES}; do rm -f ${DESTDIR}${mandir}/man8/$$m ; done
	for t in ${TARGETS}; do ${LIBTOOL_MODE_UNINSTALL} rm -f ${DESTDIR}${sbindir}/$$t ; done

clean distclean::
	rm -f ${TARGETS}

