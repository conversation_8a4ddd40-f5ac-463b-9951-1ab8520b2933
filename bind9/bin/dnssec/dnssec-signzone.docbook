<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.dnssec-signzone">
  <info>
    <date>2014-02-18</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>dnssec-signzone</application></refentrytitle>
   <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>dnssec-signzone</application></refname>
    <refpurpose>DNSSEC zone signing tool</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2000</year>
      <year>2001</year>
      <year>2002</year>
      <year>2003</year>
      <year>2004</year>
      <year>2005</year>
      <year>2006</year>
      <year>2007</year>
      <year>2008</year>
      <year>2009</year>
      <year>2011</year>
      <year>2013</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2017</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>dnssec-signzone</command>
      <arg choice="opt" rep="norepeat"><option>-a</option></arg>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">class</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-d <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-D</option></arg>
      <arg choice="opt" rep="norepeat"><option>-E <replaceable class="parameter">engine</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-e <replaceable class="parameter">end-time</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-f <replaceable class="parameter">output-file</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-g</option></arg>
      <arg choice="opt" rep="norepeat"><option>-h</option></arg>
      <arg choice="opt" rep="norepeat"><option>-i <replaceable class="parameter">interval</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-I <replaceable class="parameter">input-format</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-j <replaceable class="parameter">jitter</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-K <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-k <replaceable class="parameter">key</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-L <replaceable class="parameter">serial</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-l <replaceable class="parameter">domain</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-M <replaceable class="parameter">maxttl</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-N <replaceable class="parameter">soa-serial-format</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-o <replaceable class="parameter">origin</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-O <replaceable class="parameter">output-format</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-P</option></arg>
      <arg choice="opt" rep="norepeat"><option>-p</option></arg>
      <arg choice="opt" rep="norepeat"><option>-Q</option></arg>
      <arg choice="opt" rep="norepeat"><option>-R</option></arg>
      <arg choice="opt" rep="norepeat"><option>-r <replaceable class="parameter">randomdev</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-S</option></arg>
      <arg choice="opt" rep="norepeat"><option>-s <replaceable class="parameter">start-time</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-T <replaceable class="parameter">ttl</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-t</option></arg>
      <arg choice="opt" rep="norepeat"><option>-u</option></arg>
      <arg choice="opt" rep="norepeat"><option>-v <replaceable class="parameter">level</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-V</option></arg>
      <arg choice="opt" rep="norepeat"><option>-X <replaceable class="parameter">extended end-time</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-x</option></arg>
      <arg choice="opt" rep="norepeat"><option>-z</option></arg>
      <arg choice="opt" rep="norepeat"><option>-3 <replaceable class="parameter">salt</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-H <replaceable class="parameter">iterations</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-A</option></arg>
      <arg choice="req" rep="norepeat">zonefile</arg>
      <arg rep="repeat" choice="opt">key</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>dnssec-signzone</command>
      signs a zone.  It generates
      NSEC and RRSIG records and produces a signed version of the
      zone. The security status of delegations from the signed zone
      (that is, whether the child zones are secure or not) is
      determined by the presence or absence of a
      <filename>keyset</filename> file for each child zone.
    </para>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
        <term>-a</term>
        <listitem>
          <para>
            Verify all generated signatures.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-c <replaceable class="parameter">class</replaceable></term>
        <listitem>
          <para>
            Specifies the DNS class of the zone.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-C</term>
        <listitem>
          <para>
            Compatibility mode: Generate a
            <filename>keyset-<replaceable>zonename</replaceable></filename>
            file in addition to
            <filename>dsset-<replaceable>zonename</replaceable></filename>
            when signing a zone, for use by older versions of
            <command>dnssec-signzone</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-d <replaceable class="parameter">directory</replaceable></term>
        <listitem>
          <para>
            Look for <filename>dsset-</filename> or
            <filename>keyset-</filename> files in <option>directory</option>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-D</term>
        <listitem>
          <para>
	    Output only those record types automatically managed by
	    <command>dnssec-signzone</command>, i.e. RRSIG, NSEC,
	    NSEC3 and NSEC3PARAM records. If smart signing
	    (<option>-S</option>) is used, DNSKEY records are also
	    included. The resulting file can be included in the original
	    zone file with <command>$INCLUDE</command>. This option
	    cannot be combined with <option>-O raw</option>,
            <option>-O map</option>, or serial number updating.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-E <replaceable class="parameter">engine</replaceable></term>
        <listitem>
          <para>
            When applicable, specifies the hardware to use for
            cryptographic operations, such as a secure key store used
            for signing.
          </para>
          <para>
            When BIND is built with OpenSSL PKCS#11 support, this defaults
            to the string "pkcs11", which identifies an OpenSSL engine
            that can drive a cryptographic accelerator or hardware service
            module.  When BIND is built with native PKCS#11 cryptography
            (--enable-native-pkcs11), it defaults to the path of the PKCS#11
            provider library specified via "--with-pkcs11".
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-g</term>
        <listitem>
          <para>
            Generate DS records for child zones from
            <filename>dsset-</filename> or <filename>keyset-</filename>
            file.  Existing DS records will be removed.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-K <replaceable class="parameter">directory</replaceable></term>
        <listitem>
          <para>
            Key repository: Specify a directory to search for DNSSEC keys.
            If not specified, defaults to the current directory.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-k <replaceable class="parameter">key</replaceable></term>
        <listitem>
          <para>
            Treat specified key as a key signing key ignoring any
            key flags.  This option may be specified multiple times.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-l <replaceable class="parameter">domain</replaceable></term>
        <listitem>
          <para>
            Generate a DLV set in addition to the key (DNSKEY) and DS sets.
            The domain is appended to the name of the records.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-M <replaceable class="parameter">maxttl</replaceable></term>
        <listitem>
          <para>
            Sets the maximum TTL for the signed zone.
            Any TTL higher than <replaceable>maxttl</replaceable> in the
            input zone will be reduced to <replaceable>maxttl</replaceable>
            in the output. This provides certainty as to the largest
            possible TTL in the signed zone, which is useful to know when
            rolling keys because it is the longest possible time before
            signatures that have been retrieved by resolvers will expire
            from resolver caches.  Zones that are signed with this
            option should be configured to use a matching
            <option>max-zone-ttl</option> in <filename>named.conf</filename>.
            (Note: This option is incompatible with <option>-D</option>,
            because it modifies non-DNSSEC data in the output zone.)
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-s <replaceable class="parameter">start-time</replaceable></term>
        <listitem>
          <para>
            Specify the date and time when the generated RRSIG records
            become valid.  This can be either an absolute or relative
            time.  An absolute start time is indicated by a number
            in YYYYMMDDHHMMSS notation; 20000530144500 denotes
            14:45:00 UTC on May 30th, 2000.  A relative start time is
            indicated by +N, which is N seconds from the current time.
            If no <option>start-time</option> is specified, the current
            time minus 1 hour (to allow for clock skew) is used.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-e <replaceable class="parameter">end-time</replaceable></term>
        <listitem>
          <para>
            Specify the date and time when the generated RRSIG records
            expire.  As with <option>start-time</option>, an absolute
            time is indicated in YYYYMMDDHHMMSS notation.  A time relative
            to the start time is indicated with +N, which is N seconds from
            the start time.  A time relative to the current time is
            indicated with now+N.  If no <option>end-time</option> is
            specified, 30 days from the start time is used as a default.
            <option>end-time</option> must be later than
            <option>start-time</option>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-X <replaceable class="parameter">extended end-time</replaceable></term>
        <listitem>
          <para>
            Specify the date and time when the generated RRSIG records
            for the DNSKEY RRset will expire.  This is to be used in cases
            when the DNSKEY signatures need to persist longer than
            signatures on other records; e.g., when the private component
            of the KSK is kept offline and the KSK signature is to be
            refreshed manually.
          </para>
          <para>
            As with <option>start-time</option>, an absolute
            time is indicated in YYYYMMDDHHMMSS notation.  A time relative
            to the start time is indicated with +N, which is N seconds from
            the start time.  A time relative to the current time is
            indicated with now+N.  If no <option>extended end-time</option> is
            specified, the value of <option>end-time</option> is used as
            the default.  (<option>end-time</option>, in turn, defaults to
            30 days from the start time.) <option>extended end-time</option>
            must be later than <option>start-time</option>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-f <replaceable class="parameter">output-file</replaceable></term>
        <listitem>
          <para>
            The name of the output file containing the signed zone.  The
            default is to append <filename>.signed</filename> to
            the input filename.  If <option>output-file</option> is
            set to <literal>"-"</literal>, then the signed zone is
            written to the standard output, with a default output
            format of "full".
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-h</term>
        <listitem>
          <para>
            Prints a short summary of the options and arguments to
            <command>dnssec-signzone</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>-V</term>
        <listitem>
	  <para>
	    Prints version information.
	  </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-i <replaceable class="parameter">interval</replaceable></term>
        <listitem>
          <para>
            When a previously-signed zone is passed as input, records
            may be resigned.  The <option>interval</option> option
            specifies the cycle interval as an offset from the current
            time (in seconds).  If a RRSIG record expires after the
            cycle interval, it is retained.  Otherwise, it is considered
            to be expiring soon, and it will be replaced.
          </para>
          <para>
            The default cycle interval is one quarter of the difference
            between the signature end and start times.  So if neither
            <option>end-time</option> or <option>start-time</option>
            are specified, <command>dnssec-signzone</command>
            generates
            signatures that are valid for 30 days, with a cycle
            interval of 7.5 days.  Therefore, if any existing RRSIG records
            are due to expire in less than 7.5 days, they would be
            replaced.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-I <replaceable class="parameter">input-format</replaceable></term>
        <listitem>
          <para>
            The format of the input zone file.
	    Possible formats are <command>"text"</command> (default),
	    <command>"raw"</command>, and <command>"map"</command>.
	    This option is primarily intended to be used for dynamic
            signed zones so that the dumped zone file in a non-text
            format containing updates can be signed directly.
	    The use of this option does not make much sense for
	    non-dynamic zones.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-j <replaceable class="parameter">jitter</replaceable></term>
        <listitem>
          <para>
            When signing a zone with a fixed signature lifetime, all
            RRSIG records issued at the time of signing expires
            simultaneously.  If the zone is incrementally signed, i.e.
            a previously-signed zone is passed as input to the signer,
            all expired signatures have to be regenerated at about the
            same time.  The <option>jitter</option> option specifies a
            jitter window that will be used to randomize the signature
            expire time, thus spreading incremental signature
            regeneration over time.
          </para>
          <para>
            Signature lifetime jitter also to some extent benefits
            validators and servers by spreading out cache expiration,
            i.e. if large numbers of RRSIGs don't expire at the same time
            from all caches there will be less congestion than if all
            validators need to refetch at mostly the same time.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-L <replaceable class="parameter">serial</replaceable></term>
        <listitem>
          <para>
            When writing a signed zone to "raw" or "map" format, set the
            "source serial" value in the header to the specified serial
            number.  (This is expected to be used primarily for testing
            purposes.)
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-n <replaceable class="parameter">ncpus</replaceable></term>
        <listitem>
          <para>
            Specifies the number of threads to use.  By default, one
            thread is started for each detected CPU.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-N <replaceable class="parameter">soa-serial-format</replaceable></term>
        <listitem>
          <para>
            The SOA serial number format of the signed zone.
	    Possible formats are <command>"keep"</command> (default),
            <command>"increment"</command>, <command>"unixtime"</command>,
            and <command>"date"</command>.
          </para>

          <variablelist>
	    <varlistentry>
	      <term><command>"keep"</command></term>
              <listitem>
                <para>Do not modify the SOA serial number.</para>
	      </listitem>
            </varlistentry>

	    <varlistentry>
	      <term><command>"increment"</command></term>
              <listitem>
                <para>Increment the SOA serial number using RFC 1982
                      arithmetics.</para>
	      </listitem>
            </varlistentry>

	    <varlistentry>
	      <term><command>"unixtime"</command></term>
              <listitem>
                <para>Set the SOA serial number to the number of seconds
	        since epoch.</para>
	      </listitem>
            </varlistentry>

	    <varlistentry>
	      <term><command>"date"</command></term>
              <listitem>
                <para>Set the SOA serial number to today's date in
                YYYYMMDDNN format.</para>
	      </listitem>
            </varlistentry>
	 </variablelist>

        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-o <replaceable class="parameter">origin</replaceable></term>
        <listitem>
          <para>
            The zone origin.  If not specified, the name of the zone file
            is assumed to be the origin.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-O <replaceable class="parameter">output-format</replaceable></term>
        <listitem>
          <para>
            The format of the output file containing the signed zone.
	    Possible formats are <command>"text"</command> (default),
            which is the standard textual representation of the zone;
	    <command>"full"</command>, which is text output in a
            format suitable for processing by external scripts;
            and <command>"map"</command>, <command>"raw"</command>,
            and <command>"raw=N"</command>, which store the zone in
            binary formats for rapid loading by <command>named</command>.
            <command>"raw=N"</command> specifies the format version of
            the raw zone file: if N is 0, the raw file can be read by
            any version of <command>named</command>; if N is 1, the file
            can be read by release 9.9.0 or higher; the default is 1.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-p</term>
        <listitem>
          <para>
            Use pseudo-random data when signing the zone.  This is faster,
            but less secure, than using real random data.  This option
            may be useful when signing large zones or when the entropy
            source is limited.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-P</term>
        <listitem>
          <para>
	    Disable post sign verification tests.
          </para>
          <para>
	    The post sign verification test ensures that for each algorithm
	    in use there is at least one non revoked self signed KSK key,
	    that all revoked KSK keys are self signed, and that all records
	    in the zone are signed by the algorithm.
	    This option skips these tests.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-Q</term>
        <listitem>
          <para>
	    Remove signatures from keys that are no longer active.
          </para>
          <para>
            Normally, when a previously-signed zone is passed as input
            to the signer, and a DNSKEY record has been removed and
            replaced with a new one, signatures from the old key
            that are still within their validity period are retained.
	    This allows the zone to continue to validate with cached
	    copies of the old DNSKEY RRset.  The <option>-Q</option>
            forces <command>dnssec-signzone</command> to remove
            signatures from keys that are no longer active. This
            enables ZSK rollover using the procedure described in
            RFC 4641, section ******* ("Pre-Publish Key Rollover").
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>-R</term>
        <listitem>
          <para>
	    Remove signatures from keys that are no longer published.
          </para>
          <para>
            This option is similar to <option>-Q</option>, except it
            forces <command>dnssec-signzone</command> to signatures from
            keys that are no longer published. This enables ZSK rollover
            using the procedure described in RFC 4641, section 4.2.1.2
            ("Double Signature Zone Signing Key Rollover").
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>-r <replaceable class="parameter">randomdev</replaceable></term>
        <listitem>
          <para>
            Specifies the source of randomness.  If the operating
            system does not provide a <filename>/dev/random</filename>
            or equivalent device, the default source of randomness
            is keyboard input.  <filename>randomdev</filename>
            specifies
            the name of a character device or file containing random
            data to be used instead of the default.  The special value
            <filename>keyboard</filename> indicates that keyboard
            input should be used.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-S</term>
        <listitem>
          <para>
            Smart signing: Instructs <command>dnssec-signzone</command> to
            search the key repository for keys that match the zone being
            signed, and to include them in the zone if appropriate.
          </para>
          <para>
            When a key is found, its timing metadata is examined to
            determine how it should be used, according to the following
            rules.  Each successive rule takes priority over the prior
            ones:
          </para>
          <variablelist>
	    <varlistentry>
              <listitem>
                <para>
                  If no timing metadata has been set for the key, the key is
                  published in the zone and used to sign the zone.
                </para>
	      </listitem>
            </varlistentry>

	    <varlistentry>
              <listitem>
                <para>
                  If the key's publication date is set and is in the past, the
                  key is published in the zone.
                </para>
	      </listitem>
            </varlistentry>

	    <varlistentry>
              <listitem>
                <para>
                  If the key's activation date is set and in the past, the
                  key is published (regardless of publication date) and
                  used to sign the zone.
                </para>
	      </listitem>
            </varlistentry>

	    <varlistentry>
              <listitem>
                <para>
                  If the key's revocation date is set and in the past, and the
                  key is published, then the key is revoked, and the revoked key
                  is used to sign the zone.
                </para>
	      </listitem>
            </varlistentry>

	    <varlistentry>
              <listitem>
                <para>
                  If either of the key's unpublication or deletion dates are set
                  and in the past, the key is NOT published or used to sign the
                  zone, regardless of any other metadata.
                </para>
	      </listitem>
            </varlistentry>
	 </variablelist>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-T <replaceable class="parameter">ttl</replaceable></term>
        <listitem>
          <para>
            Specifies a TTL to be used for new DNSKEY records imported
            into the zone from the key repository.  If not
            specified, the default is the TTL value from the zone's SOA
            record.  This option is ignored when signing without
            <option>-S</option>, since DNSKEY records are not imported
            from the key repository in that case.  It is also ignored if
            there are any pre-existing DNSKEY records at the zone apex,
            in which case new records' TTL values will be set to match
            them, or if any of the imported DNSKEY records had a default
            TTL value.  In the event of a a conflict between TTL values in
            imported keys, the shortest one is used.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-t</term>
        <listitem>
          <para>
            Print statistics at completion.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-u</term>
        <listitem>
          <para>
            Update NSEC/NSEC3 chain when re-signing a previously signed
            zone.  With this option, a zone signed with NSEC can be
            switched to NSEC3, or a zone signed with NSEC3 can
            be switch to NSEC or to NSEC3 with different parameters.
            Without this option, <command>dnssec-signzone</command> will
            retain the existing chain when re-signing.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-v <replaceable class="parameter">level</replaceable></term>
        <listitem>
          <para>
            Sets the debugging level.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-x</term>
        <listitem>
          <para>
            Only sign the DNSKEY RRset with key-signing keys, and omit
            signatures from zone-signing keys.  (This is similar to the
            <command>dnssec-dnskey-kskonly yes;</command> zone option in
            <command>named</command>.)
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-z</term>
        <listitem>
          <para>
            Ignore KSK flag on key when determining what to sign.  This
            causes KSK-flagged keys to sign all records, not just the
            DNSKEY RRset.  (This is similar to the
            <command>update-check-ksk no;</command> zone option in
            <command>named</command>.)
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-3 <replaceable class="parameter">salt</replaceable></term>
        <listitem>
          <para>
            Generate an NSEC3 chain with the given hex encoded salt.
	    A dash (<replaceable class="parameter">salt</replaceable>) can
	    be used to indicate that no salt is to be used when generating		    the NSEC3 chain.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-H <replaceable class="parameter">iterations</replaceable></term>
        <listitem>
          <para>
	    When generating an NSEC3 chain, use this many iterations.  The
	    default is 10.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-A</term>
        <listitem>
          <para>
	    When generating an NSEC3 chain set the OPTOUT flag on all
	    NSEC3 records and do not generate NSEC3 records for insecure
	    delegations.
          </para>
          <para>
	    Using this option twice (i.e., <option>-AA</option>)
	    turns the OPTOUT flag off for all records.  This is useful
	    when using the <option>-u</option> option to modify an NSEC3
	    chain which previously had OPTOUT set.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>zonefile</term>
        <listitem>
          <para>
            The file containing the zone to be signed.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>key</term>
        <listitem>
          <para>
	    Specify which keys should be used to sign the zone.  If
	    no keys are specified, then the zone will be examined
	    for DNSKEY records at the zone apex.  If these are found and
	    there are matching private keys, in the current directory,
	    then these will be used for signing.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>EXAMPLE</title></info>

    <para>
      The following command signs the <userinput>example.com</userinput>
      zone with the DSA key generated by <command>dnssec-keygen</command>
      (Kexample.com.+003+17247).  Because the <command>-S</command> option
      is not being used, the zone's keys must be in the master file
      (<filename>db.example.com</filename>).  This invocation looks
      for <filename>dsset</filename> files, in the current directory,
      so that DS records can be imported from them (<command>-g</command>).
    </para>
<programlisting>% dnssec-signzone -g -o example.com db.example.com \
Kexample.com.+003+17247
db.example.com.signed
%</programlisting>
    <para>
      In the above example, <command>dnssec-signzone</command> creates
      the file <filename>db.example.com.signed</filename>.  This
      file should be referenced in a zone statement in a
      <filename>named.conf</filename> file.
    </para>
    <para>
      This example re-signs a previously signed zone with default parameters.
      The private keys are assumed to be in the current directory.
    </para>
<programlisting>% cp db.example.com.signed db.example.com
% dnssec-signzone -o example.com db.example.com
db.example.com.signed
%</programlisting>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
        <refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>,
      <citetitle>RFC 4033</citetitle>, <citetitle>RFC 4641</citetitle>.
    </para>
  </refsection>

</refentry>
