<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.dnssec-verify">
  <info>
    <date>2014-01-15</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>dnssec-verify</application></refentrytitle>
   <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>dnssec-verify</application></refname>
    <refpurpose>DNSSEC zone verification tool</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2012</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>dnssec-verify</command>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">class</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-E <replaceable class="parameter">engine</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-I <replaceable class="parameter">input-format</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-o <replaceable class="parameter">origin</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-v <replaceable class="parameter">level</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-V</option></arg>
      <arg choice="opt" rep="norepeat"><option>-x</option></arg>
      <arg choice="opt" rep="norepeat"><option>-z</option></arg>
      <arg choice="req" rep="norepeat">zonefile</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>dnssec-verify</command>
      verifies that a zone is fully signed for each algorithm found
      in the DNSKEY RRset for the zone, and that the NSEC / NSEC3
      chains are complete.
    </para>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
        <term>-c <replaceable class="parameter">class</replaceable></term>
        <listitem>
          <para>
            Specifies the DNS class of the zone.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-E <replaceable class="parameter">engine</replaceable></term>
        <listitem>
          <para>
            Specifies the cryptographic hardware to use, when applicable.
          </para>
          <para>
            When BIND is built with OpenSSL PKCS#11 support, this defaults
            to the string "pkcs11", which identifies an OpenSSL engine
            that can drive a cryptographic accelerator or hardware service
            module.  When BIND is built with native PKCS#11 cryptography
            (--enable-native-pkcs11), it defaults to the path of the PKCS#11
            provider library specified via "--with-pkcs11".
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-I <replaceable class="parameter">input-format</replaceable></term>
        <listitem>
          <para>
            The format of the input zone file.
	    Possible formats are <command>"text"</command> (default)
	    and <command>"raw"</command>.
	    This option is primarily intended to be used for dynamic
            signed zones so that the dumped zone file in a non-text
            format containing updates can be verified independently.
	    The use of this option does not make much sense for
	    non-dynamic zones.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-o <replaceable class="parameter">origin</replaceable></term>
        <listitem>
          <para>
            The zone origin.  If not specified, the name of the zone file
            is assumed to be the origin.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-v <replaceable class="parameter">level</replaceable></term>
        <listitem>
          <para>
            Sets the debugging level.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>-V</term>
        <listitem>
	  <para>
	    Prints version information.
	  </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-x</term>
        <listitem>
          <para>
            Only verify that the DNSKEY RRset is signed with key-signing
            keys.  Without this flag, it is assumed that the DNSKEY RRset
            will be signed by all active keys.  When this flag is set,
            it will not be an error if the DNSKEY RRset is not signed
            by zone-signing keys.  This corresponds to the <option>-x</option>
            option in <command>dnssec-signzone</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>-z</term>
	<listitem>
	  <para>
	    Ignore the KSK flag on the keys when determining whether
            the zone if correctly signed.  Without this flag it is
	    assumed that there will be a non-revoked, self-signed
	    DNSKEY with the KSK flag set for each algorithm and
	    that RRsets other than DNSKEY RRset will be signed with
            a different DNSKEY without the KSK flag set.
	  </para>
	  <para>
	    With this flag set, we only require that for each algorithm,
            there will be at least one non-revoked, self-signed DNSKEY,
            regardless of the KSK flag state, and that other RRsets
	    will be signed by a non-revoked key for the same algorithm
            that includes the self-signed key; the same key may be used
            for both purposes.  This corresponds to the <option>-z</option>
            option in <command>dnssec-signzone</command>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
        <term>zonefile</term>
        <listitem>
          <para>
            The file containing the zone to be signed.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para>
      <citerefentry>
        <refentrytitle>dnssec-signzone</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>,
      <citetitle>RFC 4033</citetitle>.
    </para>
  </refsection>

</refentry>
