.\" Copyright (C) 2000-2009, 2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: dnssec-signzone
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-02-18
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DNSSEC\-SIGNZONE" "8" "2014\-02\-18" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dnssec-signzone \- DNSSEC zone signing tool
.SH "SYNOPSIS"
.HP \w'\fBdnssec\-signzone\fR\ 'u
\fBdnssec\-signzone\fR [\fB\-a\fR] [\fB\-c\ \fR\fB\fIclass\fR\fR] [\fB\-d\ \fR\fB\fIdirectory\fR\fR] [\fB\-D\fR] [\fB\-E\ \fR\fB\fIengine\fR\fR] [\fB\-e\ \fR\fB\fIend\-time\fR\fR] [\fB\-f\ \fR\fB\fIoutput\-file\fR\fR] [\fB\-g\fR] [\fB\-h\fR] [\fB\-i\ \fR\fB\fIinterval\fR\fR] [\fB\-I\ \fR\fB\fIinput\-format\fR\fR] [\fB\-j\ \fR\fB\fIjitter\fR\fR] [\fB\-K\ \fR\fB\fIdirectory\fR\fR] [\fB\-k\ \fR\fB\fIkey\fR\fR] [\fB\-L\ \fR\fB\fIserial\fR\fR] [\fB\-l\ \fR\fB\fIdomain\fR\fR] [\fB\-M\ \fR\fB\fImaxttl\fR\fR] [\fB\-N\ \fR\fB\fIsoa\-serial\-format\fR\fR] [\fB\-o\ \fR\fB\fIorigin\fR\fR] [\fB\-O\ \fR\fB\fIoutput\-format\fR\fR] [\fB\-P\fR] [\fB\-p\fR] [\fB\-Q\fR] [\fB\-R\fR] [\fB\-r\ \fR\fB\fIrandomdev\fR\fR] [\fB\-S\fR] [\fB\-s\ \fR\fB\fIstart\-time\fR\fR] [\fB\-T\ \fR\fB\fIttl\fR\fR] [\fB\-t\fR] [\fB\-u\fR] [\fB\-v\ \fR\fB\fIlevel\fR\fR] [\fB\-V\fR] [\fB\-X\ \fR\fB\fIextended\ end\-time\fR\fR] [\fB\-x\fR] [\fB\-z\fR] [\fB\-3\ \fR\fB\fIsalt\fR\fR] [\fB\-H\ \fR\fB\fIiterations\fR\fR] [\fB\-A\fR] {zonefile} [key...]
.SH "DESCRIPTION"
.PP
\fBdnssec\-signzone\fR
signs a zone\&. It generates NSEC and RRSIG records and produces a signed version of the zone\&. The security status of delegations from the signed zone (that is, whether the child zones are secure or not) is determined by the presence or absence of a
keyset
file for each child zone\&.
.SH "OPTIONS"
.PP
\-a
.RS 4
Verify all generated signatures\&.
.RE
.PP
\-c \fIclass\fR
.RS 4
Specifies the DNS class of the zone\&.
.RE
.PP
\-C
.RS 4
Compatibility mode: Generate a
keyset\-\fIzonename\fR
file in addition to
dsset\-\fIzonename\fR
when signing a zone, for use by older versions of
\fBdnssec\-signzone\fR\&.
.RE
.PP
\-d \fIdirectory\fR
.RS 4
Look for
dsset\-
or
keyset\-
files in
\fBdirectory\fR\&.
.RE
.PP
\-D
.RS 4
Output only those record types automatically managed by
\fBdnssec\-signzone\fR, i\&.e\&. RRSIG, NSEC, NSEC3 and NSEC3PARAM records\&. If smart signing (\fB\-S\fR) is used, DNSKEY records are also included\&. The resulting file can be included in the original zone file with
\fB$INCLUDE\fR\&. This option cannot be combined with
\fB\-O raw\fR,
\fB\-O map\fR, or serial number updating\&.
.RE
.PP
\-E \fIengine\fR
.RS 4
When applicable, specifies the hardware to use for cryptographic operations, such as a secure key store used for signing\&.
.sp
When BIND is built with OpenSSL PKCS#11 support, this defaults to the string "pkcs11", which identifies an OpenSSL engine that can drive a cryptographic accelerator or hardware service module\&. When BIND is built with native PKCS#11 cryptography (\-\-enable\-native\-pkcs11), it defaults to the path of the PKCS#11 provider library specified via "\-\-with\-pkcs11"\&.
.RE
.PP
\-g
.RS 4
Generate DS records for child zones from
dsset\-
or
keyset\-
file\&. Existing DS records will be removed\&.
.RE
.PP
\-K \fIdirectory\fR
.RS 4
Key repository: Specify a directory to search for DNSSEC keys\&. If not specified, defaults to the current directory\&.
.RE
.PP
\-k \fIkey\fR
.RS 4
Treat specified key as a key signing key ignoring any key flags\&. This option may be specified multiple times\&.
.RE
.PP
\-l \fIdomain\fR
.RS 4
Generate a DLV set in addition to the key (DNSKEY) and DS sets\&. The domain is appended to the name of the records\&.
.RE
.PP
\-M \fImaxttl\fR
.RS 4
Sets the maximum TTL for the signed zone\&. Any TTL higher than
\fImaxttl\fR
in the input zone will be reduced to
\fImaxttl\fR
in the output\&. This provides certainty as to the largest possible TTL in the signed zone, which is useful to know when rolling keys because it is the longest possible time before signatures that have been retrieved by resolvers will expire from resolver caches\&. Zones that are signed with this option should be configured to use a matching
\fBmax\-zone\-ttl\fR
in
named\&.conf\&. (Note: This option is incompatible with
\fB\-D\fR, because it modifies non\-DNSSEC data in the output zone\&.)
.RE
.PP
\-s \fIstart\-time\fR
.RS 4
Specify the date and time when the generated RRSIG records become valid\&. This can be either an absolute or relative time\&. An absolute start time is indicated by a number in YYYYMMDDHHMMSS notation; 20000530144500 denotes 14:45:00 UTC on May 30th, 2000\&. A relative start time is indicated by +N, which is N seconds from the current time\&. If no
\fBstart\-time\fR
is specified, the current time minus 1 hour (to allow for clock skew) is used\&.
.RE
.PP
\-e \fIend\-time\fR
.RS 4
Specify the date and time when the generated RRSIG records expire\&. As with
\fBstart\-time\fR, an absolute time is indicated in YYYYMMDDHHMMSS notation\&. A time relative to the start time is indicated with +N, which is N seconds from the start time\&. A time relative to the current time is indicated with now+N\&. If no
\fBend\-time\fR
is specified, 30 days from the start time is used as a default\&.
\fBend\-time\fR
must be later than
\fBstart\-time\fR\&.
.RE
.PP
\-X \fIextended end\-time\fR
.RS 4
Specify the date and time when the generated RRSIG records for the DNSKEY RRset will expire\&. This is to be used in cases when the DNSKEY signatures need to persist longer than signatures on other records; e\&.g\&., when the private component of the KSK is kept offline and the KSK signature is to be refreshed manually\&.
.sp
As with
\fBstart\-time\fR, an absolute time is indicated in YYYYMMDDHHMMSS notation\&. A time relative to the start time is indicated with +N, which is N seconds from the start time\&. A time relative to the current time is indicated with now+N\&. If no
\fBextended end\-time\fR
is specified, the value of
\fBend\-time\fR
is used as the default\&. (\fBend\-time\fR, in turn, defaults to 30 days from the start time\&.)
\fBextended end\-time\fR
must be later than
\fBstart\-time\fR\&.
.RE
.PP
\-f \fIoutput\-file\fR
.RS 4
The name of the output file containing the signed zone\&. The default is to append
\&.signed
to the input filename\&. If
\fBoutput\-file\fR
is set to
"\-", then the signed zone is written to the standard output, with a default output format of "full"\&.
.RE
.PP
\-h
.RS 4
Prints a short summary of the options and arguments to
\fBdnssec\-signzone\fR\&.
.RE
.PP
\-V
.RS 4
Prints version information\&.
.RE
.PP
\-i \fIinterval\fR
.RS 4
When a previously\-signed zone is passed as input, records may be resigned\&. The
\fBinterval\fR
option specifies the cycle interval as an offset from the current time (in seconds)\&. If a RRSIG record expires after the cycle interval, it is retained\&. Otherwise, it is considered to be expiring soon, and it will be replaced\&.
.sp
The default cycle interval is one quarter of the difference between the signature end and start times\&. So if neither
\fBend\-time\fR
or
\fBstart\-time\fR
are specified,
\fBdnssec\-signzone\fR
generates signatures that are valid for 30 days, with a cycle interval of 7\&.5 days\&. Therefore, if any existing RRSIG records are due to expire in less than 7\&.5 days, they would be replaced\&.
.RE
.PP
\-I \fIinput\-format\fR
.RS 4
The format of the input zone file\&. Possible formats are
\fB"text"\fR
(default),
\fB"raw"\fR, and
\fB"map"\fR\&. This option is primarily intended to be used for dynamic signed zones so that the dumped zone file in a non\-text format containing updates can be signed directly\&. The use of this option does not make much sense for non\-dynamic zones\&.
.RE
.PP
\-j \fIjitter\fR
.RS 4
When signing a zone with a fixed signature lifetime, all RRSIG records issued at the time of signing expires simultaneously\&. If the zone is incrementally signed, i\&.e\&. a previously\-signed zone is passed as input to the signer, all expired signatures have to be regenerated at about the same time\&. The
\fBjitter\fR
option specifies a jitter window that will be used to randomize the signature expire time, thus spreading incremental signature regeneration over time\&.
.sp
Signature lifetime jitter also to some extent benefits validators and servers by spreading out cache expiration, i\&.e\&. if large numbers of RRSIGs don\*(Aqt expire at the same time from all caches there will be less congestion than if all validators need to refetch at mostly the same time\&.
.RE
.PP
\-L \fIserial\fR
.RS 4
When writing a signed zone to "raw" or "map" format, set the "source serial" value in the header to the specified serial number\&. (This is expected to be used primarily for testing purposes\&.)
.RE
.PP
\-n \fIncpus\fR
.RS 4
Specifies the number of threads to use\&. By default, one thread is started for each detected CPU\&.
.RE
.PP
\-N \fIsoa\-serial\-format\fR
.RS 4
The SOA serial number format of the signed zone\&. Possible formats are
\fB"keep"\fR
(default),
\fB"increment"\fR,
\fB"unixtime"\fR, and
\fB"date"\fR\&.
.PP
\fB"keep"\fR
.RS 4
Do not modify the SOA serial number\&.
.RE
.PP
\fB"increment"\fR
.RS 4
Increment the SOA serial number using RFC 1982 arithmetics\&.
.RE
.PP
\fB"unixtime"\fR
.RS 4
Set the SOA serial number to the number of seconds since epoch\&.
.RE
.PP
\fB"date"\fR
.RS 4
Set the SOA serial number to today\*(Aqs date in YYYYMMDDNN format\&.
.RE
.RE
.PP
\-o \fIorigin\fR
.RS 4
The zone origin\&. If not specified, the name of the zone file is assumed to be the origin\&.
.RE
.PP
\-O \fIoutput\-format\fR
.RS 4
The format of the output file containing the signed zone\&. Possible formats are
\fB"text"\fR
(default), which is the standard textual representation of the zone;
\fB"full"\fR, which is text output in a format suitable for processing by external scripts; and
\fB"map"\fR,
\fB"raw"\fR, and
\fB"raw=N"\fR, which store the zone in binary formats for rapid loading by
\fBnamed\fR\&.
\fB"raw=N"\fR
specifies the format version of the raw zone file: if N is 0, the raw file can be read by any version of
\fBnamed\fR; if N is 1, the file can be read by release 9\&.9\&.0 or higher; the default is 1\&.
.RE
.PP
\-p
.RS 4
Use pseudo\-random data when signing the zone\&. This is faster, but less secure, than using real random data\&. This option may be useful when signing large zones or when the entropy source is limited\&.
.RE
.PP
\-P
.RS 4
Disable post sign verification tests\&.
.sp
The post sign verification test ensures that for each algorithm in use there is at least one non revoked self signed KSK key, that all revoked KSK keys are self signed, and that all records in the zone are signed by the algorithm\&. This option skips these tests\&.
.RE
.PP
\-Q
.RS 4
Remove signatures from keys that are no longer active\&.
.sp
Normally, when a previously\-signed zone is passed as input to the signer, and a DNSKEY record has been removed and replaced with a new one, signatures from the old key that are still within their validity period are retained\&. This allows the zone to continue to validate with cached copies of the old DNSKEY RRset\&. The
\fB\-Q\fR
forces
\fBdnssec\-signzone\fR
to remove signatures from keys that are no longer active\&. This enables ZSK rollover using the procedure described in RFC 4641, section 4\&.2\&.1\&.1 ("Pre\-Publish Key Rollover")\&.
.RE
.PP
\-R
.RS 4
Remove signatures from keys that are no longer published\&.
.sp
This option is similar to
\fB\-Q\fR, except it forces
\fBdnssec\-signzone\fR
to signatures from keys that are no longer published\&. This enables ZSK rollover using the procedure described in RFC 4641, section 4\&.2\&.1\&.2 ("Double Signature Zone Signing Key Rollover")\&.
.RE
.PP
\-r \fIrandomdev\fR
.RS 4
Specifies the source of randomness\&. If the operating system does not provide a
/dev/random
or equivalent device, the default source of randomness is keyboard input\&.
randomdev
specifies the name of a character device or file containing random data to be used instead of the default\&. The special value
keyboard
indicates that keyboard input should be used\&.
.RE
.PP
\-S
.RS 4
Smart signing: Instructs
\fBdnssec\-signzone\fR
to search the key repository for keys that match the zone being signed, and to include them in the zone if appropriate\&.
.sp
When a key is found, its timing metadata is examined to determine how it should be used, according to the following rules\&. Each successive rule takes priority over the prior ones:
.PP
.RS 4
If no timing metadata has been set for the key, the key is published in the zone and used to sign the zone\&.
.RE
.PP
.RS 4
If the key\*(Aqs publication date is set and is in the past, the key is published in the zone\&.
.RE
.PP
.RS 4
If the key\*(Aqs activation date is set and in the past, the key is published (regardless of publication date) and used to sign the zone\&.
.RE
.PP
.RS 4
If the key\*(Aqs revocation date is set and in the past, and the key is published, then the key is revoked, and the revoked key is used to sign the zone\&.
.RE
.PP
.RS 4
If either of the key\*(Aqs unpublication or deletion dates are set and in the past, the key is NOT published or used to sign the zone, regardless of any other metadata\&.
.RE
.RE
.PP
\-T \fIttl\fR
.RS 4
Specifies a TTL to be used for new DNSKEY records imported into the zone from the key repository\&. If not specified, the default is the TTL value from the zone\*(Aqs SOA record\&. This option is ignored when signing without
\fB\-S\fR, since DNSKEY records are not imported from the key repository in that case\&. It is also ignored if there are any pre\-existing DNSKEY records at the zone apex, in which case new records\*(Aq TTL values will be set to match them, or if any of the imported DNSKEY records had a default TTL value\&. In the event of a a conflict between TTL values in imported keys, the shortest one is used\&.
.RE
.PP
\-t
.RS 4
Print statistics at completion\&.
.RE
.PP
\-u
.RS 4
Update NSEC/NSEC3 chain when re\-signing a previously signed zone\&. With this option, a zone signed with NSEC can be switched to NSEC3, or a zone signed with NSEC3 can be switch to NSEC or to NSEC3 with different parameters\&. Without this option,
\fBdnssec\-signzone\fR
will retain the existing chain when re\-signing\&.
.RE
.PP
\-v \fIlevel\fR
.RS 4
Sets the debugging level\&.
.RE
.PP
\-x
.RS 4
Only sign the DNSKEY RRset with key\-signing keys, and omit signatures from zone\-signing keys\&. (This is similar to the
\fBdnssec\-dnskey\-kskonly yes;\fR
zone option in
\fBnamed\fR\&.)
.RE
.PP
\-z
.RS 4
Ignore KSK flag on key when determining what to sign\&. This causes KSK\-flagged keys to sign all records, not just the DNSKEY RRset\&. (This is similar to the
\fBupdate\-check\-ksk no;\fR
zone option in
\fBnamed\fR\&.)
.RE
.PP
\-3 \fIsalt\fR
.RS 4
Generate an NSEC3 chain with the given hex encoded salt\&. A dash (\fIsalt\fR) can be used to indicate that no salt is to be used when generating the NSEC3 chain\&.
.RE
.PP
\-H \fIiterations\fR
.RS 4
When generating an NSEC3 chain, use this many iterations\&. The default is 10\&.
.RE
.PP
\-A
.RS 4
When generating an NSEC3 chain set the OPTOUT flag on all NSEC3 records and do not generate NSEC3 records for insecure delegations\&.
.sp
Using this option twice (i\&.e\&.,
\fB\-AA\fR) turns the OPTOUT flag off for all records\&. This is useful when using the
\fB\-u\fR
option to modify an NSEC3 chain which previously had OPTOUT set\&.
.RE
.PP
zonefile
.RS 4
The file containing the zone to be signed\&.
.RE
.PP
key
.RS 4
Specify which keys should be used to sign the zone\&. If no keys are specified, then the zone will be examined for DNSKEY records at the zone apex\&. If these are found and there are matching private keys, in the current directory, then these will be used for signing\&.
.RE
.SH "EXAMPLE"
.PP
The following command signs the
\fBexample\&.com\fR
zone with the DSA key generated by
\fBdnssec\-keygen\fR
(Kexample\&.com\&.+003+17247)\&. Because the
\fB\-S\fR
option is not being used, the zone\*(Aqs keys must be in the master file (db\&.example\&.com)\&. This invocation looks for
dsset
files, in the current directory, so that DS records can be imported from them (\fB\-g\fR)\&.
.sp
.if n \{\
.RS 4
.\}
.nf
% dnssec\-signzone \-g \-o example\&.com db\&.example\&.com \e
Kexample\&.com\&.+003+17247
db\&.example\&.com\&.signed
%
.fi
.if n \{\
.RE
.\}
.PP
In the above example,
\fBdnssec\-signzone\fR
creates the file
db\&.example\&.com\&.signed\&. This file should be referenced in a zone statement in a
named\&.conf
file\&.
.PP
This example re\-signs a previously signed zone with default parameters\&. The private keys are assumed to be in the current directory\&.
.sp
.if n \{\
.RS 4
.\}
.nf
% cp db\&.example\&.com\&.signed db\&.example\&.com
% dnssec\-signzone \-o example\&.com db\&.example\&.com
db\&.example\&.com\&.signed
%
.fi
.if n \{\
.RE
.\}
.SH "SEE ALSO"
.PP
\fBdnssec-keygen\fR(8),
BIND 9 Administrator Reference Manual,
RFC 4033,
RFC 4641\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2000-2009, 2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
.br
