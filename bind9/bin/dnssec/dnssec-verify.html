<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2012, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>dnssec-verify</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.dnssec-verify"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">dnssec-verify</span>
     &#8212; DNSSEC zone verification tool
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">dnssec-verify</code> 
       [<code class="option">-c <em class="replaceable"><code>class</code></em></code>]
       [<code class="option">-E <em class="replaceable"><code>engine</code></em></code>]
       [<code class="option">-I <em class="replaceable"><code>input-format</code></em></code>]
       [<code class="option">-o <em class="replaceable"><code>origin</code></em></code>]
       [<code class="option">-v <em class="replaceable"><code>level</code></em></code>]
       [<code class="option">-V</code>]
       [<code class="option">-x</code>]
       [<code class="option">-z</code>]
       {zonefile}
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>dnssec-verify</strong></span>
      verifies that a zone is fully signed for each algorithm found
      in the DNSKEY RRset for the zone, and that the NSEC / NSEC3
      chains are complete.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-c <em class="replaceable"><code>class</code></em></span></dt>
<dd>
          <p>
            Specifies the DNS class of the zone.
          </p>
        </dd>
<dt><span class="term">-E <em class="replaceable"><code>engine</code></em></span></dt>
<dd>
          <p>
            Specifies the cryptographic hardware to use, when applicable.
          </p>
          <p>
            When BIND is built with OpenSSL PKCS#11 support, this defaults
            to the string "pkcs11", which identifies an OpenSSL engine
            that can drive a cryptographic accelerator or hardware service
            module.  When BIND is built with native PKCS#11 cryptography
            (--enable-native-pkcs11), it defaults to the path of the PKCS#11
            provider library specified via "--with-pkcs11".
          </p>
        </dd>
<dt><span class="term">-I <em class="replaceable"><code>input-format</code></em></span></dt>
<dd>
          <p>
            The format of the input zone file.
	    Possible formats are <span class="command"><strong>"text"</strong></span> (default)
	    and <span class="command"><strong>"raw"</strong></span>.
	    This option is primarily intended to be used for dynamic
            signed zones so that the dumped zone file in a non-text
            format containing updates can be verified independently.
	    The use of this option does not make much sense for
	    non-dynamic zones.
          </p>
        </dd>
<dt><span class="term">-o <em class="replaceable"><code>origin</code></em></span></dt>
<dd>
          <p>
            The zone origin.  If not specified, the name of the zone file
            is assumed to be the origin.
          </p>
        </dd>
<dt><span class="term">-v <em class="replaceable"><code>level</code></em></span></dt>
<dd>
          <p>
            Sets the debugging level.
          </p>
        </dd>
<dt><span class="term">-V</span></dt>
<dd>
	  <p>
	    Prints version information.
	  </p>
        </dd>
<dt><span class="term">-x</span></dt>
<dd>
          <p>
            Only verify that the DNSKEY RRset is signed with key-signing
            keys.  Without this flag, it is assumed that the DNSKEY RRset
            will be signed by all active keys.  When this flag is set,
            it will not be an error if the DNSKEY RRset is not signed
            by zone-signing keys.  This corresponds to the <code class="option">-x</code>
            option in <span class="command"><strong>dnssec-signzone</strong></span>.
          </p>
        </dd>
<dt><span class="term">-z</span></dt>
<dd>
	  <p>
	    Ignore the KSK flag on the keys when determining whether
            the zone if correctly signed.  Without this flag it is
	    assumed that there will be a non-revoked, self-signed
	    DNSKEY with the KSK flag set for each algorithm and
	    that RRsets other than DNSKEY RRset will be signed with
            a different DNSKEY without the KSK flag set.
	  </p>
	  <p>
	    With this flag set, we only require that for each algorithm,
            there will be at least one non-revoked, self-signed DNSKEY,
            regardless of the KSK flag state, and that other RRsets
	    will be signed by a non-revoked key for the same algorithm
            that includes the self-signed key; the same key may be used
            for both purposes.  This corresponds to the <code class="option">-z</code>
            option in <span class="command"><strong>dnssec-signzone</strong></span>.
	  </p>
	</dd>
<dt><span class="term">zonefile</span></dt>
<dd>
          <p>
            The file containing the zone to be signed.
          </p>
        </dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>SEE ALSO</h2>

    <p>
      <span class="citerefentry">
        <span class="refentrytitle">dnssec-signzone</span>(8)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>,
      <em class="citetitle">RFC 4033</em>.
    </p>
  </div>

</div></body>
</html>
