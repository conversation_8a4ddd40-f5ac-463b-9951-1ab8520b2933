.\" Copyright (C) 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: dnssec-importkey
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: August 21, 2015
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DNSSEC\-IMPORTKEY" "8" "August 21, 2015" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dnssec-importkey \- import DNSKEY records from external systems so they can be managed
.SH "SYNOPSIS"
.HP \w'\fBdnssec\-importkey\fR\ 'u
\fBdnssec\-importkey\fR [\fB\-K\ \fR\fB\fIdirectory\fR\fR] [\fB\-L\ \fR\fB\fIttl\fR\fR] [\fB\-P\ \fR\fB\fIdate/offset\fR\fR] [\fB\-P\ sync\ \fR\fB\fIdate/offset\fR\fR] [\fB\-D\ \fR\fB\fIdate/offset\fR\fR] [\fB\-D\ sync\ \fR\fB\fIdate/offset\fR\fR] [\fB\-h\fR] [\fB\-v\ \fR\fB\fIlevel\fR\fR] [\fB\-V\fR] {\fBkeyfile\fR}
.HP \w'\fBdnssec\-importkey\fR\ 'u
\fBdnssec\-importkey\fR {\fB\-f\ \fR\fB\fIfilename\fR\fR} [\fB\-K\ \fR\fB\fIdirectory\fR\fR] [\fB\-L\ \fR\fB\fIttl\fR\fR] [\fB\-P\ \fR\fB\fIdate/offset\fR\fR] [\fB\-P\ sync\ \fR\fB\fIdate/offset\fR\fR] [\fB\-D\ \fR\fB\fIdate/offset\fR\fR] [\fB\-D\ sync\ \fR\fB\fIdate/offset\fR\fR] [\fB\-h\fR] [\fB\-v\ \fR\fB\fIlevel\fR\fR] [\fB\-V\fR] [\fBdnsname\fR]
.SH "DESCRIPTION"
.PP
\fBdnssec\-importkey\fR
reads a public DNSKEY record and generates a pair of \&.key/\&.private files\&. The DNSKEY record may be read from an existing \&.key file, in which case a corresponding \&.private file will be generated, or it may be read from any other file or from the standard input, in which case both \&.key and \&.private files will be generated\&.
.PP
The newly\-created \&.private file does
\fInot\fR
contain private key data, and cannot be used for signing\&. However, having a \&.private file makes it possible to set publication (\fB\-P\fR) and deletion (\fB\-D\fR) times for the key, which means the public key can be added to and removed from the DNSKEY RRset on schedule even if the true private key is stored offline\&.
.SH "OPTIONS"
.PP
\-f \fIfilename\fR
.RS 4
Zone file mode: instead of a public keyfile name, the argument is the DNS domain name of a zone master file, which can be read from
\fBfile\fR\&. If the domain name is the same as
\fBfile\fR, then it may be omitted\&.
.sp
If
\fBfile\fR
is set to
"\-", then the zone data is read from the standard input\&.
.RE
.PP
\-K \fIdirectory\fR
.RS 4
Sets the directory in which the key files are to reside\&.
.RE
.PP
\-L \fIttl\fR
.RS 4
Sets the default TTL to use for this key when it is converted into a DNSKEY RR\&. If the key is imported into a zone, this is the TTL that will be used for it, unless there was already a DNSKEY RRset in place, in which case the existing TTL would take precedence\&. Setting the default TTL to
0
or
none
removes it\&.
.RE
.PP
\-h
.RS 4
Emit usage message and exit\&.
.RE
.PP
\-v \fIlevel\fR
.RS 4
Sets the debugging level\&.
.RE
.PP
\-V
.RS 4
Prints version information\&.
.RE
.SH "TIMING OPTIONS"
.PP
Dates can be expressed in the format YYYYMMDD or YYYYMMDDHHMMSS\&. If the argument begins with a \*(Aq+\*(Aq or \*(Aq\-\*(Aq, it is interpreted as an offset from the present time\&. For convenience, if such an offset is followed by one of the suffixes \*(Aqy\*(Aq, \*(Aqmo\*(Aq, \*(Aqw\*(Aq, \*(Aqd\*(Aq, \*(Aqh\*(Aq, or \*(Aqmi\*(Aq, then the offset is computed in years (defined as 365 24\-hour days, ignoring leap years), months (defined as 30 24\-hour days), weeks, days, hours, or minutes, respectively\&. Without a suffix, the offset is computed in seconds\&. To explicitly prevent a date from being set, use \*(Aqnone\*(Aq or \*(Aqnever\*(Aq\&.
.PP
\-P \fIdate/offset\fR
.RS 4
Sets the date on which a key is to be published to the zone\&. After that date, the key will be included in the zone but will not be used to sign it\&.
.RE
.PP
\-P sync \fIdate/offset\fR
.RS 4
Sets the date on which CDS and CDNSKEY records that match this key are to be published to the zone\&.
.RE
.PP
\-D \fIdate/offset\fR
.RS 4
Sets the date on which the key is to be deleted\&. After that date, the key will no longer be included in the zone\&. (It may remain in the key repository, however\&.)
.RE
.PP
\-D sync \fIdate/offset\fR
.RS 4
Sets the date on which the CDS and CDNSKEY records that match this key are to be deleted\&.
.RE
.SH "FILES"
.PP
A keyfile can be designed by the key identification
Knnnn\&.+aaa+iiiii
or the full file name
Knnnn\&.+aaa+iiiii\&.key
as generated by
dnssec\-keygen(8)\&.
.SH "SEE ALSO"
.PP
\fBdnssec-keygen\fR(8),
\fBdnssec-signzone\fR(8),
BIND 9 Administrator Reference Manual,
RFC 5011\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
