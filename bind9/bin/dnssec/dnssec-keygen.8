.\" Copyright (C) 2000-2005, 2007-2012, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: dnssec-keygen
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: August 21, 2015
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DNSSEC\-KEYGEN" "8" "August 21, 2015" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dnssec-keygen \- DNSSEC key generation tool
.SH "SYNOPSIS"
.HP \w'\fBdnssec\-keygen\fR\ 'u
\fBdnssec\-keygen\fR [\fB\-a\ \fR\fB\fIalgorithm\fR\fR] [\fB\-b\ \fR\fB\fIkeysize\fR\fR] [\fB\-n\ \fR\fB\fInametype\fR\fR] [\fB\-3\fR] [\fB\-A\ \fR\fB\fIdate/offset\fR\fR] [\fB\-C\fR] [\fB\-c\ \fR\fB\fIclass\fR\fR] [\fB\-D\ \fR\fB\fIdate/offset\fR\fR] [\fB\-D\ sync\ \fR\fB\fIdate/offset\fR\fR] [\fB\-E\ \fR\fB\fIengine\fR\fR] [\fB\-f\ \fR\fB\fIflag\fR\fR] [\fB\-G\fR] [\fB\-g\ \fR\fB\fIgenerator\fR\fR] [\fB\-h\fR] [\fB\-I\ \fR\fB\fIdate/offset\fR\fR] [\fB\-i\ \fR\fB\fIinterval\fR\fR] [\fB\-K\ \fR\fB\fIdirectory\fR\fR] [\fB\-k\fR] [\fB\-L\ \fR\fB\fIttl\fR\fR] [\fB\-P\ \fR\fB\fIdate/offset\fR\fR] [\fB\-P\ sync\ \fR\fB\fIdate/offset\fR\fR] [\fB\-p\ \fR\fB\fIprotocol\fR\fR] [\fB\-q\fR] [\fB\-R\ \fR\fB\fIdate/offset\fR\fR] [\fB\-r\ \fR\fB\fIrandomdev\fR\fR] [\fB\-S\ \fR\fB\fIkey\fR\fR] [\fB\-s\ \fR\fB\fIstrength\fR\fR] [\fB\-t\ \fR\fB\fItype\fR\fR] [\fB\-V\fR] [\fB\-v\ \fR\fB\fIlevel\fR\fR] [\fB\-z\fR] {name}
.SH "DESCRIPTION"
.PP
\fBdnssec\-keygen\fR
generates keys for DNSSEC (Secure DNS), as defined in RFC 2535 and RFC 4034\&. It can also generate keys for use with TSIG (Transaction Signatures) as defined in RFC 2845, or TKEY (Transaction Key) as defined in RFC 2930\&.
.PP
The
\fBname\fR
of the key is specified on the command line\&. For DNSSEC keys, this must match the name of the zone for which the key is being generated\&.
.SH "OPTIONS"
.PP
\-a \fIalgorithm\fR
.RS 4
Selects the cryptographic algorithm\&. For DNSSEC keys, the value of
\fBalgorithm\fR
must be one of RSAMD5, RSASHA1, DSA, NSEC3RSASHA1, NSEC3DSA, RSASHA256, RSASHA512, ECCGOST, ECDSAP256SHA256, ECDSAP384SHA384, ED25519 or ED448\&. For TSIG/TKEY, the value must be DH (Diffie Hellman), HMAC\-MD5, HMAC\-SHA1, HMAC\-SHA224, HMAC\-SHA256, HMAC\-SHA384, or HMAC\-SHA512\&. These values are case insensitive\&.
.sp
If no algorithm is specified, then RSASHA1 will be used by default, unless the
\fB\-3\fR
option is specified, in which case NSEC3RSASHA1 will be used instead\&. (If
\fB\-3\fR
is used and an algorithm is specified, that algorithm will be checked for compatibility with NSEC3\&.)
.sp
Note 1: that for DNSSEC, RSASHA1 is a mandatory to implement algorithm, and DSA is recommended\&. For TSIG, HMAC\-MD5 is mandatory\&.
.sp
Note 2: DH, HMAC\-MD5, and HMAC\-SHA1 through HMAC\-SHA512 automatically set the \-T KEY option\&.
.RE
.PP
\-b \fIkeysize\fR
.RS 4
Specifies the number of bits in the key\&. The choice of key size depends on the algorithm used\&. RSA keys must be between 512 and 2048 bits\&. Diffie Hellman keys must be between 128 and 4096 bits\&. DSA keys must be between 512 and 1024 bits and an exact multiple of 64\&. HMAC keys must be between 1 and 512 bits\&. Elliptic curve algorithms don\*(Aqt need this parameter\&.
.sp
The key size does not need to be specified if using a default algorithm\&. The default key size is 1024 bits for zone signing keys (ZSKs) and 2048 bits for key signing keys (KSKs, generated with
\fB\-f KSK\fR)\&. However, if an algorithm is explicitly specified with the
\fB\-a\fR, then there is no default key size, and the
\fB\-b\fR
must be used\&.
.RE
.PP
\-n \fInametype\fR
.RS 4
Specifies the owner type of the key\&. The value of
\fBnametype\fR
must either be ZONE (for a DNSSEC zone key (KEY/DNSKEY)), HOST or ENTITY (for a key associated with a host (KEY)), USER (for a key associated with a user(KEY)) or OTHER (DNSKEY)\&. These values are case insensitive\&. Defaults to ZONE for DNSKEY generation\&.
.RE
.PP
\-3
.RS 4
Use an NSEC3\-capable algorithm to generate a DNSSEC key\&. If this option is used and no algorithm is explicitly set on the command line, NSEC3RSASHA1 will be used by default\&. Note that RSASHA256, RSASHA512, ECCGOST, ECDSAP256SHA256, ECDSAP384SHA384, ED25519 and ED448 algorithms are NSEC3\-capable\&.
.RE
.PP
\-C
.RS 4
Compatibility mode: generates an old\-style key, without any metadata\&. By default,
\fBdnssec\-keygen\fR
will include the key\*(Aqs creation date in the metadata stored with the private key, and other dates may be set there as well (publication date, activation date, etc)\&. Keys that include this data may be incompatible with older versions of BIND; the
\fB\-C\fR
option suppresses them\&.
.RE
.PP
\-c \fIclass\fR
.RS 4
Indicates that the DNS record containing the key should have the specified class\&. If not specified, class IN is used\&.
.RE
.PP
\-E \fIengine\fR
.RS 4
Specifies the cryptographic hardware to use, when applicable\&.
.sp
When BIND is built with OpenSSL PKCS#11 support, this defaults to the string "pkcs11", which identifies an OpenSSL engine that can drive a cryptographic accelerator or hardware service module\&. When BIND is built with native PKCS#11 cryptography (\-\-enable\-native\-pkcs11), it defaults to the path of the PKCS#11 provider library specified via "\-\-with\-pkcs11"\&.
.RE
.PP
\-f \fIflag\fR
.RS 4
Set the specified flag in the flag field of the KEY/DNSKEY record\&. The only recognized flags are KSK (Key Signing Key) and REVOKE\&.
.RE
.PP
\-G
.RS 4
Generate a key, but do not publish it or sign with it\&. This option is incompatible with \-P and \-A\&.
.RE
.PP
\-g \fIgenerator\fR
.RS 4
If generating a Diffie Hellman key, use this generator\&. Allowed values are 2 and 5\&. If no generator is specified, a known prime from RFC 2539 will be used if possible; otherwise the default is 2\&.
.RE
.PP
\-h
.RS 4
Prints a short summary of the options and arguments to
\fBdnssec\-keygen\fR\&.
.RE
.PP
\-K \fIdirectory\fR
.RS 4
Sets the directory in which the key files are to be written\&.
.RE
.PP
\-k
.RS 4
Deprecated in favor of \-T KEY\&.
.RE
.PP
\-L \fIttl\fR
.RS 4
Sets the default TTL to use for this key when it is converted into a DNSKEY RR\&. If the key is imported into a zone, this is the TTL that will be used for it, unless there was already a DNSKEY RRset in place, in which case the existing TTL would take precedence\&. If this value is not set and there is no existing DNSKEY RRset, the TTL will default to the SOA TTL\&. Setting the default TTL to
0
or
none
is the same as leaving it unset\&.
.RE
.PP
\-p \fIprotocol\fR
.RS 4
Sets the protocol value for the generated key\&. The protocol is a number between 0 and 255\&. The default is 3 (DNSSEC)\&. Other possible values for this argument are listed in RFC 2535 and its successors\&.
.RE
.PP
\-q
.RS 4
Quiet mode: Suppresses unnecessary output, including progress indication\&. Without this option, when
\fBdnssec\-keygen\fR
is run interactively to generate an RSA or DSA key pair, it will print a string of symbols to
stderr
indicating the progress of the key generation\&. A \*(Aq\&.\*(Aq indicates that a random number has been found which passed an initial sieve test; \*(Aq+\*(Aq means a number has passed a single round of the Miller\-Rabin primality test; a space means that the number has passed all the tests and is a satisfactory key\&.
.RE
.PP
\-r \fIrandomdev\fR
.RS 4
Specifies the source of randomness\&. If the operating system does not provide a
/dev/random
or equivalent device, the default source of randomness is keyboard input\&.
randomdev
specifies the name of a character device or file containing random data to be used instead of the default\&. The special value
keyboard
indicates that keyboard input should be used\&.
.RE
.PP
\-S \fIkey\fR
.RS 4
Create a new key which is an explicit successor to an existing key\&. The name, algorithm, size, and type of the key will be set to match the existing key\&. The activation date of the new key will be set to the inactivation date of the existing one\&. The publication date will be set to the activation date minus the prepublication interval, which defaults to 30 days\&.
.RE
.PP
\-s \fIstrength\fR
.RS 4
Specifies the strength value of the key\&. The strength is a number between 0 and 15, and currently has no defined purpose in DNSSEC\&.
.RE
.PP
\-T \fIrrtype\fR
.RS 4
Specifies the resource record type to use for the key\&.
\fBrrtype\fR
must be either DNSKEY or KEY\&. The default is DNSKEY when using a DNSSEC algorithm, but it can be overridden to KEY for use with SIG(0)\&.
Using any TSIG algorithm (HMAC\-* or DH) forces this option to KEY\&.
.RE
.PP
\-t \fItype\fR
.RS 4
Indicates the use of the key\&.
\fBtype\fR
must be one of AUTHCONF, NOAUTHCONF, NOAUTH, or NOCONF\&. The default is AUTHCONF\&. AUTH refers to the ability to authenticate data, and CONF the ability to encrypt data\&.
.RE
.PP
\-v \fIlevel\fR
.RS 4
Sets the debugging level\&.
.RE
.PP
\-V
.RS 4
Prints version information\&.
.RE
.SH "TIMING OPTIONS"
.PP
Dates can be expressed in the format YYYYMMDD or YYYYMMDDHHMMSS\&. If the argument begins with a \*(Aq+\*(Aq or \*(Aq\-\*(Aq, it is interpreted as an offset from the present time\&. For convenience, if such an offset is followed by one of the suffixes \*(Aqy\*(Aq, \*(Aqmo\*(Aq, \*(Aqw\*(Aq, \*(Aqd\*(Aq, \*(Aqh\*(Aq, or \*(Aqmi\*(Aq, then the offset is computed in years (defined as 365 24\-hour days, ignoring leap years), months (defined as 30 24\-hour days), weeks, days, hours, or minutes, respectively\&. Without a suffix, the offset is computed in seconds\&. To explicitly prevent a date from being set, use \*(Aqnone\*(Aq or \*(Aqnever\*(Aq\&.
.PP
\-P \fIdate/offset\fR
.RS 4
Sets the date on which a key is to be published to the zone\&. After that date, the key will be included in the zone but will not be used to sign it\&. If not set, and if the \-G option has not been used, the default is "now"\&.
.RE
.PP
\-P sync \fIdate/offset\fR
.RS 4
Sets the date on which CDS and CDNSKEY records that match this key are to be published to the zone\&.
.RE
.PP
\-A \fIdate/offset\fR
.RS 4
Sets the date on which the key is to be activated\&. After that date, the key will be included in the zone and used to sign it\&. If not set, and if the \-G option has not been used, the default is "now"\&. If set, if and \-P is not set, then the publication date will be set to the activation date minus the prepublication interval\&.
.RE
.PP
\-R \fIdate/offset\fR
.RS 4
Sets the date on which the key is to be revoked\&. After that date, the key will be flagged as revoked\&. It will be included in the zone and will be used to sign it\&.
.RE
.PP
\-I \fIdate/offset\fR
.RS 4
Sets the date on which the key is to be retired\&. After that date, the key will still be included in the zone, but it will not be used to sign it\&.
.RE
.PP
\-D \fIdate/offset\fR
.RS 4
Sets the date on which the key is to be deleted\&. After that date, the key will no longer be included in the zone\&. (It may remain in the key repository, however\&.)
.RE
.PP
\-D sync \fIdate/offset\fR
.RS 4
Sets the date on which the CDS and CDNSKEY records that match this key are to be deleted\&.
.RE
.PP
\-i \fIinterval\fR
.RS 4
Sets the prepublication interval for a key\&. If set, then the publication and activation dates must be separated by at least this much time\&. If the activation date is specified but the publication date isn\*(Aqt, then the publication date will default to this much time before the activation date; conversely, if the publication date is specified but activation date isn\*(Aqt, then activation will be set to this much time after publication\&.
.sp
If the key is being created as an explicit successor to another key, then the default prepublication interval is 30 days; otherwise it is zero\&.
.sp
As with date offsets, if the argument is followed by one of the suffixes \*(Aqy\*(Aq, \*(Aqmo\*(Aq, \*(Aqw\*(Aq, \*(Aqd\*(Aq, \*(Aqh\*(Aq, or \*(Aqmi\*(Aq, then the interval is measured in years, months, weeks, days, hours, or minutes, respectively\&. Without a suffix, the interval is measured in seconds\&.
.RE
.SH "GENERATED KEYS"
.PP
When
\fBdnssec\-keygen\fR
completes successfully, it prints a string of the form
Knnnn\&.+aaa+iiiii
to the standard output\&. This is an identification string for the key it has generated\&.
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
nnnn
is the key name\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
aaa
is the numeric representation of the algorithm\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
iiiii
is the key identifier (or footprint)\&.
.RE
.PP
\fBdnssec\-keygen\fR
creates two files, with names based on the printed string\&.
Knnnn\&.+aaa+iiiii\&.key
contains the public key, and
Knnnn\&.+aaa+iiiii\&.private
contains the private key\&.
.PP
The
\&.key
file contains a DNS KEY record that can be inserted into a zone file (directly or with a $INCLUDE statement)\&.
.PP
The
\&.private
file contains algorithm\-specific fields\&. For obvious security reasons, this file does not have general read permission\&.
.PP
Both
\&.key
and
\&.private
files are generated for symmetric cryptography algorithms such as HMAC\-MD5, even though the public and private key are equivalent\&.
.SH "EXAMPLE"
.PP
To generate a 768\-bit DSA key for the domain
\fBexample\&.com\fR, the following command would be issued:
.PP
\fBdnssec\-keygen \-a DSA \-b 768 \-n ZONE example\&.com\fR
.PP
The command would print a string of the form:
.PP
\fBKexample\&.com\&.+003+26160\fR
.PP
In this example,
\fBdnssec\-keygen\fR
creates the files
Kexample\&.com\&.+003+26160\&.key
and
Kexample\&.com\&.+003+26160\&.private\&.
.SH "SEE ALSO"
.PP
\fBdnssec-signzone\fR(8),
BIND 9 Administrator Reference Manual,
RFC 2539,
RFC 2845,
RFC 4034\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2000-2005, 2007-2012, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
.br
