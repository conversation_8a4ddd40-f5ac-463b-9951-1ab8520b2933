.\" Copyright (C) 2009-2011, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: dnssec-settime
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2015-08-21
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DNSSEC\-SETTIME" "8" "2015\-08\-21" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dnssec-settime \- set the key timing metadata for a DNSSEC key
.SH "SYNOPSIS"
.HP \w'\fBdnssec\-settime\fR\ 'u
\fBdnssec\-settime\fR [\fB\-f\fR] [\fB\-K\ \fR\fB\fIdirectory\fR\fR] [\fB\-L\ \fR\fB\fIttl\fR\fR] [\fB\-P\ \fR\fB\fIdate/offset\fR\fR] [\fB\-P\ sync\ \fR\fB\fIdate/offset\fR\fR] [\fB\-A\ \fR\fB\fIdate/offset\fR\fR] [\fB\-R\ \fR\fB\fIdate/offset\fR\fR] [\fB\-I\ \fR\fB\fIdate/offset\fR\fR] [\fB\-D\ \fR\fB\fIdate/offset\fR\fR] [\fB\-D\ sync\ \fR\fB\fIdate/offset\fR\fR] [\fB\-S\ \fR\fB\fIkey\fR\fR] [\fB\-i\ \fR\fB\fIinterval\fR\fR] [\fB\-h\fR] [\fB\-V\fR] [\fB\-v\ \fR\fB\fIlevel\fR\fR] [\fB\-E\ \fR\fB\fIengine\fR\fR] {keyfile}
.SH "DESCRIPTION"
.PP
\fBdnssec\-settime\fR
reads a DNSSEC private key file and sets the key timing metadata as specified by the
\fB\-P\fR,
\fB\-A\fR,
\fB\-R\fR,
\fB\-I\fR, and
\fB\-D\fR
options\&. The metadata can then be used by
\fBdnssec\-signzone\fR
or other signing software to determine when a key is to be published, whether it should be used for signing a zone, etc\&.
.PP
If none of these options is set on the command line, then
\fBdnssec\-settime\fR
simply prints the key timing metadata already stored in the key\&.
.PP
When key metadata fields are changed, both files of a key pair (Knnnn\&.+aaa+iiiii\&.key
and
Knnnn\&.+aaa+iiiii\&.private) are regenerated\&. Metadata fields are stored in the private file\&. A human\-readable description of the metadata is also placed in comments in the key file\&. The private file\*(Aqs permissions are always set to be inaccessible to anyone other than the owner (mode 0600)\&.
.SH "OPTIONS"
.PP
\-f
.RS 4
Force an update of an old\-format key with no metadata fields\&. Without this option,
\fBdnssec\-settime\fR
will fail when attempting to update a legacy key\&. With this option, the key will be recreated in the new format, but with the original key data retained\&. The key\*(Aqs creation date will be set to the present time\&. If no other values are specified, then the key\*(Aqs publication and activation dates will also be set to the present time\&.
.RE
.PP
\-K \fIdirectory\fR
.RS 4
Sets the directory in which the key files are to reside\&.
.RE
.PP
\-L \fIttl\fR
.RS 4
Sets the default TTL to use for this key when it is converted into a DNSKEY RR\&. If the key is imported into a zone, this is the TTL that will be used for it, unless there was already a DNSKEY RRset in place, in which case the existing TTL would take precedence\&. If this value is not set and there is no existing DNSKEY RRset, the TTL will default to the SOA TTL\&. Setting the default TTL to
0
or
none
removes it from the key\&.
.RE
.PP
\-h
.RS 4
Emit usage message and exit\&.
.RE
.PP
\-V
.RS 4
Prints version information\&.
.RE
.PP
\-v \fIlevel\fR
.RS 4
Sets the debugging level\&.
.RE
.PP
\-E \fIengine\fR
.RS 4
Specifies the cryptographic hardware to use, when applicable\&.
.sp
When BIND is built with OpenSSL PKCS#11 support, this defaults to the string "pkcs11", which identifies an OpenSSL engine that can drive a cryptographic accelerator or hardware service module\&. When BIND is built with native PKCS#11 cryptography (\-\-enable\-native\-pkcs11), it defaults to the path of the PKCS#11 provider library specified via "\-\-with\-pkcs11"\&.
.RE
.SH "TIMING OPTIONS"
.PP
Dates can be expressed in the format YYYYMMDD or YYYYMMDDHHMMSS\&. If the argument begins with a \*(Aq+\*(Aq or \*(Aq\-\*(Aq, it is interpreted as an offset from the present time\&. For convenience, if such an offset is followed by one of the suffixes \*(Aqy\*(Aq, \*(Aqmo\*(Aq, \*(Aqw\*(Aq, \*(Aqd\*(Aq, \*(Aqh\*(Aq, or \*(Aqmi\*(Aq, then the offset is computed in years (defined as 365 24\-hour days, ignoring leap years), months (defined as 30 24\-hour days), weeks, days, hours, or minutes, respectively\&. Without a suffix, the offset is computed in seconds\&. To unset a date, use \*(Aqnone\*(Aq or \*(Aqnever\*(Aq\&.
.PP
\-P \fIdate/offset\fR
.RS 4
Sets the date on which a key is to be published to the zone\&. After that date, the key will be included in the zone but will not be used to sign it\&.
.RE
.PP
\-P sync \fIdate/offset\fR
.RS 4
Sets the date on which CDS and CDNSKEY records that match this key are to be published to the zone\&.
.RE
.PP
\-A \fIdate/offset\fR
.RS 4
Sets the date on which the key is to be activated\&. After that date, the key will be included in the zone and used to sign it\&.
.RE
.PP
\-R \fIdate/offset\fR
.RS 4
Sets the date on which the key is to be revoked\&. After that date, the key will be flagged as revoked\&. It will be included in the zone and will be used to sign it\&.
.RE
.PP
\-I \fIdate/offset\fR
.RS 4
Sets the date on which the key is to be retired\&. After that date, the key will still be included in the zone, but it will not be used to sign it\&.
.RE
.PP
\-D \fIdate/offset\fR
.RS 4
Sets the date on which the key is to be deleted\&. After that date, the key will no longer be included in the zone\&. (It may remain in the key repository, however\&.)
.RE
.PP
\-D sync \fIdate/offset\fR
.RS 4
Sets the date on which the CDS and CDNSKEY records that match this key are to be deleted\&.
.RE
.PP
\-S \fIpredecessor key\fR
.RS 4
Select a key for which the key being modified will be an explicit successor\&. The name, algorithm, size, and type of the predecessor key must exactly match those of the key being modified\&. The activation date of the successor key will be set to the inactivation date of the predecessor\&. The publication date will be set to the activation date minus the prepublication interval, which defaults to 30 days\&.
.RE
.PP
\-i \fIinterval\fR
.RS 4
Sets the prepublication interval for a key\&. If set, then the publication and activation dates must be separated by at least this much time\&. If the activation date is specified but the publication date isn\*(Aqt, then the publication date will default to this much time before the activation date; conversely, if the publication date is specified but activation date isn\*(Aqt, then activation will be set to this much time after publication\&.
.sp
If the key is being set to be an explicit successor to another key, then the default prepublication interval is 30 days; otherwise it is zero\&.
.sp
As with date offsets, if the argument is followed by one of the suffixes \*(Aqy\*(Aq, \*(Aqmo\*(Aq, \*(Aqw\*(Aq, \*(Aqd\*(Aq, \*(Aqh\*(Aq, or \*(Aqmi\*(Aq, then the interval is measured in years, months, weeks, days, hours, or minutes, respectively\&. Without a suffix, the interval is measured in seconds\&.
.RE
.SH "PRINTING OPTIONS"
.PP
\fBdnssec\-settime\fR
can also be used to print the timing metadata associated with a key\&.
.PP
\-u
.RS 4
Print times in UNIX epoch format\&.
.RE
.PP
\-p \fIC/P/Psync/A/R/I/D/Dsync/all\fR
.RS 4
Print a specific metadata value or set of metadata values\&. The
\fB\-p\fR
option may be followed by one or more of the following letters or strings to indicate which value or values to print:
\fBC\fR
for the creation date,
\fBP\fR
for the publication date,
\fBPsync\fR
for the CDS and CDNSKEY publication date,
\fBA\fR
for the activation date,
\fBR\fR
for the revocation date,
\fBI\fR
for the inactivation date,
\fBD\fR
for the deletion date, and
\fBDsync\fR
for the CDS and CDNSKEY deletion date To print all of the metadata, use
\fB\-p all\fR\&.
.RE
.SH "SEE ALSO"
.PP
\fBdnssec-keygen\fR(8),
\fBdnssec-signzone\fR(8),
BIND 9 Administrator Reference Manual,
RFC 5011\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2009-2011, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
.br
