<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2009-2011, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>dnssec-settime</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.dnssec-settime"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">dnssec-settime</span>
     &#8212; set the key timing metadata for a DNSSEC key
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">dnssec-settime</code> 
       [<code class="option">-f</code>]
       [<code class="option">-K <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-L <em class="replaceable"><code>ttl</code></em></code>]
       [<code class="option">-P <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-P sync <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-A <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-R <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-I <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-D <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-D sync <em class="replaceable"><code>date/offset</code></em></code>]
       [<code class="option">-S <em class="replaceable"><code>key</code></em></code>]
       [<code class="option">-i <em class="replaceable"><code>interval</code></em></code>]
       [<code class="option">-h</code>]
       [<code class="option">-V</code>]
       [<code class="option">-v <em class="replaceable"><code>level</code></em></code>]
       [<code class="option">-E <em class="replaceable"><code>engine</code></em></code>]
       {keyfile}
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>dnssec-settime</strong></span>
      reads a DNSSEC private key file and sets the key timing metadata
      as specified by the <code class="option">-P</code>, <code class="option">-A</code>,
      <code class="option">-R</code>, <code class="option">-I</code>, and <code class="option">-D</code>
      options.  The metadata can then be used by
      <span class="command"><strong>dnssec-signzone</strong></span> or other signing software to
      determine when a key is to be published, whether it should be
      used for signing a zone, etc.
    </p>
    <p>
      If none of these options is set on the command line,
      then <span class="command"><strong>dnssec-settime</strong></span> simply prints the key timing
      metadata already stored in the key.
    </p>
    <p>
      When key metadata fields are changed, both files of a key
      pair (<code class="filename">Knnnn.+aaa+iiiii.key</code> and
      <code class="filename">Knnnn.+aaa+iiiii.private</code>) are regenerated.
      Metadata fields are stored in the private file.  A human-readable
      description of the metadata is also placed in comments in the key
      file.  The private file's permissions are always set to be
      inaccessible to anyone other than the owner (mode 0600).
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-f</span></dt>
<dd>
          <p>
            Force an update of an old-format key with no metadata fields.
            Without this option, <span class="command"><strong>dnssec-settime</strong></span> will
            fail when attempting to update a legacy key.  With this option,
            the key will be recreated in the new format, but with the
            original key data retained.  The key's creation date will be
            set to the present time.  If no other values are specified,
            then the key's publication and activation dates will also
            be set to the present time.
          </p>
        </dd>
<dt><span class="term">-K <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
          <p>
            Sets the directory in which the key files are to reside.
          </p>
        </dd>
<dt><span class="term">-L <em class="replaceable"><code>ttl</code></em></span></dt>
<dd>
          <p>
            Sets the default TTL to use for this key when it is converted
            into a DNSKEY RR.  If the key is imported into a zone,
            this is the TTL that will be used for it, unless there was
            already a DNSKEY RRset in place, in which case the existing TTL
            would take precedence.  If this value is not set and there
            is no existing DNSKEY RRset, the TTL will default to the
            SOA TTL. Setting the default TTL to <code class="literal">0</code>
            or <code class="literal">none</code> removes it from the key.
          </p>
        </dd>
<dt><span class="term">-h</span></dt>
<dd>
          <p>
            Emit usage message and exit.
          </p>
        </dd>
<dt><span class="term">-V</span></dt>
<dd>
          <p>
            Prints version information.
          </p>
        </dd>
<dt><span class="term">-v <em class="replaceable"><code>level</code></em></span></dt>
<dd>
          <p>
            Sets the debugging level.
          </p>
        </dd>
<dt><span class="term">-E <em class="replaceable"><code>engine</code></em></span></dt>
<dd>
          <p>
            Specifies the cryptographic hardware to use, when applicable.
          </p>
          <p>
            When BIND is built with OpenSSL PKCS#11 support, this defaults
            to the string "pkcs11", which identifies an OpenSSL engine
            that can drive a cryptographic accelerator or hardware service
            module.  When BIND is built with native PKCS#11 cryptography
            (--enable-native-pkcs11), it defaults to the path of the PKCS#11
            provider library specified via "--with-pkcs11".
          </p>
        </dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>TIMING OPTIONS</h2>

    <p>
      Dates can be expressed in the format YYYYMMDD or YYYYMMDDHHMMSS.
      If the argument begins with a '+' or '-', it is interpreted as
      an offset from the present time.  For convenience, if such an offset
      is followed by one of the suffixes 'y', 'mo', 'w', 'd', 'h', or 'mi',
      then the offset is computed in years (defined as 365 24-hour days,
      ignoring leap years), months (defined as 30 24-hour days), weeks,
      days, hours, or minutes, respectively.  Without a suffix, the offset
      is computed in seconds.  To unset a date, use 'none' or 'never'.
    </p>

    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-P <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
          <p>
            Sets the date on which a key is to be published to the zone.
            After that date, the key will be included in the zone but will
            not be used to sign it.
          </p>
        </dd>
<dt><span class="term">-P sync <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
          <p>
            Sets the date on which CDS and CDNSKEY records that match this
            key are to be published to the zone.
          </p>
        </dd>
<dt><span class="term">-A <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
          <p>
            Sets the date on which the key is to be activated.  After that
            date, the key will be included in the zone and used to sign
            it.
          </p>
        </dd>
<dt><span class="term">-R <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
          <p>
            Sets the date on which the key is to be revoked.  After that
            date, the key will be flagged as revoked.  It will be included
            in the zone and will be used to sign it.
          </p>
        </dd>
<dt><span class="term">-I <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
          <p>
            Sets the date on which the key is to be retired.  After that
            date, the key will still be included in the zone, but it
            will not be used to sign it.
          </p>
        </dd>
<dt><span class="term">-D <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
          <p>
            Sets the date on which the key is to be deleted.  After that
            date, the key will no longer be included in the zone.  (It
            may remain in the key repository, however.)
          </p>
        </dd>
<dt><span class="term">-D sync <em class="replaceable"><code>date/offset</code></em></span></dt>
<dd>
          <p>
            Sets the date on which the CDS and CDNSKEY records that match this
            key are to be deleted.
          </p>
        </dd>
<dt><span class="term">-S <em class="replaceable"><code>predecessor key</code></em></span></dt>
<dd>
          <p>
            Select a key for which the key being modified will be an
            explicit successor.  The name, algorithm, size, and type of the
            predecessor key must exactly match those of the key being
            modified.  The activation date of the successor key will be set
            to the inactivation date of the predecessor.  The publication
            date will be set to the activation date minus the prepublication
            interval, which defaults to 30 days.
          </p>
        </dd>
<dt><span class="term">-i <em class="replaceable"><code>interval</code></em></span></dt>
<dd>
          <p>
            Sets the prepublication interval for a key.  If set, then
            the publication and activation dates must be separated by at least
            this much time.  If the activation date is specified but the
            publication date isn't, then the publication date will default
            to this much time before the activation date; conversely, if
            the publication date is specified but activation date isn't,
            then activation will be set to this much time after publication.
          </p>
          <p>
            If the key is being set to be an explicit successor to another
            key, then the default prepublication interval is 30 days;
            otherwise it is zero.
          </p>
          <p>
            As with date offsets, if the argument is followed by one of
            the suffixes 'y', 'mo', 'w', 'd', 'h', or 'mi', then the
            interval is measured in years, months, weeks, days, hours,
            or minutes, respectively.  Without a suffix, the interval is
            measured in seconds.
          </p>
        </dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>PRINTING OPTIONS</h2>

    <p>
      <span class="command"><strong>dnssec-settime</strong></span> can also be used to print the
      timing metadata associated with a key.
    </p>

    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-u</span></dt>
<dd>
          <p>
            Print times in UNIX epoch format.
          </p>
        </dd>
<dt><span class="term">-p <em class="replaceable"><code>C/P/Psync/A/R/I/D/Dsync/all</code></em></span></dt>
<dd>
          <p>
            Print a specific metadata value or set of metadata values.
            The <code class="option">-p</code> option may be followed by one or more
            of the following letters or strings to indicate which value
            or values to print:
            <code class="option">C</code> for the creation date,
            <code class="option">P</code> for the publication date,
            <code class="option">Psync</code> for the CDS and CDNSKEY publication date,
            <code class="option">A</code> for the activation date,
            <code class="option">R</code> for the revocation date,
            <code class="option">I</code> for the inactivation date,
            <code class="option">D</code> for the deletion date, and
            <code class="option">Dsync</code> for the CDS and CDNSKEY deletion date
            To print all of the metadata, use <code class="option">-p all</code>.
          </p>
        </dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.11"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
        <span class="refentrytitle">dnssec-keygen</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">dnssec-signzone</span>(8)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>,
      <em class="citetitle">RFC 5011</em>.
    </p>
  </div>

</div></body>
</html>
