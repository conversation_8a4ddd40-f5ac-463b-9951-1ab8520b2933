<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.dnssec-keygen">
  <info>
    <date>2014-02-06</date>
  </info>
  <refentryinfo>
    <date>August 21, 2015</date>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>dnssec-keygen</application></refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>dnssec-keygen</application></refname>
    <refpurpose>DNSSEC key generation tool</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2000</year>
      <year>2001</year>
      <year>2002</year>
      <year>2003</year>
      <year>2004</year>
      <year>2005</year>
      <year>2007</year>
      <year>2008</year>
      <year>2009</year>
      <year>2010</year>
      <year>2011</year>
      <year>2012</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2017</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>dnssec-keygen</command>
      <arg choice="opt" rep="norepeat"><option>-a <replaceable class="parameter">algorithm</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-b <replaceable class="parameter">keysize</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-n <replaceable class="parameter">nametype</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-3</option></arg>
      <arg choice="opt" rep="norepeat"><option>-A <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-C</option></arg>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">class</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-D <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-D sync <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-E <replaceable class="parameter">engine</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-f <replaceable class="parameter">flag</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-G</option></arg>
      <arg choice="opt" rep="norepeat"><option>-g <replaceable class="parameter">generator</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-h</option></arg>
      <arg choice="opt" rep="norepeat"><option>-I <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-i <replaceable class="parameter">interval</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-K <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-k</option></arg>
      <arg choice="opt" rep="norepeat"><option>-L <replaceable class="parameter">ttl</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-P <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-P sync <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-p <replaceable class="parameter">protocol</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-q</option></arg>
      <arg choice="opt" rep="norepeat"><option>-R <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-r <replaceable class="parameter">randomdev</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-S <replaceable class="parameter">key</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-s <replaceable class="parameter">strength</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-t <replaceable class="parameter">type</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-V</option></arg>
      <arg choice="opt" rep="norepeat"><option>-v <replaceable class="parameter">level</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-z</option></arg>
      <arg choice="req" rep="norepeat">name</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>dnssec-keygen</command>
      generates keys for DNSSEC (Secure DNS), as defined in RFC 2535
      and RFC 4034.  It can also generate keys for use with
      TSIG (Transaction Signatures) as defined in RFC 2845, or TKEY
      (Transaction Key) as defined in RFC 2930.
    </para>
    <para>
      The <option>name</option> of the key is specified on the command
      line.  For DNSSEC keys, this must match the name of the zone for
      which the key is being generated.
    </para>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
	<term>-a <replaceable class="parameter">algorithm</replaceable></term>
	<listitem>
	  <para>
	    Selects the cryptographic algorithm.  For DNSSEC keys, the value
	    of <option>algorithm</option> must be one of RSAMD5, RSASHA1,
	    DSA, NSEC3RSASHA1, NSEC3DSA, RSASHA256, RSASHA512, ECCGOST,
	    ECDSAP256SHA256, ECDSAP384SHA384, ED25519 or ED448.
	    For TSIG/TKEY, the value must
	    be DH (Diffie Hellman), HMAC-MD5, HMAC-SHA1, HMAC-SHA224,
	    HMAC-SHA256, HMAC-SHA384, or HMAC-SHA512.  These values are
	    case insensitive.
	  </para>
	  <para>
	    If no algorithm is specified, then RSASHA1 will be used by
	    default, unless the <option>-3</option> option is specified,
	    in which case NSEC3RSASHA1 will be used instead.  (If
	    <option>-3</option> is used and an algorithm is specified,
	    that algorithm will be checked for compatibility with NSEC3.)
	  </para>
	  <para>
	    Note 1: that for DNSSEC, RSASHA1 is a mandatory to implement
	    algorithm, and DSA is recommended.  For TSIG, HMAC-MD5 is
	    mandatory.
	  </para>
	  <para>
	    Note 2: DH, HMAC-MD5, and HMAC-SHA1 through HMAC-SHA512
	    automatically set the -T KEY option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-b <replaceable class="parameter">keysize</replaceable></term>
	<listitem>
	  <para>
	    Specifies the number of bits in the key.  The choice of key
	    size depends on the algorithm used.  RSA keys must be
	    between 512 and 2048 bits.  Diffie Hellman keys must be between
	    128 and 4096 bits.  DSA keys must be between 512 and 1024
	    bits and an exact multiple of 64.  HMAC keys must be
	    between 1 and 512 bits. Elliptic curve algorithms don't need
	    this parameter.
	  </para>
	  <para>
	    The key size does not need to be specified if using a default
	    algorithm.  The default key size is 1024 bits for zone signing
	    keys (ZSKs) and 2048 bits for key signing keys (KSKs,
	    generated with <option>-f KSK</option>).  However, if an
	    algorithm is explicitly specified with the <option>-a</option>,
	    then there is no default key size, and the <option>-b</option>
	    must be used.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-n <replaceable class="parameter">nametype</replaceable></term>
	<listitem>
	  <para>
	    Specifies the owner type of the key.  The value of
	    <option>nametype</option> must either be ZONE (for a DNSSEC
	    zone key (KEY/DNSKEY)), HOST or ENTITY (for a key associated with
	    a host (KEY)),
	    USER (for a key associated with a user(KEY)) or OTHER (DNSKEY).
	    These values are case insensitive.  Defaults to ZONE for DNSKEY
	    generation.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-3</term>
	<listitem>
	  <para>
	    Use an NSEC3-capable algorithm to generate a DNSSEC key.
	    If this option is used and no algorithm is explicitly
	    set on the command line, NSEC3RSASHA1 will be used by
	    default. Note that RSASHA256, RSASHA512, ECCGOST,
	    ECDSAP256SHA256, ECDSAP384SHA384, ED25519 and ED448
	    algorithms are NSEC3-capable.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-C</term>
	<listitem>
	  <para>
	    Compatibility mode:  generates an old-style key, without
	    any metadata.  By default, <command>dnssec-keygen</command>
	    will include the key's creation date in the metadata stored
	    with the private key, and other dates may be set there as well
	    (publication date, activation date, etc).  Keys that include
	    this data may be incompatible with older versions of BIND; the
	    <option>-C</option> option suppresses them.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-c <replaceable class="parameter">class</replaceable></term>
	<listitem>
	  <para>
	    Indicates that the DNS record containing the key should have
	    the specified class.  If not specified, class IN is used.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-E <replaceable class="parameter">engine</replaceable></term>
	<listitem>
	  <para>
	    Specifies the cryptographic hardware to use, when applicable.
	  </para>
	  <para>
	    When BIND is built with OpenSSL PKCS#11 support, this defaults
	    to the string "pkcs11", which identifies an OpenSSL engine
	    that can drive a cryptographic accelerator or hardware service
	    module.  When BIND is built with native PKCS#11 cryptography
	    (--enable-native-pkcs11), it defaults to the path of the PKCS#11
	    provider library specified via "--with-pkcs11".
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-f <replaceable class="parameter">flag</replaceable></term>
	<listitem>
	  <para>
	    Set the specified flag in the flag field of the KEY/DNSKEY record.
	    The only recognized flags are KSK (Key Signing Key) and REVOKE.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-G</term>
	<listitem>
	  <para>
	    Generate a key, but do not publish it or sign with it.  This
	    option is incompatible with -P and -A.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-g <replaceable class="parameter">generator</replaceable></term>
	<listitem>
	  <para>
	    If generating a Diffie Hellman key, use this generator.
	    Allowed values are 2 and 5.  If no generator
	    is specified, a known prime from RFC 2539 will be used
	    if possible; otherwise the default is 2.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-h</term>
	<listitem>
	  <para>
	    Prints a short summary of the options and arguments to
	    <command>dnssec-keygen</command>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-K <replaceable class="parameter">directory</replaceable></term>
	<listitem>
	  <para>
	    Sets the directory in which the key files are to be written.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-k</term>
	<listitem>
	  <para>
	    Deprecated in favor of -T KEY.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-L <replaceable class="parameter">ttl</replaceable></term>
	<listitem>
	  <para>
	    Sets the default TTL to use for this key when it is converted
	    into a DNSKEY RR.  If the key is imported into a zone,
	    this is the TTL that will be used for it, unless there was
	    already a DNSKEY RRset in place, in which case the existing TTL
	    would take precedence.  If this value is not set and there
	    is no existing DNSKEY RRset, the TTL will default to the
	    SOA TTL. Setting the default TTL to <literal>0</literal>
	    or <literal>none</literal> is the same as leaving it unset.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-p <replaceable class="parameter">protocol</replaceable></term>
	<listitem>
	  <para>
	    Sets the protocol value for the generated key.  The protocol
	    is a number between 0 and 255.  The default is 3 (DNSSEC).
	    Other possible values for this argument are listed in
	    RFC 2535 and its successors.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-q</term>
	<listitem>
	  <para>
	    Quiet mode: Suppresses unnecessary output, including
	    progress indication.  Without this option, when
	    <command>dnssec-keygen</command> is run interactively
	    to generate an RSA or DSA key pair, it will print a string
	    of symbols to <filename>stderr</filename> indicating the
	    progress of the key generation.  A '.' indicates that a
	    random number has been found which passed an initial
	    sieve test; '+' means a number has passed a single
	    round of the Miller-Rabin primality test; a space
	    means that the number has passed all the tests and is
	    a satisfactory key.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-r <replaceable class="parameter">randomdev</replaceable></term>
	<listitem>
	  <para>
	    Specifies the source of randomness.  If the operating
	    system does not provide a <filename>/dev/random</filename>
	    or equivalent device, the default source of randomness
	    is keyboard input.  <filename>randomdev</filename>
	    specifies
	    the name of a character device or file containing random
	    data to be used instead of the default.  The special value
	    <filename>keyboard</filename> indicates that keyboard
	    input should be used.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-S <replaceable class="parameter">key</replaceable></term>
	<listitem>
	  <para>
	    Create a new key which is an explicit successor to an
	    existing key.  The name, algorithm, size, and type of the
	    key will be set to match the existing key.  The activation
	    date of the new key will be set to the inactivation date of
	    the existing one.  The publication date will be set to the
	    activation date minus the prepublication interval, which
	    defaults to 30 days.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-s <replaceable class="parameter">strength</replaceable></term>
	<listitem>
	  <para>
	    Specifies the strength value of the key.  The strength is
	    a number between 0 and 15, and currently has no defined
	    purpose in DNSSEC.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-T <replaceable class="parameter">rrtype</replaceable></term>
	<listitem>
	  <para>
	    Specifies the resource record type to use for the key.
	    <option>rrtype</option> must be either DNSKEY or KEY.  The
	    default is DNSKEY when using a DNSSEC algorithm, but it can be
	    overridden to KEY for use with SIG(0).
	  <para>
	  </para>
	    Using any TSIG algorithm (HMAC-* or DH) forces this option
	    to KEY.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-t <replaceable class="parameter">type</replaceable></term>
	<listitem>
	  <para>
	    Indicates the use of the key.  <option>type</option> must be
	    one of AUTHCONF, NOAUTHCONF, NOAUTH, or NOCONF.  The default
	    is AUTHCONF.  AUTH refers to the ability to authenticate
	    data, and CONF the ability to encrypt data.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-v <replaceable class="parameter">level</replaceable></term>
	<listitem>
	  <para>
	    Sets the debugging level.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-V</term>
	<listitem>
	  <para>
	    Prints version information.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>TIMING OPTIONS</title></info>


    <para>
      Dates can be expressed in the format YYYYMMDD or YYYYMMDDHHMMSS.
      If the argument begins with a '+' or '-', it is interpreted as
      an offset from the present time.  For convenience, if such an offset
      is followed by one of the suffixes 'y', 'mo', 'w', 'd', 'h', or 'mi',
      then the offset is computed in years (defined as 365 24-hour days,
      ignoring leap years), months (defined as 30 24-hour days), weeks,
      days, hours, or minutes, respectively.  Without a suffix, the offset
      is computed in seconds.  To explicitly prevent a date from being
      set, use 'none' or 'never'.
    </para>

    <variablelist>
      <varlistentry>
	<term>-P <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which a key is to be published to the zone.
	    After that date, the key will be included in the zone but will
	    not be used to sign it.  If not set, and if the -G option has
	    not been used, the default is "now".
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-P sync <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which CDS and CDNSKEY records that match this
	    key are to be published to the zone.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-A <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the key is to be activated.  After that
	    date, the key will be included in the zone and used to sign
	    it.  If not set, and if the -G option has not been used, the
	    default is "now".  If set, if and -P is not set, then
	    the publication date will be set to the activation date
	    minus the prepublication interval.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-R <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the key is to be revoked.  After that
	    date, the key will be flagged as revoked.  It will be included
	    in the zone and will be used to sign it.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-I <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the key is to be retired.  After that
	    date, the key will still be included in the zone, but it
	    will not be used to sign it.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-D <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the key is to be deleted.  After that
	    date, the key will no longer be included in the zone.  (It
	    may remain in the key repository, however.)
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-D sync <replaceable class="parameter">date/offset</replaceable></term>
	<listitem>
	  <para>
	    Sets the date on which the CDS and CDNSKEY records that match this
	    key are to be deleted.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
        <term>-i <replaceable class="parameter">interval</replaceable></term>
        <listitem>
          <para>
            Sets the prepublication interval for a key.  If set, then
            the publication and activation dates must be separated by at least
            this much time.  If the activation date is specified but the
            publication date isn't, then the publication date will default
            to this much time before the activation date; conversely, if
            the publication date is specified but activation date isn't,
            then activation will be set to this much time after publication.
          </para>
          <para>
            If the key is being created as an explicit successor to another
            key, then the default prepublication interval is 30 days;
            otherwise it is zero.
          </para>
          <para>
            As with date offsets, if the argument is followed by one of
            the suffixes 'y', 'mo', 'w', 'd', 'h', or 'mi', then the
            interval is measured in years, months, weeks, days, hours,
            or minutes, respectively.  Without a suffix, the interval is
            measured in seconds.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsection>


  <refsection><info><title>GENERATED KEYS</title></info>

    <para>
      When <command>dnssec-keygen</command> completes
      successfully,
      it prints a string of the form <filename>Knnnn.+aaa+iiiii</filename>
      to the standard output.  This is an identification string for
      the key it has generated.
    </para>
    <itemizedlist>
      <listitem>
	<para><filename>nnnn</filename> is the key name.
	</para>
      </listitem>
      <listitem>
	<para><filename>aaa</filename> is the numeric representation
	  of the
	  algorithm.
	</para>
      </listitem>
      <listitem>
	<para><filename>iiiii</filename> is the key identifier (or
	  footprint).
	</para>
      </listitem>
    </itemizedlist>
    <para><command>dnssec-keygen</command>
      creates two files, with names based
      on the printed string.  <filename>Knnnn.+aaa+iiiii.key</filename>
      contains the public key, and
      <filename>Knnnn.+aaa+iiiii.private</filename> contains the
      private
      key.
    </para>
    <para>
      The <filename>.key</filename> file contains a DNS KEY record
      that
      can be inserted into a zone file (directly or with a $INCLUDE
      statement).
    </para>
    <para>
      The <filename>.private</filename> file contains
      algorithm-specific
      fields.  For obvious security reasons, this file does not have
      general read permission.
    </para>
    <para>
      Both <filename>.key</filename> and <filename>.private</filename>
      files are generated for symmetric cryptography algorithms such as
      HMAC-MD5, even though the public and private key are equivalent.
    </para>
  </refsection>

  <refsection><info><title>EXAMPLE</title></info>

    <para>
      To generate a 768-bit DSA key for the domain
      <userinput>example.com</userinput>, the following command would be
      issued:
    </para>
    <para><userinput>dnssec-keygen -a DSA -b 768 -n ZONE example.com</userinput>
    </para>
    <para>
      The command would print a string of the form:
    </para>
    <para><userinput>Kexample.com.+003+26160</userinput>
    </para>
    <para>
      In this example, <command>dnssec-keygen</command> creates
      the files <filename>Kexample.com.+003+26160.key</filename>
      and
      <filename>Kexample.com.+003+26160.private</filename>.
    </para>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
	<refentrytitle>dnssec-signzone</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>,
      <citetitle>RFC 2539</citetitle>,
      <citetitle>RFC 2845</citetitle>,
      <citetitle>RFC 4034</citetitle>.
    </para>
  </refsection>

</refentry>
