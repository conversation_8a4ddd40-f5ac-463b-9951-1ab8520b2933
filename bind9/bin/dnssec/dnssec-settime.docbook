<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.dnssec-settime">
  <info>
    <date>2015-08-21</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>dnssec-settime</application></refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>dnssec-settime</application></refname>
    <refpurpose>set the key timing metadata for a DNSSEC key</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2009</year>
      <year>2010</year>
      <year>2011</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2017</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>dnssec-settime</command>
      <arg choice="opt" rep="norepeat"><option>-f</option></arg>
      <arg choice="opt" rep="norepeat"><option>-K <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-L <replaceable class="parameter">ttl</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-P <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-P sync <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-A <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-R <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-I <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-D <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-D sync <replaceable class="parameter">date/offset</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-S <replaceable class="parameter">key</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-i <replaceable class="parameter">interval</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-h</option></arg>
      <arg choice="opt" rep="norepeat"><option>-V</option></arg>
      <arg choice="opt" rep="norepeat"><option>-v <replaceable class="parameter">level</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-E <replaceable class="parameter">engine</replaceable></option></arg>
      <arg choice="req" rep="norepeat">keyfile</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>dnssec-settime</command>
      reads a DNSSEC private key file and sets the key timing metadata
      as specified by the <option>-P</option>, <option>-A</option>,
      <option>-R</option>, <option>-I</option>, and <option>-D</option>
      options.  The metadata can then be used by
      <command>dnssec-signzone</command> or other signing software to
      determine when a key is to be published, whether it should be
      used for signing a zone, etc.
    </para>
    <para>
      If none of these options is set on the command line,
      then <command>dnssec-settime</command> simply prints the key timing
      metadata already stored in the key.
    </para>
    <para>
      When key metadata fields are changed, both files of a key
      pair (<filename>Knnnn.+aaa+iiiii.key</filename> and
      <filename>Knnnn.+aaa+iiiii.private</filename>) are regenerated.
      Metadata fields are stored in the private file.  A human-readable
      description of the metadata is also placed in comments in the key
      file.  The private file's permissions are always set to be
      inaccessible to anyone other than the owner (mode 0600).
    </para>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
        <term>-f</term>
        <listitem>
          <para>
            Force an update of an old-format key with no metadata fields.
            Without this option, <command>dnssec-settime</command> will
            fail when attempting to update a legacy key.  With this option,
            the key will be recreated in the new format, but with the
            original key data retained.  The key's creation date will be
            set to the present time.  If no other values are specified,
            then the key's publication and activation dates will also
            be set to the present time.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-K <replaceable class="parameter">directory</replaceable></term>
        <listitem>
          <para>
            Sets the directory in which the key files are to reside.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-L <replaceable class="parameter">ttl</replaceable></term>
        <listitem>
          <para>
            Sets the default TTL to use for this key when it is converted
            into a DNSKEY RR.  If the key is imported into a zone,
            this is the TTL that will be used for it, unless there was
            already a DNSKEY RRset in place, in which case the existing TTL
            would take precedence.  If this value is not set and there
            is no existing DNSKEY RRset, the TTL will default to the
            SOA TTL. Setting the default TTL to <literal>0</literal>
            or <literal>none</literal> removes it from the key.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-h</term>
        <listitem>
          <para>
            Emit usage message and exit.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-V</term>
        <listitem>
          <para>
            Prints version information.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-v <replaceable class="parameter">level</replaceable></term>
        <listitem>
          <para>
            Sets the debugging level.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-E <replaceable class="parameter">engine</replaceable></term>
        <listitem>
          <para>
            Specifies the cryptographic hardware to use, when applicable.
          </para>
          <para>
            When BIND is built with OpenSSL PKCS#11 support, this defaults
            to the string "pkcs11", which identifies an OpenSSL engine
            that can drive a cryptographic accelerator or hardware service
            module.  When BIND is built with native PKCS#11 cryptography
            (--enable-native-pkcs11), it defaults to the path of the PKCS#11
            provider library specified via "--with-pkcs11".
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsection>

  <refsection><info><title>TIMING OPTIONS</title></info>

    <para>
      Dates can be expressed in the format YYYYMMDD or YYYYMMDDHHMMSS.
      If the argument begins with a '+' or '-', it is interpreted as
      an offset from the present time.  For convenience, if such an offset
      is followed by one of the suffixes 'y', 'mo', 'w', 'd', 'h', or 'mi',
      then the offset is computed in years (defined as 365 24-hour days,
      ignoring leap years), months (defined as 30 24-hour days), weeks,
      days, hours, or minutes, respectively.  Without a suffix, the offset
      is computed in seconds.  To unset a date, use 'none' or 'never'.
    </para>

    <variablelist>
      <varlistentry>
        <term>-P <replaceable class="parameter">date/offset</replaceable></term>
        <listitem>
          <para>
            Sets the date on which a key is to be published to the zone.
            After that date, the key will be included in the zone but will
            not be used to sign it.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-P sync <replaceable class="parameter">date/offset</replaceable></term>
        <listitem>
          <para>
            Sets the date on which CDS and CDNSKEY records that match this
            key are to be published to the zone.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-A <replaceable class="parameter">date/offset</replaceable></term>
        <listitem>
          <para>
            Sets the date on which the key is to be activated.  After that
            date, the key will be included in the zone and used to sign
            it.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-R <replaceable class="parameter">date/offset</replaceable></term>
        <listitem>
          <para>
            Sets the date on which the key is to be revoked.  After that
            date, the key will be flagged as revoked.  It will be included
            in the zone and will be used to sign it.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-I <replaceable class="parameter">date/offset</replaceable></term>
        <listitem>
          <para>
            Sets the date on which the key is to be retired.  After that
            date, the key will still be included in the zone, but it
            will not be used to sign it.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-D <replaceable class="parameter">date/offset</replaceable></term>
        <listitem>
          <para>
            Sets the date on which the key is to be deleted.  After that
            date, the key will no longer be included in the zone.  (It
            may remain in the key repository, however.)
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-D sync <replaceable class="parameter">date/offset</replaceable></term>
        <listitem>
          <para>
            Sets the date on which the CDS and CDNSKEY records that match this
            key are to be deleted.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-S <replaceable class="parameter">predecessor key</replaceable></term>
        <listitem>
          <para>
            Select a key for which the key being modified will be an
            explicit successor.  The name, algorithm, size, and type of the
            predecessor key must exactly match those of the key being
            modified.  The activation date of the successor key will be set
            to the inactivation date of the predecessor.  The publication
            date will be set to the activation date minus the prepublication
            interval, which defaults to 30 days.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-i <replaceable class="parameter">interval</replaceable></term>
        <listitem>
          <para>
            Sets the prepublication interval for a key.  If set, then
            the publication and activation dates must be separated by at least
            this much time.  If the activation date is specified but the
            publication date isn't, then the publication date will default
            to this much time before the activation date; conversely, if
            the publication date is specified but activation date isn't,
            then activation will be set to this much time after publication.
          </para>
          <para>
            If the key is being set to be an explicit successor to another
            key, then the default prepublication interval is 30 days;
            otherwise it is zero.
          </para>
          <para>
            As with date offsets, if the argument is followed by one of
            the suffixes 'y', 'mo', 'w', 'd', 'h', or 'mi', then the
            interval is measured in years, months, weeks, days, hours,
            or minutes, respectively.  Without a suffix, the interval is
            measured in seconds.
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsection>

  <refsection><info><title>PRINTING OPTIONS</title></info>

    <para>
      <command>dnssec-settime</command> can also be used to print the
      timing metadata associated with a key.
    </para>

    <variablelist>
      <varlistentry>
        <term>-u</term>
        <listitem>
          <para>
            Print times in UNIX epoch format.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-p <replaceable class="parameter">C/P/Psync/A/R/I/D/Dsync/all</replaceable></term>
        <listitem>
          <para>
            Print a specific metadata value or set of metadata values.
            The <option>-p</option> option may be followed by one or more
            of the following letters or strings to indicate which value
            or values to print:
            <option>C</option> for the creation date,
            <option>P</option> for the publication date,
            <option>Psync</option> for the CDS and CDNSKEY publication date,
            <option>A</option> for the activation date,
            <option>R</option> for the revocation date,
            <option>I</option> for the inactivation date,
            <option>D</option> for the deletion date, and
            <option>Dsync</option> for the CDS and CDNSKEY deletion date
            To print all of the metadata, use <option>-p all</option>.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
        <refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>dnssec-signzone</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>,
      <citetitle>RFC 5011</citetitle>.
    </para>
  </refsection>

</refentry>
