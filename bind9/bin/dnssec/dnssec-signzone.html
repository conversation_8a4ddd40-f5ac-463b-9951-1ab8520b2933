<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000-2009, 2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>dnssec-signzone</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.dnssec-signzone"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">dnssec-signzone</span>
     &#8212; DNSSEC zone signing tool
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">dnssec-signzone</code> 
       [<code class="option">-a</code>]
       [<code class="option">-c <em class="replaceable"><code>class</code></em></code>]
       [<code class="option">-d <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-D</code>]
       [<code class="option">-E <em class="replaceable"><code>engine</code></em></code>]
       [<code class="option">-e <em class="replaceable"><code>end-time</code></em></code>]
       [<code class="option">-f <em class="replaceable"><code>output-file</code></em></code>]
       [<code class="option">-g</code>]
       [<code class="option">-h</code>]
       [<code class="option">-i <em class="replaceable"><code>interval</code></em></code>]
       [<code class="option">-I <em class="replaceable"><code>input-format</code></em></code>]
       [<code class="option">-j <em class="replaceable"><code>jitter</code></em></code>]
       [<code class="option">-K <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-k <em class="replaceable"><code>key</code></em></code>]
       [<code class="option">-L <em class="replaceable"><code>serial</code></em></code>]
       [<code class="option">-l <em class="replaceable"><code>domain</code></em></code>]
       [<code class="option">-M <em class="replaceable"><code>maxttl</code></em></code>]
       [<code class="option">-N <em class="replaceable"><code>soa-serial-format</code></em></code>]
       [<code class="option">-o <em class="replaceable"><code>origin</code></em></code>]
       [<code class="option">-O <em class="replaceable"><code>output-format</code></em></code>]
       [<code class="option">-P</code>]
       [<code class="option">-p</code>]
       [<code class="option">-Q</code>]
       [<code class="option">-R</code>]
       [<code class="option">-r <em class="replaceable"><code>randomdev</code></em></code>]
       [<code class="option">-S</code>]
       [<code class="option">-s <em class="replaceable"><code>start-time</code></em></code>]
       [<code class="option">-T <em class="replaceable"><code>ttl</code></em></code>]
       [<code class="option">-t</code>]
       [<code class="option">-u</code>]
       [<code class="option">-v <em class="replaceable"><code>level</code></em></code>]
       [<code class="option">-V</code>]
       [<code class="option">-X <em class="replaceable"><code>extended end-time</code></em></code>]
       [<code class="option">-x</code>]
       [<code class="option">-z</code>]
       [<code class="option">-3 <em class="replaceable"><code>salt</code></em></code>]
       [<code class="option">-H <em class="replaceable"><code>iterations</code></em></code>]
       [<code class="option">-A</code>]
       {zonefile}
       [key...]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>dnssec-signzone</strong></span>
      signs a zone.  It generates
      NSEC and RRSIG records and produces a signed version of the
      zone. The security status of delegations from the signed zone
      (that is, whether the child zones are secure or not) is
      determined by the presence or absence of a
      <code class="filename">keyset</code> file for each child zone.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-a</span></dt>
<dd>
          <p>
            Verify all generated signatures.
          </p>
        </dd>
<dt><span class="term">-c <em class="replaceable"><code>class</code></em></span></dt>
<dd>
          <p>
            Specifies the DNS class of the zone.
          </p>
        </dd>
<dt><span class="term">-C</span></dt>
<dd>
          <p>
            Compatibility mode: Generate a
            <code class="filename">keyset-<em class="replaceable"><code>zonename</code></em></code>
            file in addition to
            <code class="filename">dsset-<em class="replaceable"><code>zonename</code></em></code>
            when signing a zone, for use by older versions of
            <span class="command"><strong>dnssec-signzone</strong></span>.
          </p>
        </dd>
<dt><span class="term">-d <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
          <p>
            Look for <code class="filename">dsset-</code> or
            <code class="filename">keyset-</code> files in <code class="option">directory</code>.
          </p>
        </dd>
<dt><span class="term">-D</span></dt>
<dd>
          <p>
	    Output only those record types automatically managed by
	    <span class="command"><strong>dnssec-signzone</strong></span>, i.e. RRSIG, NSEC,
	    NSEC3 and NSEC3PARAM records. If smart signing
	    (<code class="option">-S</code>) is used, DNSKEY records are also
	    included. The resulting file can be included in the original
	    zone file with <span class="command"><strong>$INCLUDE</strong></span>. This option
	    cannot be combined with <code class="option">-O raw</code>,
            <code class="option">-O map</code>, or serial number updating.
          </p>
        </dd>
<dt><span class="term">-E <em class="replaceable"><code>engine</code></em></span></dt>
<dd>
          <p>
            When applicable, specifies the hardware to use for
            cryptographic operations, such as a secure key store used
            for signing.
          </p>
          <p>
            When BIND is built with OpenSSL PKCS#11 support, this defaults
            to the string "pkcs11", which identifies an OpenSSL engine
            that can drive a cryptographic accelerator or hardware service
            module.  When BIND is built with native PKCS#11 cryptography
            (--enable-native-pkcs11), it defaults to the path of the PKCS#11
            provider library specified via "--with-pkcs11".
          </p>
        </dd>
<dt><span class="term">-g</span></dt>
<dd>
          <p>
            Generate DS records for child zones from
            <code class="filename">dsset-</code> or <code class="filename">keyset-</code>
            file.  Existing DS records will be removed.
          </p>
        </dd>
<dt><span class="term">-K <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
          <p>
            Key repository: Specify a directory to search for DNSSEC keys.
            If not specified, defaults to the current directory.
          </p>
        </dd>
<dt><span class="term">-k <em class="replaceable"><code>key</code></em></span></dt>
<dd>
          <p>
            Treat specified key as a key signing key ignoring any
            key flags.  This option may be specified multiple times.
          </p>
        </dd>
<dt><span class="term">-l <em class="replaceable"><code>domain</code></em></span></dt>
<dd>
          <p>
            Generate a DLV set in addition to the key (DNSKEY) and DS sets.
            The domain is appended to the name of the records.
          </p>
        </dd>
<dt><span class="term">-M <em class="replaceable"><code>maxttl</code></em></span></dt>
<dd>
          <p>
            Sets the maximum TTL for the signed zone.
            Any TTL higher than <em class="replaceable"><code>maxttl</code></em> in the
            input zone will be reduced to <em class="replaceable"><code>maxttl</code></em>
            in the output. This provides certainty as to the largest
            possible TTL in the signed zone, which is useful to know when
            rolling keys because it is the longest possible time before
            signatures that have been retrieved by resolvers will expire
            from resolver caches.  Zones that are signed with this
            option should be configured to use a matching
            <code class="option">max-zone-ttl</code> in <code class="filename">named.conf</code>.
            (Note: This option is incompatible with <code class="option">-D</code>,
            because it modifies non-DNSSEC data in the output zone.)
          </p>
        </dd>
<dt><span class="term">-s <em class="replaceable"><code>start-time</code></em></span></dt>
<dd>
          <p>
            Specify the date and time when the generated RRSIG records
            become valid.  This can be either an absolute or relative
            time.  An absolute start time is indicated by a number
            in YYYYMMDDHHMMSS notation; 20000530144500 denotes
            14:45:00 UTC on May 30th, 2000.  A relative start time is
            indicated by +N, which is N seconds from the current time.
            If no <code class="option">start-time</code> is specified, the current
            time minus 1 hour (to allow for clock skew) is used.
          </p>
        </dd>
<dt><span class="term">-e <em class="replaceable"><code>end-time</code></em></span></dt>
<dd>
          <p>
            Specify the date and time when the generated RRSIG records
            expire.  As with <code class="option">start-time</code>, an absolute
            time is indicated in YYYYMMDDHHMMSS notation.  A time relative
            to the start time is indicated with +N, which is N seconds from
            the start time.  A time relative to the current time is
            indicated with now+N.  If no <code class="option">end-time</code> is
            specified, 30 days from the start time is used as a default.
            <code class="option">end-time</code> must be later than
            <code class="option">start-time</code>.
          </p>
        </dd>
<dt><span class="term">-X <em class="replaceable"><code>extended end-time</code></em></span></dt>
<dd>
          <p>
            Specify the date and time when the generated RRSIG records
            for the DNSKEY RRset will expire.  This is to be used in cases
            when the DNSKEY signatures need to persist longer than
            signatures on other records; e.g., when the private component
            of the KSK is kept offline and the KSK signature is to be
            refreshed manually.
          </p>
          <p>
            As with <code class="option">start-time</code>, an absolute
            time is indicated in YYYYMMDDHHMMSS notation.  A time relative
            to the start time is indicated with +N, which is N seconds from
            the start time.  A time relative to the current time is
            indicated with now+N.  If no <code class="option">extended end-time</code> is
            specified, the value of <code class="option">end-time</code> is used as
            the default.  (<code class="option">end-time</code>, in turn, defaults to
            30 days from the start time.) <code class="option">extended end-time</code>
            must be later than <code class="option">start-time</code>.
          </p>
        </dd>
<dt><span class="term">-f <em class="replaceable"><code>output-file</code></em></span></dt>
<dd>
          <p>
            The name of the output file containing the signed zone.  The
            default is to append <code class="filename">.signed</code> to
            the input filename.  If <code class="option">output-file</code> is
            set to <code class="literal">"-"</code>, then the signed zone is
            written to the standard output, with a default output
            format of "full".
          </p>
        </dd>
<dt><span class="term">-h</span></dt>
<dd>
          <p>
            Prints a short summary of the options and arguments to
            <span class="command"><strong>dnssec-signzone</strong></span>.
          </p>
        </dd>
<dt><span class="term">-V</span></dt>
<dd>
	  <p>
	    Prints version information.
	  </p>
        </dd>
<dt><span class="term">-i <em class="replaceable"><code>interval</code></em></span></dt>
<dd>
          <p>
            When a previously-signed zone is passed as input, records
            may be resigned.  The <code class="option">interval</code> option
            specifies the cycle interval as an offset from the current
            time (in seconds).  If a RRSIG record expires after the
            cycle interval, it is retained.  Otherwise, it is considered
            to be expiring soon, and it will be replaced.
          </p>
          <p>
            The default cycle interval is one quarter of the difference
            between the signature end and start times.  So if neither
            <code class="option">end-time</code> or <code class="option">start-time</code>
            are specified, <span class="command"><strong>dnssec-signzone</strong></span>
            generates
            signatures that are valid for 30 days, with a cycle
            interval of 7.5 days.  Therefore, if any existing RRSIG records
            are due to expire in less than 7.5 days, they would be
            replaced.
          </p>
        </dd>
<dt><span class="term">-I <em class="replaceable"><code>input-format</code></em></span></dt>
<dd>
          <p>
            The format of the input zone file.
	    Possible formats are <span class="command"><strong>"text"</strong></span> (default),
	    <span class="command"><strong>"raw"</strong></span>, and <span class="command"><strong>"map"</strong></span>.
	    This option is primarily intended to be used for dynamic
            signed zones so that the dumped zone file in a non-text
            format containing updates can be signed directly.
	    The use of this option does not make much sense for
	    non-dynamic zones.
          </p>
        </dd>
<dt><span class="term">-j <em class="replaceable"><code>jitter</code></em></span></dt>
<dd>
          <p>
            When signing a zone with a fixed signature lifetime, all
            RRSIG records issued at the time of signing expires
            simultaneously.  If the zone is incrementally signed, i.e.
            a previously-signed zone is passed as input to the signer,
            all expired signatures have to be regenerated at about the
            same time.  The <code class="option">jitter</code> option specifies a
            jitter window that will be used to randomize the signature
            expire time, thus spreading incremental signature
            regeneration over time.
          </p>
          <p>
            Signature lifetime jitter also to some extent benefits
            validators and servers by spreading out cache expiration,
            i.e. if large numbers of RRSIGs don't expire at the same time
            from all caches there will be less congestion than if all
            validators need to refetch at mostly the same time.
          </p>
        </dd>
<dt><span class="term">-L <em class="replaceable"><code>serial</code></em></span></dt>
<dd>
          <p>
            When writing a signed zone to "raw" or "map" format, set the
            "source serial" value in the header to the specified serial
            number.  (This is expected to be used primarily for testing
            purposes.)
          </p>
        </dd>
<dt><span class="term">-n <em class="replaceable"><code>ncpus</code></em></span></dt>
<dd>
          <p>
            Specifies the number of threads to use.  By default, one
            thread is started for each detected CPU.
          </p>
        </dd>
<dt><span class="term">-N <em class="replaceable"><code>soa-serial-format</code></em></span></dt>
<dd>
          <p>
            The SOA serial number format of the signed zone.
	    Possible formats are <span class="command"><strong>"keep"</strong></span> (default),
            <span class="command"><strong>"increment"</strong></span>, <span class="command"><strong>"unixtime"</strong></span>,
            and <span class="command"><strong>"date"</strong></span>.
          </p>

          <div class="variablelist"><dl class="variablelist">
<dt><span class="term"><span class="command"><strong>"keep"</strong></span></span></dt>
<dd>
                <p>Do not modify the SOA serial number.</p>
	      </dd>
<dt><span class="term"><span class="command"><strong>"increment"</strong></span></span></dt>
<dd>
                <p>Increment the SOA serial number using RFC 1982
                      arithmetics.</p>
	      </dd>
<dt><span class="term"><span class="command"><strong>"unixtime"</strong></span></span></dt>
<dd>
                <p>Set the SOA serial number to the number of seconds
	        since epoch.</p>
	      </dd>
<dt><span class="term"><span class="command"><strong>"date"</strong></span></span></dt>
<dd>
                <p>Set the SOA serial number to today's date in
                YYYYMMDDNN format.</p>
	      </dd>
</dl></div>

        </dd>
<dt><span class="term">-o <em class="replaceable"><code>origin</code></em></span></dt>
<dd>
          <p>
            The zone origin.  If not specified, the name of the zone file
            is assumed to be the origin.
          </p>
        </dd>
<dt><span class="term">-O <em class="replaceable"><code>output-format</code></em></span></dt>
<dd>
          <p>
            The format of the output file containing the signed zone.
	    Possible formats are <span class="command"><strong>"text"</strong></span> (default),
            which is the standard textual representation of the zone;
	    <span class="command"><strong>"full"</strong></span>, which is text output in a
            format suitable for processing by external scripts;
            and <span class="command"><strong>"map"</strong></span>, <span class="command"><strong>"raw"</strong></span>,
            and <span class="command"><strong>"raw=N"</strong></span>, which store the zone in
            binary formats for rapid loading by <span class="command"><strong>named</strong></span>.
            <span class="command"><strong>"raw=N"</strong></span> specifies the format version of
            the raw zone file: if N is 0, the raw file can be read by
            any version of <span class="command"><strong>named</strong></span>; if N is 1, the file
            can be read by release 9.9.0 or higher; the default is 1.
          </p>
        </dd>
<dt><span class="term">-p</span></dt>
<dd>
          <p>
            Use pseudo-random data when signing the zone.  This is faster,
            but less secure, than using real random data.  This option
            may be useful when signing large zones or when the entropy
            source is limited.
          </p>
        </dd>
<dt><span class="term">-P</span></dt>
<dd>
          <p>
	    Disable post sign verification tests.
          </p>
          <p>
	    The post sign verification test ensures that for each algorithm
	    in use there is at least one non revoked self signed KSK key,
	    that all revoked KSK keys are self signed, and that all records
	    in the zone are signed by the algorithm.
	    This option skips these tests.
          </p>
        </dd>
<dt><span class="term">-Q</span></dt>
<dd>
          <p>
	    Remove signatures from keys that are no longer active.
          </p>
          <p>
            Normally, when a previously-signed zone is passed as input
            to the signer, and a DNSKEY record has been removed and
            replaced with a new one, signatures from the old key
            that are still within their validity period are retained.
	    This allows the zone to continue to validate with cached
	    copies of the old DNSKEY RRset.  The <code class="option">-Q</code>
            forces <span class="command"><strong>dnssec-signzone</strong></span> to remove
            signatures from keys that are no longer active. This
            enables ZSK rollover using the procedure described in
            RFC 4641, section ******* ("Pre-Publish Key Rollover").
          </p>
        </dd>
<dt><span class="term">-R</span></dt>
<dd>
          <p>
	    Remove signatures from keys that are no longer published.
          </p>
          <p>
            This option is similar to <code class="option">-Q</code>, except it
            forces <span class="command"><strong>dnssec-signzone</strong></span> to signatures from
            keys that are no longer published. This enables ZSK rollover
            using the procedure described in RFC 4641, section 4.2.1.2
            ("Double Signature Zone Signing Key Rollover").
          </p>
        </dd>
<dt><span class="term">-r <em class="replaceable"><code>randomdev</code></em></span></dt>
<dd>
          <p>
            Specifies the source of randomness.  If the operating
            system does not provide a <code class="filename">/dev/random</code>
            or equivalent device, the default source of randomness
            is keyboard input.  <code class="filename">randomdev</code>
            specifies
            the name of a character device or file containing random
            data to be used instead of the default.  The special value
            <code class="filename">keyboard</code> indicates that keyboard
            input should be used.
          </p>
        </dd>
<dt><span class="term">-S</span></dt>
<dd>
          <p>
            Smart signing: Instructs <span class="command"><strong>dnssec-signzone</strong></span> to
            search the key repository for keys that match the zone being
            signed, and to include them in the zone if appropriate.
          </p>
          <p>
            When a key is found, its timing metadata is examined to
            determine how it should be used, according to the following
            rules.  Each successive rule takes priority over the prior
            ones:
          </p>
          <div class="variablelist"><dl class="variablelist">
<dt></dt>
<dd>
                <p>
                  If no timing metadata has been set for the key, the key is
                  published in the zone and used to sign the zone.
                </p>
	      </dd>
<dt></dt>
<dd>
                <p>
                  If the key's publication date is set and is in the past, the
                  key is published in the zone.
                </p>
	      </dd>
<dt></dt>
<dd>
                <p>
                  If the key's activation date is set and in the past, the
                  key is published (regardless of publication date) and
                  used to sign the zone.
                </p>
	      </dd>
<dt></dt>
<dd>
                <p>
                  If the key's revocation date is set and in the past, and the
                  key is published, then the key is revoked, and the revoked key
                  is used to sign the zone.
                </p>
	      </dd>
<dt></dt>
<dd>
                <p>
                  If either of the key's unpublication or deletion dates are set
                  and in the past, the key is NOT published or used to sign the
                  zone, regardless of any other metadata.
                </p>
	      </dd>
</dl></div>
        </dd>
<dt><span class="term">-T <em class="replaceable"><code>ttl</code></em></span></dt>
<dd>
          <p>
            Specifies a TTL to be used for new DNSKEY records imported
            into the zone from the key repository.  If not
            specified, the default is the TTL value from the zone's SOA
            record.  This option is ignored when signing without
            <code class="option">-S</code>, since DNSKEY records are not imported
            from the key repository in that case.  It is also ignored if
            there are any pre-existing DNSKEY records at the zone apex,
            in which case new records' TTL values will be set to match
            them, or if any of the imported DNSKEY records had a default
            TTL value.  In the event of a a conflict between TTL values in
            imported keys, the shortest one is used.
          </p>
        </dd>
<dt><span class="term">-t</span></dt>
<dd>
          <p>
            Print statistics at completion.
          </p>
        </dd>
<dt><span class="term">-u</span></dt>
<dd>
          <p>
            Update NSEC/NSEC3 chain when re-signing a previously signed
            zone.  With this option, a zone signed with NSEC can be
            switched to NSEC3, or a zone signed with NSEC3 can
            be switch to NSEC or to NSEC3 with different parameters.
            Without this option, <span class="command"><strong>dnssec-signzone</strong></span> will
            retain the existing chain when re-signing.
          </p>
        </dd>
<dt><span class="term">-v <em class="replaceable"><code>level</code></em></span></dt>
<dd>
          <p>
            Sets the debugging level.
          </p>
        </dd>
<dt><span class="term">-x</span></dt>
<dd>
          <p>
            Only sign the DNSKEY RRset with key-signing keys, and omit
            signatures from zone-signing keys.  (This is similar to the
            <span class="command"><strong>dnssec-dnskey-kskonly yes;</strong></span> zone option in
            <span class="command"><strong>named</strong></span>.)
          </p>
        </dd>
<dt><span class="term">-z</span></dt>
<dd>
          <p>
            Ignore KSK flag on key when determining what to sign.  This
            causes KSK-flagged keys to sign all records, not just the
            DNSKEY RRset.  (This is similar to the
            <span class="command"><strong>update-check-ksk no;</strong></span> zone option in
            <span class="command"><strong>named</strong></span>.)
          </p>
        </dd>
<dt><span class="term">-3 <em class="replaceable"><code>salt</code></em></span></dt>
<dd>
          <p>
            Generate an NSEC3 chain with the given hex encoded salt.
	    A dash (<em class="replaceable"><code>salt</code></em>) can
	    be used to indicate that no salt is to be used when generating		    the NSEC3 chain.
          </p>
        </dd>
<dt><span class="term">-H <em class="replaceable"><code>iterations</code></em></span></dt>
<dd>
          <p>
	    When generating an NSEC3 chain, use this many iterations.  The
	    default is 10.
          </p>
        </dd>
<dt><span class="term">-A</span></dt>
<dd>
          <p>
	    When generating an NSEC3 chain set the OPTOUT flag on all
	    NSEC3 records and do not generate NSEC3 records for insecure
	    delegations.
          </p>
          <p>
	    Using this option twice (i.e., <code class="option">-AA</code>)
	    turns the OPTOUT flag off for all records.  This is useful
	    when using the <code class="option">-u</code> option to modify an NSEC3
	    chain which previously had OPTOUT set.
          </p>
        </dd>
<dt><span class="term">zonefile</span></dt>
<dd>
          <p>
            The file containing the zone to be signed.
          </p>
        </dd>
<dt><span class="term">key</span></dt>
<dd>
          <p>
	    Specify which keys should be used to sign the zone.  If
	    no keys are specified, then the zone will be examined
	    for DNSKEY records at the zone apex.  If these are found and
	    there are matching private keys, in the current directory,
	    then these will be used for signing.
          </p>
        </dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>EXAMPLE</h2>

    <p>
      The following command signs the <strong class="userinput"><code>example.com</code></strong>
      zone with the DSA key generated by <span class="command"><strong>dnssec-keygen</strong></span>
      (Kexample.com.+003+17247).  Because the <span class="command"><strong>-S</strong></span> option
      is not being used, the zone's keys must be in the master file
      (<code class="filename">db.example.com</code>).  This invocation looks
      for <code class="filename">dsset</code> files, in the current directory,
      so that DS records can be imported from them (<span class="command"><strong>-g</strong></span>).
    </p>
<pre class="programlisting">% dnssec-signzone -g -o example.com db.example.com \
Kexample.com.+003+17247
db.example.com.signed
%</pre>
    <p>
      In the above example, <span class="command"><strong>dnssec-signzone</strong></span> creates
      the file <code class="filename">db.example.com.signed</code>.  This
      file should be referenced in a zone statement in a
      <code class="filename">named.conf</code> file.
    </p>
    <p>
      This example re-signs a previously signed zone with default parameters.
      The private keys are assumed to be in the current directory.
    </p>
<pre class="programlisting">% cp db.example.com.signed db.example.com
% dnssec-signzone -o example.com db.example.com
db.example.com.signed
%</pre>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
        <span class="refentrytitle">dnssec-keygen</span>(8)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>,
      <em class="citetitle">RFC 4033</em>, <em class="citetitle">RFC 4641</em>.
    </p>
  </div>

</div></body>
</html>
