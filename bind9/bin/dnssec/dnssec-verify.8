.\" Copyright (C) 2012, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: dnssec-verify
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-01-15
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DNSSEC\-VERIFY" "8" "2014\-01\-15" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dnssec-verify \- DNSSEC zone verification tool
.SH "SYNOPSIS"
.HP \w'\fBdnssec\-verify\fR\ 'u
\fBdnssec\-verify\fR [\fB\-c\ \fR\fB\fIclass\fR\fR] [\fB\-E\ \fR\fB\fIengine\fR\fR] [\fB\-I\ \fR\fB\fIinput\-format\fR\fR] [\fB\-o\ \fR\fB\fIorigin\fR\fR] [\fB\-v\ \fR\fB\fIlevel\fR\fR] [\fB\-V\fR] [\fB\-x\fR] [\fB\-z\fR] {zonefile}
.SH "DESCRIPTION"
.PP
\fBdnssec\-verify\fR
verifies that a zone is fully signed for each algorithm found in the DNSKEY RRset for the zone, and that the NSEC / NSEC3 chains are complete\&.
.SH "OPTIONS"
.PP
\-c \fIclass\fR
.RS 4
Specifies the DNS class of the zone\&.
.RE
.PP
\-E \fIengine\fR
.RS 4
Specifies the cryptographic hardware to use, when applicable\&.
.sp
When BIND is built with OpenSSL PKCS#11 support, this defaults to the string "pkcs11", which identifies an OpenSSL engine that can drive a cryptographic accelerator or hardware service module\&. When BIND is built with native PKCS#11 cryptography (\-\-enable\-native\-pkcs11), it defaults to the path of the PKCS#11 provider library specified via "\-\-with\-pkcs11"\&.
.RE
.PP
\-I \fIinput\-format\fR
.RS 4
The format of the input zone file\&. Possible formats are
\fB"text"\fR
(default) and
\fB"raw"\fR\&. This option is primarily intended to be used for dynamic signed zones so that the dumped zone file in a non\-text format containing updates can be verified independently\&. The use of this option does not make much sense for non\-dynamic zones\&.
.RE
.PP
\-o \fIorigin\fR
.RS 4
The zone origin\&. If not specified, the name of the zone file is assumed to be the origin\&.
.RE
.PP
\-v \fIlevel\fR
.RS 4
Sets the debugging level\&.
.RE
.PP
\-V
.RS 4
Prints version information\&.
.RE
.PP
\-x
.RS 4
Only verify that the DNSKEY RRset is signed with key\-signing keys\&. Without this flag, it is assumed that the DNSKEY RRset will be signed by all active keys\&. When this flag is set, it will not be an error if the DNSKEY RRset is not signed by zone\-signing keys\&. This corresponds to the
\fB\-x\fR
option in
\fBdnssec\-signzone\fR\&.
.RE
.PP
\-z
.RS 4
Ignore the KSK flag on the keys when determining whether the zone if correctly signed\&. Without this flag it is assumed that there will be a non\-revoked, self\-signed DNSKEY with the KSK flag set for each algorithm and that RRsets other than DNSKEY RRset will be signed with a different DNSKEY without the KSK flag set\&.
.sp
With this flag set, we only require that for each algorithm, there will be at least one non\-revoked, self\-signed DNSKEY, regardless of the KSK flag state, and that other RRsets will be signed by a non\-revoked key for the same algorithm that includes the self\-signed key; the same key may be used for both purposes\&. This corresponds to the
\fB\-z\fR
option in
\fBdnssec\-signzone\fR\&.
.RE
.PP
zonefile
.RS 4
The file containing the zone to be signed\&.
.RE
.SH "SEE ALSO"
.PP
\fBdnssec-signzone\fR(8),
BIND 9 Administrator Reference Manual,
RFC 4033\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2012, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
