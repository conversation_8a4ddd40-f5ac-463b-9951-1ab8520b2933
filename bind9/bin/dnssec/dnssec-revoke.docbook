<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.dnssec-revoke">
  <info>
    <date>2014-01-15</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>dnssec-revoke</application></refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>dnssec-revoke</application></refname>
    <refpurpose>set the REVOKED bit on a DNSSEC key</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2009</year>
      <year>2011</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>dnssec-revoke</command>
      <arg choice="opt" rep="norepeat"><option>-hr</option></arg>
      <arg choice="opt" rep="norepeat"><option>-v <replaceable class="parameter">level</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-V</option></arg>
      <arg choice="opt" rep="norepeat"><option>-K <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-E <replaceable class="parameter">engine</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-f</option></arg>
      <arg choice="opt" rep="norepeat"><option>-R</option></arg>
      <arg choice="req" rep="norepeat">keyfile</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>dnssec-revoke</command>
      reads a DNSSEC key file, sets the REVOKED bit on the key as defined
      in RFC 5011, and creates a new pair of key files containing the
      now-revoked key.
    </para>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
	<term>-h</term>
        <listitem>
	  <para>
	    Emit usage message and exit.
	  </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-K <replaceable class="parameter">directory</replaceable></term>
        <listitem>
          <para>
            Sets the directory in which the key files are to reside.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>-r</term>
        <listitem>
	  <para>
	    After writing the new keyset files remove the original keyset
	    files.
	  </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-v <replaceable class="parameter">level</replaceable></term>
        <listitem>
          <para>
            Sets the debugging level.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>-V</term>
        <listitem>
	  <para>
	    Prints version information.
	  </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-E <replaceable class="parameter">engine</replaceable></term>
        <listitem>
          <para>
            Specifies the cryptographic hardware to use, when applicable.
          </para>
          <para>
            When BIND is built with OpenSSL PKCS#11 support, this defaults
            to the string "pkcs11", which identifies an OpenSSL engine
            that can drive a cryptographic accelerator or hardware service
            module.  When BIND is built with native PKCS#11 cryptography
            (--enable-native-pkcs11), it defaults to the path of the PKCS#11
            provider library specified via "--with-pkcs11".
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-f</term>
        <listitem>
          <para>
            Force overwrite: Causes <command>dnssec-revoke</command> to
            write the new key pair even if a file already exists matching
            the algorithm and key ID of the revoked key.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-R</term>
        <listitem>
          <para>
	    Print the key tag of the key with the REVOKE bit set but do
	    not revoke the key.
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
        <refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>,
      <citetitle>RFC 5011</citetitle>.
    </para>
  </refsection>

</refentry>
