# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

VERSION=@BIND9_VERSION@

@BIND9_MAKE_INCLUDES@

CINCLUDES =	-I${srcdir}/include ${DNS_INCLUDES} ${ISC_INCLUDES} \
		${IRS_INCLUDES} ${ISCCFG_INCLUDES} @DST_OPENSSL_INC@

CDEFINES =	@CRYPTO@ -DVERSION=\"${VERSION}\" \
		-DSYSCONFDIR=\"${sysconfdir}\"
CWARNINGS =

ISCCFGLIBS =	../../lib/isccfg/libisccfg.@A@
DNSLIBS =	../../lib/dns/libdns.@A@ @DNS_CRYPTO_LIBS@
ISCNOSYMLIBS =	../../lib/isc/libisc-nosymtbl.@A@
ISCLIBS =	../../lib/isc/libisc.@A@
IRSLIBS =	../../lib/irs/libirs.@A@

ISCCFGDEPLIBS =	../../lib/isccfg/libisccfg.@A@
DNSDEPLIBS =	../../lib/dns/libdns.@A@
ISCDEPLIBS =	../../lib/isc/libisc.@A@
IRSDEPLIBS =	../../lib/irs/libirs.@A@

DEPLIBS =	${DNSDEPLIBS} ${IRSDEPLIBS} ${ISCCFGDEPLIBS} ${ISCDEPLIBS}

LIBS =		${DNSLIBS} ${IRSLIBS} ${ISCCFGLIBS} ${ISCLIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}
NOSYMLIBS =	${DNSLIBS} ${IRSLIBS} ${ISCCFGLIBS} ${ISCNOSYMLIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}

SUBDIRS =

TARGETS =	delv@EXEEXT@

include ../../Makefile-infoblox.inc

OBJS =		delv.@O@

SRCS =		delv.c

MANPAGES =	delv.1

HTMLPAGES =	delv.html

MANOBJS =	${MANPAGES} ${HTMLPAGES}

@BIND9_MAKE_RULES@

delv@EXEEXT@: delv.@O@ ${DEPLIBS}
	export BASEOBJS="delv.@O@"; \
	export LIBS0="${DNSLIBS}"; \
	${FINALBUILDCMD}

installdirs:
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${bindir}
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${mandir}/man1

install:: delv@EXEEXT@ installdirs
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} \
		delv@EXEEXT@ ${DESTDIR}${bindir}
	${INSTALL_DATA} ${srcdir}/delv.1 ${DESTDIR}${mandir}/man1

uninstall::
	rm -f ${DESTDIR}${mandir}/man1/delv.1
	${LIBTOOL_MODE_UNINSTALL} rm -f ${DESTDIR}${bindir}/delv@EXEEXT@

doc man:: ${MANOBJS}

docclean manclean maintainer-clean::
	rm -f ${MANOBJS}

clean distclean maintainer-clean::
	rm -f ${TARGETS}
