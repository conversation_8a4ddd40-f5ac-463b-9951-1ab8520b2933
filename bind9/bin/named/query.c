/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

/*! \file */

#include <config.h>

#include <string.h>

#include <isc/hex.h>
#include <isc/mem.h>
#include <isc/print.h>
#include <isc/rwlock.h>
#include <isc/serial.h>
#include <isc/stats.h>
#include <isc/string.h>
#include <isc/thread.h>
#include <isc/util.h>

#include <dns/adb.h>
#include <dns/badcache.h>
#include <dns/byaddr.h>
#include <dns/cache.h>
#include <dns/db.h>
#include <dns/dlz.h>
#include <dns/dns64.h>
#include <dns/dnssec.h>
#include <dns/events.h>
#include <dns/message.h>
#include <dns/ncache.h>
#include <dns/nsec3.h>
#include <dns/order.h>
#include <dns/rdata.h>
#include <dns/rdataclass.h>
#include <dns/rdatalist.h>
#include <dns/rdataset.h>
#include <dns/rdatasetiter.h>
#include <dns/rdatastruct.h>
#include <dns/rdatatype.h>
#include <dns/resolver.h>
#include <dns/result.h>
#include <dns/stats.h>
#include <dns/tkey.h>
#include <dns/view.h>
#include <dns/zone.h>
#include <dns/zt.h>

#include <named/client.h>
#include <named/globals.h>
#include <named/log.h>
#include <named/server.h>
#include <named/sortlist.h>
#include <named/xfrout.h>
#ifdef	ORIGINAL_ISC_CODE
#else
#include <named/interfacemgr.h>
#include <isc/print.h>
#include <isc/msgs.h>
#include <isc/msgcat.h>
#include <dns/rbt.h>
#include <isc/atomic.h>
#include <sys/time.h>
#include <dns/cache.h>
#include <dns/dispatch.h>
#include <dns/infoblox_onedb.h>
#include <dns/infoblox_db_if.h>
#include <dns/infoblox_rpz_hitrate.h>
#include <infoblox/util.h>
#include <named/infoblox_query_util_private.h>
#include <dns/idns_find.h>
#include <dns/ib_dns_flood_detect.h>
#include <infoblox/one/snmp_trap.h>
#include <infoblox/one/events_publish.h>

static unsigned int rpz_notloaded_log_interval = 60;

static void release_orig_name(ns_client_t *client);
static void release_ibalias_orig_name(ns_client_t *client);

static const ib_subscriber_edns0_client_id_t *
ib_get_localid(ib_subscriber_edns0_client_id_t *local_id, ns_client_t *client);
static void
ib_trigger_prefetch_query(ns_client_t *client);

static isc_result_t
infoblox_get_redirect_rdataset(infoblox_redirection_type_t type,
			       dns_view_t *view,
			       dns_name_t *name,
			       dns_dbnode_t **node_p,
			       dns_db_t **dbp,
			       dns_rdataset_t *rdataset,
			       dns_rdataset_t *rds,
			       dns_rdatatype_t qtype) {
	dns_dbnode_t **tmpnode_p = NULL;
	dns_dbnode_t *tmpnode = NULL;
	isc_result_t result = ISC_R_SUCCESS;
	dns_db_t *db = *dbp;

	if (!view) {
		infoblox_log(1, "infoblox_get_%s_redirect_rdataset: no view",
			     (type == ib_nxdomain) ? "nxdomain" : "blacklist");
		result = ISC_R_FAILURE;
		goto cleanup;
	}

	if ((type == ib_nxdomain && view->nx_override && (!view->nx_enabled || !IS_NXDOMAIN_CONFIGURED(view))) ||
	    (type == ib_blacklist && view->blk_override && (!view->blk_enabled || !IS_BLACKLIST_CONFIGURED(view)))) {
		result = ISC_R_IGNORE;
		goto cleanup;
	}

	if (node_p == NULL || db == NULL || rdataset == NULL || rds == NULL) {
		infoblox_log(1, "infoblox_get_%s_redirect_rdataset: Invalid argument",
			     (type == ib_nxdomain) ? "nxdomain" : "blacklist");
		result = ISC_R_FAILURE;
		goto cleanup;
	}

	/* If recursion is on but the redirect configuration is not override,
	 * then the make_named_conf utility would generate the
	 * infoblox-xxx-redirect directive as inherited, not as configured
	 * at the view level. In fact the UI would show the proper redirect
	 * IP addresses as appropriate.
	 */

	nxdomain_blacklist_ip_t *redirect = NULL;
	if (type == ib_nxdomain) {
		if (view->nx_override || IS_NXDOMAIN_CONFIGURED(view)) {
			redirect = &view->nxdomain_ips[0];
		}
		else if (IS_NXDOMAIN_CONFIGURED(ns_g_server)) {
			redirect = &ns_g_server->nxdomain_ips[0];
		}
		else {
			result = ISC_R_IGNORE;
			goto cleanup;
		}
	} else {
		if (view->blk_override || IS_BLACKLIST_CONFIGURED(view))
			redirect = &view->blacklist_ips[0];
		else if (IS_BLACKLIST_CONFIGURED(ns_g_server))
			redirect = &ns_g_server->blacklist_ips[0];
		else {
			result = ISC_R_IGNORE;
			goto cleanup;
		}
	}

	dns_db_t *tmpdb = (type == ib_nxdomain) ?
		((view->nx_override || IS_NXDOMAIN_CONFIGURED(view)) ?
			view->nx_db : ns_g_server->nxdomain_db) :
		((view->blk_override || IS_BLACKLIST_CONFIGURED(view)) ?
			view->blk_db : ns_g_server->blacklist_db);

	// Get a pointer to the NXDOMAIN/blacklist node pointer for the view for this thread
	tmpnode_p = (type == ib_nxdomain) ? infoblox_get_nxnode_p(view) : infoblox_get_blknode_p(view);
	if (tmpnode_p == NULL) {
		infoblox_log(1, "Unable to obtain view %s's private %s node",
			     (type == ib_nxdomain) ? "NXDOMAIN" : "blacklist",
			     view->name);
		result = ISC_R_NOMEMORY; // Educated guess
		goto cleanup;
	}
	tmpnode = *tmpnode_p;

	(void)memset(rds, 0, sizeof(*rds));
	dns_rdataset_init(rds);

	result = infoblox_synthesize_node(tmpdb,
					  redirect,
					  (type == ib_nxdomain) ? view->nx_ttl : view->blk_ttl,
					  rds,
					  &tmpnode,
					  qtype);
	if (result != ISC_R_SUCCESS) {
		if (name)
			infoblox_log_name(1,
					  (type == ib_nxdomain) ?
					  "Error synthesizing NXDOMAIN node" :
					  "Error synthesizing blacklist node",
					  name,
					  isc_result_totext(result));
		else
			infoblox_log(1, "Error synthesizing %s node: %s",
				     (type == ib_nxdomain) ? "NXDOMAIN" : "blacklist",
				     isc_result_totext(result));
		goto cleanup;
	}

	/* For NXDOMAIN redirection we may not found record set
	 * (for example if only IPv4/IPv6 addresses configured for redirection
	 * and the type of query is AAAA/A), hence consider we don't found
	 * appropriate records. For blacklist redirection UI will
	 * force user to specify IPv4 addresses and only A type queries are
	 * supported for blacklist redirection. */
	if (!dns_rdataset_isassociated(rds)) {
		result = ISC_R_NOTFOUND;
		goto cleanup;
	}

	if (*tmpnode_p == NULL) {
		// Attach one initial time to the node, so that it doesn't
		// disappear when this instance of query_find() detaches.
		dns_db_attachnode(tmpdb, tmpnode, tmpnode_p);
	}

	if (*node_p != NULL)
		dns_db_detachnode(db, node_p);
	dns_db_attachnode(tmpdb, tmpnode, node_p);
	if (dns_rdataset_isassociated(rdataset)) {
		dns_rdataset_disassociate(rdataset);
	}
	dns_db_detach(dbp);
	dns_db_attach(tmpdb, dbp);
	*rdataset = *rds;

 cleanup:
	return (result);
}

static isc_result_t
infoblox_get_nxdomain_redirect_rdataset(dns_view_t *view,
                                        dns_name_t *name,
                                        dns_dbnode_t **node_p,
                                        dns_db_t **dbp,
                                        dns_rdataset_t *rdataset,
                                        dns_rdataset_t *nxrds,
                                        dns_rdatatype_t qtype) {
	return infoblox_get_redirect_rdataset(ib_nxdomain,
					      view,
					      name,
					      node_p,
					      dbp,
					      rdataset,
					      nxrds,
					      qtype);
}

static isc_result_t
infoblox_get_blacklist_redirect_rdataset(dns_view_t *view,
					 dns_name_t *name,
					 dns_dbnode_t **node_p,
					 dns_db_t **dbp,
					 dns_rdataset_t *rdataset,
					 dns_rdataset_t *blkrds,
					 dns_rdatatype_t qtype) {
	return infoblox_get_redirect_rdataset(ib_blacklist,
					      view,
					      name,
					      node_p,
					      dbp,
					      rdataset,
					      blkrds,
					      qtype);
}

#if defined(ISC_PLATFORM_HAVEXADDQ) && defined(ISC_PLATFORM_HAVECMPXCHG)
extern unsigned recursion_log_interval;

void infoblox_increment_cache_cleaning(dns_view_t *view);
void infoblox_perhaps_log_recursion (ns_client_t *client, dns_view_t *view);

static void
infoblox_increment_cache(isc_boolean_t is_cache, isc_boolean_t is_hit,
			 ns_client_t *client)
{
	if (is_cache && client && client->view) {
		dns_view_t *view = client->view;
		IB_DNS_ATTACK_INCREMENT_CHR(view->dns_attack, is_hit);
		isc_uint64_t *counter =
			is_hit ? &view->recursion_cache_hit :
			&view->recursion_cache_miss;
		(void)isc_atomic_xaddq((isc_int64_t *)counter, 1);
		infoblox_perhaps_log_recursion(client, NULL);
	}
}

void
infoblox_increment_cache_cleaning(dns_view_t *view) {
  if (view)
    (void)isc_atomic_xadd((isc_int32_t *) &view->recursion_cache_cleaning, 1);
}

// Set the Z bit for DNS responses if needed (only for non-TSIG UDP responses
// using IPv4/IPv6). This will prevent the corresponding response message from
// being cached in the hardware accelerator.  This function should be called
// during query processing, e.g., if the resulting response could be different
// depending on unusual query parameters (such as client's IP address).
static void
infoblox_perhaps_set_zbit(ns_client_t *client) {
	isc_boolean_t set_z_bit = infoblox_get_set_z_bit();
	if (set_z_bit && ((client->attributes & NS_CLIENTATTR_TCP) == 0) &&
	    (client->message->tsigkey == NULL)) {
		client->message->flags |= DNS_MESSAGEFLAG_Z;
	}
}

static isc_uint32_t global_recursion_quotas_when = 0;

static void
infoblox_log_pool_counters(infoblox_mempool_stats_t *stats, const char *view_name, const char *label) {
	// Reduce noise by only logging when we have gone over limit
	if (stats && stats->over > 0) {
		if (label == NULL)
			label = "<unknown>";
		infoblox_log (-1, "Recursion %s%s%s%s pool counters: used/limit/maxused/over = %lu/%lu/%lu/%lu",
			      view_name ? "view \"" : "",
			      view_name ? view_name : "",
			      view_name ? "\" " : "",
			      label,
			      (long unsigned)stats->alloc,
			      (long unsigned)stats->limit,
			      (long unsigned)stats->max,
			      (long unsigned)stats->over);
	}
}

// The "bounded allocations" are loops that ierate a maximum of 64 times.
// In order to reduce noise, we only log if we have gone over a threshold,
// or if the 'over' counter is non-zero.
#define INFOBLOX_ITERATION_TRIGGER 32

static void
infoblox_log_bounded_allocation(infoblox_mempool_stats_t *stats1, infoblox_mempool_stats_t *stats2,
				const char *view_name,
				const char *label1, const char *label2) {
	if ((stats1 && stats1->alloc && (stats1->over || stats1->max > INFOBLOX_ITERATION_TRIGGER)) ||
	    (stats2 && stats2->alloc && (stats2->over || stats2->max > INFOBLOX_ITERATION_TRIGGER))) {
		if (label1 == NULL)
			label1 = "<unknown>";
		if (label2 == NULL)
			label2 = "<unknown>";

		double avg1 = 0.0, avg2 = 0.0;
		unsigned i1 = 0, i2 = 0;
		unsigned dec1 = 0, dec2 = 0;
		if (stats1) {
			avg1 = (double)stats1->limit / (double)stats1->alloc;
			if (avg1 < 1.0)
				avg1 = 1.0;
			// This is ridiculous, but "%3.2f/%lu/%lu" didn't work here (was fine in
			// a test program using printf()). So, we roll our own fixed point conversion.
			i1 = (unsigned)avg1;
			dec1 = (unsigned)(((double)100)*(avg1-(double)i1));
		}
		if (stats2) {
			avg2 = (double)stats2->limit / (double)stats2->alloc;
			if (avg2 < 1.0)
				avg2 = 1.0;
			i2 = (unsigned)avg2;
			dec2 = (unsigned)(((double)100)*(avg2-(double)i2));
		}
		infoblox_log (-1, "Recursion %s%s%s%s: avg/max/over = %u.%02u/%lu/%lu; %s: avg/max/over = %u.%02u/%lu/%lu",
			      view_name ? "view \"" : "",
                              view_name ? view_name : "",
                              view_name ? "\" " : "",
                              label1,
			      i1, dec1, (long unsigned)stats1->max, (long unsigned)stats1->over,
			      label2,
			      i2, dec2, (long unsigned)stats2->max, (long unsigned)stats2->over);
	}
}

static void
infoblox_log_disp_quotas(dns_view_t *view) {
	if (view == NULL || view->resolver == NULL)
		return;

	dns_dispatch_t *disp;
	infoblox_mempool_stats_t disp_stat, random_port_stat, qid_stat;

	disp = dns_resolver_dispatchv4(view->resolver);
	if (disp) {
		infoblox_disp_get_stats(disp, &disp_stat, &random_port_stat, &qid_stat);
		infoblox_log_pool_counters(&disp_stat, view->name, "dispatch v4");
		infoblox_log_bounded_allocation(&random_port_stat, &qid_stat, view->name, "random ports v4", "query ids v4");
	}

	disp = dns_resolver_dispatchv6(view->resolver);
	if (disp) {
		infoblox_disp_get_stats(disp, &disp_stat, &random_port_stat, &qid_stat);
		infoblox_log_pool_counters(&disp_stat, view->name, "dispatch v6");
		infoblox_log_bounded_allocation(&random_port_stat, &qid_stat, view->name, "random ports v6", "query ids v6");
	}
}

static void
infoblox_log_dispatch_quotas(infoblox_dispmgr_stats_t *dispmgrstats, const char *view_name) {
	if (dispmgrstats) {
		infoblox_log_pool_counters(&dispmgrstats->depool, view_name, "event");
		infoblox_log_pool_counters(&dispmgrstats->rpool, view_name, "reply");
		infoblox_log_pool_counters(&dispmgrstats->dpool, view_name, "allocation");
		infoblox_log_pool_counters(&dispmgrstats->bpool, view_name, "buffer");
		infoblox_log_pool_counters(&dispmgrstats->spool, view_name, "socket");
	}
}

static void
infoblox_log_clients_per_query(dns_view_t *view) {
	if (view == NULL || view->resolver == NULL)
		return;

	unsigned softlimit = 0, hardlimit = 0;
	isc_uint64_t count = 0, total = 0;
	isc_uint32_t curlimit = 0, max = 0;
	isc_uint64_t softover = 0, hardover = 0;
	isc_uint64_t est_max_req_client_per_query = 0;

	infoblox_dns_resolver_get_clients_per_query_stats(view->resolver,
							  &softlimit, &hardlimit, &count, &total,
							  &curlimit, &max, &softover, &hardover,
							  &est_max_req_client_per_query);
	// No activity => don't log
	if (count == 0)
		return;

	double avg = (double)total / (double)count;
	if (avg < 1.0)
		avg = 1.0;
	// "%3.2f" didn't work here, so roll our own fixed point format
	unsigned i = (unsigned)avg;
	unsigned dec = (unsigned)(((double)100)*(avg-(double)i));

	// Don't log the boring average=1.00 case, unless we have over-limit events
	if ((i == 1 && dec == 0) && softover == 0 && hardover == 0)
		return;

	infoblox_log (-1, "Recursion %s%s%s clients per query: "
		      "limit/max/avg/soft-limit/limit-over/hard-limit/h-over/est-max-req = "
		      "%u/%u/%u.%02u/%u/%lu/%u/%lu/%lu",
		      view->name ? "view \"" : "",
		      view->name ? view->name : "",
		      view->name ? "\"" : "",
		      curlimit, max,
		      i, dec,
		      softlimit, (long unsigned)softover, hardlimit, (long unsigned)hardover,
		      (long unsigned)est_max_req_client_per_query);
}

static void
infoblox_log_recursion_quotas(dns_view_t *view) {
	isc_uint64_t rquota_sfail = 0, rquota_hfail = 0;
	isc_uint32_t rquota_used = 0, rquota_softlimit = 0, rquota_hardlimit = 0, rquota_max = 0;
	isc_uint32_t socketevents_maxused = 0, socketevents_limit = 0;
	isc_int32_t recursing_low_prio_count = 0;
	isc_uint64_t socketevents_reached = 0;
	infoblox_dispmgr_stats_t dispmgrstats;

	if (view == NULL) {
		// The counters are unsynchronized, but we can slightly improve
		// their reference to the same point in time by collecting the
		// data first, then logging.
		rquota_used = ns_g_server->recursionquota.used;
		rquota_max = ns_g_server->recursionquota.maxused;
		rquota_softlimit = ns_g_server->recursionquota.soft;
		rquota_sfail = ns_g_server->recursionquota.softfail;
		rquota_hardlimit = ns_g_server->recursionquota.max;
		rquota_hfail = ns_g_server->recursionquota.hardfail;
		recursing_low_prio_count = ns_g_server->infoblox_recursing_low_prio_count;

		infoblox_dispmgr_get_mempool_stats(ns_g_dispatchmgr, &dispmgrstats);

		if (infoblox_socket_get_fdevents_counters) {
			infoblox_socket_get_fdevents_counters(ns_g_socketmgr,
							      &socketevents_maxused,
							      &socketevents_limit,
							      &socketevents_reached);
		}

		infoblox_log (-1, "Recursion client quota: used/max/soft-limit/s-over/hard-limit/h-over/low-pri = %u/%u/%u/%lu/%u/%lu/%d",
			      rquota_used, rquota_max,
			      rquota_softlimit, (long unsigned)rquota_sfail,
			      rquota_hardlimit, (long unsigned)rquota_hfail,
			      recursing_low_prio_count);

		infoblox_log_dispatch_quotas(&dispmgrstats, NULL);

		// Reduce noise by only logging if we've reached the limit
		if (infoblox_socket_get_fdevents_counters && socketevents_reached) {
			infoblox_log (-1, "Recursion socket events: maxused/limit/reached = %u/%u/%lu",
				      socketevents_maxused, socketevents_limit, (long unsigned)socketevents_reached);
		}
	} else {
		infoblox_log_disp_quotas(view);
		infoblox_log_clients_per_query(view);
	}
}

void
infoblox_perhaps_log_recursion (ns_client_t *client, dns_view_t *view)
{
  time_t logged = 0;
  struct timeval now;
  dns_view_t *view_local = (view ? view : ((client && client->view) ? client->view : NULL));

  // If recursion_log_interval is 0, then disable logging
  if ( view_local == NULL || recursion_log_interval == 0)
     return;

  (void) gettimeofday (&now, NULL);
  logged = view_local->recursion_logged;

  if (view ||
      ((unsigned)(now.tv_sec - logged) >= recursion_log_interval)) {
    isc_uint64_t miss;
    isc_uint64_t hit;
    isc_uint64_t cache_cleaning;
    unsigned long cache_size;
    char *name;
    dns_cache_t *cache;

    if (view) {
      name = view->name;

      // Views with names that begin with '_' are generally used for
      // internal purposes, do not have a recursive cache in any
      // meaningful sense, and should be skipped when logging. The
      // exception is, of course, the "_default" view.
      if (name && name[0] == '_' && strcmp(&name[1], "default") != 0)
        return;
    } else {
      isc_int32_t old;

      old = isc_atomic_cmpxchg((isc_int32_t *)(&view_local->recursion_logged), logged, now.tv_sec);
      // If we got the old value back, we managed to change the
      // value, and should log. Otherwise, another thread has
      // performed the logging.
      if (old != logged)
	return;

      if (client && client->view)
	name = client->view->name;
      else
	name = NULL;
    }

    hit = view_local->recursion_cache_hit;
    miss = view_local->recursion_cache_miss;
    cache_cleaning = view_local->recursion_cache_cleaning;
    cache = (client && client->view && client->view->cache) ? client->view->cache :
      ((view && view->cache) ? view->cache : NULL);
    cache_size = infoblox_recursion_cache_size (cache);

    // We expect periodic cache cleaning activity to usually be zero, so
    // don't log the cache_cleaning value unless it is non-zero.
    if (cache_cleaning > 0) {
      // infoblox_log treats log level -1 as ISC_LOG_INFO and it's always logged
      infoblox_log (-1, "Recursion cache view \"%s\": size = %lu, hits = %" ISC_PRINT_QUADFORMAT "u,"
		    " misses = %" ISC_PRINT_QUADFORMAT "u, cleaning chunks = %" ISC_PRINT_QUADFORMAT "u",
		    name ? name : "",
		    (long unsigned)cache_size,
		    hit, miss, cache_cleaning);
    } else {
      // infoblox_log treats log level -1 as ISC_LOG_INFO and it's always logged
      infoblox_log (-1, "Recursion cache view \"%s\": size = %lu, hits = %" ISC_PRINT_QUADFORMAT "u,"
		    " misses = %" ISC_PRINT_QUADFORMAT "u",
		    name ? name : "", (long unsigned)cache_size, hit, miss);
    }

    // Recursion quotas
    if (view) {
      // We expect to be invoked with a non-NULL view argument when the
      // 'named' is about to exit. We also want the global stats in that
      // case, but only once, so we log those if the view name is the
      // default one.
      if (view->name && strcmp(view->name, "_default") == 0)
	infoblox_log_recursion_quotas(NULL);
      // And now the view itself
      infoblox_log_recursion_quotas(view);
    } else {
      // If we're the first thread to get here more than recursion_log_interval seconds
      // since the last time we logged global recursion quotas, do that logging now.
      logged = global_recursion_quotas_when;
      if ((unsigned)(now.tv_sec - logged) >= recursion_log_interval) {
	isc_uint32_t old;
	old = isc_atomic_cmpxchg((isc_int32_t *)(&global_recursion_quotas_when), logged, now.tv_sec);
	if (old == (isc_uint32_t)logged) {
	  infoblox_log_recursion_quotas(NULL);
	}
      }

      // Now the per-view quotas
      if (client && client->view) {
	infoblox_log_recursion_quotas(client->view);
      } else {
	for (view = ISC_LIST_HEAD(ns_g_server->viewlist);
	     view;
	     view = ISC_LIST_NEXT(view, link)) {
	  // Skip the built-in "_bind" and "_meta" views (in fact, skip
	  // any view with a name that starts with '_', and isn't "_default").
	  if (view->name &&
	      view->name[0] == '_' && strcmp(&view->name[1], "default") != 0)
	    continue;
	  infoblox_log_recursion_quotas(view);
	}
      }
    }
  }
}
#else /* defined(ISC_PLATFORM_HAVEXADDQ) && defined(ISC_PLATFORM_HAVECMPXCHG) */
static void
infoblox_increment_cache(isc_boolean_t is_cache, isc_boolean_t is_hit,
			 ns_client_t *client)
{
	UNUSED(is_cache);
	UNUSED(is_hit);
	UNUSED(client);
}
void
infoblox_increment_cache_cleaning(dns_view_t *view) {
  // Do nothing
}

void
infoblox_perhaps_log_recursion (ns_client_t *client, dns_view_t *view)
{
  // Do nothing
}
#endif
#endif

#if 0
/*
 * It has been recommended that DNS64 be changed to return excluded
 * AAAA addresses if DNS64 synthesis does not occur.  This minimises
 * the impact on the lookup results.  While most DNS AAAA lookups are
 * done to send IP packets to a host, not all of them are and filtering
 * excluded addresses has a negative impact on those uses.
 */
#define dns64_bis_return_excluded_addresses 1
#endif

/*% Partial answer? */
#define PARTIALANSWER(c)	(((c)->query.attributes & \
				  NS_QUERYATTR_PARTIALANSWER) != 0)
/*% Use Cache? */
#define USECACHE(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_CACHEOK) != 0)
/*% Recursion OK? */
#define RECURSIONOK(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_RECURSIONOK) != 0)
/*% Recursing? */
#define RECURSING(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_RECURSING) != 0)
/*% Cache glue ok? */
#define CACHEGLUEOK(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_CACHEGLUEOK) != 0)
/*% Want Recursion? */
#define WANTRECURSION(c)	(((c)->query.attributes & \
				  NS_QUERYATTR_WANTRECURSION) != 0)
/*% Want DNSSEC? */
#define WANTDNSSEC(c)		(((c)->attributes & \
				  NS_CLIENTATTR_WANTDNSSEC) != 0)
/*% Want WANTAD? */
#define WANTAD(c)		(((c)->attributes & \
				  NS_CLIENTATTR_WANTAD) != 0)
/*% Client presented a valid COOKIE. */
#define HAVECOOKIE(c)		(((c)->attributes & \
				  NS_CLIENTATTR_HAVECOOKIE) != 0)
/*% Client presented a COOKIE. */
#define WANTCOOKIE(c)		(((c)->attributes & \
				  NS_CLIENTATTR_WANTCOOKIE) != 0)
/*% Client presented a valid ECS option. */
#define ECS_RECEIVED(c)		(((c)->attributes & \
				  NS_CLIENTATTR_ECSRECEIVED) != 0)
/*% Whether to forward ECS options for this client. */
#define ECS_FORWARD(c)		(((c)->attributes & \
				  NS_CLIENTATTR_ECSFORWARD) != 0)

/*% TCP client? */
#define TCP_CLIENT(c)		(((c)->attributes & NS_CLIENTATTR_TCP) != 0)

/*% No authority? */
#define NOAUTHORITY(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_NOAUTHORITY) != 0)
/*% No additional? */
#define NOADDITIONAL(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_NOADDITIONAL) != 0)
/*% Secure? */
#define SECURE(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_SECURE) != 0)
/*% DNS64 A lookup? */
#define DNS64(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_DNS64) != 0)

#define DNS64EXCLUDE(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_DNS64EXCLUDE) != 0)

#define REDIRECT(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_REDIRECT) != 0)

/*% No QNAME Proof? */
#define NOQNAME(r)		(((r)->attributes & \
				  DNS_RDATASETATTR_NOQNAME) != 0)

#ifdef ORIGINAL_ISC_CODE
#else
// Skip NXDOMAIN redirection ?
#define	SKIPNXDOMAINREDIRECT(c)	(((c)->query.attributes & \
				  NS_QUERYATTR_SKIPNXDOMAINREDIRECT) != 0)
// Skip RPZ (because we have NXDOMAIN/Blacklist) ?
#define SKIPRPZ(c)		(((c)->query.attributes & \
				  NS_QUERYATTR_SKIPRPZ) != 0)

/*% Query ever rewritten? */
#define QRYPROCESSED(c)	(((c)->query.attributes & \
				  NS_QUERYATTR_QNAMEPROCESSED) != 0)

/*% Current query rewritten? */
#define QRYREWRITTEN(c)	(((c)->query.attributes & \
				  NS_QUERYATTR_QNAMEREWRITTEN) != 0)

/*% Current query asynchronously returning? */
#define PROXYASYNC(c)  (((c)->query.attributes & NS_QUERYATTR_PC_ASYNC) != 0)

/*% Initiated by IBALIAS record? */
#define IBALIASINITIATED(c)	(((c)->query.attributes & \
				  NS_QUERYATTR_IBALIAS_INITIATED) != 0)

/*% Re-doing RPZ rewrite to reconfirm there's no match in any RPZs? */
#define IB_REDOINGRPZ(c)	(((c)->query.attributes & \
				  NS_QUERYATTR_IB_REDOINGRPZ) != 0)
#endif	/* ORIGINAL_ISC_CODE */

#ifdef WANT_QUERYTRACE
static inline void
client_trace(ns_client_t *client, int level, const char *message) {
	if (client != NULL && client->query.qname != NULL) {
		if (isc_log_wouldlog(ns_g_lctx, level)) {
			char qbuf[DNS_NAME_FORMATSIZE];
			char tbuf[DNS_RDATATYPE_FORMATSIZE];
			dns_name_format(client->query.qname,
					qbuf, sizeof(qbuf));
			dns_rdatatype_format(client->query.qtype,
					     tbuf, sizeof(tbuf));
			isc_log_write(ns_g_lctx,
				      NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_QUERY, level,
				      "query client=%p thread=0x%lx "
				      "(%s/%s): %s",
				      client,
				      (unsigned long) isc_thread_self(),
				      qbuf, tbuf, message);
		}
	 } else {
		isc_log_write(ns_g_lctx,
			      NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, level,
			      "query client=%p thread=0x%lx "
			      "(<unknown-query>): %s",
			      client,
			      (unsigned long) isc_thread_self(),
			      message);
	}
}
#define CTRACE(l,m)	  client_trace(client, l, m)
#else
#define CTRACE(l,m) ((void)m)
#endif /* WANT_QUERYTRACE */


#define DNS_GETDB_NOEXACT 0x01U
#define DNS_GETDB_NOLOG 0x02U
#define DNS_GETDB_PARTIAL 0x04U
#define DNS_GETDB_IGNOREACL 0x08U

#define PENDINGOK(x)	(((x) & DNS_DBFIND_PENDINGOK) != 0)

#define SFCACHE_CDFLAG 0x1

/*
 * These have the same semantics as:
 *
 * 	foo_attach(b, a);
 *	foo_detach(&a);
 *
 * without the locking and magic testing.
 *
 * We use SAVE and RESTORE as that shows the operation being performed.
 */
#define SAVE(a, b) do { INSIST(a == NULL); a = b; b = NULL; } while (0)
#define RESTORE(a, b) SAVE(a, b)

typedef struct client_additionalctx {
	ns_client_t *client;
	dns_rdataset_t *rdataset;
} client_additionalctx_t;

static isc_result_t
query_find(ns_client_t *client, dns_fetchevent_t *event, dns_rdatatype_t qtype);

static isc_boolean_t
validate(ns_client_t *client, dns_db_t *db, dns_name_t *name,
	 dns_rdataset_t *rdataset, dns_rdataset_t *sigrdataset);

static void
query_findclosestnsec3(dns_name_t *qname, dns_db_t *db,
		       dns_dbversion_t *version, ns_client_t *client,
		       dns_rdataset_t *rdataset, dns_rdataset_t *sigrdataset,
		       dns_name_t *fname, isc_boolean_t exact,
		       dns_name_t *found);

static inline void
log_queryerror(ns_client_t *client, isc_result_t result, int line, int level);

static void
rpz_st_clear(ns_client_t *client);

static isc_boolean_t
rpz_ck_dnssec(ns_client_t *client, isc_result_t qresult,
	      dns_rdataset_t *rdataset, dns_rdataset_t *sigrdataset);

/*%
 * Increment query statistics counters.
 */
void
ns_query_incstats(ns_client_t *client, isc_statscounter_t counter) {
	dns_zone_t *zone = client->query.authzone;
	dns_rdatatype_t qtype;
	dns_rdataset_t *rdataset;
	isc_stats_t *zonestats;
	dns_stats_t *querystats = NULL;
#ifdef ORIGINAL_ISC_CODE
#else
	// for RPZ events, update counters for corresponding RPZ zone, not
	// queried zone
	if (counter == dns_nsstatscounter_rpzrewrite ||
	    counter == dns_nsstatscounter_rpznxdomainnodata ||
	    counter == dns_nsstatscounter_rpzpassthru ||
	    counter == dns_nsstatscounter_rpzsubstitute) {
		if (client->query.rpz_st &&
		    client->query.rpz_st->m.rpz) {
			// originally, we thought that rpz_st->m.zone reflects
			// RPZ zone which matched, but testing revealed that
			// it's (always?) NULL. So instead, we keep our own
			// reference to corresponding RPZ zone inside
			// dns_rpz_zone_t.
			zone = client->query.rpz_st->m.rpz->ibzone;
		} else {
			// unexpected client structure,  skip per-zone counters update
			zone = NULL;
		}
	}
#endif

	isc_stats_increment(ns_g_server->nsstats, counter);

	if (zone == NULL)
		return;

	/* Do regular response type stats */
	zonestats = dns_zone_getrequeststats(zone);

	if (zonestats != NULL)
		isc_stats_increment(zonestats, counter);

	/* Do query type statistics
	 *
	 * We only increment per-type if we're using the authoritative
	 * answer counter, preventing double-counting.
	 */
	if (counter == dns_nsstatscounter_authans) {
		querystats = dns_zone_getrcvquerystats(zone);
		if (querystats != NULL) {
			rdataset = ISC_LIST_HEAD(client->query.qname->list);
			if (rdataset != NULL) {
				qtype = rdataset->type;
				dns_rdatatypestats_increment(querystats, qtype);
			}
		}
	}
}

static void
query_send(ns_client_t *client) {
	isc_statscounter_t counter;
#ifdef ORIGINAL_ISC_CODE
#else
	if (ns_g_noaa) {
		client->message->flags &= ~DNS_MESSAGEFLAG_AA;
	}
#endif
	if ((client->message->flags & DNS_MESSAGEFLAG_AA) == 0)
		ns_query_incstats(client, dns_nsstatscounter_nonauthans);
	else
		ns_query_incstats(client, dns_nsstatscounter_authans);

	if (client->message->rcode == dns_rcode_noerror) {
		dns_section_t answer = DNS_SECTION_ANSWER;
		if (ISC_LIST_EMPTY(client->message->sections[answer])) {
			if (client->query.isreferral)
				counter = dns_nsstatscounter_referral;
			else
#ifdef ORIGINAL_ISC_CODE
				counter = dns_nsstatscounter_nxrrset;
#else
			{
				counter = dns_nsstatscounter_nxrrset;
				if (client->view && client->view->infoblox_top_query_nxdomain) {
					infoblox_tq_nxdomain_increment(client->view, client->query.qname,
								       dns_rcode_nxrrset);
                                }
			}
#endif
		} else
			counter = dns_nsstatscounter_success;
	} else if (client->message->rcode == dns_rcode_nxdomain)
#ifdef ORIGINAL_ISC_CODE
		counter = dns_nsstatscounter_nxdomain;
#else
	{
		counter = dns_nsstatscounter_nxdomain;
		if (client->view && client->view->infoblox_top_query_nxdomain) {
			infoblox_tq_nxdomain_increment(client->view, client->query.qname,
						       dns_rcode_nxdomain);
		}
	}
#endif
	else if (client->message->rcode == dns_rcode_badcookie)
		counter = dns_nsstatscounter_badcookie;
	else /* We end up here in case of YXDOMAIN, and maybe others */
		counter = dns_nsstatscounter_failure;

	ns_query_incstats(client, counter);
	ns_client_send(client);
}

static void
query_error(ns_client_t *client, isc_result_t result, int line) {
	int loglevel = ISC_LOG_DEBUG(3);

	switch (result) {
	case DNS_R_SERVFAIL:
		loglevel = ISC_LOG_DEBUG(1);
		ns_query_incstats(client, dns_nsstatscounter_servfail);
#ifdef ORIGINAL_ISC_CODE
#else
		if (client->view && client->view->infoblox_top_query_servfail &&
		    client->query.qname != NULL)
		{
			infoblox_tq_servfail_increment(client->view, client->query.qname,
						       ib_servfail_sent);
		}
#endif
		break;
	case DNS_R_FORMERR:
		ns_query_incstats(client, dns_nsstatscounter_formerr);
		break;
	default:
		ns_query_incstats(client, dns_nsstatscounter_failure);
		break;
	}

	if (ns_g_server->log_queries)
		loglevel = ISC_LOG_INFO;

	log_queryerror(client, result, line, loglevel);

	ns_client_error(client, result);
}

static void
query_next(ns_client_t *client, isc_result_t result) {
	if (result == DNS_R_DUPLICATE)
		ns_query_incstats(client, dns_nsstatscounter_duplicate);
	else if (result == DNS_R_DROP)
		ns_query_incstats(client, dns_nsstatscounter_dropped);
	else
		ns_query_incstats(client, dns_nsstatscounter_failure);
	ns_client_next(client, result);
}

static inline void
query_freefreeversions(ns_client_t *client, isc_boolean_t everything) {
	ns_dbversion_t *dbversion, *dbversion_next;
	unsigned int i;

	for (dbversion = ISC_LIST_HEAD(client->query.freeversions), i = 0;
	     dbversion != NULL;
	     dbversion = dbversion_next, i++)
	{
		dbversion_next = ISC_LIST_NEXT(dbversion, link);
		/*
		 * If we're not freeing everything, we keep the first three
		 * dbversions structures around.
		 */
		if (i > 3 || everything) {
			ISC_LIST_UNLINK(client->query.freeversions, dbversion,
					link);
			isc_mem_put(client->mctx, dbversion,
				    sizeof(*dbversion));
		}
	}
}

void
ns_query_cancel(ns_client_t *client) {
	REQUIRE(NS_CLIENT_VALID(client));

	LOCK(&client->query.fetchlock);
	if (client->query.fetch != NULL) {
		dns_resolver_cancelfetch(client->query.fetch);

		client->query.fetch = NULL;
	}
	UNLOCK(&client->query.fetchlock);
}

static inline void
query_putrdataset(ns_client_t *client, dns_rdataset_t **rdatasetp) {
	dns_rdataset_t *rdataset = *rdatasetp;

	CTRACE(ISC_LOG_DEBUG(3), "query_putrdataset");
	if (rdataset != NULL) {
		if (dns_rdataset_isassociated(rdataset))
			dns_rdataset_disassociate(rdataset);
		dns_message_puttemprdataset(client->message, rdatasetp);
	}
	CTRACE(ISC_LOG_DEBUG(3), "query_putrdataset: done");
}

static inline void
query_reset(ns_client_t *client, isc_boolean_t everything) {
	isc_buffer_t *dbuf, *dbuf_next;
	ns_dbversion_t *dbversion, *dbversion_next;
#ifdef ORIGINAL_ISC_CODE
#else
	ib_prefetchinfo_t *prefetchinfo;
#endif

	CTRACE(ISC_LOG_DEBUG(3), "query_reset");

	/*%
	 * Reset the query state of a client to its default state.
	 */

	/*
	 * Cancel the fetch if it's running.
	 */
	ns_query_cancel(client);

	/*
	 * Cleanup any active versions.
	 */
	for (dbversion = ISC_LIST_HEAD(client->query.activeversions);
	     dbversion != NULL;
	     dbversion = dbversion_next) {
		dbversion_next = ISC_LIST_NEXT(dbversion, link);
		dns_db_closeversion(dbversion->db, &dbversion->version,
				    ISC_FALSE);
		dns_db_detach(&dbversion->db);
		ISC_LIST_INITANDAPPEND(client->query.freeversions,
				      dbversion, link);
	}
	ISC_LIST_INIT(client->query.activeversions);

	if (client->query.authdb != NULL)
		dns_db_detach(&client->query.authdb);
	if (client->query.authzone != NULL)
		dns_zone_detach(&client->query.authzone);

	if (client->query.dns64_aaaa != NULL)
		query_putrdataset(client, &client->query.dns64_aaaa);
	if (client->query.dns64_sigaaaa != NULL)
		query_putrdataset(client, &client->query.dns64_sigaaaa);
	if (client->query.dns64_aaaaok != NULL) {
		isc_mem_put(client->mctx, client->query.dns64_aaaaok,
			    client->query.dns64_aaaaoklen *
			    sizeof(isc_boolean_t));
		client->query.dns64_aaaaok =  NULL;
		client->query.dns64_aaaaoklen =  0;
	}

	query_putrdataset(client, &client->query.redirect.rdataset);
	query_putrdataset(client, &client->query.redirect.sigrdataset);
	if (client->query.redirect.db != NULL) {
		if (client->query.redirect.node != NULL)
			dns_db_detachnode(client->query.redirect.db,
					  &client->query.redirect.node);
		dns_db_detach(&client->query.redirect.db);
	}
	if (client->query.redirect.zone != NULL)
		dns_zone_detach(&client->query.redirect.zone);

	query_freefreeversions(client, everything);

	for (dbuf = ISC_LIST_HEAD(client->query.namebufs);
	     dbuf != NULL;
	     dbuf = dbuf_next) {
		dbuf_next = ISC_LIST_NEXT(dbuf, link);
		if (dbuf_next != NULL || everything) {
			ISC_LIST_UNLINK(client->query.namebufs, dbuf, link);
			isc_buffer_free(&dbuf);
		}
	}

#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * If we are resetting the query on completion of processing one
	 * (in which case we have a qname) and we need to monitor RPZ hit rate,
	 * update the relevant counters and trigger a deeper consideration
	 * if the total count reaches the threshold.
	 *
	 * Note: these counts are global (shared by the entire server),
	 * so, for example, if we have multiple views with or without using
	 * RPZs it's possible that the hit rate is considered normal
	 * incorrectly even if it's actually not.
	 */
	if (client->query.qname != NULL &&
	    ns_g_server->ib_rpz_hit_monitor != NULL)
	{
		infoblox_threshold_monitor_count(
			ns_g_server->ib_rpz_hit_monitor, 0,
			client->query.infoblox_rpz_rewritten);
	}
#endif

#ifdef ORIGINAL_ISC_CODE
	if (client->query.restarts > 0) {
#else
	if (client->query.restarts > 0 || QRYPROCESSED(client)) {
#endif
		/*
		 * client->query.qname was dynamically allocated.
		 */
		dns_message_puttempname(client->message,
					&client->query.qname);
	}
	client->query.qname = NULL;
#ifdef ORIGINAL_ISC_CODE
#else
	client->query.attributes &= ~(NS_QUERYATTR_QNAMEPROCESSED|
				      NS_QUERYATTR_QNAMEREWRITTEN);
	release_orig_name(client); /* ORIGQNAMESTORED is cleared here */
	client->query.ib_orig_qname = NULL;
	client->query.ib_qname = NULL;

	/* ibalias_orig_qname and IBALIAS_ORIGQNAMESTORED is cleared inside */
	release_ibalias_orig_name(client);
	client->query.ibalias_ttl = ISC_UINT32_MAX;
#endif
	client->query.attributes = (NS_QUERYATTR_RECURSIONOK |
				    NS_QUERYATTR_CACHEOK |
				    NS_QUERYATTR_SECURE);
	client->query.restarts = 0;
	client->query.timerset = ISC_FALSE;
	if (client->query.rpz_st != NULL) {
		rpz_st_clear(client);
		if (everything) {
			isc_mem_put(client->mctx, client->query.rpz_st,
				    sizeof(*client->query.rpz_st));
			client->query.rpz_st = NULL;
		}
	}
	client->query.origqname = NULL;
	client->query.dboptions = 0;
	client->query.fetchoptions = 0;
	client->query.gluedb = NULL;
	client->query.authdbset = ISC_FALSE;
	client->query.isreferral = ISC_FALSE;
	client->query.dns64_options = 0;
	client->query.dns64_ttl = ISC_UINT32_MAX;
	client->query.resp_result = DNS_R_UNKNOWN;
	client->query.qtype = dns_rdatatype_none;
#ifdef ORIGINAL_ISC_CODE
#else
	client->query.infoblox_check_perhaps_log_query_or_response_done = ISC_FALSE;
	client->query.infoblox_perhaps_log_query_or_response = ISC_FALSE;
	client->query.infoblox_have_formatted_qname = ISC_FALSE;
	client->query.infoblox_rpz_found_white_list = ISC_FALSE;
	client->query.infoblox_rpz_rewrite_candidate = ISC_FALSE;
	client->query.infoblox_rpz_rewritten = ISC_FALSE;
        /* Cleanup prefetchinfo if present */
	for (prefetchinfo = ISC_LIST_HEAD(client->query.prefetchinfo);
	     prefetchinfo != NULL;
	     prefetchinfo = ISC_LIST_HEAD(client->query.prefetchinfo))
	{
		ISC_LIST_UNLINK(client->query.prefetchinfo, prefetchinfo, link);
		dns_name_free(&prefetchinfo->ib_prefetchname, client->mctx);
		isc_mem_put(client->mctx, prefetchinfo, sizeof(ib_prefetchinfo_t));
	}
#endif
}

static void
query_next_callback(ns_client_t *client) {
	query_reset(client, ISC_FALSE);
}

void
ns_query_free(ns_client_t *client) {
	REQUIRE(NS_CLIENT_VALID(client));

	query_reset(client, ISC_TRUE);
}

static inline isc_result_t
query_newnamebuf(ns_client_t *client) {
	isc_buffer_t *dbuf;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "query_newnamebuf");
	/*%
	 * Allocate a name buffer.
	 */

	dbuf = NULL;
	result = isc_buffer_allocate(client->mctx, &dbuf, 1024);
	if (result != ISC_R_SUCCESS) {
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_newnamebuf: isc_buffer_allocate failed: done");
		return (result);
	}
	ISC_LIST_APPEND(client->query.namebufs, dbuf, link);

	CTRACE(ISC_LOG_DEBUG(3), "query_newnamebuf: done");
	return (ISC_R_SUCCESS);
}

static inline isc_buffer_t *
query_getnamebuf(ns_client_t *client) {
	isc_buffer_t *dbuf;
	isc_result_t result;
	isc_region_t r;

	CTRACE(ISC_LOG_DEBUG(3), "query_getnamebuf");
	/*%
	 * Return a name buffer with space for a maximal name, allocating
	 * a new one if necessary.
	 */

	if (ISC_LIST_EMPTY(client->query.namebufs)) {
		result = query_newnamebuf(client);
		if (result != ISC_R_SUCCESS) {
		    CTRACE(ISC_LOG_DEBUG(3),
			   "query_getnamebuf: query_newnamebuf failed: done");
			return (NULL);
		}
	}

	dbuf = ISC_LIST_TAIL(client->query.namebufs);
	INSIST(dbuf != NULL);
	isc_buffer_availableregion(dbuf, &r);
	if (r.length < DNS_NAME_MAXWIRE) {
		result = query_newnamebuf(client);
		if (result != ISC_R_SUCCESS) {
		    CTRACE(ISC_LOG_DEBUG(3),
			   "query_getnamebuf: query_newnamebuf failed: done");
			return (NULL);

		}
		dbuf = ISC_LIST_TAIL(client->query.namebufs);
		isc_buffer_availableregion(dbuf, &r);
		INSIST(r.length >= 255);
	}
	CTRACE(ISC_LOG_DEBUG(3), "query_getnamebuf: done");
	return (dbuf);
}

static inline void
query_keepname(ns_client_t *client, dns_name_t *name, isc_buffer_t *dbuf) {
	isc_region_t r;

	CTRACE(ISC_LOG_DEBUG(3), "query_keepname");
	/*%
	 * 'name' is using space in 'dbuf', but 'dbuf' has not yet been
	 * adjusted to take account of that.  We do the adjustment.
	 */

	REQUIRE((client->query.attributes & NS_QUERYATTR_NAMEBUFUSED) != 0);

	dns_name_toregion(name, &r);
	isc_buffer_add(dbuf, r.length);
	dns_name_setbuffer(name, NULL);
	client->query.attributes &= ~NS_QUERYATTR_NAMEBUFUSED;
}

static inline void
query_releasename(ns_client_t *client, dns_name_t **namep) {
	dns_name_t *name = *namep;

	/*%
	 * 'name' is no longer needed.  Return it to our pool of temporary
	 * names.  If it is using a name buffer, relinquish its exclusive
	 * rights on the buffer.
	 */

	CTRACE(ISC_LOG_DEBUG(3), "query_releasename");
	if (dns_name_hasbuffer(name)) {
		INSIST((client->query.attributes & NS_QUERYATTR_NAMEBUFUSED)
		       != 0);
		client->query.attributes &= ~NS_QUERYATTR_NAMEBUFUSED;
	}
	dns_message_puttempname(client->message, namep);
	CTRACE(ISC_LOG_DEBUG(3), "query_releasename: done");
}

static inline dns_name_t *
query_newname(ns_client_t *client, isc_buffer_t *dbuf,
	      isc_buffer_t *nbuf)
{
	dns_name_t *name;
	isc_region_t r;
	isc_result_t result;

	REQUIRE((client->query.attributes & NS_QUERYATTR_NAMEBUFUSED) == 0);

	CTRACE(ISC_LOG_DEBUG(3), "query_newname");
	name = NULL;
	result = dns_message_gettempname(client->message, &name);
	if (result != ISC_R_SUCCESS) {
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_newname: dns_message_gettempname failed: done");
		return (NULL);
	}
	isc_buffer_availableregion(dbuf, &r);
	isc_buffer_init(nbuf, r.base, r.length);
	dns_name_init(name, NULL);
	dns_name_setbuffer(name, nbuf);
	client->query.attributes |= NS_QUERYATTR_NAMEBUFUSED;

	CTRACE(ISC_LOG_DEBUG(3), "query_newname: done");
	return (name);
}

static inline dns_rdataset_t *
query_newrdataset(ns_client_t *client) {
	dns_rdataset_t *rdataset;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "query_newrdataset");
	rdataset = NULL;
	result = dns_message_gettemprdataset(client->message, &rdataset);
	if (result != ISC_R_SUCCESS) {
	  CTRACE(ISC_LOG_DEBUG(3),
		 "query_newrdataset: "
		 "dns_message_gettemprdataset failed: done");
		return (NULL);
	}

	CTRACE(ISC_LOG_DEBUG(3), "query_newrdataset: done");
	return (rdataset);
}

static inline isc_result_t
query_newdbversion(ns_client_t *client, unsigned int n) {
	unsigned int i;
	ns_dbversion_t *dbversion;

	for (i = 0; i < n; i++) {
		dbversion = isc_mem_get(client->mctx, sizeof(*dbversion));
		if (dbversion != NULL) {
			dbversion->db = NULL;
			dbversion->version = NULL;
			ISC_LIST_INITANDAPPEND(client->query.freeversions,
					      dbversion, link);
		} else {
			/*
			 * We only return ISC_R_NOMEMORY if we couldn't
			 * allocate anything.
			 */
			if (i == 0)
				return (ISC_R_NOMEMORY);
			else
				return (ISC_R_SUCCESS);
		}
	}

	return (ISC_R_SUCCESS);
}

static inline ns_dbversion_t *
query_getdbversion(ns_client_t *client) {
	isc_result_t result;
	ns_dbversion_t *dbversion;

	if (ISC_LIST_EMPTY(client->query.freeversions)) {
		result = query_newdbversion(client, 1);
		if (result != ISC_R_SUCCESS)
			return (NULL);
	}
	dbversion = ISC_LIST_HEAD(client->query.freeversions);
	INSIST(dbversion != NULL);
	ISC_LIST_UNLINK(client->query.freeversions, dbversion, link);

	return (dbversion);
}

isc_result_t
ns_query_init(ns_client_t *client) {
	isc_result_t result;

	REQUIRE(NS_CLIENT_VALID(client));

	ISC_LIST_INIT(client->query.namebufs);
	ISC_LIST_INIT(client->query.activeversions);
	ISC_LIST_INIT(client->query.freeversions);
	client->query.restarts = 0;
	client->query.timerset = ISC_FALSE;
	client->query.rpz_st = NULL;
	client->query.qname = NULL;
#ifdef ORIGINAL_ISC_CODE
#else
	client->query.attributes &= ~(NS_QUERYATTR_QNAMEPROCESSED|
				      NS_QUERYATTR_QNAMEREWRITTEN|
				      NS_QUERYATTR_ORIGQNAMESTORED);
	client->query.ib_orig_qname = NULL;
	client->query.ib_qname = NULL;

	client->query.attributes &= ~NS_QUERYATTR_IBALIAS_ORIGQNAMESTORED;
	client->query.ibalias_orig_qname = NULL;
#endif
	/*
	 * This mutex is destroyed when the client is destroyed in
	 * exit_check().
	 */
	result = isc_mutex_init(&client->query.fetchlock);
	if (result != ISC_R_SUCCESS)
		return (result);
	client->query.fetch = NULL;
	client->query.prefetch = NULL;
	client->query.authdb = NULL;
	client->query.authzone = NULL;
	client->query.authdbset = ISC_FALSE;
	client->query.isreferral = ISC_FALSE;
	client->query.dns64_aaaa = NULL;
	client->query.dns64_sigaaaa = NULL;
	client->query.dns64_aaaaok = NULL;
	client->query.dns64_aaaaoklen = 0;
	client->query.redirect.db = NULL;
	client->query.redirect.node = NULL;
	client->query.redirect.zone = NULL;
	client->query.redirect.qtype = dns_rdatatype_none;
	client->query.redirect.result = ISC_R_SUCCESS;
	client->query.redirect.rdataset = NULL;
	client->query.redirect.sigrdataset = NULL;
	client->query.redirect.authoritative = ISC_FALSE;
	client->query.redirect.is_zone  = ISC_FALSE;
	dns_fixedname_init(&client->query.redirect.fixed);
	client->query.redirect.fname =
		dns_fixedname_name(&client->query.redirect.fixed);
	client->query.qtype = dns_rdatatype_none;
	client->query.resp_result = DNS_R_UNKNOWN;
#ifdef ORIGINAL_ISC_CODE
#else
	client->query.infoblox_check_perhaps_log_query_or_response_done = ISC_FALSE;
	client->query.infoblox_perhaps_log_query_or_response = ISC_FALSE;
	client->query.infoblox_have_formatted_qname = ISC_FALSE;
	ISC_LIST_INIT(client->query.prefetchinfo);
#endif
	query_reset(client, ISC_FALSE);
	result = query_newdbversion(client, 3);
	if (result != ISC_R_SUCCESS) {
		DESTROYLOCK(&client->query.fetchlock);
		return (result);
	}
	result = query_newnamebuf(client);
	if (result != ISC_R_SUCCESS) {
		query_freefreeversions(client, ISC_TRUE);
		DESTROYLOCK(&client->query.fetchlock);
	}

	return (result);
}

static ns_dbversion_t *
query_findversion(ns_client_t *client, dns_db_t *db) {
	ns_dbversion_t *dbversion;

	/*%
	 * We may already have done a query related to this
	 * database.  If so, we must be sure to make subsequent
	 * queries from the same version.
	 */
	for (dbversion = ISC_LIST_HEAD(client->query.activeversions);
	     dbversion != NULL;
	     dbversion = ISC_LIST_NEXT(dbversion, link)) {
		if (dbversion->db == db)
			break;
	}

	if (dbversion == NULL) {
		/*
		 * This is a new zone for this query.  Add it to
		 * the active list.
		 */
		dbversion = query_getdbversion(client);
		if (dbversion == NULL)
			return (NULL);
		dns_db_attach(db, &dbversion->db);
		dns_db_currentversion(db, &dbversion->version);
		dbversion->acl_checked = ISC_FALSE;
		dbversion->queryok = ISC_FALSE;
		ISC_LIST_APPEND(client->query.activeversions,
				dbversion, link);
	}

	return (dbversion);
}

static inline isc_result_t
query_validatezonedb(ns_client_t *client, dns_name_t *name,
		     dns_rdatatype_t qtype, unsigned int options,
		     dns_zone_t *zone, dns_db_t *db,
		     dns_dbversion_t **versionp)
{
	isc_result_t result;
	dns_acl_t *queryacl, *queryonacl;
	ns_dbversion_t *dbversion;

	REQUIRE(zone != NULL);
	REQUIRE(db != NULL);

	/*
	 * This limits our searching to the zone where the first name
	 * (the query target) was looked for.  This prevents following
	 * CNAMES or DNAMES into other zones and prevents returning
	 * additional data from other zones.
	 */
	if (!client->view->additionalfromauth &&
	    client->query.authdbset &&
	    db != client->query.authdb)
		return (DNS_R_REFUSED);

	/*
	 * Non recursive query to a static-stub zone is prohibited; its
	 * zone content is not public data, but a part of local configuration
	 * and should not be disclosed.
	 */
	if (dns_zone_gettype(zone) == dns_zone_staticstub &&
	    !RECURSIONOK(client)) {
		return (DNS_R_REFUSED);
	}

	/*
	 * If the zone has an ACL, we'll check it, otherwise
	 * we use the view's "allow-query" ACL.  Each ACL is only checked
	 * once per query.
	 *
	 * Also, get the database version to use.
	 */

	/*
	 * Get the current version of this database.
	 */
	dbversion = query_findversion(client, db);
	if (dbversion == NULL) {
		CTRACE(ISC_LOG_ERROR, "unable to get db version");
		return (DNS_R_SERVFAIL);
	}

	if ((options & DNS_GETDB_IGNOREACL) != 0)
		goto approved;
	if (dbversion->acl_checked) {
		if (!dbversion->queryok)
			return (DNS_R_REFUSED);
		goto approved;
	}

	queryacl = dns_zone_getqueryacl(zone);
	if (queryacl == NULL) {
		queryacl = client->view->queryacl;
		if ((client->query.attributes &
		     NS_QUERYATTR_QUERYOKVALID) != 0) {
			/*
			 * We've evaluated the view's queryacl already.  If
			 * NS_QUERYATTR_QUERYOK is set, then the client is
			 * allowed to make queries, otherwise the query should
			 * be refused.
			 */
			dbversion->acl_checked = ISC_TRUE;
			if ((client->query.attributes &
			     NS_QUERYATTR_QUERYOK) == 0) {
				dbversion->queryok = ISC_FALSE;
				return (DNS_R_REFUSED);
			}
			dbversion->queryok = ISC_TRUE;
			goto approved;
		}
	}

	result = ns_client_checkaclsilent(client, NULL, queryacl, ISC_TRUE);
	if ((options & DNS_GETDB_NOLOG) == 0) {
		char msg[NS_CLIENT_ACLMSGSIZE("query")];
		if (result == ISC_R_SUCCESS) {
			if (isc_log_wouldlog(ns_g_lctx, ISC_LOG_DEBUG(3))) {
				ns_client_aclmsg("query", name, qtype,
						 client->view->rdclass,
						 msg, sizeof(msg));
				ns_client_log(client,
					      DNS_LOGCATEGORY_SECURITY,
					      NS_LOGMODULE_QUERY,
					      ISC_LOG_DEBUG(3),
					      "%s approved", msg);
			}
		} else {
			ns_client_aclmsg("query", name, qtype,
					 client->view->rdclass,
					 msg, sizeof(msg));
			ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
				      NS_LOGMODULE_QUERY, ISC_LOG_INFO,
				      "%s denied", msg);
		}
	}

	if (queryacl == client->view->queryacl) {
		if (result == ISC_R_SUCCESS) {
			/*
			 * We were allowed by the default
			 * "allow-query" ACL.  Remember this so we
			 * don't have to check again.
			 */
			client->query.attributes |= NS_QUERYATTR_QUERYOK;
		}
		/*
		 * We've now evaluated the view's query ACL, and
		 * the NS_QUERYATTR_QUERYOK attribute is now valid.
		 */
		client->query.attributes |= NS_QUERYATTR_QUERYOKVALID;
	}

	/* If and only if we've gotten this far, check allow-query-on too */
	if (result == ISC_R_SUCCESS) {
		queryonacl = dns_zone_getqueryonacl(zone);
		if (queryonacl == NULL)
			queryonacl = client->view->queryonacl;

		result = ns_client_checkaclsilent(client, &client->destaddr,
						  queryonacl, ISC_TRUE);
		if ((options & DNS_GETDB_NOLOG) == 0 &&
		    result != ISC_R_SUCCESS)
			ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
				      NS_LOGMODULE_QUERY, ISC_LOG_INFO,
				      "query-on denied");
	}

	dbversion->acl_checked = ISC_TRUE;
	if (result != ISC_R_SUCCESS) {
		dbversion->queryok = ISC_FALSE;
		return (DNS_R_REFUSED);
	}
	dbversion->queryok = ISC_TRUE;

 approved:
	/* Transfer ownership, if necessary. */
	if (versionp != NULL)
		*versionp = dbversion->version;
	return (ISC_R_SUCCESS);
}

static inline isc_result_t
query_getzonedb(ns_client_t *client, dns_name_t *name, dns_rdatatype_t qtype,
		unsigned int options, dns_zone_t **zonep, dns_db_t **dbp,
		dns_dbversion_t **versionp)
{
	isc_result_t result;
	unsigned int ztoptions;
	dns_zone_t *zone = NULL;
	dns_db_t *db = NULL;
	isc_boolean_t partial = ISC_FALSE;

	REQUIRE(zonep != NULL && *zonep == NULL);
	REQUIRE(dbp != NULL && *dbp == NULL);

	/*%
	 * Find a zone database to answer the query.
	 */
	ztoptions = ((options & DNS_GETDB_NOEXACT) != 0) ?
		DNS_ZTFIND_NOEXACT : 0;

	result = dns_zt_find(client->view->zonetable, name, ztoptions, NULL,
			     &zone);

	if (result == DNS_R_PARTIALMATCH)
		partial = ISC_TRUE;
	if (result == ISC_R_SUCCESS || result == DNS_R_PARTIALMATCH)
		result = dns_zone_getdb(zone, &db);

	if (result != ISC_R_SUCCESS)
		goto fail;

	result = query_validatezonedb(client, name, qtype, options, zone, db,
				      versionp);

	if (result != ISC_R_SUCCESS)
		goto fail;

	/* Transfer ownership. */
	*zonep = zone;
	*dbp = db;

	if (partial && (options & DNS_GETDB_PARTIAL) != 0)
		return (DNS_R_PARTIALMATCH);
	return (ISC_R_SUCCESS);

 fail:
	if (zone != NULL)
		dns_zone_detach(&zone);
	if (db != NULL)
		dns_db_detach(&db);

	return (result);
}

#ifdef ORIGINAL_ISC_CODE
#else
typedef enum ib_client_val_mode {
    ib_src_port,
    ib_src_ip_addr,
    ib_dest_ip_addr,
} ib_client_val_mode;

static void
infoblox_get_ns_client_value(ns_client_t *client, char *buf, size_t len, ib_client_val_mode mode)
{
	// initially copy-pasted from ns_client_name and below
	if (mode == ib_dest_ip_addr || client->peeraddr_valid) {
		isc_result_t result;
		isc_buffer_t ibuf;
		char tmpbuf[ISC_SOCKADDR_FORMATSIZE];

		isc_buffer_init(&ibuf, tmpbuf, len);
		switch (mode) {
		case ib_dest_ip_addr:
			result = isc_sockaddr_totext(&client->interface->addr, &ibuf);
			break;
		case ib_src_ip_addr:
		case ib_src_port:
			result = isc_sockaddr_totext(&client->peeraddr, &ibuf);
			break;
		default:
			result = ISC_R_FAILURE;
			break;
		}

		if (result == ISC_R_SUCCESS) {
			char *hashp = NULL;
			switch (mode) {
			case ib_dest_ip_addr:
			case ib_src_ip_addr:
				// truncate up to the first hash character
				hashp = strchr(tmpbuf, '#');
				if (hashp)
					*hashp = '\0';
				else
					result = ISC_R_FAILURE;
				break;
			case ib_src_port:
				hashp = strchr(tmpbuf, '#');
				if (hashp)
					memmove(tmpbuf, hashp + 1, strlen(hashp + 1) + 1);
				else
					result = ISC_R_FAILURE;
				break;
			default:
				result = ISC_R_FAILURE;
				break;
			}
		}

		if (result != ISC_R_SUCCESS) {
			/*
			 * The message is the same as in netaddr.c.
			 */
			snprintf(buf, len,
				 isc_msgcat_get(isc_msgcat, ISC_MSGSET_NETADDR,
				 ISC_MSG_UNKNOWNADDR,
				 "<unknown address, family %u>"),
				 (mode == ib_dest_ip_addr? client->interface->addr: client->peeraddr).type.sa.sa_family);
		} else {
			snprintf(buf, len, "%s", tmpbuf);
		}
		buf[len - 1] = '\0';

	} else {
		snprintf(buf, len, "@%p", client);
	}
}

/*
 * Perform LBDN match and possibly GSLB processing, and replace the currently
 * recognized answer with the GSLB data.
 *
 * This function is supposed to be called for post-processing immediately
 * followed by a call to dns_db_findext() whose result is to be used as the
 * answer of the query.
 *
 * 'fname', 'nodep', and 'rdataset' are expected to be set by dns_db_findext()
 * and may be replaced if GSLB processing takes place.  This function takes
 * the original result code from dns_db_findext() and returns a (possibly)
 * replaced result code (if no GSLB processing happens it returns the passed
 * original code).
 *
 * Note that not all calls to dns_db_findext() are done for this purpose.
 * Since the GSLB processing shouldn't apply for such cases, we need to
 * selectively call this function depending on the context of the caller.
 * Likewise, if a future extension introduces another call to dns_db_findext()
 * to use the result for query answer, we'll need to update the caller so this
 * function will be called.  This is a bit error prone, but, unfortunately,
 * there doesn't seem to be an easy way to unify the logic that works for all
 * possible cases.
 */
static isc_result_t
infoblox_query_idns_find(ns_client_t *client, dns_name_t *qname,
			 dns_rdatatype_t type, dns_db_t *db,
			 isc_boolean_t is_zone, dns_dbnode_t **nodep,
			 dns_dbversion_t *version, dns_name_t *fname,
			 dns_rdataset_t *rdataset,
			 dns_rdataset_t **sigrdatasetp,
			 isc_result_t orig_result)
{
	/*
	 * In many cases we can skip GSLB processing altogether:
	 * - When the feature is disabled
	 * - If the answer doesn't come from zone DB (cache, etc)
	 * - If the query type is 'ANY' (which we consider a special case for
	 *   debugging purposes)
	 * - If the qname actually doesn't belong to the zone (delegation or
	 *   DNAME).
	 */
	if (!ns_g_server->infoblox_enable_dtc ||
	    !is_zone || type == dns_rdatatype_any ||
	    orig_result == DNS_R_DELEGATION || orig_result == DNS_R_DNAME) {
		return (orig_result);
	}

	isc_result_t result;
	dns_rdataset_t idns_rdataset;
	dns_rdataset_t idns_sigrdataset;
	isc_boolean_t is_replaced = ISC_FALSE;
	isc_boolean_t sig_replaced = ISC_FALSE;

	dns_rdataset_init(&idns_rdataset);
	dns_rdataset_init(&idns_sigrdataset);
	result = infoblox_idns_find(db, version, &client->peeraddr,
				    client->message, client->ecs_opt,
				    qname, type,
				    client->now, &idns_rdataset,
				    &idns_sigrdataset, WANTDNSSEC(client));
	switch (result) {
	case ISC_R_SUCCESS:
	case DNS_R_CNAME:
		/*
		 * We've found a possible answer (or CNAME) via GSLB.
		 * replace the answer found in the zone (if any) with the
		 * GSLB-based answer.  Note that cloning rdataset is safe
		 * even if the structure is function local, since its
		 * associated resources are allocated inside client's message.
		 */
		if (dns_rdataset_isassociated(rdataset))
			dns_rdataset_disassociate(rdataset);
		dns_rdataset_clone(&idns_rdataset, rdataset);

		/* Set GSLBANSWER in case needed for response logging */
		rdataset->attributes |= DNS_RDATASETATTR_GSLBANSWER;
		dns_name_reset(fname);
		dns_name_copy(qname, fname, NULL);
		is_replaced = ISC_TRUE;

		if (WANTDNSSEC(client) &&
		    dns_rdataset_isassociated(&idns_sigrdataset)) {
			/* Replace DNSSEC data. */
			if (sigrdatasetp) {
				if (dns_rdataset_isassociated(*sigrdatasetp))
					dns_rdataset_disassociate(*sigrdatasetp);
				dns_rdataset_clone(&idns_sigrdataset, *sigrdatasetp);
			}
			sig_replaced = TRUE;
		}
		break;

	case ISC_R_NOTFOUND: /* no LBDN match. keep the original result. */
		result = orig_result;
		break;

	case DNS_R_NXRRSET:
		/*
		 * The qname matched but the qtype is not of our interest.
		 * This is similar to 'NOTFOUND', but if the original zone
		 * lookup resulted in NXDOMAIN we'll pretend the name exists
		 * to prevent the NXDOMAIN from being cached at the client.
		 */
		if (orig_result == DNS_R_NXDOMAIN)
			is_replaced = ISC_TRUE;
		else
			result = orig_result;
		break;

	case DNS_R_HINTNXRRSET:
		/* DTC had no results and does not wish to use DNS. */
		is_replaced = ISC_TRUE;
		result = DNS_R_NXRRSET;
		break;

	case DNS_R_NXDOMAIN:
		/*
		 * RFE-7113: Like in the case above, DTC doesn't wish to use DNS.
		 */
		is_replaced = ISC_TRUE;
		break;

	default:
		/*
		 * In this case result should be ISC_R_FAILURE; otherwise it's
		 * an internal bug.  We could INSIST that, but since we'll
		 * return SERVFAIL for the case of FAILURE anyway, we could be
		 * less harsh here.  The log message should indicate that the
		 * result is something really unexpected, not even R_FAILURE.
		 */
		if (result != ISC_R_FAILURE) {
			ns_client_log(client, NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
				      "DTC match or lookup failed unexpectedly: %s",
				      isc_result_totext(result));
			result = ISC_R_FAILURE;
		}
		break;
	}

	if (dns_rdataset_isassociated(&idns_rdataset))
		dns_rdataset_disassociate(&idns_rdataset);

	/*
	 * If we've faked the answer, subsequent code may assume the existence
	 * of a corresponding DB node.  We'll use the node given in the original
	 * lookup (if existent) or the zone's origin node.
	 * Note: either case, the resulting 'node' is generally independent
	 * from the synthesized answer (i.e. replaced 'fname' and 'rdataset').
	 * It's inevitable since the synthesized answer doesn't exist in the
	 * actual zone.  To the extent of the code inspection and test
	 * coverage, this doesn't seem to be an issue, but if we find a case
	 * where 'node' is expected to have some relationship to the answer,
	 * we'll need to introduce trickier handling such as creating a
	 * temporary pseudo node in 'db'.
	 */
	if (is_replaced && *nodep == NULL) {
		isc_result_t tresult = dns_db_getoriginnode(db, nodep);
		if (tresult != ISC_R_SUCCESS) {
			ns_client_log(client, NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
				      "can't get DB node for GSLB answer: %s",
				      isc_result_totext(tresult));
			if (dns_rdataset_isassociated(rdataset))
				dns_rdataset_disassociate(rdataset);
			dns_name_reset(fname);
			result = ISC_R_FAILURE;
		}
	}

	if (is_replaced && !sig_replaced) {
		/* Turn off DNSSEC. */
		if (sigrdatasetp && *sigrdatasetp) {
			query_putrdataset(client, sigrdatasetp);
		}
		client->attributes &= ~(NS_CLIENTATTR_WANTDNSSEC |
					NS_CLIENTATTR_WANTAD);
		client->message->flags &= ~DNS_MESSAGEFLAG_AD;
	}

	return (result);
}

/*
 * Wrapper for inet_ntop(), converting ib_ip_address_kind to integer AF number,
 * and returning the converted string so the caller can directly use the result.
 */
static const char *
ib_inet_ntop(enum ib_ip_address_kind kind, const void *addr,
	     char *buf, socklen_t buflen)
{
	int af;
	switch (kind) {
	case IP_ADDRESS_IPV4:
		af = AF_INET;
		break;
	case IP_ADDRESS_IPV6:
		af = AF_INET6;
		break;
	default:
		return ("unknown");
	}
	if (inet_ntop(af, addr, buf, buflen) != NULL)
		return (buf);
	return ("invalid");
}

/* Build commonly used RPZ-related log message. */
static const char *
ib_format_rpz_logmsg(char *msg, size_t msglen, isc_boolean_t disabled,
		     dns_rpz_policy_t policy, dns_rpz_type_t type,
		     const char *policy_str, const char *qname,
		     const char *qtype, const char *pname)
{
	snprintf(msg, msglen,
		 "%srpz %s %s rewrite %s [%s] via %s",
		 disabled ? "disabled " : "",
		 dns_rpz_type2str(policy == DNS_RPZ_POLICY_MISS ?
				  DNS_RPZ_TYPE_QNAME : type),
		 policy_str, qname, qtype, pname);
	msg[msglen - 1] = '\0';

	return (msg);
}

/* Conversion function that converts from enum literals in dns_rpz_type_t to enum literals
 * in ib_dns_rpz_type_t so that the compiler can warn if any enum literals are missed.
 */
static ib_dns_rpz_type_t
convert_dns_rpz_type(dns_rpz_type_t type) {
	switch (type) {
	case DNS_RPZ_TYPE_CLIENT_IP:
		return (IB_RPZ_TYPE_CLIENT_IP);
	case DNS_RPZ_TYPE_QNAME:
		return (IB_RPZ_TYPE_QNAME);
	case DNS_RPZ_TYPE_IP:
		return (IB_RPZ_TYPE_IP);
	case DNS_RPZ_TYPE_NSDNAME:
		return (IB_RPZ_TYPE_NSDNAME);
	case DNS_RPZ_TYPE_NSIP:
		return (IB_RPZ_TYPE_NSIP);
	case DNS_RPZ_TYPE_BAD:
		return (IB_RPZ_TYPE_BAD);
	}
	INSIST(0);
}

/*
 * Publish an RPZ event via the outbound API.
 * This is a helper for rpz_log_rewrite(), taking parameters to publish
 * from the caller in a straightforward way (assuming they are sane).
 */
static ib_return_t
infoblox_publish_rpz_event(const ns_client_t *client, isc_boolean_t disabled,
			   const char *policy_str, dns_rpz_type_t type,
			   const char *qname, dns_rdatatype_t qtype,
			   const char *policy_name, unsigned severity)
{
	infoblox_events_publish_rpz_data_t ib_data;
	isc_time_t now;

	TIME_NOW(&now);
	ib_data.timestamp.tv_sec = now.seconds;
	ib_data.timestamp.tv_nsec = now.nanoseconds;
	ib_data.rpz_policy_disabled = disabled;
	ib_data.rpz_cef_log_enabled =
		client->view->infoblox_enable_rpz_logging_cef;
	ib_data.rpz_policy_str = policy_str;
	/* assume consistency between dns_rpz_type_t and ib_dns_rpz_type_t: */
	ib_data.rpz_type = convert_dns_rpz_type(type);
	ib_data.rpz_severity = severity;
	switch (client->destaddr.family) {
	case AF_INET:
		ib_data.dst_ip_kind = IP_ADDRESS_IPV4;
		memcpy(ib_data.dst_ip, &client->destaddr.type.in,
		       sizeof(client->destaddr.type.in));
		break;
	case AF_INET6:
		ib_data.dst_ip_kind = IP_ADDRESS_IPV6;
		memcpy(ib_data.dst_ip, &client->destaddr.type.in6,
		       sizeof(client->destaddr.type.in6));
		break;
	default:
		ib_data.dst_ip_kind = IP_ADDRESS_INVALID; /* unexpected case */
		break;
	}
	switch (isc_sockaddr_pf(&client->peeraddr)) {
	case PF_INET:
		ib_data.src_ip_kind = IP_ADDRESS_IPV4;
		memcpy(ib_data.src_ip, &client->peeraddr.type.sin.sin_addr,
		       sizeof(client->peeraddr.type.sin.sin_addr));
		break;
	case PF_INET6:
		ib_data.src_ip_kind = IP_ADDRESS_IPV6;
		memcpy(ib_data.src_ip, &client->peeraddr.type.sin6.sin6_addr,
		       sizeof(client->peeraddr.type.sin6.sin6_addr));
		break;
	default:
		ib_data.src_ip_kind = IP_ADDRESS_INVALID; /* unexpected */
		break;
	}
	ib_data.src_port = isc_sockaddr_getport(&client->peeraddr);
	ib_data.query_type = qtype;
	ib_data.query_name = qname;
	ib_data.view_name = client->view->name;
	ib_data.rule_name = policy_name;

	/*
	 * Mainly for testing purposes we dump the values just built in a
	 * debug log message.  Don't even bother to construct the log message
	 * unless the higher log level is explicitly specified.
	 */
	if (infoblox_would_log(3)) {
		char src_buf[INET6_ADDRSTRLEN];
		char dst_buf[INET6_ADDRSTRLEN];
		infoblox_log(3,
			     "publishing RPZ data time=%ld.%09ld, disabled=%s, "
			     "log=%s, policy=%s, type=%d, severity=%u, dst=%s, "
			     "src=%s#%u, qtype=%d, qname=%s, view='%s', "
			     "rule=%s",
			     ib_data.timestamp.tv_sec,
			     ib_data.timestamp.tv_nsec,
			     ib_data.rpz_policy_disabled ? "yes" : "no",
			     ib_data.rpz_cef_log_enabled ? "yes" : "no",
			     ib_data.rpz_policy_str, ib_data.rpz_type,
			     ib_data.rpz_severity,
			     ib_inet_ntop(ib_data.dst_ip_kind, ib_data.dst_ip,
					  dst_buf, sizeof(dst_buf)),
			     ib_inet_ntop(ib_data.src_ip_kind, ib_data.src_ip,
					  src_buf, sizeof(src_buf)),
			     ib_data.src_port, ib_data.query_type,
			     ib_data.query_name, ib_data.view_name,
			     ib_data.rule_name);
	}

	return (infoblox_rpz_event_publish_send_data(&ib_data));
}
#endif	/* ORIGINAL_ISC_CODE */

static void
rpz_log_rewrite(ns_client_t *client, isc_boolean_t disabled,
		dns_rpz_policy_t policy, dns_rpz_type_t type,
		dns_zone_t *p_zone, dns_name_t *p_name,
#ifdef ORIGINAL_ISC_CODE
		dns_name_t *cname, dns_rpz_num_t rpz_num)
#else
		dns_name_t *cname, dns_rpz_num_t rpz_num,
		infoblox_rpz_redirect_info_t *rri,
		unsigned severity, const char *category, isc_boolean_t warn)
#endif
{
	isc_stats_t *zonestats;
	char qname_buf[DNS_NAME_FORMATSIZE];
	char p_name_buf[DNS_NAME_FORMATSIZE];
	char cname_buf[DNS_NAME_FORMATSIZE] = { 0 };
	const char *s1 = cname_buf, *s2 = cname_buf;
	dns_rpz_st_t *st;

#ifdef ORIGINAL_ISC_CODE
#else
	/* When redoing we're just checking if there's a matching rule.
	 * We don't want any other side effects like logging or statistics
	 * update. */
	if (IB_REDOINGRPZ(client))
		return;
#endif

	/*
	 * Count enabled rewrites in the global counter.
	 * Count both enabled and disabled rewrites for each zone.
	 */
	if (!disabled && policy != DNS_RPZ_POLICY_PASSTHRU) {
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_rpz_rewrites);
	}
	if (p_zone != NULL) {
		zonestats = dns_zone_getrequeststats(p_zone);
		if (zonestats != NULL)
			isc_stats_increment(zonestats,
					    dns_nsstatscounter_rpz_rewrites);
	}

#ifdef ORIGINAL_ISC_CODE
#else
	// A disabled policy is indicated by the 'disabled' parameter.
	// We don't include those in the reports.
	if (client->view && client->view->infoblox_top_query_rpz && !disabled) {
		infoblox_tq_client_increment(client->view, &client->peeraddr,
					     client->query.qname, policy, rri,
					     p_name, severity);
	}

	// Parental control would set Z-bit (or, later, log rewrites),
	// for whitelist hits.  The whitelist hit will be resolved normally,
	// with category, so we never want to set the Z-bit here.
	// Don't set Z-bit for Warning PCP, as DCA can process WPCP.
	// Just using bypass_warn level to avoid not used error for 'warn'.
	if ((client->query.attributes & NS_QUERYATTR_PC_WHITELIST) == 0) {
		if(warn == ISC_FALSE)
		     infoblox_perhaps_set_zbit(client);
        }

	// update global rpz counter in server and zone statistics
	ns_query_incstats(client, dns_nsstatscounter_rpzrewrite);

	// also update corresponding server and zone counters based on policy type
	switch (policy) {
		case DNS_RPZ_POLICY_PASSTHRU:
		case DNS_RPZ_POLICY_MISS:
			ns_query_incstats(client,
					   dns_nsstatscounter_rpzpassthru);
			break;
		case DNS_RPZ_POLICY_NXDOMAIN:
		case DNS_RPZ_POLICY_NODATA:
			ns_query_incstats(client,
					  dns_nsstatscounter_rpznxdomainnodata);
			break;
		case DNS_RPZ_POLICY_CNAME:
		case DNS_RPZ_POLICY_WILDCNAME:
		case DNS_RPZ_POLICY_RECORD:
			ns_query_incstats(client,
					  dns_nsstatscounter_rpzsubstitute);
			break;
		default:
			// do nothing
			break;
	}

	isc_boolean_t do_publish = ISC_FALSE;
	if (ns_g_server->ib_event_publish != NULL) {
		do_publish = infoblox_get_event_publish_enabled(
			ns_g_server->ib_event_publish, IB_EVENT_PUBLISH_RPZ);
	}
#endif

#ifdef ORIGINAL_ISC_CODE
	if (!isc_log_wouldlog(ns_g_lctx, DNS_RPZ_INFO_LEVEL))
#else
	/*
	 * Build parameters for logs, as necessary.  If parental control
	 * has indicated that we hit a whitelist, don't bother to
	 * log data.  Otherwise, determine if outbound publishing is
	 * enabled, or if the log level is sufficiently high.
	 *
	 * In practice, the publishing check is redundant since
	 * isc_log_wouldlog() would always return true for the 'info'
	 * level, but we express the intent explicitly.
	 */
	if (!isc_log_wouldlog(ns_g_lctx, DNS_RPZ_INFO_LEVEL) && !do_publish)
#endif
		return;

	st = client->query.rpz_st;
#ifdef ORIGINAL_ISC_CODE
#else
	/* We can be here without having RPZs if parental control lie is
	 * applied, in which case rpz_st should be NULL and can't be used. */
	
	/*
         * RPZ logging check should happen for RPZ hits only,
         * for category match, logging check shouldn't be done.
         */
	if (st != NULL && category == NULL)
#endif
	if ((st->popt.no_log & DNS_RPZ_ZBIT(rpz_num)) != 0)
		return;

	dns_name_format(client->query.qname, qname_buf, sizeof(qname_buf));
	dns_name_format(p_name, p_name_buf, sizeof(p_name_buf));
	if (cname != NULL) {
		s1 = " (CNAME to: ";
		dns_name_format(cname, cname_buf, sizeof(cname_buf));
		s2 = ")";
	}

#ifdef ORIGINAL_ISC_CODE
	ns_client_log(client, DNS_LOGCATEGORY_RPZ, NS_LOGMODULE_QUERY,
		      DNS_RPZ_INFO_LEVEL, "%srpz %s %s rewrite %s via %s%s%s%s",
		      disabled ? "disabled " : "",
		      dns_rpz_type2str(type), dns_rpz_policy2str(policy),
		      qname_buf, p_name_buf, s1, cname_buf, s2);
#else
	dns_rdataset_t *rdataset;
	dns_rdatatype_t qtype = dns_rdatatype_none;
	char typename[DNS_RDATATYPE_FORMATSIZE] = "<UNAVAIL>";
	rdataset = ISC_LIST_HEAD(client->query.origqname->list);
	if (rdataset != NULL) {
		qtype = rdataset->type;
		dns_rdatatype_format(qtype, typename, sizeof(typename));
	}
	const char *policy_str =
		dns_rpz_policy2str(policy == DNS_RPZ_POLICY_MISS ?
				   DNS_RPZ_POLICY_PASSTHRU : policy);
	char msgbuf[2048];

	if (do_publish) {
		ib_return_t ibret;

		ibret = infoblox_publish_rpz_event(client, disabled, policy_str,
						   type, qname_buf, qtype,
						   p_name_buf, severity);
		if (ibret != IERR_SUCCESS) {
			const char *msg =
				ib_format_rpz_logmsg(msgbuf, sizeof(msgbuf),
						     disabled, policy, type,
						     policy_str, qname_buf,
						     typename, p_name_buf);
			infoblox_log(1, "error publishing RPZ data (%s): %s",
				     msg, ib_error_get_string(ibret));
		}
	}

	/*
	 * Avoid formatting overhead if it wouldn't be logged in the end.
	 * Note: 'wouldlog' actually always returns true for "INFO" level,
	 * so this check is currently useless.  But the check is cheap so
	 * we proactively perform it.
	 */
	if (!isc_log_wouldlog(ns_g_lctx, DNS_RPZ_INFO_LEVEL))
		return;

	// Also avoid log for rpz 31 zone passthrough/whitelist domain 
	if (client->view->infoblox_pc_enable && client->subscriber != NULL &&
			st != NULL && st->m.policy == DNS_RPZ_POLICY_PASSTHRU &&
			(client->view->infoblox_pc_proxy_all_whitelist == st->m.rpz->num ||
			 (client->view->infoblox_ss_enable_global_allow_rpz_list &&
			  st->m.rpz->num == client->view->infoblox_ss_global_allow_rpz_list)))
		return;

	if (!infoblox_parental_control_test_mode() && category != NULL && strcmp(category, "PXY") == 0)
		return;

	const char *msg = ib_format_rpz_logmsg(msgbuf, sizeof(msgbuf), disabled,
					       policy, type, policy_str,
					       qname_buf, typename, p_name_buf);

	// By default, we always log rpz messages in CEF format
	if (client->view->infoblox_enable_rpz_logging_cef) {
		char src[ISC_SOCKADDR_FORMATSIZE];
		infoblox_get_ns_client_value(client, src, sizeof(src), ib_src_ip_addr);

		char src_port[ISC_SOCKADDR_FORMATSIZE];
		infoblox_get_ns_client_value(client, src_port, sizeof(src_port), ib_src_port);

		char dst[ISC_SOCKADDR_FORMATSIZE];
		infoblox_get_ns_client_value(client, dst, sizeof(dst), ib_dest_ip_addr);

		const char *subscriber_log =
			client->subscriber != NULL ?
			client->subscriber->cef_log_string : NULL;

		isc_log_write(ns_g_lctx, DNS_LOGCATEGORY_RPZ, NS_LOGMODULE_QUERY, DNS_RPZ_INFO_LEVEL,
			      "CEF:0|Infoblox|NIOS|%s|RPZ-%s|%s|%u|app=DNS dst=%s src=%s spt=%s view=%s qtype=%s msg=\"%s\"%s%s CAT=%s",
			      rc_external_version(),
			      dns_rpz_type2str(policy == DNS_RPZ_POLICY_MISS? DNS_RPZ_TYPE_QNAME: type),
			      policy_str,
			      severity,
			      dst, src, src_port,
			      client->view->name? client->view->name: "<unknown view>",
			      typename, msg,
			      subscriber_log != NULL ? " " : "",
			      subscriber_log != NULL ? subscriber_log : "",
			      category != NULL ? category : "RPZ");
	// But we still retain old behavior for compatibility
	} else {
		ns_client_log(client, DNS_LOGCATEGORY_RPZ, NS_LOGMODULE_QUERY,
			      DNS_RPZ_INFO_LEVEL, "%s", msg);
	}
#endif
}

#ifdef ORIGINAL_ISC_CODE
#else

/*
 * Check if need to log regarding 'zone not loaded' should be throttled.
 */
static isc_boolean_t
infoblox_throttle_log_rpz_notloaded(ns_client_t *client, int level,
				    isc_result_t result)
{
	time_t logged = 0;
	struct timeval now;

	if (result != DNS_R_NOTLOADED)
		return (ISC_FALSE);
	if (client->view == NULL)
		return (ISC_FALSE);

	(void) gettimeofday(&now, NULL);
	logged = client->view->infoblox_rpz_notloaded_logged;

	if (((unsigned)(now.tv_sec - logged) >= rpz_notloaded_log_interval)) {
		isc_int32_t old = isc_atomic_cmpxchg((isc_int32_t *)(&client->view->infoblox_rpz_notloaded_logged), logged, now.tv_sec);
		if (old == logged) {
			if (client->view->infoblox_rpz_notloaded_throttled != 0)
			{
				ns_client_log(client,
					      NS_LOGCATEGORY_QUERY_ERRORS,
					      NS_LOGMODULE_QUERY, level,
					      "Suppressed %u messages for view \"%s\" regarding RPZ zones that have not been loaded",
					      client->view->infoblox_rpz_notloaded_throttled,
					      client->view->name);
			}
			// due to the fact that other thread can still increase
			// the counter, the result here is not very accurate.
			// but is ok for this particular purpose
			client->view->infoblox_rpz_notloaded_throttled = 0;
			return (ISC_FALSE);
		}
	} else {
		client->view->infoblox_rpz_notloaded_throttled++;
	}
	return (ISC_TRUE);
}

#endif

static void
rpz_log_fail_helper(ns_client_t *client, int level, dns_name_t *p_name,
		    dns_rpz_type_t rpz_type1, dns_rpz_type_t rpz_type2,
		    const char *str, isc_result_t result)
{
	char qnamebuf[DNS_NAME_FORMATSIZE];
	char p_namebuf[DNS_NAME_FORMATSIZE];
	const char *failed;
	const char *slash;
	const char *via;
	const char *str_blank;
	const char *rpztypestr1;
	const char *rpztypestr2;

#ifdef ORIGINAL_ISC_CODE
#else
	/* See rpz_log_rewrite() */
	if (IB_REDOINGRPZ(client))
		return;

        /* TODO: Revisit this. Assuming that the state is valid for TQ
	 * after any error not explicitly listed here is risky.
	 */
	if (client->view && client->view->infoblox_top_query_rpz &&
	    (result != ISC_R_FAILURE) && (result != ISC_R_TIMEDOUT) &&
	    (result != DNS_R_BROKENCHAIN) && (result != DNS_R_NOTLOADED) &&
	    (result != DNS_R_DROP) && (result != DNS_R_DUPLICATE) &&
	    strcmp(str, "stop on unrecognized qresult in rpz_rewrite()")) {
		dns_zone_t *rpz_zone = NULL;
		dns_name_t *rpz_qname = NULL;
		if (client->query.rpz_st) {
			rpz_zone = client->query.rpz_st->m.zone;
			rpz_qname = client->query.rpz_st->p_name;
		}
		infoblox_tq_client_increment(client->view, &client->peeraddr,
					     client->query.qname,
					     DNS_RPZ_POLICY_ERROR, NULL,
					     rpz_qname,
					     infoblox_zone_get_rpz_severity(rpz_zone));
	}

        infoblox_perhaps_set_zbit(client);
#endif

	if (!isc_log_wouldlog(ns_g_lctx, level))
		return;

	if (infoblox_throttle_log_rpz_notloaded(client, level, result)) {
		return;
	}

	/*
	 * bin/tests/system/rpz/tests.sh looks for "rpz.*failed" for problems.
	 */
	if (level <= DNS_RPZ_DEBUG_LEVEL1)
		failed = "failed: ";
	else
		failed = ": ";

	rpztypestr1 = dns_rpz_type2str(rpz_type1);
	if (rpz_type2 != DNS_RPZ_TYPE_BAD) {
		slash = "/";
		rpztypestr2 = dns_rpz_type2str(rpz_type2);
	} else {
		slash = "";
		rpztypestr2 = "";
	}

	str_blank = (*str != ' ' && *str != '\0') ? " " : "";
	dns_name_format(client->query.qname, qnamebuf, sizeof(qnamebuf));
	if (p_name != NULL) {
		via = " via ";
		dns_name_format(p_name, p_namebuf, sizeof(p_namebuf));
	} else {
		via = "";
		p_namebuf[0] = '\0';
	}

	ns_client_log(client, NS_LOGCATEGORY_QUERY_ERRORS,
		      NS_LOGMODULE_QUERY, level,
		      "rpz %s%s%s rewrite %s%s%s%s%s%s : %s",
		      rpztypestr1, slash, rpztypestr2,
		      qnamebuf, via, p_namebuf, str_blank,
		      str, failed, isc_result_totext(result));
}

static void
rpz_log_fail(ns_client_t *client, int level, dns_name_t *p_name,
	     dns_rpz_type_t rpz_type, const char *str, isc_result_t result)
{
	rpz_log_fail_helper(client, level, p_name,
			    rpz_type, DNS_RPZ_TYPE_BAD, str, result);
}

/*
 * Get a policy rewrite zone database.
 */
static isc_result_t
rpz_getdb(ns_client_t *client, dns_name_t *p_name, dns_rpz_type_t rpz_type,
	  dns_zone_t **zonep, dns_db_t **dbp, dns_dbversion_t **versionp)
{
	char qnamebuf[DNS_NAME_FORMATSIZE];
	char p_namebuf[DNS_NAME_FORMATSIZE];
	dns_dbversion_t *rpz_version = NULL;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_getdb");

	result = query_getzonedb(client, p_name, dns_rdatatype_any,
				 DNS_GETDB_IGNOREACL, zonep, dbp, &rpz_version);
	if (result == ISC_R_SUCCESS) {
		dns_rpz_st_t *st = client->query.rpz_st;

		/*
		 * It isn't meaningful to log this message when
		 * logging is disabled for some policy zones.
		 */
		if (st->popt.no_log == 0 &&
		    isc_log_wouldlog(ns_g_lctx, DNS_RPZ_DEBUG_LEVEL2))
		{
			dns_name_format(client->query.qname, qnamebuf,
					sizeof(qnamebuf));
			dns_name_format(p_name, p_namebuf, sizeof(p_namebuf));
			ns_client_log(client, DNS_LOGCATEGORY_RPZ,
				      NS_LOGMODULE_QUERY, DNS_RPZ_DEBUG_LEVEL2,
				      "try rpz %s rewrite %s via %s",
				      dns_rpz_type2str(rpz_type),
				      qnamebuf, p_namebuf);
		}
		*versionp = rpz_version;
		return (ISC_R_SUCCESS);
	}
	rpz_log_fail(client, DNS_RPZ_ERROR_LEVEL, p_name, rpz_type,
		     " query_getzonedb()", result);
	return (result);
}

static inline isc_result_t
query_getcachedb(ns_client_t *client, dns_name_t *name, dns_rdatatype_t qtype,
		 dns_db_t **dbp, unsigned int options)
{
	isc_result_t result;
	isc_boolean_t check_acl;
	dns_db_t *db = NULL;

	REQUIRE(dbp != NULL && *dbp == NULL);

	/*%
	 * Find a cache database to answer the query.
	 * This may fail with DNS_R_REFUSED if the client
	 * is not allowed to use the cache.
	 */

	if (!USECACHE(client))
		return (DNS_R_REFUSED);
	dns_db_attach(client->view->cachedb, &db);

	if ((client->query.attributes & NS_QUERYATTR_CACHEACLOKVALID) != 0) {
		/*
		 * We've evaluated the view's cacheacl already.  If
		 * NS_QUERYATTR_CACHEACLOK is set, then the client is
		 * allowed to make queries, otherwise the query should
		 * be refused.
		 */
		check_acl = ISC_FALSE;
		if ((client->query.attributes & NS_QUERYATTR_CACHEACLOK) == 0)
			goto refuse;
	} else {
		/*
		 * We haven't evaluated the view's queryacl yet.
		 */
		check_acl = ISC_TRUE;
	}

	if (check_acl) {
		isc_boolean_t log = ISC_TF((options & DNS_GETDB_NOLOG) == 0);
		char msg[NS_CLIENT_ACLMSGSIZE("query (cache)")];

		result = ns_client_checkaclsilent(client, NULL,
						  client->view->cacheacl,
						  ISC_TRUE);
		if (result == ISC_R_SUCCESS) {
			/*
			 * We were allowed by the "allow-query-cache" ACL.
			 * Remember this so we don't have to check again.
			 */
			client->query.attributes |=
				NS_QUERYATTR_CACHEACLOK;
			if (log && isc_log_wouldlog(ns_g_lctx,
						     ISC_LOG_DEBUG(3)))
			{
				ns_client_aclmsg("query (cache)", name, qtype,
						 client->view->rdclass,
						 msg, sizeof(msg));
				ns_client_log(client,
					      DNS_LOGCATEGORY_SECURITY,
					      NS_LOGMODULE_QUERY,
					      ISC_LOG_DEBUG(3),
					      "%s approved", msg);
			}
		} else if (log) {
			ns_client_aclmsg("query (cache)", name, qtype,
					 client->view->rdclass, msg,
					 sizeof(msg));
			ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
				      NS_LOGMODULE_QUERY, ISC_LOG_INFO,
				      "%s denied", msg);
		}
		/*
		 * We've now evaluated the view's query ACL, and
		 * the NS_QUERYATTR_CACHEACLOKVALID attribute is now valid.
		 */
		client->query.attributes |= NS_QUERYATTR_CACHEACLOKVALID;

		if (result != ISC_R_SUCCESS)
			goto refuse;
	}

	/* Approved. */

	/* Transfer ownership. */
	*dbp = db;

	return (ISC_R_SUCCESS);

 refuse:
	result = DNS_R_REFUSED;

	if (db != NULL)
		dns_db_detach(&db);

	return (result);
}

static inline isc_result_t
query_getdb(ns_client_t *client, dns_name_t *name, dns_rdatatype_t qtype,
	    unsigned int options, dns_zone_t **zonep, dns_db_t **dbp,
	    dns_dbversion_t **versionp, isc_boolean_t *is_zonep)
{
	isc_result_t result;

	isc_result_t tresult;
	unsigned int namelabels;
	unsigned int zonelabels;
	dns_zone_t *zone = NULL;

	REQUIRE(zonep != NULL && *zonep == NULL);

	/* Calculate how many labels are in name. */
	namelabels = dns_name_countlabels(name);
	zonelabels = 0;

	/* Try to find name in bind's standard database. */
	result = query_getzonedb(client, name, qtype, options, &zone,
				 dbp, versionp);

	/* See how many labels are in the zone's name.	  */
	if (result == ISC_R_SUCCESS && zone != NULL)
		zonelabels = dns_name_countlabels(dns_zone_getorigin(zone));

	/*
	 * If # zone labels < # name labels, try to find an even better match
	 * Only try if DLZ drivers are loaded for this view
	 */
	if (ISC_UNLIKELY(zonelabels < namelabels &&
			 !ISC_LIST_EMPTY(client->view->dlz_searched)))
	{
		dns_clientinfomethods_t cm;
		dns_clientinfo_t ci;
		dns_db_t *tdbp;

		dns_clientinfomethods_init(&cm, ns_client_sourceip);
		dns_clientinfo_init(&ci, client, &client->ecs, NULL);

		tdbp = NULL;
		tresult = dns_view_searchdlz(client->view, name,
					     zonelabels, &cm, &ci, &tdbp);
		 /* If we successful, we found a better match. */
		if (tresult == ISC_R_SUCCESS) {
			ns_dbversion_t *dbversion;

			/*
			 * If the previous search returned a zone, detach it.
			 */
			if (zone != NULL)
				dns_zone_detach(&zone);

			/*
			 * If the previous search returned a database,
			 * detach it.
			 */
			if (*dbp != NULL)
				dns_db_detach(dbp);

			/*
			 * If the previous search returned a version, clear it.
			 */
			*versionp = NULL;

			dbversion = query_findversion(client, tdbp);
			if (dbversion == NULL) {
				tresult = ISC_R_NOMEMORY;
			} else {
				/*
				 * Be sure to return our database.
				 */
				*dbp = tdbp;
				*versionp = dbversion->version;
			}

			/*
			 * We return a null zone, No stats for DLZ zones.
			 */
			zone = NULL;
			result = tresult;
		}
	}

	/* If successful, Transfer ownership of zone. */
	if (result == ISC_R_SUCCESS) {
		*zonep = zone;
		/*
		 * If neither attempt above succeeded, return the cache instead
		 */
		*is_zonep = ISC_TRUE;
	} else if (result == ISC_R_NOTFOUND) {
		result = query_getcachedb(client, name, qtype, dbp, options);
		*is_zonep = ISC_FALSE;
	}
	return (result);
}

static inline isc_boolean_t
query_isduplicate(ns_client_t *client, dns_name_t *name,
		  dns_rdatatype_t type, dns_name_t **mnamep)
{
	dns_section_t section;
	dns_name_t *mname = NULL;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "query_isduplicate");

	for (section = DNS_SECTION_ANSWER;
	     section <= DNS_SECTION_ADDITIONAL;
	     section++) {
		result = dns_message_findname(client->message, section,
					      name, type, 0, &mname, NULL);
		if (result == ISC_R_SUCCESS) {
			/*
			 * We've already got this RRset in the response.
			 */
			CTRACE(ISC_LOG_DEBUG(3),
			       "query_isduplicate: true: done");
			return (ISC_TRUE);
		} else if (result == DNS_R_NXRRSET) {
			/*
			 * The name exists, but the rdataset does not.
			 */
			if (section == DNS_SECTION_ADDITIONAL)
				break;
		} else
			RUNTIME_CHECK(result == DNS_R_NXDOMAIN);
		mname = NULL;
	}

	if (mnamep != NULL)
		*mnamep = mname;

	CTRACE(ISC_LOG_DEBUG(3), "query_isduplicate: false: done");
	return (ISC_FALSE);
}

static isc_result_t
query_addadditional(void *arg, dns_name_t *name, dns_rdatatype_t qtype) {
	ns_client_t *client = arg;
	isc_result_t result, eresult;
	dns_dbnode_t *node;
	dns_db_t *db;
	dns_name_t *fname, *mname;
	dns_rdataset_t *rdataset, *sigrdataset, *trdataset;
	isc_buffer_t *dbuf;
	isc_buffer_t b;
	dns_dbversion_t *version;
	isc_boolean_t added_something, need_addname;
	dns_zone_t *zone;
	dns_rdatatype_t type;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;
	dns_rdatasetadditional_t additionaltype;

	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(qtype != dns_rdatatype_any);

	if (!WANTDNSSEC(client) && dns_rdatatype_isdnssec(qtype))
		return (ISC_R_SUCCESS);

	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional");

	/*
	 * Initialization.
	 */
	eresult = ISC_R_SUCCESS;
	fname = NULL;
	rdataset = NULL;
	sigrdataset = NULL;
	trdataset = NULL;
	db = NULL;
	version = NULL;
	node = NULL;
	added_something = ISC_FALSE;
	need_addname = ISC_FALSE;
	zone = NULL;
	additionaltype = dns_rdatasetadditional_fromauth;

	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, NULL, NULL);

	/*
	 * We treat type A additional section processing as if it
	 * were "any address type" additional section processing.
	 * To avoid multiple lookups, we do an 'any' database
	 * lookup and iterate over the node.
	 */
	if (qtype == dns_rdatatype_a)
		type = dns_rdatatype_any;
	else
		type = qtype;

	/*
	 * Get some resources.
	 */
	dbuf = query_getnamebuf(client);
	if (dbuf == NULL)
		goto cleanup;
	fname = query_newname(client, dbuf, &b);
	rdataset = query_newrdataset(client);
	if (fname == NULL || rdataset == NULL)
		goto cleanup;
	if (WANTDNSSEC(client)) {
		sigrdataset = query_newrdataset(client);
		if (sigrdataset == NULL)
			goto cleanup;
	}

	/*
	 * Look for a zone database that might contain authoritative
	 * additional data.
	 */
	result = query_getzonedb(client, name, qtype, DNS_GETDB_NOLOG,
				 &zone, &db, &version);
	if (result != ISC_R_SUCCESS)
		goto try_cache;

	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional: db_find");

	/*
	 * Since we are looking for authoritative data, we do not set
	 * the GLUEOK flag.  Glue will be looked for later, but not
	 * necessarily in the same database.
	 */
	result = dns_db_findext(db, name, version, type,
				client->query.dboptions,
				client->now, &node, fname, &cm, &ci,
				rdataset, sigrdataset);
	if (result == ISC_R_SUCCESS) {
		if (sigrdataset != NULL && !dns_db_issecure(db) &&
		    dns_rdataset_isassociated(sigrdataset))
			dns_rdataset_disassociate(sigrdataset);
		goto found;
	}

	if (dns_rdataset_isassociated(rdataset))
		dns_rdataset_disassociate(rdataset);
	if (sigrdataset != NULL && dns_rdataset_isassociated(sigrdataset))
		dns_rdataset_disassociate(sigrdataset);
	if (node != NULL)
		dns_db_detachnode(db, &node);
	version = NULL;
	dns_db_detach(&db);

	/*
	 * No authoritative data was found.  The cache is our next best bet.
	 */

 try_cache:
	additionaltype = dns_rdatasetadditional_fromcache;
	result = query_getcachedb(client, name, qtype, &db, DNS_GETDB_NOLOG);
	if (result != ISC_R_SUCCESS)
		/*
		 * Most likely the client isn't allowed to query the cache.
		 */
		goto try_glue;
	/*
	 * Attempt to validate glue.
	 */
	if (sigrdataset == NULL) {
		sigrdataset = query_newrdataset(client);
		if (sigrdataset == NULL)
			goto cleanup;
	}
	result = dns_db_findext(db, name, version, type,
				client->query.dboptions |
				DNS_DBFIND_GLUEOK | DNS_DBFIND_ADDITIONALOK,
				client->now, &node, fname, &cm, &ci,
				rdataset, sigrdataset);

	dns_cache_updatestats(client->view->cache, result);
	if (!WANTDNSSEC(client))
		query_putrdataset(client, &sigrdataset);
	if (result == ISC_R_SUCCESS)
		goto found;

	if (dns_rdataset_isassociated(rdataset))
		dns_rdataset_disassociate(rdataset);
	if (sigrdataset != NULL && dns_rdataset_isassociated(sigrdataset))
		dns_rdataset_disassociate(sigrdataset);
	if (node != NULL)
		dns_db_detachnode(db, &node);
	dns_db_detach(&db);

 try_glue:
	/*
	 * No cached data was found.  Glue is our last chance.
	 * RFC1035 sayeth:
	 *
	 *	NS records cause both the usual additional section
	 *	processing to locate a type A record, and, when used
	 *	in a referral, a special search of the zone in which
	 *	they reside for glue information.
	 *
	 * This is the "special search".  Note that we must search
	 * the zone where the NS record resides, not the zone it
	 * points to, and that we only do the search in the delegation
	 * case (identified by client->query.gluedb being set).
	 */

	if (client->query.gluedb == NULL)
		goto cleanup;

	/*
	 * Don't poison caches using the bailiwick protection model.
	 */
	if (!dns_name_issubdomain(name, dns_db_origin(client->query.gluedb)))
		goto cleanup;

	dns_db_attach(client->query.gluedb, &db);

	additionaltype = dns_rdatasetadditional_fromglue;
	result = dns_db_findext(db, name, version, type,
				client->query.dboptions | DNS_DBFIND_GLUEOK,
				client->now, &node, fname, &cm, &ci,
				rdataset, sigrdataset);
	if (!(result == ISC_R_SUCCESS ||
	      result == DNS_R_ZONECUT ||
	      result == DNS_R_GLUE))
		goto cleanup;

 found:
	/*
	 * We have found a potential additional data rdataset, or
	 * at least a node to iterate over.
	 */
	query_keepname(client, fname, dbuf);

	/*
	 * If we have an rdataset, add it to the additional data
	 * section.
	 */
	mname = NULL;
	if (dns_rdataset_isassociated(rdataset) &&
	    !query_isduplicate(client, fname, type, &mname)) {
		if (mname != NULL) {
			INSIST(mname != fname);
			query_releasename(client, &fname);
			fname = mname;
		} else
			need_addname = ISC_TRUE;
		ISC_LIST_APPEND(fname->list, rdataset, link);
		trdataset = rdataset;
		rdataset = NULL;
		added_something = ISC_TRUE;
		/*
		 * Note: we only add SIGs if we've added the type they cover,
		 * so we do not need to check if the SIG rdataset is already
		 * in the response.
		 */
		if (sigrdataset != NULL &&
		    dns_rdataset_isassociated(sigrdataset))
		{
			ISC_LIST_APPEND(fname->list, sigrdataset, link);
			sigrdataset = NULL;
		}
	}

	if (qtype == dns_rdatatype_a) {
#ifdef ALLOW_FILTER_AAAA
		isc_boolean_t have_a = ISC_FALSE;
#endif

		/*
		 * We now go looking for A and AAAA records, along with
		 * their signatures.
		 *
		 * XXXRTH  This code could be more efficient.
		 */
		if (rdataset != NULL) {
			if (dns_rdataset_isassociated(rdataset))
				dns_rdataset_disassociate(rdataset);
		} else {
			rdataset = query_newrdataset(client);
			if (rdataset == NULL)
				goto addname;
		}
		if (sigrdataset != NULL) {
			if (dns_rdataset_isassociated(sigrdataset))
				dns_rdataset_disassociate(sigrdataset);
		} else if (WANTDNSSEC(client)) {
			sigrdataset = query_newrdataset(client);
			if (sigrdataset == NULL)
				goto addname;
		}
		if (query_isduplicate(client, fname, dns_rdatatype_a, NULL))
			goto aaaa_lookup;

		result = dns_db_findrdataset(db, node, version,
					     dns_rdatatype_a, 0,
					     client->now,
					     rdataset, sigrdataset);
		if (result == DNS_R_NCACHENXDOMAIN)
			goto addname;
		if (result == DNS_R_NCACHENXRRSET) {
			dns_rdataset_disassociate(rdataset);
			if (sigrdataset != NULL &&
			    dns_rdataset_isassociated(sigrdataset))
				dns_rdataset_disassociate(sigrdataset);
		}
		if (result == ISC_R_SUCCESS) {
			mname = NULL;
#ifdef ALLOW_FILTER_AAAA
			have_a = ISC_TRUE;
#endif
			if (additionaltype == dns_rdatasetadditional_fromcache &&
			    (DNS_TRUST_PENDING(rdataset->trust) ||
			     DNS_TRUST_GLUE(rdataset->trust)) &&
			    !validate(client, db, fname, rdataset, sigrdataset))
			{
				dns_rdataset_disassociate(rdataset);
				if (sigrdataset != NULL &&
				    dns_rdataset_isassociated(sigrdataset))
					dns_rdataset_disassociate(sigrdataset);
				/* treat as if not found */
			} else if (!query_isduplicate(client, fname,
					       dns_rdatatype_a, &mname))
			{
				if (mname != fname) {
					if (mname != NULL) {
						query_releasename(client,
								  &fname);
						fname = mname;
					} else
						need_addname = ISC_TRUE;
				}
				ISC_LIST_APPEND(fname->list, rdataset, link);
				added_something = ISC_TRUE;
				if (sigrdataset != NULL &&
				    dns_rdataset_isassociated(sigrdataset))
				{
					ISC_LIST_APPEND(fname->list,
							sigrdataset, link);
					sigrdataset =
						query_newrdataset(client);
				}
				rdataset = query_newrdataset(client);
				if (rdataset == NULL)
					goto addname;
				if (WANTDNSSEC(client) && sigrdataset == NULL)
					goto addname;
			} else {
				dns_rdataset_disassociate(rdataset);
				if (sigrdataset != NULL &&
				    dns_rdataset_isassociated(sigrdataset))
					dns_rdataset_disassociate(sigrdataset);
			}
		}
  aaaa_lookup:
		if (query_isduplicate(client, fname, dns_rdatatype_aaaa, NULL))
			goto addname;

		result = dns_db_findrdataset(db, node, version,
					     dns_rdatatype_aaaa, 0,
					     client->now,
					     rdataset, sigrdataset);
		if (result == DNS_R_NCACHENXDOMAIN)
			goto addname;
		if (result == DNS_R_NCACHENXRRSET) {
			dns_rdataset_disassociate(rdataset);
			if (sigrdataset != NULL &&
			    dns_rdataset_isassociated(sigrdataset))
				dns_rdataset_disassociate(sigrdataset);
		}
		if (result == ISC_R_SUCCESS) {
			mname = NULL;
			/*
			 * There's an A; check whether we're filtering AAAA
			 */
#ifdef ALLOW_FILTER_AAAA
			if (have_a &&
			    (client->filter_aaaa == dns_aaaa_break_dnssec ||
			    (client->filter_aaaa == dns_aaaa_filter &&
			     (!WANTDNSSEC(client) || sigrdataset == NULL ||
			      !dns_rdataset_isassociated(sigrdataset)))))
				goto addname;
#endif
			if (additionaltype == dns_rdatasetadditional_fromcache &&
			    (DNS_TRUST_PENDING(rdataset->trust) ||
			     DNS_TRUST_GLUE(rdataset->trust)) &&
			    !validate(client, db, fname, rdataset, sigrdataset))
			{
				dns_rdataset_disassociate(rdataset);
				if (sigrdataset != NULL &&
				    dns_rdataset_isassociated(sigrdataset))
					dns_rdataset_disassociate(sigrdataset);
				/* treat as if not found */
			} else if (!query_isduplicate(client, fname,
					       dns_rdatatype_aaaa, &mname))
			{
				if (mname != fname) {
					if (mname != NULL) {
						query_releasename(client,
								  &fname);
						fname = mname;
					} else
						need_addname = ISC_TRUE;
				}
				ISC_LIST_APPEND(fname->list, rdataset, link);
				added_something = ISC_TRUE;
				if (sigrdataset != NULL &&
				    dns_rdataset_isassociated(sigrdataset))
				{
					ISC_LIST_APPEND(fname->list,
							sigrdataset, link);
					sigrdataset = NULL;
				}
				rdataset = NULL;
			}
		}
	}

 addname:
	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional: addname");
	/*
	 * If we haven't added anything, then we're done.
	 */
	if (!added_something)
		goto cleanup;

	/*
	 * We may have added our rdatasets to an existing name, if so, then
	 * need_addname will be ISC_FALSE.  Whether we used an existing name
	 * or a new one, we must set fname to NULL to prevent cleanup.
	 */
	if (need_addname)
		dns_message_addname(client->message, fname,
				    DNS_SECTION_ADDITIONAL);
	fname = NULL;

	/*
	 * In a few cases, we want to add additional data for additional
	 * data.  It's simpler to just deal with special cases here than
	 * to try to create a general purpose mechanism and allow the
	 * rdata implementations to do it themselves.
	 *
	 * This involves recursion, but the depth is limited.  The
	 * most complex case is adding a SRV rdataset, which involves
	 * recursing to add address records, which in turn can cause
	 * recursion to add KEYs.
	 */
	if (type == dns_rdatatype_srv && trdataset != NULL) {
		/*
		 * If we're adding SRV records to the additional data
		 * section, it's helpful if we add the SRV additional data
		 * as well.
		 */
		eresult = dns_rdataset_additionaldata(trdataset,
						      query_addadditional,
						      client);
	}

 cleanup:
	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional: cleanup");
	query_putrdataset(client, &rdataset);
	if (sigrdataset != NULL)
		query_putrdataset(client, &sigrdataset);
	if (fname != NULL)
		query_releasename(client, &fname);
	if (node != NULL)
		dns_db_detachnode(db, &node);
	if (db != NULL)
		dns_db_detach(&db);
	if (zone != NULL)
		dns_zone_detach(&zone);

	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional: done");
	return (eresult);
}

static inline void
query_discardcache(ns_client_t *client, dns_rdataset_t *rdataset_base,
		   dns_rdatasetadditional_t additionaltype,
		   dns_rdatatype_t type, dns_zone_t **zonep, dns_db_t **dbp,
		   dns_dbversion_t **versionp, dns_dbnode_t **nodep,
		   dns_name_t *fname)
{
	dns_rdataset_t *rdataset;

	while  ((rdataset = ISC_LIST_HEAD(fname->list)) != NULL) {
		ISC_LIST_UNLINK(fname->list, rdataset, link);
		query_putrdataset(client, &rdataset);
	}
	if (*versionp != NULL)
		dns_db_closeversion(*dbp, versionp, ISC_FALSE);
	if (*nodep != NULL)
		dns_db_detachnode(*dbp, nodep);
	if (*dbp != NULL)
		dns_db_detach(dbp);
	if (*zonep != NULL)
		dns_zone_detach(zonep);
	(void)dns_rdataset_putadditional(client->view->acache, rdataset_base,
					 additionaltype, type);
}

static inline isc_result_t
query_iscachevalid(dns_zone_t *zone, dns_db_t *db, dns_db_t *db0,
		   dns_dbversion_t *version)
{
	isc_result_t result = ISC_R_SUCCESS;
	dns_dbversion_t *version_current = NULL;
	dns_db_t *db_current = db0;

	if (db_current == NULL) {
		result = dns_zone_getdb(zone, &db_current);
		if (result != ISC_R_SUCCESS)
			return (result);
	}
	dns_db_currentversion(db_current, &version_current);
	if (db_current != db || version_current != version) {
		result = ISC_R_FAILURE;
		goto cleanup;
	}

 cleanup:
	dns_db_closeversion(db_current, &version_current, ISC_FALSE);
	if (db0 == NULL && db_current != NULL)
		dns_db_detach(&db_current);

	return (result);
}

static isc_result_t
query_addadditional2(void *arg, dns_name_t *name, dns_rdatatype_t qtype) {
	client_additionalctx_t *additionalctx = arg;
	dns_rdataset_t *rdataset_base;
	ns_client_t *client;
	isc_result_t result, eresult;
	dns_dbnode_t *node, *cnode;
	dns_db_t *db, *cdb;
	dns_name_t *fname, *mname0, cfname;
	dns_rdataset_t *rdataset, *sigrdataset;
	dns_rdataset_t *crdataset, *crdataset_next;
	isc_buffer_t *dbuf;
	isc_buffer_t b;
	dns_dbversion_t *version, *cversion;
	isc_boolean_t added_something, need_addname, needadditionalcache;
	isc_boolean_t need_sigrrset;
	dns_zone_t *zone;
	dns_rdatatype_t type;
	dns_rdatasetadditional_t additionaltype;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;

	/*
	 * If we don't have an additional cache call query_addadditional.
	 */
	client = additionalctx->client;
	REQUIRE(NS_CLIENT_VALID(client));

	if (qtype != dns_rdatatype_a || client->view->acache == NULL) {
		/*
		 * This function is optimized for "address" types.  For other
		 * types, use a generic routine.
		 * XXX: ideally, this function should be generic enough.
		 */
		return (query_addadditional(additionalctx->client,
					    name, qtype));
	}

	/*
	 * Initialization.
	 */
	rdataset_base = additionalctx->rdataset;
	eresult = ISC_R_SUCCESS;
	fname = NULL;
	rdataset = NULL;
	sigrdataset = NULL;
	db = NULL;
	cdb = NULL;
	version = NULL;
	cversion = NULL;
	node = NULL;
	cnode = NULL;
	added_something = ISC_FALSE;
	need_addname = ISC_FALSE;
	zone = NULL;
	needadditionalcache = ISC_FALSE;
	POST(needadditionalcache);
	additionaltype = dns_rdatasetadditional_fromauth;
	dns_name_init(&cfname, NULL);
	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, NULL, NULL);

	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional2");

	/*
	 * We treat type A additional section processing as if it
	 * were "any address type" additional section processing.
	 * To avoid multiple lookups, we do an 'any' database
	 * lookup and iterate over the node.
	 * XXXJT: this approach can cause a suboptimal result when the cache
	 * DB only has partial address types and the glue DB has remaining
	 * ones.
	 */
	type = dns_rdatatype_any;

	/*
	 * Get some resources.
	 */
	dbuf = query_getnamebuf(client);
	if (dbuf == NULL)
		goto cleanup;
	fname = query_newname(client, dbuf, &b);
	if (fname == NULL)
		goto cleanup;
	dns_name_setbuffer(&cfname, &b); /* share the buffer */

	/* Check additional cache */
	result = dns_rdataset_getadditional(rdataset_base, additionaltype,
					    type, client->view->acache, &zone,
					    &cdb, &cversion, &cnode, &cfname,
					    client->message, client->now);
	if (result != ISC_R_SUCCESS)
		goto findauthdb;
	if (zone == NULL) {
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_addadditional2: auth zone not found");
		goto try_cache;
	}

	/* Is the cached DB up-to-date? */
	result = query_iscachevalid(zone, cdb, NULL, cversion);
	if (result != ISC_R_SUCCESS) {
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_addadditional2: old auth additional cache");
		query_discardcache(client, rdataset_base, additionaltype,
				   type, &zone, &cdb, &cversion, &cnode,
				   &cfname);
		goto findauthdb;
	}

	if (cnode == NULL) {
		/*
		 * We have a negative cache.  We don't have to check the zone
		 * ACL, since the result (not using this zone) would be same
		 * regardless of the result.
		 */
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_addadditional2: negative auth additional cache");
		dns_db_closeversion(cdb, &cversion, ISC_FALSE);
		dns_db_detach(&cdb);
		dns_zone_detach(&zone);
		goto try_cache;
	}

	result = query_validatezonedb(client, name, qtype, DNS_GETDB_NOLOG,
				      zone, cdb, NULL);
	if (result != ISC_R_SUCCESS) {
		query_discardcache(client, rdataset_base, additionaltype,
				   type, &zone, &cdb, &cversion, &cnode,
				   &cfname);
		goto try_cache;
	}

	/* We've got an active cache. */
	CTRACE(ISC_LOG_DEBUG(3),
	       "query_addadditional2: auth additional cache");
	dns_db_closeversion(cdb, &cversion, ISC_FALSE);
	db = cdb;
	node = cnode;
	dns_name_clone(&cfname, fname);
	query_keepname(client, fname, dbuf);
	goto foundcache;

	/*
	 * Look for a zone database that might contain authoritative
	 * additional data.
	 */
 findauthdb:
	result = query_getzonedb(client, name, qtype, DNS_GETDB_NOLOG,
				 &zone, &db, &version);
	if (result != ISC_R_SUCCESS) {
		/* Cache the negative result */
		(void)dns_rdataset_setadditional(rdataset_base, additionaltype,
						 type, client->view->acache,
						 NULL, NULL, NULL, NULL,
						 NULL);
		goto try_cache;
	}

	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional2: db_find");

	/*
	 * Since we are looking for authoritative data, we do not set
	 * the GLUEOK flag.  Glue will be looked for later, but not
	 * necessarily in the same database.
	 */
	node = NULL;
	result = dns_db_findext(db, name, version, type,
				client->query.dboptions,
				client->now, &node, fname, &cm, &ci,
				NULL, NULL);
	if (result == ISC_R_SUCCESS)
		goto found;

	/* Cache the negative result */
	(void)dns_rdataset_setadditional(rdataset_base, additionaltype,
					 type, client->view->acache, zone, db,
					 version, NULL, fname);

	if (node != NULL)
		dns_db_detachnode(db, &node);
	version = NULL;
	dns_db_detach(&db);

	/*
	 * No authoritative data was found.  The cache is our next best bet.
	 */

 try_cache:
	additionaltype = dns_rdatasetadditional_fromcache;
	result = query_getcachedb(client, name, qtype, &db, DNS_GETDB_NOLOG);
	if (result != ISC_R_SUCCESS)
		/*
		 * Most likely the client isn't allowed to query the cache.
		 */
		goto try_glue;

	result = dns_db_findext(db, name, version, type,
				client->query.dboptions |
				 DNS_DBFIND_GLUEOK | DNS_DBFIND_ADDITIONALOK,
				client->now, &node, fname, &cm, &ci,
				NULL, NULL);
	if (result == ISC_R_SUCCESS)
		goto found;

	if (node != NULL)
		dns_db_detachnode(db, &node);
	dns_db_detach(&db);

 try_glue:
	/*
	 * No cached data was found.  Glue is our last chance.
	 * RFC1035 sayeth:
	 *
	 *	NS records cause both the usual additional section
	 *	processing to locate a type A record, and, when used
	 *	in a referral, a special search of the zone in which
	 *	they reside for glue information.
	 *
	 * This is the "special search".  Note that we must search
	 * the zone where the NS record resides, not the zone it
	 * points to, and that we only do the search in the delegation
	 * case (identified by client->query.gluedb being set).
	 */
	if (client->query.gluedb == NULL)
		goto cleanup;

	/*
	 * Don't poison caches using the bailiwick protection model.
	 */
	if (!dns_name_issubdomain(name, dns_db_origin(client->query.gluedb)))
		goto cleanup;

	/* Check additional cache */
	additionaltype = dns_rdatasetadditional_fromglue;
	result = dns_rdataset_getadditional(rdataset_base, additionaltype,
					    type, client->view->acache, NULL,
					    &cdb, &cversion, &cnode, &cfname,
					    client->message, client->now);
	if (result != ISC_R_SUCCESS)
		goto findglue;

	result = query_iscachevalid(zone, cdb, client->query.gluedb, cversion);
	if (result != ISC_R_SUCCESS) {
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_addadditional2: old glue additional cache");
		query_discardcache(client, rdataset_base, additionaltype,
				   type, &zone, &cdb, &cversion, &cnode,
				   &cfname);
		goto findglue;
	}

	if (cnode == NULL) {
		/* We have a negative cache. */
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_addadditional2: negative glue additional cache");
		dns_db_closeversion(cdb, &cversion, ISC_FALSE);
		dns_db_detach(&cdb);
		goto cleanup;
	}

	/* Cache hit. */
	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional2: glue additional cache");
	dns_db_closeversion(cdb, &cversion, ISC_FALSE);
	db = cdb;
	node = cnode;
	dns_name_clone(&cfname, fname);
	query_keepname(client, fname, dbuf);
	goto foundcache;

 findglue:
	dns_db_attach(client->query.gluedb, &db);
	result = dns_db_findext(db, name, version, type,
				client->query.dboptions | DNS_DBFIND_GLUEOK,
				client->now, &node, fname, &cm, &ci,
				NULL, NULL);
	if (!(result == ISC_R_SUCCESS ||
	      result == DNS_R_ZONECUT ||
	      result == DNS_R_GLUE)) {
		/* cache the negative result */
		(void)dns_rdataset_setadditional(rdataset_base, additionaltype,
						 type, client->view->acache,
						 NULL, db, version, NULL,
						 fname);
		goto cleanup;
	}

 found:
	/*
	 * We have found a DB node to iterate over from a DB.
	 * We are going to look for address RRsets (i.e., A and AAAA) in the DB
	 * node we've just found.  We'll then store the complete information
	 * in the additional data cache.
	 */
	dns_name_clone(fname, &cfname);
	query_keepname(client, fname, dbuf);
	needadditionalcache = ISC_TRUE;

	rdataset = query_newrdataset(client);
	if (rdataset == NULL)
		goto cleanup;

	sigrdataset = query_newrdataset(client);
	if (sigrdataset == NULL)
		goto cleanup;

	if (additionaltype == dns_rdatasetadditional_fromcache &&
	    query_isduplicate(client, fname, dns_rdatatype_a, NULL))
		goto aaaa_lookup;

	/*
	 * Find A RRset with sig RRset.  Even if we don't find a sig RRset
	 * for a client using DNSSEC, we'll continue the process to make a
	 * complete list to be cached.  However, we need to cancel the
	 * caching when something unexpected happens, in order to avoid
	 * caching incomplete information.
	 */
	result = dns_db_findrdataset(db, node, version, dns_rdatatype_a, 0,
				     client->now, rdataset, sigrdataset);
	/*
	 * If we can't promote glue/pending from the cache to secure
	 * then drop it.
	 */
	if (result == ISC_R_SUCCESS &&
	    additionaltype == dns_rdatasetadditional_fromcache &&
	    (DNS_TRUST_PENDING(rdataset->trust) ||
	     DNS_TRUST_GLUE(rdataset->trust)) &&
	    !validate(client, db, fname, rdataset, sigrdataset)) {
		dns_rdataset_disassociate(rdataset);
		if (dns_rdataset_isassociated(sigrdataset))
			dns_rdataset_disassociate(sigrdataset);
		result = ISC_R_NOTFOUND;
	}
	if (result == DNS_R_NCACHENXDOMAIN)
		goto setcache;
	if (result == DNS_R_NCACHENXRRSET) {
		dns_rdataset_disassociate(rdataset);
		if (dns_rdataset_isassociated(sigrdataset))
			dns_rdataset_disassociate(sigrdataset);
	}
	if (result == ISC_R_SUCCESS) {
		/* Remember the result as a cache */
		ISC_LIST_APPEND(cfname.list, rdataset, link);
		if (dns_rdataset_isassociated(sigrdataset)) {
			ISC_LIST_APPEND(cfname.list, sigrdataset, link);
			sigrdataset = query_newrdataset(client);
		}
		rdataset = query_newrdataset(client);
		if (sigrdataset == NULL || rdataset == NULL) {
			/* do not cache incomplete information */
			goto foundcache;
		}
	}

 aaaa_lookup:
	if (additionaltype == dns_rdatasetadditional_fromcache &&
	    query_isduplicate(client, fname, dns_rdatatype_aaaa, NULL))
		goto foundcache;
	/* Find AAAA RRset with sig RRset */
	result = dns_db_findrdataset(db, node, version, dns_rdatatype_aaaa, 0,
				     client->now, rdataset, sigrdataset);
	/*
	 * If we can't promote glue/pending from the cache to secure
	 * then drop it.
	 */
	if (result == ISC_R_SUCCESS &&
	    additionaltype == dns_rdatasetadditional_fromcache &&
	    (DNS_TRUST_PENDING(rdataset->trust) ||
	     DNS_TRUST_GLUE(rdataset->trust)) &&
	    !validate(client, db, fname, rdataset, sigrdataset)) {
		dns_rdataset_disassociate(rdataset);
		if (dns_rdataset_isassociated(sigrdataset))
			dns_rdataset_disassociate(sigrdataset);
		result = ISC_R_NOTFOUND;
	}
	if (result == ISC_R_SUCCESS) {
		ISC_LIST_APPEND(cfname.list, rdataset, link);
		rdataset = NULL;
		if (dns_rdataset_isassociated(sigrdataset)) {
			ISC_LIST_APPEND(cfname.list, sigrdataset, link);
			sigrdataset = NULL;
		}
	}

 setcache:
	/*
	 * Set the new result in the cache if required.  We do not support
	 * caching additional data from a cache DB.
	 */
	if (needadditionalcache == ISC_TRUE &&
	    (additionaltype == dns_rdatasetadditional_fromauth ||
	     additionaltype == dns_rdatasetadditional_fromglue)) {
		(void)dns_rdataset_setadditional(rdataset_base, additionaltype,
						 type, client->view->acache,
						 zone, db, version, node,
						 &cfname);
	}

 foundcache:
	need_sigrrset = ISC_FALSE;
	mname0 = NULL;
	for (crdataset = ISC_LIST_HEAD(cfname.list);
	     crdataset != NULL;
	     crdataset = crdataset_next) {
		dns_name_t *mname;

		crdataset_next = ISC_LIST_NEXT(crdataset, link);

		mname = NULL;
		if (crdataset->type == dns_rdatatype_a ||
		    crdataset->type == dns_rdatatype_aaaa) {
			if (!query_isduplicate(client, fname, crdataset->type,
					       &mname)) {
				if (mname != fname) {
					if (mname != NULL) {
						/*
						 * A different type of this name is
						 * already stored in the additional
						 * section.  We'll reuse the name.
						 * Note that this should happen at most
						 * once.  Otherwise, fname->link could
						 * leak below.
						 */
						INSIST(mname0 == NULL);

						query_releasename(client, &fname);
						fname = mname;
						mname0 = mname;
					} else
						need_addname = ISC_TRUE;
				}
				ISC_LIST_UNLINK(cfname.list, crdataset, link);
				ISC_LIST_APPEND(fname->list, crdataset, link);
				added_something = ISC_TRUE;
				need_sigrrset = ISC_TRUE;
			} else
				need_sigrrset = ISC_FALSE;
		} else if (crdataset->type == dns_rdatatype_rrsig &&
			   need_sigrrset && WANTDNSSEC(client)) {
			ISC_LIST_UNLINK(cfname.list, crdataset, link);
			ISC_LIST_APPEND(fname->list, crdataset, link);
			added_something = ISC_TRUE; /* just in case */
			need_sigrrset = ISC_FALSE;
		}
	}

	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional2: addname");

	/*
	 * If we haven't added anything, then we're done.
	 */
	if (!added_something)
		goto cleanup;

	/*
	 * We may have added our rdatasets to an existing name, if so, then
	 * need_addname will be ISC_FALSE.  Whether we used an existing name
	 * or a new one, we must set fname to NULL to prevent cleanup.
	 */
	if (need_addname)
		dns_message_addname(client->message, fname,
				    DNS_SECTION_ADDITIONAL);
	fname = NULL;

 cleanup:
	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional2: cleanup");

	if (rdataset != NULL)
		query_putrdataset(client, &rdataset);
	if (sigrdataset != NULL)
		query_putrdataset(client, &sigrdataset);
	while  ((crdataset = ISC_LIST_HEAD(cfname.list)) != NULL) {
		ISC_LIST_UNLINK(cfname.list, crdataset, link);
		query_putrdataset(client, &crdataset);
	}
	if (fname != NULL)
		query_releasename(client, &fname);
	if (node != NULL)
		dns_db_detachnode(db, &node);
	if (db != NULL)
		dns_db_detach(&db);
	if (zone != NULL)
		dns_zone_detach(&zone);

	CTRACE(ISC_LOG_DEBUG(3), "query_addadditional2: done");
	return (eresult);
}

static inline void
query_addrdataset(ns_client_t *client, dns_name_t *fname,
		  dns_rdataset_t *rdataset)
{
	client_additionalctx_t additionalctx;

	/*
	 * Add 'rdataset' and any pertinent additional data to
	 * 'fname', a name in the response message for 'client'.
	 */

	CTRACE(ISC_LOG_DEBUG(3), "query_addrdataset");

	ISC_LIST_APPEND(fname->list, rdataset, link);

	if (client->view->order != NULL)
#ifdef ORIGINAL_ISC_CODE
		rdataset->attributes |= dns_order_find(client->view->order,
						       fname, rdataset->type,
						       rdataset->rdclass);
#else
	{
		unsigned int attributes = dns_order_find(client->view->order,
							 fname, rdataset->type,
							 rdataset->rdclass);
		if (! infoblox_host_rrset_order() || attributes == 0) {
			rdataset->attributes |= attributes;
		} else {
			// Order type from named.conf overrides one from the DB
			rdataset->attributes &= ~(DNS_RDATASETATTR_FIXEDORDER|DNS_RDATASETATTR_RANDOMIZE);
			rdataset->attributes |= attributes;
		}
	}
#endif
	rdataset->attributes |= DNS_RDATASETATTR_LOADORDER;

	if (NOADDITIONAL(client))
		return;

	/*
	 * Add additional data.
	 *
	 * We don't care if dns_rdataset_additionaldata() fails.
	 */
	additionalctx.client = client;
	additionalctx.rdataset = rdataset;
	(void)dns_rdataset_additionaldata(rdataset, query_addadditional2,
					  &additionalctx);
	CTRACE(ISC_LOG_DEBUG(3), "query_addrdataset: done");
}

static isc_result_t
query_dns64(ns_client_t *client, dns_name_t **namep, dns_rdataset_t *rdataset,
	    dns_rdataset_t *sigrdataset, isc_buffer_t *dbuf,
	    dns_section_t section)
{
	dns_name_t *name, *mname;
	dns_rdata_t *dns64_rdata;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	dns_rdatalist_t *dns64_rdatalist;
	dns_rdataset_t *dns64_rdataset;
	dns_rdataset_t *mrdataset;
	isc_buffer_t *buffer;
	isc_region_t r;
	isc_result_t result;
	dns_view_t *view = client->view;
	isc_netaddr_t netaddr;
	dns_dns64_t *dns64;
	unsigned int flags = 0;

	/*%
	 * To the current response for 'client', add the answer RRset
	 * '*rdatasetp' and an optional signature set '*sigrdatasetp', with
	 * owner name '*namep', to section 'section', unless they are
	 * already there.  Also add any pertinent additional data.
	 *
	 * If 'dbuf' is not NULL, then '*namep' is the name whose data is
	 * stored in 'dbuf'.  In this case, query_addrrset() guarantees that
	 * when it returns the name will either have been kept or released.
	 */
	CTRACE(ISC_LOG_DEBUG(3), "query_dns64");
	name = *namep;
	mname = NULL;
	mrdataset = NULL;
	buffer = NULL;
	dns64_rdata = NULL;
	dns64_rdataset = NULL;
	dns64_rdatalist = NULL;
	result = dns_message_findname(client->message, section,
				      name, dns_rdatatype_aaaa,
				      rdataset->covers,
				      &mname, &mrdataset);
	if (result == ISC_R_SUCCESS) {
		/*
		 * We've already got an RRset of the given name and type.
		 * There's nothing else to do;
		 */
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_dns64: dns_message_findname succeeded: done");
		if (dbuf != NULL)
			query_releasename(client, namep);
		return (ISC_R_SUCCESS);
	} else if (result == DNS_R_NXDOMAIN) {
		/*
		 * The name doesn't exist.
		 */
		if (dbuf != NULL)
			query_keepname(client, name, dbuf);
		dns_message_addname(client->message, name, section);
		*namep = NULL;
		mname = name;
	} else {
		RUNTIME_CHECK(result == DNS_R_NXRRSET);
		if (dbuf != NULL)
			query_releasename(client, namep);
	}

	if (rdataset->trust != dns_trust_secure &&
	    (section == DNS_SECTION_ANSWER ||
	     section == DNS_SECTION_AUTHORITY))
		client->query.attributes &= ~NS_QUERYATTR_SECURE;

	isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);

	result = isc_buffer_allocate(client->mctx, &buffer, view->dns64cnt *
				     16 * dns_rdataset_count(rdataset));
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	result = dns_message_gettemprdataset(client->message, &dns64_rdataset);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	result = dns_message_gettemprdatalist(client->message,
					      &dns64_rdatalist);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	dns_rdatalist_init(dns64_rdatalist);
	dns64_rdatalist->rdclass = dns_rdataclass_in;
	dns64_rdatalist->type = dns_rdatatype_aaaa;
	if (client->query.dns64_ttl != ISC_UINT32_MAX)
		dns64_rdatalist->ttl = ISC_MIN(rdataset->ttl,
					       client->query.dns64_ttl);
	else
		dns64_rdatalist->ttl = ISC_MIN(rdataset->ttl, 600);

	if (RECURSIONOK(client))
		flags |= DNS_DNS64_RECURSIVE;

	/*
	 * We use the signatures from the A lookup to set DNS_DNS64_DNSSEC
	 * as this provides a easy way to see if the answer was signed.
	 */
	if (sigrdataset != NULL && dns_rdataset_isassociated(sigrdataset))
		flags |= DNS_DNS64_DNSSEC;

	for (result = dns_rdataset_first(rdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(rdataset)) {
		for (dns64 = ISC_LIST_HEAD(client->view->dns64);
		     dns64 != NULL; dns64 = dns_dns64_next(dns64)) {

			dns_rdataset_current(rdataset, &rdata);
			isc_buffer_availableregion(buffer, &r);
			INSIST(r.length >= 16);
			result = dns_dns64_aaaafroma(dns64, &netaddr,
						     client->signer,
						     &ns_g_server->aclenv,
						     flags, rdata.data, r.base);
			if (result != ISC_R_SUCCESS) {
				dns_rdata_reset(&rdata);
				continue;
			}
			isc_buffer_add(buffer, 16);
			isc_buffer_remainingregion(buffer, &r);
			isc_buffer_forward(buffer, 16);
			result = dns_message_gettemprdata(client->message,
							  &dns64_rdata);
			if (result != ISC_R_SUCCESS)
				goto cleanup;
			dns_rdata_init(dns64_rdata);
			dns_rdata_fromregion(dns64_rdata, dns_rdataclass_in,
					     dns_rdatatype_aaaa, &r);
			ISC_LIST_APPEND(dns64_rdatalist->rdata, dns64_rdata,
					link);
			dns64_rdata = NULL;
			dns_rdata_reset(&rdata);
		}
	}
	if (result != ISC_R_NOMORE)
		goto cleanup;

	if (ISC_LIST_EMPTY(dns64_rdatalist->rdata))
		goto cleanup;

	result = dns_rdatalist_tordataset(dns64_rdatalist, dns64_rdataset);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	dns_rdataset_setownercase(dns64_rdataset, mname);
	client->query.attributes |= NS_QUERYATTR_NOADDITIONAL;
	dns64_rdataset->trust = rdataset->trust;
	query_addrdataset(client, mname, dns64_rdataset);
	dns64_rdataset = NULL;
	dns64_rdatalist = NULL;
	dns_message_takebuffer(client->message, &buffer);
	ns_query_incstats(client, dns_nsstatscounter_dns64);
	result = ISC_R_SUCCESS;

 cleanup:
	if (buffer != NULL)
		isc_buffer_free(&buffer);

	if (dns64_rdata != NULL)
		dns_message_puttemprdata(client->message, &dns64_rdata);

	if (dns64_rdataset != NULL)
		dns_message_puttemprdataset(client->message, &dns64_rdataset);

	if (dns64_rdatalist != NULL) {
		for (dns64_rdata = ISC_LIST_HEAD(dns64_rdatalist->rdata);
		     dns64_rdata != NULL;
		     dns64_rdata = ISC_LIST_HEAD(dns64_rdatalist->rdata))
		{
			ISC_LIST_UNLINK(dns64_rdatalist->rdata,
					dns64_rdata, link);
			dns_message_puttemprdata(client->message, &dns64_rdata);
		}
		dns_message_puttemprdatalist(client->message, &dns64_rdatalist);
	}

	CTRACE(ISC_LOG_DEBUG(3), "query_dns64: done");
	return (result);
}

static void
query_filter64(ns_client_t *client, dns_name_t **namep,
	       dns_rdataset_t *rdataset, isc_buffer_t *dbuf,
	       dns_section_t section)
{
	dns_name_t *name, *mname;
	dns_rdata_t *myrdata;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	dns_rdatalist_t *myrdatalist;
	dns_rdataset_t *myrdataset;
	isc_buffer_t *buffer;
	isc_region_t r;
	isc_result_t result;
	unsigned int i;

	CTRACE(ISC_LOG_DEBUG(3), "query_filter64");

	INSIST(client->query.dns64_aaaaok != NULL);
	INSIST(client->query.dns64_aaaaoklen == dns_rdataset_count(rdataset));

	name = *namep;
	mname = NULL;
	buffer = NULL;
	myrdata = NULL;
	myrdataset = NULL;
	myrdatalist = NULL;
	result = dns_message_findname(client->message, section,
				      name, dns_rdatatype_aaaa,
				      rdataset->covers,
				      &mname, &myrdataset);
	if (result == ISC_R_SUCCESS) {
		/*
		 * We've already got an RRset of the given name and type.
		 * There's nothing else to do;
		 */
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_filter64: dns_message_findname succeeded: done");
		if (dbuf != NULL)
			query_releasename(client, namep);
		return;
	} else if (result == DNS_R_NXDOMAIN) {
		mname = name;
		*namep = NULL;
	} else {
		RUNTIME_CHECK(result == DNS_R_NXRRSET);
		if (dbuf != NULL)
			query_releasename(client, namep);
		dbuf = NULL;
	}

	if (rdataset->trust != dns_trust_secure &&
	    (section == DNS_SECTION_ANSWER ||
	     section == DNS_SECTION_AUTHORITY))
		client->query.attributes &= ~NS_QUERYATTR_SECURE;

	result = isc_buffer_allocate(client->mctx, &buffer,
				     16 * dns_rdataset_count(rdataset));
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	result = dns_message_gettemprdataset(client->message, &myrdataset);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	result = dns_message_gettemprdatalist(client->message, &myrdatalist);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	dns_rdatalist_init(myrdatalist);
	myrdatalist->rdclass = dns_rdataclass_in;
	myrdatalist->type = dns_rdatatype_aaaa;
	myrdatalist->ttl = rdataset->ttl;

	i = 0;
	for (result = dns_rdataset_first(rdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(rdataset)) {
		if (!client->query.dns64_aaaaok[i++])
			continue;
		dns_rdataset_current(rdataset, &rdata);
		INSIST(rdata.length == 16);
		isc_buffer_putmem(buffer, rdata.data, rdata.length);
		isc_buffer_remainingregion(buffer, &r);
		isc_buffer_forward(buffer, rdata.length);
		result = dns_message_gettemprdata(client->message, &myrdata);
		if (result != ISC_R_SUCCESS)
			goto cleanup;
		dns_rdata_init(myrdata);
		dns_rdata_fromregion(myrdata, dns_rdataclass_in,
				     dns_rdatatype_aaaa, &r);
		ISC_LIST_APPEND(myrdatalist->rdata, myrdata, link);
		myrdata = NULL;
		dns_rdata_reset(&rdata);
	}
	if (result != ISC_R_NOMORE)
		goto cleanup;

	result = dns_rdatalist_tordataset(myrdatalist, myrdataset);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	dns_rdataset_setownercase(myrdataset, name);
	client->query.attributes |= NS_QUERYATTR_NOADDITIONAL;
	if (mname == name) {
		if (dbuf != NULL)
			query_keepname(client, name, dbuf);
		dns_message_addname(client->message, name, section);
		dbuf = NULL;
	}
	myrdataset->trust = rdataset->trust;
	query_addrdataset(client, mname, myrdataset);
	myrdataset = NULL;
	myrdatalist = NULL;
	dns_message_takebuffer(client->message, &buffer);

 cleanup:
	if (buffer != NULL)
		isc_buffer_free(&buffer);

	if (myrdata != NULL)
		dns_message_puttemprdata(client->message, &myrdata);

	if (myrdataset != NULL)
		dns_message_puttemprdataset(client->message, &myrdataset);

	if (myrdatalist != NULL) {
		for (myrdata = ISC_LIST_HEAD(myrdatalist->rdata);
		     myrdata != NULL;
		     myrdata = ISC_LIST_HEAD(myrdatalist->rdata))
		{
			ISC_LIST_UNLINK(myrdatalist->rdata, myrdata, link);
			dns_message_puttemprdata(client->message, &myrdata);
		}
		dns_message_puttemprdatalist(client->message, &myrdatalist);
	}
	if (dbuf != NULL)
		query_releasename(client, &name);

	CTRACE(ISC_LOG_DEBUG(3), "query_filter64: done");
}

static void
query_addrrset(ns_client_t *client, dns_name_t **namep,
	       dns_rdataset_t **rdatasetp, dns_rdataset_t **sigrdatasetp,
	       isc_buffer_t *dbuf, dns_section_t section)
{
	dns_name_t *name, *mname;
	dns_rdataset_t *rdataset, *mrdataset, *sigrdataset;
	isc_result_t result;

	/*%
	 * To the current response for 'client', add the answer RRset
	 * '*rdatasetp' and an optional signature set '*sigrdatasetp', with
	 * owner name '*namep', to section 'section', unless they are
	 * already there.  Also add any pertinent additional data.
	 *
	 * If 'dbuf' is not NULL, then '*namep' is the name whose data is
	 * stored in 'dbuf'.  In this case, query_addrrset() guarantees that
	 * when it returns the name will either have been kept or released.
	 */
	CTRACE(ISC_LOG_DEBUG(3), "query_addrrset");
	name = *namep;
#ifdef ORIGINAL_ISC_CODE
#else
	if (section == DNS_SECTION_ANSWER) {
		isc_boolean_t release_namep = ISC_FALSE;
		if (QRYREWRITTEN(client) &&
		    dns_name_equal(name, client->query.ib_qname)) {
			/*
			 * ib_orig_qname was already kept in the client at the
			 * time of creation.
			 */
			name = client->query.ib_orig_qname;
			release_namep = ISC_TRUE;
		}

		if (IBALIASINITIATED(client)) {
			INSIST(client->query.ibalias_orig_qname != NULL);
			/*
			 * ibalias_orig_qname was already kept in the client
			 * at the time NS_QUERYATTR_IBALIAS_INITIATED is set.
			 */
			name = client->query.ibalias_orig_qname;
			release_namep = ISC_TRUE;
		}

		// If dbuf!=NULL, must release before query_newname(), and,
		// in any case, we are not going to use the original 'name'.
		// Note that we should keep namep intact in case it's non NULL;
		// in that case the caller is responsible to release the name.
		if (release_namep && dbuf != NULL) {
			query_releasename(client, namep);
			/* Reset dbuf to NULL to avoid accidental release.*/
			dbuf = NULL;
		}
	}

#endif
	rdataset = *rdatasetp;
	if (sigrdatasetp != NULL)
		sigrdataset = *sigrdatasetp;
	else
		sigrdataset = NULL;
	mname = NULL;
	mrdataset = NULL;
	result = dns_message_findname(client->message, section,
				      name, rdataset->type, rdataset->covers,
				      &mname, &mrdataset);
	if (result == ISC_R_SUCCESS) {
		/*
		 * We've already got an RRset of the given name and type.
		 */
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_addrrset: dns_message_findname succeeded: done");
		if (dbuf != NULL)
			query_releasename(client, namep);
		if ((rdataset->attributes & DNS_RDATASETATTR_REQUIRED) != 0)
			mrdataset->attributes |= DNS_RDATASETATTR_REQUIRED;
		return;
	} else if (result == DNS_R_NXDOMAIN) {
		/*
		 * The name doesn't exist.
		 */
		if (dbuf != NULL)
			query_keepname(client, name, dbuf);
		dns_message_addname(client->message, name, section);
#ifdef ORIGINAL_ISC_CODE
		*namep = NULL;
#else
		/*
		 * Set the stored attribute to indicate the ownership is
		 * transferred if we use the stored original qname after
		 * rewriting or finding matching IBALIAS record.
		 */
		if (name == client->query.ib_orig_qname) {
			client->query.attributes |=
				NS_QUERYATTR_ORIGQNAMESTORED;
		} else if (name == client->query.ibalias_orig_qname) {
			client->query.attributes |=
				NS_QUERYATTR_IBALIAS_ORIGQNAMESTORED;
		} else
			*namep = NULL;
#endif
		mname = name;
	} else {
		RUNTIME_CHECK(result == DNS_R_NXRRSET);
		if (dbuf != NULL)
			query_releasename(client, namep);
	}

	if (rdataset->trust != dns_trust_secure &&
	    (section == DNS_SECTION_ANSWER ||
	     section == DNS_SECTION_AUTHORITY))
		client->query.attributes &= ~NS_QUERYATTR_SECURE;

#ifdef ORIGINAL_ISC_CODE
#else
	infoblox_set_masked_auto_created_a_visible (1);

	/* Update rdataset TTL before adding it to response if searching
	 * is initiated by IBALIAS record */
	if (IBALIASINITIATED(client) &&
	    name == client->query.ibalias_orig_qname) {
		if (client->query.ibalias_ttl < rdataset->ttl)
			rdataset->ttl = client->query.ibalias_ttl;
	}
#endif

	/*
	 * Note: we only add SIGs if we've added the type they cover, so
	 * we do not need to check if the SIG rdataset is already in the
	 * response.
	 */
	query_addrdataset(client, mname, rdataset);
	*rdatasetp = NULL;
	if (sigrdataset != NULL && dns_rdataset_isassociated(sigrdataset)) {
		/*
		 * We have a signature.  Add it to the response.
		 */
		ISC_LIST_APPEND(mname->list, sigrdataset, link);
		*sigrdatasetp = NULL;
	}
#ifdef ORIGINAL_ISC_CODE
#else
	infoblox_set_masked_auto_created_a_visible (0);
#endif
	CTRACE(ISC_LOG_DEBUG(3), "query_addrrset: done");
}

static inline isc_result_t
query_addsoa(ns_client_t *client, dns_db_t *db, dns_dbversion_t *version,
	     unsigned int override_ttl, isc_boolean_t isassociated,
	     dns_section_t section)
{
	dns_name_t *name;
	dns_dbnode_t *node;
	isc_result_t result, eresult;
	dns_rdataset_t *rdataset = NULL, *sigrdataset = NULL;
	dns_rdataset_t **sigrdatasetp = NULL;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;

	CTRACE(ISC_LOG_DEBUG(3), "query_addsoa");
	/*
	 * Initialization.
	 */
	eresult = ISC_R_SUCCESS;
	name = NULL;
	rdataset = NULL;
	node = NULL;

	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, NULL, NULL);

	/*
	 * Don't add the SOA record for test which set "-T nosoa".
	 */
	if (ns_g_nosoa && (!WANTDNSSEC(client) || !isassociated))
		return (ISC_R_SUCCESS);

	/*
	 * Get resources and make 'name' be the database origin.
	 */
	result = dns_message_gettempname(client->message, &name);
	if (result != ISC_R_SUCCESS)
		return (result);
	dns_name_init(name, NULL);
	dns_name_clone(dns_db_origin(db), name);
	rdataset = query_newrdataset(client);
	if (rdataset == NULL) {
		CTRACE(ISC_LOG_ERROR, "unable to allocate rdataset");
		eresult = DNS_R_SERVFAIL;
		goto cleanup;
	}
	if (WANTDNSSEC(client) && dns_db_issecure(db)) {
		sigrdataset = query_newrdataset(client);
		if (sigrdataset == NULL) {
			CTRACE(ISC_LOG_ERROR, "unable to allocate sigrdataset");
			eresult = DNS_R_SERVFAIL;
			goto cleanup;
		}
	}

	/*
	 * Find the SOA.
	 */
	result = dns_db_getoriginnode(db, &node);
	if (result == ISC_R_SUCCESS) {
		result = dns_db_findrdataset(db, node, version,
					     dns_rdatatype_soa, 0, client->now,
					     rdataset, sigrdataset);
	} else {
		dns_fixedname_t foundname;
		dns_name_t *fname;

		dns_fixedname_init(&foundname);
		fname = dns_fixedname_name(&foundname);

		result = dns_db_findext(db, name, version, dns_rdatatype_soa,
#ifdef ORIGINAL_ISC_CODE
					client->query.dboptions, 0, &node,
#else
					// If the caller didn't specify zero_ttl, we can accept
					// a cached SOA.
					!override_ttl ? client->query.dboptions : client->query.dboptions | DNS_DBFIND_CACHED_SOA_OK,
					0, &node,
#endif
					fname, &cm, &ci, rdataset, sigrdataset);
	}
	if (result != ISC_R_SUCCESS) {
		/*
		 * This is bad.  We tried to get the SOA RR at the zone top
		 * and it didn't work!
		 */
		CTRACE(ISC_LOG_ERROR, "unable to find SOA RR at zone apex");
		eresult = DNS_R_SERVFAIL;
	} else {
		/*
		 * Extract the SOA MINIMUM.
		 */
		dns_rdata_soa_t soa;
		dns_rdata_t rdata = DNS_RDATA_INIT;
		result = dns_rdataset_first(rdataset);
		RUNTIME_CHECK(result == ISC_R_SUCCESS);
		dns_rdataset_current(rdataset, &rdata);
		result = dns_rdata_tostruct(&rdata, &soa, NULL);
		if (result != ISC_R_SUCCESS)
			goto cleanup;

		if (override_ttl != ISC_UINT32_MAX &&
		    override_ttl < rdataset->ttl) {
			rdataset->ttl = override_ttl;
			if (sigrdataset != NULL)
				sigrdataset->ttl = override_ttl;
		}

		/*
		 * Add the SOA and its SIG to the response, with the
		 * TTLs adjusted per RFC2308 section 3.
		 */
		if (rdataset->ttl > soa.minimum)
			rdataset->ttl = soa.minimum;
		if (sigrdataset != NULL && sigrdataset->ttl > soa.minimum)
			sigrdataset->ttl = soa.minimum;

		if (sigrdataset != NULL)
			sigrdatasetp = &sigrdataset;
		else
			sigrdatasetp = NULL;

		if (section == DNS_SECTION_ADDITIONAL)
			rdataset->attributes |= DNS_RDATASETATTR_REQUIRED;
		query_addrrset(client, &name, &rdataset, sigrdatasetp, NULL,
			       section);
	}

 cleanup:
	query_putrdataset(client, &rdataset);
	if (sigrdataset != NULL)
		query_putrdataset(client, &sigrdataset);
	if (name != NULL)
		query_releasename(client, &name);
	if (node != NULL)
		dns_db_detachnode(db, &node);

	return (eresult);
}

static inline isc_result_t
query_addns(ns_client_t *client, dns_db_t *db, dns_dbversion_t *version) {
	dns_name_t *name, *fname;
	dns_dbnode_t *node;
	isc_result_t result, eresult;
	dns_fixedname_t foundname;
	dns_rdataset_t *rdataset = NULL, *sigrdataset = NULL;
	dns_rdataset_t **sigrdatasetp = NULL;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;

	CTRACE(ISC_LOG_DEBUG(3), "query_addns");
	/*
	 * Initialization.
	 */
	eresult = ISC_R_SUCCESS;
	name = NULL;
	rdataset = NULL;
	node = NULL;
	dns_fixedname_init(&foundname);
	fname = dns_fixedname_name(&foundname);
	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, NULL, NULL);

	/*
	 * Get resources and make 'name' be the database origin.
	 */
	result = dns_message_gettempname(client->message, &name);
	if (result != ISC_R_SUCCESS) {
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_addns: dns_message_gettempname failed: done");
		return (result);
	}
	dns_name_init(name, NULL);
	dns_name_clone(dns_db_origin(db), name);
	rdataset = query_newrdataset(client);
	if (rdataset == NULL) {
		CTRACE(ISC_LOG_ERROR,
		       "query_addns: query_newrdataset failed");
		eresult = DNS_R_SERVFAIL;
		goto cleanup;
	}
	if (WANTDNSSEC(client) && dns_db_issecure(db)) {
		sigrdataset = query_newrdataset(client);
		if (sigrdataset == NULL) {
			CTRACE(ISC_LOG_ERROR,
			       "query_addns: query_newrdataset failed");
			eresult = DNS_R_SERVFAIL;
			goto cleanup;
		}
	}

	/*
	 * Find the NS rdataset.
	 */
	result = dns_db_getoriginnode(db, &node);
	if (result == ISC_R_SUCCESS) {
		result = dns_db_findrdataset(db, node, version,
					     dns_rdatatype_ns, 0,
					     client->now,
					     rdataset, sigrdataset);
	} else {
		CTRACE(ISC_LOG_DEBUG(3), "query_addns: calling dns_db_find");
		result = dns_db_findext(db, name, NULL, dns_rdatatype_ns,
					client->query.dboptions, 0, &node,
					fname, &cm, &ci, rdataset, sigrdataset);
		CTRACE(ISC_LOG_DEBUG(3), "query_addns: dns_db_find complete");
	}
	if (result != ISC_R_SUCCESS) {
		CTRACE(ISC_LOG_ERROR,
		       "query_addns: "
		       "dns_db_findrdataset or dns_db_find failed");
		/*
		 * This is bad.  We tried to get the NS rdataset at the zone
		 * top and it didn't work!
		 */
		eresult = DNS_R_SERVFAIL;
	} else {
		if (sigrdataset != NULL)
			sigrdatasetp = &sigrdataset;
		else
			sigrdatasetp = NULL;
		query_addrrset(client, &name, &rdataset, sigrdatasetp, NULL,
			       DNS_SECTION_AUTHORITY);
	}

 cleanup:
	CTRACE(ISC_LOG_DEBUG(3), "query_addns: cleanup");
	query_putrdataset(client, &rdataset);
	if (sigrdataset != NULL)
		query_putrdataset(client, &sigrdataset);
	if (name != NULL)
		query_releasename(client, &name);
	if (node != NULL)
		dns_db_detachnode(db, &node);

	CTRACE(ISC_LOG_DEBUG(3), "query_addns: done");
	return (eresult);
}

static isc_result_t
query_add_cname(ns_client_t *client, dns_name_t *qname, dns_name_t *tname,
		dns_trust_t trust, dns_ttl_t ttl)
{
	dns_rdataset_t *rdataset;
	dns_rdatalist_t *rdatalist;
	dns_rdata_t *rdata;
	isc_region_t r;
	dns_name_t *aname;
	isc_result_t result;

	/*
	 * We assume the name data referred to by tname won't go away.
	 */

	aname = NULL;
	result = dns_message_gettempname(client->message, &aname);
	if (result != ISC_R_SUCCESS)
		return (result);
	result = dns_name_dup(qname, client->mctx, aname);
	if (result != ISC_R_SUCCESS) {
		dns_message_puttempname(client->message, &aname);
		return (result);
	}

	rdatalist = NULL;
	result = dns_message_gettemprdatalist(client->message, &rdatalist);
	if (result != ISC_R_SUCCESS) {
		dns_message_puttempname(client->message, &aname);
		return (result);
	}
	rdata = NULL;
	result = dns_message_gettemprdata(client->message, &rdata);
	if (result != ISC_R_SUCCESS) {
		dns_message_puttempname(client->message, &aname);
		dns_message_puttemprdatalist(client->message, &rdatalist);
		return (result);
	}
	rdataset = NULL;
	result = dns_message_gettemprdataset(client->message, &rdataset);
	if (result != ISC_R_SUCCESS) {
		dns_message_puttempname(client->message, &aname);
		dns_message_puttemprdatalist(client->message, &rdatalist);
		dns_message_puttemprdata(client->message, &rdata);
		return (result);
	}
	rdatalist->type = dns_rdatatype_cname;
	rdatalist->rdclass = client->message->rdclass;
	rdatalist->ttl = ttl;

	dns_name_toregion(tname, &r);
	rdata->data = r.base;
	rdata->length = r.length;
	rdata->rdclass = client->message->rdclass;
	rdata->type = dns_rdatatype_cname;

	ISC_LIST_APPEND(rdatalist->rdata, rdata, link);
	RUNTIME_CHECK(dns_rdatalist_tordataset(rdatalist, rdataset)
		      == ISC_R_SUCCESS);
	rdataset->trust = trust;
	dns_rdataset_setownercase(rdataset, aname);

	query_addrrset(client, &aname, &rdataset, NULL, NULL,
		       DNS_SECTION_ANSWER);
	if (rdataset != NULL) {
		if (dns_rdataset_isassociated(rdataset))
			dns_rdataset_disassociate(rdataset);
		dns_message_puttemprdataset(client->message, &rdataset);
	}
	if (aname != NULL)
		dns_message_puttempname(client->message, &aname);

	return (ISC_R_SUCCESS);
}

/*
 * Mark the RRsets as secure.  Update the cache (db) to reflect the
 * change in trust level.
 */
static void
mark_secure(ns_client_t *client, dns_db_t *db, dns_name_t *name,
	    dns_rdata_rrsig_t *rrsig, dns_rdataset_t *rdataset,
	    dns_rdataset_t *sigrdataset)
{
	isc_result_t result;
	dns_dbnode_t *node = NULL;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;
	isc_stdtime_t now;

	rdataset->trust = dns_trust_secure;
	sigrdataset->trust = dns_trust_secure;
	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, &client->ecs, NULL);

	/*
	 * Save the updated secure state.  Ignore failures.
	 */
	result = dns_db_findnodeext(db, name, ISC_TRUE, &cm, &ci, &node);
	if (result != ISC_R_SUCCESS)
		return;

	isc_stdtime_get(&now);
	dns_rdataset_trimttl(rdataset, sigrdataset, rrsig, now,
			     client->view->acceptexpired);

	(void)dns_db_addrdataset(db, node, NULL, client->now, rdataset,
				 0, NULL);
	(void)dns_db_addrdataset(db, node, NULL, client->now, sigrdataset,
				 0, NULL);
	dns_db_detachnode(db, &node);
}

/*
 * Find the secure key that corresponds to rrsig.
 * Note: 'keyrdataset' maintains state between successive calls,
 * there may be multiple keys with the same keyid.
 * Return ISC_FALSE if we have exhausted all the possible keys.
 */
static isc_boolean_t
get_key(ns_client_t *client, dns_db_t *db, dns_rdata_rrsig_t *rrsig,
	dns_rdataset_t *keyrdataset, dst_key_t **keyp)
{
	isc_result_t result;
	dns_dbnode_t *node = NULL;
	isc_boolean_t secure = ISC_FALSE;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;

	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, NULL, NULL);

	if (!dns_rdataset_isassociated(keyrdataset)) {
		result = dns_db_findnodeext(db, &rrsig->signer, ISC_FALSE,
					    &cm, &ci, &node);
		if (result != ISC_R_SUCCESS)
			return (ISC_FALSE);

		result = dns_db_findrdataset(db, node, NULL,
					     dns_rdatatype_dnskey, 0,
					     client->now, keyrdataset, NULL);
		dns_db_detachnode(db, &node);
		if (result != ISC_R_SUCCESS)
			return (ISC_FALSE);

		if (keyrdataset->trust != dns_trust_secure)
			return (ISC_FALSE);

		result = dns_rdataset_first(keyrdataset);
	} else
		result = dns_rdataset_next(keyrdataset);

	for ( ; result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(keyrdataset)) {
		dns_rdata_t rdata = DNS_RDATA_INIT;
		isc_buffer_t b;

		dns_rdataset_current(keyrdataset, &rdata);
		isc_buffer_init(&b, rdata.data, rdata.length);
		isc_buffer_add(&b, rdata.length);
		result = dst_key_fromdns(&rrsig->signer, rdata.rdclass, &b,
					 client->mctx, keyp);
		if (result != ISC_R_SUCCESS)
			continue;
		if (rrsig->algorithm == (dns_secalg_t)dst_key_alg(*keyp) &&
		    rrsig->keyid == (dns_keytag_t)dst_key_id(*keyp) &&
		    dst_key_iszonekey(*keyp)) {
			secure = ISC_TRUE;
			break;
		}
		dst_key_free(keyp);
	}
	return (secure);
}

static isc_boolean_t
verify(dst_key_t *key, dns_name_t *name, dns_rdataset_t *rdataset,
       dns_rdata_t *rdata, ns_client_t *client)
{
	isc_result_t result;
	dns_fixedname_t fixed;
	isc_boolean_t ignore = ISC_FALSE;

	dns_fixedname_init(&fixed);

again:
	result = dns_dnssec_verify3(name, rdataset, key, ignore,
				    client->view->maxbits, client->mctx,
				    rdata, NULL);
	if (result == DNS_R_SIGEXPIRED && client->view->acceptexpired) {
		ignore = ISC_TRUE;
		goto again;
	}
	if (result == ISC_R_SUCCESS || result == DNS_R_FROMWILDCARD)
		return (ISC_TRUE);
	return (ISC_FALSE);
}

/*
 * Validate the rdataset if possible with available records.
 */
static isc_boolean_t
validate(ns_client_t *client, dns_db_t *db, dns_name_t *name,
	 dns_rdataset_t *rdataset, dns_rdataset_t *sigrdataset)
{
	isc_result_t result;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	dns_rdata_rrsig_t rrsig;
	dst_key_t *key = NULL;
	dns_rdataset_t keyrdataset;

	if (sigrdataset == NULL || !dns_rdataset_isassociated(sigrdataset))
		return (ISC_FALSE);

	for (result = dns_rdataset_first(sigrdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(sigrdataset)) {

		dns_rdata_reset(&rdata);
		dns_rdataset_current(sigrdataset, &rdata);
		result = dns_rdata_tostruct(&rdata, &rrsig, NULL);
		if (result != ISC_R_SUCCESS)
			return (ISC_FALSE);
		if (!dns_resolver_algorithm_supported(client->view->resolver,
						      name, rrsig.algorithm))
			continue;
		if (!dns_name_issubdomain(name, &rrsig.signer))
			continue;
		dns_rdataset_init(&keyrdataset);
		do {
			if (!get_key(client, db, &rrsig, &keyrdataset, &key))
				break;
			if (verify(key, name, rdataset, &rdata, client)) {
				dst_key_free(&key);
				dns_rdataset_disassociate(&keyrdataset);
				mark_secure(client, db, name, &rrsig,
					    rdataset, sigrdataset);
				return (ISC_TRUE);
			}
			dst_key_free(&key);
		} while (1);
		if (dns_rdataset_isassociated(&keyrdataset))
			dns_rdataset_disassociate(&keyrdataset);
	}
	return (ISC_FALSE);
}

static void
query_addbestns(ns_client_t *client) {
	dns_db_t *db, *zdb;
	dns_dbnode_t *node;
	dns_name_t *fname, *zfname;
	dns_rdataset_t *rdataset, *sigrdataset, *zrdataset, *zsigrdataset;
	isc_boolean_t is_zone, use_zone;
	isc_buffer_t *dbuf;
	isc_result_t result;
	dns_dbversion_t *version;
	dns_zone_t *zone;
	isc_buffer_t b;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;

	CTRACE(ISC_LOG_DEBUG(3), "query_addbestns");
	fname = NULL;
	zfname = NULL;
	rdataset = NULL;
	zrdataset = NULL;
	sigrdataset = NULL;
	zsigrdataset = NULL;
	node = NULL;
	db = NULL;
	zdb = NULL;
	version = NULL;
	zone = NULL;
	is_zone = ISC_FALSE;
	use_zone = ISC_FALSE;

	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, NULL, NULL);

	/*
	 * Find the right database.
	 */
	result = query_getdb(client, client->query.qname, dns_rdatatype_ns, 0,
			     &zone, &db, &version, &is_zone);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

#ifdef ORIGINAL_ISC_CODE
#else
	INFOBLOX_SET_VIEW(client->view);
#endif

 db_find:
	/*
	 * We'll need some resources...
	 */
	dbuf = query_getnamebuf(client);
	if (dbuf == NULL)
		goto cleanup;
	fname = query_newname(client, dbuf, &b);
	rdataset = query_newrdataset(client);
	if (fname == NULL || rdataset == NULL)
		goto cleanup;
	/*
	 * Get the RRSIGs if the client requested them or if we may
	 * need to validate answers from the cache.
	 */
	if (WANTDNSSEC(client) || !is_zone) {
		sigrdataset = query_newrdataset(client);
		if (sigrdataset == NULL)
			goto cleanup;
	}

	/*
	 * Now look for the zonecut.
	 */
	if (is_zone) {
		result = dns_db_findext(db, client->query.qname, version,
					dns_rdatatype_ns,
					client->query.dboptions,
					client->now, &node, fname,
					&cm, &ci, rdataset, sigrdataset);
		if (result != DNS_R_DELEGATION)
			goto cleanup;
		if (USECACHE(client)) {
			query_keepname(client, fname, dbuf);
			dns_db_detachnode(db, &node);
			SAVE(zdb, db);
			SAVE(zfname, fname);
			SAVE(zrdataset, rdataset);
			SAVE(zsigrdataset, sigrdataset);
			version = NULL;
			dns_db_attach(client->view->cachedb, &db);
			is_zone = ISC_FALSE;
			goto db_find;
		}
	} else {
		result = dns_db_findzonecut(db, client->query.qname,
					    client->query.dboptions,
					    client->now, &node, fname,
					    rdataset, sigrdataset);
		if (result == ISC_R_SUCCESS) {
			if (zfname != NULL &&
			    !dns_name_issubdomain(fname, zfname)) {
				/*
				 * We found a zonecut in the cache, but our
				 * zone delegation is better.
				 */
				use_zone = ISC_TRUE;
			}
		} else if (result == ISC_R_NOTFOUND && zfname != NULL) {
			/*
			 * We didn't find anything in the cache, but we
			 * have a zone delegation, so use it.
			 */
			use_zone = ISC_TRUE;
		} else
			goto cleanup;
	}

	if (use_zone) {
		query_releasename(client, &fname);
		/*
		 * We've already done query_keepname() on
		 * zfname, so we must set dbuf to NULL to
		 * prevent query_addrrset() from trying to
		 * call query_keepname() again.
		 */
		dbuf = NULL;
		query_putrdataset(client, &rdataset);
		if (sigrdataset != NULL)
			query_putrdataset(client, &sigrdataset);

		if (node != NULL)
			dns_db_detachnode(db, &node);
		dns_db_detach(&db);

		RESTORE(db, zdb);
		RESTORE(fname, zfname);
		RESTORE(rdataset, zrdataset);
		RESTORE(sigrdataset, zsigrdataset);
	}

	/*
	 * Attempt to validate RRsets that are pending or that are glue.
	 */
	if ((DNS_TRUST_PENDING(rdataset->trust) ||
	     (sigrdataset != NULL && DNS_TRUST_PENDING(sigrdataset->trust)))
	    && !validate(client, db, fname, rdataset, sigrdataset) &&
	    !PENDINGOK(client->query.dboptions))
		goto cleanup;

	if ((DNS_TRUST_GLUE(rdataset->trust) ||
	     (sigrdataset != NULL && DNS_TRUST_GLUE(sigrdataset->trust))) &&
	    !validate(client, db, fname, rdataset, sigrdataset) &&
	    SECURE(client) && WANTDNSSEC(client))
		goto cleanup;

	/*
	 * If the answer is secure only add NS records if they are secure
	 * when the client may be looking for AD in the response.
	 */
	if (SECURE(client) && (WANTDNSSEC(client) || WANTAD(client)) &&
	    ((rdataset->trust != dns_trust_secure) ||
	    (sigrdataset != NULL && sigrdataset->trust != dns_trust_secure)))
		goto cleanup;

	/*
	 * If the client doesn't want DNSSEC we can discard the sigrdataset
	 * now.
	 */
	if (!WANTDNSSEC(client))
		query_putrdataset(client, &sigrdataset);
	query_addrrset(client, &fname, &rdataset, &sigrdataset, dbuf,
		       DNS_SECTION_AUTHORITY);

 cleanup:
	if (rdataset != NULL)
		query_putrdataset(client, &rdataset);
	if (sigrdataset != NULL)
		query_putrdataset(client, &sigrdataset);
	if (fname != NULL)
		query_releasename(client, &fname);
	if (node != NULL)
		dns_db_detachnode(db, &node);
	if (db != NULL)
		dns_db_detach(&db);
	if (zone != NULL)
		dns_zone_detach(&zone);
	if (zdb != NULL) {
		query_putrdataset(client, &zrdataset);
		if (zsigrdataset != NULL)
			query_putrdataset(client, &zsigrdataset);
		if (zfname != NULL)
			query_releasename(client, &zfname);
		dns_db_detach(&zdb);
	}
}

static void
fixrdataset(ns_client_t *client, dns_rdataset_t **rdataset) {
	if (*rdataset == NULL)
		*rdataset = query_newrdataset(client);
	else  if (dns_rdataset_isassociated(*rdataset))
		dns_rdataset_disassociate(*rdataset);
}

static void
fixfname(ns_client_t *client, dns_name_t **fname, isc_buffer_t **dbuf,
	 isc_buffer_t *nbuf)
{
	if (*fname == NULL) {
		*dbuf = query_getnamebuf(client);
		if (*dbuf == NULL)
			return;
		*fname = query_newname(client, *dbuf, nbuf);
	}
}

static void
query_addds(ns_client_t *client, dns_db_t *db, dns_dbnode_t *node,
	    dns_dbversion_t *version, dns_name_t *name)
{
	dns_fixedname_t fixed;
	dns_name_t *fname = NULL;
	dns_name_t *rname;
	dns_rdataset_t *rdataset, *sigrdataset;
	isc_buffer_t *dbuf, b;
	isc_result_t result;
	unsigned int count;

	CTRACE(ISC_LOG_DEBUG(3), "query_addds");
	rname = NULL;
	rdataset = NULL;
	sigrdataset = NULL;

	/*
	 * We'll need some resources...
	 */
	rdataset = query_newrdataset(client);
	sigrdataset = query_newrdataset(client);
	if (rdataset == NULL || sigrdataset == NULL)
		goto cleanup;

	/*
	 * Look for the DS record, which may or may not be present.
	 */
	result = dns_db_findrdataset(db, node, version, dns_rdatatype_ds, 0,
				     client->now, rdataset, sigrdataset);
	/*
	 * If we didn't find it, look for an NSEC.
	 */
	if (result == ISC_R_NOTFOUND)
		result = dns_db_findrdataset(db, node, version,
					     dns_rdatatype_nsec, 0, client->now,
					     rdataset, sigrdataset);
	if (result != ISC_R_SUCCESS && result != ISC_R_NOTFOUND)
		goto addnsec3;
	if (!dns_rdataset_isassociated(rdataset) ||
	    !dns_rdataset_isassociated(sigrdataset))
		goto addnsec3;

	/*
	 * We've already added the NS record, so if the name's not there,
	 * we have other problems.  Use this name rather than calling
	 * query_addrrset().
	 */
	result = dns_message_firstname(client->message, DNS_SECTION_AUTHORITY);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	rname = NULL;
	dns_message_currentname(client->message, DNS_SECTION_AUTHORITY,
				&rname);
	result = dns_message_findtype(rname, dns_rdatatype_ns, 0, NULL);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	ISC_LIST_APPEND(rname->list, rdataset, link);
	ISC_LIST_APPEND(rname->list, sigrdataset, link);
	rdataset = NULL;
	sigrdataset = NULL;
	return;

   addnsec3:
	if (!dns_db_iszone(db))
		goto cleanup;
	/*
	 * Add the NSEC3 which proves the DS does not exist.
	 */
	dbuf = query_getnamebuf(client);
	if (dbuf == NULL)
		goto cleanup;
	fname = query_newname(client, dbuf, &b);
	dns_fixedname_init(&fixed);
	if (dns_rdataset_isassociated(rdataset))
		dns_rdataset_disassociate(rdataset);
	if (dns_rdataset_isassociated(sigrdataset))
		dns_rdataset_disassociate(sigrdataset);
	query_findclosestnsec3(name, db, version, client, rdataset,
			       sigrdataset, fname, ISC_TRUE,
			       dns_fixedname_name(&fixed));
	if (!dns_rdataset_isassociated(rdataset))
		goto cleanup;
	query_addrrset(client, &fname, &rdataset, &sigrdataset, dbuf,
		       DNS_SECTION_AUTHORITY);
	/*
	 * Did we find the closest provable encloser instead?
	 * If so add the nearest to the closest provable encloser.
	 */
	if (!dns_name_equal(name, dns_fixedname_name(&fixed))) {
		count = dns_name_countlabels(dns_fixedname_name(&fixed)) + 1;
		dns_name_getlabelsequence(name,
					  dns_name_countlabels(name) - count,
					  count, dns_fixedname_name(&fixed));
		fixfname(client, &fname, &dbuf, &b);
		fixrdataset(client, &rdataset);
		fixrdataset(client, &sigrdataset);
		if (fname == NULL || rdataset == NULL || sigrdataset == NULL)
				goto cleanup;
		query_findclosestnsec3(dns_fixedname_name(&fixed), db, version,
				       client, rdataset, sigrdataset, fname,
				       ISC_FALSE, NULL);
		if (!dns_rdataset_isassociated(rdataset))
			goto cleanup;
		query_addrrset(client, &fname, &rdataset, &sigrdataset, dbuf,
			       DNS_SECTION_AUTHORITY);
	}

 cleanup:
	if (rdataset != NULL)
		query_putrdataset(client, &rdataset);
	if (sigrdataset != NULL)
		query_putrdataset(client, &sigrdataset);
	if (fname != NULL)
		query_releasename(client, &fname);
}

static void
query_addwildcardproof(ns_client_t *client, dns_db_t *db,
		       dns_dbversion_t *version, dns_name_t *name,
#ifdef ORIGINAL_ISC_CODE
#else
		       dns_name_t *ofname,
		       dns_rdataset_t *ordataset,
		       dns_rdataset_t *osigrdataset,
#endif
		       isc_boolean_t ispositive, isc_boolean_t nodata)
{
	isc_buffer_t *dbuf, b;
	dns_name_t *fname;
	dns_rdataset_t *rdataset, *sigrdataset;
	dns_fixedname_t wfixed;
	dns_name_t *wname;
	dns_dbnode_t *node;
	unsigned int options;
	unsigned int olabels, nlabels, labels;
	isc_result_t result;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	dns_rdata_nsec_t nsec;
	isc_boolean_t have_wname;
	int order;
	dns_fixedname_t cfixed;
	dns_name_t *cname;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;

	CTRACE(ISC_LOG_DEBUG(3), "query_addwildcardproof");
	fname = NULL;
	rdataset = NULL;
	sigrdataset = NULL;
	node = NULL;

	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, NULL, NULL);

	/*
	 * Get the NOQNAME proof then if !ispositive
	 * get the NOWILDCARD proof.
	 *
	 * DNS_DBFIND_NOWILD finds the NSEC records that covers the
	 * name ignoring any wildcard.  From the owner and next names
	 * of this record you can compute which wildcard (if it exists)
	 * will match by finding the longest common suffix of the
	 * owner name and next names with the qname and prefixing that
	 * with the wildcard label.
	 *
	 * e.g.
	 *   Given:
	 *	example SOA
	 *	example NSEC b.example
	 *	b.example A
	 *	b.example NSEC a.d.example
	 *	a.d.example A
	 *	a.d.example NSEC g.f.example
	 *	g.f.example A
	 *	g.f.example NSEC z.i.example
	 *	z.i.example A
	 *	z.i.example NSEC example
	 *
	 *   QNAME:
	 *   a.example -> example NSEC b.example
	 *	owner common example
	 *	next common example
	 *	wild *.example
	 *   d.b.example -> b.example NSEC a.d.example
	 *	owner common b.example
	 *	next common example
	 *	wild *.b.example
	 *   a.f.example -> a.d.example NSEC g.f.example
	 *	owner common example
	 *	next common f.example
	 *	wild *.f.example
	 *  j.example -> z.i.example NSEC example
	 *	owner common example
	 *	next common example
	 *	wild *.example
	 */
	options = client->query.dboptions | DNS_DBFIND_NOWILD;
	dns_fixedname_init(&wfixed);
	wname = dns_fixedname_name(&wfixed);
 again:
	have_wname = ISC_FALSE;
	/*
	 * We'll need some resources...
	 */
#ifdef ORIGINAL_ISC_CODE
#else
	if (ofname == NULL || ordataset == NULL || osigrdataset == NULL ||
	    !dns_rdataset_isassociated(ordataset)) {
		ofname = NULL; // So that we can check just ofname==NULL below
#endif
	dbuf = query_getnamebuf(client);
	if (dbuf == NULL)
		goto cleanup;
	fname = query_newname(client, dbuf, &b);
	rdataset = query_newrdataset(client);
	sigrdataset = query_newrdataset(client);
#ifdef ORIGINAL_ISC_CODE
#else
	} else {
		// If our caller supplied an fname, rdataset, and sigrdataset,
		// we can use those instead of looking up the exact same data
		// in the DB.
		dbuf = NULL;
		fname = ofname;
		rdataset = ordataset;
		sigrdataset = osigrdataset;
		// Note: If ofname etc. aren't NULL, then we've already
		//       called query_addrrset(), so the calls to
		//       query_addrrset() below are effectively no-ops.
	}
#endif
	if (fname == NULL || rdataset == NULL || sigrdataset == NULL)
		goto cleanup;

#ifdef ORIGINAL_ISC_CODE
#else
	if (ofname == NULL) {
#endif
	result = dns_db_findext(db, name, version, dns_rdatatype_nsec,
				options, 0, &node, fname, &cm, &ci,
				rdataset, sigrdataset);
	if (node != NULL)
		dns_db_detachnode(db, &node);
#ifdef ORIGINAL_ISC_CODE
#else
	} else {
		result = DNS_R_NXDOMAIN;
	}
#endif

	if (!dns_rdataset_isassociated(rdataset)) {
		/*
		 * No NSEC proof available, return NSEC3 proofs instead.
		 */
		dns_fixedname_init(&cfixed);
		cname = dns_fixedname_name(&cfixed);
		/*
		 * Find the closest encloser.
		 */
		dns_name_copy(name, cname, NULL);
		while (result == DNS_R_NXDOMAIN) {
			labels = dns_name_countlabels(cname) - 1;
			/*
			 * Sanity check.
			 */
			if (labels == 0U)
				goto cleanup;
			dns_name_split(cname, labels, NULL, cname);
			result = dns_db_findext(db, cname, version,
						dns_rdatatype_nsec,
						options, 0, NULL, fname,
						&cm, &ci, NULL, NULL);
		}
		/*
		 * Add closest (provable) encloser NSEC3.
		 */
		query_findclosestnsec3(cname, db, version, client, rdataset,
				       sigrdataset, fname, ISC_TRUE, cname);
		if (!dns_rdataset_isassociated(rdataset))
			goto cleanup;
		if (!ispositive)
			query_addrrset(client, &fname, &rdataset, &sigrdataset,
				       dbuf, DNS_SECTION_AUTHORITY);

		/*
		 * Replace resources which were consumed by query_addrrset.
		 */
		if (fname == NULL) {
			dbuf = query_getnamebuf(client);
			if (dbuf == NULL)
				goto cleanup;
			fname = query_newname(client, dbuf, &b);
		}

		if (rdataset == NULL)
			rdataset = query_newrdataset(client);
		else if (dns_rdataset_isassociated(rdataset))
			dns_rdataset_disassociate(rdataset);

		if (sigrdataset == NULL)
			sigrdataset = query_newrdataset(client);
		else if (dns_rdataset_isassociated(sigrdataset))
			dns_rdataset_disassociate(sigrdataset);

		if (fname == NULL || rdataset == NULL || sigrdataset == NULL)
			goto cleanup;
		/*
		 * Add no qname proof.
		 */
		labels = dns_name_countlabels(cname) + 1;
		if (dns_name_countlabels(name) == labels)
			dns_name_copy(name, wname, NULL);
		else
			dns_name_split(name, labels, NULL, wname);

		query_findclosestnsec3(wname, db, version, client, rdataset,
				       sigrdataset, fname, ISC_FALSE, NULL);
		if (!dns_rdataset_isassociated(rdataset))
			goto cleanup;
		query_addrrset(client, &fname, &rdataset, &sigrdataset,
			       dbuf, DNS_SECTION_AUTHORITY);

		if (ispositive)
			goto cleanup;

		/*
		 * Replace resources which were consumed by query_addrrset.
		 */
		if (fname == NULL) {
			dbuf = query_getnamebuf(client);
			if (dbuf == NULL)
				goto cleanup;
			fname = query_newname(client, dbuf, &b);
		}

		if (rdataset == NULL)
			rdataset = query_newrdataset(client);
		else if (dns_rdataset_isassociated(rdataset))
			dns_rdataset_disassociate(rdataset);

		if (sigrdataset == NULL)
			sigrdataset = query_newrdataset(client);
		else if (dns_rdataset_isassociated(sigrdataset))
			dns_rdataset_disassociate(sigrdataset);

		if (fname == NULL || rdataset == NULL || sigrdataset == NULL)
			goto cleanup;
		/*
		 * Add the no wildcard proof.
		 */
		result = dns_name_concatenate(dns_wildcardname,
					      cname, wname, NULL);
		if (result != ISC_R_SUCCESS)
			goto cleanup;

		query_findclosestnsec3(wname, db, version, client, rdataset,
				       sigrdataset, fname, nodata, NULL);
		if (!dns_rdataset_isassociated(rdataset))
			goto cleanup;
		query_addrrset(client, &fname, &rdataset, &sigrdataset,
			       dbuf, DNS_SECTION_AUTHORITY);

		goto cleanup;
	} else if (result == DNS_R_NXDOMAIN) {
		if (!ispositive)
			result = dns_rdataset_first(rdataset);
		if (result == ISC_R_SUCCESS) {
			dns_rdataset_current(rdataset, &rdata);
			result = dns_rdata_tostruct(&rdata, &nsec, NULL);
		}
		if (result == ISC_R_SUCCESS) {
			(void)dns_name_fullcompare(name, fname, &order,
						   &olabels);
			(void)dns_name_fullcompare(name, &nsec.next, &order,
						   &nlabels);
			/*
			 * Check for a pathological condition created when
			 * serving some malformed signed zones and bail out.
			 */
			if (dns_name_countlabels(name) == nlabels)
				goto cleanup;

			if (olabels > nlabels)
				dns_name_split(name, olabels, NULL, wname);
			else
				dns_name_split(name, nlabels, NULL, wname);
			result = dns_name_concatenate(dns_wildcardname,
						      wname, wname, NULL);
			if (result == ISC_R_SUCCESS)
				have_wname = ISC_TRUE;
			dns_rdata_freestruct(&nsec);
		}
		query_addrrset(client, &fname, &rdataset, &sigrdataset,
			       dbuf, DNS_SECTION_AUTHORITY);
	}
#ifdef ORIGINAL_ISC_CODE
#else
	if (ofname) {
		// Don't use ofname/ordataset/osigrdataset after jumping to 'again'
		ofname = NULL;
		ordataset = NULL;
		osigrdataset = NULL;
		// Reset in preparation for the wildcard lookup
		fname = NULL;
		rdataset = NULL;
		sigrdataset = NULL;
	}
#endif
	if (rdataset != NULL)
		query_putrdataset(client, &rdataset);
	if (sigrdataset != NULL)
		query_putrdataset(client, &sigrdataset);
	if (fname != NULL)
		query_releasename(client, &fname);
	if (have_wname) {
		ispositive = ISC_TRUE;	/* prevent loop */
		if (!dns_name_equal(name, wname)) {
			name = wname;
			goto again;
		}
	}
 cleanup:
#ifdef ORIGINAL_ISC_CODE
#else
	if (ofname) {
		// Avoid cleanup of data passed in from our caller
		fname = NULL;
		rdataset = NULL;
		sigrdataset = NULL;
	}
#endif
	if (rdataset != NULL)
		query_putrdataset(client, &rdataset);
	if (sigrdataset != NULL)
		query_putrdataset(client, &sigrdataset);
	if (fname != NULL)
		query_releasename(client, &fname);
}

static void
query_addnxrrsetnsec(ns_client_t *client, dns_db_t *db,
		     dns_dbversion_t *version, dns_name_t **namep,
		     dns_rdataset_t **rdatasetp, dns_rdataset_t **sigrdatasetp)
{
	dns_name_t *name;
	dns_rdataset_t *sigrdataset;
	dns_rdata_t sigrdata;
	dns_rdata_rrsig_t sig;
	unsigned int labels;
	isc_buffer_t *dbuf, b;
	dns_name_t *fname;
	isc_result_t result;

	name = *namep;
	if ((name->attributes & DNS_NAMEATTR_WILDCARD) == 0) {
		query_addrrset(client, namep, rdatasetp, sigrdatasetp,
			       NULL, DNS_SECTION_AUTHORITY);
		return;
	}

	if (sigrdatasetp == NULL)
		return;

	sigrdataset = *sigrdatasetp;
	if (sigrdataset == NULL || !dns_rdataset_isassociated(sigrdataset))
		return;
	result = dns_rdataset_first(sigrdataset);
	if (result != ISC_R_SUCCESS)
		return;
	dns_rdata_init(&sigrdata);
	dns_rdataset_current(sigrdataset, &sigrdata);
	result = dns_rdata_tostruct(&sigrdata, &sig, NULL);
	if (result != ISC_R_SUCCESS)
		return;

	labels = dns_name_countlabels(name);
	if ((unsigned int)sig.labels + 1 >= labels)
		return;

	/* XXX */
	query_addwildcardproof(client, db, version, client->query.qname,
#ifdef ORIGINAL_ISC_CODE
#else
			       NULL, NULL, NULL,
#endif
			       ISC_TRUE, ISC_FALSE);

	/*
	 * We'll need some resources...
	 */
	dbuf = query_getnamebuf(client);
	if (dbuf == NULL)
		return;
	fname = query_newname(client, dbuf, &b);
	if (fname == NULL)
		return;
	dns_name_split(name, sig.labels + 1, NULL, fname);
	/* This will succeed, since we've stripped labels. */
	RUNTIME_CHECK(dns_name_concatenate(dns_wildcardname, fname, fname,
					   NULL) == ISC_R_SUCCESS);
	query_addrrset(client, &fname, rdatasetp, sigrdatasetp,
		       dbuf, DNS_SECTION_AUTHORITY);
}

static void
free_devent(ns_client_t *client, isc_event_t **eventp,
	    dns_fetchevent_t **deventp)
{
	dns_fetchevent_t *devent = *deventp;

	REQUIRE((void*)(*eventp) == (void *)(*deventp));

	if (devent->fetch != NULL)
		dns_resolver_destroyfetch(&devent->fetch);
	if (devent->node != NULL)
		dns_db_detachnode(devent->db, &devent->node);
	if (devent->db != NULL)
		dns_db_detach(&devent->db);
	if (devent->rdataset != NULL)
		query_putrdataset(client, &devent->rdataset);
	if (devent->sigrdataset != NULL)
		query_putrdataset(client, &devent->sigrdataset);
	/*
	 * If the two pointers are the same then leave the setting of
	 * (*deventp) to NULL to isc_event_free.
	 */
	if ((void *)eventp != (void *)deventp)
		(*deventp) = NULL;
	isc_event_free(eventp);
}

static void
query_resume(isc_task_t *task, isc_event_t *event) {
	dns_fetchevent_t *devent = (dns_fetchevent_t *)event;
	dns_fetch_t *fetch = NULL;
	ns_client_t *client;
	isc_boolean_t fetch_canceled, client_shuttingdown;
	isc_result_t result;
	isc_logcategory_t *logcategory = NS_LOGCATEGORY_QUERY_ERRORS;
	int errorloglevel;

	/*
	 * Resume a query after recursion.
	 */

#ifdef ORIGINAL_ISC_CODE
#else
	// Note that ib_query_resume_proxy() reuses much of the code
	// from this function, so any changes here will have to be
	// reproduced there.
#endif

	UNUSED(task);

	REQUIRE(event->ev_type == DNS_EVENT_FETCHDONE);
	client = devent->ev_arg;
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(task == client->task);
	REQUIRE(RECURSING(client));

	LOCK(&client->query.fetchlock);
	if (client->query.fetch != NULL) {
		/*
		 * This is the fetch we've been waiting for.
		 */
		INSIST(devent->fetch == client->query.fetch);
		client->query.fetch = NULL;
		fetch_canceled = ISC_FALSE;
		/*
		 * Update client->now.
		 */
		isc_stdtime_get(&client->now);
	} else {
		/*
		 * This is a fetch completion event for a canceled fetch.
		 * Clean up and don't resume the find.
		 */
		fetch_canceled = ISC_TRUE;
	}
	UNLOCK(&client->query.fetchlock);
	INSIST(client->query.fetch == NULL);

	client->query.attributes &= ~NS_QUERYATTR_RECURSING;
	SAVE(fetch, devent->fetch);

	/*
	 * If this client is shutting down, or this transaction
	 * has timed out, do not resume the find.
	 */
	client_shuttingdown = ns_client_shuttingdown(client);
	if (fetch_canceled || client_shuttingdown) {
		free_devent(client, &event, &devent);
		if (fetch_canceled) {
			CTRACE(ISC_LOG_ERROR, "fetch cancelled");
			query_error(client, DNS_R_SERVFAIL, __LINE__);
		} else
			query_next(client, ISC_R_CANCELED);
		/*
		 * This may destroy the client.
		 */
		ns_client_detach(&client);
	} else {
		result = query_find(client, devent, 0);
		if (result != ISC_R_SUCCESS) {
			if (result == DNS_R_SERVFAIL)
				errorloglevel = ISC_LOG_DEBUG(2);
			else
				errorloglevel = ISC_LOG_DEBUG(4);
			if (isc_log_wouldlog(ns_g_lctx, errorloglevel)) {
				dns_resolver_logfetch(fetch, ns_g_lctx,
						      logcategory,
						      NS_LOGMODULE_QUERY,
						      errorloglevel, ISC_FALSE);
			}
		}
	}

	dns_resolver_destroyfetch(&fetch);
}

#ifdef ORIGINAL_ISC_CODE
#else
static void
ib_query_resume_proxy(isc_task_t *task, isc_event_t *event) {
	dns_fetchevent_t *devent = (dns_fetchevent_t *)event;
	ns_client_t *client;
	isc_boolean_t client_shuttingdown;
	isc_result_t result;

	// Resume a proxy query after recursion.  Note that most of this
	// is copied from query_resume(), and any changes there will have
	// to be replicated here.

	REQUIRE(event->ev_type == DNS_EVENT_FETCHDONE);
	client = devent->ev_arg;
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(task == client->task);
	REQUIRE(client->query.fetch == NULL);

	isc_stdtime_get(&client->now);

	/*
	 * If this client is shutting down, or this transaction
	 * has timed out, do not resume the find.
	 */
	client_shuttingdown = ns_client_shuttingdown(client);
	INSIST(client->message != NULL);
	if (client_shuttingdown || !PROXYASYNC(client) ||
	    client->message->id != devent->id) {
		isc_event_free(&event);
		query_next(client, ISC_R_CANCELED);
		/*
		 * This may destroy the client.
		 */
		ns_client_detach(&client);
	} else
		result = query_find(client, devent, 0);
}
#endif

static void
prefetch_done(isc_task_t *task, isc_event_t *event) {
	dns_fetchevent_t *devent = (dns_fetchevent_t *)event;
	ns_client_t *client;

	UNUSED(task);

	REQUIRE(event->ev_type == DNS_EVENT_FETCHDONE);
	client = devent->ev_arg;
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(task == client->task);

	LOCK(&client->query.fetchlock);
	if (client->query.prefetch != NULL) {
		INSIST(devent->fetch == client->query.prefetch);
		client->query.prefetch = NULL;
	}
	UNLOCK(&client->query.fetchlock);
	free_devent(client, &event, &devent);
#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * If pending prefetch present due to CNAME/DNAME chain
	 * then refresh the records using the same client. 
	 * Detach client once all records are refreshed.
	 */ 
	if (!ISC_LIST_EMPTY(client->query.prefetchinfo)) {
		ib_trigger_prefetch_query(client);
	} else {
#endif
	ns_client_detach(&client);
#ifdef ORIGINAL_ISC_CODE
#else
	}
#endif
}

#ifdef ORIGINAL_ISC_CODE
#else
static void
ib_trigger_prefetch_query(ns_client_t *client)
{
	isc_result_t result;
	dns_rdataset_t *tmprdataset;
	ib_prefetchinfo_t *prefetchinfo;
	dns_ecs_t prefetch_ecs;
	dns_ecs_t *ecs;
	unsigned int options;

	prefetchinfo = ISC_LIST_HEAD(client->query.prefetchinfo);
	ISC_LIST_UNLINK(client->query.prefetchinfo, prefetchinfo, link);

	if (client->view != NULL) {
		/*
		 * Below code is redundant and is also present under query_prefetch(). 
		 * Please ensure any changes here shall be under query_prefetch() 
		 * as applicable.
		 */
		if (client->prefetch_ecs.source > 0) {
			/*
			 * Instead of using the cached entry's address
			 * prefix-length, use the client's source prefix-length
			 * when prefetching.
			 */
			prefetch_ecs = client->prefetch_ecs;
			prefetch_ecs.scope = 0;
			ecs = &prefetch_ecs;
		} else {
			ecs = NULL;
		}

		tmprdataset = query_newrdataset(client);
		if (tmprdataset == NULL)
			goto cleanup;

		options = client->query.fetchoptions | DNS_FETCHOPT_PREFETCH;
		if (TCP_CLIENT(client)) {
			options |= DNS_FETCHOPT_TCPCLIENT;
		}

		result = dns_resolver_createfetch4(client->view->resolver,
						   &prefetchinfo->ib_prefetchname,
						   prefetchinfo->ib_prefetchtype, NULL,
						   NULL, ecs, &client->peeraddr,
						   client->message->id, options,
						   0, NULL, client->task,
						   prefetch_done, client,
						   tmprdataset, NULL,
						   &client->query.prefetch);
		if (result != ISC_R_SUCCESS) {
			query_putrdataset(client, &tmprdataset);
			goto cleanup;
		}

		dns_name_free(&prefetchinfo->ib_prefetchname, client->mctx);
		isc_mem_put(client->mctx, prefetchinfo, sizeof(ib_prefetchinfo_t));
		return;
	}

cleanup:
	dns_name_free(&prefetchinfo->ib_prefetchname, client->mctx);
	isc_mem_put(client->mctx, prefetchinfo, sizeof(ib_prefetchinfo_t));
	/*
	 * In case of error, cleanup the prefetch list and detach the 
	 * client
	 */ 
	for (prefetchinfo = ISC_LIST_HEAD(client->query.prefetchinfo);
	     prefetchinfo != NULL;
	     prefetchinfo = ISC_LIST_HEAD(client->query.prefetchinfo))
	{
		ISC_LIST_UNLINK(client->query.prefetchinfo, prefetchinfo, link);
		dns_name_free(&prefetchinfo->ib_prefetchname, client->mctx);
		isc_mem_put(client->mctx, prefetchinfo, sizeof(ib_prefetchinfo_t));
	}
	ns_client_detach(&client);
	
	return;
}

/* Check if query prefetch need be triggered for IBALIAS record.
 * To avoid triggering prefetch each time whenever there is a query to IBALIAS
 * record, we need determine what prefetch_trigger value is used. Since the
 * upper limit value of prefetch_trigger is 10 (see configure_view() in
 * server.c), if input rdataset_ttl is larger than it, no prefetch is triggered.
 * In the case prefetch_trigger is set to 0, we will force a prefetch for
 * IBALIAS record using the upper limit value of prefetch_trigger, otherwise
 * the configured prefetch_trigger value is used.
 */
static inline isc_boolean_t
is_ibalias_trigger_prefech(const ns_client_t *client, dns_ttl_t rdataset_ttl) {
	if (!IBALIASINITIATED(client) || rdataset_ttl > 10) {
		return (ISC_FALSE);
	}
	if (client->view->prefetch_trigger &&
	    rdataset_ttl > client->view->prefetch_trigger)
		return (ISC_FALSE);
	return (ISC_TRUE);
}
#endif
static void
query_prefetch(ns_client_t *client, dns_name_t *qname,
	       dns_rdataset_t *rdataset)
{
	isc_result_t result;
	dns_rdataset_t *tmprdataset;
#ifdef ORIGINAL_ISC_CODE
#else
	ib_prefetchinfo_t *tmpprefetchinfo;
#endif
	ns_client_t *dummy = NULL;
	unsigned int options;
	dns_ecs_t prefetch_ecs;
	dns_ecs_t *ecs;

#ifdef ORIGINAL_ISC_CODE
	if (client->query.prefetch != NULL ||
	    client->view->prefetch_trigger == 0U ||
	    rdataset->ttl > client->view->prefetch_trigger ||
	    (rdataset->attributes & DNS_RDATASETATTR_PREFETCH) == 0)
		return;
#else
	if ((client->view->prefetch_trigger == 0U ||
	     rdataset->ttl > client->view->prefetch_trigger ||
	     (rdataset->attributes & DNS_RDATASETATTR_PREFETCH) == 0) &&
	     !(rdataset->attributes & DNS_RDATASETATTR_EXPIRED_HEADER) &&
	     !is_ibalias_trigger_prefech(client, rdataset->ttl)) {
		return;
	} else if (client->query.prefetch != NULL) {
		if ((rdataset->attributes & DNS_RDATASETATTR_EXPIRED_HEADER) == 0)
			return;
		/*
		 * If prefetch is already triggered then gather names/types of
		 * stale records in a list refresh them one by one during 
		 * prefetch_done
		 */
		tmpprefetchinfo = isc_mem_get(client->mctx, sizeof(ib_prefetchinfo_t));
		if (tmpprefetchinfo == NULL)
			return;

		dns_name_init(&tmpprefetchinfo->ib_prefetchname, NULL);
		result = dns_name_dup(qname, client->mctx, &tmpprefetchinfo->ib_prefetchname);
		if (result != ISC_R_SUCCESS) {
			isc_mem_put(client->mctx, tmpprefetchinfo, sizeof(ib_prefetchinfo_t));
			return;
		}

		tmpprefetchinfo->ib_prefetchtype = rdataset->type;
		ISC_LIST_APPEND(client->query.prefetchinfo, tmpprefetchinfo, link);
		dns_rdataset_clearprefetch(rdataset);
		return;
	}
#endif

#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * New method ib_trigger_prefetch_query() has been added to trigger
	 * prefetch for CNAME/DNAME chain. prefetch_ecs and resolver fetch context
	 * creation code below is redundant and is also under ib_trigger_prefetch_query(). 
	 * Please ensure that any changes here are also done in ib_trigger_prefetch_query() 
	 * as applicable.
	 */
#endif
	if (client->prefetch_ecs.source > 0) {
		/*
		 * Instead of using the cached entry's address
		 * prefix-length, use the client's source prefix-length
		 * when prefetching.
		 */
		prefetch_ecs = client->prefetch_ecs;
		prefetch_ecs.scope = 0;
		ecs = &prefetch_ecs;
	} else {
		ecs = NULL;
	}

	if (client->recursionquota == NULL) {
		result = isc_quota_attach(&ns_g_server->recursionquota,
					  &client->recursionquota);
		if (result == ISC_R_SUCCESS && !client->mortal &&
		    !TCP_CLIENT(client))
		{
			result = ns_client_replace(client);
		}
		if (result != ISC_R_SUCCESS) {
#ifdef ORIGINAL_ISC_CODE
#else
			isc_quota_detach(&client->recursionquota);
#endif
			return;
		}
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_recursclients);
	}

	tmprdataset = query_newrdataset(client);
	if (tmprdataset == NULL)
		return;

	options = client->query.fetchoptions | DNS_FETCHOPT_PREFETCH;
	if (TCP_CLIENT(client)) {
		options |= DNS_FETCHOPT_TCPCLIENT;
	}
#ifdef ORIGINAL_ISC_CODE
#else
	options |= IB_DNS_FETCHOPT_SKIP_EDNS_OPTS;

	if (isc_log_wouldlog(ns_g_lctx, ISC_LOG_DEBUG(9))) {
		char qbuf[DNS_NAME_FORMATSIZE];
		char tbuf[DNS_RDATATYPE_FORMATSIZE];
		dns_name_format(qname,
				qbuf, sizeof(qbuf));
		dns_rdatatype_format(rdataset->type,
				     tbuf, sizeof(tbuf));
		isc_log_write(ns_g_lctx,
			      NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, ISC_LOG_DEBUG(9),
			      "prefetching (%s/%s)",
			      qbuf, tbuf);
	}
#endif

	ns_client_attach(client, &dummy);

	result = dns_resolver_createfetch4(client->view->resolver,
					   qname, rdataset->type, NULL,
					   NULL, ecs, &client->peeraddr,
					   client->message->id, options,
					   0, NULL, client->task,
					   prefetch_done, client,
					   tmprdataset, NULL,
					   &client->query.prefetch);
	if (result != ISC_R_SUCCESS) {
		query_putrdataset(client, &tmprdataset);
		ns_client_detach(&dummy);
	}
	dns_rdataset_clearprefetch(rdataset);
}

static isc_result_t
query_recurse(ns_client_t *client, dns_rdatatype_t qtype, dns_name_t *qname,
	      dns_name_t *qdomain, dns_rdataset_t *nameservers,
	      isc_boolean_t resuming)
{
	isc_result_t result;
	dns_rdataset_t *rdataset, *sigrdataset;
	dns_ecs_t *ecs = NULL;
	unsigned int options;

	if (!resuming)
		ns_query_incstats(client, dns_nsstatscounter_recursion);
#ifdef ORIGINAL_ISC_CODE
#else
	infoblox_increment_cache(1, ISC_FALSE, client);
#endif

	/*
	 * We are about to recurse, which means that this client will
	 * be unavailable for serving new requests for an indeterminate
	 * amount of time.  If this client is currently responsible
	 * for handling incoming queries, set up a new client
	 * object to handle them while we are waiting for a
	 * response.  There is no need to replace TCP clients
	 * because those have already been replaced when the
	 * connection was accepted (if allowed by the TCP quota).
	 */
	if (client->recursionquota == NULL) {
		result = isc_quota_attach(&ns_g_server->recursionquota,
					  &client->recursionquota);

		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_recursclients);

		if  (result == ISC_R_SOFTQUOTA) {
			static isc_stdtime_t last = 0;
			isc_stdtime_t now;
			isc_stdtime_get(&now);
			if (now != last) {
				last = now;
				ns_client_log(client, NS_LOGCATEGORY_CLIENT,
					      NS_LOGMODULE_QUERY,
					      ISC_LOG_WARNING,
					      "recursive-clients soft limit "
					      "exceeded (%d/%d/%d), "
					      "aborting oldest query",
					      client->recursionquota->used,
					      client->recursionquota->soft,
					      client->recursionquota->max);
			}
			ns_client_killoldestquery(client);
			result = ISC_R_SUCCESS;
		} else if (result == ISC_R_QUOTA) {
			static isc_stdtime_t last = 0;
			isc_stdtime_t now;
			isc_stdtime_get(&now);
			if (now != last) {
				last = now;
				ns_client_log(client, NS_LOGCATEGORY_CLIENT,
					      NS_LOGMODULE_QUERY,
					      ISC_LOG_WARNING,
					      "no more recursive clients "
					      "(%d/%d/%d): %s",
					      ns_g_server->recursionquota.used,
					      ns_g_server->recursionquota.soft,
					      ns_g_server->recursionquota.max,
					      isc_result_totext(result));
#ifdef ORIGINAL_ISC_CODE
#else
				/* if we are here, there is no need to detach quota or decrement
				 * as when isc_quota_attach returns DNS_R_QUOTA, it does not
				 * do the attach. We still need to decrement, as we do the
				 * increment without regard to the result of the isc_quota_attach.
				 */
				isc_stats_decrement(ns_g_server->nsstats,
				    dns_nsstatscounter_recursclients);
#endif
			}
			ns_client_killoldestquery(client);
		}
		if (result == ISC_R_SUCCESS && !client->mortal &&
		    !TCP_CLIENT(client))
		{
			result = ns_client_replace(client);
			if (result != ISC_R_SUCCESS) {
				ns_client_log(client, NS_LOGCATEGORY_CLIENT,
					      NS_LOGMODULE_QUERY,
					      ISC_LOG_WARNING,
					      "ns_client_replace() failed: %s",
					      isc_result_totext(result));
				isc_quota_detach(&client->recursionquota);
				isc_stats_decrement(ns_g_server->nsstats,
				    dns_nsstatscounter_recursclients);
			}
		}
#ifdef ORIGINAL_ISC_CODE
#else
		isc_result_t snmp_result = ISC_R_SUCCESS;
		snmp_result = infoblox_quota_test_trap(&ns_g_server->recursionquota);
		if (snmp_result == ISC_R_QUOTATRIGGER) {
			infoblox_send_snmp_trap_recursive_clients(FALSE,
					(ns_g_server->recursionquota.trigger *
					 ns_g_server->recursionquota.max)/100,
					(ns_g_server->recursionquota.reset *
					 ns_g_server->recursionquota.max)/100,
					ns_g_server->recursionquota.used);
		} else if (snmp_result == ISC_R_QUOTARESET) {
			infoblox_send_snmp_trap_recursive_clients(TRUE,
					(ns_g_server->recursionquota.trigger *
					 ns_g_server->recursionquota.max)/100,
					(ns_g_server->recursionquota.reset *
					 ns_g_server->recursionquota.max)/100,
					ns_g_server->recursionquota.used);
		}
#endif
		if (result != ISC_R_SUCCESS)
			return (result);
		ns_client_recursing(client);
	}
#ifdef ORIGINAL_ISC_CODE
#else
	// If we're recursing to query for a proxy address, we exit here,
	// skipping the regular recursive query setup in favor of our
	// own setup.
	if (PROXYASYNC(client))
		return (ISC_R_SUCCESS);
#endif

	/*
	 * Invoke the resolver.
	 */
	REQUIRE(nameservers == NULL || nameservers->type == dns_rdatatype_ns);
	REQUIRE(client->query.fetch == NULL);

	rdataset = query_newrdataset(client);
	if (rdataset == NULL)
		return (ISC_R_NOMEMORY);
	if (WANTDNSSEC(client)) {
		sigrdataset = query_newrdataset(client);
		if (sigrdataset == NULL) {
			query_putrdataset(client, &rdataset);
			return (ISC_R_NOMEMORY);
		}
	} else
		sigrdataset = NULL;

	if (client->query.timerset == ISC_FALSE)
		ns_client_settimeout(client, 60);

	options = client->query.fetchoptions;
	if (TCP_CLIENT(client)) {
		options |= DNS_FETCHOPT_TCPCLIENT;
	}

	if (ECS_FORWARD(client))
		ecs = &client->ecs;

#ifdef ORIGINAL_ISC_CODE
	result = dns_resolver_createfetch4(client->view->resolver,
					   qname, qtype, qdomain, nameservers,
					   ecs, &client->peeraddr,
					   client->message->id,
					   options, 0, NULL,
					   client->task, query_resume, client,
					   rdataset, sigrdataset,
					   &client->query.fetch);
#else
	infoblox_addropts_t *addropts =
		client->view->infoblox_addr_opt_carryover ?
		&client->infoblox_addropts : NULL;
	result = ib_dns_resolver_createfetch5(client->view->resolver,
					      qname, qtype, qdomain,
					      nameservers,
					      ecs, &client->peeraddr,
					      client->message->id,
					      options, 0, NULL,
					      client->task, query_resume,
					      client,
					      rdataset, sigrdataset,
					      addropts,
					      &client->query.fetch);
#endif

	if (result == ISC_R_SUCCESS) {
		/*
		 * Record that we're waiting for an event.  A client which
		 * is shutting down will not be destroyed until all the
		 * events have been received.
		 */
	} else {
		query_putrdataset(client, &rdataset);
		if (sigrdataset != NULL)
			query_putrdataset(client, &sigrdataset);
	}

	return (result);
}

static inline void
rpz_clean(dns_zone_t **zonep, dns_db_t **dbp, dns_dbnode_t **nodep,
	  dns_rdataset_t **rdatasetp)
{
	if (nodep != NULL && *nodep != NULL) {
		REQUIRE(dbp != NULL && *dbp != NULL);
		dns_db_detachnode(*dbp, nodep);
	}
	if (dbp != NULL && *dbp != NULL)
		dns_db_detach(dbp);
	if (zonep != NULL && *zonep != NULL)
		dns_zone_detach(zonep);
	if (rdatasetp != NULL && *rdatasetp != NULL &&
	    dns_rdataset_isassociated(*rdatasetp))
		dns_rdataset_disassociate(*rdatasetp);
}

static inline void
rpz_match_clear(dns_rpz_st_t *st) {
	rpz_clean(&st->m.zone, &st->m.db, &st->m.node, &st->m.rdataset);
	st->m.version = NULL;
}

static inline isc_result_t
rpz_ready(ns_client_t *client, dns_rdataset_t **rdatasetp) {
	REQUIRE(rdatasetp != NULL);

	CTRACE(ISC_LOG_DEBUG(3), "rpz_ready");

	if (*rdatasetp == NULL) {
		*rdatasetp = query_newrdataset(client);
		if (*rdatasetp == NULL) {
			CTRACE(ISC_LOG_ERROR,
			       "rpz_ready: query_newrdataset failed");
			return (DNS_R_SERVFAIL);
		}
	} else if (dns_rdataset_isassociated(*rdatasetp)) {
		dns_rdataset_disassociate(*rdatasetp);
	}
	return (ISC_R_SUCCESS);
}

static void
rpz_st_clear(ns_client_t *client) {
	dns_rpz_st_t *st = client->query.rpz_st;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_st_clear");

	if (st->m.rdataset != NULL)
		query_putrdataset(client, &st->m.rdataset);
	rpz_match_clear(st);

	rpz_clean(NULL, &st->r.db, NULL, NULL);
	if (st->r.ns_rdataset != NULL)
		query_putrdataset(client, &st->r.ns_rdataset);
	if (st->r.r_rdataset != NULL)
		query_putrdataset(client, &st->r.r_rdataset);

	rpz_clean(&st->q.zone, &st->q.db, &st->q.node, NULL);
	if (st->q.rdataset != NULL)
		query_putrdataset(client, &st->q.rdataset);
	if (st->q.sigrdataset != NULL)
		query_putrdataset(client, &st->q.sigrdataset);
	st->state = 0;
	st->m.type = DNS_RPZ_TYPE_BAD;
	st->m.policy = DNS_RPZ_POLICY_MISS;
}

static dns_rpz_zbits_t
rpz_get_zbits(ns_client_t *client,
	      dns_rdatatype_t ip_type, dns_rpz_type_t rpz_type)
{
	dns_rpz_st_t *st;
	dns_rpz_zbits_t zbits;

	REQUIRE(client != NULL);
	REQUIRE(client->query.rpz_st != NULL);

	st = client->query.rpz_st;

	switch (rpz_type) {
	case DNS_RPZ_TYPE_CLIENT_IP:
		zbits = st->have.client_ip;
		break;
	case DNS_RPZ_TYPE_QNAME:
		zbits = st->have.qname;
		break;
	case DNS_RPZ_TYPE_IP:
		if (ip_type == dns_rdatatype_a) {
			zbits = st->have.ipv4;
		} else if (ip_type == dns_rdatatype_aaaa) {
			zbits = st->have.ipv6;
		} else {
			zbits = st->have.ip;
		}
		break;
	case DNS_RPZ_TYPE_NSDNAME:
		zbits = st->have.nsdname;
		break;
	case DNS_RPZ_TYPE_NSIP:
		if (ip_type == dns_rdatatype_a) {
			zbits = st->have.nsipv4;
		} else if (ip_type == dns_rdatatype_aaaa) {
			zbits = st->have.nsipv6;
		} else {
			zbits = st->have.nsip;
		}
		break;
	default:
		INSIST(0);
		break;
	}

	/*
	 * Choose
	 *	the earliest configured policy zone (rpz->num)
	 *	QNAME over IP over NSDNAME over NSIP (rpz_type)
	 *	the smallest name,
	 *	the longest IP address prefix,
	 *	the lexically smallest address.
	 */
	if (st->m.policy != DNS_RPZ_POLICY_MISS) {
		if (st->m.type >= rpz_type) {
			zbits &= DNS_RPZ_ZMASK(st->m.rpz->num);
		} else{
			zbits &= DNS_RPZ_ZMASK(st->m.rpz->num) >> 1;
		}
	}

	/*
	 * If the client wants recursion, allow only compatible policies.
	 */
	if (!RECURSIONOK(client))
		zbits &= st->popt.no_rd_ok;

#ifdef ORIGINAL_ISC_CODE
#else
	// If subscriber services is turned on, either match against
	// the selected per-subscriber bitmap, or the default bitmap.
	// If no bits are specified for a known subscriber, use the
	// default.  If REDOINGRPZ flag is set, we're checking if there were
	// a match in any RPZs including skipped ones.
	if (zbits && client->view->infoblox_ss_enable && !IB_REDOINGRPZ(client))
	{
		dns_rpz_zbits_t orig_zbits = zbits;
		if (client->subscriber != NULL) {
			dns_rpz_zbits_t local_zbits;

                        if (client->view->infoblox_ss_bypass_rpz_filtering && client->subscriber->action == IB_SUBSCRIBER_ACTION_PASSTHRU)
			{
				local_zbits = 0;
			} else {
				if (client->subscriber->zbits != 0)
					local_zbits = client->subscriber->zbits;
				else
					local_zbits = client->view->infoblox_ss_default_bitmap;
			}

			if (client->view->infoblox_ss_enable_global_allow_rpz_list) {
				local_zbits |= DNS_RPZ_ZBIT(client->view->infoblox_ss_global_allow_rpz_list);
			}

			// Set z-bit if the query matches pc proxy all whitelist RPZ zone
			// irrespecitve if subscriber proxy-all configuration
			local_zbits |=
			       DNS_RPZ_ZBIT(client->view->infoblox_pc_proxy_all_whitelist);
			zbits &= local_zbits;
		} else
			zbits &= client->view->infoblox_ss_default_bitmap;

		// If some of the original RPZ zbits are filtered out, remember
		// the fact so we can run a second pass for looking into the
		// skipped RPZs later.
		if (zbits != orig_zbits)
			st->state |= IB_DNS_RPZ_SKIPPED;
	}
	// On redoing, we should basically consider all RPZs ignoring
	// the subscriber-specific or "default" filtering bitmap.  But the
	// proxy-all white list RPZ is special: it's actually not an RPZ
	// but works as a "white list" only with pass-thru rules that are not
	// even logged.  We should not consider a rule in the white list to
	// be an RPZ match or let it hide an actual RPZ rule with lower
	// priority.
	if (client->view->infoblox_ss_enable && IB_REDOINGRPZ(client))
        {
		zbits &= ~DNS_RPZ_ZBIT(client->view->infoblox_pc_proxy_all_whitelist);
		if (client->view->infoblox_ss_enable_global_allow_rpz_list)
		    zbits &= ~DNS_RPZ_ZBIT(client->view->infoblox_ss_global_allow_rpz_list);
        }
#endif

	return (zbits);
}

static void
query_rpzfetch(ns_client_t *client, dns_name_t *qname, dns_rdatatype_t type) {
	isc_result_t result;
	dns_rdataset_t *tmprdataset;
	ns_client_t *dummy = NULL;
	unsigned int options;

	if (client->query.prefetch != NULL)
		return;

	if (client->recursionquota == NULL) {
		result = isc_quota_attach(&ns_g_server->recursionquota,
					  &client->recursionquota);
		if (result == ISC_R_SUCCESS && !client->mortal &&
		    !TCP_CLIENT(client))
		{
			result = ns_client_replace(client);
		}
		if (result != ISC_R_SUCCESS) {
#ifdef ORIGINAL_ISC_CODE
#else
			isc_quota_detach(&client->recursionquota);
#endif
			return;
		}
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_recursclients);
	}

	tmprdataset = query_newrdataset(client);
	if (tmprdataset == NULL)
		return;

	options = client->query.fetchoptions;
	if (TCP_CLIENT(client)) {
		options |= DNS_FETCHOPT_TCPCLIENT;
	}

	ns_client_attach(client, &dummy);

	result = dns_resolver_createfetch4(client->view->resolver, qname, type,
					   NULL, NULL, NULL, &client->peeraddr,
					   client->message->id, options, 0,
					   NULL, client->task, prefetch_done,
					   client, tmprdataset, NULL,
					   &client->query.prefetch);
	if (result != ISC_R_SUCCESS) {
		query_putrdataset(client, &tmprdataset);
		ns_client_detach(&dummy);
	}
}

/*
 * Get an NS, A, or AAAA rrset related to the response for the client
 * to check the contents of that rrset for hits by eligible policy zones.
 */
static isc_result_t
rpz_rrset_find(ns_client_t *client, dns_name_t *name, dns_rdatatype_t type,
	       dns_rpz_type_t rpz_type, dns_db_t **dbp,
	       dns_dbversion_t *version, dns_rdataset_t **rdatasetp,
	       isc_boolean_t resuming)
{
	dns_rpz_st_t *st;
	isc_boolean_t is_zone;
	dns_dbnode_t *node;
	dns_fixedname_t fixed;
	dns_name_t *found;
	isc_result_t result;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_rrset_find");

	st = client->query.rpz_st;
	if ((st->state & DNS_RPZ_RECURSING) != 0) {
		INSIST(st->r.r_type == type);
		INSIST(dns_name_equal(name, st->r_name));
		INSIST(*rdatasetp == NULL ||
		       !dns_rdataset_isassociated(*rdatasetp));
		st->state &= ~DNS_RPZ_RECURSING;
		RESTORE(*dbp, st->r.db);
		if (*rdatasetp != NULL)
			query_putrdataset(client, rdatasetp);
		RESTORE(*rdatasetp, st->r.r_rdataset);
		result = st->r.r_result;
		if (result == DNS_R_DELEGATION) {
			CTRACE(ISC_LOG_ERROR, "RPZ recursing");
			rpz_log_fail(client, DNS_RPZ_ERROR_LEVEL, name,
				     rpz_type, " rpz_rrset_find(1)", result);
			st->m.policy = DNS_RPZ_POLICY_ERROR;
			result = DNS_R_SERVFAIL;
		}
		return (result);
	}

	result = rpz_ready(client, rdatasetp);
	if (result != ISC_R_SUCCESS) {
		st->m.policy = DNS_RPZ_POLICY_ERROR;
		return (result);
	}
	if (*dbp != NULL) {
		is_zone = ISC_FALSE;
	} else {
		dns_zone_t *zone;

		version = NULL;
		zone = NULL;
		result = query_getdb(client, name, type, 0, &zone, dbp,
				     &version, &is_zone);
		if (result != ISC_R_SUCCESS) {
			rpz_log_fail(client, DNS_RPZ_ERROR_LEVEL, name,
				     rpz_type, " rpz_rrset_find(2)", result);
			st->m.policy = DNS_RPZ_POLICY_ERROR;
			if (zone != NULL)
				dns_zone_detach(&zone);
			return (result);
		}
		if (zone != NULL)
			dns_zone_detach(&zone);
	}

	node = NULL;
	dns_fixedname_init(&fixed);
	found = dns_fixedname_name(&fixed);
	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, NULL, NULL);
	result = dns_db_findext(*dbp, name, version, type, DNS_DBFIND_GLUEOK,
				client->now, &node, found,
				&cm, &ci, *rdatasetp, NULL);
#ifdef ORIGINAL_ISC_CODE
#else
	if (rpz_type == DNS_RPZ_TYPE_IP) {
		/*
		 * If the RPZ type is 'IP', this would go to the answer section
		 * of the response, so we need to check if that answer first
		 * needs to be replaced via GSLB processing.  Note that we
		 * shouldn't do this for other RPZ types since they are
		 * irrelevant to the answer.
		 */
		result = infoblox_query_idns_find(client, name, type, *dbp,
						  is_zone, &node, version,
						  found, *rdatasetp, NULL, result);
	}
#endif
	if (result == DNS_R_DELEGATION && is_zone && USECACHE(client)) {
		/*
		 * Try the cache if we're authoritative for an
		 * ancestor but not the domain itself.
		 */
		rpz_clean(NULL, dbp, &node, rdatasetp);
		version = NULL;
		dns_db_attach(client->view->cachedb, dbp);
		result = dns_db_findext(*dbp, name, version, type,
					0, client->now, &node, found,
					&cm, &ci, *rdatasetp, NULL);
	}
	rpz_clean(NULL, dbp, &node, NULL);
	if (result == DNS_R_DELEGATION) {
		rpz_clean(NULL, NULL, NULL, rdatasetp);
		/*
		 * Recurse for NS rrset or A or AAAA rrset for an NS.
		 * Do not recurse for addresses for the query name.
		 */
#ifdef ORIGINAL_ISC_CODE
#else
		/* On redoing, we suppress the internal recursion for
		 * simplicity, but still need to signal there's a potential
		 * match, so we return DELEGATION. */
		if (rpz_type != DNS_RPZ_TYPE_IP && IB_REDOINGRPZ(client))
			result = DNS_R_DELEGATION;
		else
#endif
		if (rpz_type == DNS_RPZ_TYPE_IP) {
			result = DNS_R_NXRRSET;
		} else if (!client->view->rpzs->p.nsip_wait_recurse) {
			query_rpzfetch(client, name, type);
			result = DNS_R_NXRRSET;
		} else {
			dns_name_copy(name, st->r_name, NULL);
			result = query_recurse(client, type, st->r_name,
					       NULL, NULL, resuming);
			if (result == ISC_R_SUCCESS) {
				st->state |= DNS_RPZ_RECURSING;
				result = DNS_R_DELEGATION;
			}
		}
	}
	return (result);
}

/*
 * Compute a policy owner name, p_name, in a policy zone given the needed
 * policy type and the trigger name.
 */
static isc_result_t
rpz_get_p_name(ns_client_t *client, dns_name_t *p_name,
	       dns_rpz_zone_t *rpz, dns_rpz_type_t rpz_type,
	       dns_name_t *trig_name)
{
	dns_offsets_t prefix_offsets;
	dns_name_t prefix, *suffix;
	unsigned int first, labels;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_get_p_name");

	/*
	 * The policy owner name consists of a suffix depending on the type
	 * and policy zone and a prefix that is the longest possible string
	 * from the trigger name that keesp the resulting policy owner name
	 * from being too long.
	 */
	switch (rpz_type) {
	case DNS_RPZ_TYPE_CLIENT_IP:
		suffix = &rpz->client_ip;
		break;
	case DNS_RPZ_TYPE_QNAME:
		suffix = &rpz->origin;
		break;
	case DNS_RPZ_TYPE_IP:
		suffix = &rpz->ip;
		break;
	case DNS_RPZ_TYPE_NSDNAME:
		suffix = &rpz->nsdname;
		break;
	case DNS_RPZ_TYPE_NSIP:
		suffix = &rpz->nsip;
		break;
	default:
		INSIST(0);
	}

	/*
	 * Start with relative version of the full trigger name,
	 * and trim enough allow the addition of the suffix.
	 */
	dns_name_init(&prefix, prefix_offsets);
	labels = dns_name_countlabels(trig_name);
	first = 0;
	for (;;) {
		dns_name_getlabelsequence(trig_name, first, labels-first-1,
					  &prefix);
		result = dns_name_concatenate(&prefix, suffix, p_name, NULL);
		if (result == ISC_R_SUCCESS)
			break;
		INSIST(result == DNS_R_NAMETOOLONG);
		/*
		 * Trim the trigger name until the combination is not too long.
		 */
		if (labels-first < 2) {
			rpz_log_fail(client, DNS_RPZ_ERROR_LEVEL, suffix,
				     rpz_type, " concatentate()", result);
			return (ISC_R_FAILURE);
		}
		/*
		 * Complain once about trimming the trigger name.
		 */
		if (first == 0) {
			rpz_log_fail(client, DNS_RPZ_DEBUG_LEVEL1, suffix,
				     rpz_type, " concatentate()", result);
		}
		++first;
	}
	return (ISC_R_SUCCESS);
}

/*
 * Look in policy zone rpz for a policy of rpz_type by p_name.
 * The self-name (usually the client qname or an NS name) is compared with
 * the target of a CNAME policy for the old style passthru encoding.
 * If found, the policy is recorded in *zonep, *dbp, *versionp, *nodep,
 * *rdatasetp, and *policyp.
 * The target DNS type, qtype, chooses the best rdataset for *rdatasetp.
 * The caller must decide if the found policy is most suitable, including
 * better than a previously found policy.
 * If it is best, the caller records it in client->query.rpz_st->m.
 */
static isc_result_t
rpz_find_p(ns_client_t *client, dns_name_t *self_name, dns_rdatatype_t qtype,
	   dns_name_t *p_name, dns_rpz_zone_t *rpz, dns_rpz_type_t rpz_type,
	   dns_zone_t **zonep, dns_db_t **dbp, dns_dbversion_t **versionp,
	   dns_dbnode_t **nodep, dns_rdataset_t **rdatasetp,
	   dns_rpz_policy_t *policyp)
{
	dns_fixedname_t foundf;
	dns_name_t *found;
	isc_result_t result;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;
	isc_boolean_t found_a = ISC_FALSE;

	REQUIRE(nodep != NULL);

	CTRACE(ISC_LOG_DEBUG(3), "rpz_find_p");

	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, NULL, NULL);

	/*
	 * Try to find either a CNAME or the type of record demanded by the
	 * request from the policy zone.
	 */
	rpz_clean(zonep, dbp, nodep, rdatasetp);
	result = rpz_ready(client, rdatasetp);
	if (result != ISC_R_SUCCESS) {
		CTRACE(ISC_LOG_ERROR, "rpz_ready() failed");
		return (DNS_R_SERVFAIL);
	}
	*versionp = NULL;
	result = rpz_getdb(client, p_name, rpz_type, zonep, dbp, versionp);
	if (result != ISC_R_SUCCESS)
		return (DNS_R_NXDOMAIN);
	dns_fixedname_init(&foundf);
	found = dns_fixedname_name(&foundf);

	result = dns_db_findext(*dbp, p_name, *versionp, dns_rdatatype_any, 0,
				client->now, nodep, found, &cm, &ci,
				*rdatasetp, NULL);
	/*
	 * Choose the best rdataset if we found something.
	 */
	if (result == ISC_R_SUCCESS) {
		dns_rdatasetiter_t *rdsiter;

		rdsiter = NULL;
		result = dns_db_allrdatasets(*dbp, *nodep, *versionp, 0,
					     &rdsiter);
		if (result != ISC_R_SUCCESS) {
			rpz_log_fail(client, DNS_RPZ_ERROR_LEVEL, p_name,
				     rpz_type, " allrdatasets()", result);
			CTRACE(ISC_LOG_ERROR,
			       "rpz_find_p: allrdatasets failed");
			return (DNS_R_SERVFAIL);
		}
		if (qtype == dns_rdatatype_aaaa &&
		    !ISC_LIST_EMPTY(client->view->dns64)) {
			for (result = dns_rdatasetiter_first(rdsiter);
			     result == ISC_R_SUCCESS;
			     result = dns_rdatasetiter_next(rdsiter)) {
				dns_rdatasetiter_current(rdsiter, *rdatasetp);
				if ((*rdatasetp)->type == dns_rdatatype_a) {
					found_a = ISC_TRUE;
				}
				dns_rdataset_disassociate(*rdatasetp);
			}
		}
		for (result = dns_rdatasetiter_first(rdsiter);
		     result == ISC_R_SUCCESS;
		     result = dns_rdatasetiter_next(rdsiter)) {
			dns_rdatasetiter_current(rdsiter, *rdatasetp);
			if ((*rdatasetp)->type == dns_rdatatype_cname ||
			    (*rdatasetp)->type == qtype)
				break;
			dns_rdataset_disassociate(*rdatasetp);
		}
		dns_rdatasetiter_destroy(&rdsiter);
		if (result != ISC_R_SUCCESS) {
			if (result != ISC_R_NOMORE) {
				rpz_log_fail(client, DNS_RPZ_ERROR_LEVEL,
					     p_name, rpz_type,
					     " rdatasetiter", result);
				CTRACE(ISC_LOG_ERROR,
				       "rpz_find_p: rdatasetiter failed");
				return (DNS_R_SERVFAIL);
			}
			/*
			 * Ask again to get the right DNS_R_DNAME/NXRRSET/...
			 * result if there is neither a CNAME nor target type.
			 */
			if (dns_rdataset_isassociated(*rdatasetp))
				dns_rdataset_disassociate(*rdatasetp);
			dns_db_detachnode(*dbp, nodep);

			if (qtype == dns_rdatatype_rrsig ||
			    qtype == dns_rdatatype_sig)
				result = DNS_R_NXRRSET;
			else
				result = dns_db_findext(*dbp, p_name, *versionp,
							qtype, 0, client->now,
							nodep, found, &cm, &ci,
							*rdatasetp, NULL);
		}
	}
	switch (result) {
	case ISC_R_SUCCESS:
		if ((*rdatasetp)->type != dns_rdatatype_cname) {
			*policyp = DNS_RPZ_POLICY_RECORD;
		} else {
			*policyp = dns_rpz_decode_cname(rpz, *rdatasetp,
							self_name);
			if ((*policyp == DNS_RPZ_POLICY_RECORD ||
			     *policyp == DNS_RPZ_POLICY_WILDCNAME) &&
			    qtype != dns_rdatatype_cname &&
			    qtype != dns_rdatatype_any)
				return (DNS_R_CNAME);
		}
		return (ISC_R_SUCCESS);
	case DNS_R_NXRRSET:
		if (found_a) {
			*policyp = DNS_RPZ_POLICY_DNS64;
		} else {
			*policyp = DNS_RPZ_POLICY_NODATA;
		}		return (result);
	case DNS_R_DNAME:
		/*
		 * DNAME policy RRs have very few if any uses that are not
		 * better served with simple wildcards.  Making them work would
		 * require complications to get the number of labels matched
		 * in the name or the found name to the main DNS_R_DNAME case
		 * in query_find().  The domain also does not appear in the
		 * summary database at the right level, so this happens only
		 * with a single policy zone when we have no summary database.
		 * Treat it as a miss.
		 */
	case DNS_R_NXDOMAIN:
	case DNS_R_EMPTYNAME:
		return (DNS_R_NXDOMAIN);
	default:
		rpz_log_fail(client, DNS_RPZ_ERROR_LEVEL, p_name, rpz_type,
			     "", result);
		CTRACE(ISC_LOG_ERROR,
		       "rpz_find_p: unexpected result");
		return (DNS_R_SERVFAIL);
	}
}

static void
rpz_save_p(dns_rpz_st_t *st, dns_rpz_zone_t *rpz, dns_rpz_type_t rpz_type,
	   dns_rpz_policy_t policy, dns_name_t *p_name, dns_rpz_prefix_t prefix,
	   isc_result_t result, dns_zone_t **zonep, dns_db_t **dbp,
	   dns_dbnode_t **nodep, dns_rdataset_t **rdatasetp,
	   dns_dbversion_t *version)
{
	dns_rdataset_t *trdataset = NULL;

	rpz_match_clear(st);
	st->m.rpz = rpz;
	st->m.type = rpz_type;
	st->m.policy = policy;
	dns_name_copy(p_name, st->p_name, NULL);
	st->m.prefix = prefix;
	st->m.result = result;
	SAVE(st->m.zone, *zonep);
	SAVE(st->m.db, *dbp);
	SAVE(st->m.node, *nodep);
	if (*rdatasetp != NULL && dns_rdataset_isassociated(*rdatasetp)) {
		/*
		 * Save the replacement rdataset from the policy
		 * and make the previous replacement rdataset scratch.
		 */
		SAVE(trdataset, st->m.rdataset);
		SAVE(st->m.rdataset, *rdatasetp);
		SAVE(*rdatasetp, trdataset);
		st->m.ttl = ISC_MIN(st->m.rdataset->ttl, rpz->max_policy_ttl);
	} else {
		st->m.ttl = ISC_MIN(DNS_RPZ_TTL_DEFAULT, rpz->max_policy_ttl);
	}
	SAVE(st->m.version, version);
}

/*
 * Check this address in every eligible policy zone.
 */
static isc_result_t
rpz_rewrite_ip(ns_client_t *client, const isc_netaddr_t *netaddr,
	       dns_rdatatype_t qtype, dns_rpz_type_t rpz_type,
	       dns_rpz_zbits_t zbits, dns_rdataset_t **p_rdatasetp)
{
	dns_rpz_zones_t *rpzs;
	dns_rpz_st_t *st;
	dns_rpz_zone_t *rpz;
	dns_rpz_prefix_t prefix;
	dns_rpz_num_t rpz_num;
	dns_fixedname_t ip_namef, p_namef;
	dns_name_t *ip_name, *p_name;
	dns_zone_t *p_zone;
	dns_db_t *p_db;
	dns_dbversion_t *p_version;
	dns_dbnode_t *p_node;
	dns_rpz_policy_t policy;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_rewrite_ip");

	dns_fixedname_init(&ip_namef);
	ip_name = dns_fixedname_name(&ip_namef);

	p_zone = NULL;
	p_db = NULL;
	p_node = NULL;

	rpzs = client->view->rpzs;
	st = client->query.rpz_st;
	while (zbits != 0) {
		rpz_num = dns_rpz_find_ip(rpzs, rpz_type, zbits, netaddr,
					  ip_name, &prefix);
		if (rpz_num == DNS_RPZ_INVALID_NUM)
			break;
		zbits &= (DNS_RPZ_ZMASK(rpz_num) >> 1);

		/*
		 * Do not try applying policy zones that cannot replace a
		 * previously found policy zone.
		 * Stop looking if the next best choice cannot
		 * replace what we already have.
		 */
		rpz = rpzs->zones[rpz_num];
		if (st->m.policy != DNS_RPZ_POLICY_MISS) {
			if (st->m.rpz->num < rpz->num)
				break;
			if (st->m.rpz->num == rpz->num &&
			    (st->m.type < rpz_type ||
			     st->m.prefix > prefix))
				break;
		}

		/*
		 * Get the policy for a prefix at least as long
		 * as the prefix of the entry we had before.
		 */
		dns_fixedname_init(&p_namef);
		p_name = dns_fixedname_name(&p_namef);
		result = rpz_get_p_name(client, p_name, rpz, rpz_type, ip_name);
		if (result != ISC_R_SUCCESS)
			continue;
		result = rpz_find_p(client, ip_name, qtype,
				    p_name, rpz, rpz_type,
				    &p_zone, &p_db, &p_version, &p_node,
				    p_rdatasetp, &policy);
		switch (result) {
		case DNS_R_NXDOMAIN:
			/*
			 * Continue after a policy record that is missing
			 * contrary to the summary data.  The summary
			 * data can out of date during races with and among
			 * policy zone updates.
			 */
			CTRACE(ISC_LOG_ERROR,
			       "rpz_rewrite_ip: mismatched summary data; "
			       "continuing");
			continue;
		case DNS_R_SERVFAIL:
			rpz_clean(&p_zone, &p_db, &p_node, p_rdatasetp);
			st->m.policy = DNS_RPZ_POLICY_ERROR;
			return (DNS_R_SERVFAIL);
		default:
			/*
			 * Forget this policy if it is not preferable
			 * to the previously found policy.
			 * If this policy is not good, then stop looking
			 * because none of the later policy zones would work.
			 *
			 * With more than one applicable policy, prefer
			 * the earliest configured policy,
			 * client-IP over QNAME over IP over NSDNAME over NSIP,
			 * the longest prefix
			 * the lexically smallest address.
			 * dns_rpz_find_ip() ensures st->m.rpz->num >= rpz->num.
			 * We can compare new and current p_name because
			 * both are of the same type and in the same zone.
			 * The tests above eliminate other reasons to
			 * reject this policy.  If this policy can't work,
			 * then neither can later zones.
			 */
			if (st->m.policy != DNS_RPZ_POLICY_MISS &&
			    rpz->num == st->m.rpz->num &&
			     (st->m.type == rpz_type &&
			      st->m.prefix == prefix &&
			      0 > dns_name_rdatacompare(st->p_name, p_name)))
				break;

			/*
			 * Stop checking after saving an enabled hit in this
			 * policy zone.  The radix tree in the policy zone
			 * ensures that we found the longest match.
			 */
			if (rpz->policy != DNS_RPZ_POLICY_DISABLED) {
				CTRACE(ISC_LOG_DEBUG(3),
				       "rpz_rewrite_ip: rpz_save_p");
				rpz_save_p(st, rpz, rpz_type,
					   policy, p_name, prefix, result,
					   &p_zone, &p_db, &p_node,
					   p_rdatasetp, p_version);
				break;
			}

			/*
			 * Log DNS_RPZ_POLICY_DISABLED zones
			 * and try the next eligible policy zone.
			 */
			rpz_log_rewrite(client, ISC_TRUE, policy, rpz_type,
#ifdef ORIGINAL_ISC_CODE
					p_zone, p_name, NULL, rpz_num);
#else
					p_zone, p_name, NULL, rpz_num, NULL,
					infoblox_zone_get_rpz_severity(p_zone),
					NULL, ISC_FALSE);
#endif
		}
	}

	rpz_clean(&p_zone, &p_db, &p_node, p_rdatasetp);
	return (ISC_R_SUCCESS);
}

/*
 * Check the IP addresses in the A or AAAA rrsets for name against
 * all eligible rpz_type (IP or NSIP) response policy rewrite rules.
 */
static isc_result_t
rpz_rewrite_ip_rrset(ns_client_t *client,
		     dns_name_t *name, dns_rdatatype_t qtype,
		     dns_rpz_type_t rpz_type, dns_rdatatype_t ip_type,
		     dns_db_t **ip_dbp, dns_dbversion_t *ip_version,
		     dns_rdataset_t **ip_rdatasetp,
		     dns_rdataset_t **p_rdatasetp, isc_boolean_t resuming)
{
	dns_rpz_zbits_t zbits;
	isc_netaddr_t netaddr;
	struct in_addr ina;
	struct in6_addr in6a;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_rewrite_ip_rrset");

	zbits = rpz_get_zbits(client, ip_type, rpz_type);
	if (zbits == 0)
		return (ISC_R_SUCCESS);

	/*
	 * Get the A or AAAA rdataset.
	 */
	result = rpz_rrset_find(client, name, ip_type, rpz_type, ip_dbp,
				ip_version, ip_rdatasetp, resuming);
	switch (result) {
	case ISC_R_SUCCESS:
	case DNS_R_GLUE:
	case DNS_R_ZONECUT:
		break;
	case DNS_R_EMPTYNAME:
	case DNS_R_EMPTYWILD:
	case DNS_R_NXDOMAIN:
	case DNS_R_NCACHENXDOMAIN:
	case DNS_R_NXRRSET:
	case DNS_R_NCACHENXRRSET:
	case ISC_R_NOTFOUND:
		return (ISC_R_SUCCESS);
	case DNS_R_DELEGATION:
	case DNS_R_DUPLICATE:
	case DNS_R_DROP:
		return (result);
	case DNS_R_CNAME:
	case DNS_R_DNAME:
		rpz_log_fail(client, DNS_RPZ_DEBUG_LEVEL1, name, rpz_type,
			     " NS address rewrite rrset", result);
		return (ISC_R_SUCCESS);
	default:
		if (client->query.rpz_st->m.policy != DNS_RPZ_POLICY_ERROR) {
			client->query.rpz_st->m.policy = DNS_RPZ_POLICY_ERROR;
			rpz_log_fail(client, DNS_RPZ_ERROR_LEVEL, name,
				     rpz_type, " NS address rewrite rrset",
				     result);
		}
		CTRACE(ISC_LOG_ERROR,
		       "rpz_rewrite_ip_rrset: unexpected result");
		return (DNS_R_SERVFAIL);
	}

	/*
	 * Check all of the IP addresses in the rdataset.
	 */
	for (result = dns_rdataset_first(*ip_rdatasetp);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(*ip_rdatasetp)) {

		dns_rdata_t rdata = DNS_RDATA_INIT;
		dns_rdataset_current(*ip_rdatasetp, &rdata);
		switch (rdata.type) {
		case dns_rdatatype_a:
			INSIST(rdata.length == 4);
			memmove(&ina.s_addr, rdata.data, 4);
			isc_netaddr_fromin(&netaddr, &ina);
			break;
		case dns_rdatatype_aaaa:
			INSIST(rdata.length == 16);
			memmove(in6a.s6_addr, rdata.data, 16);
			isc_netaddr_fromin6(&netaddr, &in6a);
			break;
		default:
			continue;
		}

		result = rpz_rewrite_ip(client, &netaddr, qtype, rpz_type,
					zbits, p_rdatasetp);
		if (result != ISC_R_SUCCESS)
			return (result);
	}

	return (ISC_R_SUCCESS);
}

/*
 * Look for IP addresses in A and AAAA rdatasets
 * that trigger all eligible IP or NSIP policy rules.
 */
static isc_result_t
rpz_rewrite_ip_rrsets(ns_client_t *client, dns_name_t *name,
		      dns_rdatatype_t qtype, dns_rpz_type_t rpz_type,
		      dns_rdataset_t **ip_rdatasetp, isc_boolean_t resuming)
{
	dns_rpz_st_t *st;
	dns_dbversion_t *ip_version;
	dns_db_t *ip_db;
	dns_rdataset_t *p_rdataset;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_rewrite_ip_rrsets");

	st = client->query.rpz_st;
	ip_version = NULL;
	ip_db = NULL;
	p_rdataset = NULL;
	if ((st->state & DNS_RPZ_DONE_IPv4) == 0 &&
	    (qtype == dns_rdatatype_a ||
	     qtype == dns_rdatatype_any ||
	     rpz_type == DNS_RPZ_TYPE_NSIP)) {
		/*
		 * Rewrite based on an IPv4 address that will appear
		 * in the ANSWER section or if we are checking IP addresses.
		 */
		result = rpz_rewrite_ip_rrset(client, name, qtype,
					      rpz_type, dns_rdatatype_a,
					      &ip_db, ip_version, ip_rdatasetp,
					      &p_rdataset, resuming);
		if (result == ISC_R_SUCCESS)
			st->state |= DNS_RPZ_DONE_IPv4;
	} else {
		result = ISC_R_SUCCESS;
	}
	if (result == ISC_R_SUCCESS &&
	    (qtype == dns_rdatatype_aaaa ||
	     qtype == dns_rdatatype_any ||
	     rpz_type == DNS_RPZ_TYPE_NSIP)) {
		/*
		 * Rewrite based on IPv6 addresses that will appear
		 * in the ANSWER section or if we are checking IP addresses.
		 */
		result = rpz_rewrite_ip_rrset(client, name,  qtype,
					      rpz_type, dns_rdatatype_aaaa,
					      &ip_db, ip_version, ip_rdatasetp,
					      &p_rdataset, resuming);
	}
	if (ip_db != NULL)
		dns_db_detach(&ip_db);
	query_putrdataset(client, &p_rdataset);
	return (result);
}

/*
 * Try to rewrite a request for a qtype rdataset based on the trigger name
 * trig_name and rpz_type (DNS_RPZ_TYPE_QNAME or DNS_RPZ_TYPE_NSDNAME).
 * Record the results including the replacement rdataset if any
 * in client->query.rpz_st.
 * *rdatasetp is a scratch rdataset.
 */
static isc_result_t
rpz_rewrite_name(ns_client_t *client, dns_name_t *trig_name,
		 dns_rdatatype_t qtype, dns_rpz_type_t rpz_type,
		 dns_rpz_zbits_t allowed_zbits, dns_rdataset_t **rdatasetp)
{
	dns_rpz_zones_t *rpzs;
	dns_rpz_zone_t *rpz;
	dns_rpz_st_t *st;
	dns_fixedname_t p_namef;
	dns_name_t *p_name;
	dns_rpz_zbits_t zbits;
	dns_rpz_num_t rpz_num;
	dns_zone_t *p_zone;
	dns_db_t *p_db;
	dns_dbversion_t *p_version;
	dns_dbnode_t *p_node;
	dns_rpz_policy_t policy;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_rewrite_name");

	zbits = rpz_get_zbits(client, qtype, rpz_type);
	zbits &= allowed_zbits;
	if (zbits == 0)
		return (ISC_R_SUCCESS);

	rpzs = client->view->rpzs;

	/*
	 * Use the summary database to find the bit mask of policy zones
	 * with policies for this trigger name. We do this even if there
	 * is only one eligible policy zone so that wildcard triggers
	 * are matched correctly, and not into their parent.
	 */
	zbits = dns_rpz_find_name(rpzs, rpz_type, zbits, trig_name);
	if (zbits == 0)
		return (ISC_R_SUCCESS);

	dns_fixedname_init(&p_namef);
	p_name = dns_fixedname_name(&p_namef);

	p_zone = NULL;
	p_db = NULL;
	p_node = NULL;

	st = client->query.rpz_st;

	/*
	 * Check the trigger name in every policy zone that the summary data
	 * says has a hit for the trigger name.
	 * Most of the time there are no eligible zones and the summary data
	 * keeps us from getting this far.
	 * We check the most eligible zone first and so usually check only
	 * one policy zone.
	 */
	for (rpz_num = 0; zbits != 0; ++rpz_num, zbits >>= 1) {
		if ((zbits & 1) == 0)
			continue;

		/*
		 * Do not check policy zones that cannot replace a previously
		 * found policy.
		 */
		rpz = rpzs->zones[rpz_num];
		if (st->m.policy != DNS_RPZ_POLICY_MISS) {
			if (st->m.rpz->num < rpz->num)
				break;
			if (st->m.rpz->num == rpz->num &&
			    st->m.type < rpz_type)
				break;
		}

		/*
		 * Get the next policy zone's record for this trigger name.
		 */
		result = rpz_get_p_name(client, p_name, rpz, rpz_type,
					trig_name);
		if (result != ISC_R_SUCCESS)
			continue;
		result = rpz_find_p(client, trig_name, qtype, p_name,
				    rpz, rpz_type,
				    &p_zone, &p_db, &p_version, &p_node,
				    rdatasetp, &policy);
		switch (result) {
		case DNS_R_NXDOMAIN:
			/*
			 * Continue after a missing policy record
			 * contrary to the summary data.  The summary
			 * data can out of date during races with and among
			 * policy zone updates.
			 */
			CTRACE(ISC_LOG_ERROR,
			       "rpz_rewrite_name: mismatched summary data; "
			       "continuing");
			continue;
		case DNS_R_SERVFAIL:
			rpz_clean(&p_zone, &p_db, &p_node, rdatasetp);
			st->m.policy = DNS_RPZ_POLICY_ERROR;
			return (DNS_R_SERVFAIL);
		default:
			/*
			 * With more than one applicable policy, prefer
			 * the earliest configured policy,
			 * client-IP over QNAME over IP over NSDNAME over NSIP,
			 * and the smallest name.
			 * We known st->m.rpz->num >= rpz->num  and either
			 * st->m.rpz->num > rpz->num or st->m.type >= rpz_type
			 */
			if (st->m.policy != DNS_RPZ_POLICY_MISS &&
			    rpz->num == st->m.rpz->num &&
			    (st->m.type < rpz_type ||
			     (st->m.type == rpz_type &&
			      0 >= dns_name_compare(p_name, st->p_name))))
				continue;
#if 0
			/*
			 * This code would block a customer reported information
			 * leak of rpz rules by rewriting requests in the
			 * rpz-ip, rpz-nsip, rpz-nsdname,and rpz-passthru TLDs.
			 * Without this code, a bad guy could request
			 * ********.10.rpz-ip. to find the policy rule for
			 * ********/14.  It is an insignificant leak and this
			 * code is not worth its cost, because the bad guy
			 * could publish "evil.com A ********" and request
			 * evil.com to get the same information.
			 * Keep code with "#if 0" in case customer demand
			 * is irresistible.
			 *
			 * We have the less frequent case of a triggered
			 * policy.  Check that we have not trigger on one
			 * of the pretend RPZ TLDs.
			 * This test would make it impossible to rewrite
			 * names in TLDs that start with "rpz-" should
			 * ICANN ever allow such TLDs.
			 */
			unsigned int labels;
			labels = dns_name_countlabels(trig_name);
			if (labels >= 2) {
				dns_label_t label;

				dns_name_getlabel(trig_name, labels-2, &label);
				if (label.length >= sizeof(DNS_RPZ_PREFIX)-1 &&
				    strncasecmp((const char *)label.base+1,
						DNS_RPZ_PREFIX,
						sizeof(DNS_RPZ_PREFIX)-1) == 0)
					continue;
			}
#endif
			if (rpz->policy != DNS_RPZ_POLICY_DISABLED) {
				CTRACE(ISC_LOG_DEBUG(3),
				       "rpz_rewrite_name: rpz_save_p");
				rpz_save_p(st, rpz, rpz_type,
					   policy, p_name, 0, result,
					   &p_zone, &p_db, &p_node,
					   rdatasetp, p_version);
				/*
				 * After a hit, higher numbered policy zones
				 * are irrelevant
				 */
				rpz_clean(&p_zone, &p_db, &p_node, rdatasetp);
				return (ISC_R_SUCCESS);
			}
			/*
			 * Log DNS_RPZ_POLICY_DISABLED zones
			 * and try the next eligible policy zone.
			 */
			rpz_log_rewrite(client, ISC_TRUE, policy, rpz_type,
#ifdef ORIGINAL_ISC_CODE
					p_zone, p_name, NULL, rpz_num);
#else
					p_zone, p_name, NULL, rpz_num, NULL,
					infoblox_zone_get_rpz_severity(p_zone),
					NULL, ISC_FALSE);
#endif
			break;
		}
	}

	rpz_clean(&p_zone, &p_db, &p_node, rdatasetp);
	return (ISC_R_SUCCESS);
}

static void
rpz_rewrite_ns_skip(ns_client_t *client, dns_name_t *nsname,
		    isc_result_t result, int level, const char *str)
{
	dns_rpz_st_t *st;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_rewrite_ns_skip");

	st = client->query.rpz_st;

	if (str != NULL)
		rpz_log_fail_helper(client, level, nsname,
				    DNS_RPZ_TYPE_NSIP, DNS_RPZ_TYPE_NSDNAME,
				    str, result);
	if (st->r.ns_rdataset != NULL &&
	    dns_rdataset_isassociated(st->r.ns_rdataset))
		dns_rdataset_disassociate(st->r.ns_rdataset);

	st->r.label--;
}

#ifdef ORIGINAL_ISC_CODE
#else
static isc_boolean_t
ib_do_nxdomain_modify(ns_client_t *client,
		      dns_rdatatype_t qtype,
		      dns_rdataset_t *rdataset,
		      isc_result_t result) {
	isc_boolean_t retval = ISC_FALSE;

	if (result == DNS_R_NCACHENXDOMAIN &&
	    (qtype == dns_rdatatype_a || qtype == dns_rdatatype_aaaa) &&
	    // Checking rdataset!=NULL doesn't really have anything
	    // to do with whether we should or should not do NXDOMAIN/MODIFY.
	    // However, we need the rdataset in order to be able to
	    // provide redirection addresses.
	    rdataset != NULL &&
	    ! SKIPNXDOMAINREDIRECT(client) &&
	    (IS_NXDOMAIN_CONFIGURED(ns_g_server) ||
	     IS_NXDOMAIN_CONFIGURED(client->view)) &&
	    (!WANTDNSSEC(client) || client->view->nxdomain_break_dnssec))
		retval = ISC_TRUE;

	return (retval);
}

/*
 * Match an address against infoblox-deny-rpz ACL. If the address is found
 * in the list return ISC_TRUE.
 */
static isc_boolean_t
dns_infoblox_deny_rpz_acl_match(const isc_sockaddr_t *peeraddr)
{
	isc_netaddr_t netaddr;
	int	match;

	if (ns_g_server->infoblox_denyrpzacl != NULL) {
		isc_netaddr_fromsockaddr(&netaddr, peeraddr);

		/*
		 * Function dns_acl_match always returns ISC_R_SUCCESS so
		 * no additional error processing here.
		 */
		if (dns_acl_match(&netaddr, NULL,
				  ns_g_server->infoblox_denyrpzacl,
				  &ns_g_server->aclenv,
				  &match, NULL) == ISC_R_SUCCESS && match > 0)
			return ISC_TRUE;
	}

	return ISC_FALSE;
}

/*
 * Helper for rpz_rewrite().  We've added conditions that bypass main RPZ
 * processing, so if we decide not to do it then ensure the state is cleared.
 * If return true if RPZ processing should be performed; or else returns false.
 */
static inline isc_boolean_t
infoblox_qf_should_do_rpz(ns_client_t *client, isc_boolean_t is_zone,
			  dns_rdatatype_t qtype, dns_rdataset_t *rdataset,
			  isc_result_t result) {
	if ((!SKIPRPZ(client) &&
	     // If this is recursion data (is_zone==ISC_FALSE), and we will
	     // apply an NXDOMAIN MODIFY, skip RPZ.
	     (is_zone || !ib_do_nxdomain_modify(client, qtype, rdataset, result))) &&
	    // If client address is listed in infoblox-deny-rpz, skip RPZ.
	    !dns_infoblox_deny_rpz_acl_match(&client->peeraddr) &&
	    // If the query name is known to have no RPZ rewrite, skip RPZ.
	    !(client->query.infoblox_rpz_found_white_list =
	      infoblox_rpz_cache_find(client->view->rpz_white_list,
				      client->query.qname, qtype,
				      WANTDNSSEC(client)))) {
		return (ISC_TRUE);
	}
	if (isc_log_wouldlog(ns_g_lctx, ISC_LOG_DEBUG(3))) {
	        ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, ISC_LOG_DEBUG(3),
			      "Infoblox RPZ whitelist cache hit");
	}
	if (client->query.rpz_st != NULL) {
		infoblox_log(3, "Skipping rpz_rewrite() and clearing RPZ state.");
                rpz_st_clear(client);
	}
	return (ISC_FALSE);
}
#endif

/*
 * RPZ query result types
 */
typedef enum {
	RPZ_QRESULT_TYPE_DONE,
	RPZ_QRESULT_TYPE_RESTART,
	RPZ_QRESULT_TYPE_RECURSE,
} rpz_qresult_type_t;

/*
 * Look for response policy zone QNAME, NSIP, and NSDNAME rewriting.
 */
static isc_result_t
rpz_rewrite(ns_client_t *client, dns_rdatatype_t qtype,
	    isc_result_t qresult, isc_boolean_t resuming,
#ifdef ORIGINAL_ISC_CODE
#else
	    isc_boolean_t is_zone,
#endif
	    dns_rdataset_t *ordataset, dns_rdataset_t *osigset)
{
	dns_rpz_zones_t *rpzs;
	dns_rpz_st_t *st;
	dns_rdataset_t *rdataset;
	dns_fixedname_t nsnamef;
	dns_name_t *nsname;
	rpz_qresult_type_t qresult_type;
	dns_rpz_zbits_t zbits;
	isc_result_t result = ISC_R_SUCCESS;
	dns_rpz_have_t have;
	dns_rpz_popt_t popt;
	int rpz_ver;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_rewrite");

	rpzs = client->view->rpzs;
	st = client->query.rpz_st;

	if (rpzs == NULL ||
	    (st != NULL && (st->state & DNS_RPZ_REWRITTEN) != 0))
		return (DNS_R_DISALLOWED);

	RWLOCK(&rpzs->search_lock, isc_rwlocktype_read);
	if (rpzs->p.num_zones == 0 ||
	    (!RECURSIONOK(client) && rpzs->p.no_rd_ok == 0) ||
	    !rpz_ck_dnssec(client, qresult, ordataset, osigset))
	{
		RWUNLOCK(&rpzs->search_lock, isc_rwlocktype_read);
		return (DNS_R_DISALLOWED);
	}
	have = rpzs->have;
	popt = rpzs->p;
	rpz_ver = rpzs->rpz_ver;
	RWUNLOCK(&rpzs->search_lock, isc_rwlocktype_read);

#ifdef ORIGINAL_ISC_CODE
#else
	if (!infoblox_qf_should_do_rpz(client, is_zone, qtype, ordataset,
				       qresult)) {
		return (DNS_R_DISALLOWED);
	}
#endif

	if (st == NULL) {
		st = isc_mem_get(client->mctx, sizeof(*st));
		if (st == NULL)
			return (ISC_R_NOMEMORY);
		st->state = 0;
	}
	if (st->state == 0) {
		st->state |= DNS_RPZ_ACTIVE;
		memset(&st->m, 0, sizeof(st->m));
		st->m.type = DNS_RPZ_TYPE_BAD;
		st->m.policy = DNS_RPZ_POLICY_MISS;
		st->m.ttl = ~0;
		memset(&st->r, 0, sizeof(st->r));
		memset(&st->q, 0, sizeof(st->q));
		dns_fixedname_init(&st->_p_namef);
		dns_fixedname_init(&st->_r_namef);
		dns_fixedname_init(&st->_fnamef);
		st->p_name = dns_fixedname_name(&st->_p_namef);
		st->r_name = dns_fixedname_name(&st->_r_namef);
		st->fname = dns_fixedname_name(&st->_fnamef);
		st->have = have;
		st->popt = popt;
		st->rpz_ver = rpz_ver;
		client->query.rpz_st = st;
	}

	/*
	 * There is nothing to rewrite if the main query failed.
	 */
	switch (qresult) {
	case ISC_R_SUCCESS:
	case DNS_R_GLUE:
	case DNS_R_ZONECUT:
		qresult_type = RPZ_QRESULT_TYPE_DONE;
		break;
	case DNS_R_EMPTYNAME:
	case DNS_R_NXRRSET:
	case DNS_R_NXDOMAIN:
	case DNS_R_EMPTYWILD:
	case DNS_R_NCACHENXDOMAIN:
	case DNS_R_NCACHENXRRSET:
	case DNS_R_CNAME:
	case DNS_R_DNAME:
		qresult_type = RPZ_QRESULT_TYPE_RESTART;
		break;
	case DNS_R_DELEGATION:
	case ISC_R_NOTFOUND:
		/*
		 * If recursion is on, do only tentative rewriting.
		 * If recursion is off, this the normal and only time we
		 * can rewrite.
		 */
		if (RECURSIONOK(client))
			qresult_type = RPZ_QRESULT_TYPE_RECURSE;
		else
			qresult_type = RPZ_QRESULT_TYPE_RESTART;
		break;
	case ISC_R_FAILURE:
	case ISC_R_TIMEDOUT:
	case DNS_R_BROKENCHAIN:
		rpz_log_fail(client, DNS_RPZ_DEBUG_LEVEL3, client->query.qname,
			     DNS_RPZ_TYPE_QNAME,
			     " stop on qresult in rpz_rewrite()", qresult);
		return (ISC_R_SUCCESS);
	default:
		rpz_log_fail(client, DNS_RPZ_DEBUG_LEVEL1, client->query.qname,
			     DNS_RPZ_TYPE_QNAME,
			     " stop on unrecognized qresult in rpz_rewrite()",
			     qresult);
		return (ISC_R_SUCCESS);
	}

	rdataset = NULL;

	if ((st->state & (DNS_RPZ_DONE_CLIENT_IP | DNS_RPZ_DONE_QNAME)) !=
	    (DNS_RPZ_DONE_CLIENT_IP | DNS_RPZ_DONE_QNAME)) {
		isc_netaddr_t netaddr;
		dns_rpz_zbits_t allowed;

		if (qresult_type == RPZ_QRESULT_TYPE_RECURSE) {
			/*
			 * This request needs recursion that has not been done.
			 * Get bits for the policy zones that do not need
			 * to wait for the results of recursion.
			 */
			allowed = st->have.qname_skip_recurse;
			if (allowed == 0)
				return (ISC_R_SUCCESS);
		} else {
			allowed = DNS_RPZ_ALL_ZBITS;
		}

		/*
		 * Check once for triggers for the client IP address.
		 */
		if ((st->state & DNS_RPZ_DONE_CLIENT_IP) == 0) {
			zbits = rpz_get_zbits(client, dns_rdatatype_none,
					      DNS_RPZ_TYPE_CLIENT_IP);
			zbits &= allowed;
			if (zbits != 0) {
				isc_netaddr_fromsockaddr(&netaddr,
							&client->peeraddr);
				result = rpz_rewrite_ip(client, &netaddr, qtype,
							DNS_RPZ_TYPE_CLIENT_IP,
							zbits, &rdataset);
				if (result != ISC_R_SUCCESS)
					goto cleanup;
			}
		}

		/*
		 * Check triggers for the query name if this is the first time
		 * for the current qname.
		 * There is a first time for each name in a CNAME chain
		 */
		if ((st->state & DNS_RPZ_DONE_QNAME) == 0) {
			result = rpz_rewrite_name(client, client->query.qname,
						  qtype, DNS_RPZ_TYPE_QNAME,
						  allowed, &rdataset);
			if (result != ISC_R_SUCCESS)
				goto cleanup;

			/*
			 * Check IPv4 addresses in A RRs next.
			 * Reset to the start of the NS names.
			 */
			st->r.label = dns_name_countlabels(client->query.qname);
			st->state &= ~(DNS_RPZ_DONE_QNAME_IP |
				       DNS_RPZ_DONE_IPv4);

		}

		/*
		 * Quit if this was an attempt to find a qname or
		 * client-IP trigger before recursion.
		 * We will be back if no pre-recursion triggers hit.
		 * For example, consider 2 policy zones, both with qname and
		 * IP address triggers.  If the qname misses the 1st zone,
		 * then we cannot know whether a hit for the qname in the
		 * 2nd zone matters until after recursing to get the A RRs and
		 * testing them in the first zone.
		 * Do not bother saving the work from this attempt,
		 * because recusion is so slow.
		 */
		if (qresult_type == RPZ_QRESULT_TYPE_RECURSE)
			goto cleanup;

		/*
		 * DNS_RPZ_DONE_QNAME but not DNS_RPZ_DONE_CLIENT_IP
		 * is reset at the end of dealing with each CNAME.
		 */
		st->state |= (DNS_RPZ_DONE_CLIENT_IP | DNS_RPZ_DONE_QNAME);
	}

	/*
	 * Check known IP addresses for the query name if the database
	 * lookup resulted in some addresses (qresult_type ==
	 * RPZ_QRESULT_TYPE_DONE) and if we have not already checked them.
	 * Any recursion required for the query has already happened.
	 * Do not check addresses that will not be in the ANSWER section.
	 */
	if ((st->state & DNS_RPZ_DONE_QNAME_IP) == 0 &&
	    qresult_type == RPZ_QRESULT_TYPE_DONE &&
	    rpz_get_zbits(client, qtype, DNS_RPZ_TYPE_IP) != 0) {
		result = rpz_rewrite_ip_rrsets(client,
					       client->query.qname, qtype,
					       DNS_RPZ_TYPE_IP,
					       &rdataset, resuming);
		if (result != ISC_R_SUCCESS)
			goto cleanup;
		/*
		 * We are finished checking the IP addresses for the qname.
		 * Start with IPv4 if we will check NS IP addesses.
		 */
		st->state |= DNS_RPZ_DONE_QNAME_IP;
		st->state &= ~DNS_RPZ_DONE_IPv4;
	}

	/*
	 * Stop looking for rules if there are none of the other kinds
	 * that could override what we already have.
	 */
	if (rpz_get_zbits(client, dns_rdatatype_any,
			  DNS_RPZ_TYPE_NSDNAME) == 0 &&
	    rpz_get_zbits(client, dns_rdatatype_any,
			  DNS_RPZ_TYPE_NSIP) == 0) {
		result = ISC_R_SUCCESS;
		goto cleanup;
	}

#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * If NSIP/NSDNAME is disabled, then do not proceed further
	 * to process those rules.
	 */
	if (g_infoblox_disable_nsdname_nsip == ISC_TRUE) {
		isc_log_write(ns_g_lctx,
			      NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, ISC_LOG_DEBUG(5),
			      "NSIP/NSDNAME rules are disabled, so "
			      "skip processing those rules");
		result = ISC_R_SUCCESS;
		goto cleanup;
	}
#endif

	dns_fixedname_init(&nsnamef);
	dns_name_clone(client->query.qname, dns_fixedname_name(&nsnamef));
	while (st->r.label > st->popt.min_ns_labels) {
		/*
		 * Get NS rrset for each domain in the current qname.
		 */
		if (st->r.label == dns_name_countlabels(client->query.qname)) {
			nsname = client->query.qname;
		} else {
			nsname = dns_fixedname_name(&nsnamef);
			dns_name_split(client->query.qname, st->r.label,
				       NULL, nsname);
		}
		if (st->r.ns_rdataset == NULL ||
		    !dns_rdataset_isassociated(st->r.ns_rdataset))
		{
			dns_db_t *db = NULL;
			result = rpz_rrset_find(client, nsname,
						dns_rdatatype_ns,
						DNS_RPZ_TYPE_NSDNAME,
						&db, NULL, &st->r.ns_rdataset,
						resuming);
			if (db != NULL)
				dns_db_detach(&db);
			if (st->m.policy == DNS_RPZ_POLICY_ERROR)
				goto cleanup;
			switch (result) {
			case ISC_R_SUCCESS:
				result = dns_rdataset_first(st->r.ns_rdataset);
				if (result != ISC_R_SUCCESS)
					goto cleanup;
				st->state &= ~(DNS_RPZ_DONE_NSDNAME |
					       DNS_RPZ_DONE_IPv4);
				break;
			case DNS_R_DELEGATION:
			case DNS_R_DUPLICATE:
			case DNS_R_DROP:
				goto cleanup;
			case DNS_R_EMPTYNAME:
			case DNS_R_NXRRSET:
			case DNS_R_EMPTYWILD:
			case DNS_R_NXDOMAIN:
			case DNS_R_NCACHENXDOMAIN:
			case DNS_R_NCACHENXRRSET:
			case ISC_R_NOTFOUND:
			case DNS_R_CNAME:
			case DNS_R_DNAME:
				rpz_rewrite_ns_skip(client, nsname, result,
						    0, NULL);
				continue;
			case ISC_R_TIMEDOUT:
			case DNS_R_BROKENCHAIN:
			case ISC_R_FAILURE:
				rpz_rewrite_ns_skip(client, nsname, result,
						DNS_RPZ_DEBUG_LEVEL3,
						" NS rpz_rrset_find()");
				continue;
			default:
				rpz_rewrite_ns_skip(client, nsname, result,
						DNS_RPZ_INFO_LEVEL,
						" unrecognized NS"
						" rpz_rrset_find()");
				continue;
			}
		}
		/*
		 * Check all NS names.
		 */
		do {
			dns_rdata_ns_t ns;
			dns_rdata_t nsrdata = DNS_RDATA_INIT;

			dns_rdataset_current(st->r.ns_rdataset, &nsrdata);
			result = dns_rdata_tostruct(&nsrdata, &ns, NULL);
			dns_rdata_reset(&nsrdata);
			if (result != ISC_R_SUCCESS) {
				rpz_log_fail(client, DNS_RPZ_ERROR_LEVEL,
					     nsname, DNS_RPZ_TYPE_NSIP,
					     " rdata_tostruct()", result);
				st->m.policy = DNS_RPZ_POLICY_ERROR;
				goto cleanup;
			}
			/*
			 * Do nothing about "NS ."
			 */
			if (dns_name_equal(&ns.name, dns_rootname)) {
				dns_rdata_freestruct(&ns);
				result = dns_rdataset_next(st->r.ns_rdataset);
				continue;
			}
			/*
			 * Check this NS name if we did not handle it
			 * during a previous recursion.
			 */
			if ((st->state & DNS_RPZ_DONE_NSDNAME) == 0) {
				result = rpz_rewrite_name(client, &ns.name,
							qtype,
							DNS_RPZ_TYPE_NSDNAME,
							DNS_RPZ_ALL_ZBITS,
							&rdataset);
				if (result != ISC_R_SUCCESS) {
					dns_rdata_freestruct(&ns);
					goto cleanup;
				}
				st->state |= DNS_RPZ_DONE_NSDNAME;
			}
			/*
			 * Check all IP addresses for this NS name.
			 */
			result = rpz_rewrite_ip_rrsets(client, &ns.name, qtype,
						       DNS_RPZ_TYPE_NSIP,
						       &rdataset, resuming);
			dns_rdata_freestruct(&ns);
			if (result != ISC_R_SUCCESS)
				goto cleanup;
			st->state &= ~(DNS_RPZ_DONE_NSDNAME |
				       DNS_RPZ_DONE_IPv4);
			result = dns_rdataset_next(st->r.ns_rdataset);
		} while (result == ISC_R_SUCCESS);
		dns_rdataset_disassociate(st->r.ns_rdataset);
		st->r.label--;

		if (rpz_get_zbits(client, dns_rdatatype_any,
				  DNS_RPZ_TYPE_NSDNAME) == 0 &&
		    rpz_get_zbits(client, dns_rdatatype_any,
				  DNS_RPZ_TYPE_NSIP) == 0)
			break;
	}

	/*
	 * Use the best hit, if any.
	 */
	result = ISC_R_SUCCESS;

cleanup:
	if (st->m.policy != DNS_RPZ_POLICY_MISS &&
	    st->m.policy != DNS_RPZ_POLICY_ERROR &&
	    st->m.rpz->policy != DNS_RPZ_POLICY_GIVEN)
		st->m.policy = st->m.rpz->policy;
#ifdef ORIGINAL_ISC_CODE
#else
	// Check to see if we got a hit on the RPZ for the proxy whitelist.
	// If so, set a flag, as we don't want to proxy this query.
	if (client->view->infoblox_pc_enable && client->subscriber != NULL &&
	    client->subscriber->proxy_all &&
	    st->m.policy == DNS_RPZ_POLICY_PASSTHRU &&
	    st->m.rpz->num == client->view->infoblox_pc_proxy_all_whitelist) {
		client->query.attributes |= NS_QUERYATTR_PC_WHITELIST;

		if (isc_log_wouldlog(ns_g_lctx, ISC_LOG_DEBUG(3))) {
			char namebuf[DNS_NAME_FORMATSIZE];
			const char *subscriber_cef =
				client->subscriber->cef_log_string ?
				client->subscriber->cef_log_string :
				"No CEF string";

			dns_name_format(client->query.qname, namebuf,
					sizeof(namebuf));

			ns_client_log(client, NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_QUERY, ISC_LOG_DEBUG(3),
				      "subscriber %s: proxy all whitelist "
				      "hit for %s", subscriber_cef, namebuf);
		}
	}
#endif

	if (st->m.policy == DNS_RPZ_POLICY_MISS ||
	    st->m.policy == DNS_RPZ_POLICY_PASSTHRU ||
	    st->m.policy == DNS_RPZ_POLICY_ERROR) {
		if (st->m.policy == DNS_RPZ_POLICY_PASSTHRU &&
		    result != DNS_R_DELEGATION)
			rpz_log_rewrite(client, ISC_FALSE, st->m.policy,
#ifdef ORIGINAL_ISC_CODE
					st->m.type, st->m.zone, st->p_name,
					NULL, st->m.rpz->num);
#else
					st->m.type, st->m.zone, st->p_name,
					NULL, st->m.rpz->num,
					NULL,
					infoblox_zone_get_rpz_severity(st->m.zone),
					NULL, ISC_FALSE);
#endif

		rpz_match_clear(st);
	}
	if (st->m.policy == DNS_RPZ_POLICY_ERROR) {
		CTRACE(ISC_LOG_ERROR, "SERVFAIL due to RPZ policy");
		st->m.type = DNS_RPZ_TYPE_BAD;
		result = DNS_R_SERVFAIL;
	}
	query_putrdataset(client, &rdataset);
	if ((st->state & DNS_RPZ_RECURSING) == 0)
		rpz_clean(NULL, &st->r.db, NULL, &st->r.ns_rdataset);

	return (result);
}

/*
 * See if response policy zone rewriting is allowed by a lack of interest
 * by the client in DNSSEC or a lack of signatures.
 */
static isc_boolean_t
rpz_ck_dnssec(ns_client_t *client, isc_result_t qresult,
	      dns_rdataset_t *rdataset, dns_rdataset_t *sigrdataset)
{
	dns_fixedname_t fixed;
	dns_name_t *found;
	dns_rdataset_t trdataset;
	dns_rdatatype_t type;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_ck_dnssec");

	if (client->view->rpzs->p.break_dnssec || !WANTDNSSEC(client))
		return (ISC_TRUE);

	/*
	 * We do not know if there are signatures if we have not recursed
	 * for them.
	 */
	if (qresult == DNS_R_DELEGATION || qresult == ISC_R_NOTFOUND)
		return (ISC_FALSE);

	if (sigrdataset == NULL)
		return (ISC_TRUE);
	if (dns_rdataset_isassociated(sigrdataset))
		return (ISC_FALSE);

	/*
	 * We are happy to rewrite nothing.
	 */
	if (rdataset == NULL || !dns_rdataset_isassociated(rdataset))
		return (ISC_TRUE);
	/*
	 * Do not rewrite if there is any sign of signatures.
	 */
	if (rdataset->type == dns_rdatatype_nsec ||
	    rdataset->type == dns_rdatatype_nsec3 ||
	    rdataset->type == dns_rdatatype_rrsig)
		return (ISC_FALSE);

	/*
	 * Look for a signature in a negative cache rdataset.
	 */
	if ((rdataset->attributes & DNS_RDATASETATTR_NEGATIVE) == 0)
		return (ISC_TRUE);
	dns_fixedname_init(&fixed);
	found = dns_fixedname_name(&fixed);
	dns_rdataset_init(&trdataset);
	for (result = dns_rdataset_first(rdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(rdataset)) {
		dns_ncache_current(rdataset, found, &trdataset);
		type = trdataset.type;
		dns_rdataset_disassociate(&trdataset);
		if (type == dns_rdatatype_nsec ||
		    type == dns_rdatatype_nsec3 ||
		    type == dns_rdatatype_rrsig)
			return (ISC_FALSE);
	}
	return (ISC_TRUE);
}

/*
 * Add a CNAME to the query response, including translating foo.evil.com and
 *	*.evil.com CNAME *.example.com
 * to
 *	foo.evil.com CNAME foo.evil.com.example.com
 */
static isc_result_t
rpz_add_cname(ns_client_t *client, dns_rpz_st_t *st,
	      dns_name_t *cname, dns_name_t *fname, isc_buffer_t *dbuf)
{
	dns_fixedname_t prefix, suffix;
	unsigned int labels;
	isc_result_t result;

	CTRACE(ISC_LOG_DEBUG(3), "rpz_add_cname");

	labels = dns_name_countlabels(cname);
	if (labels > 2 && dns_name_iswildcard(cname)) {
		dns_fixedname_init(&prefix);
		dns_name_split(client->query.qname, 1,
			       dns_fixedname_name(&prefix), NULL);
		dns_fixedname_init(&suffix);
		dns_name_split(cname, labels-1,
			       NULL, dns_fixedname_name(&suffix));
		result = dns_name_concatenate(dns_fixedname_name(&prefix),
					      dns_fixedname_name(&suffix),
					      fname, NULL);
		if (result == DNS_R_NAMETOOLONG)
			client->message->rcode = dns_rcode_yxdomain;
	} else {
		result = dns_name_copy(cname, fname, NULL);
		RUNTIME_CHECK(result == ISC_R_SUCCESS);
	}
	if (result != ISC_R_SUCCESS)
		return (result);
	query_keepname(client, fname, dbuf);
	result = query_add_cname(client, client->query.qname,
				 fname, dns_trust_authanswer, st->m.ttl);
	if (result != ISC_R_SUCCESS)
		return (result);
	rpz_log_rewrite(client, ISC_FALSE, st->m.policy,
			st->m.type, st->m.zone, st->p_name, fname,
#ifdef ORIGINAL_ISC_CODE
			st->m.rpz->num);
#else
			st->m.rpz->num,
			NULL, infoblox_zone_get_rpz_severity(st->m.zone), NULL, ISC_FALSE);
#endif
	ns_client_qnamereplace(client, fname);
	/*
	 * Turn off DNSSEC because the results of a
	 * response policy zone cannot verify.
	 */
	client->attributes &= ~(NS_CLIENTATTR_WANTDNSSEC |
				NS_CLIENTATTR_WANTAD);
	return (ISC_R_SUCCESS);
}

#define MAX_RESTARTS 16

#define QUERY_ERROR(r) \
do { \
	eresult = r; \
	want_restart = ISC_FALSE; \
	line = __LINE__; \
} while (0)

#define RECURSE_ERROR(r) \
do { \
	if ((r) == DNS_R_DUPLICATE || (r) == DNS_R_DROP) \
		QUERY_ERROR(r); \
	else \
		QUERY_ERROR(DNS_R_SERVFAIL); \
} while (0)

/*
 * Extract a network address from the RDATA of an A or AAAA
 * record.
 *
 * Returns:
 *	ISC_R_SUCCESS
 *	ISC_R_NOTIMPLEMENTED	The rdata is not a known address type.
 */
static isc_result_t
rdata_tonetaddr(const dns_rdata_t *rdata, isc_netaddr_t *netaddr) {
	struct in_addr ina;
	struct in6_addr in6a;

	switch (rdata->type) {
	case dns_rdatatype_a:
		INSIST(rdata->length == 4);
		memmove(&ina.s_addr, rdata->data, 4);
		isc_netaddr_fromin(netaddr, &ina);
		return (ISC_R_SUCCESS);
	case dns_rdatatype_aaaa:
		INSIST(rdata->length == 16);
		memmove(in6a.s6_addr, rdata->data, 16);
		isc_netaddr_fromin6(netaddr, &in6a);
		return (ISC_R_SUCCESS);
	default:
		return (ISC_R_NOTIMPLEMENTED);
	}
}

/*
 * Find the sort order of 'rdata' in the topology-like
 * ACL forming the second element in a 2-element top-level
 * sortlist statement.
 */
static int
query_sortlist_order_2element(const dns_rdata_t *rdata, const void *arg) {
	isc_netaddr_t netaddr;

	if (rdata_tonetaddr(rdata, &netaddr) != ISC_R_SUCCESS)
		return (INT_MAX);
	return (ns_sortlist_addrorder2(&netaddr, arg));
}

/*
 * Find the sort order of 'rdata' in the matching element
 * of a 1-element top-level sortlist statement.
 */
static int
query_sortlist_order_1element(const dns_rdata_t *rdata, const void *arg) {
	isc_netaddr_t netaddr;

	if (rdata_tonetaddr(rdata, &netaddr) != ISC_R_SUCCESS)
		return (INT_MAX);
	return (ns_sortlist_addrorder1(&netaddr, arg));
}

/*
 * Find the sortlist statement that applies to 'client' and set up
 * the sortlist info in in client->message appropriately.
 */
static void
setup_query_sortlist(ns_client_t *client) {
	isc_netaddr_t netaddr;
	dns_rdatasetorderfunc_t order = NULL;
	const void *order_arg = NULL;

	isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);
	switch (ns_sortlist_setup(client->view->sortlist,
			       &netaddr, &order_arg)) {
	case NS_SORTLISTTYPE_1ELEMENT:
		order = query_sortlist_order_1element;
		break;
	case NS_SORTLISTTYPE_2ELEMENT:
		order = query_sortlist_order_2element;
		break;
	case NS_SORTLISTTYPE_NONE:
		order = NULL;
		break;
	default:
		INSIST(0);
		break;
	}
	dns_message_setsortorder(client->message, order, order_arg);
}

static void
query_addnoqnameproof(ns_client_t *client, dns_rdataset_t *rdataset) {
	isc_buffer_t *dbuf, b;
	dns_name_t *fname;
	dns_rdataset_t *neg, *negsig;
	isc_result_t result = ISC_R_NOMEMORY;

	CTRACE(ISC_LOG_DEBUG(3), "query_addnoqnameproof");

	fname = NULL;
	neg = NULL;
	negsig = NULL;

	dbuf = query_getnamebuf(client);
	if (dbuf == NULL)
		goto cleanup;
	fname = query_newname(client, dbuf, &b);
	neg = query_newrdataset(client);
	negsig = query_newrdataset(client);
	if (fname == NULL || neg == NULL || negsig == NULL)
		goto cleanup;

	result = dns_rdataset_getnoqname(rdataset, fname, neg, negsig);
	RUNTIME_CHECK(result == ISC_R_SUCCESS);

	query_addrrset(client, &fname, &neg, &negsig, dbuf,
		       DNS_SECTION_AUTHORITY);

	if ((rdataset->attributes & DNS_RDATASETATTR_CLOSEST) == 0)
		goto cleanup;

	if (fname == NULL) {
		dbuf = query_getnamebuf(client);
		if (dbuf == NULL)
			goto cleanup;
		fname = query_newname(client, dbuf, &b);
	}
	if (neg == NULL)
		neg = query_newrdataset(client);
	else if (dns_rdataset_isassociated(neg))
		dns_rdataset_disassociate(neg);
	if (negsig == NULL)
		negsig = query_newrdataset(client);
	else if (dns_rdataset_isassociated(negsig))
		dns_rdataset_disassociate(negsig);
	if (fname == NULL || neg == NULL || negsig == NULL)
		goto cleanup;
	result = dns_rdataset_getclosest(rdataset, fname, neg, negsig);
	RUNTIME_CHECK(result == ISC_R_SUCCESS);

	query_addrrset(client, &fname, &neg, &negsig, dbuf,
		       DNS_SECTION_AUTHORITY);

 cleanup:
	if (neg != NULL)
		query_putrdataset(client, &neg);
	if (negsig != NULL)
		query_putrdataset(client, &negsig);
	if (fname != NULL)
		query_releasename(client, &fname);
}

static inline void
answer_in_glue(ns_client_t *client, dns_rdatatype_t qtype) {
	dns_name_t *name;
	dns_message_t *msg;
	dns_section_t section = DNS_SECTION_ADDITIONAL;
	dns_rdataset_t *rdataset = NULL;

	msg = client->message;
	for (name = ISC_LIST_HEAD(msg->sections[section]);
	     name != NULL;
	     name = ISC_LIST_NEXT(name, link))
		if (dns_name_equal(name, client->query.qname)) {
			for (rdataset = ISC_LIST_HEAD(name->list);
			     rdataset != NULL;
			     rdataset = ISC_LIST_NEXT(rdataset, link))
				if (rdataset->type == qtype)
					break;
			break;
		}
	if (rdataset != NULL) {
		ISC_LIST_UNLINK(msg->sections[section], name, link);
		ISC_LIST_PREPEND(msg->sections[section], name, link);
		ISC_LIST_UNLINK(name->list, rdataset, link);
		ISC_LIST_PREPEND(name->list, rdataset, link);
		rdataset->attributes |= DNS_RDATASETATTR_REQUIRED;
	}
}

static unsigned char inaddr10_offsets[] = { 0, 3, 11, 16 };
static unsigned char inaddr172_offsets[] = { 0, 3, 7, 15, 20 };
static unsigned char inaddr192_offsets[] = { 0, 4, 8, 16, 21 };

static unsigned char inaddr10[] = "\00210\007IN-ADDR\004ARPA";

static unsigned char inaddr16172[] = "\00216\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr17172[] = "\00217\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr18172[] = "\00218\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr19172[] = "\00219\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr20172[] = "\00220\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr21172[] = "\00221\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr22172[] = "\00222\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr23172[] = "\00223\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr24172[] = "\00224\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr25172[] = "\00225\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr26172[] = "\00226\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr27172[] = "\00227\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr28172[] = "\00228\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr29172[] = "\00229\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr30172[] = "\00230\003172\007IN-ADDR\004ARPA";
static unsigned char inaddr31172[] = "\00231\003172\007IN-ADDR\004ARPA";

static unsigned char inaddr168192[] = "\003168\003192\007IN-ADDR\004ARPA";

static dns_name_t rfc1918names[] = {
	DNS_NAME_INITABSOLUTE(inaddr10, inaddr10_offsets),
	DNS_NAME_INITABSOLUTE(inaddr16172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr17172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr18172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr19172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr20172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr21172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr22172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr23172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr24172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr25172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr26172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr27172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr28172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr29172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr30172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr31172, inaddr172_offsets),
	DNS_NAME_INITABSOLUTE(inaddr168192, inaddr192_offsets)
};


static unsigned char prisoner_data[] = "\010prisoner\004iana\003org";
static unsigned char hostmaster_data[] = "\012hostmaster\014root-servers\003org";

static unsigned char prisoner_offsets[] = { 0, 9, 14, 18 };
static unsigned char hostmaster_offsets[] = { 0, 11, 24, 28 };

static dns_name_t prisoner =
	DNS_NAME_INITABSOLUTE(prisoner_data, prisoner_offsets);
static dns_name_t hostmaster =
	DNS_NAME_INITABSOLUTE(hostmaster_data, hostmaster_offsets);

static void
warn_rfc1918(ns_client_t *client, dns_name_t *fname, dns_rdataset_t *rdataset) {
	unsigned int i;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	dns_rdata_soa_t soa;
	dns_rdataset_t found;
	isc_result_t result;

	for (i = 0; i < (sizeof(rfc1918names)/sizeof(*rfc1918names)); i++) {
		if (dns_name_issubdomain(fname, &rfc1918names[i])) {
			dns_rdataset_init(&found);
			result = dns_ncache_getrdataset(rdataset,
							&rfc1918names[i],
							dns_rdatatype_soa,
							&found);
			if (result != ISC_R_SUCCESS)
				return;

			result = dns_rdataset_first(&found);
			RUNTIME_CHECK(result == ISC_R_SUCCESS);
			dns_rdataset_current(&found, &rdata);
			result = dns_rdata_tostruct(&rdata, &soa, NULL);
			RUNTIME_CHECK(result == ISC_R_SUCCESS);
			if (dns_name_equal(&soa.origin, &prisoner) &&
			    dns_name_equal(&soa.contact, &hostmaster)) {
				char buf[DNS_NAME_FORMATSIZE];
				dns_name_format(fname, buf, sizeof(buf));
				ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
					      NS_LOGMODULE_QUERY,
					      ISC_LOG_WARNING,
					      "RFC 1918 response from "
					      "Internet for %s", buf);
			}
			dns_rdataset_disassociate(&found);
			return;
		}
	}
}

static void
query_findclosestnsec3(dns_name_t *qname, dns_db_t *db,
		       dns_dbversion_t *version, ns_client_t *client,
		       dns_rdataset_t *rdataset, dns_rdataset_t *sigrdataset,
		       dns_name_t *fname, isc_boolean_t exact,
		       dns_name_t *found)
{
	unsigned char salt[256];
	size_t salt_length;
	isc_uint16_t iterations;
	isc_result_t result;
	unsigned int dboptions;
	dns_fixedname_t fixed;
	dns_hash_t hash;
	dns_name_t name;
	unsigned int skip = 0, labels;
	dns_rdata_nsec3_t nsec3;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	isc_boolean_t optout;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;

	salt_length = sizeof(salt);
	result = dns_db_getnsec3parameters(db, version, &hash, NULL,
					   &iterations, salt, &salt_length);
	if (result != ISC_R_SUCCESS)
		return;

	dns_name_init(&name, NULL);
	dns_name_clone(qname, &name);
	labels = dns_name_countlabels(&name);
	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, NULL, NULL);

	/*
	 * Map unknown algorithm to known value.
	 */
	if (hash == DNS_NSEC3_UNKNOWNALG)
		hash = 1;

 again:
	dns_fixedname_init(&fixed);
	result = dns_nsec3_hashname(&fixed, NULL, NULL, &name,
				    dns_db_origin(db), hash,
				    iterations, salt, salt_length);
	if (result != ISC_R_SUCCESS)
		return;

	dboptions = client->query.dboptions | DNS_DBFIND_FORCENSEC3;
	result = dns_db_findext(db, dns_fixedname_name(&fixed), version,
				dns_rdatatype_nsec3, dboptions, client->now,
				NULL, fname, &cm, &ci, rdataset, sigrdataset);

	if (result == DNS_R_NXDOMAIN) {
		if (!dns_rdataset_isassociated(rdataset)) {
			return;
		}
		result = dns_rdataset_first(rdataset);
		INSIST(result == ISC_R_SUCCESS);
		dns_rdataset_current(rdataset, &rdata);
		dns_rdata_tostruct(&rdata, &nsec3, NULL);
		dns_rdata_reset(&rdata);
		optout = ISC_TF((nsec3.flags & DNS_NSEC3FLAG_OPTOUT) != 0);
		if (found != NULL && optout &&
		    dns_name_issubdomain(&name, dns_db_origin(db)))
		{
			dns_rdataset_disassociate(rdataset);
			if (dns_rdataset_isassociated(sigrdataset))
				dns_rdataset_disassociate(sigrdataset);
			skip++;
			dns_name_getlabelsequence(qname, skip, labels - skip,
						  &name);
			ns_client_log(client, DNS_LOGCATEGORY_DNSSEC,
				      NS_LOGMODULE_QUERY, ISC_LOG_DEBUG(3),
				      "looking for closest provable encloser");
			goto again;
		}
		if (exact)
			ns_client_log(client, DNS_LOGCATEGORY_DNSSEC,
				      NS_LOGMODULE_QUERY, ISC_LOG_WARNING,
				      "expected a exact match NSEC3, got "
				      "a covering record");

	} else if (result != ISC_R_SUCCESS) {
		return;
	} else if (!exact)
		ns_client_log(client, DNS_LOGCATEGORY_DNSSEC,
			      NS_LOGMODULE_QUERY, ISC_LOG_WARNING,
			      "expected covering NSEC3, got an exact match");
	if (found == qname) {
		if (skip != 0U)
			dns_name_getlabelsequence(qname, skip, labels - skip,
						  found);
	} else if (found != NULL)
		dns_name_copy(&name, found, NULL);
	return;
}

#ifdef ALLOW_FILTER_AAAA
static isc_boolean_t
is_v4_client(ns_client_t *client) {
	if (isc_sockaddr_pf(&client->peeraddr) == AF_INET)
		return (ISC_TRUE);
	if (isc_sockaddr_pf(&client->peeraddr) == AF_INET6 &&
	    IN6_IS_ADDR_V4MAPPED(&client->peeraddr.type.sin6.sin6_addr))
		return (ISC_TRUE);
	return (ISC_FALSE);
}

static isc_boolean_t
is_v6_client(ns_client_t *client) {
	if (isc_sockaddr_pf(&client->peeraddr) == AF_INET6 &&
	    !IN6_IS_ADDR_V4MAPPED(&client->peeraddr.type.sin6.sin6_addr))
		return (ISC_TRUE);
	return (ISC_FALSE);
}
#endif

static isc_uint32_t
dns64_ttl(dns_db_t *db, dns_dbversion_t *version) {
	dns_dbnode_t *node = NULL;
	dns_rdata_soa_t soa;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	dns_rdataset_t rdataset;
	isc_result_t result;
	isc_uint32_t ttl = ISC_UINT32_MAX;

	dns_rdataset_init(&rdataset);

	result = dns_db_getoriginnode(db, &node);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	result = dns_db_findrdataset(db, node, version, dns_rdatatype_soa,
				     0, 0, &rdataset, NULL);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	result = dns_rdataset_first(&rdataset);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	dns_rdataset_current(&rdataset, &rdata);
	result = dns_rdata_tostruct(&rdata, &soa, NULL);
	RUNTIME_CHECK(result == ISC_R_SUCCESS);
	ttl = ISC_MIN(rdataset.ttl, soa.minimum);

cleanup:
	if (dns_rdataset_isassociated(&rdataset))
		dns_rdataset_disassociate(&rdataset);
	if (node != NULL)
		dns_db_detachnode(db, &node);
	return (ttl);
}

static isc_boolean_t
dns64_aaaaok(ns_client_t *client, dns_rdataset_t *rdataset,
	     dns_rdataset_t *sigrdataset)
{
	isc_netaddr_t netaddr;
	dns_dns64_t *dns64 = ISC_LIST_HEAD(client->view->dns64);
	unsigned int flags = 0;
	unsigned int i, count;
	isc_boolean_t *aaaaok;

	INSIST(client->query.dns64_aaaaok == NULL);
	INSIST(client->query.dns64_aaaaoklen == 0);
	INSIST(client->query.dns64_aaaa == NULL);
	INSIST(client->query.dns64_sigaaaa == NULL);

	if (dns64 == NULL)
		return (ISC_TRUE);

	if (RECURSIONOK(client))
		flags |= DNS_DNS64_RECURSIVE;

	if (sigrdataset != NULL && dns_rdataset_isassociated(sigrdataset))
		flags |= DNS_DNS64_DNSSEC;

	count = dns_rdataset_count(rdataset);
	aaaaok = isc_mem_get(client->mctx, sizeof(isc_boolean_t) * count);

	isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);
	if (dns_dns64_aaaaok(dns64, &netaddr, client->signer,
			     &ns_g_server->aclenv, flags, rdataset,
			     aaaaok, count)) {
		for (i = 0; i < count; i++) {
			if (aaaaok != NULL && !aaaaok[i]) {
				SAVE(client->query.dns64_aaaaok, aaaaok);
				client->query.dns64_aaaaoklen = count;
				break;
			}
		}
		if (aaaaok != NULL)
			isc_mem_put(client->mctx, aaaaok,
				    sizeof(isc_boolean_t) * count);
		return (ISC_TRUE);
	}
	if (aaaaok != NULL)
		isc_mem_put(client->mctx, aaaaok,
			    sizeof(isc_boolean_t) * count);
	return (ISC_FALSE);
}

#ifdef ORIGINAL_ISC_CODE
#else
static infoblox_redirect_action_t
infoblox_perhaps_intercept(ns_client_t *client,
			   dns_rdatatype_t qtype,
			   dns_rdataset_t *rdataset,
			   dns_db_t **dbp,
			   isc_buffer_t *dbuf,
			   isc_buffer_t *b,
			   dns_dbnode_t **node_p,
			   dns_name_t **fname_p) {
	infoblox_redirect_action_t retaction = ib_redirect_pass;
	isc_netaddr_t srcaddr;
	dns_rdataset_t nxrds;
	infoblox_nxbl_t *cache = NULL;
	unsigned types_active = 0, types_not_matched = 0;
	isc_boolean_t have_searched_cache = ISC_FALSE, have_cached_nxdomain = ISC_FALSE;
	isc_boolean_t have_cached_blacklist = ISC_FALSE;
	infoblox_redirection_type_t type = ib_nxdomain;
	infoblox_redirect_action_t action = ib_redirect_pass;
	const char *pattern = "<null>", *rs_name = "<null>";
	isc_result_t result;

	if (client->peeraddr_valid)
		isc_netaddr_fromsockaddr(&srcaddr, &client->peeraddr);

	// Intercept blacklist
	// Note that, per the PRD, blacklist interception applies regardless
	// of the query type. However, if the action is REDIRECT, and the
	// query type is anything other than 'A', we REFUSE the lookup.
	if (!client->view->blk_skipped &&
	    rdataset != NULL &&
	    (!WANTDNSSEC(client) || client->view->blacklist_break_dnssec)) {
		isc_boolean_t matched = ISC_FALSE;

		if (cache == NULL && client->view->nxblcache != NULL) {
			infoblox_nxbl_attach(client->view->nxblcache, &cache);
		}
		if (cache != NULL) {
			result = infoblox_nxbl_find(cache, client->query.qname, NULL,
						    &type, &action, &rs_name, &pattern);
			have_searched_cache = ISC_TRUE;
			if (result == ISC_R_SUCCESS) {
				if (type == ib_nxdomain_blacklist_nonexistent) {
					// This is a universal whitelist entry
					retaction = ib_redirect_pass;
					goto cleanup;
				} else if (type == ib_blacklist) {
					matched = ISC_TRUE;
					have_cached_blacklist = ISC_TRUE;
				} else if (type == ib_nxdomain) {
					// Since we're working on blacklists right now, we can't
					// use this entry here. However, remember that we have it,
					// in case we look for NXDOMAIN matches below.
					have_cached_nxdomain = ISC_TRUE;
				}
			}
		}
		if (! matched) {
			matched = infoblox_check_blacklist(client->view, client->query.qname,
							   &action, &pattern, &rs_name);
			// If we find a blacklist rule match, the action and pattern
			// will no longer reflect a cached NXDOMAIN entry we might
			// have found above.
			if (matched)
				have_cached_nxdomain = ISC_FALSE;
			if (matched && cache) {
				result = infoblox_nxbl_add(cache, client->query.qname,
							   ib_blacklist, action,
							   rs_name, pattern);
				if (result != ISC_R_SUCCESS) {
					char *qname = NULL;
					(void)dns_name_tostring(client->query.qname, &qname,
								   client->view->mctx);
					isc_log_write(ns_g_lctx,
						      NS_LOGCATEGORY_CLIENT,
						      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
						      "Error adding blacklist match result for \'%s\' to cache for view \'%s\': %s",
						      qname ? qname : "<unknown>",
						      client->view->name, isc_result_totext(result));
					if (qname)
						isc_mem_free(client->view->mctx, qname);
				} else {
					have_cached_blacklist = ISC_TRUE;
				}
			} else if (! matched) {
				types_not_matched += 1;
			}
		}

		if (matched) {
			char *qname = NULL;
			char onbuf[ISC_NETADDR_FORMATSIZE] = "<unknown>";

			// XXXXXX statistics ?
			if ((action == ib_redirect_redirect || action == ib_redirect_modify) &&
			    (client->view->blk_action == ib_redirect_refused ||
			     qtype != dns_rdatatype_a)) {
				action = ib_redirect_refused;
			}
			if (client->view->blk_log_query) {
				result = dns_name_tostring(client->query.qname, &qname,
							   client->view->mctx);
				if (client->peeraddr_valid)
				  isc_netaddr_format(&srcaddr, onbuf, sizeof(onbuf));
			}
			if ((ib_redirect_redirect == action || action == ib_redirect_modify) &&
			    (IS_BLACKLIST_CONFIGURED(ns_g_server) ||
			     IS_BLACKLIST_CONFIGURED(client->view))) {
				result = infoblox_get_blacklist_redirect_rdataset(client->view,
										  client->query.qname,
										  node_p, dbp, rdataset,
										  &nxrds, qtype);
				if (ISC_R_SUCCESS == result) {
					/* log query */
					if (client->view->blk_log_query) {
						isc_log_write(ns_g_lctx,
                                 NS_LOGCATEGORY_CLIENT,
                                 NS_LOGMODULE_QUERY, ISC_LOG_INFO,
                                 "Intercept (blacklist rule): \'%s\' matched ruleset-pattern (redirect): \'%s\' -- \'%s\' (%s)",
                                 qname ? qname : "<unknown>",
                                 rs_name, pattern,
                                 onbuf);
					}
					if (qname) {
						isc_mem_free(client->view->mctx, qname);
						qname = NULL;
					}
					// If no fname, copy qname to fname for ANSWER section
					if (fname_p) {
						if (*fname_p == NULL) {
							*fname_p = query_newname(client, dbuf, b);
						}
						result = dns_name_copy(client->query.qname, *fname_p, NULL);
						if (ISC_R_SUCCESS != result) {
							infoblox_log(3, "Error copying qname into fname: %s",
								     isc_result_totext(result));
						}
					}
					retaction = ib_redirect_redirect;
					client->query.attributes |= NS_QUERYATTR_SKIPRPZ;
					goto cleanup;
				} else {
					infoblox_log_name(1, "Intercept (blacklist) failure",
							  client->query.qname,
							  isc_result_totext(result));
				}
			} else if (ib_redirect_refused == action) {
				if (client->view->blk_log_query) {
					isc_log_write(ns_g_lctx,
                             NS_LOGCATEGORY_CLIENT,
                             NS_LOGMODULE_QUERY, ISC_LOG_INFO,
                             "Intercept (blacklist rule): \'%s\' matched ruleset-pattern (refusal): \'%s\' -- \'%s\' (%s)",
                             qname ? qname : "<unknown>",
                             rs_name, pattern,
                             onbuf);
				}
				if (qname) {
					isc_mem_free(client->view->mctx, qname);
					qname = NULL;
				}
				retaction = ib_redirect_refused;
				client->query.attributes |= NS_QUERYATTR_SKIPRPZ;
				goto cleanup;
			} else if (ib_redirect_pass == action) {
				if (client->view->blk_log_query) {
				  isc_log_write(ns_g_lctx,
                           NS_LOGCATEGORY_CLIENT,
                           NS_LOGMODULE_QUERY, ISC_LOG_INFO,
                           "Intercept (blacklist rule): \'%s\' matched ruleset-pattern (pass): \'%s\' -- \'%s\' (%s)",
                           qname ? qname : "<unknown>",
                           rs_name, pattern,
                           onbuf);
				}
				client->query.attributes |= NS_QUERYATTR_SKIPRPZ;
			}
			if (qname) {
				isc_mem_free(client->view->mctx, qname);
				qname = NULL;
			}
		}
	}
	if (!client->view->nx_skipped &&
	    (qtype == dns_rdatatype_a || qtype == dns_rdatatype_aaaa) &&
	    rdataset != NULL &&
	    (IS_NXDOMAIN_CONFIGURED(ns_g_server) ||
	     IS_NXDOMAIN_CONFIGURED(client->view)) &&
	    (!WANTDNSSEC(client) || client->view->nxdomain_break_dnssec)) {
		isc_boolean_t matched = ISC_FALSE;

		if (have_cached_nxdomain) {
			matched = ISC_TRUE;
		} else if (have_searched_cache) {
			// No use looking again. Fall through to rule matching.
		} else if (cache || client->view->nxblcache) {
			if (cache == NULL)
				infoblox_nxbl_attach(client->view->nxblcache, &cache);
			if (cache != NULL) {
				result = infoblox_nxbl_find(cache, client->query.qname, NULL,
							    &type, &action, &rs_name, &pattern);
				if (result == ISC_R_SUCCESS) {
					if (type == ib_nxdomain_blacklist_nonexistent) {
						// Universal whitelist entry
						retaction = ib_redirect_pass;
						goto cleanup;
					} else if (type == ib_nxdomain) {
						matched = ISC_TRUE;
					}
				}
			}
		}

		if (! matched) {
			matched = infoblox_check_nxdomain(client->view, client->query.qname,
							  &action, &pattern, &rs_name);
			// If we found a match, then add it to the cache. However,
			// don't overwrite an existing cached blacklist match.
			if (matched && cache && !have_cached_blacklist) {
				result = infoblox_nxbl_add(cache, client->query.qname,
                                                           ib_nxdomain, action,
							   rs_name, pattern);
				if (result != ISC_R_SUCCESS) {
					char qname_buf[DNS_NAME_FORMATSIZE];
					dns_name_format(client->query.qname,
							qname_buf,
							sizeof(qname_buf));
					isc_log_write(ns_g_lctx,
						      NS_LOGCATEGORY_CLIENT,
						      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
						      "Error adding NXDOMAIN match result for \'%s\' to cache for view \'%s\': %s",
						      qname_buf,
						      client->view->name, isc_result_totext(result));
				}
			} else if (! matched) {
				types_not_matched += 1;
			}
		}

		if (matched) {
			char *qname = NULL;
			char onbuf[ISC_NETADDR_FORMATSIZE] = "<unknown>";

			if (client->view->nx_log_query) {
				result = dns_name_tostring(client->query.qname, &qname,
							   client->view->mctx);
				if (client->peeraddr_valid)
					isc_netaddr_format(&srcaddr, onbuf, sizeof(onbuf));
			}
			/* TODO: match stats? */
			if (ib_redirect_redirect == action) {
				result =  infoblox_get_nxdomain_redirect_rdataset(client->view,
										  client->query.qname,
										  node_p, dbp, rdataset,
										  &nxrds, qtype);
				if (ISC_R_SUCCESS == result) {
					/* log query */
					if (client->view->nx_log_query) {
				  		isc_log_write(ns_g_lctx, NS_LOGCATEGORY_CLIENT,
                                 NS_LOGMODULE_QUERY, ISC_LOG_INFO,
                                 "Intercept (NXDOMAIN rule): \'%s\' matched ruleset-pattern (redirect): \'%s\' -- \'%s\' (%s)",
                                 qname ? qname : "<unknown>",
                                 rs_name, pattern,
                                 onbuf);
						if (qname) {
							isc_mem_free(client->view->mctx, qname);
							qname = NULL;
						}
					}
					/* There is no fname as there is nothing in the db that matches the query.
					 * Explicitly set fname to be qname so qname appears in
					 * the answer section in the intercepted matched response.
					 */
					if (fname_p) {
					  	if (!*fname_p) {
							*fname_p = query_newname(client, dbuf, b);
						}
						result = dns_name_copy(client->query.qname, *fname_p, NULL);
						if (ISC_R_SUCCESS != result) {
							infoblox_log(3, "Error copying qname into fname: %s",
								     isc_result_totext(result));
						}
					}
					retaction = ib_redirect_redirect;
					client->query.attributes |= NS_QUERYATTR_SKIPRPZ;
					goto cleanup;
				} else if (ISC_R_NOTFOUND == result) {
					goto cleanup;
				} else {
					infoblox_log_name(1, "Intercept (NXDOMAIN) failure",
							  client->query.qname,
							  isc_result_totext(result));
				}
			} else if (action == ib_redirect_pass) {
#if 0
				// GUI choice for NXDOMAIN says "log redirected queries",
				// and that reflects the PRD.
				// A bit odd, and just in case we do want to log pass rules,
				// we keep this code around, but deactivated.
				if (client->view->nx_log_query) {
					isc_log_write(ns_g_lctx, NS_LOGCATEGORY_CLIENT,
                             NS_LOGMODULE_QUERY, ISC_LOG_INFO,
                             "Intercept (NXDOMAIN rule): \'%s\' matched ruleset-pattern (pass): \'%s\' -- \'%s\' (%s)",
                             qname ? qname : "<unknown>",
                             rs_name, pattern,
                             onbuf);
				}
#endif
				// Set flag to avoid NXDOMAIN processing after query
				client->query.attributes |= NS_QUERYATTR_SKIPNXDOMAINREDIRECT;
				client->query.attributes |= NS_QUERYATTR_SKIPRPZ;
			} else if (action == ib_redirect_modify) {
				// We now want to redirect if the actual response is an
				// NXDOMAIN. Since that's the default behavior, we don't need
				// to do anything, except log if so configured.
				retaction = ib_redirect_modify;
				// Note: In this case, we do NOT set NS_QUERYATTR_SKIPRPZ.
				//       We must defer decision on whether or not to override
				//       RPZ until we know if we got an DNS_R_NCACHENXDOMAIN
				//       for the lookup.
				if (client->view->nx_log_query) {
					isc_log_write(ns_g_lctx, NS_LOGCATEGORY_CLIENT, NS_LOGMODULE_QUERY, ISC_LOG_INFO,
						      "Intercept (NXDOMAIN rule): \'%s\' matched ruleset-pattern (modify): \'%s\' -- \'%s\' (%s)",
						      qname ? qname : "<unknown>",
						      rs_name, pattern,
						      onbuf);
				}
			}
			if (qname) {
				isc_mem_free(client->view->mctx, qname);
				qname = NULL;
			}
		}
	}

 cleanup:
	if (types_not_matched > 0) {
		// Is blacklist checking active ?  We only check data that is NOT
		// unique to this query.
		if (!client->view->blk_skipped)
			types_active += 1;
		// Is NXDOMAIN checking active ?  Again ignore data unique to query.
		if (!client->view->nx_skipped &&
		    (IS_NXDOMAIN_CONFIGURED(ns_g_server) ||
		     IS_NXDOMAIN_CONFIGURED(client->view)))
			types_active += 1;
	}
	// If all active types (blacklist and/or NXDOMAIN) were checked, and
	// we didn't find any matching rule, we can add a whitelist entry, so
	// that we can skip rule lookup for this name in the future.
	if (cache && types_active > 0 && types_not_matched == types_active && retaction == ib_redirect_pass) {
		result = infoblox_nxbl_add(cache, client->query.qname,
					   ib_nxdomain_blacklist_nonexistent, ib_redirect_pass,
					   NULL, NULL);
		if (result != ISC_R_SUCCESS) {
			char qname_buf[DNS_NAME_FORMATSIZE];
			dns_name_format(client->query.qname, qname_buf,
					sizeof(qname_buf));
			isc_log_write(ns_g_lctx,
				      NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
				      "Error adding whitelist entry for \'%s\' to cache for view \'%s\': %s",
				      qname_buf,
				      client->view->name, isc_result_totext(result));
		}
	}

	if (cache)
		infoblox_nxbl_detach(&cache);

	return (retaction);
}

// Parental control subscriber specific logging for fake addresses, and
// warnings. Both require the original name of the query.
// TODO-PARENTALCONTROL: RPZ subscriber specific log for 'PXY' that also be
// incorporating into this function.
static isc_result_t
ib_subscriber_rpz_log_rewrite(ns_client_t *client, ib_categories_t categories,
			      ib_subscriber_action_t action, isc_boolean_t warn) {

	ib_subscriber_t *sub = client->subscriber;
	char cat_txt[sizeof("WRN_0x0123456789abcdef0123456789abcdef")];
	const char *category_txt = cat_txt;
	int n = 0;

	if (warn) {
		n = snprintf(cat_txt, sizeof(cat_txt),
			     "WRN_0x%016" ISC_PRINT_QUADFORMAT "x"
			     "%016" ISC_PRINT_QUADFORMAT "x",
			     (isc_uint64_t) (categories >> 64),
			     (isc_uint64_t) categories);
	} else {
		if (sub->categories == IB_CATEGORY_REDIRECT_ALL) {
			// Note: "REDIRECT_ALL" is most preferred so
			// this should come first.
			category_txt = "ALL";
		} else if (action == IB_SUBSCRIBER_ACTION_REDIRECT)
			category_txt = "BL";
		else {
			n = snprintf(cat_txt, sizeof(cat_txt),
			     "0x%016" ISC_PRINT_QUADFORMAT "x"
			     "%016" ISC_PRINT_QUADFORMAT "x",
			     (isc_uint64_t) (categories >> 64),
			     (isc_uint64_t) categories);
		     }
	}
	if (n < 0 || n >= (int) sizeof(cat_txt)) {
		// shouldn't happen as we passed a
		// sufficient size of buffer.
		return (ISC_R_UNEXPECTED);
	}
	rpz_log_rewrite(client, ISC_FALSE, DNS_RPZ_POLICY_CNAME,
			DNS_RPZ_TYPE_QNAME, NULL,
			client->query.origqname, NULL, 0, NULL,
			IB_RPZ_SEV_MAJOR, category_txt, warn);

	return (ISC_R_SUCCESS);
}

/*
 * This function returns the position of the least significant bit set
 * in 'pc'. It uses a GCC extension __builtin_ctz() which is only
 * defined for unsigned ints and the category mask is 128-bit, so it
 * requires this helper function.
 */
static unsigned int
infoblox_pc_get_lowest_category(ib_categories_t pc) {
	unsigned int t;

	REQUIRE(pc != 0U);
	INSIST(sizeof(pc) == 16);

	t = pc & 0xffffffffU;
	if (t != 0U) {
		return (__builtin_ctz(t));
	}

	t = (pc >> 32) & 0xffffffffU;
	if (t != 0U) {
		return (32 + __builtin_ctz(t));
	}

	t = (pc >> 64) & 0xffffffffU;
	if (t != 0U) {
		return (64 + __builtin_ctz(t));
	}

	t = (pc >> 96) & 0xffffffffU;
	INSIST(t != 0U);
	return (96 + __builtin_ctz(t));
}

// Helper for infoblox_pc_category_match(), retrieve the categories and other
// attributes for the qname.  It also handles special considerations for DCA.
static isc_result_t
ib_get_qname_categories(ns_client_t *client, ib_categories_t *categoriesp,
			isc_boolean_t *dynamicp, isc_boolean_t *unknownp,
			isc_boolean_t *caidp)
{
	isc_result_t result;
	isc_boolean_t pxy_all_wl = FALSE;
	ib_subscriber_t	*sub = client->subscriber;

	dns_rpz_st_t *st;
	st = client->query.rpz_st;

	// set rpz whitelist proxy-all true if needed to get synthetic bit set in categories
	if (client->view->infoblox_pc_enable && client->subscriber != NULL &&
			st != NULL && st->m.policy == DNS_RPZ_POLICY_PASSTHRU &&
			client->view->infoblox_pc_proxy_all_whitelist) {
		pxy_all_wl = TRUE;
	}


	// Note that we query for the original QNAME, not the updated
	// QNAME we get after iterating through CNAMEs, etc.
	result = infoblox_subscriber_categories(client->query.origqname,
						categoriesp, dynamicp,
						unknownp, caidp, pxy_all_wl);
	if (result != ISC_R_SUCCESS) {
		char name[DNS_NAME_FORMATSIZE];

		dns_name_format(client->query.qname, name, sizeof(name));
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_QUERY, ISC_LOG_WARNING,
			      "infoblox_subscriber_categories failed to "
			      "return information for %s", name);
		return (result);
	}

    // If we get categories 0 with return success then categorization
    // module isn't initialized yet. Hence resolving this pc subscriber's
    // query normally without sending the category result in resp. This may
    // happen if we recv a pc subscriber query before we initialize
    // the zVelo categorization engine using its db file.
    if (*categoriesp == 0x0) {
        sub->matched_categories = *categoriesp;
        return (result);
    }

        
	if (isc_log_wouldlog(ns_g_lctx, ISC_LOG_DEBUG(3)))
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_QUERY, ISC_LOG_DEBUG(3),
			      "infoblox_subscriber_categories succeeded "
			      "categories %016" ISC_PRINT_QUADFORMAT "x"
			      "%016" ISC_PRINT_QUADFORMAT "x, "
			      "dynamic %d, unknown %d",
			      (isc_uint64_t)(*categoriesp >> 64),
			      (isc_uint64_t)*categoriesp, *dynamicp, *unknownp);

	// If we successfully get rating information for the qname
	// and this system enables DNS cache acceleration (DCA),
	// we'll pass DCA the category bitmap in the form of custom
	// EDNS option, except:
	// - when other subscribers can ignore the bitmaps due to
	//   subscriber specific conditions
	// - for TCP or TSIG-signed queries/responses
	// The check for edns_category_opt is redundant as this function
	// should not be called more than once, but we make sure the
	// wrong type of option is never chosen even if there's a bug
	// in the caller side.

	if (infoblox_get_set_z_bit() && client->signer == NULL &&
	    !TCP_CLIENT(client) && sub->edns_category_opt == 0)
	{
		// If EDNS OPT RR is already expected to be added
		// we use CATEGORY2 option; otherwise we'll deceive
		// the client's send logic into including OPT RR with
		// CATEGORY1 option.  The choice is critical as it
		// determines how DCA removes the option.
		sub->edns_category_opt = DNS_OPT_IB_SUBSCRIBER_CATEGORY2;
		if ((client->attributes & NS_CLIENTATTR_WANTOPT) == 0) {
			client->attributes |= NS_CLIENTATTR_WANTOPT;
			sub->edns_category_opt =
				DNS_OPT_IB_SUBSCRIBER_CATEGORY1;
		}
		sub->matched_categories = *categoriesp;
	}

	return (ISC_R_SUCCESS);
}

// Helper for infoblox_pc_category_match(), replace the current answer with
// a faked CNAME for redirection.
static isc_result_t
ib_subscriber_redirect(ns_client_t *client, ib_categories_t matched,
		       dns_name_t **fnamep, isc_buffer_t *dbuf)
{
	ib_subscriber_t	*sub = client->subscriber;
	ib_subscriber_action_t action = sub->action;
	isc_result_t result = ISC_R_SUCCESS;

	// Identify the redirect target name. If we're returning
	// a faked CNAME due to a "redirect all" policy of the
	// subscriber or qname-based category match, we use
	// the CNAME target for the subscriber; otherwise
	// (i.e., when the BW list action suggests redirect)
	// we use the common name configured for the view.
	// Note that BW list is preferred over category match,
	// but if we're returning CNAME due to the BW list
	// no category matching was performed, so we can simply
	// check 'matched' below and don't have to check
	// 'action' explicitly.
	dns_name_t tmpname;
	dns_name_t *cname_src = client->view->infoblox_pc_blocking_cname;
	if (matched != 0 || sub->categories == IB_CATEGORY_REDIRECT_ALL) {
		isc_region_t r;
		dns_name_init(&tmpname, NULL);
		r.base = sub->redirect_name.ndata;
		r.length = sub->redirect_name.nlen;
		dns_name_fromregion(&tmpname, &r);
		cname_src = &tmpname;
	}

	// Log CEF-style message for redirect, "identical
	// to RPZ CEF messages" as per the DS.
	result = ib_subscriber_rpz_log_rewrite(client, matched, action,
					       ISC_FALSE);
	if (result != ISC_R_SUCCESS)
		return (result);

	// Insert the redirect CNAME into our answer.  Here,
	// we need to make sure the underlying name data is
	// valid until the final response is sent, so we use
	// passed 'fnamep' as a placeholder and 'keep' it.
	// Note that *fnamep should have a sufficiently large
	// buffer and dns_name_copy will clear it, so the copy
	// must always succeed.
	dns_name_t *tname = *fnamep;
	result = dns_name_copy(cname_src, tname, NULL);
	RUNTIME_CHECK(result == ISC_R_SUCCESS);
	query_keepname(client, tname, dbuf);
	result = query_add_cname(client, client->query.qname, tname,
				 dns_trust_authanswer, 0);
	if (result != ISC_R_SUCCESS) {
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
			      "Failed to add CNAME for query");
		return (result);
	}
	// Replace qname.  It needs to be valid until it's
	// replaced again or the end of query processing, so
	// we reset *fnamep to prevent it from being freed.
	*fnamep = NULL;
	ns_client_qnamereplace(client, tname);

	// Turn off DNSSEC because the results cannot verify.
	client->attributes &=
		~(NS_CLIENTATTR_WANTDNSSEC | NS_CLIENTATTR_WANTAD);
	client->query.attributes |= NS_QUERYATTR_PC_CNAME;

	return (ISC_R_SUCCESS);
}

// Given that we have a subscriber entry, figure out if we need to return a
// faked CNAME redirect (so-called 'block', a confusing term) for this query
// name, and create and return it if we do.
// 'fnamep' will be used as a placeholder for the CNAME target whose underlying
// data is guaranteed to be valid until the response is sent out (stored in
// *dbuf).  If *fnamep is used for this purpose, it will be NULL-cleared to
// prevent it from being reused or accidentally freed.
// If no redirection happens, check if we need to perform additional lookup
// for the faked answer address (so-called "proxying", another confusing term).
// If so, indicate the need via '*need_request_addressp'.
static isc_result_t
infoblox_pc_category_match(ns_client_t *client, dns_name_t **fnamep,
			   isc_buffer_t *dbuf,
			   isc_boolean_t *need_request_addrsp)
{
	isc_result_t result = ISC_R_SUCCESS;
	ib_categories_t categories = 0;
	isc_boolean_t dynamic = ISC_FALSE;
	isc_boolean_t unknown = ISC_FALSE;
	isc_boolean_t caid = ISC_FALSE;
	ib_subscriber_action_t action = client->subscriber->action;
	ib_subscriber_t	*sub = client->subscriber;
	isc_boolean_t caic = ((sub->categories & IB_CATEGORY_CAIC) != 0);
	ib_subscriber_edns0_client_id_t local_id;
	const ib_subscriber_edns0_client_id_t *local_idp;

	// We may now need to retrieve subscriber-independent attributes for
	// the query name.  Since it's expensive, we try that only when it
	// may matter:
	// 1. subscriber has non-0 category or non-0 pc_category bitmap, to
	//    match it with qname categories.
	// 2. subscriber indicated the possible need for a dynamic fake answer
	//    (so-called "proxy") to match corresponding qname attributes:
	//    dynamic, unknown or caid or an RPZ passthrough when proxy RPZ
	//    passthrough is enabled (RFE-9982).
	// 3. Subscriber that always return a fake answer must adhere to a
	//    list of categories  that will never use a fake answer (proxy).
	local_idp = ib_get_localid(&local_id, client);
	if (sub->categories != 0 || sub->pc_categories != 0 ||
	    sub->dynamic || sub->unknown || caic ||
	    (client->view->infoblox_pc_proxy_rpz_passthru_enable && local_idp != NULL) ||
	    (sub->noproxy_categories != 0 && sub->proxy_all)) {
		result = ib_get_qname_categories(client, &categories, &dynamic,
						 &unknown, &caid);
	}

	// return fail if categorization is not successful
	// why are we returning from here not just below ib_get_qname_categories,
	// it is so that we try to get categories for whitelist, if in case 
	// failed then we resolve normally just above IB_SUBSCRIBER_ACTION_PASSTHRU case.
	if (result != ISC_R_SUCCESS)
		return (result);

        // NIOS-77480: If we get categories all-0 with return success then 
        // categorization module isn't initialized yet. Hence resolve normally.
        if (categories == 0 && !action) {
		return (result);
	}

	/* NIOS-83054 / SNIN-157 - lookup for faked answer addresses
	 * if the domain categorizes to 44.
	 */
	if ((categories & IB_CUSTOM_CATEGORY_4) != 0 &&
	    client->view->infoblox_pc_proxy_rpz_passthru_enable) {
		*need_request_addrsp = ISC_TRUE;
	}
        
	/* we should redirect,
	 * If we match the block list per subscriber
	 * If we are matching special category bit pattern.
	 */
	if (action == IB_SUBSCRIBER_ACTION_REDIRECT ||
	    sub->categories == IB_CATEGORY_REDIRECT_ALL)
		return (ib_subscriber_redirect(client, 0, fnamep, dbuf));

        // If the query name was known to be subject to 'pass through'
        // subscriber action, no fake happens here.
        if (action == IB_SUBSCRIBER_ACTION_PASSTHRU)
        {
                if (categories != 0) {
                        client->rated_category =
                                infoblox_pc_get_lowest_category(categories);
                }
                return (ISC_R_SUCCESS);
        }

	/* we should redirect,
	 * If we successfully get rating information for the qname and
	 * qname categories match subscriber specified ones.
	 */
	ib_categories_t matched = categories & sub->categories;
	if (matched != 0)
		return (ib_subscriber_redirect(client, matched, fnamep, dbuf));

	// Check the need to request lookup for faked answer addresses.  This
	// happens when the subscriber info requested so ('dynamic', 
	// or 'caid' qnames).
	// CAID-categorized qname gets a dynamic fake answer ONLY if CAIC bit
	// is set in the subscriber PC policy.
	if ((sub->dynamic && dynamic) || (caid && caic)) {
		*need_request_addrsp = ISC_TRUE;
		return (ISC_R_SUCCESS);
	}

	// If the qname pc_categories match subscriber specified ones, we
	// should log a warning CEF-style message.
	ib_categories_t pc_matched = categories & sub->pc_categories;
	if (pc_matched != 0) {
		result = ib_subscriber_rpz_log_rewrite(client, pc_matched,
						       action, ISC_TRUE);

		if (result != ISC_R_SUCCESS)
			return (result);

		// Check the need to request a lookup for a faked answer.
		// Pass the pc_categories lowest bit to the lookup request.
		if (sub->dynamic) {
			client->warning_vip = TRUE;
			client->rated_category =
				infoblox_pc_get_lowest_category(pc_matched);
			*need_request_addrsp = ISC_TRUE;
			return (ISC_R_SUCCESS);
		}
	}

	// Check the need to request lookup for faked answer addresses.  This
	// happens when the subscriber info requested unconditionally, but
	// only when the qname categories have no match in the
	// subscriber-specified exceptions ('noproxy').
	if ((categories & sub->noproxy_categories) == 0 && sub->proxy_all) {
		*need_request_addrsp = ISC_TRUE;
	}

	return (ISC_R_SUCCESS);
}

static isc_result_t
infoblox_pc_proxy_rdataset(ns_client_t *client,
			   dns_rdatatype_t qtype,
			   dns_rdataclass_t qclass,
			   dns_rdataset_t **proxy_rdataset,
			   isc_sockaddr_t *proxy_addr)
{
	isc_result_t result;
	isc_region_t r;
	isc_buffer_t *buffer = NULL;
	dns_rdata_t *proxy_rdata = NULL;
	dns_rdatalist_t *proxy_rdatalist = NULL;

	REQUIRE(proxy_rdataset != NULL);
	REQUIRE(*proxy_rdataset == NULL);
	REQUIRE((qtype == dns_rdatatype_aaaa &&
		 proxy_addr->type.sa.sa_family == AF_INET6) ||
		(qtype == dns_rdatatype_a &&
		 proxy_addr->type.sa.sa_family == AF_INET));

	/*%
	 * Record the provided proxy address into an rdataset
	 * for future insertion into the outgoing answer.
	 * Based on query_dns64().
	 */

	result = isc_buffer_allocate(client->mctx, &buffer,
				     qtype == dns_rdatatype_a ?
				     sizeof(struct in_addr) :
				     sizeof(struct in6_addr));
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	result = dns_message_gettemprdataset(client->message, proxy_rdataset);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	result = dns_message_gettemprdatalist(client->message,
					      &proxy_rdatalist);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	result = dns_message_gettemprdata(client->message, &proxy_rdata);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	dns_rdata_init(proxy_rdata);
	dns_rdatalist_init(proxy_rdatalist);

	proxy_rdatalist->rdclass = qclass;
	proxy_rdatalist->type = qtype;
	// Each query needs a separate proxy, so the TTL should be 0.  But
	// our system (beyond the scope of 'named') is not capable enough to
	// handle the resulting volume of queries, so we arbitrarily increase
	// it to 5 until the poor performance is fixed.  See NIOS-66801.
	// We hardcode it here as this is the only place we use this value.
	proxy_rdatalist->ttl = 5;
	isc_buffer_availableregion(buffer, &r);

	if (qtype == dns_rdatatype_a)
		isc_buffer_putmem(buffer,
				  (unsigned char *) &proxy_addr->type.sin.sin_addr.s_addr,
				  sizeof(proxy_addr->type.sin.sin_addr.s_addr));
	else
		isc_buffer_putmem(buffer,
				  (unsigned char *) &proxy_addr->type.sin6.sin6_addr.s6_addr,
				  sizeof(proxy_addr->type.sin6.sin6_addr.s6_addr));

	dns_rdata_fromregion(proxy_rdata, qclass, qtype, &r);
	ISC_LIST_APPEND(proxy_rdatalist->rdata, proxy_rdata, link);
	proxy_rdata = NULL;

	result = dns_rdatalist_tordataset(proxy_rdatalist, *proxy_rdataset);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	dns_message_takebuffer(client->message, &buffer);
	return (result);

 cleanup:
	if (buffer != NULL)
		isc_buffer_free(&buffer);

	if (proxy_rdata != NULL)
		dns_message_puttemprdata(client->message, &proxy_rdata);

	if (*proxy_rdataset != NULL)
		dns_message_puttemprdataset(client->message, proxy_rdataset);

	if (proxy_rdatalist != NULL) {
		for (proxy_rdata = ISC_LIST_HEAD(proxy_rdatalist->rdata);
		     proxy_rdata != NULL;
		     proxy_rdata = ISC_LIST_HEAD(proxy_rdatalist->rdata))
		{
			ISC_LIST_UNLINK(proxy_rdatalist->rdata,
					proxy_rdata, link);
			dns_message_puttemprdata(client->message, &proxy_rdata);
		}
		dns_message_puttemprdatalist(client->message, &proxy_rdatalist);
	}

	return (result);
}
#endif

/*
 * Look for the name and type in the redirection zone.  If found update
 * the arguments as appropriate.  Return ISC_TRUE if a update was
 * performed.
 *
 * Only perform the update if the client is in the allow query acl and
 * returning the update would not cause a DNSSEC validation failure.
 */
static isc_result_t
redirect(ns_client_t *client, dns_name_t *name, dns_rdataset_t *rdataset,
	 dns_dbnode_t **nodep, dns_db_t **dbp, dns_dbversion_t **versionp,
	 dns_rdatatype_t qtype)
{
	dns_db_t *db = NULL;
	dns_dbnode_t *node = NULL;
	dns_fixedname_t fixed;
	dns_name_t *found;
	dns_rdataset_t trdataset;
	isc_result_t result;
	dns_rdatatype_t type;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;
	ns_dbversion_t *dbversion;

	CTRACE(ISC_LOG_DEBUG(3), "redirect");

	if (client->view->redirect == NULL)
		return (ISC_R_NOTFOUND);

	dns_fixedname_init(&fixed);
	found = dns_fixedname_name(&fixed);
	dns_rdataset_init(&trdataset);

	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, &client->ecs, NULL);

	if (WANTDNSSEC(client) && dns_db_iszone(*dbp) && dns_db_issecure(*dbp))
		return (ISC_R_NOTFOUND);

	if (WANTDNSSEC(client) && dns_rdataset_isassociated(rdataset)) {
		if (rdataset->trust == dns_trust_secure)
			return (ISC_R_NOTFOUND);
		if (rdataset->trust == dns_trust_ultimate &&
		    (rdataset->type == dns_rdatatype_nsec ||
		     rdataset->type == dns_rdatatype_nsec3))
			return (ISC_R_NOTFOUND);
		if ((rdataset->attributes & DNS_RDATASETATTR_NEGATIVE) != 0) {
			for (result = dns_rdataset_first(rdataset);
			     result == ISC_R_SUCCESS;
			     result = dns_rdataset_next(rdataset)) {
				dns_ncache_current(rdataset, found, &trdataset);
				type = trdataset.type;
				dns_rdataset_disassociate(&trdataset);
				if (type == dns_rdatatype_nsec ||
				    type == dns_rdatatype_nsec3 ||
				    type == dns_rdatatype_rrsig)
					return (ISC_R_NOTFOUND);
			}
		}
	}

	result = ns_client_checkaclsilent(client, NULL,
				 dns_zone_getqueryacl(client->view->redirect),
					  ISC_TRUE);
	if (result != ISC_R_SUCCESS)
		return (ISC_R_NOTFOUND);

	result = dns_zone_getdb(client->view->redirect, &db);
	if (result != ISC_R_SUCCESS)
		return (ISC_R_NOTFOUND);

	dbversion = query_findversion(client, db);
	if (dbversion == NULL) {
		dns_db_detach(&db);
		return (ISC_R_NOTFOUND);
	}

	/*
	 * Lookup the requested data in the redirect zone.
	 */
	result = dns_db_findext(db, client->query.qname, dbversion->version,
				qtype, DNS_DBFIND_NOZONECUT, client->now,
				&node, found, &cm, &ci, &trdataset, NULL);
	if (result == DNS_R_NXRRSET || result == DNS_R_NCACHENXRRSET) {
		if (dns_rdataset_isassociated(rdataset))
			dns_rdataset_disassociate(rdataset);
		if (dns_rdataset_isassociated(&trdataset))
			dns_rdataset_disassociate(&trdataset);
		goto nxrrset;
	} else if (result != ISC_R_SUCCESS) {
		if (dns_rdataset_isassociated(&trdataset))
			dns_rdataset_disassociate(&trdataset);
		if (node != NULL)
			dns_db_detachnode(db, &node);
		dns_db_detach(&db);
		return (ISC_R_NOTFOUND);
	}

	CTRACE(ISC_LOG_DEBUG(3), "redirect: found data: done");
	dns_name_copy(found, name, NULL);
	if (dns_rdataset_isassociated(rdataset))
		dns_rdataset_disassociate(rdataset);
	if (dns_rdataset_isassociated(&trdataset)) {
		dns_rdataset_clone(&trdataset, rdataset);
		dns_rdataset_disassociate(&trdataset);
	}
 nxrrset:
	if (*nodep != NULL)
		dns_db_detachnode(*dbp, nodep);
	dns_db_detach(dbp);
	dns_db_attachnode(db, node, nodep);
	dns_db_attach(db, dbp);
	dns_db_detachnode(db, &node);
	dns_db_detach(&db);
	*versionp = dbversion->version;

	client->query.attributes |= (NS_QUERYATTR_NOAUTHORITY |
				     NS_QUERYATTR_NOADDITIONAL);

	return (result);
}

static isc_result_t
redirect2(ns_client_t *client, dns_name_t *name, dns_rdataset_t *rdataset,
	  dns_dbnode_t **nodep, dns_db_t **dbp, dns_dbversion_t **versionp,
	  dns_rdatatype_t qtype, isc_boolean_t *is_zonep)
{
	dns_db_t *db = NULL;
	dns_dbnode_t *node = NULL;
	dns_fixedname_t fixed;
	dns_fixedname_t fixedredirect;
	dns_name_t *found, *redirectname;
	dns_rdataset_t trdataset;
	isc_result_t result;
	dns_rdatatype_t type;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;
	dns_dbversion_t *version = NULL;
	dns_zone_t *zone = NULL;
	isc_boolean_t is_zone;
	unsigned int options;

	CTRACE(ISC_LOG_DEBUG(3), "redirect2");

	if (client->view->redirectzone == NULL)
		return (ISC_R_NOTFOUND);

	if (dns_name_issubdomain(name, client->view->redirectzone))
		return (ISC_R_NOTFOUND);

	dns_fixedname_init(&fixed);
	found = dns_fixedname_name(&fixed);
	dns_rdataset_init(&trdataset);

	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client, &client->ecs, NULL);

	if (WANTDNSSEC(client) && dns_db_iszone(*dbp) && dns_db_issecure(*dbp))
		return (ISC_R_NOTFOUND);

	if (WANTDNSSEC(client) && dns_rdataset_isassociated(rdataset)) {
		if (rdataset->trust == dns_trust_secure)
			return (ISC_R_NOTFOUND);
		if (rdataset->trust == dns_trust_ultimate &&
		    (rdataset->type == dns_rdatatype_nsec ||
		     rdataset->type == dns_rdatatype_nsec3))
			return (ISC_R_NOTFOUND);
		if ((rdataset->attributes & DNS_RDATASETATTR_NEGATIVE) != 0) {
			for (result = dns_rdataset_first(rdataset);
			     result == ISC_R_SUCCESS;
			     result = dns_rdataset_next(rdataset)) {
				dns_ncache_current(rdataset, found, &trdataset);
				type = trdataset.type;
				dns_rdataset_disassociate(&trdataset);
				if (type == dns_rdatatype_nsec ||
				    type == dns_rdatatype_nsec3 ||
				    type == dns_rdatatype_rrsig)
					return (ISC_R_NOTFOUND);
			}
		}
	}

	dns_fixedname_init(&fixedredirect);
	redirectname = dns_fixedname_name(&fixedredirect);
	if (dns_name_countlabels(name) > 1U) {
		dns_name_t prefix;
		unsigned int labels = dns_name_countlabels(name) - 1;

		dns_name_init(&prefix, NULL);
		dns_name_getlabelsequence(name, 0, labels, &prefix);
		result = dns_name_concatenate(&prefix,
					      client->view->redirectzone,
					      redirectname, NULL);
		if (result != ISC_R_SUCCESS)
			return (ISC_R_NOTFOUND);
	} else
		dns_name_copy(redirectname, client->view->redirectzone, NULL);

	options = 0;
	result = query_getdb(client, redirectname, qtype, options, &zone,
			     &db, &version, &is_zone);
	if (result != ISC_R_SUCCESS)
		return (ISC_R_NOTFOUND);
	if (zone != NULL)
		dns_zone_detach(&zone);

	/*
	 * Lookup the requested data in the redirect zone.
	 */
	result = dns_db_findext(db, redirectname, version,
				qtype, 0, client->now,
				&node, found, &cm, &ci, &trdataset, NULL);
	if (result == DNS_R_NXRRSET || result == DNS_R_NCACHENXRRSET) {
		if (dns_rdataset_isassociated(rdataset))
			dns_rdataset_disassociate(rdataset);
		if (dns_rdataset_isassociated(&trdataset))
			dns_rdataset_disassociate(&trdataset);
		goto nxrrset;
	} else if (result == ISC_R_NOTFOUND || result == DNS_R_DELEGATION) {
		/*
		 * Cleanup.
		 */
		if (dns_rdataset_isassociated(&trdataset))
			dns_rdataset_disassociate(&trdataset);
		if (node != NULL)
			dns_db_detachnode(db, &node);
		dns_db_detach(&db);
		/*
		 * Don't loop forever if the lookup failed last time.
		 */
		if (!REDIRECT(client)) {
			result = query_recurse(client, qtype, redirectname,
					       NULL, NULL, ISC_TRUE);
			if (result == ISC_R_SUCCESS) {
				client->query.attributes |=
						NS_QUERYATTR_RECURSING;
				client->query.attributes |=
						NS_QUERYATTR_REDIRECT;
				return (DNS_R_CONTINUE);
			}
		}
		return (ISC_R_NOTFOUND);
	} else if (result != ISC_R_SUCCESS) {
		if (dns_rdataset_isassociated(&trdataset))
			dns_rdataset_disassociate(&trdataset);
		if (node != NULL)
			dns_db_detachnode(db, &node);
		dns_db_detach(&db);
		return (ISC_R_NOTFOUND);
	}

	CTRACE(ISC_LOG_DEBUG(3), "redirect2: found data: done");
	/*
	 * Adjust the found name to not include the redirectzone suffix.
	 */
	dns_name_split(found, dns_name_countlabels(client->view->redirectzone),
		       found, NULL);
	/*
	 * Make the name absolute.
	 */
	result = dns_name_concatenate(found, dns_rootname, found, NULL);
	RUNTIME_CHECK(result == ISC_R_SUCCESS);

	dns_name_copy(found, name, NULL);
	if (dns_rdataset_isassociated(rdataset))
		dns_rdataset_disassociate(rdataset);
	if (dns_rdataset_isassociated(&trdataset)) {
		dns_rdataset_clone(&trdataset, rdataset);
		dns_rdataset_disassociate(&trdataset);
	}
 nxrrset:
	if (*nodep != NULL)
		dns_db_detachnode(*dbp, nodep);
	dns_db_detach(dbp);
	dns_db_attachnode(db, node, nodep);
	dns_db_attach(db, dbp);
	dns_db_detachnode(db, &node);
	dns_db_detach(&db);
	*is_zonep = is_zone;
	*versionp = version;

	client->query.attributes |= (NS_QUERYATTR_NOAUTHORITY |
				     NS_QUERYATTR_NOADDITIONAL);

	return (result);
}

static isc_boolean_t
is_v4mapped_address(dns_ecs_t *ecs) {
	const isc_uint8_t v4mapped_prefix[] = {
		0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0xff, 0xff
	};

	if (ecs->addr.family == AF_INET6) {
		if ((ecs->source >= 96) &&
		    (memcmp((isc_uint8_t *) &ecs->addr.type.in6,
			    v4mapped_prefix, 12) == 0))
			return (ISC_TRUE);
	}

	return (ISC_FALSE);
}

#ifdef ORIGINAL_ISC_CODE
#else
static void
log_query_rewrite(ns_client_t *client, dns_rdatatype_t qtype) {
	char qnamebuf[DNS_NAME_FORMATSIZE];
	char orig_qnamebuf[DNS_NAME_FORMATSIZE];
	char typebuf[DNS_RDATATYPE_FORMATSIZE];
	dns_rdatatype_format(qtype, typebuf, sizeof(typebuf));
	dns_name_format(client->query.qname, orig_qnamebuf,
			sizeof(orig_qnamebuf));
	dns_name_format(client->query.ib_qname, qnamebuf, sizeof(qnamebuf));
	ns_client_log(client, NS_LOGCATEGORY_IB_QUERY_REWRITE,
		      NS_LOGMODULE_QUERY, ISC_LOG_INFO,
		      "rewriting query name '%s' to '%s', type %s",
		      orig_qnamebuf, qnamebuf, typebuf);
}

static void
release_orig_name(ns_client_t *client) {
	if (client->query.ib_orig_qname != NULL &&
	    (client->query.attributes & NS_QUERYATTR_ORIGQNAMESTORED) == 0) {
		query_releasename(client, &client->query.ib_orig_qname);
	}
	client->query.ib_orig_qname = NULL;
	client->query.attributes &= ~NS_QUERYATTR_ORIGQNAMESTORED;
}

static void
release_ibalias_orig_name(ns_client_t *client) {
	if (client->query.ibalias_orig_qname != NULL &&
	    (client->query.attributes & NS_QUERYATTR_IBALIAS_ORIGQNAMESTORED) == 0) {
		query_releasename(client, &client->query.ibalias_orig_qname);
	}
	client->query.ibalias_orig_qname = NULL;
	client->query.attributes &= ~NS_QUERYATTR_IBALIAS_ORIGQNAMESTORED;
}

// Perform query rewrite
static isc_result_t
maybe_rewrite_qname(ns_client_t *client, dns_rdatatype_t qtype) {
	isc_buffer_t b;
	isc_result_t result = ISC_R_SUCCESS;
	isc_buffer_t *prep_buf = NULL;

	/* Check if the qname is subject to rewriting */
	dns_rbtnode_t *node = NULL;
	result = dns_rbt_findnode(client->view->infoblox_query_rewrite_domains,
				  client->query.qname, NULL, &node, NULL, 0,
				  NULL, NULL);
	if (result != ISC_R_SUCCESS && result != DNS_R_PARTIALMATCH) {
		/*
		 * In this case result must be ISC_R_NOTFOUND as we didn't pass
		 * non-NULL foundname to dns_rbt_findnode().  If it's not,
		 * it may mean dns_rbt_findnode() has been updated in a
		 * backward incompatible way, or there may be a serious
		 * internal bug - we cannot be sure.  We'll continue the
		 * process, but if the following message is logged we should
		 * definitely identify the cause and fix it.
		 */
		if (result != ISC_R_NOTFOUND) {
			char namebuf[DNS_NAME_FORMATSIZE];
			dns_name_format(client->query.qname, namebuf,
					sizeof(namebuf));
			isc_log_write(ns_g_lctx, NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
				      "%s: unexpected result code from "
				      "dns_rbt_findnode(%s): %s", __FUNCTION__,
				      namebuf, isc_result_totext(result));
		}
		if (QRYREWRITTEN(client)) {
			client->query.attributes &=
				~NS_QUERYATTR_QNAMEREWRITTEN;
			release_orig_name(client);
			client->query.ib_qname = NULL;
		}
		return (ISC_R_SUCCESS);
	}

	infoblox_perhaps_set_zbit(client);

	/* In our initial version we only rewrite queries for IPv4 clients. */
	if (isc_sockaddr_pf(&client->peeraddr) != AF_INET) {
		ns_client_log(client, NS_LOGCATEGORY_IB_QUERY_REWRITE,
			      NS_LOGMODULE_QUERY, ISC_LOG_INFO,
			      "Query rewrite skipped for non-IPv4 client");
		return (ISC_R_SUCCESS);
	}

	/* Construct the label to be prepended to the original qname */
	result = infoblox_make_rewrite_label(
		client->mctx, &prep_buf,
		client->view->infoblox_query_rewrite_prefix,
		client->view->infoblox_query_rewrite_prefixlen,
		&client->peeraddr);
	if (result != ISC_R_SUCCESS)
		return (result);

	isc_buffer_t *dbuf = query_getnamebuf(client);
	if (dbuf == NULL) {
		/* An unexpected error.  Log in the client category. */
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
			      "Unable to obtain buffer for query name rewrite");
		result = ISC_R_NOMEMORY;
		goto cleanup;
	}

	/* Construct a new rewritten qname and store it in ib_qname */
	client->query.ib_qname = query_newname(client, dbuf, &b);
	if (client->query.ib_qname == NULL) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
			      "Unable to get temporary name for query "
			      "name rewrite");
		result = ISC_R_NOMEMORY;
		goto cleanup;
	}
	result = dns_name_fromtext(client->query.ib_qname, prep_buf, NULL, 0,
				   NULL);
	if (result != ISC_R_SUCCESS) {
		/* This is most likely a local config error, log it so. */
		ns_client_log(client, NS_LOGCATEGORY_IB_QUERY_REWRITE,
			      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
			      "Error constructing prepended label for "
			      "query rewrite: %s",
			      isc_result_totext(result));
		query_releasename(client, &client->query.ib_qname);
		goto cleanup;
	}
	result = dns_name_concatenate(client->query.ib_qname,
				      client->query.qname,
				      client->query.ib_qname, NULL);
	if (result != ISC_R_SUCCESS) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, ISC_LOG_INFO,
			      "Error concatenating name for query rewrite: %s",
			      isc_result_totext(result));
		query_releasename(client, &client->query.ib_qname);
		goto cleanup;
	}
	// Make the client structure aware of the new name so the underlying
	// buffer will be active (and we need to do it here so we can call
	// query_newname below)
	query_keepname(client, client->query.ib_qname, dbuf);

	// Make a copy of the original query name.  In case the previous
	// original name hasn't been stored in the message (rare but possible
	// in cases such as CNAME loops), make sure it's cleaned up first.
	release_orig_name(client);
	dbuf = query_getnamebuf(client);
	if (dbuf == NULL) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
			      "Unable to obtain buffer for original query name "
			      "before rewrite");
		result = ISC_R_NOMEMORY;
		query_releasename(client, &client->query.ib_qname);
		goto cleanup;
	}
	client->query.ib_orig_qname = query_newname(client, dbuf, &b);
	if (client->query.ib_orig_qname == NULL) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
			      "Unable to get temporary name for query "
			      "name rewrite");
		result = ISC_R_NOMEMORY;
		query_releasename(client, &client->query.ib_qname);
		goto cleanup;
	}
	result = dns_name_copy(client->query.qname, client->query.ib_orig_qname,
			       NULL);
	RUNTIME_CHECK(result == ISC_R_SUCCESS); /* we should have enough buf */
	query_keepname(client, client->query.ib_orig_qname, dbuf);

	/*
	 * log the rewrite event.  We assume logs in QUERY_REWRITE category
	 * are suppressed by default.
	 */
	log_query_rewrite(client, qtype);

	// Switch the query name
	ns_client_qnamereplace(client, client->query.ib_qname);

	// Various code uses client->query.restarts to tell if the
	// client->query.qname has been dynamically allocated. We
	// don't want to set client->query.restarts to be non-zero
	// here (that would affect certain actions that look for
	// client->query.restarts==0 later in query_find), so we use two
	// additional attribute flags.
	// QNAMEPROCESSED is set the first time we do replace the qname (this
	// is for proper cleanup when client->query.restarts is zero).
	// QNAMEREWRITTEN is set when we have replaced the qname for this
	// particular iteration of the chain of lookups (this is used to
	// rewrite the owner names of records added to the response).
	client->query.attributes |= (NS_QUERYATTR_QNAMEPROCESSED|
				     NS_QUERYATTR_QNAMEREWRITTEN);

cleanup:
	if (prep_buf != NULL)
		isc_buffer_free(&prep_buf);
	return (result);
}
#endif

#ifdef ORIGINAL_ISC_CODE
#else
// Did we find an expired RR set in the cache ?
extern __thread isc_boolean_t ib_rrset_extant_but_expired;

/* Retrieve SOA record from the zone where IBALIAS record resides in, then add
 * it into response.
 */
static isc_result_t
query_ibalias_addsoa(ns_client_t *client) {
	REQUIRE(IBALIASINITIATED(client) == ISC_TRUE);
	REQUIRE(client->query.ibalias_orig_qname != NULL);

	dns_zone_t *zone = NULL;
	dns_db_t *db = NULL;
	dns_dbversion_t *version = NULL;
	isc_boolean_t is_zone;
	isc_result_t result;

	/* Retrieve db and version for IBALIAS record belonging to domain name.
	 * The version is retrieved from client->query.activeversions and will
	 * be detached automatically when client->query is destroyed.
	 */
	result = query_getdb(client, client->query.ibalias_orig_qname,
			     dns_rdatatype_ibalias, 0, &zone, &db, &version,
			     &is_zone);
	if (result == ISC_R_SUCCESS) {
		result = query_addsoa(client, db, version, ISC_UINT32_MAX,
				      ISC_FALSE, DNS_SECTION_AUTHORITY);
	}
	if (zone != NULL)
		dns_zone_detach(&zone);
	if (db != NULL)
		dns_db_detach(&db);
	return (result);
}

/*
 * Build original query name if for the first time. And replace query name with
 * found target name in IBALIAS record.
 */
static isc_result_t
query_find_ibalias(ns_client_t *client, dns_name_t **fname,
		   dns_rdataset_t *rdataset, dns_rdatatype_t qtype) {
	isc_result_t result = ISC_R_SUCCESS;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	dns_rdata_in_ibalias_t ibalias;
	isc_buffer_t b;
	isc_buffer_t *dbuf;
	dns_name_t *tname = NULL;

	/* Prepare original query name. This is done only once for
	 * IBALIAS record, since only the original query name is set
	 * in ANSWER section in response even there are multiple qname
	 * replacements.
	 */
	/* Skip preparing original query name if it's already done */
	if (IBALIASINITIATED(client))
		goto replace_qname;

	/* Mark query result is initiated by IBALIAS record */
	client->query.attributes |= NS_QUERYATTR_IBALIAS_INITIATED;

	/*
	 * We set the PARTIALANSWER attribute so that if anything goes
	 * wrong later on, we'll return what we've got so far.
	 */
	client->query.attributes |= NS_QUERYATTR_PARTIALANSWER;

	/* Recursion is allowed for IBALIAS record even recursion is disabled.
	 * Enable recursion and cache for the client silently. */
	client->query.attributes |= (NS_QUERYATTR_RECURSIONOK |
				     NS_QUERYATTR_CACHEOK);
	/* Skip cache ACL check */
	client->query.attributes |= (NS_QUERYATTR_CACHEACLOK|
				     NS_QUERYATTR_CACHEACLOKVALID);

	/* fname references to the buffer from client->query.namebufs, which
	 * is returned by query_getnamebuf(). So release fname before
	 * preparing original query name.
	 */
	query_releasename(client, fname);

	dbuf = query_getnamebuf(client);
	if (dbuf == NULL) {
		/* An unexpected error. Log in the client category. */
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
			      "Unable to obtain buffer for query"
			      " original name");
		result = ISC_R_NOMEMORY;
		goto cleanup;
	}
	client->query.ibalias_orig_qname = query_newname(client, dbuf, &b);
	if (client->query.ibalias_orig_qname == NULL) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
			      "Unable to get temporary name for"
			      "original query name");
		result = ISC_R_NOMEMORY;
		goto cleanup;
	}
	/* Use client->query.qname rather than client->query.origqname
	 * since client->query.qname may be changed and different
	 * to client->query.origqname.
	 * client->query.qname is the name containing IBALIAS record.
	 */
	result = dns_name_copy(client->query.qname,
			       client->query.ibalias_orig_qname, NULL);
	/* we should have enough buffer */
	RUNTIME_CHECK(result == ISC_R_SUCCESS);
	query_keepname(client, client->query.ibalias_orig_qname, dbuf);

replace_qname:
	/*
	 * Reset qname to target name of the IBALIAS record for query type.
	 */
	result = dns_message_gettempname(client->message, &tname);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	/* Get IBALIAS record RDATA for query type */
	for (result = dns_rdataset_first(rdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(rdataset)) {
		dns_rdataset_current(rdataset, &rdata);
		result = dns_rdata_tostruct(&rdata, &ibalias,
					    NULL);
		dns_rdata_reset(&rdata);
		if (result != ISC_R_SUCCESS ||
		    ibalias.rtype == qtype)
			break;
		dns_rdata_freestruct(&ibalias);
	}

	/* We should find corresponding IBALIAS target, otherwise
	 * we can not go into this DNS_R_IBALIAS switch branch.
	 * There must be something wrong if result isn't ISC_R_SUCCESS.
	 */
	if (result != ISC_R_SUCCESS) {
		dns_message_puttempname(client->message, &tname);
		goto cleanup;
	}
	dns_name_init(tname, NULL);
	result = dns_name_dup(&ibalias.target, client->mctx, tname);
	if (result != ISC_R_SUCCESS) {
		dns_message_puttempname(client->message, &tname);
		dns_rdata_freestruct(&ibalias);
		goto cleanup;
	}
	/* Update minimum of IBALIAS records target ttl */
	if (client->query.ibalias_ttl > ibalias.ttl)
		client->query.ibalias_ttl = ibalias.ttl;
	dns_rdata_freestruct(&ibalias);
	ns_client_qnamereplace(client, tname);

cleanup:
	return result;
}

/* If the query includes an EDNS client ID option, build 'local ID' structure
 * from it and return a pointer to it; otherwise it returns NULL.
 * 'local_id' is a caller-supplied placeholder to build the data, which must
 * not be NULL and must have a sufficient storage. */
static const ib_subscriber_edns0_client_id_t *
ib_get_localid(ib_subscriber_edns0_client_id_t *local_id, ns_client_t *client) {
	if (client->ib_client_id != NULL) {
		memset(local_id, 0, sizeof(*local_id));
		local_id->idtype = client->ib_client_id->idtype;
		local_id->idlen = client->ib_client_id->idlen;
		if (client->ib_client_id->idlen != 0) {
			local_id->id = (unsigned char *)
				(client->ib_client_id + 1);
		}
		return (local_id);
	}
	return (NULL);
}

static unsigned int
infoblox_get_last_queried_find_options(ns_client_t *client,
				       dns_rdatatype_t type,
				       dns_zone_t *zone)
{
	unsigned int find_options = DNS_DBFIND_UPDATE_LAST_QUERIED;

	if (zone != NULL) {
		dns_acl_t *zone_lastquerieddenylistacl;
		isc_result_t denylisted;

		zone_lastquerieddenylistacl =
			infoblox_dns_zone_getlastquerieddenylistacl(zone);
		denylisted = ns_client_checkaclsilent
			(client, NULL,
			 zone_lastquerieddenylistacl,
			 ISC_FALSE);

		if (denylisted == ISC_R_SUCCESS) {
			find_options &= ~(DNS_DBFIND_UPDATE_LAST_QUERIED);

			if (isc_log_wouldlog(ns_g_lctx, ISC_LOG_DEBUG(1))) {
				char namebuf[DNS_NAME_FORMATSIZE];
				char typename[DNS_RDATATYPE_FORMATSIZE];
				char classname[DNS_RDATACLASS_FORMATSIZE];

				/* Log the ACL denial at debug level */
				dns_name_format(client->query.qname, namebuf,
						sizeof(namebuf));
				dns_rdatatype_format(type, typename,
						     sizeof(typename));
				dns_rdataclass_format(client->message->rdclass,
						      classname,
						      sizeof(classname));
				ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
					      NS_LOGMODULE_QUERY,
					      ISC_LOG_DEBUG(1),
					      "client blocked from updating "
					      "last-queried timestamp in "
					      "query %s/%s/%s",
					      namebuf, typename, classname);
			}
		}
	}

	return (find_options);
}

#endif


/*
 * Do the bulk of query processing for the current query of 'client'.
 * If 'event' is non-NULL, we are returning from recursion and 'qtype'
 * is ignored.  Otherwise, 'qtype' is the query type.
 */
static isc_result_t
query_find(ns_client_t *client, dns_fetchevent_t *event, dns_rdatatype_t qtype)
{
	dns_db_t *db, *zdb;
	dns_dbnode_t *node;
	dns_rdatatype_t type = qtype;
	dns_name_t *fname, *zfname, *tname, *prefix;
	dns_rdataset_t *rdataset, *trdataset;
	dns_rdataset_t *sigrdataset, *zrdataset, *zsigrdataset;
	dns_rdataset_t **sigrdatasetp;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	dns_rdatasetiter_t *rdsiter;
	isc_boolean_t want_restart, is_zone, need_wildcardproof;
	isc_boolean_t is_staticstub_zone;
	isc_boolean_t authoritative = ISC_FALSE;
	isc_boolean_t answer_has_ns = ISC_FALSE;
	unsigned int n, nlabels;
	dns_namereln_t namereln;
	int order;
	isc_buffer_t *dbuf;
	isc_buffer_t b;
	isc_result_t result, eresult, tresult;
	dns_fixedname_t fixed;
	dns_fixedname_t wildcardname;
	dns_dbversion_t *version, *zversion;
	dns_zone_t *zone;
	dns_rdata_cname_t cname;
	dns_rdata_dname_t dname;
	unsigned int options;
	isc_boolean_t empty_wild;
	dns_rdataset_t *noqname;
	dns_rpz_st_t *rpz_st;
	isc_boolean_t resuming;
	int line = -1;
	isc_boolean_t dns64_exclude, dns64, rpz;
	isc_boolean_t nxrewrite = ISC_FALSE;
	isc_boolean_t redirected = ISC_FALSE;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;
	isc_boolean_t block_clientinfo;
	char errmsg[256];
	isc_boolean_t associated;
	dns_section_t section;
	dns_ttl_t ttl;
	isc_boolean_t failcache;
	isc_uint32_t flags;
#ifdef WANT_QUERYTRACE
	char mbuf[BUFSIZ];
	char qbuf[DNS_NAME_FORMATSIZE];
	char tbuf[DNS_RDATATYPE_FORMATSIZE];
#endif
	dns_name_t *rpzqname;

#ifdef	ORIGINAL_ISC_CODE
#else
	isc_boolean_t proxy_event = ISC_FALSE;
	isc_sockaddr_t proxy_result;
	dns_rdataset_t nxrds;
	int	returning_from_recursion = 0; // Insignificant unless (defined(ISC_PLATFORM_HAVEXADD) && defined(ISC_PLATFORM_HAVECMPXCHG)) is true (which we expect is the case)
#define INFOBLOX_IS_CACHE (!authoritative && !returning_from_recursion)
	int	reset_last_queried = 0;
#endif

	CTRACE(ISC_LOG_DEBUG(3), "query_find");

	/*
	 * One-time initialization.
	 *
	 * It's especially important to initialize anything that the cleanup
	 * code might cleanup.
	 */

	eresult = ISC_R_SUCCESS;
	fname = NULL;
	zfname = NULL;
	rdataset = NULL;
	zrdataset = NULL;
	sigrdataset = NULL;
	zsigrdataset = NULL;
	zversion = NULL;
	node = NULL;
	db = NULL;
	zdb = NULL;
	version = NULL;
	zone = NULL;
	need_wildcardproof = ISC_FALSE;
	empty_wild = ISC_FALSE;
	dns64_exclude = dns64 = rpz = ISC_FALSE;
	options = 0;
	resuming = ISC_FALSE;
	is_zone = ISC_FALSE;
	is_staticstub_zone = ISC_FALSE;

	dns_clientinfomethods_init(&cm, ns_client_sourceip);
	dns_clientinfo_init(&ci, client,
			    ECS_RECEIVED(client) ? &client->ecs : NULL,
			    NULL);

#ifdef WANT_QUERYTRACE
	if (client->query.origqname != NULL)
		dns_name_format(client->query.origqname, qbuf,
				sizeof(qbuf));
	else
		snprintf(qbuf, sizeof(qbuf), "<unset>");

	snprintf(mbuf, sizeof(mbuf) - 1,
		 "client attr:0x%x, query attr:0x%X, restarts:%d, "
		 "origqname:%s, timer:%d, authdb:%d, referral:%d",
		 client->attributes,
		 client->query.attributes,
		 client->query.restarts, qbuf,
		 (int) client->query.timerset,
		 (int) client->query.authdbset,
		 (int) client->query.isreferral);
	CTRACE(ISC_LOG_DEBUG(3), mbuf);
#endif

	if (event != NULL) {
		/*
		 * We're returning from recursion.  Restore the query context
		 * and resume.
		 */

#ifdef ORIGINAL_ISC_CODE
#else
		returning_from_recursion = 1;
#endif
		want_restart = ISC_FALSE;

		rpz_st = client->query.rpz_st;
#ifdef ORIGINAL_ISC_CODE
#else
		// Are we returning from an asynchronous proxy query?
		if (PROXYASYNC(client)) {
			proxy_event = ISC_TRUE;
			client->query.attributes &= ~NS_QUERYATTR_PC_ASYNC;
			proxy_result = event->proxy_result;
			qtype = event->qtype;
		} else
#endif
		if (rpz_st != NULL &&
		    (rpz_st->state & DNS_RPZ_RECURSING) != 0)
		{
			CTRACE(ISC_LOG_DEBUG(3), "resume from RPZ recursion");
#ifdef WANT_QUERYTRACE
			{
				char pbuf[DNS_NAME_FORMATSIZE] = "<unset>";
				char fbuf[DNS_NAME_FORMATSIZE] = "<unset>";
				if (rpz_st->r_name != NULL)
					dns_name_format(rpz_st->r_name,
							qbuf, sizeof(qbuf));
				else
					snprintf(qbuf, sizeof(qbuf),
						 "<unset>");
				if (rpz_st->p_name != NULL)
					dns_name_format(rpz_st->p_name,
							pbuf, sizeof(pbuf));
				if (rpz_st->fname != NULL)
					dns_name_format(rpz_st->fname,
							fbuf, sizeof(fbuf));

				snprintf(mbuf, sizeof(mbuf) - 1,
					 "rpz rname:%s, pname:%s, fname:%s",
					 qbuf, pbuf, fbuf);
				CTRACE(ISC_LOG_DEBUG(3), mbuf);
			}
#endif

			is_zone = rpz_st->q.is_zone;
			authoritative = rpz_st->q.authoritative;
			RESTORE(zone, rpz_st->q.zone);
			RESTORE(node, rpz_st->q.node);
			RESTORE(db, rpz_st->q.db);
			RESTORE(rdataset, rpz_st->q.rdataset);
			RESTORE(sigrdataset, rpz_st->q.sigrdataset);
			qtype = rpz_st->q.qtype;

			if (event->node != NULL)
				dns_db_detachnode(event->db, &event->node);
			SAVE(rpz_st->r.db, event->db);
			rpz_st->r.r_type = event->qtype;
			SAVE(rpz_st->r.r_rdataset, event->rdataset);
			query_putrdataset(client, &event->sigrdataset);
		} else if (REDIRECT(client)) {
			/*
			 * Restore saved state.
			 */
			CTRACE(ISC_LOG_DEBUG(3),
			       "resume from redirect recursion");
#ifdef WANT_QUERYTRACE
			dns_name_format(client->query.redirect.fname,
					qbuf, sizeof(qbuf));
			dns_rdatatype_format(client->query.redirect.qtype,
					     tbuf, sizeof(tbuf));
			snprintf(mbuf, sizeof(mbuf) - 1,
				 "redirect fname:%s, qtype:%s, auth:%d",
				 qbuf, tbuf,
				 client->query.redirect.authoritative);
			CTRACE(ISC_LOG_DEBUG(3), mbuf);
#endif
			qtype = client->query.redirect.qtype;
			INSIST(client->query.redirect.rdataset != NULL);
			RESTORE(rdataset, client->query.redirect.rdataset);
			RESTORE(sigrdataset,
				client->query.redirect.sigrdataset);
			RESTORE(db, client->query.redirect.db);
			RESTORE(node, client->query.redirect.node);
			RESTORE(zone, client->query.redirect.zone);
			authoritative = client->query.redirect.authoritative;
			is_zone = client->query.redirect.is_zone;

			/*
			 * Free resources used while recursing.
			 */
			query_putrdataset(client, &event->rdataset);
			query_putrdataset(client, &event->sigrdataset);
			if (event->node != NULL)
				dns_db_detachnode(event->db, &event->node);
			if (event->db != NULL)
				dns_db_detach(&event->db);
		} else {
			CTRACE(ISC_LOG_DEBUG(3),
			       "resume from normal recursion");
			authoritative = ISC_FALSE;

			qtype = event->qtype;
			SAVE(db, event->db);
			SAVE(node, event->node);
			SAVE(rdataset, event->rdataset);
			SAVE(sigrdataset, event->sigrdataset);
			/*
			 * If the remote nameserver returned an answer
			 * with an ECS option, then we update the ECS
			 * option we send back with the scope we
			 * received. (We rely on any non-matching ECS
			 * responses having been caught by the
			 * resolver.)
			 */
			if (ECS_RECEIVED(client) &&
			    (event->ecs.scope != 0xff) &&
			    (client->ecs.scope == 0xff))
			{
				client->ecs.scope = event->ecs.scope;
			}
		}
#ifdef ORIGINAL_ISC_CODE
#else
		/* In the case parental-control proxy, rdataset will be
		 * generated in this function. */
		if (!proxy_event)
#endif
		INSIST(rdataset != NULL);

		if (qtype == dns_rdatatype_rrsig || qtype == dns_rdatatype_sig)
			type = dns_rdatatype_any;
		else
			type = qtype;

		if (DNS64(client)) {
			client->query.attributes &= ~NS_QUERYATTR_DNS64;
			dns64 = ISC_TRUE;
		}
		if (DNS64EXCLUDE(client)) {
			client->query.attributes &= ~NS_QUERYATTR_DNS64EXCLUDE;
			dns64_exclude = ISC_TRUE;
		}

		if (rpz_st != NULL &&
		    (rpz_st->state & DNS_RPZ_RECURSING) != 0)
		{
			/*
			 * Has response policy changed out from under us?
			 */
			if (rpz_st->rpz_ver != client->view->rpzs->rpz_ver) {
				ns_client_log(client, NS_LOGCATEGORY_CLIENT,
					      NS_LOGMODULE_QUERY,
					      DNS_RPZ_INFO_LEVEL,
					      "query_find: RPZ settings "
					      "out of date "
					      "(rpz_ver %d, expected %d)",
					      client->view->rpzs->rpz_ver,
					      rpz_st->rpz_ver);
				QUERY_ERROR(DNS_R_SERVFAIL);
				goto cleanup;
			}
		}

		/*
		 * We'll need some resources...
		 */
		dbuf = query_getnamebuf(client);
		if (dbuf == NULL) {
			CTRACE(ISC_LOG_ERROR,
			       "query_find: query_getnamebuf failed (1)");
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}
		fname = query_newname(client, dbuf, &b);
		if (fname == NULL) {
			CTRACE(ISC_LOG_ERROR,
			       "query_find: query_newname failed (1)");
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}
		if (rpz_st != NULL &&
		    (rpz_st->state & DNS_RPZ_RECURSING) != 0) {
			tname = rpz_st->fname;
		} else if (REDIRECT(client)) {
			tname = client->query.redirect.fname;
		} else {
			tname = dns_fixedname_name(&event->foundname);
		}
		result = dns_name_copy(tname, fname, NULL);
		if (result != ISC_R_SUCCESS) {
			CTRACE(ISC_LOG_ERROR,
			       "query_find: dns_name_copy failed");
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}
		if (rpz_st != NULL &&
		    (rpz_st->state & DNS_RPZ_RECURSING) != 0) {
			rpz_st->r.r_result = event->result;
			result = rpz_st->q.result;
			free_devent(client, ISC_EVENT_PTR(&event), &event);
		} else if (REDIRECT(client)) {
			result = client->query.redirect.result;
			is_zone = client->query.redirect.is_zone;
		} else {
			result = event->result;
		}
		resuming = ISC_TRUE;
#ifdef ORIGINAL_ISC_CODE
#else
		if (proxy_event)
			goto ib_pc_proxy_resume;
#endif
		goto resume;
	}

	/*
	 * Not returning from recursion.
	 *
	 * First, check for a recent match in the view's SERVFAIL cache.
	 * If we find one, and it was from a query with CD=1, *or*
	 * if the current query has CD=0, then we can just return
	 * SERVFAIL now.
	 */
	if (RECURSIONOK(client)) {
		flags = 0;
#ifdef ENABLE_AFL
		if (ns_g_fuzz_type == ns_fuzz_resolver) {
			failcache = ISC_FALSE;
		} else {
			failcache = dns_badcache_find(client->view->failcache,
						      client->query.qname, qtype,
						      &flags, &client->tnow);
		}
#else
		failcache = dns_badcache_find(client->view->failcache,
					      client->query.qname, qtype,
					      &flags, &client->tnow);
#endif
		if (failcache &&
		    (((flags & NS_FAILCACHE_CD) != 0) ||
		     ((client->message->flags & DNS_MESSAGEFLAG_CD) == 0)))
		{
			if (isc_log_wouldlog(ns_g_lctx, ISC_LOG_DEBUG(1))) {
				char namebuf[DNS_NAME_FORMATSIZE];
				char typename[DNS_RDATATYPE_FORMATSIZE];

				dns_name_format(client->query.qname,
						namebuf, sizeof(namebuf));
				dns_rdatatype_format(qtype, typename,
						     sizeof(typename));
				ns_client_log(client, NS_LOGCATEGORY_CLIENT,
					      NS_LOGMODULE_QUERY,
					      ISC_LOG_DEBUG(1),
					      "servfail cache hit %s/%s (%s)",
					      namebuf, typename,
					      ((flags & NS_FAILCACHE_CD) != 0)
					       ? "CD=1"
					       : "CD=0");
			}
			client->attributes |= NS_CLIENTATTR_NOSETFC;
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}
	}

	/*
	 * If it's a SIG query, we'll iterate the node.
	 */
	if (qtype == dns_rdatatype_rrsig || qtype == dns_rdatatype_sig)
		type = dns_rdatatype_any;
	else
		type = qtype;

 restart:
	CTRACE(ISC_LOG_DEBUG(3), "query_find: restart");
	want_restart = ISC_FALSE;
	authoritative = ISC_FALSE;
	version = NULL;
	zversion = NULL;
	need_wildcardproof = ISC_FALSE;
	rpz = ISC_FALSE;

#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * Check if we need to rewrite query name if the feature is enabled
	 * (indicated as infoblox_query_rewrite_domains being non-NULL)
	 */
	if (client->view->infoblox_query_rewrite_domains != NULL &&
	    maybe_rewrite_qname(client, qtype) != ISC_R_SUCCESS)
	{
		QUERY_ERROR(DNS_R_SERVFAIL);
		goto cleanup;
	}
#endif

	if (client->view->checknames &&
	    !dns_rdata_checkowner(client->query.qname,
				  client->message->rdclass,
				  qtype, ISC_FALSE)) {
		char namebuf[DNS_NAME_FORMATSIZE];
		char typename[DNS_RDATATYPE_FORMATSIZE];
		char classname[DNS_RDATACLASS_FORMATSIZE];

		dns_name_format(client->query.qname, namebuf, sizeof(namebuf));
		dns_rdatatype_format(qtype, typename, sizeof(typename));
		dns_rdataclass_format(client->message->rdclass, classname,
				      sizeof(classname));
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
			      "check-names failure %s/%s/%s", namebuf,
			      typename, classname);
		QUERY_ERROR(DNS_R_REFUSED);
		goto cleanup;
	}

	/*
	 * First we must find the right database.
	 */
	options &= DNS_GETDB_NOLOG; /* Preserve DNS_GETDB_NOLOG. */
	if (dns_rdatatype_atparent(qtype) &&
	    !dns_name_equal(client->query.qname, dns_rootname))
		options |= DNS_GETDB_NOEXACT;
	result = query_getdb(client, client->query.qname, qtype, options,
			     &zone, &db, &version, &is_zone);
	if (ISC_UNLIKELY((result != ISC_R_SUCCESS || !is_zone) &&
			 qtype == dns_rdatatype_ds &&
			 !RECURSIONOK(client) &&
			 (options & DNS_GETDB_NOEXACT) != 0))
	{
		/*
		 * If the query type is DS, look to see if we are
		 * authoritative for the child zone.
		 */
		dns_db_t *tdb = NULL;
		dns_zone_t *tzone = NULL;
		dns_dbversion_t *tversion = NULL;

		tresult = query_getzonedb(client, client->query.qname, qtype,
					 DNS_GETDB_PARTIAL, &tzone, &tdb,
					 &tversion);
		if (tresult == ISC_R_SUCCESS) {
			options &= ~DNS_GETDB_NOEXACT;
			query_putrdataset(client, &rdataset);
			if (db != NULL)
				dns_db_detach(&db);
			if (zone != NULL)
				dns_zone_detach(&zone);
			version = NULL;
			RESTORE(version, tversion);
			RESTORE(db, tdb);
			RESTORE(zone, tzone);
			is_zone = ISC_TRUE;
			result = ISC_R_SUCCESS;
		} else {
			if (tdb != NULL)
				dns_db_detach(&tdb);
			if (tzone != NULL)
				dns_zone_detach(&tzone);
		}
	}
	if (result != ISC_R_SUCCESS) {
		if (result == DNS_R_REFUSED) {
			if (WANTRECURSION(client)) {
				ns_query_incstats(client,
					  dns_nsstatscounter_recurserej);
			} else
				ns_query_incstats(client,
						  dns_nsstatscounter_authrej);
			if (!PARTIALANSWER(client))
				QUERY_ERROR(DNS_R_REFUSED);
#ifdef ORIGINAL_ISC_CODE
#else
		}
		else if (result == DNS_R_NOTLOADED) {
			/*
			 * make loading of the zone a priority by moving the
			 * corresponding loading task to the requested zones'
			 * section of the loading queue
			 */

			// a zone pointer was not returned, so we need to fetch
			// it by ourselves
			isc_result_t tresult;
			tresult = dns_zt_find(client->view->zonetable,
					      client->query.qname, 0, NULL,
					      &zone);
			if (tresult == ISC_R_SUCCESS ||
			    tresult == DNS_R_PARTIALMATCH)
			{
				INSIST(zone != NULL);
				// If there is a zone which is not loaded yet,
				// move it into the requested area in the
				// loading queue, if not already
				if (!infoblox_zone_isdbinitialized(zone)) {
					infoblox_dns_zone_make_last_requested(
						zone, "query");
					QUERY_ERROR(DNS_R_DROP);
				} else
					QUERY_ERROR(DNS_R_SERVFAIL);
			}
			else
			{
				// earlier we got DNS_R_NOTLOADED while now
				// we've failed to find the needed zone in the
				// view. This is definitely some inconsistency
				// problem, so log the error and do nothing
				ns_client_log(client,
					      NS_LOGCATEGORY_QUERY_ERRORS,
					      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
					      "zone not loaded and not found in zone table: %s",
					      isc_result_totext(tresult));
				QUERY_ERROR(DNS_R_SERVFAIL);
			}
			// zone will be detached in cleanup
#endif
		} else {
			CTRACE(ISC_LOG_ERROR,
			       "query_find: query_getdb failed");
			QUERY_ERROR(DNS_R_SERVFAIL);
		}
		goto cleanup;
	}

	is_staticstub_zone = ISC_FALSE;
	if (is_zone) {
		authoritative = ISC_TRUE;
		if (zone != NULL &&
		    dns_zone_gettype(zone) == dns_zone_staticstub)
			is_staticstub_zone = ISC_TRUE;
	}

	if (event == NULL && client->query.restarts == 0) {
		if (is_zone) {
			if (zone != NULL) {
				/*
				 * if is_zone = true, zone = NULL then this is
				 * a DLZ zone.  Don't attempt to attach zone.
				 */
				dns_zone_attach(zone, &client->query.authzone);
			}
			dns_db_attach(db, &client->query.authdb);
		}
		client->query.authdbset = ISC_TRUE;

		/* Track TCP vs UDP stats per zone */
		if (TCP_CLIENT(client))
			ns_query_incstats(client, dns_nsstatscounter_tcp);
		else
			ns_query_incstats(client, dns_nsstatscounter_udp);
	}

#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * Remember it if this is an SOA query for the origin of an
	 * authoritative zone and zone transfer from this client would be
	 * allowed.  Note that we don't reject this query itself even if the
	 * zone transfer would be rejected.
	 * This check only matters if this is a TCP client and we restrict
	 * the case for multiple requests in the same TCP connection.  We don't
	 * bother to do the check for other cases to avoid unnecessary overhead.
	 */
	if ((client->attributes & NS_CLIENTATTR_TCP) != 0 &&
	    ns_g_server->infoblox_disable_multiple_dns_tcp_request &&
	    is_zone && zone != NULL && qtype == dns_rdatatype_soa &&
	    dns_name_equal(dns_zone_getorigin(zone), client->query.qname) &&
	    ns_client_checkaclsilent(client, NULL, dns_zone_getxfracl(zone),
				     ISC_TRUE) == ISC_R_SUCCESS)
	{
		client->seen_soa_query = ISC_TRUE;
	}

	INFOBLOX_SET_VIEW(client->view);
	// Do we need to set up the last-queried stuff ?
	if (infoblox_perhaps_last_queried() && is_zone && zone) {
		isc_boolean_t last_queried_zone = ISC_FALSE;
		isc_boolean_t last_queried_rr = ISC_FALSE;
		u_int64_t last_queried_rr_start = 0;

		infoblox_dns_zone_get_last_queried(zone, &last_queried_zone, &last_queried_rr,
						   &last_queried_rr_start);
		if (last_queried_zone || last_queried_rr) {
			// As a safety measure, reset the DDNS zone
			infoblox_set_ddns_zone(NULL);
			// Establish the data that enables last-queried updates
			// for this zone.
			infoblox_set_last_queried(last_queried_rr, last_queried_rr_start,
					    last_queried_rr, last_queried_zone, zone);
			reset_last_queried = ISC_TRUE;
		}
	}
#endif

 db_find:
	/*
	 * If we're not authoritative for this QNAME, and this is a
	 * recursive query, and it includes an ECS option with a
	 * non-zero prefix length, and the client is not covered
	 * by the 'ecs-forward' ACL, then forget we ever saw
	 * an ECS option.
	 */
	if (!authoritative &&
	    (WANTRECURSION(client) && RECURSIONOK(client) &&
	     ECS_RECEIVED(client) && !ECS_FORWARD(client) &&
	     client->ecs.source != 0))
	{
		CTRACE(ISC_LOG_DEBUG(3),
		       "query_find: ECS forwarding denied");
		QUERY_ERROR(DNS_R_REFUSED);
		goto cleanup;
	}

	CTRACE(ISC_LOG_DEBUG(3), "query_find: db_find");
	/*
	 * We'll need some resources...
	 */
	dbuf = query_getnamebuf(client);
	if (ISC_UNLIKELY(dbuf == NULL)) {
		CTRACE(ISC_LOG_ERROR,
		       "query_find: query_getnamebuf failed (2)");
		QUERY_ERROR(DNS_R_SERVFAIL);
		goto cleanup;
	}
	fname = query_newname(client, dbuf, &b);
	rdataset = query_newrdataset(client);
	if (ISC_UNLIKELY(fname == NULL || rdataset == NULL)) {
		CTRACE(ISC_LOG_ERROR,
		       "query_find: query_newname failed (2)");
		QUERY_ERROR(DNS_R_SERVFAIL);
		goto cleanup;
	}
	if (WANTDNSSEC(client) && (!is_zone || dns_db_issecure(db))) {
		sigrdataset = query_newrdataset(client);
		if (sigrdataset == NULL) {
			CTRACE(ISC_LOG_ERROR,
			       "query_find: query_newrdataset failed (2)");
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}
	}

#ifdef	ORIGINAL_ISC_CODE
#else
	// Possibly intercept. We check for feature enabled
	// here, but leave the rest of the checking to
	// infoblox_perhaps_intercept(). Note that we only
	// intercept if the lookup would be for the cache.
	if (! is_zone && RECURSIONOK(client) &&
	    (!client->view->blk_skipped || !client->view->nx_skipped)) {
		infoblox_redirect_action_t action;
		dns_rdataset_t *oldrdataset = rdataset;

		action = infoblox_perhaps_intercept(client, qtype, rdataset,
						    &db, dbuf, &b, &node, &fname);
		if (action == ib_redirect_redirect) {
			goto after_result_switch;
		} else if (action == ib_redirect_refused) {
			QUERY_ERROR(DNS_R_REFUSED);
			goto cleanup;
		} // else, pass or modify, just continue
		if (node != NULL)
			dns_db_detachnode(db, &node);
		if (rdataset != NULL && oldrdataset == NULL)
			query_putrdataset(client, &rdataset);
	}
#endif

#ifdef ORIGINAL_ISC_CODE
#else
	if (!is_zone)
		ib_rrset_extant_but_expired = ISC_FALSE;
#endif

	/*
	 * XXXMUKS: &ci fields need to be set. Note that cache's
	 * findext() expects that ci->ecs.source > 0 only for permitted
	 * types. If a non-zero source is passed, it will look in the
	 * ECS cache which may not hold some types of RRs.
	 */

	block_clientinfo = ISC_FALSE;
	dns_ecs_init(&client->prefetch_ecs);
	if (!is_zone) {
		isc_uint8_t ecsbits4, ecsbits6;

		ecsbits4 = client->view->ecsbits4;
		ecsbits6 = client->view->ecsbits6;

		if (dns_ecszones_name_allowed(client->view->ecszones,
					      client->query.qname,
					      &ecsbits4, &ecsbits6) &&
		    dns_ecs_type_allowed(client->view->ecstypes, type))
		{

			if (!ECS_RECEIVED(client)) {
				isc_netaddr_fromsockaddr(&ci.ecs.addr,
							 &client->peeraddr);

				switch (ci.ecs.addr.family) {
				case AF_INET:
					ci.ecs.source = ecsbits4;
					break;
				case AF_INET6:
					ci.ecs.source = ecsbits6;
					break;
				default:
					/*
					 * Must not get anything other
					 * than v4 or v6 from client
					 * address.
					 */
					INSIST(0);
				}
			} else {
				switch (ci.ecs.addr.family) {
				case 0:
					/*
					 * XXXMUKS: This may need some
					 * tweaking in the calling code?
					 * Or is it OK to just handle
					 * FAMILY=0 here?
					 */
					ci.ecs.source = 0;
					break;
				case AF_INET:
					if (ci.ecs.source > ecsbits4)
						ci.ecs.source = ecsbits4;
					break;
				case AF_INET6:
					if (ci.ecs.source > ecsbits6)
						ci.ecs.source = ecsbits6;
					break;
				default:
					/*
					 * Must not get anything other
					 * than v4 or v6 from
					 * caller. The caller has
					 * supplied a broken ECS
					 * (undefined family, nonzero
					 * prefix length).
					 */
					INSIST(0);
				}
			}

			/*
			 * Check if it is a query with IPv6 family and
			 * address in the V4MAPPED space. If it is,
			 * refuse the query. This applies to both
			 * queries from such client addresses and
			 * queries including addresses in forwarded ECS
			 * option.
			 *
			 * NOTE: This condition cannot normally happen
			 * as IPv6 address prefixes are clamped at
			 * /56. But the check is left here as a
			 * safeguard against programming
			 * errors. V4MAPPED entries must not enter the
			 * cache.
			 */
			if (is_v4mapped_address(&ci.ecs)) {
				CTRACE(ISC_LOG_DEBUG(3),
				       "query_find: ECS forwarding "
				       "denied for V4MAPPED IPv6 "
				       "address in option");
				QUERY_ERROR(DNS_R_REFUSED);
				goto cleanup;
			}

			/*
			 * XXXMUKS: Somehow, ci's scope seems to get set
			 * to something != 0xff here, perhaps due to
			 * goto within this function?! This ought to be
			 * handled, so that ci's scope is set properly
			 * for return to client.
			 */
			ci.ecs.scope = 0xff;

			/*
			 * Save the effective ECS that's used in cache
			 * find to be used by prefetch later.
			 */
			client->prefetch_ecs = ci.ecs;

#ifdef ORIGINAL_ISC_CODE
#else
			/* If a query is subject to ECS handling, the
			 * corresponding final answer will be excluded
			 * from DNS cache acceleration.
			 * This is because we don't have a way to return
			 * per-subnet answer from the caching hardware yet.
			 */
			infoblox_perhaps_set_zbit(client);
#endif
		} else {
			block_clientinfo = ISC_TRUE;
		}
	}

#ifdef WANT_QUERYTRACE
	if (!is_zone) {
		char lbuf[2048];

		if (!block_clientinfo) {
			char ecsbuf[DNS_ECS_FORMATSIZE];

			dns_ecs_format(&ci.ecs, ecsbuf, sizeof(ecsbuf));

			snprintf(lbuf, sizeof(lbuf) - 1,
				 "db_find: calling dns_db_findext() with "
				 "ci.ecs=[%s], type=%d",
				 ecsbuf, type);
		} else {
			snprintf(lbuf, sizeof(lbuf) - 1,
				 "db_find: calling dns_db_findext() with "
				 "ci=NULL, type=%d", type);
		}
		CTRACE(ISC_LOG_DEBUG(3), lbuf);
	}
#endif

	/*
	 * Now look for an answer in the database.  If this is a dns64
	 * AAAA lookup on a rpz database adjust the qname.
 	 */
	if (dns64 && rpz)
		rpzqname = client->query.rpz_st->p_name;
	else
		rpzqname = client->query.qname;

#ifdef	ORIGINAL_ISC_CODE
#else
	if (is_zone) {
		client->query.dboptions |=
			infoblox_get_last_queried_find_options(client, type,
							       zone);
	}

	/*
	 * We perform the "infoblox-allow-query-domain" ACL check here,
	 * as restarts (e.g., for the target of a CNAME) may also need
	 * checking.
	 */
	if (ISC_UNLIKELY(client->view->infobloxallowquerydomain != NULL)) {
		dns_acl_t *acl = NULL;

		result = dns_rbt_findname
			(client->view->infobloxallowquerydomain, rpzqname,
			 0, NULL, (void **) &acl);
		if (result == ISC_R_SUCCESS || result == DNS_R_PARTIALMATCH) {
			/*
			 * Set z-bit when a domain is listed in 
			 * infoblox-allow-query-domain ACLs
			 * to skip caching at DCA
			 */ 
			infoblox_perhaps_set_zbit(client);

			if (ns_client_checkaclsilent(client, NULL, acl,
						     ISC_FALSE))
			{
				/*
				 * Was the qname for the original query,
				 * or the target of CNAME/DNAME
				 * processing?
				 */
				result = (client->query.restarts == 0) ?
					DNS_R_REFUSED : ISC_R_SUCCESS;
				QUERY_ERROR(result);
				goto cleanup;
			}
		}
	}
#endif

	result = dns_db_findext(db, rpzqname, version, type,
				client->query.dboptions, client->now,
				&node, fname, &cm,
				block_clientinfo ? NULL : &ci,
				rdataset, sigrdataset);

#ifdef	ORIGINAL_ISC_CODE
#else
	/*
	 * Clear the DNS_DBFIND_UPDATE_LAST_QUERIED flag if it was set
	 * from the return value of
	 * infoblox_get_last_queried_find_options() above.
	 */
	client->query.dboptions &= ~(DNS_DBFIND_UPDATE_LAST_QUERIED);
#endif

	/*
	 * Fixup fname and sigrdataset.
	 */
	if (dns64 && rpz) {
		isc_result_t rresult;

		rresult = dns_name_copy(client->query.qname, fname, NULL);
		RUNTIME_CHECK(rresult == ISC_R_SUCCESS);
		if (sigrdataset != NULL &&
		    dns_rdataset_isassociated(sigrdataset))
			dns_rdataset_disassociate(sigrdataset);
	}

#ifdef	ORIGINAL_ISC_CODE
#else
	// If we consulted the cache, did we match something that was expired,
	// so that this is a cache refresh ?
	if (!is_zone) {
		if (ib_rrset_extant_but_expired) {
			// Set for client when we found an expired cache entry.
			// Remains in effect until the client is destroyed.
			client->query.attributes |= NS_QUERYATTR_CACHE_REFRESH;
		}
	}

	/* An expired record having DNS_RDATASETATTR_EXPIRED_HEADER bit set
	 * must be a cached record because such bit is set for cached record
	 * only (in rbtdb.c:bind_dataset()). If such expired record is
	 * retrieved from cache, it should be re-fetched from upstream server
	 * no matter what kind of error code is returned with this record.
	 * Prefetch should happen before such rdataset is disassociated.
	 * Prefetch won't be triggered twice which is ensured by
	 * query_prefetch().
	 */
	if (!is_zone && RECURSIONOK(client) &&
	    (rdataset->attributes & DNS_RDATASETATTR_EXPIRED_HEADER) != 0) {
		/* The found rdataset may have different type rather than
		 * query type. But we will replace with query type only when
		 * the found rdataset's type is dns_rdatatype_none.
		 */
		if (rdataset->type == dns_rdatatype_none) {
			/* Clone the rdataset since we are changing type
			 * before calling query_prefetch() to it. */
			dns_rdataset_t cloned_rdataset;
			dns_rdataset_init(&cloned_rdataset);
			dns_rdataset_clone(rdataset, &cloned_rdataset);
			cloned_rdataset.type = qtype;
			query_prefetch(client, fname, &cloned_rdataset);
			dns_rdataset_disassociate(&cloned_rdataset);
		} else {
			query_prefetch(client, fname, rdataset);
		}
	}

	/*
	 * This would be used for the answer, so we need to check if GSLB
	 * processing is necessary and replace the result if it is.
	 */
	result = infoblox_query_idns_find(client, client->query.qname, type, db,
					  is_zone, &node, version, fname,
					  rdataset, &sigrdataset, result);
#endif
#ifdef WANT_QUERYTRACE
	if (!is_zone) {
		char lbuf[2048];

		snprintf(lbuf, sizeof(lbuf) - 1,
			 "db_find: dns_db_findext() returned %d (%s)",
			 result, isc_result_totext(result));
		CTRACE(ISC_LOG_DEBUG(3), lbuf);
	}
#endif

	/*
	 * Fixup fname and sigrdataset.
	 */
	if (dns64 && rpz) {
		isc_result_t rresult;

		rresult = dns_name_copy(client->query.qname, fname, NULL);
		RUNTIME_CHECK(rresult == ISC_R_SUCCESS);
		if (sigrdataset != NULL &&
		    dns_rdataset_isassociated(sigrdataset))
			dns_rdataset_disassociate(sigrdataset);
	}

	if (!is_zone)
		dns_cache_updatestats(client->view->cache, result);

 resume:
	CTRACE(ISC_LOG_DEBUG(3), "query_find: resume");

	/*
	 * Rate limit these responses to this client.
	 * Do not delay counting and handling obvious referrals,
	 *	since those won't come here again.
	 * Delay handling delegations for which we are certain to recurse and
	 *	return here (DNS_R_DELEGATION, not a child of one of our
	 *	own zones, and recursion enabled)
	 * Don't mess with responses rewritten by RPZ
	 * Count each response at most once.
	 */
	if (client->view->rrls != NULL && !HAVECOOKIE(client) &&
#ifdef ORIGINAL_ISC_CODE
#else
	    client->view->rrls[0] != NULL && client->view->rrls[0]->enabled &&
#endif
	    ((fname != NULL && dns_name_isabsolute(fname)) ||
	     (result == ISC_R_NOTFOUND && !RECURSIONOK(client))) &&
	    !(result == DNS_R_DELEGATION && !is_zone && RECURSIONOK(client)) &&
	    (client->query.rpz_st == NULL ||
	     (client->query.rpz_st->state & DNS_RPZ_REWRITTEN) == 0)&&
	    (client->query.attributes & NS_QUERYATTR_RRL_CHECKED) == 0)
	{
		dns_rdataset_t nc_rdataset;
		isc_boolean_t wouldlog;
		char log_buf[DNS_RRL_LOG_BUF_LEN];
		isc_result_t nc_result, resp_result;
		dns_rrl_result_t rrl_result;

		client->query.attributes |= NS_QUERYATTR_RRL_CHECKED;

		wouldlog = isc_log_wouldlog(ns_g_lctx, DNS_RRL_LOG_DROP);
		tname = fname;
		if (result == DNS_R_NXDOMAIN) {
			/*
			 * Use the database origin name to rate limit NXDOMAIN
			 */
			if (db != NULL)
				tname = dns_db_origin(db);
			resp_result = result;
		} else if (result == DNS_R_NCACHENXDOMAIN &&
			   rdataset != NULL &&
			   dns_rdataset_isassociated(rdataset) &&
			   (rdataset->attributes &
			    DNS_RDATASETATTR_NEGATIVE) != 0) {
			/*
			 * Try to use owner name in the negative cache SOA.
			 */
			dns_fixedname_init(&fixed);
			dns_rdataset_init(&nc_rdataset);
			for (nc_result = dns_rdataset_first(rdataset);
			     nc_result == ISC_R_SUCCESS;
			     nc_result = dns_rdataset_next(rdataset)) {
				dns_ncache_current(rdataset,
						   dns_fixedname_name(&fixed),
						   &nc_rdataset);
				if (nc_rdataset.type == dns_rdatatype_soa) {
					dns_rdataset_disassociate(&nc_rdataset);
					tname = dns_fixedname_name(&fixed);
					break;
				}
				dns_rdataset_disassociate(&nc_rdataset);
			}
			resp_result = DNS_R_NXDOMAIN;
		} else if (result == DNS_R_NXRRSET ||
			   result == DNS_R_EMPTYNAME) {
			resp_result = DNS_R_NXRRSET;
		} else if (result == DNS_R_DELEGATION) {
			resp_result = result;
		} else if (result == ISC_R_NOTFOUND) {
			/*
			 * Handle referral to ".", including when recursion
			 * is off or not requested and the hints have not
			 * been loaded or we have "additional-from-cache no".
			 */
			tname = dns_rootname;
			resp_result = DNS_R_DELEGATION;
		} else {
			resp_result = ISC_R_SUCCESS;
			client->query.qtype = qtype;
			client->query.resp_result = resp_result;
			dns_fixedname_init(&client->query.fname);
			dns_name_copy(tname,
				      dns_fixedname_name(&client->query.fname),
				      NULL);
		}
		rrl_result = dns_rrl(client->view, &client->peeraddr,
				     TCP_CLIENT(client),
				     client->message->rdclass, qtype, tname,
				     resp_result, client->now, 0, 0,
				     wouldlog, ISC_FALSE,
				     log_buf, sizeof(log_buf));
		if (rrl_result != DNS_RRL_RESULT_OK) {
			/*
			 * Log dropped or slipped responses in the query
			 * category so that requests are not silently lost.
			 * Starts of rate-limited bursts are logged in
			 * DNS_LOGCATEGORY_RRL.
			 *
			 * Dropped responses are counted with dropped queries
			 * in QryDropped while slipped responses are counted
			 * with other truncated responses in RespTruncated.
			 */
			if (wouldlog) {
				ns_client_log(client, DNS_LOGCATEGORY_RRL,
					      NS_LOGMODULE_QUERY,
					      DNS_RRL_LOG_DROP,
					      "%s", log_buf);
			}
			if (result != DNS_RRL_RESULT_LOGONLY) {
				if (rrl_result == DNS_RRL_RESULT_DROP) {
					/*
					 * These will also be counted in
					 * dns_nsstatscounter_dropped
					 */
					ns_query_incstats(client,
					    dns_nsstatscounter_ratedropped);
					QUERY_ERROR(DNS_R_DROP);
				} else {
					/*
					 * These will also be counted in
					 * dns_nsstatscounter_truncatedresp
					 */
					ns_query_incstats(client,
					    dns_nsstatscounter_rateslipped);
					if (WANTCOOKIE(client)) {
						client->message->flags &=
							~DNS_MESSAGEFLAG_AA;
						client->message->flags &=
							~DNS_MESSAGEFLAG_AD;
						client->message->rcode =
							   dns_rcode_badcookie;
					} else {
						client->message->flags |=
							DNS_MESSAGEFLAG_TC;
						if (resp_result ==
						    DNS_R_NXDOMAIN)
							client->message->rcode =
							     dns_rcode_nxdomain;
					}
				}
				goto cleanup;
			}
		}
	} else if (!TCP_CLIENT(client) && client->view->requireservercookie &&
		   WANTCOOKIE(client) && !HAVECOOKIE(client)) {
		client->message->flags &= ~DNS_MESSAGEFLAG_AA;
		client->message->flags &= ~DNS_MESSAGEFLAG_AD;
		client->message->rcode = dns_rcode_badcookie;
		goto cleanup;
	}

	if (!RECURSING(client) &&
	    !dns_name_equal(client->query.qname, dns_rootname))
	{
		isc_result_t rresult;

		rresult = rpz_rewrite(client, qtype, result, resuming,
#ifdef ORIGINAL_ISC_CODE
#else
				      is_zone,
#endif
				      rdataset, sigrdataset);
		rpz_st = client->query.rpz_st;
#ifdef ORIGINAL_ISC_CODE
#else
		/* If we haven't found an RPZ rule or we are applying a
		 * "proxy-all white list" pass through rule, the response is
		 * likely to be genuine from the real authoritative server and
		 * could be cached at firmware (DCA).  But we need to make sure
		 * this genuine answer would be returned for any subscriber
		 * regardless of their policies.  We need to consider two cases:
		 * 1. some RPZs are skipped due to SSP, in which case a rule
		 *    in other RPZ could tweak the answer.
		 * 2. this is a result of proxy-all whitelist, in which case
		 *    a rule in a less-preferred RPZ could tweak the answer.
		 * For these cases we'll re-run rpz_rewrite(), without any
		 * subscriber-specific policy.  If it still misses an RPZ rule,
		 * we can now be sure that it can be safely cached at DCA;
		 * otherwise we set the Z bit to prevent it from being cached.
		 * Note that we only do this additional check when the original
		 * rpz_rewrite returns a success.  If it requires an internal
		 * recursion (DNS_R_DELEGATION), we postpone the check until
		 * we get the final result.  This will help allow more answers
		 * to be safely cached at DCA.  If the first rpz_rewrite() fails
		 * the result will be SERVFAIL, which won't be cached at DCA
		 * anyway.  We also check 'ss_enable' to avoid unnecessary
		 * double check, although other conditions should actually
		 * ensure it's enabled. */
		if (client->view->infoblox_ss_enable &&
		    rresult == ISC_R_SUCCESS && rpz_st != NULL &&
		    ((rpz_st->m.policy == DNS_RPZ_POLICY_MISS &&
		      (rpz_st->state & IB_DNS_RPZ_SKIPPED) != 0 &&
                      !(client->view->infoblox_ss_bypass_rpz_filtering && client->subscriber != NULL &&
                        client->subscriber->action == IB_SUBSCRIBER_ACTION_PASSTHRU)) ||
		     (client->query.attributes & NS_QUERYATTR_PC_WHITELIST) != 0))
		{
			isc_result_t tresult;
			dns_rpz_st_t *saved_st = rpz_st;
			client->query.rpz_st = NULL;
			client->query.attributes |= NS_QUERYATTR_IB_REDOINGRPZ;
			tresult = rpz_rewrite(client, qtype, result, resuming,
					      is_zone, rdataset, sigrdataset);
			rpz_st = client->query.rpz_st;
			/* If we now have a rule, we clearly shouldn't allow
			 * the answer to be cached.  If it requires a recursion,
			 * we give up and decide it can't be cached at this
			 * point, not to make the process too complicated.
			 * It may not be so obvious in case this pass of
			 * rewrite fails, but we err on the side of caution
			 * and avoid caching, too. */
			if (rpz_st != NULL) {
				if (tresult != ISC_R_SUCCESS ||
				    rpz_st->m.policy != DNS_RPZ_POLICY_MISS)
					infoblox_perhaps_set_zbit(client);
				rpz_st_clear(client);
				isc_mem_put(client->mctx, rpz_st,
					    sizeof(*rpz_st));
			}
			client->query.attributes &= ~NS_QUERYATTR_IB_REDOINGRPZ;
			client->query.rpz_st = saved_st;
			rpz_st = saved_st;
			rpz_st->state &= ~IB_DNS_RPZ_SKIPPED;
		}
#endif
		switch (rresult) {
		case ISC_R_SUCCESS:
			break;
		case DNS_R_DISALLOWED:
			goto norpz;
		case DNS_R_DELEGATION:
			/*
			 * recursing for NS names or addresses,
			 * so save the main query state
			 */
			rpz_st->q.qtype = qtype;
			rpz_st->q.is_zone = is_zone;
			rpz_st->q.authoritative = authoritative;
			SAVE(rpz_st->q.zone, zone);
			SAVE(rpz_st->q.db, db);
			SAVE(rpz_st->q.node, node);
			SAVE(rpz_st->q.rdataset, rdataset);
			SAVE(rpz_st->q.sigrdataset, sigrdataset);
			dns_name_copy(fname, rpz_st->fname, NULL);
			rpz_st->q.result = result;
			client->query.attributes |= NS_QUERYATTR_RECURSING;
			goto cleanup;
		default:
			RECURSE_ERROR(rresult);
			goto cleanup;
		}
#ifdef ORIGINAL_ISC_CODE
#else
		client->query.infoblox_rpz_rewrite_candidate = ISC_TRUE;
#endif

		if (rpz_st->m.policy != DNS_RPZ_POLICY_MISS)
			rpz_st->state |= DNS_RPZ_REWRITTEN;
		if (rpz_st->m.policy != DNS_RPZ_POLICY_MISS &&
		    rpz_st->m.policy != DNS_RPZ_POLICY_PASSTHRU &&
		    (rpz_st->m.policy != DNS_RPZ_POLICY_TCP_ONLY ||
		     !TCP_CLIENT(client)) &&
		    rpz_st->m.policy != DNS_RPZ_POLICY_ERROR)
		{
			/*
			 * We got a hit and are going to answer with our
			 * fiction. Ensure that we answer with the name
			 * we looked up even if we were stopped short
			 * in recursion or for a deferral.
			 */
			rresult = dns_name_copy(client->query.qname,
						fname, NULL);
			RUNTIME_CHECK(rresult == ISC_R_SUCCESS);
			rpz_clean(&zone, &db, &node, NULL);
			if (rpz_st->m.rdataset != NULL) {
				query_putrdataset(client, &rdataset);
				RESTORE(rdataset, rpz_st->m.rdataset);
			} else if (rdataset != NULL &&
				   dns_rdataset_isassociated(rdataset)) {
				dns_rdataset_disassociate(rdataset);
			}
			version = NULL;

			RESTORE(node, rpz_st->m.node);
			RESTORE(db, rpz_st->m.db);
			RESTORE(version, rpz_st->m.version);
			RESTORE(zone, rpz_st->m.zone);

#ifdef ORIGINAL_ISC_CODE
#else
			/*
			 * If we're rewriting the response due to an RPZ rule,
			 * remember that for checking the hit rate later.
			 * Note that we exclude PASSTHRU rules; that's
			 * intentional.
			 */
			client->query.infoblox_rpz_rewritten = ISC_TRUE;

			// We shouldn't have the whitelist bit set if we're
			// going to make any changes to the answer.
			REQUIRE((client->query.attributes &
			         NS_QUERYATTR_PC_WHITELIST) == 0);
#endif

			switch (rpz_st->m.policy) {
			case DNS_RPZ_POLICY_TCP_ONLY:
				client->message->flags |= DNS_MESSAGEFLAG_TC;
				if (result == DNS_R_NXDOMAIN ||
				    result == DNS_R_NCACHENXDOMAIN)
					client->message->rcode =
						    dns_rcode_nxdomain;
				rpz_log_rewrite(client, ISC_FALSE,
						rpz_st->m.policy,
						rpz_st->m.type, zone,
						rpz_st->p_name, NULL,
#ifdef ORIGINAL_ISC_CODE
						rpz_st->m.rpz->num);
#else
						rpz_st->m.rpz->num,
						NULL, infoblox_zone_get_rpz_severity(zone),
						NULL, ISC_FALSE);
#endif
				goto cleanup;
			case DNS_RPZ_POLICY_DROP:
				QUERY_ERROR(DNS_R_DROP);
				rpz_log_rewrite(client, ISC_FALSE,
						rpz_st->m.policy,
						rpz_st->m.type, zone,
						rpz_st->p_name, NULL,
#ifdef ORIGINAL_ISC_CODE
						rpz_st->m.rpz->num);
#else
						rpz_st->m.rpz->num,
						NULL, infoblox_zone_get_rpz_severity(zone),
						NULL, ISC_FALSE);
#endif
				goto cleanup;
			case DNS_RPZ_POLICY_NXDOMAIN:
				result = DNS_R_NXDOMAIN;
				nxrewrite = ISC_TRUE;
				rpz = ISC_TRUE;
				break;
			case DNS_RPZ_POLICY_NODATA:
				nxrewrite = ISC_TRUE;
				/* FALLTHROUGH */
			case DNS_RPZ_POLICY_DNS64:
				result = DNS_R_NXRRSET;
				rpz = ISC_TRUE;
				break;
			case DNS_RPZ_POLICY_RECORD:
				result = rpz_st->m.result;
				if (qtype == dns_rdatatype_any &&
				    result != DNS_R_CNAME) {
					/*
					 * We will add all of the rdatasets of
					 * the node by iterating later,
					 * and set the TTL then.
					 */
					if (dns_rdataset_isassociated(rdataset))
					    dns_rdataset_disassociate(rdataset);
				} else {
					/*
					 * We will add this rdataset.
					 */
					rdataset->ttl = ISC_MIN(rdataset->ttl,
								rpz_st->m.ttl);
				}
				rpz = ISC_TRUE;
				break;
			case DNS_RPZ_POLICY_WILDCNAME:
				result = dns_rdataset_first(rdataset);
				RUNTIME_CHECK(result == ISC_R_SUCCESS);
				dns_rdataset_current(rdataset, &rdata);
				result = dns_rdata_tostruct(&rdata, &cname,
							    NULL);
				RUNTIME_CHECK(result == ISC_R_SUCCESS);
				dns_rdata_reset(&rdata);
				result = rpz_add_cname(client, rpz_st,
						       &cname.cname,
						       fname, dbuf);
				if (result != ISC_R_SUCCESS)
					goto cleanup;
				fname = NULL;
				want_restart = ISC_TRUE;
				goto cleanup;
			case DNS_RPZ_POLICY_CNAME:
				/*
				 * Add overridding CNAME from a named.conf
				 * response-policy statement
				 */
				result = rpz_add_cname(client, rpz_st,
						       &rpz_st->m.rpz->cname,
						       fname, dbuf);
				if (result != ISC_R_SUCCESS)
					goto cleanup;
				fname = NULL;
				want_restart = ISC_TRUE;
				goto cleanup;
			default:
				INSIST(0);
			}

			/*
			 * Turn off DNSSEC because the results of a
			 * response policy zone cannot verify.
			 */
			client->attributes &= ~(NS_CLIENTATTR_WANTDNSSEC |
						NS_CLIENTATTR_WANTAD);
			client->message->flags &= ~DNS_MESSAGEFLAG_AD;
			query_putrdataset(client, &sigrdataset);
			is_zone = ISC_TRUE;
#ifdef ORIGINAL_ISC_CODE
			rpz_log_rewrite(client, ISC_FALSE, rpz_st->m.policy,
					rpz_st->m.type, zone, rpz_st->p_name,
					NULL, rpz_st->m.rpz->num);
#else
			infoblox_rpz_redirect_info_t rri, *rrip;
			if (rpz_st->m.policy == DNS_RPZ_POLICY_RECORD) {
				rri.db = db;
				rri.node = node;
				rri.version = version;
				rrip = &rri;
			} else {
				rrip = NULL;
			}
			rpz_log_rewrite(client, ISC_FALSE, rpz_st->m.policy,
					rpz_st->m.type, zone, rpz_st->p_name,
					NULL, rpz_st->m.rpz->num,
					rrip, infoblox_zone_get_rpz_severity(zone),
					NULL, ISC_FALSE);
#endif
		}
	}

 norpz:
#ifdef ORIGINAL_ISC_CODE
#else
	if (IBALIASINITIATED(client)) {
		/* IBALIAS record was found and we are in the process of
		 * searching records for IBALIAS target name. */
		if (result == DNS_R_NXDOMAIN) {
			/* DNS_R_NXDOMAIN indicates target name of matching
			 * IBALIAS record doesn't exist, not the query name
			 * which IBALIAS record belongs to, so here we change
			 * error code to DNS_R_NXRRSET.
			 */
			result = DNS_R_NXRRSET;
		}
	}
#endif
	switch (result) {
	case ISC_R_SUCCESS:
		/*
		 * ISC_R_SUCCESS case is handled in the main line below.
		 */
#ifdef ORIGINAL_ISC_CODE
#else
		infoblox_increment_cache(INFOBLOX_IS_CACHE, ISC_TRUE, client);
#endif

		/*
		 * If we found the answer in the cache, copy the SCOPE
		 * PREFIX-LENGTH to the client option.
		 */
		if (!is_zone && (event == NULL) && ECS_RECEIVED(client) &&
		    (client->ecs.scope == 0xff))
			client->ecs.scope = ci.ecs.scope;
		break;
	case DNS_R_GLUE:
	case DNS_R_ZONECUT:
		/*
		 * These cases are handled in the main line below.
		 */
		INSIST(is_zone);
		authoritative = ISC_FALSE;
#ifdef ORIGINAL_ISC_CODE
#else
		infoblox_increment_cache(INFOBLOX_IS_CACHE, ISC_TRUE, client);
#endif
		break;
	case ISC_R_NOTFOUND:
		/*
		 * The cache doesn't even have the root NS.  Get them from
		 * the hints DB.
		 */
#ifdef ORIGINAL_ISC_CODE
		INSIST(!is_zone);
#else
		if (is_zone)
		  {
		    if (dns_zone_is_zdb(zone))
		      {
			// The zone may have been deleted, but this could also
			// be due to some more serious problem. Emit a SERVFAIL
			// error to let the client know that we could give no
			// certain answer one way or another.
			dns_zone_log (zone, ISC_LOG_ERROR,
				      "No NS records; zone may have been deleted");
			QUERY_ERROR (DNS_R_SERVFAIL);
			goto cleanup;
		      }
		    else
		      {
			// Assertion failure as in the vanilla code
			INSIST(!is_zone);
		      }
		  }
#endif
		if (db != NULL)
			dns_db_detach(&db);

		if (client->view->hints == NULL) {
			/* We have no hints. */
			result = ISC_R_FAILURE;
		} else {
			dns_db_attach(client->view->hints, &db);
			result = dns_db_findext(db, dns_rootname,
						NULL, dns_rdatatype_ns,
						0, client->now, &node,
						fname, &cm, &ci,
						rdataset, sigrdataset);
		}
		if (result != ISC_R_SUCCESS) {
			/*
			 * Nonsensical root hints may require cleanup.
			 */
			if (dns_rdataset_isassociated(rdataset))
				dns_rdataset_disassociate(rdataset);
			if (sigrdataset != NULL &&
			    dns_rdataset_isassociated(sigrdataset))
				dns_rdataset_disassociate(sigrdataset);
			if (node != NULL)
				dns_db_detachnode(db, &node);

			/*
			 * We don't have any root server hints, but
			 * we may have working forwarders, so try to
			 * recurse anyway.
			 */
			if (RECURSIONOK(client)) {
				INSIST(!REDIRECT(client));
				result = query_recurse(client, qtype,
						       client->query.qname,
						       NULL, NULL, resuming);
				if (result == ISC_R_SUCCESS) {
					client->query.attributes |=
						NS_QUERYATTR_RECURSING;
					if (dns64)
						client->query.attributes |=
							NS_QUERYATTR_DNS64;
					if (dns64_exclude)
						client->query.attributes |=
						      NS_QUERYATTR_DNS64EXCLUDE;
				} else
					RECURSE_ERROR(result);
				goto cleanup;
			} else {
				/* Unable to give root server referral. */
				CTRACE(ISC_LOG_ERROR,
				       "unable to give root server referral");
				QUERY_ERROR(DNS_R_SERVFAIL);
				goto cleanup;
			}
		}
		/*
		 * XXXRTH  We should trigger root server priming here.
		 */
		/* FALLTHROUGH */
	case DNS_R_DELEGATION:
		authoritative = ISC_FALSE;
		if (is_zone) {
			/*
			 * Look to see if we are authoritative for the
			 * child zone if the query type is DS.
			 */
			if (!RECURSIONOK(client) &&
			    (options & DNS_GETDB_NOEXACT) != 0 &&
			    qtype == dns_rdatatype_ds) {
				dns_db_t *tdb = NULL;
				dns_zone_t *tzone = NULL;
				dns_dbversion_t *tversion = NULL;
				result = query_getzonedb(client,
							 client->query.qname,
							 qtype,
							 DNS_GETDB_PARTIAL,
							 &tzone, &tdb,
							 &tversion);
				if (result == ISC_R_SUCCESS) {
					options &= ~DNS_GETDB_NOEXACT;
					query_putrdataset(client, &rdataset);
					if (sigrdataset != NULL)
						query_putrdataset(client,
								  &sigrdataset);
					if (fname != NULL)
						query_releasename(client,
								  &fname);
					if (node != NULL)
						dns_db_detachnode(db, &node);
					if (db != NULL)
						dns_db_detach(&db);
					if (zone != NULL)
						dns_zone_detach(&zone);
					version = NULL;
					RESTORE(version, tversion);
					RESTORE(db, tdb);
					RESTORE(zone, tzone);
					authoritative = ISC_TRUE;
					goto db_find;
				}
				if (tdb != NULL)
					dns_db_detach(&tdb);
				if (tzone != NULL)
					dns_zone_detach(&tzone);
			}
			/*
			 * We're authoritative for an ancestor of QNAME.
			 */
			if (!USECACHE(client) || !RECURSIONOK(client)) {
				isc_boolean_t detach = ISC_FALSE;

				dns_fixedname_init(&fixed);
				dns_name_copy(fname,
					      dns_fixedname_name(&fixed), NULL);

				/*
				 * If we don't have a cache, this is the best
				 * answer.
				 *
				 * If the client is making a nonrecursive
				 * query we always give out the authoritative
				 * delegation.  This way even if we get
				 * junk in our cache, we won't fail in our
				 * role as the delegating authority if another
				 * nameserver asks us about a delegated
				 * subzone.
				 *
				 * We enable the retrieval of glue for this
				 * database by setting client->query.gluedb.
				 */
				if (db != NULL &&
				    client->query.gluedb == NULL) {
					dns_db_attach(db,
						      &client->query.gluedb);
					detach = ISC_TRUE;
				}
				client->query.isreferral = ISC_TRUE;
				/*
				 * We must ensure NOADDITIONAL is off,
				 * because the generation of
				 * additional data is required in
				 * delegations.
				 */
				client->query.attributes &=
					~NS_QUERYATTR_NOADDITIONAL;
				if (sigrdataset != NULL)
					sigrdatasetp = &sigrdataset;
				else
					sigrdatasetp = NULL;
				query_addrrset(client, &fname,
					       &rdataset, sigrdatasetp,
					       dbuf, DNS_SECTION_AUTHORITY);
				if (detach) {
					dns_db_detach(&client->query.gluedb);
				}
				if (WANTDNSSEC(client))
					query_addds(client, db, node, version,
						   dns_fixedname_name(&fixed));
#ifdef ORIGINAL_ISC_CODE
#else
				infoblox_increment_cache(INFOBLOX_IS_CACHE, ISC_TRUE, client);
#endif
			} else {
				/*
				 * We might have a better answer or delegation
				 * in the cache.  We'll remember the current
				 * values of fname, rdataset, and sigrdataset.
				 * We'll then go looking for QNAME in the
				 * cache.  If we find something better, we'll
				 * use it instead.
				 */
				query_keepname(client, fname, dbuf);
				dns_db_detachnode(db, &node);
				SAVE(zdb, db);
				SAVE(zfname, fname);
				SAVE(zversion, version);
				SAVE(zrdataset, rdataset);
				SAVE(zsigrdataset, sigrdataset);
				dns_db_attach(client->view->cachedb, &db);
				is_zone = ISC_FALSE;
				goto db_find;
			}
		} else {
			if (zfname != NULL &&
			    (!dns_name_issubdomain(fname, zfname) ||
			     (is_staticstub_zone &&
			      dns_name_equal(fname, zfname)))) {
				/*
				 * In the following cases use "authoritative"
				 * data instead of the cache delegation:
				 * 1. We've already got a delegation from
				 *    authoritative data, and it is better
				 *    than what we found in the cache.
				 * 2. The query name matches the origin name
				 *    of a static-stub zone.  This needs to be
				 *    considered for the case where the NS of
				 *    the static-stub zone and the cached NS
				 *    are different.  We still need to contact
				 *    the nameservers configured in the
				 *    static-stub zone.
				 */
				query_releasename(client, &fname);
				/*
				 * We've already done query_keepname() on
				 * zfname, so we must set dbuf to NULL to
				 * prevent query_addrrset() from trying to
				 * call query_keepname() again.
				 */
				dbuf = NULL;
				query_putrdataset(client, &rdataset);
				if (sigrdataset != NULL)
					query_putrdataset(client,
							  &sigrdataset);
				version = NULL;

				RESTORE(fname, zfname);
				RESTORE(version, zversion);
				RESTORE(rdataset, zrdataset);
				RESTORE(sigrdataset, zsigrdataset);
				/*
				 * We don't clean up zdb here because we
				 * may still need it.  It will get cleaned
				 * up by the main cleanup code.
				 */
			}

			if (RECURSIONOK(client)) {
				/*
				 * Recurse!
				 */
				INSIST(!REDIRECT(client));
				if (dns_rdatatype_atparent(type))
					result = query_recurse(client, qtype,
							 client->query.qname,
							 NULL, NULL, resuming);
				else if (dns64)
					result = query_recurse(client,
							 dns_rdatatype_a,
							 client->query.qname,
							 NULL, NULL, resuming);
				else
					result = query_recurse(client, qtype,
							 client->query.qname,
							 fname, rdataset,
							 resuming);

				if (result == ISC_R_SUCCESS) {
					client->query.attributes |=
						NS_QUERYATTR_RECURSING;
					if (dns64)
						client->query.attributes |=
							NS_QUERYATTR_DNS64;
					if (dns64_exclude)
						client->query.attributes |=
						      NS_QUERYATTR_DNS64EXCLUDE;
				} else if (result == DNS_R_DUPLICATE ||
					   result == DNS_R_DROP ||
					   result == DNS_R_REFUSED)
					QUERY_ERROR(result);
				else
					RECURSE_ERROR(result);
			} else {
				isc_boolean_t detach = ISC_FALSE;

				dns_fixedname_init(&fixed);
				dns_name_copy(fname,
					      dns_fixedname_name(&fixed), NULL);
				/*
				 * This is the best answer.
				 */
				client->query.attributes |=
					NS_QUERYATTR_CACHEGLUEOK;
				client->query.isreferral = ISC_TRUE;

				if (zdb != NULL &&
				    client->query.gluedb == NULL) {
					dns_db_attach(zdb,
						      &client->query.gluedb);
					detach = ISC_TRUE;
				}

				/*
				 * We must ensure NOADDITIONAL is off,
				 * because the generation of
				 * additional data is required in
				 * delegations.
				 */
				client->query.attributes &=
					~NS_QUERYATTR_NOADDITIONAL;
				if (sigrdataset != NULL)
					sigrdatasetp = &sigrdataset;
				else
					sigrdatasetp = NULL;
				query_addrrset(client, &fname,
					       &rdataset, sigrdatasetp,
					       dbuf, DNS_SECTION_AUTHORITY);
				client->query.attributes &=
					~NS_QUERYATTR_CACHEGLUEOK;
				if (detach) {
					dns_db_detach(&client->query.gluedb);
				}

				if (WANTDNSSEC(client))
					query_addds(client, db, node, version,
						   dns_fixedname_name(&fixed));
#ifdef ORIGINAL_ISC_CODE
#else
				infoblox_increment_cache(INFOBLOX_IS_CACHE, ISC_TRUE, client);
#endif
			}
		}
		goto cleanup;

	case DNS_R_EMPTYNAME:
	case DNS_R_NXRRSET:
	iszone_nxrrset:
		INSIST(is_zone);

#ifdef dns64_bis_return_excluded_addresses
		if (dns64)
#else
		if (dns64 && !dns64_exclude)
#endif
		{
			/*
			 * Restore the answers from the previous AAAA lookup.
			 */
			if (rdataset != NULL)
				query_putrdataset(client, &rdataset);
			if (sigrdataset != NULL)
				query_putrdataset(client, &sigrdataset);
			RESTORE(rdataset, client->query.dns64_aaaa);
			RESTORE(sigrdataset, client->query.dns64_sigaaaa);
			if (fname == NULL) {
				dbuf = query_getnamebuf(client);
				if (dbuf == NULL) {
					CTRACE(ISC_LOG_ERROR,
					       "query_find: "
					       "query_getnamebuf failed (3)");
					QUERY_ERROR(DNS_R_SERVFAIL);
					goto cleanup;
				}
				fname = query_newname(client, dbuf, &b);
				if (fname == NULL) {
					CTRACE(ISC_LOG_ERROR,
					       "query_find: "
					       "query_newname failed (3)");
					QUERY_ERROR(DNS_R_SERVFAIL);
					goto cleanup;
				}
			}
			dns_name_copy(client->query.qname, fname, NULL);
			dns64 = ISC_FALSE;
#ifdef dns64_bis_return_excluded_addresses
			/*
			 * Resume the diverted processing of the AAAA response?
			 */
			if (dns64_excluded)
				break;
#endif
		} else if (result == DNS_R_NXRRSET &&
			   !ISC_LIST_EMPTY(client->view->dns64) &&
			   !nxrewrite &&
			   client->message->rdclass == dns_rdataclass_in &&
			   qtype == dns_rdatatype_aaaa)
		{
			/*
			 * Look to see if there are A records for this
			 * name.
			 */
			SAVE(client->query.dns64_aaaa, rdataset);
			SAVE(client->query.dns64_sigaaaa, sigrdataset);
			client->query.dns64_ttl = dns64_ttl(db, version);
			query_releasename(client, &fname);
			dns_db_detachnode(db, &node);
			type = qtype = dns_rdatatype_a;
			dns64 = ISC_TRUE;
			goto db_find;
		}

		/*
		 * Look for a NSEC3 record if we don't have a NSEC record.
		 */
 nxrrset_rrsig:
		if (redirected)
			goto cleanup;
		if (!dns_rdataset_isassociated(rdataset) &&
		     WANTDNSSEC(client)) {
			if ((fname->attributes & DNS_NAMEATTR_WILDCARD) == 0) {
				dns_name_t *found;
				dns_name_t *qname;

				dns_fixedname_init(&fixed);
				found = dns_fixedname_name(&fixed);
				qname = client->query.qname;

				query_findclosestnsec3(qname, db, version,
						       client, rdataset,
						       sigrdataset, fname,
						       ISC_TRUE, found);
				/*
				 * Did we find the closest provable encloser
				 * instead? If so add the nearest to the
				 * closest provable encloser.
				 */
				if (dns_rdataset_isassociated(rdataset) &&
				    !dns_name_equal(qname, found) &&
				    !(ns_g_nonearest &&
				      qtype != dns_rdatatype_ds))
				{
					unsigned int count;
					unsigned int skip;

					/*
					 * Add the closest provable encloser.
					 */
					query_addrrset(client, &fname,
						       &rdataset, &sigrdataset,
						       dbuf,
						       DNS_SECTION_AUTHORITY);

					count = dns_name_countlabels(found)
							 + 1;
					skip = dns_name_countlabels(qname) -
							 count;
					dns_name_getlabelsequence(qname, skip,
								  count,
								  found);

					fixfname(client, &fname, &dbuf, &b);
					fixrdataset(client, &rdataset);
					fixrdataset(client, &sigrdataset);
					if (fname == NULL ||
					    rdataset == NULL ||
					    sigrdataset == NULL) {
						CTRACE(ISC_LOG_ERROR,
						       "query_find: "
						       "failure getting "
						       "closest encloser");
						QUERY_ERROR(DNS_R_SERVFAIL);
						goto cleanup;
					}
					/*
					 * 'nearest' doesn't exist so
					 * 'exist' is set to ISC_FALSE.
					 */
					query_findclosestnsec3(found, db,
							       version,
							       client,
							       rdataset,
							       sigrdataset,
							       fname,
							       ISC_FALSE,
							       NULL);
				}
			} else {
				query_releasename(client, &fname);
				query_addwildcardproof(client, db, version,
						       client->query.qname,
#ifdef ORIGINAL_ISC_CODE
#else
						       NULL, NULL, NULL,
#endif
						       ISC_FALSE, ISC_TRUE);
			}
		}
		if (dns_rdataset_isassociated(rdataset)) {
			/*
			 * If we've got a NSEC record, we need to save the
			 * name now because we're going call query_addsoa()
			 * below, and it needs to use the name buffer.
			 */
			query_keepname(client, fname, dbuf);
		} else if (fname != NULL) {
			/*
			 * We're not going to use fname, and need to release
			 * our hold on the name buffer so query_addsoa()
			 * may use it.
			 */
			query_releasename(client, &fname);
		}

		/*
		 * Add SOA to the additional section if generated by a RPZ
		 * rewrite.
		 */
		associated = dns_rdataset_isassociated(rdataset);
		section = nxrewrite ? DNS_SECTION_ADDITIONAL :
				      DNS_SECTION_AUTHORITY;

#ifdef ORIGINAL_ISC_CODE
#else
		/* If the NXRRSET result isn't due to RPZ rewrite and IBALIAS
		 * record is found in search path, we need add SOA record from
		 * the zone where IBALIAS record resides in, rather than the
		 * zone where the IBALIAS record target name resides in.
		 *
		 * In the case that NXRRSET result is due to RPZ rewrite, RPZ
		 * always adds SOA of RPZ zone into ADDITIONAL section in
		 * response. We will keep this RPZ convention even if IBALIAS
		 * record is involved.
		 */
		if (IBALIASINITIATED(client) && !nxrewrite)
			result = query_ibalias_addsoa(client);
		else
#endif
		result = query_addsoa(client, db, version, ISC_UINT32_MAX,
				      associated, section);
		if (result != ISC_R_SUCCESS) {
			QUERY_ERROR(result);
			goto cleanup;
		}

		/*
		 * Add NSEC record if we found one.
		 */
		if (WANTDNSSEC(client)) {
			if (dns_rdataset_isassociated(rdataset))
				query_addnxrrsetnsec(client, db, version,
						     &fname, &rdataset,
						     &sigrdataset);
		}
#ifdef ORIGINAL_ISC_CODE
#else
		infoblox_increment_cache(INFOBLOX_IS_CACHE, ISC_TRUE, client);
#endif
		goto cleanup;

	case DNS_R_EMPTYWILD:
		empty_wild = ISC_TRUE;
		/* FALLTHROUGH */

	case DNS_R_NXDOMAIN:
		INSIST(is_zone || REDIRECT(client));
		if (!empty_wild) {
			tresult = redirect(client, fname, rdataset, &node,
					   &db, &version, type);
			if (tresult == ISC_R_SUCCESS) {
				ns_query_incstats(client,
					  dns_nsstatscounter_nxdomainredirect);
				break;
			}
			if (tresult == DNS_R_NXRRSET) {
				redirected = ISC_TRUE;
				goto iszone_nxrrset;
			}
			if (tresult == DNS_R_NCACHENXRRSET) {
				redirected = ISC_TRUE;
				is_zone = ISC_FALSE;
				goto ncache_nxrrset;
			}
			tresult = redirect2(client, fname, rdataset, &node,
					    &db, &version, type, &is_zone);
			if (tresult == DNS_R_CONTINUE) {
				ns_query_incstats(client,
				   dns_nsstatscounter_nxdomainredirect_rlookup);
				client->query.redirect.qtype = qtype;
				INSIST(rdataset != NULL);
				SAVE(client->query.redirect.rdataset, rdataset);
				SAVE(client->query.redirect.sigrdataset,
				     sigrdataset);
				SAVE(client->query.redirect.db, db);
				SAVE(client->query.redirect.node, node);
				SAVE(client->query.redirect.zone, zone);
				client->query.redirect.result = DNS_R_NXDOMAIN;
				dns_name_copy(fname,
					      client->query.redirect.fname,
					      NULL);
				client->query.redirect.authoritative =
					authoritative;
				client->query.redirect.is_zone = is_zone;
				goto cleanup;
			}
			if (tresult == ISC_R_SUCCESS) {
				ns_query_incstats(client,
					  dns_nsstatscounter_nxdomainredirect);
				break;
			}
			if (tresult == DNS_R_NXRRSET) {
				redirected = ISC_TRUE;
				goto iszone_nxrrset;
			}
			if (tresult == DNS_R_NCACHENXRRSET) {
				redirected = ISC_TRUE;
				is_zone = ISC_FALSE;
				goto ncache_nxrrset;
			}
		}
		if (dns_rdataset_isassociated(rdataset)) {
			/*
			 * If we've got a NSEC record, we need to save the
			 * name now because we're going call query_addsoa()
			 * below, and it needs to use the name buffer.
			 */
			query_keepname(client, fname, dbuf);
		} else if (fname != NULL) {
			/*
			 * We're not going to use fname, and need to release
			 * our hold on the name buffer so query_addsoa()
			 * may use it.
			 */
			query_releasename(client, &fname);
		}

		/*
		 * Add SOA to the additional section if generated by a
		 * RPZ rewrite.
		 *
		 * If the query was for a SOA record force the
		 * ttl to zero so that it is possible for clients to find
		 * the containing zone of an arbitrary name with a stub
		 * resolver and not have it cached.
		 */
		associated = dns_rdataset_isassociated(rdataset);
		section = nxrewrite ? DNS_SECTION_ADDITIONAL :
				      DNS_SECTION_AUTHORITY;
		ttl = ISC_UINT32_MAX;
		if (!nxrewrite && qtype == dns_rdatatype_soa &&
		    zone != NULL && dns_zone_getzeronosoattl(zone))
			ttl = 0;
		result = query_addsoa(client, db, version, ttl, associated,
				      section);
		if (result != ISC_R_SUCCESS) {
			QUERY_ERROR(result);
			goto cleanup;
		}

		if (WANTDNSSEC(client)) {
#ifdef ORIGINAL_ISC_CODE
#else
			// We already have the result of a search for the client->query.qname,
			// So we can pass those to query_addwildcardproof(), which saves it from
			// having to look up the data again. However, query_addrrset() may reset
			// the pointers, so we must save copies (it doesn't release the items to
			// which the pointers, well, point; it just zeroes out the pointers).
			dns_name_t *ofname = fname;
			dns_rdataset_t *tmprdataset = rdataset;
			dns_rdataset_t *tmpsigrdataset = sigrdataset;
#endif
			/*
			 * Add NSEC record if we found one.
			 */
			if (dns_rdataset_isassociated(rdataset))
				query_addrrset(client, &fname, &rdataset,
					       &sigrdataset,
					       NULL, DNS_SECTION_AUTHORITY);
#ifdef ORIGINAL_ISC_CODE
			query_addwildcardproof(client, db, version,
					       client->query.qname, ISC_FALSE,
					       ISC_FALSE);
#else
			query_addwildcardproof(client, db, version,
                                               client->query.qname,
					       ofname, tmprdataset, tmpsigrdataset,
					       ISC_FALSE, ISC_FALSE);
#endif
		}

		/*
		 * Set message rcode.
		 */
		if (empty_wild)
			client->message->rcode = dns_rcode_noerror;
		else
			client->message->rcode = dns_rcode_nxdomain;
#ifdef ORIGINAL_ISC_CODE
#else
		infoblox_increment_cache(INFOBLOX_IS_CACHE, ISC_TRUE, client);
#endif
		goto cleanup;

	case DNS_R_NCACHENXDOMAIN:
		tresult = redirect(client, fname, rdataset, &node,
				   &db, &version, type);
		if (tresult == ISC_R_SUCCESS) {
			ns_query_incstats(client,
					  dns_nsstatscounter_nxdomainredirect);
			break;
		}
		if (tresult == DNS_R_NXRRSET) {
			redirected = ISC_TRUE;
			is_zone = ISC_TRUE;
			goto iszone_nxrrset;
		}
		if (tresult == DNS_R_NCACHENXRRSET) {
			redirected = ISC_TRUE;
			result = tresult;
			goto ncache_nxrrset;
		}
		tresult = redirect2(client, fname, rdataset, &node,
				    &db, &version, type, &is_zone);
		if (tresult == DNS_R_CONTINUE) {
			ns_query_incstats(client,
				  dns_nsstatscounter_nxdomainredirect_rlookup);
			SAVE(client->query.redirect.db, db);
			SAVE(client->query.redirect.node, node);
			SAVE(client->query.redirect.zone, zone);
			client->query.redirect.qtype = qtype;
			INSIST(rdataset != NULL);
			SAVE(client->query.redirect.rdataset, rdataset);
			SAVE(client->query.redirect.sigrdataset, sigrdataset);
			client->query.redirect.result = DNS_R_NCACHENXDOMAIN;
			dns_name_copy(fname, client->query.redirect.fname,
				      NULL);
			client->query.redirect.authoritative = authoritative;
			client->query.redirect.is_zone = is_zone;
			goto cleanup;
		}
		if (tresult == ISC_R_SUCCESS) {
			ns_query_incstats(client,
					  dns_nsstatscounter_nxdomainredirect);
			break;
		}
		if (tresult == DNS_R_NXRRSET) {
			redirected = ISC_TRUE;
			is_zone = ISC_TRUE;
			goto iszone_nxrrset;
		}
		if (tresult == DNS_R_NCACHENXRRSET) {
			redirected = ISC_TRUE;
			result = tresult;
			goto ncache_nxrrset;
		}
		/* FALLTHROUGH */

	case DNS_R_NCACHENXRRSET:
	ncache_nxrrset:
		INSIST(!is_zone);
#ifdef ORIGINAL_ISC_CODE
#else
		// If so configured, intercept NXDOMAIN responses. Any error
		// is logged, but then we fall through to normal NXDOMAIN
		// processing.
		if (ib_do_nxdomain_modify(client, qtype, rdataset, result)) {
			isc_result_t nxresult;
			nxresult = infoblox_get_nxdomain_redirect_rdataset(client->view,
									   client->query.qname, &node, &db, rdataset, &nxrds, qtype);
			if (nxresult == ISC_R_SUCCESS) {
				/* log query */
				if (client->view->nx_log_query)
				{
					char qname_buf[DNS_NAME_FORMATSIZE];
					char onbuf[ISC_NETADDR_FORMATSIZE] = "<unknown>";

					dns_name_format(client->query.qname,
							qname_buf,
							sizeof(qname_buf));
					if (client->peeraddr_valid)
					{
						isc_netaddr_t srcaddr;
						isc_netaddr_fromsockaddr(&srcaddr, &client->peeraddr);
						isc_netaddr_format(&srcaddr, onbuf, sizeof(onbuf));
					}

					isc_log_write(ns_g_lctx, NS_LOGCATEGORY_CLIENT,
						      NS_LOGMODULE_QUERY, ISC_LOG_INFO,
						      "Redirected NXDOMAIN query: \'%s\' (%s)",
						      qname_buf, onbuf);
				}
				// Count as a cache hit
				infoblox_increment_cache(INFOBLOX_IS_CACHE, ISC_TRUE, client);
				// No AUTHORITY or ADDITIONAL sections
				client->query.attributes |= (NS_QUERYATTR_NOAUTHORITY |
							     NS_QUERYATTR_NOADDITIONAL);
				break;
			}
			// Else just fall through
		}
		client->query.attributes &= ~NS_QUERYATTR_SKIPNXDOMAINREDIRECT;
#endif
		authoritative = ISC_FALSE;
		/*
		 * Set message rcode, if required.
		 */
#ifdef ORIGINAL_ISC_CODE
		if (result == DNS_R_NCACHENXDOMAIN)
			client->message->rcode = dns_rcode_nxdomain;
#else
		if (result == DNS_R_NCACHENXDOMAIN) {
			/* IBALIAS record was found and we are in the process
			 * of recursive querying records for IBALIAS target
			 * name.
			 * DNS_R_NCACHENXDOMAIN indicates target name of
			 * matching IBALIAS record doesn't exist, not the query
			 * name which IBALIAS record belongs to, so here we
			 * change error code to NOERROR.
			 */
			if (IBALIASINITIATED(client))
				client->message->rcode = dns_rcode_noerror;
			else
				client->message->rcode = dns_rcode_nxdomain;
		}
#endif
		/*
		 * Look for RFC 1918 leakage from Internet.
		 */
		if (result == DNS_R_NCACHENXDOMAIN &&
		    qtype == dns_rdatatype_ptr &&
		    client->message->rdclass == dns_rdataclass_in &&
		    dns_name_countlabels(fname) == 7)
			warn_rfc1918(client, fname, rdataset);

#ifdef dns64_bis_return_excluded_addresses
		if (dns64)
#else
		if (dns64 && !dns64_exclude)
#endif
		{
			/*
			 * Restore the answers from the previous AAAA lookup.
			 */
			if (rdataset != NULL)
				query_putrdataset(client, &rdataset);
			if (sigrdataset != NULL)
				query_putrdataset(client, &sigrdataset);
			RESTORE(rdataset, client->query.dns64_aaaa);
			RESTORE(sigrdataset, client->query.dns64_sigaaaa);
			if (fname == NULL) {
				dbuf = query_getnamebuf(client);
				if (dbuf == NULL) {
					CTRACE(ISC_LOG_ERROR,
					       "query_find: "
					       "query_getnamebuf failed (4)");
					QUERY_ERROR(DNS_R_SERVFAIL);
					goto cleanup;
				}
				fname = query_newname(client, dbuf, &b);
				if (fname == NULL) {
					CTRACE(ISC_LOG_ERROR,
					       "query_find: "
					       "query_newname failed (4)");
					QUERY_ERROR(DNS_R_SERVFAIL);
					goto cleanup;
				}
			}
			dns_name_copy(client->query.qname, fname, NULL);
			dns64 = ISC_FALSE;
#ifdef dns64_bis_return_excluded_addresses
			if (dns64_excluded)
				break;
#endif
		} else if (result == DNS_R_NCACHENXRRSET &&
			   !ISC_LIST_EMPTY(client->view->dns64) &&
			   client->message->rdclass == dns_rdataclass_in &&
			   qtype == dns_rdatatype_aaaa)
		{
			/*
			 * Look to see if there are A records for this
			 * name.
			 */

			/*
			 * If the ttl is zero we need to workout if we have just
			 * decremented to zero or if there was no negative cache
			 * ttl in the answer.
			 */
			if (rdataset->ttl != 0)
				client->query.dns64_ttl = rdataset->ttl;
			else if (dns_rdataset_first(rdataset) == ISC_R_SUCCESS)
				client->query.dns64_ttl = 0;
			SAVE(client->query.dns64_aaaa, rdataset);
			SAVE(client->query.dns64_sigaaaa, sigrdataset);
			query_releasename(client, &fname);
			dns_db_detachnode(db, &node);
			type = qtype = dns_rdatatype_a;
			dns64 = ISC_TRUE;
			goto db_find;
		}

		/*
		 * We don't call query_addrrset() because we don't need any
		 * of its extra features (and things would probably break!).
		 */
		if (dns_rdataset_isassociated(rdataset)) {
			query_keepname(client, fname, dbuf);
			dns_message_addname(client->message, fname,
					    DNS_SECTION_AUTHORITY);
			ISC_LIST_APPEND(fname->list, rdataset, link);
			fname = NULL;
			rdataset = NULL;
		}
#ifdef ORIGINAL_ISC_CODE
#else
		infoblox_increment_cache(INFOBLOX_IS_CACHE, ISC_TRUE, client);
#endif
		goto cleanup;

	case DNS_R_CNAME:
 		/*
		 * If we have a zero ttl from the cache refetch it.
		 */
		if (!is_zone && !resuming && rdataset->ttl == 0 &&
		    RECURSIONOK(client))
		{
			if (dns_rdataset_isassociated(rdataset))
				dns_rdataset_disassociate(rdataset);
			if (sigrdataset != NULL &&
			    dns_rdataset_isassociated(sigrdataset))
				dns_rdataset_disassociate(sigrdataset);
			if (node != NULL)
				dns_db_detachnode(db, &node);

			result = query_recurse(client, qtype,
					       client->query.qname,
					       NULL, NULL, resuming);
			if (result == ISC_R_SUCCESS) {
				client->query.attributes |=
					NS_QUERYATTR_RECURSING;
				if (dns64)
					client->query.attributes |=
						NS_QUERYATTR_DNS64;
				if (dns64_exclude)
					client->query.attributes |=
					      NS_QUERYATTR_DNS64EXCLUDE;
			} else
				RECURSE_ERROR(result);
			goto cleanup;
		}

		/*
		 * If we found the answer in the cache, copy the SCOPE
		 * PREFIX-LENGTH to the client option.
		 */
		if (!is_zone && (event == NULL) && ECS_RECEIVED(client) &&
		    (client->ecs.scope == 0xff))
			client->ecs.scope = ci.ecs.scope;

#ifdef ORIGINAL_ISC_CODE
		/*
		 * If we have a zero ttl from the cache refetch it.
		 */
		if (!is_zone && !resuming && rdataset->ttl == 0 &&
		    RECURSIONOK(client))
		{
			if (dns_rdataset_isassociated(rdataset))
				dns_rdataset_disassociate(rdataset);
			if (sigrdataset != NULL &&
			    dns_rdataset_isassociated(sigrdataset))
				dns_rdataset_disassociate(sigrdataset);
			if (node != NULL)
				dns_db_detachnode(db, &node);

			INSIST(!REDIRECT(client));
			result = query_recurse(client, qtype,
					       client->query.qname,
					       NULL, NULL, resuming);
			if (result == ISC_R_SUCCESS) {
				client->query.attributes |=
					NS_QUERYATTR_RECURSING;
				if (dns64)
					client->query.attributes |=
						NS_QUERYATTR_DNS64;
				if (dns64_exclude)
					client->query.attributes |=
					      NS_QUERYATTR_DNS64EXCLUDE;
			} else
				RECURSE_ERROR(result);
			goto cleanup;
		}
#else
		/*
		 * Remove duplicated block of code (which may have come
		 * from a bad ECS merge). See a few lines above for the
		 * initial block of code that this is a copy of.
		 */
#endif

		/*
		 * Keep a copy of the rdataset.  We have to do this because
		 * query_addrrset may clear 'rdataset' (to prevent the
		 * cleanup code from cleaning it up).
		 */
		trdataset = rdataset;
		/*
		 * Add the CNAME to the answer section.
		 */
		if (sigrdataset != NULL)
			sigrdatasetp = &sigrdataset;
		else
			sigrdatasetp = NULL;
		if (WANTDNSSEC(client) &&
		    (fname->attributes & DNS_NAMEATTR_WILDCARD) != 0)
		{
			dns_fixedname_init(&wildcardname);
			dns_name_copy(fname, dns_fixedname_name(&wildcardname),
				      NULL);
			need_wildcardproof = ISC_TRUE;
		}
		if (NOQNAME(rdataset) && WANTDNSSEC(client))
			noqname = rdataset;
		else
			noqname = NULL;
#ifdef ORIGINAL_ISC_CODE
		if (!is_zone && RECURSIONOK(client))
#else
		if (!is_zone && RECURSIONOK(client) && 
		    ((rdataset->attributes & DNS_RDATASETATTR_EXPIRED_HEADER) == 0))
#endif
			query_prefetch(client, fname, rdataset);

#ifdef ORIGINAL_ISC_CODE
#else
		/*
		 * If this CNAME record is a target of a IBALIAS record
		 * in search path, don't add the CNAME RRs (links in the
		 * chain) to the reply.
		 */
		if (!IBALIASINITIATED(client)) {
#endif

		query_addrrset(client, &fname, &rdataset, sigrdatasetp, dbuf,
			       DNS_SECTION_ANSWER);
		if (noqname != NULL)
			query_addnoqnameproof(client, noqname);
		/*
		 * We set the PARTIALANSWER attribute so that if anything goes
		 * wrong later on, we'll return what we've got so far.
		 */
		client->query.attributes |= NS_QUERYATTR_PARTIALANSWER;

#ifdef ORIGINAL_ISC_CODE
#else
		} else {
			/*
			 * This is just a comment to note that when
			 * following IBALIAS, fname would be cleaned-up
			 * under the cleanup label below, so there's no
			 * need to explicitly call query_releasename()
			 * here. In any case, the dns_message also keeps
			 * a list of allocated tempnames.
			 */
		}
#endif

		/*
		 * Reset qname to be the target name of the CNAME and restart
		 * the query.
		 */
		tname = NULL;
		result = dns_message_gettempname(client->message, &tname);
		if (result != ISC_R_SUCCESS)
			goto cleanup;
		result = dns_rdataset_first(trdataset);
		if (result != ISC_R_SUCCESS) {
			dns_message_puttempname(client->message, &tname);
			goto cleanup;
		}
		dns_rdataset_current(trdataset, &rdata);
		result = dns_rdata_tostruct(&rdata, &cname, NULL);
		dns_rdata_reset(&rdata);
		if (result != ISC_R_SUCCESS) {
			dns_message_puttempname(client->message, &tname);
			goto cleanup;
		}
		dns_name_init(tname, NULL);
		result = dns_name_dup(&cname.cname, client->mctx, tname);
		if (result != ISC_R_SUCCESS) {
			dns_message_puttempname(client->message, &tname);
			dns_rdata_freestruct(&cname);
			goto cleanup;
		}
		dns_rdata_freestruct(&cname);
		ns_client_qnamereplace(client, tname);
		want_restart = ISC_TRUE;
		if (!WANTRECURSION(client))
			options |= DNS_GETDB_NOLOG;
		goto addauth;
#ifdef ORIGINAL_ISC_CODE
#else
	case DNS_R_IBALIAS:
		INSIST(is_zone);
		INSIST(event == NULL);
		INSIST(sigrdataset == NULL); //IBALIAS doesn't support DNSSEC

		result = query_find_ibalias(client, &fname, rdataset, qtype);
		if (result != ISC_R_SUCCESS)
			goto cleanup;

		want_restart = ISC_TRUE;
		if (!WANTRECURSION(client))
			options |= DNS_GETDB_NOLOG;
		goto addauth;
#endif
	case DNS_R_DNAME:
#ifdef ORIGINAL_ISC_CODE
#else
		/*
		 * If a rewritten query resulted in DNAME we cannot always
		 * provide well-defined answer.  Since it's very unlikely DNAME
		 * is actually used, we'll simply make it fail with SERVFAIL.
		 * We log the event regardless in the general client category
		 * as it should be very rare (if it ever happens), and if it
		 * really happens it should be worth noting.
		 */
		if (QRYREWRITTEN(client)) {
			ns_client_log(client, NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_QUERY, ISC_LOG_INFO,
				      "DNAME and query rewrite cannot coexist");
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}
#endif

		/*
		 * Compare the current qname to the found name.  We need
		 * to know how many labels and bits are in common because
		 * we're going to have to split qname later on.
		 */
		namereln = dns_name_fullcompare(client->query.qname, fname,
						&order, &nlabels);
		INSIST(namereln == dns_namereln_subdomain);
		/*
		 * Keep a copy of the rdataset.  We have to do this because
		 * query_addrrset may clear 'rdataset' (to prevent the
		 * cleanup code from cleaning it up).
		 */
		trdataset = rdataset;
		/*
		 * Add the DNAME to the answer section.
		 */
		if (sigrdataset != NULL)
			sigrdatasetp = &sigrdataset;
		else
			sigrdatasetp = NULL;
		if (WANTDNSSEC(client) &&
		    (fname->attributes & DNS_NAMEATTR_WILDCARD) != 0)
		{
			dns_fixedname_init(&wildcardname);
			dns_name_copy(fname, dns_fixedname_name(&wildcardname),
				      NULL);
			need_wildcardproof = ISC_TRUE;
		}
#ifdef ORIGINAL_ISC_CODE
		if (!is_zone && RECURSIONOK(client))
#else
		if (!is_zone && RECURSIONOK(client) &&
		    ((rdataset->attributes & DNS_RDATASETATTR_EXPIRED_HEADER) == 0))
#endif
			query_prefetch(client, fname, rdataset);

#ifdef ORIGINAL_ISC_CODE
#else
		/*
		 * If this DNAME record is a target of a IBALIAS record
		 * in search path, don't add the DNAME RRs (links in the
		 * chain) to the reply.
		 */
		if (!IBALIASINITIATED(client)) {
#endif

		query_addrrset(client, &fname, &rdataset, sigrdatasetp, dbuf,
			       DNS_SECTION_ANSWER);
		/*
		 * We set the PARTIALANSWER attribute so that if anything goes
		 * wrong later on, we'll return what we've got so far.
		 */
		client->query.attributes |= NS_QUERYATTR_PARTIALANSWER;

#ifdef ORIGINAL_ISC_CODE
#else
		} else {
			/*
			 * Release fname here as the code below expects
			 * it to be consumed.
			 */
			query_releasename(client, &fname);
		}
#endif

		/*
		 * Get the target name of the DNAME.
		 */
		tname = NULL;
		result = dns_message_gettempname(client->message, &tname);
		if (result != ISC_R_SUCCESS)
			goto cleanup;
		result = dns_rdataset_first(trdataset);
		if (result != ISC_R_SUCCESS) {
			dns_message_puttempname(client->message, &tname);
			goto cleanup;
		}
		dns_rdataset_current(trdataset, &rdata);
		result = dns_rdata_tostruct(&rdata, &dname, NULL);
		dns_rdata_reset(&rdata);
		if (result != ISC_R_SUCCESS) {
			dns_message_puttempname(client->message, &tname);
			goto cleanup;
		}
		dns_name_clone(&dname.dname, tname);
		dns_rdata_freestruct(&dname);
		/*
		 * Construct the new qname consisting of
		 * <found name prefix>.<dname target>
		 */
		dns_fixedname_init(&fixed);
		prefix = dns_fixedname_name(&fixed);
		dns_name_split(client->query.qname, nlabels, prefix, NULL);
		INSIST(fname == NULL);
		dbuf = query_getnamebuf(client);
		if (dbuf == NULL) {
			dns_message_puttempname(client->message, &tname);
			goto cleanup;
		}
		fname = query_newname(client, dbuf, &b);
		if (fname == NULL) {
			dns_message_puttempname(client->message, &tname);
			goto cleanup;
		}
		result = dns_name_concatenate(prefix, tname, fname, NULL);
		dns_message_puttempname(client->message, &tname);

		/*
		 * RFC2672, section 4.1, subsection 3c says
		 * we should return YXDOMAIN if the constructed
		 * name would be too long.
		 */
		if (result == DNS_R_NAMETOOLONG)
			client->message->rcode = dns_rcode_yxdomain;
		if (result != ISC_R_SUCCESS)
			goto cleanup;

		query_keepname(client, fname, dbuf);

#ifdef ORIGINAL_ISC_CODE
#else
		/*
		 * If this DNAME record is a target of a IBALIAS record
		 * in search path, don't add synthesized CNAME RRs to
		 * the reply.
		 */
		if (!IBALIASINITIATED(client)) {
#endif

		/*
		 * Synthesize a CNAME consisting of
		 *   <old qname> <dname ttl> CNAME <new qname>
		 *	    with <dname trust value>
		 *
		 * Synthesize a CNAME so old old clients that don't understand
		 * DNAME can chain.
		 *
		 * We do not try to synthesize a signature because we hope
		 * that security aware servers will understand DNAME.  Also,
		 * even if we had an online key, making a signature
		 * on-the-fly is costly, and not really legitimate anyway
		 * since the synthesized CNAME is NOT in the zone.
		 */
		result = query_add_cname(client, client->query.qname, fname,
					 trdataset->trust, trdataset->ttl);
		if (result != ISC_R_SUCCESS)
			goto cleanup;

#ifdef ORIGINAL_ISC_CODE
#else
		}
#endif
		/*
		 * Switch to the new qname and restart.
		 */
		ns_client_qnamereplace(client, fname);
		fname = NULL;
		want_restart = ISC_TRUE;
		if (!WANTRECURSION(client))
			options |= DNS_GETDB_NOLOG;
		goto addauth;
	case DNS_R_REFUSED:
		CTRACE(ISC_LOG_DEBUG(1), "resumed with REFUSED");
		QUERY_ERROR(DNS_R_REFUSED);
		goto cleanup;
	default:
		/*
		 * Something has gone wrong.
		 */
		snprintf(errmsg, sizeof(errmsg) - 1,
			 "query_find: unexpected error after resuming: %s",
			 isc_result_totext(result));
		CTRACE(ISC_LOG_ERROR, errmsg);
		QUERY_ERROR(DNS_R_SERVFAIL);
		goto cleanup;
	}

#ifdef ORIGINAL_ISC_CODE
#else
 after_result_switch: ;
	// Skip parental control if it's not turned on, if we didn't
	// get subscriber information, or if we already got an RPZ whitelist hit.
	if (!client->view->infoblox_pc_enable || client->subscriber == NULL ||
	    (client->query.rpz_st != NULL && client->query.rpz_st->m.rpz != NULL &&
	     ((client->query.rpz_st->state & DNS_RPZ_REWRITTEN) != 0) &&
	      (!(client->query.rpz_st->m.policy == DNS_RPZ_POLICY_PASSTHRU &&
	         client->query.rpz_st->m.rpz->num != client->view->infoblox_pc_proxy_all_whitelist) ||
              (client->view->infoblox_ss_enable_global_allow_rpz_list &&
               client->query.rpz_st->m.rpz->num == client->view->infoblox_ss_global_allow_rpz_list))))
	{
		    goto parental_control_done;
	}

	// Handle parental control category lookup and processing.
	// Skip the test if we don't have any defined categories,
	// and we haven't asked to proxy all or proxy unknown or dynamic
	// categories or blacklist/whitelist domains.
	isc_boolean_t ibpc_request_addrs = ISC_FALSE;
	if (result == ISC_R_SUCCESS &&
	    (client->subscriber->categories != 0 ||
	     client->subscriber->pc_categories != 0 ||
	     client->subscriber->proxy_all ||
	     client->subscriber->unknown || client->subscriber->dynamic ||
	     client->subscriber->action ||
	    (client->view->infoblox_pc_enable && client->view->infoblox_pc_proxy_rpz_passthru_enable)) &&
	    (client->query.attributes & NS_QUERYATTR_PC_CNAME) == 0) {
		result = infoblox_pc_category_match(client, &fname, dbuf,
						    &ibpc_request_addrs);
		if (result == ISC_R_SUCCESS &&
		    (client->query.attributes & NS_QUERYATTR_PC_CNAME) != 0) {
			// The answer set up before we inserted
			// the blocking CNAME needs to be
			// cleared, so it isn't inserted into
			// the ultimate answer;  similarly,
			// any DNSSEC answers we have are now
			// invalid.
			if (dns_rdataset_isassociated(rdataset))
				dns_rdataset_disassociate(rdataset);
			if (sigrdataset != NULL &&
			    dns_rdataset_isassociated(sigrdataset))
				dns_rdataset_disassociate(sigrdataset);
			// Delete sigrdataset?
			want_restart = ISC_TRUE;
			goto addauth;
		} else if (result != ISC_R_SUCCESS) {
                        client->attributes |= NS_CLIENTATTR_NOSETFC;
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}
	}

	// Handle parental control proxying -- for certain subscribers,
	// we'll redirect queries to a dynamically created proxy address.
	// This is required when
	//  - the subscriber requested proxy-all;
	//  - when the subscriber requested proxying for unknown or
	//    dynamic categories;
	//  - rpz PASSTHRU hit and 'Enforce the global proxy list' enabled

	// Note that we skip proxy actions if
	//  - we got a hit on the RPZ-based whitelist.
	//    This was checked during RPZ processing,
	//    and the NS_QUERYATTR_PC_WHITELIST flag was set if found.
	//  - subscriber is a guest.
	//    This was checked during getting subscriber cache record,
	//    see subscriber_get().

	ib_bool_t is_valid = FALSE;
	if (client->subscriber->proxy_addr_pri.ss_family == AF_INET ||
		client->subscriber->proxy_addr_pri.ss_family == AF_INET6) {
		is_valid = TRUE;
	}

	if (result == ISC_R_SUCCESS &&
	    (qtype == dns_rdatatype_a || qtype == dns_rdatatype_aaaa) &&
	    (client->query.attributes & NS_QUERYATTR_PC_WHITELIST) == 0 &&
	    client->subscriber->guest == ISC_FALSE &&
	    ibpc_request_addrs && is_valid) {
		INSIST(dns_rdataset_isassociated(rdataset));

		client->query.attributes |= NS_QUERYATTR_PC_ASYNC;
		result = query_recurse(client, qtype, NULL, NULL, NULL,
					resuming);
		if (result == ISC_R_SUCCESS) {
			ib_subscriber_edns0_client_id_t local_id;
			const ib_subscriber_edns0_client_id_t *local_idp;

			local_idp = ib_get_localid(&local_id, client);
			result = infoblox_subscriber_request_proxy(
					client->mctx, &client->peeraddr,
					&client->destaddr,
					client->subscriber->id,
					local_idp,
					rdataset,
					client->query.origqname,
					client->query.qname, client->task,
					ib_query_resume_proxy,
					client->message->id, qtype, client,
					(struct sockaddr *)
					&client->subscriber->proxy_addr_pri,
					(struct sockaddr *)
					&client->subscriber->proxy_addr_sec,
					(struct sockaddr *)
					&client->subscriber->other_addr,
					client->warning_vip,
					client->rated_category,
					client->subscriber->non_nated);
		}
		if (result != ISC_R_SUCCESS) {
			client->query.attributes &= ~NS_QUERYATTR_PC_ASYNC;
                        client->attributes |= NS_CLIENTATTR_NOSETFC;
			QUERY_ERROR(DNS_R_SERVFAIL);
		}

		// If we successfully queued the sevent, we'll get a
		// callback when the proxy answer is ready.  For now,
		// we just leave the function and wait for that answer.
		// When the answer arrives, processing will start at
		// ib_pc_proxy_resume, immediately below.

		// Save the original answer, so that we can attempt to
		// restore it if the parental control address lookup fails.
		// Clear rdataset and sigrdataset so that the saved original
		// answer is not freed during cleanup.
		client->ibsppc_orig_rdataset = rdataset;
		client->ibsppc_orig_sigrdataset = sigrdataset;
		rdataset = NULL;
		sigrdataset = NULL;

		goto cleanup;
	}

	goto parental_control_done;

ib_pc_proxy_resume:
		INSIST(rdataset == NULL);
		INSIST(sigrdataset == NULL);

		// Processing for a proxy event begins here.  We've got
		// either an error message (in which case we fallback to
		// the original answer if the error is recoverable, or we
		// fail the query if the error is unrecoverable), or
		// we've sucessfully retrieved the parental control address.
		if (result == DNS_R_IBSPPCLOOKUPFAILURE) {
			rdataset = client->ibsppc_orig_rdataset;
			sigrdataset = client->ibsppc_orig_sigrdataset;
			client->ibsppc_orig_rdataset = NULL;
			client->ibsppc_orig_sigrdataset = NULL;
			goto parental_control_done;
		} else if (result != ISC_R_SUCCESS) {
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}
		if (isc_log_wouldlog(ns_g_lctx, ISC_LOG_DEBUG(3))) {
			char query_name[DNS_NAME_FORMATSIZE];
			dns_name_format(client->query.origqname, query_name,
					sizeof(query_name));

			char proxy_addr_text[ISC_SOCKADDR_FORMATSIZE];
			isc_sockaddr_format(&proxy_result, proxy_addr_text,
					    sizeof(proxy_addr_text));

			char *extra = index(proxy_addr_text, '#');
			if (extra != NULL)
				*extra = 0;

			ns_client_log(client, NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_QUERY, ISC_LOG_DEBUG(3),
				      "proxy query result %s for %s, [%s]",
				      proxy_addr_text, query_name,
				      client->subscriber->cef_log_string ?
				      client->subscriber->cef_log_string : "");
		}

		result = infoblox_pc_proxy_rdataset(client, qtype,
						    client->message->rdclass,
						    &rdataset, &proxy_result);
		if (result != ISC_R_SUCCESS) {
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}

		// Write a CEF log for the proxy update, which also handles
		// setting the Z-bit if necessary.
		rpz_log_rewrite(client, ISC_FALSE, DNS_RPZ_POLICY_CNAME,
				DNS_RPZ_TYPE_CLIENT_IP, NULL,
				client->query.origqname, NULL, 0, NULL,
				IB_RPZ_SEV_MAJOR, "PXY", ISC_FALSE);

		// Turn off DNSSEC because the results cannot verify.
		client->attributes &=
			~(NS_CLIENTATTR_WANTDNSSEC | NS_CLIENTATTR_WANTAD);
		client->message->flags &= ~DNS_MESSAGEFLAG_AD;

		// TODO-PARENTALCONTROL:  We should add a fake, RPZ-style
		// authority section as a hint that the original answer has
		// been replaced.

 parental_control_done:
#endif

	if (WANTDNSSEC(client) &&
	    (fname->attributes & DNS_NAMEATTR_WILDCARD) != 0)
	{
		dns_fixedname_init(&wildcardname);
		dns_name_copy(fname, dns_fixedname_name(&wildcardname), NULL);
		need_wildcardproof = ISC_TRUE;
	}

#ifdef ALLOW_FILTER_AAAA
	/*
	 * The filter-aaaa-on-v4 option should suppress AAAAs for IPv4
	 * clients if there is an A; filter-aaaa-on-v6 option does the same
	 * for IPv6 clients.
	 */
	client->filter_aaaa = dns_aaaa_ok;
	if (client->view->v4_aaaa != dns_aaaa_ok ||
	    client->view->v6_aaaa != dns_aaaa_ok)
	{
		result = ns_client_checkaclsilent(client, NULL,
						  client->view->aaaa_acl,
						  ISC_TRUE);
		if (result == ISC_R_SUCCESS &&
		    client->view->v4_aaaa != dns_aaaa_ok &&
		    is_v4_client(client))
			client->filter_aaaa = client->view->v4_aaaa;
		else if (result == ISC_R_SUCCESS &&
			 client->view->v6_aaaa != dns_aaaa_ok &&
			 is_v6_client(client))
			client->filter_aaaa = client->view->v6_aaaa;
	}

#endif

	if (type == dns_rdatatype_any) {
		/*
		 * For minimal-any, we only add records that
		 * match this type or cover this type.
		 */
		dns_rdatatype_t onetype = 0;
#ifdef ALLOW_FILTER_AAAA
		isc_boolean_t have_aaaa, have_a, have_sig;

		/*
		 * If we are not authoritative, assume there is a A
		 * even in if it is not in our cache.  This assumption could
		 * be wrong but it is a good bet.
		 */
		have_aaaa = ISC_FALSE;
		have_a = !authoritative;
		have_sig = ISC_FALSE;
#endif
		/*
		 * XXXRTH  Need to handle zonecuts with special case
		 * code.
		 */
		n = 0;
		rdsiter = NULL;
		result = dns_db_allrdatasets(db, node, version, 0, &rdsiter);
		if (result != ISC_R_SUCCESS) {
			CTRACE(ISC_LOG_ERROR,
			       "query_find: type any; allrdatasets failed");
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}

		/*
		 * Calling query_addrrset() with a non-NULL dbuf is going
		 * to either keep or release the name.  We don't want it to
		 * release fname, since we may have to call query_addrrset()
		 * more than once.  That means we have to call query_keepname()
		 * now, and pass a NULL dbuf to query_addrrset().
		 *
		 * If we do a query_addrrset() below, we must set fname to
		 * NULL before leaving this block, otherwise we might try to
		 * cleanup fname even though we're using it!
		 */
		query_keepname(client, fname, dbuf);
		tname = fname;
		result = dns_rdatasetiter_first(rdsiter);
		while (result == ISC_R_SUCCESS) {
			dns_rdatasetiter_current(rdsiter, rdataset);
#ifdef ALLOW_FILTER_AAAA
			/*
			 * Notice the presence of A and AAAAs so
			 * that AAAAs can be hidden from IPv4 clients.
			 */
			if (client->filter_aaaa != dns_aaaa_ok) {
				if (rdataset->type == dns_rdatatype_aaaa)
					have_aaaa = ISC_TRUE;
				else if (rdataset->type == dns_rdatatype_a)
					have_a = ISC_TRUE;
			}
#endif
			/*
			 * We found an NS RRset; no need to add one later.
			 */
			if (qtype == dns_rdatatype_any &&
			    rdataset->type == dns_rdatatype_ns)
			{
				answer_has_ns = ISC_TRUE;
			}

			if (is_zone && qtype == dns_rdatatype_any &&
			    !dns_db_issecure(db) &&
			    dns_rdatatype_isdnssec(rdataset->type)) {
				/*
				 * The zone is transitioning from insecure
				 * to secure. Hide the dnssec records from
				 * ANY queries.
				 */
				dns_rdataset_disassociate(rdataset);
			} else if (client->view->minimal_any &&
				   !TCP_CLIENT(client) &&
				   !WANTDNSSEC(client) &&
				   qtype == dns_rdatatype_any &&
				   (rdataset->type == dns_rdatatype_sig ||
				    rdataset->type == dns_rdatatype_rrsig)) {
				CTRACE(ISC_LOG_DEBUG(5), "query_find: "
				       "minimal-any skip signature");
				dns_rdataset_disassociate(rdataset);
			} else if (client->view->minimal_any &&
				   !TCP_CLIENT(client) &&
				   onetype != 0 &&
				   rdataset->type != onetype &&
				   rdataset->covers != onetype) {
				CTRACE(ISC_LOG_DEBUG(5), "query_find: "
				       "minimal-any skip rdataset");
				dns_rdataset_disassociate(rdataset);
			} else if ((qtype == dns_rdatatype_any ||
			     rdataset->type == qtype) && rdataset->type != 0) {
#ifdef ALLOW_FILTER_AAAA
				if (dns_rdatatype_isdnssec(rdataset->type))
					have_sig = ISC_TRUE;
#endif
				if (NOQNAME(rdataset) && WANTDNSSEC(client))
					noqname = rdataset;
				else
					noqname = NULL;
#ifdef ORIGINAL_ISC_CODE
				rpz_st = client->query.rpz_st;
				if (rpz_st != NULL)
					rdataset->ttl = ISC_MIN(rdataset->ttl,
							    rpz_st->m.ttl);
#else
				/*
				 * Retain pre-bind9.9.2 behavior:
				 * don't trim ttl with RPZ ttl which is usually
				 * equals to DNS_RPZ_TTL_DEFAULT or zero.
				 */
#endif
#ifdef ORIGINAL_ISC_CODE
				if (!is_zone && RECURSIONOK(client)) {
#else
				if (!is_zone && RECURSIONOK(client) &&
				    ((rdataset->attributes & DNS_RDATASETATTR_EXPIRED_HEADER) == 0)) {
#endif
					dns_name_t *name;
					name = (fname != NULL) ? fname : tname;
					query_prefetch(client, name, rdataset);
				}
				/*
				 * Remember the first RRtype we find so we
				 * can skip others with minimal-any.
				 */
				if (rdataset->type == dns_rdatatype_sig ||
				    rdataset->type == dns_rdatatype_rrsig)
					onetype = rdataset->covers;
				else
					onetype = rdataset->type;
				query_addrrset(client,
					       fname != NULL ? &fname : &tname,
					       &rdataset, NULL,
					       NULL, DNS_SECTION_ANSWER);
				if (noqname != NULL)
					query_addnoqnameproof(client, noqname);
				n++;
				INSIST(tname != NULL);
				/*
				 * rdataset is non-NULL only in certain
				 * pathological cases involving DNAMEs.
				 */
				if (rdataset != NULL)
					query_putrdataset(client, &rdataset);
				rdataset = query_newrdataset(client);
				if (rdataset == NULL)
					break;
			} else {
				/*
				 * We're not interested in this rdataset.
				 */
				dns_rdataset_disassociate(rdataset);
			}
			result = dns_rdatasetiter_next(rdsiter);
		}

#ifdef ALLOW_FILTER_AAAA
		/*
		 * Filter AAAAs if there is an A and there is no signature
		 * or we are supposed to break DNSSEC.
		 */
		if (client->filter_aaaa == dns_aaaa_break_dnssec)
			client->attributes |= NS_CLIENTATTR_FILTER_AAAA;
		else if (client->filter_aaaa != dns_aaaa_ok &&
#ifdef ORIGINAL_ISC_CODE
			 have_aaaa && have_a &&
#else
			 // For better or worse, ISC decided that if
			 // there is an AAAA but no A, we should give
			 // the client the AAAA even if filtering is
			 // enabled. Other than that, we want to filter
			 // even if we haven't seen any A or AAAA records.
			 // If we don't filter simply because we didn't
			 // see any A or AAAA records, we could leave
			 // AAAA records in the AUTHORITY or ADDITIONAL
			 // sections.
			 //
			 // Thus, filter if we do NOT have AAAA without A.
			 // (BTW, since ISC assumes have_a if we aren't
			 // authoritative, this means that, even in the
			 // unmodified ISC code, we will filter out a
			 // lone AAAA without an A, while in the authoritative
			 // case, we would leave the AAAA in place. Strange,
			 // though makes sense in a way, since an ANY query
			 // (which is where we are right now) doesn't
			 // refresh the cached data, so there _could_ be
			 // an A that simply has expired. In any case, we
			 // preserve that behavior.)
			 !(have_aaaa && !have_a) &&
#endif
			 (!have_sig || !WANTDNSSEC(client)))
			  client->attributes |= NS_CLIENTATTR_FILTER_AAAA;
#endif
		if (fname != NULL)
			dns_message_puttempname(client->message, &fname);

		if (n == 0) {
			/*
			 * No matching rdatasets found in cache. If we were
			 * searching for RRSIG/SIG, that's probably okay;
			 * otherwise this is an error condition.
			 */
			if ((qtype == dns_rdatatype_rrsig ||
			     qtype == dns_rdatatype_sig) &&
			    result == ISC_R_NOMORE) {
				if (!is_zone) {
					authoritative = ISC_FALSE;
					dns_rdatasetiter_destroy(&rdsiter);
					client->attributes &= ~NS_CLIENTATTR_RA;
					goto addauth;
				}

				if (qtype == dns_rdatatype_rrsig &&
				    dns_db_issecure(db)) {
					char namebuf[DNS_NAME_FORMATSIZE];
					dns_name_format(client->query.qname,
							namebuf,
							sizeof(namebuf));
					ns_client_log(client,
						      DNS_LOGCATEGORY_DNSSEC,
						      NS_LOGMODULE_QUERY,
						      ISC_LOG_WARNING,
						      "missing signature "
						      "for %s", namebuf);
				}

				dns_rdatasetiter_destroy(&rdsiter);
				fname = query_newname(client, dbuf, &b);
				goto nxrrset_rrsig;
#ifdef ORIGINAL_ISC_CODE
#else
			} else if (qtype == dns_rdatatype_any &&
				   result == ISC_R_NOMORE &&
				   is_zone &&
				   infoblox_rdatasetiter_censored_data(rdsiter)) {
				// We have censored special IB-RR-types,
				// leaving no data for the name.
				dns_rdatasetiter_destroy(&rdsiter);
				fname = query_newname(client, dbuf, &b);
				goto iszone_nxrrset;
#endif
			} else {
				CTRACE(ISC_LOG_ERROR,
				       "query_find: no matching rdatasets "
				       "in cache");
				result = DNS_R_SERVFAIL;
			}
		}

		dns_rdatasetiter_destroy(&rdsiter);
		if (result != ISC_R_NOMORE) {
			CTRACE(ISC_LOG_ERROR,
			       "query_find: dns_rdatasetiter_destroy failed");
			QUERY_ERROR(DNS_R_SERVFAIL);
			goto cleanup;
		}
	} else {
		/*
		 * This is the "normal" case -- an ordinary question to which
		 * we know the answer.
		 */

		/*
		 * If we have a zero ttl from the cache refetch it.
		 */
		if (!is_zone && !resuming && rdataset->ttl == 0 &&
#if ORIGINAL_ISC_CODE
#else
		    (rdataset->attributes & DNS_RDATASETATTR_EXPIRED_HEADER)
		    == 0 &&
#endif
		    RECURSIONOK(client))
		{
			if (dns_rdataset_isassociated(rdataset))
				dns_rdataset_disassociate(rdataset);
			if (sigrdataset != NULL &&
			    dns_rdataset_isassociated(sigrdataset))
				dns_rdataset_disassociate(sigrdataset);
			if (node != NULL)
				dns_db_detachnode(db, &node);

			INSIST(!REDIRECT(client));
			result = query_recurse(client, qtype,
					       client->query.qname,
					       NULL, NULL, resuming);
			if (result == ISC_R_SUCCESS) {
				client->query.attributes |=
					NS_QUERYATTR_RECURSING;
				if (dns64)
					client->query.attributes |=
						NS_QUERYATTR_DNS64;
				if (dns64_exclude)
					client->query.attributes |=
					      NS_QUERYATTR_DNS64EXCLUDE;
			} else
				RECURSE_ERROR(result);
			goto cleanup;
		}

		/*
		 * Check to see if the AAAA RRset has non-excluded addresses
		 * in it.  If not look for a A RRset.
		 *
		 * Note: the order of dns64_aaaaok() and filter_aaaa check is
		 * important. Both result is fetches being called but the
		 * dns64 case goes to db_find while the filter_aaaa case
		 * adds the records now for later potential exclusion.
		 */
		INSIST(client->query.dns64_aaaaok == NULL);

		if (qtype == dns_rdatatype_aaaa && !dns64_exclude &&
		    !ISC_LIST_EMPTY(client->view->dns64) &&
		    client->message->rdclass == dns_rdataclass_in &&
		    !dns64_aaaaok(client, rdataset, sigrdataset)) {
			/*
			 * Look to see if there are A records for this
			 * name.
			 */
			client->query.dns64_ttl = rdataset->ttl;
			SAVE(client->query.dns64_aaaa, rdataset);
			SAVE(client->query.dns64_sigaaaa, sigrdataset);
			query_releasename(client, &fname);
			dns_db_detachnode(db, &node);
			type = qtype = dns_rdatatype_a;
			dns64_exclude = dns64 = ISC_TRUE;
			goto db_find;
		}

#ifdef ALLOW_FILTER_AAAA
		/*
		 * Optionally hide AAAAs from IPv4 clients if there is an A.
		 * We add the AAAAs now, but might refuse to render them later
		 * after DNSSEC is figured out.
		 * This could be more efficient, but the whole idea is
		 * so fundamentally wrong, unavoidably inaccurate, and
		 * unneeded that it is best to keep it as short as possible.
		 */
		if (client->filter_aaaa == dns_aaaa_break_dnssec ||
		    (client->filter_aaaa == dns_aaaa_filter &&
		     (!WANTDNSSEC(client) || sigrdataset == NULL ||
		     !dns_rdataset_isassociated(sigrdataset))))
		{
			if (qtype == dns_rdatatype_aaaa) {
				trdataset = query_newrdataset(client);

				/* Reset ECS scope for this query */
				ci.ecs.scope = 0xff;
				result = dns_db_findrdatasetext(db, node,
								version,
								dns_rdatatype_a,
								0, client->now,
								&cm, &ci,
								trdataset,
								NULL);
				if (dns_rdataset_isassociated(trdataset))
					dns_rdataset_disassociate(trdataset);
				query_putrdataset(client, &trdataset);

				/*
				 * We have an AAAA but the A is not in our cache.
				 * Assume any result other than DNS_R_DELEGATION
				 * or ISC_R_NOTFOUND means there is no A and
				 * so AAAAs are ok.
				 * Assume there is no A if we can't recurse
				 * for this client, although that could be
				 * the wrong answer. What else can we do?
				 * Besides, that we have the AAAA and are using
				 * this mechanism suggests that we care more
				 * about As than AAAAs and would have cached
				 * the A if it existed.
				 */
				if (result == ISC_R_SUCCESS) {
					client->attributes |=
						    NS_CLIENTATTR_FILTER_AAAA;

				} else if (authoritative ||
					   !RECURSIONOK(client) ||
					   (result != DNS_R_DELEGATION &&
					    result != ISC_R_NOTFOUND)) {
					client->attributes &=
						    ~NS_CLIENTATTR_FILTER_AAAA;
				} else {
					/*
					 * This is an ugly kludge to recurse
					 * for the A and discard the result.
					 *
					 * Continue to add the AAAA now.
					 * We'll make a note to not render it
					 * if the recursion for the A succeeds.
					 */
					INSIST(!REDIRECT(client));
					result = query_recurse(client,
							dns_rdatatype_a,
							client->query.qname,
							NULL, NULL, resuming);
					if (result == ISC_R_SUCCESS) {
					    client->attributes |=
						    NS_CLIENTATTR_FILTER_AAAA_RC;
					    client->query.attributes |=
							NS_QUERYATTR_RECURSING;
					}
				}

			} else if (qtype == dns_rdatatype_a &&
				   (client->attributes &
					    NS_CLIENTATTR_FILTER_AAAA_RC) != 0) {
				client->attributes &=
					    ~NS_CLIENTATTR_FILTER_AAAA_RC;
				client->attributes |=
					    NS_CLIENTATTR_FILTER_AAAA;
				dns_rdataset_disassociate(rdataset);
				if (sigrdataset != NULL &&
				    dns_rdataset_isassociated(sigrdataset))
					dns_rdataset_disassociate(sigrdataset);
				goto cleanup;
			}
#ifdef ORIGINAL_ISC_CODE
#else
			else {
				client->attributes |=
					    NS_CLIENTATTR_FILTER_AAAA;
			}
#endif
		}
#endif

		if (sigrdataset != NULL)
			sigrdatasetp = &sigrdataset;
		else
			sigrdatasetp = NULL;
		if (NOQNAME(rdataset) && WANTDNSSEC(client))
			noqname = rdataset;
		else
			noqname = NULL;
		/*
		 * Special case NS handling
		 */
		if (is_zone && qtype == dns_rdatatype_ns) {
			/*
			 * We've already got an NS, no need to add one in
			 * the authority section
			 */
			if (dns_name_equal(client->query.qname,
					   dns_db_origin(db)))
			{
				answer_has_ns = ISC_TRUE;
			}

			/*
			 * BIND 8 priming queries need the additional section.
			 */
			if (dns_name_equal(client->query.qname, dns_rootname)) {
				client->query.attributes &=
					~NS_QUERYATTR_NOADDITIONAL;
			}
		}

		/*
		 * Return the time to expire for slave and master zones.
		 */
		if (zone != NULL && is_zone && qtype == dns_rdatatype_soa &&
		    (client->attributes & NS_CLIENTATTR_WANTEXPIRE) != 0 &&
		    client->query.restarts == 0) {
			dns_zone_t *raw = NULL, *mayberaw;

			dns_zone_getraw(zone, &raw);
			mayberaw = (raw != NULL) ? raw : zone;

			if (dns_zone_gettype(mayberaw) == dns_zone_slave) {
				isc_time_t expiretime;
				isc_uint32_t secs;
				dns_zone_getexpiretime(zone, &expiretime);
				secs = isc_time_seconds(&expiretime);
				if (secs >= client->now &&
				    result == ISC_R_SUCCESS) {
					client->attributes |=
						NS_CLIENTATTR_HAVEEXPIRE;
					client->expire = secs - client->now;
				}
			}
			if (dns_zone_gettype(mayberaw) == dns_zone_master) {
				dns_rdata_soa_t soa;
				result = dns_rdataset_first(rdataset);
				RUNTIME_CHECK(result == ISC_R_SUCCESS);
				dns_rdataset_current(rdataset, &rdata);
				result = dns_rdata_tostruct(&rdata, &soa, NULL);
				RUNTIME_CHECK(result == ISC_R_SUCCESS);
				client->expire = soa.expire;
				client->attributes |= NS_CLIENTATTR_HAVEEXPIRE;
			}
			if (raw != NULL)
				dns_zone_detach(&raw);
		}

		if (dns64) {
			qtype = type = dns_rdatatype_aaaa;
			result = query_dns64(client, &fname, rdataset,
					     sigrdataset, dbuf,
					     DNS_SECTION_ANSWER);
			noqname = NULL;
			dns_rdataset_disassociate(rdataset);
			dns_message_puttemprdataset(client->message, &rdataset);
			if (result == ISC_R_NOMORE) {
#ifndef dns64_bis_return_excluded_addresses
				if (dns64_exclude) {
					if (!is_zone)
						goto cleanup;
					/*
					 * Add a fake SOA record.
					 */
					(void)query_addsoa(client, db, version,
							   600, ISC_FALSE,
							DNS_SECTION_AUTHORITY);
					goto cleanup;
				}
#endif
				if (is_zone)
					goto iszone_nxrrset;
				else
					goto ncache_nxrrset;
			} else if (result != ISC_R_SUCCESS) {
				eresult = result;
				goto cleanup;
			}
		} else if (client->query.dns64_aaaaok != NULL) {
			query_filter64(client, &fname, rdataset, dbuf,
				       DNS_SECTION_ANSWER);
			query_putrdataset(client, &rdataset);
		} else {
#ifdef ORIGINAL_ISC_CODE
			if (!is_zone && RECURSIONOK(client))
#else
			if (!is_zone && RECURSIONOK(client) &&
			    ((rdataset->attributes & DNS_RDATASETATTR_EXPIRED_HEADER) == 0))
#endif
				query_prefetch(client, fname, rdataset);
			query_addrrset(client, &fname, &rdataset,
				       sigrdatasetp, dbuf, DNS_SECTION_ANSWER);
		}

		if (noqname != NULL)
			query_addnoqnameproof(client, noqname);
#ifdef ORIGINAL_ISC_CODE
		/*
		 * 'rdataset' will only be non-NULL here if the ANSWER section
		 * of the message to be sent to the client already contains an
		 * RRset with the same owner name and the same type as
		 * 'rdataset'.  This should never happen, with one exception:
		 * when chasing DNAME records, one of the DNAME records placed
		 * in the ANSWER section may turn out to be the final answer to
		 * the client's query, but we have no way of knowing that until
		 * now.  In such a case, 'rdataset' will be freed later, so we
		 * do not need to free it here.
		 */
#else
		// If RPZ rewrites have been done, and given a suitably
		// convoluted setup where a zone level override dovetails
		// with an RPZ rule, it's possible that we legitimately did
		// not add 'rdataset' to the answer because it already
		// was there.
		if (rdataset &&
		    client->query.rpz_st &&
                    ((client->query.rpz_st->state & DNS_RPZ_REWRITTEN) != 0)) {
			query_putrdataset(client, &rdataset);
		}
#endif
		INSIST(rdataset == NULL || qtype == dns_rdatatype_dname);
	}

 addauth:
	CTRACE(ISC_LOG_DEBUG(3), "query_find: addauth");
	/*
	 * Add NS records to the authority section (if we haven't already
	 * added them to the answer section).
	 */
	if (!want_restart && !NOAUTHORITY(client)) {
		if (is_zone) {
			if (!answer_has_ns) {
				(void)query_addns(client, db, version);
			}
		} else if (!answer_has_ns && qtype != dns_rdatatype_ns) {
			if (fname != NULL) {
				query_releasename(client, &fname);
			}
			query_addbestns(client);
		}
	}

	/*
	 * Add NSEC records to the authority section if they're needed for
	 * DNSSEC wildcard proofs.
	 */
	if (need_wildcardproof && dns_db_issecure(db))
		query_addwildcardproof(client, db, version,
				       dns_fixedname_name(&wildcardname),
#ifdef ORIGINAL_ISC_CODE
#else
				       NULL, NULL, NULL,
#endif
				       ISC_TRUE, ISC_FALSE);
 cleanup:
	CTRACE(ISC_LOG_DEBUG(3), "query_find: cleanup");
	/*
	 * General cleanup.
	 */
	rpz_st = client->query.rpz_st;
	if (rpz_st != NULL && (rpz_st->state & DNS_RPZ_RECURSING) == 0) {
		rpz_match_clear(rpz_st);
		rpz_st->state &= ~DNS_RPZ_DONE_QNAME;
	}
	if (rdataset != NULL)
		query_putrdataset(client, &rdataset);
	if (sigrdataset != NULL)
		query_putrdataset(client, &sigrdataset);
	if (fname != NULL)
		query_releasename(client, &fname);
#ifdef ORIGINAL_ISC_CODE
	if (node != NULL)
		dns_db_detachnode(db, &node);
	if (db != NULL)
		dns_db_detach(&db);
#else
 	// The 'client' structure has pointers to the RR data in the 'node',
 	// so detaching the node here creates a race, if the node is modified
 	// (or deleted) before we've created the wire format data below.
 	// In order to detach the node, we also need the 'db', so hang on to
 	// that too.

	// This is a good place to reset the last-queried data in the TSD.
	if (reset_last_queried)
		infoblox_reset_last_queried();

#endif
	if (zone != NULL)
		dns_zone_detach(&zone);
	if (zdb != NULL) {
		query_putrdataset(client, &zrdataset);
		if (zsigrdataset != NULL)
			query_putrdataset(client, &zsigrdataset);
		if (zfname != NULL)
			query_releasename(client, &zfname);
		dns_db_detach(&zdb);
	}
	if (event != NULL) {
		free_devent(client, ISC_EVENT_PTR(&event), &event);
	}

	/*
	 * AA bit.
	 */
	if (client->query.restarts == 0 && !authoritative) {
		/*
		 * We're not authoritative, so we must ensure the AA bit
		 * isn't set.
		 */
		client->message->flags &= ~DNS_MESSAGEFLAG_AA;
	}

	/*
	 * Restart the query?
	 */
	if (want_restart && client->query.restarts < MAX_RESTARTS) {
		client->query.restarts++;
#ifdef ORIGINAL_ISC_CODE
#else
 		// Before starting over, release the 'node' and the 'db'.
 		if (node != NULL)
 			dns_db_detachnode(db, &node);
 		if (db != NULL)
 			dns_db_detach(&db);
#endif
		goto restart;
	}

	if (eresult != ISC_R_SUCCESS &&
	    (!PARTIALANSWER(client) || WANTRECURSION(client)
	     || eresult == DNS_R_DROP)) {
		if (eresult == DNS_R_DUPLICATE || eresult == DNS_R_DROP) {
			/*
			 * This was a duplicate query that we are
			 * recursing on or the result of rate limiting.
			 * Don't send a response now for a duplicate query,
			 * because the original will still cause a response.
			 */
			query_next(client, eresult);
		} else {
			/*
			 * If we don't have any answer to give the client,
			 * or if the client requested recursion and thus wanted
			 * the complete answer, send an error response.
			 */
			INSIST(line >= 0);
			query_error(client, eresult, line);
		}
		ns_client_detach(&client);
#ifdef ORIGINAL_ISC_CODE
	} else if (!RECURSING(client)) {
#else
	} else if (!RECURSING(client) && !PROXYASYNC(client)) {
#endif
		/*
		 * We are done.  Set up sortlist data for the message
		 * rendering code, make a final tweak to the AA bit if the
		 * auth-nxdomain config option says so, then render and
		 * send the response.
		 */
		setup_query_sortlist(client);

		/*
		 * If this is a referral and the answer to the question
		 * is in the glue sort it to the start of the additional
		 * section.
		 */
		if (ISC_LIST_EMPTY(client->message->sections[DNS_SECTION_ANSWER]) &&
		    client->message->rcode == dns_rcode_noerror &&
		    (qtype == dns_rdatatype_a || qtype == dns_rdatatype_aaaa))
			answer_in_glue(client, qtype);

		if (client->message->rcode == dns_rcode_nxdomain &&
		    client->view->auth_nxdomain == ISC_TRUE)
			client->message->flags |= DNS_MESSAGEFLAG_AA;

		/*
		 * If the response is somehow unexpected for the client and this
		 * is a result of recursion, return an error to the caller
		 * to indicate it may need to be logged.
		 */
		if (resuming &&
		    (ISC_LIST_EMPTY(client->message->sections[DNS_SECTION_ANSWER]) ||
		     client->message->rcode != dns_rcode_noerror))
			eresult = ISC_R_FAILURE;

#ifdef ORIGINAL_ISC_CODE
#else
		if (client->view->rpz_white_list &&
		    client->query.infoblox_rpz_rewrite_candidate &&
		    !client->query.infoblox_rpz_found_white_list &&
		    eresult == ISC_R_SUCCESS &&
		    client->query.rpz_st &&
		    ((client->query.rpz_st->state & DNS_RPZ_REWRITTEN) == 0)) {
			isc_result_t rpzwlres;
			rpzwlres = infoblox_rpz_cache_add(
				client->view->rpz_white_list,
				client->query.qname, qtype, WANTDNSSEC(client));
			if (rpzwlres != ISC_R_SUCCESS) {
				char qname_buf[DNS_NAME_FORMATSIZE];
				dns_name_format(client->query.qname, qname_buf,
						sizeof(qname_buf));
				isc_log_write(ns_g_lctx,
					      NS_LOGCATEGORY_CLIENT,
					      NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
					      "Error adding RPZ white list entry for \'%s\' to cache for view \'%s\': %s",
					      qname_buf, client->view->name, isc_result_totext(rpzwlres));
			}
		}
#endif
		query_send(client);
		ns_client_detach(&client);
	}
#ifdef	ORIGINAL_ISC_CODE
#else
 	// Now we're really done with the 'node' and the 'db'
 	if (node != NULL)
		dns_db_detachnode(db, &node);
 	if (db != NULL)
		dns_db_detach(&db);
#endif
	CTRACE(ISC_LOG_DEBUG(3), "query_find: done");

	return (eresult);
}

#ifdef ORIGINAL_ISC_CODE
#else
isc_boolean_t
infoblox_should_log_dns_data(ns_client_t *client) {
	REQUIRE(NS_CLIENT_VALID(client));

	if (client->query.infoblox_check_perhaps_log_query_or_response_done == ISC_TRUE) {
		return client->query.infoblox_perhaps_log_query_or_response;
	}

	isc_boolean_t should_log = ISC_TRUE;

	/* we don't have valid qname now */
	if (client->query.qname == NULL) {
		if (client->view != NULL &&
				client->view->infoblox_query_log_table != NULL) {
			/* we have some include domains in include list => we shouldn't log
			 * query/response with name that couldn't be determined.
			 */
			should_log = ISC_FALSE;
		} else {
			/* we don't have any include list => logging query/response */
			should_log = ISC_TRUE;
		}
		goto search_done;
	}

	isc_result_t result;
	void *dummy = NULL;

	if (client->view->infoblox_query_not_log_table) {
		result = dns_rbt_findname(client->view->infoblox_query_not_log_table,
				client->query.qname, 0, NULL, &dummy);
		if (result == ISC_R_SUCCESS || result == DNS_R_PARTIALMATCH) {
			// The queried domain matches a domain name specified in exclusion list,
			// don't write it to query log.
			should_log = ISC_FALSE;
			goto search_done;
		}
	}

	if (client->view->infoblox_query_log_table) {
		result = dns_rbt_findname(client->view->infoblox_query_log_table,
				client->query.qname,
				0, NULL, &dummy);
		if (result != ISC_R_SUCCESS && result != DNS_R_PARTIALMATCH) {
			if (result != ISC_R_NOTFOUND) {
				char namebuf[DNS_NAME_FORMATSIZE];
				dns_name_format(client->query.qname, namebuf, sizeof(namebuf));
				isc_log_write(ns_g_lctx,
						NS_LOGCATEGORY_CLIENT,
						NS_LOGMODULE_QUERY, ISC_LOG_ERROR,
						"Error matching the requested domain name %s "
						"against the query log table: %s",
						namebuf,
						isc_result_totext(result));
			}
			should_log = ISC_FALSE;
			goto search_done;
		}
	}

search_done:
	client->query.infoblox_check_perhaps_log_query_or_response_done = ISC_TRUE;
	client->query.infoblox_perhaps_log_query_or_response = should_log;
	return should_log;
}

/* If target == NULL, put formatted query to tmp buffer
 * client->query.infoblox_formatted_qname_buffer, otherwise
 * copy from tmp buffer to target buffer or format directly to target buffer
 * if tmp buffer is empty. The caller is responsible not to pass
 * client->query.infoblox_formatted_qname_buffer to this function because no
 * additional checks are performed.
 */
void
infoblox_format_query_info(ns_client_t *client, isc_buffer_t *target) {
	REQUIRE(NS_CLIENT_VALID(client));
	dns_name_t *qname = NULL;

	isc_buffer_t *_target = NULL;
	if (target == NULL) {
		/* we want to format to tmp buffer */
		if (client->query.infoblox_have_formatted_qname == ISC_TRUE) {
			/* we already have formatted qname in tmp buufer */
			return;
		}
		_target = &client->query.infoblox_formatted_qname_buffer;
		if (ISC_BUFFER_VALID(_target))
			isc_buffer_invalidate(&client->query.infoblox_formatted_qname_buffer);
		isc_buffer_init(&client->query.infoblox_formatted_qname_buffer,
				client->query.infoblox_formatted_qname,
				INFOBLOX_FORMATTED_QNAME_LENGTH);
	} else if (client->query.infoblox_have_formatted_qname == ISC_TRUE) {
		/* we have formatted qname so set _target to tmp buffer and just copy
		 * from tmp buffer (_target) to dest (target)
		 */
		_target = &client->query.infoblox_formatted_qname_buffer;
	} else {
		/* we don't have formatted qname & target is not NULL => we format
		 * qname directly to target
		 */
		REQUIRE(ISC_BUFFER_VALID(target));
		_target = target;
	}

	if (client->query.infoblox_have_formatted_qname != ISC_TRUE) {
#define QUERY_LABEL "query: "
		/* putting formatted data to tmp buffer to preserve it for future use */
		infoblox_isc_buffer_putmem(_target, (const unsigned char*)QUERY_LABEL, sizeof(QUERY_LABEL) - 1);

		if (client->query.qname != NULL) {
			qname = client->query.qname;
		} else {
                	/* If query name is NULL in the client structure, try to extract it from message */
                	if (client->message != NULL) {
				isc_result_t result;
				result = dns_message_firstname(client->message, DNS_SECTION_QUESTION);
				if (result == ISC_R_SUCCESS) {
					dns_message_currentname(client->message, DNS_SECTION_QUESTION,
								&qname);
				}
			}
		}

		if (qname != NULL) {
			char namebuf[DNS_NAME_FORMATSIZE];
			char classname[DNS_RDATACLASS_FORMATSIZE];
			char typename[DNS_RDATATYPE_FORMATSIZE];
			dns_rdataset_t *rdataset;

			rdataset = ISC_LIST_HEAD(qname->list);
			INSIST(rdataset != NULL);

			dns_name_format(qname, namebuf, sizeof(namebuf));
			dns_rdataclass_format(rdataset->rdclass, classname, sizeof(classname));
			dns_rdatatype_format(rdataset->type, typename, sizeof(typename));

			infoblox_isc_buffer_putmem(_target, (const unsigned char*)namebuf, strlen(namebuf));
			infoblox_isc_buffer_putmem(_target, (const unsigned char*)" ", 1);
			infoblox_isc_buffer_putmem(_target, (const unsigned char*)classname, strlen(classname));
			infoblox_isc_buffer_putmem(_target, (const unsigned char*)" ", 1);
			infoblox_isc_buffer_putmem(_target, (const unsigned char*)typename, strlen(typename));
			client->query.infoblox_have_formatted_qname = ISC_TRUE;
		} else {
#define QNAME_NOT_FOUND "<unknown due to query error>"
			infoblox_isc_buffer_putmem(_target,
					(const unsigned char*)QNAME_NOT_FOUND,
					sizeof(QNAME_NOT_FOUND) - 1);
		}
		return;
	}
	/* we have formatted query now in _target so just put it to target */
	infoblox_isc_buffer_putmem(target, isc_buffer_base(_target), isc_buffer_usedlength(_target));
}

#define INFOBLOX_QUERY_BUFFER_LENGTH 2048
static inline void
infoblox_log_query(ns_client_t *client, unsigned int flags, unsigned int extflags) {

	if (infoblox_should_log_dns_data(client) == ISC_FALSE) {
		return;
	}

	char onbuf[ISC_NETADDR_FORMATSIZE];
	isc_netaddr_format(&client->destaddr, onbuf, sizeof(onbuf));

	char buffer[INFOBLOX_QUERY_BUFFER_LENGTH];
	isc_buffer_t query_buffer;
	isc_buffer_init(&query_buffer, buffer, INFOBLOX_QUERY_BUFFER_LENGTH - 1);
	if (infoblox_get_timestamp(&query_buffer) != ISC_R_SUCCESS) {
#define FAILTMMSG "<Failed to get timestamp>"
		infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)FAILTMMSG, sizeof(FAILTMMSG) - 1);
	}
	infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)" ", 1);
	infoblox_format_client_info(client, &query_buffer);
	infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)" ", 1);
	infoblox_format_query_info(client, &query_buffer);
	infoblox_isc_buffer_putmem(&query_buffer, WANTRECURSION(client) ? (const unsigned char*)" +" : (const unsigned char*)" -", 2);
	if (client->signer != NULL)
		infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)"S", 1);
	if (client->opt != NULL)
		infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)"E", 1);
	if ((client->attributes & NS_CLIENTATTR_TCP) != 0)
		infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)"T", 1);
	if ((extflags & DNS_MESSAGEEXTFLAG_DO) != 0)
		infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)"D", 1);
	if ((flags & DNS_MESSAGEFLAG_CD) != 0)
		infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)"C", 1);
	infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)" (", 2);
	infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)onbuf, strlen(onbuf));
#define BRACKET_AND_NEWLINE ")\n"
	infoblox_isc_buffer_putmem(&query_buffer, (const unsigned char*)BRACKET_AND_NEWLINE, sizeof(BRACKET_AND_NEWLINE) - 1);
	/* we've init buffer to allow terminating NUL */
	buffer[isc_buffer_usedlength(&query_buffer)] = '\0';
	/* no explicit handling of any error required here as
	 * log_dns_entry() produces all required error messages to syslog
	 */
	(void)infoblox_log_dns_entry(buffer);
}
#endif

static inline void
log_tat(ns_client_t *client) {
	char namebuf[DNS_NAME_FORMATSIZE];
	char clientbuf[ISC_NETADDR_FORMATSIZE];
	char classname[DNS_RDATACLASS_FORMATSIZE];
	isc_netaddr_t netaddr;
	char *tags = NULL;
	size_t taglen = 0;

	if (!isc_log_wouldlog(ns_g_lctx, ISC_LOG_INFO)) {
		return;
	}

	if ((client->query.qtype != dns_rdatatype_null ||
	     !dns_name_istat(client->query.qname)) &&
	    (client->keytag == NULL ||
	     client->query.qtype != dns_rdatatype_dnskey))
	{
		return;
	}

	isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);
	dns_name_format(client->query.qname, namebuf, sizeof(namebuf));
	isc_netaddr_format(&client->destaddr, clientbuf, sizeof(clientbuf));
	dns_rdataclass_format(client->view->rdclass, classname,
			      sizeof(classname));

	if (client->query.qtype == dns_rdatatype_dnskey) {
		isc_uint16_t keytags = client->keytag_len / 2;
		size_t len = taglen = sizeof("65000") * keytags + 1;
		char *cp = tags = isc_mem_get(client->mctx, taglen);
		int i = 0;

		INSIST(client->keytag != NULL);
		if (tags != NULL) {
			while (keytags-- > 0U) {
				int n;
				isc_uint16_t keytag;
				keytag = (client->keytag[i * 2] << 8) |
					 client->keytag[i * 2 + 1];
				n = snprintf(cp, len, " %u", keytag);
				if (n > 0 && (size_t)n <= len) {
					cp += n;
					len -= n;
					i++;
				} else {
					break;
				}
			}
		}
	}

	isc_log_write(ns_g_lctx, NS_LOGCATEGORY_TAT, NS_LOGMODULE_QUERY,
		      ISC_LOG_INFO, "trust-anchor-telemetry '%s/%s' from %s%s",
		      namebuf, classname, clientbuf, tags != NULL? tags : "");
	if (tags != NULL) {
		isc_mem_put(client->mctx, tags, taglen);
	}
}

static inline void
log_query(ns_client_t *client, unsigned int flags, unsigned int extflags) {
	char namebuf[DNS_NAME_FORMATSIZE];
	char typename[DNS_RDATATYPE_FORMATSIZE];
	char classname[DNS_RDATACLASS_FORMATSIZE];
	char onbuf[ISC_NETADDR_FORMATSIZE];
	char ednsbuf[sizeof("E(65535)")] = { 0 };
	char ecsbuf[ISC_NETADDR_FORMATSIZE + sizeof(" [ECS /255/0]") - 1] = {0};
	dns_rdataset_t *rdataset;
	int level = ISC_LOG_INFO;

	if (! isc_log_wouldlog(ns_g_lctx, level))
		return;

	rdataset = ISC_LIST_HEAD(client->query.qname->list);
	INSIST(rdataset != NULL);
	dns_name_format(client->query.qname, namebuf, sizeof(namebuf));
	dns_rdataclass_format(rdataset->rdclass, classname, sizeof(classname));
	dns_rdatatype_format(rdataset->type, typename, sizeof(typename));
	isc_netaddr_format(&client->destaddr, onbuf, sizeof(onbuf));

	if (client->ednsversion >= 0)
		snprintf(ednsbuf, sizeof(ednsbuf), "E(%hd)",
			 client->ednsversion);

	if (ECS_RECEIVED(client)) {
		char tmpbuf[ISC_NETADDR_FORMATSIZE];
		isc_netaddr_format(&client->ecs.addr, tmpbuf, sizeof(tmpbuf));
		snprintf(ecsbuf, sizeof(ecsbuf), " [ECS %s/%u/0]", tmpbuf,
			 client->ecs.source);
	}

	ns_client_log(client, NS_LOGCATEGORY_QUERIES, NS_LOGMODULE_QUERY,
		      level, "query: %s %s %s %s%s%s%s%s%s%s (%s)%s", namebuf,
		      classname, typename, WANTRECURSION(client) ? "+" : "-",
		      (client->signer != NULL) ? "S" : "", ednsbuf,
		      TCP_CLIENT(client) ? "T" : "",
		      ((extflags & DNS_MESSAGEEXTFLAG_DO) != 0) ? "D" : "",
		      ((flags & DNS_MESSAGEFLAG_CD) != 0) ? "C" : "",
		      HAVECOOKIE(client) ? "V" : WANTCOOKIE(client) ? "K" : "",
		      onbuf, ecsbuf);
}

static inline void
log_queryerror(ns_client_t *client, isc_result_t result, int line, int level) {
	char namebuf[DNS_NAME_FORMATSIZE];
	char typename[DNS_RDATATYPE_FORMATSIZE];
	char classname[DNS_RDATACLASS_FORMATSIZE];
	const char *namep, *typep, *classp, *sep1, *sep2;
	dns_rdataset_t *rdataset;

	if (!isc_log_wouldlog(ns_g_lctx, level))
		return;

	namep = typep = classp = sep1 = sep2 = "";

	/*
	 * Query errors can happen for various reasons.  In some cases we cannot
	 * even assume the query contains a valid question section, so we should
	 * expect exceptional cases.
	 */
	if (client->query.origqname != NULL) {
		dns_name_format(client->query.origqname, namebuf,
				sizeof(namebuf));
		namep = namebuf;
		sep1 = " for ";

		rdataset = ISC_LIST_HEAD(client->query.origqname->list);
		if (rdataset != NULL) {
			dns_rdataclass_format(rdataset->rdclass, classname,
					      sizeof(classname));
			classp = classname;
			dns_rdatatype_format(rdataset->type, typename,
					     sizeof(typename));
			typep = typename;
			sep2 = "/";
		}
	}

	ns_client_log(client, NS_LOGCATEGORY_QUERY_ERRORS, NS_LOGMODULE_QUERY,
		      level, "query failed (%s)%s%s%s%s%s%s at %s:%d",
		      isc_result_totext(result), sep1, namep, sep2,
		      classp, sep2, typep, __FILE__, line);
}

void
ns_query_start(ns_client_t *client) {
	isc_result_t result;
	dns_message_t *message = client->message;
	dns_rdataset_t *rdataset;
	ns_client_t *qclient;
	dns_rdatatype_t qtype;
	unsigned int saved_extflags = client->extflags;
	unsigned int saved_flags = client->message->flags;

	REQUIRE(NS_CLIENT_VALID(client));

	CTRACE(ISC_LOG_DEBUG(3), "ns_query_start");

	/*
	 * Test only.
	 */
	if (ns_g_clienttest && !TCP_CLIENT(client)) {
		result = ns_client_replace(client);
		if (result == ISC_R_SHUTTINGDOWN) {
			ns_client_next(client, result);
			return;
		} else if (result != ISC_R_SUCCESS) {
			query_error(client, result, __LINE__);
			return;
		}
	}

	/*
	 * Ensure that appropriate cleanups occur.
	 */
	client->next = query_next_callback;

	/*
	 * Behave as if we don't support DNSSEC if not enabled.
	 */
	if (!client->view->enablednssec) {
		message->flags &= ~DNS_MESSAGEFLAG_CD;
		client->extflags &= ~DNS_MESSAGEEXTFLAG_DO;
	}

	if ((message->flags & DNS_MESSAGEFLAG_RD) != 0)
		client->query.attributes |= NS_QUERYATTR_WANTRECURSION;

	if ((client->extflags & DNS_MESSAGEEXTFLAG_DO) != 0)
		client->attributes |= NS_CLIENTATTR_WANTDNSSEC;

	switch (client->view->minimalresponses) {
	case dns_minimal_no:
		break;
	case dns_minimal_yes:
		client->query.attributes |= (NS_QUERYATTR_NOAUTHORITY |
					     NS_QUERYATTR_NOADDITIONAL);
		break;
	case dns_minimal_noauth:
		client->query.attributes |= NS_QUERYATTR_NOAUTHORITY;
		break;
	case dns_minimal_noauthrec:
		if ((message->flags & DNS_MESSAGEFLAG_RD) != 0)
			client->query.attributes |= NS_QUERYATTR_NOAUTHORITY;
		break;
	}

	if ((client->view->cachedb == NULL)
	    || (!client->view->additionalfromcache)) {
		/*
		 * We don't have a cache.  Turn off cache support and
		 * recursion.
		 */
		client->query.attributes &=
			~(NS_QUERYATTR_RECURSIONOK|NS_QUERYATTR_CACHEOK);
		client->attributes |= NS_CLIENTATTR_NOSETFC;
	} else if ((client->attributes & NS_CLIENTATTR_RA) == 0 ||
		   (message->flags & DNS_MESSAGEFLAG_RD) == 0) {
		/*
		 * If the client isn't allowed to recurse (due to
		 * "recursion no", the allow-recursion ACL, or the
		 * lack of a resolver in this view), or if it
		 * doesn't want recursion, turn recursion off.
		 */
		client->query.attributes &= ~NS_QUERYATTR_RECURSIONOK;
		client->attributes |= NS_CLIENTATTR_NOSETFC;
	}

	/*
	 * Check for multiple question queries, since edns1 is dead.
	 */
	if (message->counts[DNS_SECTION_QUESTION] > 1) {
		query_error(client, DNS_R_FORMERR, __LINE__);
		return;
	}

	/*
	 * Get the question name.
	 */
#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * We also extract the query name in infoblox_format_query_info function
	 * only if client->query.qname is NULL
	 */
#endif
	result = dns_message_firstname(message, DNS_SECTION_QUESTION);
	if (result != ISC_R_SUCCESS) {
		query_error(client, result, __LINE__);
		return;
	}
	dns_message_currentname(message, DNS_SECTION_QUESTION,
				&client->query.qname);
	client->query.origqname = client->query.qname;
	result = dns_message_nextname(message, DNS_SECTION_QUESTION);
	if (result != ISC_R_NOMORE) {
		if (result == ISC_R_SUCCESS) {
			/*
			 * There's more than one QNAME in the question
			 * section.
			 */
			query_error(client, DNS_R_FORMERR, __LINE__);
		} else
			query_error(client, result, __LINE__);
		return;
	}

	if (ns_g_server->log_queries)
		log_query(client, saved_flags, saved_extflags);
#ifdef ORIGINAL_ISC_CODE
#else
	/* Here we need to check whether response logging is enabled. And if so and
	 * response to this particular query should be logged (either to syslog or
	 * to reporting) we need to format query info. This is required because here
	 * we still have original qname which may be changed later (e.g.: CNAME lookup).
	 * Checking global flag first and after that checking reporting/syslog flags
	 * to minimize impact if logging is disabled.
	 */
	if (infoblox_g_detailed_response_logging == ISC_TRUE) {
		if ((ns_g_server->infoblox_detailed_response_logging == ISC_TRUE &&
					infoblox_should_log_dns_data(client) == ISC_TRUE) ||
				ns_g_server->infoblox_detailed_response_logging_tosyslog == ISC_TRUE) {
			infoblox_format_query_info(client, NULL);
		}
	}

	/* Reporting query logging */
	if (ns_g_server->infoblox_query_logging == ISC_TRUE) {
		infoblox_log_query(client, saved_flags, saved_extflags);
	}

	/* Reporting top query */
	if (client->view && ns_g_server->infoblox_top_query) {
		if (client->view->infoblox_top_query_client) {
			dns_zone_t *rpz_zone = NULL;
			dns_name_t *rpz_qname = NULL;
			if (client->query.rpz_st) {
				rpz_zone = client->query.rpz_st->m.zone;
				rpz_qname = client->query.rpz_st->p_name;
			}
			result = infoblox_tq_client_increment(client->view, &client->peeraddr,
							      NULL, DNS_RPZ_POLICY_GIVEN, NULL, rpz_qname,
							      infoblox_zone_get_rpz_severity(rpz_zone));
			if (result != ISC_R_SUCCESS) {
				static isc_stdtime_t last = 0;
				isc_stdtime_t now;
				isc_stdtime_get(&now);
				if (now != last) {
					last = now;
					log_queryerror(client, result, __LINE__, ISC_LOG_WARNING);
				}
			}
		}
		if (client->view->infoblox_top_query_name ||
		    client->view->infoblox_client_per_zone ||
		    client->view->infoblox_client_per_fqdn) {
			result = infoblox_tq_name_increment(client->view, client->query.qname,
							    &client->peeraddr);
			if (result != ISC_R_SUCCESS) {
				static isc_stdtime_t last = 0;
				isc_stdtime_t now;
				isc_stdtime_get(&now);
				if (now != last) {
					last = now;
					log_queryerror(client, result, __LINE__, ISC_LOG_WARNING);
				}
			}
		}
		if (client->view->infoblox_top_query_rr_type) {
			dns_rdataset_t *rdataset = ISC_LIST_HEAD(client->query.qname->list);
			if (rdataset != NULL) {
				result = infoblox_tq_rr_type_increment(client->view, rdataset->type);
				if (result != ISC_R_SUCCESS) {
					static isc_stdtime_t last = 0;
					isc_stdtime_t now;
					isc_stdtime_get(&now);
					if (now != last) {
						last = now;
						log_queryerror(client, result, __LINE__, ISC_LOG_WARNING);
					}
                        	}
			}
		}
		if (client->view->infoblox_top_query_per_ip_block_tree) {
			result = infoblox_tq_ipblock_increment(client->view, &client->peeraddr);
			if (result != ISC_R_SUCCESS) {
				static isc_stdtime_t last = 0;
				isc_stdtime_t now;
				isc_stdtime_get(&now);
				if (now != last) {
					last = now;
					log_queryerror(client, result, __LINE__, ISC_LOG_WARNING);
				}
			}
		}
	}
#endif

	/*
	 * Check for meta-queries like IXFR and AXFR.
	 */
	rdataset = ISC_LIST_HEAD(client->query.qname->list);
	INSIST(rdataset != NULL);
	client->query.qtype = qtype = rdataset->type;
	dns_rdatatypestats_increment(ns_g_server->rcvquerystats, qtype);

	log_tat(client);

	if (dns_rdatatype_ismeta(qtype)) {
		switch (qtype) {
		case dns_rdatatype_any:
			break; /* Let query_find handle it. */
		case dns_rdatatype_ixfr:
		case dns_rdatatype_axfr:
			ns_xfr_start(client, rdataset->type);
			return;
		case dns_rdatatype_maila:
		case dns_rdatatype_mailb:
			query_error(client, DNS_R_NOTIMP, __LINE__);
			return;
		case dns_rdatatype_tkey:
			result = dns_tkey_processquery(client->message,
						ns_g_server->tkeyctx,
						client->view->dynamickeys);
			if (result == ISC_R_SUCCESS)
				query_send(client);
			else
				query_error(client, result, __LINE__);
			return;
		default: /* TSIG, etc. */
			query_error(client, DNS_R_FORMERR, __LINE__);
			return;
		}
	}

	/*
	 * Turn on minimal response for (C)DNSKEY and (C)DS queries.
	 */
	if (qtype == dns_rdatatype_dnskey || qtype == dns_rdatatype_ds ||
	    qtype == dns_rdatatype_cdnskey || qtype == dns_rdatatype_cds)
	{
		client->query.attributes |= (NS_QUERYATTR_NOAUTHORITY |
					     NS_QUERYATTR_NOADDITIONAL);
	}

	/*
	 * Maybe turn on minimal responses for ANY queries.
	 */
	if (qtype == dns_rdatatype_any &&
	    client->view->minimal_any && !TCP_CLIENT(client))
		client->query.attributes |= (NS_QUERYATTR_NOAUTHORITY |
					     NS_QUERYATTR_NOADDITIONAL);

	/*
	 * Turn on minimal responses for EDNS/UDP bufsize 512 queries.
	 */
	if (client->ednsversion >= 0 && client->udpsize <= 512U &&
	    !TCP_CLIENT(client))
	{
		client->query.attributes |= (NS_QUERYATTR_NOAUTHORITY |
					     NS_QUERYATTR_NOADDITIONAL);
	}

	/*
	 * If the client has requested that DNSSEC checking be disabled,
	 * allow lookups to return pending data and instruct the resolver
	 * to return data before validation has completed.
	 *
	 * We don't need to set DNS_DBFIND_PENDINGOK when validation is
	 * disabled as there will be no pending data.
	 */
	if (message->flags & DNS_MESSAGEFLAG_CD ||
	    qtype == dns_rdatatype_rrsig)
	{
		client->query.dboptions |= DNS_DBFIND_PENDINGOK;
		client->query.fetchoptions |= DNS_FETCHOPT_NOVALIDATE;
	} else if (!client->view->enablevalidation)
		client->query.fetchoptions |= DNS_FETCHOPT_NOVALIDATE;

	/*
	 * Allow glue NS records to be added to the authority section
	 * if the answer is secure.
	 */
	if (message->flags & DNS_MESSAGEFLAG_CD)
		client->query.attributes &= ~NS_QUERYATTR_SECURE;

	/*
	 * Set NS_CLIENTATTR_WANTDNSSEC if the client has set AD in the query.
	 * This allows AD to be returned on queries without DO set.
	 */
	if ((message->flags & DNS_MESSAGEFLAG_AD) != 0)
		client->attributes |= NS_CLIENTATTR_WANTAD;

	/*
	 * This is an ordinary query.
	 */
	result = dns_message_reply(message, ISC_TRUE);
	if (result != ISC_R_SUCCESS) {
		query_next(client, result);
		return;
	}

	/*
	 * Assume authoritative response until it is known to be
	 * otherwise.
	 *
	 * If "-T noaa" has been set on the command line don't set
	 * AA on authoritative answers.
	 */
	if (!ns_g_noaa)
		message->flags |= DNS_MESSAGEFLAG_AA;

	/*
	 * Set AD.  We must clear it if we add non-validated data to a
	 * response.
	 */
	if (WANTDNSSEC(client) || WANTAD(client))
		message->flags |= DNS_MESSAGEFLAG_AD;

	qclient = NULL;
	ns_client_attach(client, &qclient);
#ifdef ORIGINAL_ISC_CODE
#else
	// If subscriber services is turned on, grab subscriber data.
	if (qclient->view->infoblox_ss_enable && qclient->subscriber == NULL) {
		ib_subscriber_edns0_client_id_t local_id;
		const ib_subscriber_edns0_client_id_t *local_idp =
			ib_get_localid(&local_id, qclient);
		result = infoblox_get_subscriber_data(qclient->mctx,
						      client->query.origqname,
						      &qclient->peeraddr,
						      &qclient->destaddr,
						      local_idp,
						      &qclient->subscriber);
		if (result == ISC_R_SUCCESS) {
			ib_categories_t categories =
				qclient->subscriber->categories;
			ib_categories_t pc_categories =
				qclient->subscriber->pc_categories;
			char *cef_log_string =
				qclient->subscriber->cef_log_string;

			ns_client_log(qclient, DNS_LOGCATEGORY_SECURITY,
				      NS_LOGMODULE_QUERY, ISC_LOG_DEBUG(3),
				      "Subscriber retrieved:  "
				      "cef [%s], id [%s], "
				      "bitmap 0x%08x, "
				      "category 0x"
				      "%016" ISC_PRINT_QUADFORMAT "x"
				      "%016" ISC_PRINT_QUADFORMAT "x, "
				      "pc_category 0x"
				      "%016" ISC_PRINT_QUADFORMAT "x"
				      "%016" ISC_PRINT_QUADFORMAT "x, "
				      "proxy %d, unknown %d, dynamic %d, non_nated %d",
				      cef_log_string, qclient->subscriber->id,
				      qclient->subscriber->zbits,
				      (isc_uint64_t) (categories >> 64),
				      (isc_uint64_t) categories,
				      (isc_uint64_t) (pc_categories >> 64),
				      (isc_uint64_t) pc_categories,
				      qclient->subscriber->proxy_all,
				      qclient->subscriber->unknown,
				      qclient->subscriber->dynamic,
				      qclient->subscriber->non_nated);
		}
	}
#endif
	(void)query_find(qclient, NULL, qtype);
}
