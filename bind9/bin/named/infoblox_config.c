/*
 * Copyright (c) 2017 Infoblox Inc. All Rights Reserved.
 */

#include <config.h>

#include <isc/types.h>
#include <isc/result.h>
#include <isc/hex.h>
#include <isc/util.h>

#include <isccfg/namedconf.h>

#include <dns/infoblox_rpz_hitrate.h>
#include <dns/infoblox_onedb.h>

#include <named/config.h>
#include <named/globals.h>
#include <named/infoblox_config.h>

#define CHECK(op) \
	do { result = (op);					 \
	       if (result != ISC_R_SUCCESS) goto cleanup;	 \
	} while (0)

/*
 * This file is a placeholder for various Infoblox-specific extensions to
 * named.conf.  The content of this file is essentially a part of server.c,
 * but is extracted into a separate file so it can be easily exercised from
 * unit tests.
 *
 * So it generally shares the same assumptions that server.c (in particular,
 * load_configuration() and its friends).  For example, it assumes some
 * particular options are always specified in defaults.
 *
 * The main purpose of this separation is to minimize dependency when linked
 * into test code.  So be careful about introducing new dependency.
 * In particular, referring to ns_g_server is likely to cause a trouble and
 * should be avoided.
 */

/*
 * Parse infoblox-rpz-hit-rate-xxx named.conf options.
 * 'maps' assumed to hold the custom options in named.conf and default options.
 * The rest of the parameters are used to call infoblox_rpz_hitrate_create().
 *
 * If options are parsed successfully and the notification is enabled,
 * a new threshold monitor object is created and pointed to by '*monitorp'.
 * If parsing fails (in which case an error result will be returned) or
 * notification is disabled (in which case ISC_R_SUCCESS will be returned),
 * '*monitorp' will be intact.
 */
isc_result_t
infoblox_config_rpz_hitrate(const cfg_obj_t *maps[3], isc_mem_t *mctx,
			    isc_task_t *task, isc_timermgr_t *timermgr,
			    infoblox_threshold_monitor_t **monitorp)
{
	const cfg_obj_t *obj;
	isc_result_t result;
	isc_uint32_t time_interval, max_events, min_events, high, low;

	REQUIRE(monitorp != NULL && *monitorp == NULL);

	obj = NULL;
	ns_config_get(maps, "infoblox-rpz-hit-rate-notification", &obj);
	if (!cfg_obj_asboolean(obj))
		return (ISC_R_SUCCESS);

	obj = NULL;
	result = ns_config_get(maps,
			       "infoblox-rpz-hit-rate-notification-reset-value",
			       &obj);
	INSIST(result == ISC_R_SUCCESS);
	low = cfg_obj_asuint32(obj);

	obj = NULL;
	result = ns_config_get(maps,
			       "infoblox-rpz-hit-rate-notification-trigger-value",
			       &obj);
	INSIST(result == ISC_R_SUCCESS);
	high = cfg_obj_asuint32(obj);
	if (high > 100) {
		cfg_obj_log(obj, ns_g_lctx, ISC_LOG_ERROR,
			    "RPZ hit rate trigger value '%u' is out of range, "
			    "must be <= 100", high);
		return (ISC_R_RANGE);
	}

	if (high <= low) {
		cfg_obj_log(obj, ns_g_lctx, ISC_LOG_ERROR,
			    "RPZ hit rate trigger value '%u' must be larger "
			    "than reset value '%u'", high, low);
		return (ISC_R_RANGE);
	}

	obj = NULL;
	result = ns_config_get(maps, "infoblox-rpz-hit-rate-interval", &obj);
	INSIST(result == ISC_R_SUCCESS);
	time_interval = cfg_obj_asuint32(obj);
	if (time_interval < 1 || time_interval > 86400) {
		cfg_obj_log(obj, ns_g_lctx, ISC_LOG_ERROR,
			    "RPZ hit rate interval '%u' is out of range, "
			    "must be in [1, 86400]", time_interval);
		return (ISC_R_RANGE);
	}

	obj = NULL;
	result = ns_config_get(maps, "infoblox-rpz-hit-rate-min-query", &obj);
	INSIST(result == ISC_R_SUCCESS);
	min_events = cfg_obj_asuint32(obj);

	obj = NULL;
	result = ns_config_get(maps, "infoblox-rpz-hit-rate-max-query", &obj);
	INSIST(result == ISC_R_SUCCESS);
	max_events = cfg_obj_asuint32(obj);
	if (max_events > (1UL << 30)) {
		cfg_obj_log(obj, ns_g_lctx, ISC_LOG_ERROR,
			    "RPZ hit rate max query '%u' is out of range, "
			    "must be <= %lu", time_interval, (1UL << 30));
		return (ISC_R_RANGE);
	}

	if (max_events <= min_events) {
		cfg_obj_log(obj, ns_g_lctx, ISC_LOG_ERROR,
			    "RPZ hit rate max query '%u' must be larger "
			    "than min query '%u'", max_events, min_events);
		return (ISC_R_RANGE);
	}

	return (infoblox_rpz_hitrate_create(mctx, task, timermgr, time_interval,
					    max_events, min_events, high, low,
					    NULL, monitorp));
}

/*
 * Parse infoblox-smartcache(-xxx) options.
 * 'maps' assumed to hold the custom options in named.conf and default options.
 */
isc_result_t
infoblox_configure_smartcache(const cfg_obj_t *maps[3])
{
	const cfg_obj_t *obj;
	isc_result_t result;

	/*
	 * Find SmartCache enabled flag
	 */
	obj = NULL;
	result = ns_config_get(maps, "infoblox-smartcache", &obj);
	if (result == ISC_R_SUCCESS) {
		isc_boolean_t enabled = cfg_obj_asboolean(obj);
		infoblox_set_smartcache_enabled(enabled);
	}
	/* Find expired record TTL */
	obj = NULL;
	result = ns_config_get(maps, "infoblox-smartcache-expired-record-ttl", &obj);
	if (result == ISC_R_SUCCESS) {
		infoblox_set_smartcache_expired_record_ttl(cfg_obj_asuint32(obj));
	}

	/* Find timeout for expired record */
	obj = NULL;
	result = ns_config_get(maps, "infoblox-smartcache-expired-record-timeout", &obj);
	if (result == ISC_R_SUCCESS) {
		infoblox_set_smartcache_expired_record_timeout(cfg_obj_asuint32(obj));
	}

	return (ISC_R_SUCCESS);
}
