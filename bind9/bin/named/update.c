/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

#ifdef INFOBLOX_INCREMENTAL_SIGNING
#include <named/config.h>
#else
#include <config.h>
#endif

#include <isc/netaddr.h>
#include <isc/print.h>
#include <isc/serial.h>
#include <isc/stats.h>
#include <isc/string.h>
#include <isc/taskpool.h>
#include <isc/util.h>

#include <dns/db.h>
#include <dns/dbiterator.h>
#include <dns/diff.h>
#include <dns/dnssec.h>
#include <dns/events.h>
#include <dns/fixedname.h>
#include <dns/journal.h>
#include <dns/keyvalues.h>
#include <dns/message.h>
#include <dns/nsec.h>
#include <dns/nsec3.h>
#include <dns/private.h>
#include <dns/rdataclass.h>
#include <dns/rdataset.h>
#include <dns/rdatasetiter.h>
#include <dns/rdatastruct.h>
#include <dns/rdatatype.h>
#include <dns/soa.h>
#include <dns/ssu.h>
#include <dns/tsig.h>
#include <dns/update.h>
#include <dns/view.h>
#include <dns/zone.h>
#include <dns/zt.h>

#include <named/client.h>
#include <named/log.h>
#include <named/server.h>
#include <named/update.h>
#ifdef	ORIGINAL_ISC_CODE
#else
#include <unistd.h>
#include <errno.h>
#include <dns/infoblox_onedb.h>
#include <dns/infoblox_db_if.h>
#include <dns/infoblox_zdb_if.h>
#include <dns/infoblox_zrq.h>
#include <dns/infoblox_ha_state.h>
#include <dns/infoblox_sdu_pattern.h>

static dns_zone_t *infoblox_update_zone = NULL;
static ns_client_t *infoblox_update_client = NULL;

#endif

/*! \file
 * \brief
 * This module implements dynamic update as in RFC2136.
 */

/*
 *  XXX TODO:
 * - document strict minimality
 */

/**************************************************************************/

/*%
 * Log level for tracing dynamic update protocol requests.
 */
#define LOGLEVEL_PROTOCOL	ISC_LOG_INFO

/*%
 * Log level for low-level debug tracing.
 */
#define LOGLEVEL_DEBUG		ISC_LOG_DEBUG(8)

/*%
 * Check an operation for failure.  These macros all assume that
 * the function using them has a 'result' variable and a 'failure'
 * label.
 */
#define CHECK(op) \
	do { result = (op); \
		if (result != ISC_R_SUCCESS) goto failure; \
	} while (0)

/*%
 * Fail unconditionally with result 'code', which must not
 * be ISC_R_SUCCESS.  The reason for failure presumably has
 * been logged already.
 *
 * The test against ISC_R_SUCCESS is there to keep the Solaris compiler
 * from complaining about "end-of-loop code not reached".
 */

#define FAIL(code) \
	do {							\
		result = (code);				\
		if (result != ISC_R_SUCCESS) goto failure;	\
	} while (0)

/*%
 * Fail unconditionally and log as a client error.
 * The test against ISC_R_SUCCESS is there to keep the Solaris compiler
 * from complaining about "end-of-loop code not reached".
 */
#define FAILC(code, msg) \
	do {							\
		const char *_what = "failed";			\
		result = (code);				\
		switch (result) {				\
		case DNS_R_NXDOMAIN:				\
		case DNS_R_YXDOMAIN:				\
		case DNS_R_YXRRSET:				\
		case DNS_R_NXRRSET:				\
			_what = "unsuccessful";			\
		}						\
		update_log(client, zone, LOGLEVEL_PROTOCOL,	\
			   "update %s: %s (%s)", _what,		\
			   msg, isc_result_totext(result));	\
		if (result != ISC_R_SUCCESS) goto failure;	\
	} while (0)
#define PREREQFAILC(code, msg) \
	do {							\
		inc_stats(zone, dns_nsstatscounter_updatebadprereq); \
		FAILC(code, msg);				\
	} while (0)

#define FAILN(code, name, msg) \
	do {								\
		const char *_what = "failed";				\
		result = (code);					\
		switch (result) {					\
		case DNS_R_NXDOMAIN:					\
		case DNS_R_YXDOMAIN:					\
		case DNS_R_YXRRSET:					\
		case DNS_R_NXRRSET:					\
			_what = "unsuccessful";				\
		}							\
		if (isc_log_wouldlog(ns_g_lctx, LOGLEVEL_PROTOCOL)) {	\
			char _nbuf[DNS_NAME_FORMATSIZE];		\
			dns_name_format(name, _nbuf, sizeof(_nbuf));	\
			update_log(client, zone, LOGLEVEL_PROTOCOL,	\
				   "update %s: %s: %s (%s)", _what, _nbuf, \
				   msg, isc_result_totext(result));	\
		}							\
		if (result != ISC_R_SUCCESS) goto failure;		\
	} while (0)
#define PREREQFAILN(code, name, msg) \
	do {								\
		inc_stats(zone, dns_nsstatscounter_updatebadprereq); \
		FAILN(code, name, msg);					\
	} while (0)

#define FAILNT(code, name, type, msg) \
	do {								\
		const char *_what = "failed";				\
		result = (code);					\
		switch (result) {					\
		case DNS_R_NXDOMAIN:					\
		case DNS_R_YXDOMAIN:					\
		case DNS_R_YXRRSET:					\
		case DNS_R_NXRRSET:					\
			_what = "unsuccessful";				\
		}							\
		if (isc_log_wouldlog(ns_g_lctx, LOGLEVEL_PROTOCOL)) {	\
			char _nbuf[DNS_NAME_FORMATSIZE];		\
			char _tbuf[DNS_RDATATYPE_FORMATSIZE];		\
			dns_name_format(name, _nbuf, sizeof(_nbuf));	\
			dns_rdatatype_format(type, _tbuf, sizeof(_tbuf)); \
			update_log(client, zone, LOGLEVEL_PROTOCOL,	\
				   "update %s: %s/%s: %s (%s)",		\
				   _what, _nbuf, _tbuf, msg,		\
				   isc_result_totext(result));		\
		}							\
		if (result != ISC_R_SUCCESS) goto failure;		\
	} while (0)
#define PREREQFAILNT(code, name, type, msg)				\
	do {								\
		inc_stats(zone, dns_nsstatscounter_updatebadprereq); \
		FAILNT(code, name, type, msg);				\
	} while (0)

/*%
 * Fail unconditionally and log as a server error.
 * The test against ISC_R_SUCCESS is there to keep the Solaris compiler
 * from complaining about "end-of-loop code not reached".
 */
#define FAILS(code, msg) \
	do {							\
		result = (code);				\
		update_log(client, zone, LOGLEVEL_PROTOCOL,	\
			   "error: %s: %s",			\
			   msg, isc_result_totext(result));	\
		if (result != ISC_R_SUCCESS) goto failure;	\
	} while (0)

/*
 * Return TRUE if NS_CLIENTATTR_TCP is set in the attributes other FALSE.
 */
#define TCPCLIENT(client) (((client)->attributes & NS_CLIENTATTR_TCP) != 0)

/**************************************************************************/

typedef struct rr rr_t;

struct rr {
	/* dns_name_t name; */
	isc_uint32_t		ttl;
	dns_rdata_t		rdata;
#ifdef ORIGINAL_ISC_CODE
#else
	isc_uint32_t		updated;
#endif
};

typedef struct update_event update_event_t;

struct update_event {
	ISC_EVENT_COMMON(update_event_t);
	dns_zone_t		*zone;
	isc_result_t		result;
	dns_message_t		*answer;
};

/*%
 * Prepare an RR for the addition of the new RR 'ctx->update_rr',
 * with TTL 'ctx->update_rr_ttl', to its rdataset, by deleting
 * the RRs if it is replaced by the new RR or has a conflicting TTL.
 * The necessary changes are appended to ctx->del_diff and ctx->add_diff;
 * we need to do all deletions before any additions so that we don't run
 * into transient states with conflicting TTLs.
 */

typedef struct {
	dns_db_t *db;
	dns_dbversion_t *ver;
	dns_diff_t *diff;
	dns_name_t *name;
	dns_name_t *oldname;
	dns_rdata_t *update_rr;
	dns_ttl_t update_rr_ttl;
	isc_boolean_t ignore_add;
	dns_diff_t del_diff;
	dns_diff_t add_diff;
#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * will be set to true before the preparation if creation_timestamp has
	 * to be updated even if the update doesn't change the RR.
	 */
	isc_boolean_t update_creation_timestamp_on_unchanged;
#endif
} add_rr_prepare_ctx_t;

/**************************************************************************/
/*
 * Forward declarations.
 */

static void update_action(isc_task_t *task, isc_event_t *event);
static void updatedone_action(isc_task_t *task, isc_event_t *event);
static isc_result_t send_forward_event(ns_client_t *client, dns_zone_t *zone);
static void forward_done(isc_task_t *task, isc_event_t *event);
static isc_result_t add_rr_prepare_action(void *data, rr_t *rr);

/**************************************************************************/

static void
update_log(ns_client_t *client, dns_zone_t *zone,
	   int level, const char *fmt, ...) ISC_FORMAT_PRINTF(4, 5);

static void
update_log(ns_client_t *client, dns_zone_t *zone,
	   int level, const char *fmt, ...)
{
	va_list ap;
	char message[4096];
	char namebuf[DNS_NAME_FORMATSIZE];
	char classbuf[DNS_RDATACLASS_FORMATSIZE];

	if (client == NULL || zone == NULL)
		return;

	if (isc_log_wouldlog(ns_g_lctx, level) == ISC_FALSE)
		return;

	dns_name_format(dns_zone_getorigin(zone), namebuf,
			sizeof(namebuf));
	dns_rdataclass_format(dns_zone_getclass(zone), classbuf,
			      sizeof(classbuf));

	va_start(ap, fmt);
	vsnprintf(message, sizeof(message), fmt, ap);
	va_end(ap);

	ns_client_log(client, NS_LOGCATEGORY_UPDATE, NS_LOGMODULE_UPDATE,
		      level, "updating zone '%s/%s': %s",
		      namebuf, classbuf, message);
}

static void
update_log_cb(void *arg, dns_zone_t *zone, int level, const char *message) {
	update_log(arg, zone, level, "%s", message);
}

/*%
 * Increment updated-related statistics counters.
 */
static inline void
inc_stats(dns_zone_t *zone, isc_statscounter_t counter) {
#if defined(ORIGINAL_ISC_CODE) || !defined(INFOBLOX_INCREMENTAL_SIGNING)
	isc_stats_increment(ns_g_server->nsstats, counter);
#else
        /* NOP (ns_g_server does not exist) */
#endif

	if (zone != NULL) {
		isc_stats_t *zonestats = dns_zone_getrequeststats(zone);
		if (zonestats != NULL)
			isc_stats_increment(zonestats, counter);
	}
}

/*%
 * Check if we could have queried for the contents of this zone or
 * if the zone is potentially updateable.
 * If the zone can potentially be updated and the check failed then
 * log a error otherwise we log a informational message.
 */
static isc_result_t
checkqueryacl(ns_client_t *client, dns_acl_t *queryacl, dns_name_t *zonename,
	      dns_acl_t *updateacl, dns_ssutable_t *ssutable)
{
	char namebuf[DNS_NAME_FORMATSIZE];
	char classbuf[DNS_RDATACLASS_FORMATSIZE];
	int level;
	isc_result_t result;

	result = ns_client_checkaclsilent(client, NULL, queryacl, ISC_TRUE);
	if (result != ISC_R_SUCCESS) {
		dns_name_format(zonename, namebuf, sizeof(namebuf));
		dns_rdataclass_format(client->view->rdclass, classbuf,
				      sizeof(classbuf));

		level = (updateacl == NULL && ssutable == NULL) ?
				ISC_LOG_INFO : ISC_LOG_ERROR;

		ns_client_log(client, NS_LOGCATEGORY_UPDATE_SECURITY,
			      NS_LOGMODULE_UPDATE, level,
			      "update '%s/%s' denied due to allow-query",
			      namebuf, classbuf);
	} else if (updateacl == NULL && ssutable == NULL) {
		dns_name_format(zonename, namebuf, sizeof(namebuf));
		dns_rdataclass_format(client->view->rdclass, classbuf,
				      sizeof(classbuf));

		result = DNS_R_REFUSED;
		ns_client_log(client, NS_LOGCATEGORY_UPDATE_SECURITY,
			      NS_LOGMODULE_UPDATE, ISC_LOG_INFO,
			      "update '%s/%s' denied", namebuf, classbuf);
	}
	return (result);
}

/*%
 * Override the default acl logging when checking whether a client
 * can update the zone or whether we can forward the request to the
 * master based on IP address.
 *
 * 'message' contains the type of operation that is being attempted.
 * 'slave' indicates if this is a slave zone.  If 'acl' is NULL then
 * log at debug=3.
 * If the zone has no access controls configured ('acl' == NULL &&
 * 'has_ssutable == ISC_FALS) log the attempt at info, otherwise
 * at error.
 *
 * If the request was signed log that we received it.
 */
static isc_result_t
checkupdateacl(ns_client_t *client, dns_acl_t *acl, const char *message,
	       dns_name_t *zonename, isc_boolean_t slave,
	       isc_boolean_t has_ssutable)
{
	char namebuf[DNS_NAME_FORMATSIZE];
	char classbuf[DNS_RDATACLASS_FORMATSIZE];
	int level = ISC_LOG_ERROR;
	const char *msg = "denied";
	isc_result_t result;

	if (slave && acl == NULL) {
		result = DNS_R_NOTIMP;
		level = ISC_LOG_DEBUG(3);
		msg = "disabled";
	} else {
		result = ns_client_checkaclsilent(client, NULL, acl, ISC_FALSE);
		if (result == ISC_R_SUCCESS) {
			level = ISC_LOG_DEBUG(3);
			msg = "approved";
		} else if (acl == NULL && !has_ssutable) {
			level = ISC_LOG_INFO;
		}
	}

	if (client->signer != NULL) {
		dns_name_format(client->signer, namebuf, sizeof(namebuf));
		ns_client_log(client, NS_LOGCATEGORY_UPDATE_SECURITY,
			      NS_LOGMODULE_UPDATE, ISC_LOG_INFO,
			      "signer \"%s\" %s", namebuf, msg);
	}

	dns_name_format(zonename, namebuf, sizeof(namebuf));
	dns_rdataclass_format(client->view->rdclass, classbuf,
			      sizeof(classbuf));

	ns_client_log(client, NS_LOGCATEGORY_UPDATE_SECURITY,
		      NS_LOGMODULE_UPDATE, level, "%s '%s/%s' %s",
		      message, namebuf, classbuf, msg);
	return (result);
}

/*%
 * Update a single RR in version 'ver' of 'db' and log the
 * update in 'diff'.
 *
 * Ensures:
 * \li	'*tuple' == NULL.  Either the tuple is freed, or its
 *	ownership has been transferred to the diff.
 */
static isc_result_t
do_one_tuple(dns_difftuple_t **tuple, dns_db_t *db, dns_dbversion_t *ver,
	     dns_diff_t *diff)
{
	dns_diff_t temp_diff;
	isc_result_t result;

	/*
	 * Create a singleton diff.
	 */
	dns_diff_init(diff->mctx, &temp_diff);
	ISC_LIST_APPEND(temp_diff.tuples, *tuple, link);

	/*
	 * Apply it to the database.
	 */
	result = dns_diff_apply(&temp_diff, db, ver);
	ISC_LIST_UNLINK(temp_diff.tuples, *tuple, link);
	if (result != ISC_R_SUCCESS) {
		dns_difftuple_free(tuple);
		return (result);
	}

	/*
	 * Merge it into the current pending journal entry.
	 */
	dns_diff_appendminimal(diff, tuple);
#ifdef ORIGINAL_ISC_CODE
#else
	/* Move generated bulk host sourced tuples into diff->bhs_tuples.
	 * We defer minimal optimization. */
	dns_difftuple_t * temp_tuple, * next;
	for (temp_tuple = ISC_LIST_HEAD(temp_diff.bhs_tuples);
			temp_tuple != NULL; temp_tuple = next) {
		next = ISC_LIST_NEXT(temp_tuple, link);
		ISC_LIST_UNLINK(temp_diff.bhs_tuples, temp_tuple, link);
		ISC_LIST_APPEND(diff->bhs_tuples, temp_tuple, link);
	}
#endif

	/*
	 * Do not clear temp_diff.
	 */
	return (ISC_R_SUCCESS);
}

/*%
 * Perform the updates in 'updates' in version 'ver' of 'db' and log the
 * update in 'diff'.
 *
 * Ensures:
 * \li	'updates' is empty.
 */
static isc_result_t
do_diff(dns_diff_t *updates, dns_db_t *db, dns_dbversion_t *ver,
	dns_diff_t *diff)
{
	isc_result_t result;
#ifdef ORIGINAL_ISC_CODE
#else
	unsigned is_ttl_delete = 0;
	dns_ttl_t ttl = 0;
#endif
	while (! ISC_LIST_EMPTY(updates->tuples)) {
		dns_difftuple_t *t = ISC_LIST_HEAD(updates->tuples);
		ISC_LIST_UNLINK(updates->tuples, t, link);
#ifdef ORIGINAL_ISC_CODE
#else
		is_ttl_delete = t->is_ttl_delete;
		ttl = t->ttl;
#endif
		CHECK(do_one_tuple(&t, db, ver, diff));
	}
	return (ISC_R_SUCCESS);

 failure:
#ifdef ORIGINAL_ISC_CODE
#else
	if (is_ttl_delete && result == DNS_R_REFUSED &&
	    infoblox_update_zone != NULL && infoblox_update_client != NULL) {
		// We've received an update that requires modification of the TTL of
		// an existing entry. However, that existing entry cannot be updated,
		// perhaps because it is a shared record, or an auto-created entry.
		update_log(infoblox_update_client, infoblox_update_zone, LOGLEVEL_PROTOCOL,
			   "Update rejected because TTL differs from existing unmodifiable "
			   "entry: Use TTL %u", ttl);
	}
#endif
	dns_diff_clear(diff);
	return (result);
}

static isc_result_t
update_one_rr(dns_db_t *db, dns_dbversion_t *ver, dns_diff_t *diff,
	      dns_diffop_t op, dns_name_t *name, dns_ttl_t ttl,
	      dns_rdata_t *rdata)
{
	dns_difftuple_t *tuple = NULL;
	isc_result_t result;
	result = dns_difftuple_create(diff->mctx, op,
				      name, ttl, rdata, &tuple);
	if (result != ISC_R_SUCCESS)
		return (result);
	return (do_one_tuple(&tuple, db, ver, diff));
}

#ifdef ISC_ORIGINAL_CODE
#else
static isc_result_t delete_if_action(void *data, rr_t *rr);
static isc_result_t add_rr_prepare_action(void *data, rr_t *rr);
static isc_boolean_t ib_delete_is_zdb(void *data);
static isc_result_t ib_change_ttl(void *data, dns_ttl_t oldttl, dns_ttl_t newttl,
				  dns_rdata_t *rdata);
#endif
/**************************************************************************/
/*
 * Callback-style iteration over rdatasets and rdatas.
 *
 * foreach_rrset() can be used to iterate over the RRsets
 * of a name and call a callback function with each
 * one.  Similarly, foreach_rr() can be used to iterate
 * over the individual RRs at name, optionally restricted
 * to RRs of a given type.
 *
 * The callback functions are called "actions" and take
 * two arguments: a void pointer for passing arbitrary
 * context information, and a pointer to the current RRset
 * or RR.  By convention, their names end in "_action".
 */

/*
 * XXXRTH  We might want to make this public somewhere in libdns.
 */

/*%
 * Function type for foreach_rrset() iterator actions.
 */
typedef isc_result_t rrset_func(void *data, dns_rdataset_t *rrset);

/*%
 * Function type for foreach_rr() iterator actions.
 */
typedef isc_result_t rr_func(void *data, rr_t *rr);

/*%
 * Internal context struct for foreach_node_rr().
 */
typedef struct {
	rr_func *	rr_action;
	void *		rr_action_data;
} foreach_node_rr_ctx_t;

/*%
 * Internal helper function for foreach_node_rr().
 */
static isc_result_t
foreach_node_rr_action(void *data, dns_rdataset_t *rdataset) {
	isc_result_t result;
	foreach_node_rr_ctx_t *ctx = data;
#ifdef ISC_ORIGINAL_CODE
#else
	if (rdataset->bulkhost_sourced && ctx->rr_action == delete_if_action)
	{
		INSIST(rdataset->type == dns_rdatatype_a ||
				rdataset->type == dns_rdatatype_ptr);
		/* MMDNS:BULKHOST
		 * Retrieving bulk host sourced rdataset indicates there is no non-bulk
		 * host sourced rdataset (either non-existant or masked) for the name.
		 * As bulk host sourced rdataset can not be deleted until bulk host is
		 * removed, so we return DNS_R_REFUSED as original implementation.
		 * It also prevents putting any bulk host sourced rdataset into
		 * journal file.
		 */
		return DNS_R_REFUSED;
	}
#endif
	for (result = dns_rdataset_first(rdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(rdataset))
	{
#ifdef ORIGINAL_ISC_CODE
		rr_t rr = { 0, DNS_RDATA_INIT };
#else
		rr_t rr = { 0, DNS_RDATA_INIT, 0 };
#endif

		dns_rdataset_current(rdataset, &rr.rdata);
		rr.ttl = rdataset->ttl;
		result = (*ctx->rr_action)(ctx->rr_action_data, &rr);
		if (result != ISC_R_SUCCESS)
			return (result);
	}
	if (result != ISC_R_NOMORE)
		return (result);
	return (ISC_R_SUCCESS);
}

/*%
 * For each rdataset of 'name' in 'ver' of 'db', call 'action'
 * with the rdataset and 'action_data' as arguments.  If the name
 * does not exist, do nothing.
 *
 * If 'action' returns an error, abort iteration and return the error.
 */
static isc_result_t
foreach_rrset(dns_db_t *db, dns_dbversion_t *ver, dns_name_t *name,
	      rrset_func *action, void *action_data)
{
	isc_result_t result;
	dns_dbnode_t *node;
	dns_rdatasetiter_t *iter;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;
	dns_dbversion_t *oldver = NULL;

	dns_clientinfomethods_init(&cm, ns_client_sourceip);

	/*
	 * Only set the clientinfo 'versionp' if the new version is
	 * different from the current version
	 */
	dns_db_currentversion(db, &oldver);
	dns_clientinfo_init(&ci, NULL, NULL, (ver != oldver) ? ver : NULL);
	dns_db_closeversion(db, &oldver, ISC_FALSE);

	node = NULL;
	result = dns_db_findnodeext(db, name, ISC_FALSE, &cm, &ci, &node);
	if (result == ISC_R_NOTFOUND)
		return (ISC_R_SUCCESS);
	if (result != ISC_R_SUCCESS)
		return (result);

	iter = NULL;
	result = dns_db_allrdatasets(db, node, ver,
				     (isc_stdtime_t) 0, &iter);
	if (result != ISC_R_SUCCESS)
		goto cleanup_node;

	for (result = dns_rdatasetiter_first(iter);
	     result == ISC_R_SUCCESS;
	     result = dns_rdatasetiter_next(iter))
	{
		dns_rdataset_t rdataset;

		dns_rdataset_init(&rdataset);
		dns_rdatasetiter_current(iter, &rdataset);

		result = (*action)(action_data, &rdataset);

		dns_rdataset_disassociate(&rdataset);
		if (result != ISC_R_SUCCESS)
			goto cleanup_iterator;
	}
	if (result == ISC_R_NOMORE)
		result = ISC_R_SUCCESS;

 cleanup_iterator:
	dns_rdatasetiter_destroy(&iter);

 cleanup_node:
	dns_db_detachnode(db, &node);

	return (result);
}

/*%
 * For each RR of 'name' in 'ver' of 'db', call 'action'
 * with the RR and 'action_data' as arguments.  If the name
 * does not exist, do nothing.
 *
 * If 'action' returns an error, abort iteration
 * and return the error.
 */
static isc_result_t
foreach_node_rr(dns_db_t *db, dns_dbversion_t *ver, dns_name_t *name,
		rr_func *rr_action, void *rr_action_data)
{
	foreach_node_rr_ctx_t ctx;
	ctx.rr_action = rr_action;
	ctx.rr_action_data = rr_action_data;
	return (foreach_rrset(db, ver, name,
			      foreach_node_rr_action, &ctx));
}


#ifdef ORIGINAL_ISC_CODE
#else

/* A helper for foreach_rr() (see below), updating the creation timestamp
 * of an RR being added but ignored as a duplicate.  This function identifies
 * the rdata in 'rdataset' that was retrieved from 'db', which should have
 * originally built from data in OneDB.  This will make sure the OneDB key
 * is used from the RDATA even if the RR in 'ctx' has a different character
 * case than that in OneDB. */
static isc_result_t
ib_update_creation_timestamp(dns_db_t *db, add_rr_prepare_ctx_t *ctx,
			     dns_rdataset_t *rdataset)
{
	isc_result_t result;
	infoblox_dns_name_t iname = INFOBLOX_DNS_NAME_INITIALIZER;

	iname = infoblox_dns_name_totext(NULL, ctx->name,
					 dns_db_origin(db), 0, 1);
	if (iname.zone == NULL || iname.host == NULL) {
		infoblox_log(1, "%s: Error obtaining zone name.", __FUNCTION__);
		return (ISC_R_UNEXPECTED);
	}
	for (result = dns_rdataset_first(rdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(rdataset))
	{
		dns_rdata_t rdata = DNS_RDATA_INIT;
		dns_rdataset_current(rdataset, &rdata);
		if (dns_rdata_casecompare(&rdata, ctx->update_rr) != 0)
			continue;
		result = infoblox_update_creation_timestamp_and_ddns_principal(
			&rdata, iname.zone, iname.host);
		if (result == ISC_R_SUCCESS)
			return (result);
	}
	infoblox_log(1, "%s: %s", __FUNCTION__, isc_result_totext(result));
	return (result);
}
#endif

/*%
 * For each of the RRs specified by 'db', 'ver', 'name', 'type',
 * (which can be dns_rdatatype_any to match any type), and 'covers', call
 * 'action' with the RR and 'action_data' as arguments. If the name
 * does not exist, or if no RRset of the given type exists at the name,
 * do nothing.
 *
 * If 'action' returns an error, abort iteration and return the error.
 */
static isc_result_t
foreach_rr(dns_db_t *db, dns_dbversion_t *ver, dns_name_t *name,
	   dns_rdatatype_t type, dns_rdatatype_t covers, rr_func *rr_action,
	   void *rr_action_data)
{

	isc_result_t result;
	dns_dbnode_t *node;
	dns_rdataset_t rdataset;
	dns_clientinfomethods_t cm;
	dns_clientinfo_t ci;
	dns_dbversion_t *oldver = NULL;
	dns_fixedname_t fixed;
#ifdef ORIGINAL_ISC_CODE
#else
	isc_boolean_t *ib_deleted = NULL;
	add_rr_prepare_ctx_t *add_rr_prepare_ctx = NULL;
	infoblox_dns_name_t iname = INFOBLOX_DNS_NAME_INITIALIZER;
#endif

	dns_clientinfomethods_init(&cm, ns_client_sourceip);

	/*
	 * Only set the clientinfo 'versionp' if the new version is
	 * different from the current version
	 */
	dns_db_currentversion(db, &oldver);
	dns_clientinfo_init(&ci, NULL, NULL, (ver != oldver) ? ver : NULL);
	dns_db_closeversion(db, &oldver, ISC_FALSE);

	if (type == dns_rdatatype_any)
		return (foreach_node_rr(db, ver, name,
					rr_action, rr_action_data));

	node = NULL;
	if (type == dns_rdatatype_nsec3 ||
	    (type == dns_rdatatype_rrsig && covers == dns_rdatatype_nsec3))
		result = dns_db_findnsec3node(db, name, ISC_FALSE, &node);
	else
		result = dns_db_findnodeext(db, name, ISC_FALSE,
					    &cm, &ci, &node);
#ifdef	ORIGINAL_ISC_CODE
	if (result == ISC_R_NOTFOUND)
		return (ISC_R_SUCCESS);
	if (result != ISC_R_SUCCESS)
		return (result);
#else
	if (ISC_R_NOTFOUND == result) {
	  if (NULL != node) {
	    infoblox_log(4, "foreach_rr: detaching node (not found)");
	    dns_db_detachnode(db, &node);
	  }
	  return (ISC_R_SUCCESS);
	}
	if (ISC_R_SUCCESS != result) {
	  if (NULL != node) {
	    infoblox_log(4, "foreach_rr: detaching node (result=%s)",
			 isc_result_totext(result));
	    dns_db_detachnode(db, &node);
	  }
	  return (result);
	}
#endif

	dns_rdataset_init(&rdataset);
	result = dns_db_findrdataset(db, node, ver, type, covers,
				     (isc_stdtime_t) 0, &rdataset, NULL);
	if (result == ISC_R_NOTFOUND) {
		result = ISC_R_SUCCESS;
		goto cleanup_node;
	}
	if (result != ISC_R_SUCCESS)
		goto cleanup_node;
#ifdef ISC_ORIGINAL_CODE
#else
	if (rdataset.bulkhost_sourced && rr_action == add_rr_prepare_action)
	{
		INSIST(rdataset.type == dns_rdatatype_a ||
				rdataset.type == dns_rdatatype_ptr);
		/* MMDNS:BULKHOST
		 * Retrieving bulk host sourced rdataset indicates there is no non-bulk
		 * host sourced rdataset (either non-existant or masked) for the name.
		 * As bulk host rdataset is always overwritten by non-bulk host rdataset,
		 * we can skip add_rr_prepare_action() to pre-process any bulk host sourced
		 * rdataset against incoming non-bulk host sourced rdataset.
		 * It also prevents putting any bulk host sourced rdataset
		 * into journal file.
		 */
		result = ISC_R_SUCCESS;
		goto cleanup_rdataset;
	}
#endif

	if (rr_action == add_rr_prepare_action) {
		add_rr_prepare_ctx_t *ctx = rr_action_data;

		dns_fixedname_init(&fixed);
		ctx->oldname = dns_fixedname_name(&fixed);
		dns_name_copy(name, ctx->oldname, NULL);
		dns_rdataset_getownercase(&rdataset, ctx->oldname);
	}

#ifdef ORIGINAL_ISC_CODE
#else
	unsigned int ib_rdatacount, ib_rrcount, ib_updated;
	isc_boolean_t ib_is_zdb = (rr_action == delete_if_action && ib_delete_is_zdb(rr_action_data));
	if (ib_is_zdb && (ib_rdatacount = dns_rdataset_count(&rdataset)) > 1) {
		// Allocate an array to track which RRs are deleted and which
		// still exist.
		ib_deleted = malloc(ib_rdatacount * sizeof(ib_deleted[0]));
		if (ib_deleted == NULL) {
			result = ISC_R_NOMEMORY;
			goto cleanup_rdataset;
		}
		ib_rrcount = 0;
		ib_updated = 0;
	}
#endif
	for (result = dns_rdataset_first(&rdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(&rdataset))
	{
#ifdef ORIGINAL_ISC_CODE
		rr_t rr = { 0, DNS_RDATA_INIT };
#else
		rr_t rr = { 0, DNS_RDATA_INIT, 0 };
#endif
		dns_rdataset_current(&rdataset, &rr.rdata);
		rr.ttl = rdataset.ttl;
		result = (*rr_action)(rr_action_data, &rr);
		if (result != ISC_R_SUCCESS)
			goto cleanup_rdataset;
#ifdef ORIGINAL_ISC_CODE
#else
		// Track which RRs we delete
		if (ib_deleted) {
			if (rr.updated) {
				ib_updated += 1;
				ib_deleted[ib_rrcount++] = ISC_TRUE;
			} else {
				ib_deleted[ib_rrcount++] = ISC_FALSE;
			}
		}
#endif
	}
	if (result != ISC_R_NOMORE)
		goto cleanup_rdataset;
	result = ISC_R_SUCCESS;

#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * On checking of changes in RR there is a special handling if forced
	 * update of creation timestamp is enabled. If the option is set
	 * creation timestamp is updated regardless of changes in RR.
	 * Also SDU check is performed. In case of successful update
	 * update_creation_timestamp_on_unchanged flag is set in the
	 * preparation context.
	 */
	if (rr_action == add_rr_prepare_action) {
		add_rr_prepare_ctx = rr_action_data;
		if (add_rr_prepare_ctx->ignore_add &&
		    add_rr_prepare_ctx->update_creation_timestamp_on_unchanged)
		{
			result = ib_update_creation_timestamp(
				db, add_rr_prepare_ctx, &rdataset);
			if (result != ISC_R_SUCCESS)
				goto cleanup_rdataset;
		}
	}
	// If there was more than one RR to begin with, we deleted at least
	// one RR, and there is at least one RR left, then we need to check
	// if the TTL of the RR set changed.
	// Note: If we uncovered a bulk-host-sourced RR, then we will have
	// deleted all existing RRs (ib_rrcount==ib_updated), and the following
	// if-statement does not trigger. That is what we want, since there
	// is separate code to deal with unmasking bulk host data.
	if (ib_deleted && ib_updated >= 1 && ib_rrcount != ib_updated) {
		// Find the minimum TTL, and if different from
		// rdataset.ttl, generate the diffs to update
		// the TTL for the RR set.
		dns_ttl_t minttl = INFOBLOX_ZDB_MAX_TTL;
		// If we get here, we know that we are using the ZDB
		result = infoblox_zdb_get_minimum_ttl(db, node, rdataset.type, &minttl);
		if (result != ISC_R_SUCCESS)
			goto cleanup_rdataset;
		// If the TTL for the DB data is different from the
		// in-memory RR set, change the TTL for all remaining
		// RRs.
		// minttl==INFOBLOX_ZDB_MAX_TTL indicates no change should be made.
		if (minttl != INFOBLOX_ZDB_MAX_TTL && minttl != rdataset.ttl) {
			for (result = dns_rdataset_first(&rdataset), ib_rrcount = 0;
			     result == ISC_R_SUCCESS;
			     result = dns_rdataset_next(&rdataset), ib_rrcount++) {
				if (ib_deleted[ib_rrcount])
					continue;
				dns_rdata_t ib_rdata = DNS_RDATA_INIT;
				dns_rdataset_current(&rdataset, &ib_rdata);
				result = ib_change_ttl(rr_action_data, rdataset.ttl, minttl, &ib_rdata);
				if (result != ISC_R_SUCCESS)
					goto cleanup_rdataset;
			}
			if (result != ISC_R_NOMORE)
				goto cleanup_rdataset;
			result = ISC_R_SUCCESS;
		}
	}
#endif

 cleanup_rdataset:
#ifdef ORIGINAL_ISC_CODE
#else
	if (ib_deleted)
		free(ib_deleted);
	infoblox_dns_name_free(NULL, &iname);
#endif
	dns_rdataset_disassociate(&rdataset);
 cleanup_node:
	dns_db_detachnode(db, &node);

	return (result);
}

/**************************************************************************/
/*
 * Various tests on the database contents (for prerequisites, etc).
 */

/*%
 * Function type for predicate functions that compare a database RR 'db_rr'
 * against an update RR 'update_rr'.
 */
typedef isc_boolean_t rr_predicate(dns_rdata_t *update_rr, dns_rdata_t *db_rr);

/*%
 * Helper function for rrset_exists().
 */
static isc_result_t
rrset_exists_action(void *data, rr_t *rr) {
	UNUSED(data);
	UNUSED(rr);
	return (ISC_R_EXISTS);
}

/*%
 * Utility macro for RR existence checking functions.
 *
 * If the variable 'result' has the value ISC_R_EXISTS or
 * ISC_R_SUCCESS, set *exists to ISC_TRUE or ISC_FALSE,
 * respectively, and return success.
 *
 * If 'result' has any other value, there was a failure.
 * Return the failure result code and do not set *exists.
 *
 * This would be more readable as "do { if ... } while(0)",
 * but that form generates tons of warnings on Solaris 2.6.
 */
#define RETURN_EXISTENCE_FLAG				\
	return ((result == ISC_R_EXISTS) ?		\
		(*exists = ISC_TRUE, ISC_R_SUCCESS) :	\
		((result == ISC_R_SUCCESS) ?		\
		 (*exists = ISC_FALSE, ISC_R_SUCCESS) :	\
		 result))

/*%
 * Set '*exists' to true iff an rrset of the given type exists,
 * to false otherwise.
 */
static isc_result_t
rrset_exists(dns_db_t *db, dns_dbversion_t *ver, dns_name_t *name,
	     dns_rdatatype_t type, dns_rdatatype_t covers,
	     isc_boolean_t *exists)
{
	isc_result_t result;
	result = foreach_rr(db, ver, name, type, covers,
			    rrset_exists_action, NULL);
	RETURN_EXISTENCE_FLAG;
}

/*%
 * Helper function for cname_incompatible_rrset_exists.
 */
static isc_result_t
cname_compatibility_action(void *data, dns_rdataset_t *rrset) {
	UNUSED(data);
	if (rrset->type != dns_rdatatype_cname &&
#ifdef ORIGINAL_ISC_CODE
#else
		/* Skip bulk host sourced records as they are obscured by
		 * CNAME rdatatype */
		rrset->bulkhost_sourced != ISC_TRUE &&
#endif
	    ! dns_rdatatype_isdnssec(rrset->type))
		return (ISC_R_EXISTS);
	return (ISC_R_SUCCESS);
}

/*%
 * Check whether there is an rrset incompatible with adding a CNAME RR,
 * i.e., anything but another CNAME (which can be replaced) or a
 * DNSSEC RR (which can coexist).
 *
 * If such an incompatible rrset exists, set '*exists' to ISC_TRUE.
 * Otherwise, set it to ISC_FALSE.
 */
static isc_result_t
cname_incompatible_rrset_exists(dns_db_t *db, dns_dbversion_t *ver,
				dns_name_t *name, isc_boolean_t *exists) {
	isc_result_t result;
	result = foreach_rrset(db, ver, name,
			       cname_compatibility_action, NULL);
	RETURN_EXISTENCE_FLAG;
}

/*%
 * Helper function for rr_count().
 */
static isc_result_t
count_rr_action(void *data, rr_t *rr) {
	int *countp = data;
	UNUSED(rr);
	(*countp)++;
	return (ISC_R_SUCCESS);
}

/*%
 * Count the number of RRs of 'type' belonging to 'name' in 'ver' of 'db'.
 */
static isc_result_t
rr_count(dns_db_t *db, dns_dbversion_t *ver, dns_name_t *name,
	 dns_rdatatype_t type, dns_rdatatype_t covers, int *countp)
{
	*countp = 0;
	return (foreach_rr(db, ver, name, type, covers,
			   count_rr_action, countp));
}

/*%
 * Context struct and helper function for name_exists().
 */

static isc_result_t
name_exists_action(void *data, dns_rdataset_t *rrset) {
	UNUSED(data);
	UNUSED(rrset);
	return (ISC_R_EXISTS);
}

/*%
 * Set '*exists' to true iff the given name exists, to false otherwise.
 */
static isc_result_t
name_exists(dns_db_t *db, dns_dbversion_t *ver, dns_name_t *name,
	    isc_boolean_t *exists)
{
	isc_result_t result;
	result = foreach_rrset(db, ver, name,
			       name_exists_action, NULL);
	RETURN_EXISTENCE_FLAG;
}

/*
 *	'ssu_check_t' is used to pass the arguments to
 *	dns_ssutable_checkrules() to the callback function
 *	ssu_checkrule().
 */
typedef struct {
	/* The ownername of the record to be updated. */
	dns_name_t *name;

	/* The signature's name if the request was signed. */
	dns_name_t *signer;

	/* The address of the client. */
	isc_netaddr_t *addr;

	/* Whether the request was sent via TCP. */
	isc_boolean_t tcp;

	/* The ssu table to check against. */
	dns_ssutable_t *table;

	/* the key used for TKEY requests */
	dst_key_t *key;
} ssu_check_t;

static isc_result_t
ssu_checkrule(void *data, dns_rdataset_t *rrset) {
	ssu_check_t *ssuinfo = data;
	isc_boolean_t result;

	/*
	 * If we're deleting all records, it's ok to delete RRSIG and NSEC even
	 * if we're normally not allowed to.
	 */
	if (rrset->type == dns_rdatatype_rrsig ||
	    rrset->type == dns_rdatatype_nsec)
		return (ISC_R_SUCCESS);
	result = dns_ssutable_checkrules2(ssuinfo->table, ssuinfo->signer,
					  ssuinfo->name, ssuinfo->addr,
					  ssuinfo->tcp, &ns_g_server->aclenv,
					  rrset->type, ssuinfo->key);
	return (result == ISC_TRUE ? ISC_R_SUCCESS : ISC_R_FAILURE);
}

static isc_boolean_t
ssu_checkall(dns_db_t *db, dns_dbversion_t *ver, dns_name_t *name,
	     dns_ssutable_t *ssutable, dns_name_t *signer,
	     isc_netaddr_t *addr, isc_boolean_t tcp, dst_key_t *key)
{
	isc_result_t result;
	ssu_check_t ssuinfo;

	ssuinfo.name = name;
	ssuinfo.table = ssutable;
	ssuinfo.signer = signer;
	ssuinfo.addr = addr;
	ssuinfo.tcp = tcp;
	ssuinfo.key = key;
	result = foreach_rrset(db, ver, name, ssu_checkrule, &ssuinfo);
	return (ISC_TF(result == ISC_R_SUCCESS));
}

#ifdef ORIGINAL_ISC_CODE
#else
/* Types of interest for updates from dhcpd. */
typedef struct {
  isc_boolean_t a;
  isc_boolean_t aaaa;
  isc_boolean_t dhcid;
  isc_boolean_t txt;   // Only TXT RRs with dhcid content
  isc_boolean_t other; // Includes non-dhcid TXT RRs
} dhcpd_types_t;

static isc_result_t ib_is_dhcid_txt(rr_t *rr, isc_boolean_t *is_dhcid_txt);

static isc_result_t
dhcpd_types_action(void *data, rr_t *rr) {
	if (data == NULL || rr == NULL) return ISC_R_UNEXPECTED;

	dhcpd_types_t *types = (dhcpd_types_t *)data;
	switch (rr->rdata.type) {
	case dns_rdatatype_a: types->a = ISC_TRUE; break;
	case dns_rdatatype_aaaa: types->aaaa = ISC_TRUE; break;
	case dns_rdatatype_dhcid: types->dhcid = ISC_TRUE; break;
	case dns_rdatatype_txt:
	  {
	    isc_boolean_t is_dhcid_txt = ISC_FALSE;
	    isc_result_t result = ib_is_dhcid_txt(rr, &is_dhcid_txt);
	    if (result != ISC_R_SUCCESS)
	      return (result);
	    // Only a TXT RR containing what looks like a dhcid is counted
	    // as a TXT. All other TXT RRs count as 'other'.
	    if (is_dhcid_txt)
	      types->txt = ISC_TRUE;
	    else
	      types->other = ISC_TRUE;
	  }
	  break;
	default: types->other = ISC_TRUE; break;
	}

	return ISC_R_SUCCESS;
}

/* Determine which types of interest exist in a single pass. */
static isc_result_t
rrset_dhcpd_types(dns_db_t *db, dns_dbversion_t *ver, dns_name_t *name,
		  dhcpd_types_t *types) {
	return foreach_rr(db, ver, name, dns_rdatatype_any, 0,
			  dhcpd_types_action, types);
}
#endif

/**************************************************************************/
/*
 * Checking of "RRset exists (value dependent)" prerequisites.
 *
 * In the RFC2136 section 3.2.5, this is the pseudocode involving
 * a variable called "temp", a mapping of <name, type> tuples to rrsets.
 *
 * Here, we represent the "temp" data structure as (non-minimal) "dns_diff_t"
 * where each tuple has op==DNS_DIFFOP_EXISTS.
 */


/*%
 * Append a tuple asserting the existence of the RR with
 * 'name' and 'rdata' to 'diff'.
 */
static isc_result_t
temp_append(dns_diff_t *diff, dns_name_t *name, dns_rdata_t *rdata) {
	isc_result_t result;
	dns_difftuple_t *tuple = NULL;

	REQUIRE(DNS_DIFF_VALID(diff));
	CHECK(dns_difftuple_create(diff->mctx, DNS_DIFFOP_EXISTS,
				   name, 0, rdata, &tuple));
	ISC_LIST_APPEND(diff->tuples, tuple, link);
 failure:
	return (result);
}

/*%
 * Compare two rdatasets represented as sorted lists of tuples.
 * All list elements must have the same owner name and type.
 * Return ISC_R_SUCCESS if the rdatasets are equal, rcode(dns_rcode_nxrrset)
 * if not.
 */
static isc_result_t
temp_check_rrset(dns_difftuple_t *a, dns_difftuple_t *b) {
	for (;;) {
		if (a == NULL || b == NULL)
			break;
		INSIST(a->op == DNS_DIFFOP_EXISTS &&
		       b->op == DNS_DIFFOP_EXISTS);
		INSIST(a->rdata.type == b->rdata.type);
		INSIST(dns_name_equal(&a->name, &b->name));
		if (dns_rdata_casecompare(&a->rdata, &b->rdata) != 0)
			return (DNS_R_NXRRSET);
		a = ISC_LIST_NEXT(a, link);
		b = ISC_LIST_NEXT(b, link);
	}
	if (a != NULL || b != NULL)
		return (DNS_R_NXRRSET);
	return (ISC_R_SUCCESS);
}

/*%
 * A comparison function defining the sorting order for the entries
 * in the "temp" data structure.  The major sort key is the owner name,
 * followed by the type and rdata.
 */
static int
temp_order(const void *av, const void *bv) {
	dns_difftuple_t const * const *ap = av;
	dns_difftuple_t const * const *bp = bv;
	dns_difftuple_t const *a = *ap;
	dns_difftuple_t const *b = *bp;
	int r;
	r = dns_name_compare(&a->name, &b->name);
	if (r != 0)
		return (r);
	r = (b->rdata.type - a->rdata.type);
	if (r != 0)
		return (r);
	r = dns_rdata_casecompare(&a->rdata, &b->rdata);
	return (r);
}

/*%
 * Check the "RRset exists (value dependent)" prerequisite information
 * in 'temp' against the contents of the database 'db'.
 *
 * Return ISC_R_SUCCESS if the prerequisites are satisfied,
 * rcode(dns_rcode_nxrrset) if not.
 *
 * 'temp' must be pre-sorted.
 */

static isc_result_t
temp_check(isc_mem_t *mctx, dns_diff_t *temp, dns_db_t *db,
	   dns_dbversion_t *ver, dns_name_t *tmpname, dns_rdatatype_t *typep)
{
	isc_result_t result;
	dns_name_t *name;
	dns_dbnode_t *node;
	dns_difftuple_t *t;
	dns_diff_t trash;

	dns_diff_init(mctx, &trash);

	/*
	 * For each name and type in the prerequisites,
	 * construct a sorted rdata list of the corresponding
	 * database contents, and compare the lists.
	 */
	t = ISC_LIST_HEAD(temp->tuples);
	while (t != NULL) {
		name = &t->name;
		(void)dns_name_copy(name, tmpname, NULL);
		*typep = t->rdata.type;

		/* A new unique name begins here. */
		node = NULL;
		result = dns_db_findnode(db, name, ISC_FALSE, &node);
		if (result == ISC_R_NOTFOUND) {
			dns_diff_clear(&trash);
			return (DNS_R_NXRRSET);
		}
		if (result != ISC_R_SUCCESS) {
			dns_diff_clear(&trash);
			return (result);
		}

		/* A new unique type begins here. */
		while (t != NULL && dns_name_equal(&t->name, name)) {
			dns_rdatatype_t type, covers;
			dns_rdataset_t rdataset;
			dns_diff_t d_rrs; /* Database RRs with
						this name and type */
			dns_diff_t u_rrs; /* Update RRs with
						this name and type */

			*typep = type = t->rdata.type;
			if (type == dns_rdatatype_rrsig ||
			    type == dns_rdatatype_sig)
				covers = dns_rdata_covers(&t->rdata);
			else if (type == dns_rdatatype_any) {
				dns_db_detachnode(db, &node);
				dns_diff_clear(&trash);
				return (DNS_R_NXRRSET);
			} else
				covers = 0;

			/*
			 * Collect all database RRs for this name and type
			 * onto d_rrs and sort them.
			 */
			dns_rdataset_init(&rdataset);
			result = dns_db_findrdataset(db, node, ver, type,
						     covers, (isc_stdtime_t) 0,
						     &rdataset, NULL);
			if (result != ISC_R_SUCCESS) {
				dns_db_detachnode(db, &node);
				dns_diff_clear(&trash);
				return (DNS_R_NXRRSET);
			}

			dns_diff_init(mctx, &d_rrs);
			dns_diff_init(mctx, &u_rrs);

			for (result = dns_rdataset_first(&rdataset);
			     result == ISC_R_SUCCESS;
			     result = dns_rdataset_next(&rdataset))
			{
				dns_rdata_t rdata = DNS_RDATA_INIT;
				dns_rdataset_current(&rdataset, &rdata);
				result = temp_append(&d_rrs, name, &rdata);
				if (result != ISC_R_SUCCESS)
					goto failure;
			}
			if (result != ISC_R_NOMORE)
				goto failure;
			result = dns_diff_sort(&d_rrs, temp_order);
			if (result != ISC_R_SUCCESS)
				goto failure;

			/*
			 * Collect all update RRs for this name and type
			 * onto u_rrs.  No need to sort them here -
			 * they are already sorted.
			 */
			while (t != NULL &&
			       dns_name_equal(&t->name, name) &&
			       t->rdata.type == type)
			{
				dns_difftuple_t *next =
					ISC_LIST_NEXT(t, link);
				ISC_LIST_UNLINK(temp->tuples, t, link);
				ISC_LIST_APPEND(u_rrs.tuples, t, link);
				t = next;
			}

			/* Compare the two sorted lists. */
			result = temp_check_rrset(ISC_LIST_HEAD(u_rrs.tuples),
						  ISC_LIST_HEAD(d_rrs.tuples));
			if (result != ISC_R_SUCCESS)
				goto failure;

			/*
			 * We are done with the tuples, but we can't free
			 * them yet because "name" still points into one
			 * of them.  Move them on a temporary list.
			 */
			ISC_LIST_APPENDLIST(trash.tuples, u_rrs.tuples, link);
			ISC_LIST_APPENDLIST(trash.tuples, d_rrs.tuples, link);
			dns_rdataset_disassociate(&rdataset);

			continue;

		    failure:
			dns_diff_clear(&d_rrs);
			dns_diff_clear(&u_rrs);
			dns_diff_clear(&trash);
			dns_rdataset_disassociate(&rdataset);
			dns_db_detachnode(db, &node);
			return (result);
		}

		dns_db_detachnode(db, &node);
	}

	dns_diff_clear(&trash);
	return (ISC_R_SUCCESS);
}

/**************************************************************************/
/*
 * Conditional deletion of RRs.
 */

/*%
 * Context structure for delete_if().
 */

typedef struct {
	rr_predicate *predicate;
	dns_db_t *db;
	dns_dbversion_t *ver;
	dns_diff_t *diff;
	dns_name_t *name;
	dns_rdata_t *update_rr;
#ifdef ORIGINAL_ISC_CODE
#else
	isc_boolean_t is_zdb;
#endif
} conditional_delete_ctx_t;

/*%
 * Predicate functions for delete_if().
 */

/*%
 * Return true iff 'db_rr' is neither a SOA nor an NS RR nor
 * an RRSIG nor an NSEC3PARAM nor a NSEC.
 */
static isc_boolean_t
type_not_soa_nor_ns_p(dns_rdata_t *update_rr, dns_rdata_t *db_rr) {
	UNUSED(update_rr);
	return ((db_rr->type != dns_rdatatype_soa &&
		 db_rr->type != dns_rdatatype_ns &&
		 db_rr->type != dns_rdatatype_nsec3param &&
		 db_rr->type != dns_rdatatype_rrsig &&
		 db_rr->type != dns_rdatatype_nsec) ?
		ISC_TRUE : ISC_FALSE);
}

/*%
 * Return true iff 'db_rr' is neither a RRSIG nor a NSEC.
 */
static isc_boolean_t
type_not_dnssec(dns_rdata_t *update_rr, dns_rdata_t *db_rr) {
	UNUSED(update_rr);
	return ((db_rr->type != dns_rdatatype_rrsig &&
		 db_rr->type != dns_rdatatype_nsec) ?
		ISC_TRUE : ISC_FALSE);
}

/*%
 * Return true always.
 */
static isc_boolean_t
true_p(dns_rdata_t *update_rr, dns_rdata_t *db_rr) {
	UNUSED(update_rr);
	UNUSED(db_rr);
	return (ISC_TRUE);
}

/*%
 * Return true iff the two RRs have identical rdata.
 */
static isc_boolean_t
rr_equal_p(dns_rdata_t *update_rr, dns_rdata_t *db_rr) {
	/*
	 * XXXRTH  This is not a problem, but we should consider creating
	 *         dns_rdata_equal() (that used dns_name_equal()), since it
	 *         would be faster.  Not a priority.
	 */
	return (dns_rdata_casecompare(update_rr, db_rr) == 0 ?
		ISC_TRUE : ISC_FALSE);
}

/*%
 * Return true iff 'update_rr' should replace 'db_rr' according
 * to the special RFC2136 rules for CNAME, SOA, and WKS records.
 *
 * RFC2136 does not mention NSEC or DNAME, but multiple NSECs or DNAMEs
 * make little sense, so we replace those, too.
 *
 * Additionally replace RRSIG that have been generated by the same key
 * for the same type.  This simplifies refreshing a offline KSK by not
 * requiring that the old RRSIG be deleted.  It also simplifies key
 * rollover by only requiring that the new RRSIG be added.
 */
static isc_boolean_t
replaces_p(dns_rdata_t *update_rr, dns_rdata_t *db_rr) {
	dns_rdata_rrsig_t updatesig, dbsig;
	isc_result_t result;

	if (db_rr->type != update_rr->type)
		return (ISC_FALSE);
	if (db_rr->type == dns_rdatatype_cname)
		return (ISC_TRUE);
	if (db_rr->type == dns_rdatatype_dname)
		return (ISC_TRUE);
	if (db_rr->type == dns_rdatatype_soa)
		return (ISC_TRUE);
	if (db_rr->type == dns_rdatatype_nsec)
		return (ISC_TRUE);
	if (db_rr->type == dns_rdatatype_rrsig) {
		/*
		 * Replace existing RRSIG with the same keyid,
		 * covered and algorithm.
		 */
		result = dns_rdata_tostruct(db_rr, &dbsig, NULL);
		RUNTIME_CHECK(result == ISC_R_SUCCESS);
		result = dns_rdata_tostruct(update_rr, &updatesig, NULL);
		RUNTIME_CHECK(result == ISC_R_SUCCESS);
		if (dbsig.keyid == updatesig.keyid &&
		    dbsig.covered == updatesig.covered &&
		    dbsig.algorithm == updatesig.algorithm)
			return (ISC_TRUE);
	}
	if (db_rr->type == dns_rdatatype_wks) {
		/*
		 * Compare the address and protocol fields only.  These
		 * form the first five bytes of the RR data.  Do a
		 * raw binary comparison; unpacking the WKS RRs using
		 * dns_rdata_tostruct() might be cleaner in some ways.
		 */
		INSIST(db_rr->length >= 5 && update_rr->length >= 5);
		return (memcmp(db_rr->data, update_rr->data, 5) == 0 ?
			ISC_TRUE : ISC_FALSE);
	}

	if (db_rr->type == dns_rdatatype_nsec3param) {
		if (db_rr->length != update_rr->length)
			return (ISC_FALSE);
		INSIST(db_rr->length >= 4 && update_rr->length >= 4);
		/*
		 * Replace NSEC3PARAM records that only differ by the
		 * flags field.
		 */
		if (db_rr->data[0] == update_rr->data[0] &&
		    memcmp(db_rr->data+2, update_rr->data+2,
			   update_rr->length - 2) == 0)
			return (ISC_TRUE);
	}
	return (ISC_FALSE);
}

/*%
 * Internal helper function for delete_if().
 */
static isc_result_t
delete_if_action(void *data, rr_t *rr) {
	conditional_delete_ctx_t *ctx = data;
	if ((*ctx->predicate)(ctx->update_rr, &rr->rdata)) {
		isc_result_t result;
		result = update_one_rr(ctx->db, ctx->ver, ctx->diff,
				       DNS_DIFFOP_DEL, ctx->name,
				       rr->ttl, &rr->rdata);
#ifdef ORIGINAL_ISC_CODE
#else
		if (result == ISC_R_SUCCESS)
			rr->updated += 1;
#endif
		return (result);
	} else {
		return (ISC_R_SUCCESS);
	}
}

/*%
 * Conditionally delete RRs.  Apply 'predicate' to the RRs
 * specified by 'db', 'ver', 'name', and 'type' (which can
 * be dns_rdatatype_any to match any type).  Delete those
 * RRs for which the predicate returns true, and log the
 * deletions in 'diff'.
 */
static isc_result_t
delete_if(rr_predicate *predicate, dns_db_t *db, dns_dbversion_t *ver,
	  dns_name_t *name, dns_rdatatype_t type, dns_rdatatype_t covers,
#ifdef ORIGINAL_ISC_CODE
	  dns_rdata_t *update_rr, dns_diff_t *diff)
#else
	  dns_rdata_t *update_rr, dns_diff_t *diff,
	  dns_zone_t *zone)
#endif
{
	conditional_delete_ctx_t ctx;
	ctx.predicate = predicate;
	ctx.db = db;
	ctx.ver = ver;
	ctx.diff = diff;
	ctx.name = name;
	ctx.update_rr = update_rr;
#ifdef ORIGINAL_ISC_CODE
#else
	ctx.is_zdb = (zone && dns_zone_is_zdb(zone));
#endif
	return (foreach_rr(db, ver, name, type, covers,
			   delete_if_action, &ctx));
}

#ifdef ORIGINAL_ISC_CODE
#else
static isc_boolean_t
ib_delete_is_zdb(void *data) {
	conditional_delete_ctx_t *ctx = data;
	if (ctx && ctx->is_zdb)
		return (ISC_TRUE);
	else
		return (ISC_FALSE);
}

// Generate a delete and an add of the specified RR data,
// changing the TTL from old to new.
static isc_result_t
ib_change_ttl(void *data, dns_ttl_t oldttl, dns_ttl_t newttl,
	      dns_rdata_t *rdata) {
	conditional_delete_ctx_t *ctx = data;
	isc_result_t result;

	result = update_one_rr(ctx->db, ctx->ver, ctx->diff,
			       DNS_DIFFOP_DEL, ctx->name,
			       oldttl, rdata);
	if (result == ISC_R_SUCCESS) {
		result = update_one_rr(ctx->db, ctx->ver, ctx->diff,
				       DNS_DIFFOP_ADD, ctx->name,
				       newttl, rdata);
	}

	return (result);
}
#endif

/**************************************************************************/

static isc_result_t
add_rr_prepare_action(void *data, rr_t *rr) {
	isc_result_t result = ISC_R_SUCCESS;
	add_rr_prepare_ctx_t *ctx = data;
	dns_difftuple_t *tuple = NULL;
#ifdef ORIGINAL_ISC_CODE
	isc_boolean_t equal, case_equal, ttl_equal;

	/*
	 * Are the new and old cases equal?
	 */
	case_equal = dns_name_caseequal(ctx->name, ctx->oldname);
#else
	isc_boolean_t equal, ttl_equal;
#endif

	/*
	 * Are the ttl's equal?
	 */
	ttl_equal = rr->ttl == ctx->update_rr_ttl;

	/*
	 * If the update RR is a "duplicate" of a existing RR,
	 * the update should be silently ignored.
	 */
	equal = ISC_TF(dns_rdata_casecompare(&rr->rdata, ctx->update_rr) == 0);
#ifdef ORIGINAL_ISC_CODE
	if (equal && case_equal && ttl_equal) {
#else
	if (equal && ttl_equal) {
#endif
		ctx->ignore_add = ISC_TRUE;
		return (ISC_R_SUCCESS);
	}

	/*
	 * If this RR is "equal" to the update RR, it should
	 * be deleted before the update RR is added.
	 */
	if (replaces_p(ctx->update_rr, &rr->rdata)) {
		CHECK(dns_difftuple_create(ctx->del_diff.mctx, DNS_DIFFOP_DEL,
					   ctx->oldname, rr->ttl, &rr->rdata,
					   &tuple));
		dns_diff_append(&ctx->del_diff, &tuple);
		return (ISC_R_SUCCESS);
	}

	/*
	 * If this RR differs in TTL or case from the update RR,
	 * its TTL and case must be adjusted.
	 */
#ifdef ORIGINAL_ISC_CODE
	if (!ttl_equal || !case_equal) {
#else
	if (!ttl_equal) {
#endif
		CHECK(dns_difftuple_create(ctx->del_diff.mctx, DNS_DIFFOP_DEL,
					   ctx->oldname, rr->ttl, &rr->rdata,
					   &tuple));
#ifdef ORIGINAL_ISC_CODE
#else
		tuple->is_ttl_delete = 1;
#endif
		dns_diff_append(&ctx->del_diff, &tuple);
		if (!equal) {
			CHECK(dns_difftuple_create(ctx->add_diff.mctx,
						   DNS_DIFFOP_ADD, ctx->name,
						   ctx->update_rr_ttl,
						   &rr->rdata, &tuple));
			dns_diff_append(&ctx->add_diff, &tuple);
		}
	}
 failure:
	return (result);
}

/**************************************************************************/
/*
 * Miscellaneous subroutines.
 */

/*%
 * Extract a single update RR from 'section' of dynamic update message
 * 'msg', with consistency checking.
 *
 * Stores the owner name, rdata, and TTL of the update RR at 'name',
 * 'rdata', and 'ttl', respectively.
 */
static void
get_current_rr(dns_message_t *msg, dns_section_t section,
	       dns_rdataclass_t zoneclass, dns_name_t **name,
	       dns_rdata_t *rdata, dns_rdatatype_t *covers,
	       dns_ttl_t *ttl, dns_rdataclass_t *update_class)
{
	dns_rdataset_t *rdataset;
	isc_result_t result;
	dns_message_currentname(msg, section, name);
	rdataset = ISC_LIST_HEAD((*name)->list);
	INSIST(rdataset != NULL);
	INSIST(ISC_LIST_NEXT(rdataset, link) == NULL);
	*covers = rdataset->covers;
	*ttl = rdataset->ttl;
	result = dns_rdataset_first(rdataset);
	INSIST(result == ISC_R_SUCCESS);
	dns_rdataset_current(rdataset, rdata);
	INSIST(dns_rdataset_next(rdataset) == ISC_R_NOMORE);
	*update_class = rdata->rdclass;
	rdata->rdclass = zoneclass;
}

/*%
 * Increment the SOA serial number of database 'db', version 'ver'.
 * Replace the SOA record in the database, and log the
 * change in 'diff'.
 */

	/*
	 * XXXRTH  Failures in this routine will be worth logging, when
	 *         we have a logging system.  Failure to find the zonename
	 *	   or the SOA rdataset warrant at least an UNEXPECTED_ERROR().
	 */

static isc_result_t
update_soa_serial(dns_db_t *db, dns_dbversion_t *ver, dns_diff_t *diff,
		  isc_mem_t *mctx, dns_updatemethod_t method)
{
	dns_difftuple_t *deltuple = NULL;
	dns_difftuple_t *addtuple = NULL;
	isc_uint32_t serial;
	isc_result_t result;

	CHECK(dns_db_createsoatuple(db, ver, mctx, DNS_DIFFOP_DEL, &deltuple));
	CHECK(dns_difftuple_copy(deltuple, &addtuple));
	addtuple->op = DNS_DIFFOP_ADD;

	serial = dns_soa_getserial(&addtuple->rdata);
	serial = dns_update_soaserial(serial, method);
	dns_soa_setserial(serial, &addtuple->rdata);
	CHECK(do_one_tuple(&deltuple, db, ver, diff));
	CHECK(do_one_tuple(&addtuple, db, ver, diff));
	result = ISC_R_SUCCESS;

 failure:
	if (addtuple != NULL)
		dns_difftuple_free(&addtuple);
	if (deltuple != NULL)
		dns_difftuple_free(&deltuple);
	return (result);
}

/*%
 * Check that the new SOA record at 'update_rdata' does not
 * illegally cause the SOA serial number to decrease or stay
 * unchanged relative to the existing SOA in 'db'.
 *
 * Sets '*ok' to ISC_TRUE if the update is legal, ISC_FALSE if not.
 *
 * William King points out that RFC2136 is inconsistent about
 * the case where the serial number stays unchanged:
 *
 *   section 3.4.2.2 requires a server to ignore a SOA update request
 *   if the serial number on the update SOA is less_than_or_equal to
 *   the zone SOA serial.
 *
 *   section 3.6 requires a server to ignore a SOA update request if
 *   the serial is less_than the zone SOA serial.
 *
 * Paul says 3.4.2.2 is correct.
 *
 */
static isc_result_t
check_soa_increment(dns_db_t *db, dns_dbversion_t *ver,
		    dns_rdata_t *update_rdata, isc_boolean_t *ok)
{
	isc_uint32_t db_serial;
	isc_uint32_t update_serial;
	isc_result_t result;

	update_serial = dns_soa_getserial(update_rdata);

	result = dns_db_getsoaserial(db, ver, &db_serial);
	if (result != ISC_R_SUCCESS)
		return (result);

	if (DNS_SERIAL_GE(db_serial, update_serial)) {
		*ok = ISC_FALSE;
	} else {
		*ok = ISC_TRUE;
	}

	return (ISC_R_SUCCESS);

}

/**************************************************************************/
/*%
 * The actual update code in all its glory.  We try to follow
 * the RFC2136 pseudocode as closely as possible.
 */

static isc_result_t
send_update_event(ns_client_t *client, dns_zone_t *zone) {
	isc_result_t result = ISC_R_SUCCESS;
	update_event_t *event = NULL;
	isc_task_t *zonetask = NULL;
	ns_client_t *evclient;

#ifdef ORIGINAL_ISC_CODE
#else
	// If we're in the process of shutting down, it's possible that the
	// next task for this client structure, before the update_action()
	// we just queued, will be client_shutdown(), which will set the
	// client 'newstate' to NS_CLIENTSTATE_FREED, and then call exit_check(),
	// which promptly detaches the view. Hence, when update_action()
	// executes, client->view is NULL, and chaos ensues.
	//
	// In order to avoid that, we set a flag that tells exit_check()
	// that it mustn't touch client->view. We later unset that flag in
	// updatedone_action().
	(void) pthread_mutex_lock (&client->view_lock);
	if (client->view != NULL)
	  {
	    client->retain_view = 1;
	  }
	else
	  {
	    // Oops, exit_check() got in before us. There's no use actually
	    // queueing the update_action() task (it will be fatally inconvenienced
	    // by not having a valid client->view), so return an error.
	    (void) pthread_mutex_unlock (&client->view_lock);
	    return (ISC_R_SHUTTINGDOWN);
	  }
	(void) pthread_mutex_unlock (&client->view_lock);
#endif

	event = (update_event_t *)
		isc_event_allocate(client->mctx, client, DNS_EVENT_UPDATE,
				   update_action, NULL, sizeof(*event));
	if (event == NULL)
		FAIL(ISC_R_NOMEMORY);
	event->zone = zone;
	event->result = ISC_R_SUCCESS;

	evclient = NULL;
	ns_client_attach(client, &evclient);
	INSIST(client->nupdates == 0);
	client->nupdates++;
	event->ev_arg = evclient;

	dns_zone_gettask(zone, &zonetask);
	isc_task_send(zonetask, ISC_EVENT_PTR(&event));

 failure:
#ifdef ORIGINAL_ISC_CODE
#else
	if (result != ISC_R_SUCCESS)
	  {
	    // Don't need to protect the client->view pointer
	    (void) pthread_mutex_lock (&client->view_lock);
	    client->retain_view = 0;
	    (void) pthread_mutex_unlock (&client->view_lock);
	  }
#endif
	if (event != NULL)
		isc_event_free(ISC_EVENT_PTR(&event));
	return (result);
}

static void
respond(ns_client_t *client, isc_result_t result) {
	isc_result_t msg_result;

	msg_result = dns_message_reply(client->message, ISC_TRUE);
	if (msg_result != ISC_R_SUCCESS)
		goto msg_failure;
	client->message->rcode = dns_result_torcode(result);

	ns_client_send(client);
	return;

 msg_failure:
	isc_log_write(ns_g_lctx, NS_LOGCATEGORY_UPDATE, NS_LOGMODULE_UPDATE,
		      ISC_LOG_ERROR,
		      "could not create update response message: %s",
		      isc_result_totext(msg_result));
	ns_client_next(client, msg_result);
}

void
ns_update_start(ns_client_t *client, isc_result_t sigresult) {
	dns_message_t *request = client->message;
	isc_result_t result;
	dns_name_t *zonename;
	dns_rdataset_t *zone_rdataset;
	dns_zone_t *zone = NULL, *raw = NULL;

#ifdef ORIGINAL_ISC_CODE
#else
	// In passive HA mode, ignore any update attempts.
	if (!infoblox_get_is_ha_active())
		FAILC(DNS_R_SERVFAIL, "update ignored in passive HA mode");
#endif

	/*
	 * Interpret the zone section.
	 */
	result = dns_message_firstname(request, DNS_SECTION_ZONE);
	if (result != ISC_R_SUCCESS)
		FAILC(DNS_R_FORMERR, "update zone section empty");

	/*
	 * The zone section must contain exactly one "question", and
	 * it must be of type SOA.
	 */
	zonename = NULL;
	dns_message_currentname(request, DNS_SECTION_ZONE, &zonename);
	zone_rdataset = ISC_LIST_HEAD(zonename->list);
	if (zone_rdataset->type != dns_rdatatype_soa)
		FAILC(DNS_R_FORMERR,
		      "update zone section contains non-SOA");
	if (ISC_LIST_NEXT(zone_rdataset, link) != NULL)
		FAILC(DNS_R_FORMERR,
		      "update zone section contains multiple RRs");

	/* The zone section must have exactly one name. */
	result = dns_message_nextname(request, DNS_SECTION_ZONE);
	if (result != ISC_R_NOMORE)
		FAILC(DNS_R_FORMERR,
		      "update zone section contains multiple RRs");

	result = dns_zt_find(client->view->zonetable, zonename, 0, NULL,
			     &zone);
	if (result != ISC_R_SUCCESS)
		FAILC(DNS_R_NOTAUTH, "not authoritative for update zone");

#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * If we are a primary (master or dlz) server of the zone and we've
	 * received an update request for it before we complete the initial
	 * load attempt, we should drop the query, prioritizing the load of
	 * the zone.
	 */
	if (!infoblox_zone_isdbinitialized(zone) &&
	    (dns_zone_gettype(zone) == dns_zone_master ||
	     dns_zone_gettype(zone) == dns_zone_dlz))
	{
		infoblox_dns_zone_make_last_requested(zone, "update");
		dns_zone_detach(&zone);

		/* See query_find() for failing with DROP */
		update_log(client, zone, ISC_LOG_ERROR,
			   "update failed: dropped update as initial "
			   "zone DB load has not yet been attempted");
		ns_client_next(client, DNS_R_DROP);
		return;
	}
#endif

	/*
	 * If there is a raw (unsigned) zone associated with this
	 * zone then it processes the UPDATE request.
	 */
	dns_zone_getraw(zone, &raw);
	if (raw != NULL) {
		dns_zone_detach(&zone);
		dns_zone_attach(raw, &zone);
		dns_zone_detach(&raw);
	}

	switch(dns_zone_gettype(zone)) {
	case dns_zone_master:
	case dns_zone_dlz:
		/*
		 * We can now fail due to a bad signature as we now know
		 * that we are the master.
		 */
		if (sigresult != ISC_R_SUCCESS)
			FAIL(sigresult);
		CHECK(send_update_event(client, zone));
		break;
	case dns_zone_slave:
		CHECK(checkupdateacl(client, dns_zone_getforwardacl(zone),
				     "update forwarding", zonename, ISC_TRUE,
				     ISC_FALSE));
		CHECK(send_forward_event(client, zone));
		break;
	default:
		FAILC(DNS_R_NOTAUTH, "not authoritative for update zone");
	}
	return;

 failure:
	if (result == DNS_R_REFUSED) {
		INSIST(dns_zone_gettype(zone) == dns_zone_slave);
		inc_stats(zone, dns_nsstatscounter_updaterej);
	}
	/*
	 * We failed without having sent an update event to the zone.
	 * We are still in the client task context, so we can
	 * simply give an error response without switching tasks.
	 */
	respond(client, result);
	if (zone != NULL)
		dns_zone_detach(&zone);
}

/*%
 * DS records are not allowed to exist without corresponding NS records,
 * RFC 3658, 2.2 Protocol Change,
 * "DS RRsets MUST NOT appear at non-delegation points or at a zone's apex".
 */

static isc_result_t
remove_orphaned_ds(dns_db_t *db, dns_dbversion_t *newver, dns_diff_t *diff) {
	isc_result_t result;
	isc_boolean_t ns_exists;
	dns_difftuple_t *tupple;
	dns_diff_t temp_diff;

	dns_diff_init(diff->mctx, &temp_diff);

	for (tupple = ISC_LIST_HEAD(diff->tuples);
	     tupple != NULL;
	     tupple = ISC_LIST_NEXT(tupple, link)) {
		if (!((tupple->op == DNS_DIFFOP_DEL &&
		       tupple->rdata.type == dns_rdatatype_ns) ||
		      (tupple->op == DNS_DIFFOP_ADD &&
		       tupple->rdata.type == dns_rdatatype_ds)))
			continue;
		CHECK(rrset_exists(db, newver, &tupple->name,
				   dns_rdatatype_ns, 0, &ns_exists));
		if (ns_exists &&
		    !dns_name_equal(&tupple->name, dns_db_origin(db)))
			continue;
		CHECK(delete_if(true_p, db, newver, &tupple->name,
#ifdef ORIGINAL_ISC_CODE
				dns_rdatatype_ds, 0, NULL, &temp_diff));
#else
				dns_rdatatype_ds, 0, NULL, &temp_diff, NULL));
#endif
	}
	result = ISC_R_SUCCESS;

 failure:
	for (tupple = ISC_LIST_HEAD(temp_diff.tuples);
	     tupple != NULL;
	     tupple = ISC_LIST_HEAD(temp_diff.tuples)) {
		ISC_LIST_UNLINK(temp_diff.tuples, tupple, link);
		dns_diff_appendminimal(diff, &tupple);
	}
	return (result);
}

/*
 * This implements the post load integrity checks for mx records.
 */
static isc_result_t
check_mx(ns_client_t *client, dns_zone_t *zone,
	 dns_db_t *db, dns_dbversion_t *newver, dns_diff_t *diff)
{
	char tmp[sizeof("xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:***************.")];
	char ownerbuf[DNS_NAME_FORMATSIZE];
	char namebuf[DNS_NAME_FORMATSIZE];
	char altbuf[DNS_NAME_FORMATSIZE];
	dns_difftuple_t *t;
	dns_fixedname_t fixed;
	dns_name_t *foundname;
	dns_rdata_mx_t mx;
	dns_rdata_t rdata;
	isc_boolean_t ok = ISC_TRUE;
	isc_boolean_t isaddress;
	isc_result_t result;
	struct in6_addr addr6;
	struct in_addr addr;
	unsigned int options;

	dns_fixedname_init(&fixed);
	foundname = dns_fixedname_name(&fixed);
	dns_rdata_init(&rdata);
	options = dns_zone_getoptions(zone);

	for (t = ISC_LIST_HEAD(diff->tuples);
	     t != NULL;
	     t = ISC_LIST_NEXT(t, link)) {
		if (t->op != DNS_DIFFOP_ADD ||
		    t->rdata.type != dns_rdatatype_mx)
			continue;

		result = dns_rdata_tostruct(&t->rdata, &mx, NULL);
		RUNTIME_CHECK(result == ISC_R_SUCCESS);
		/*
		 * Check if we will error out if we attempt to reload the
		 * zone.
		 */
		dns_name_format(&mx.mx, namebuf, sizeof(namebuf));
		dns_name_format(&t->name, ownerbuf, sizeof(ownerbuf));
		isaddress = ISC_FALSE;
		if ((options & DNS_ZONEOPT_CHECKMX) != 0 &&
		    strlcpy(tmp, namebuf, sizeof(tmp)) < sizeof(tmp)) {
			if (tmp[strlen(tmp) - 1] == '.')
				tmp[strlen(tmp) - 1] = '\0';
			if (inet_aton(tmp, &addr) == 1 ||
			    inet_pton(AF_INET6, tmp, &addr6) == 1)
				isaddress = ISC_TRUE;
		}

		if (isaddress && (options & DNS_ZONEOPT_CHECKMXFAIL) != 0) {
			update_log(client, zone, ISC_LOG_ERROR,
				   "%s/MX: '%s': %s",
				   ownerbuf, namebuf,
				   dns_result_totext(DNS_R_MXISADDRESS));
			ok = ISC_FALSE;
		} else if (isaddress) {
			update_log(client, zone, ISC_LOG_WARNING,
				   "%s/MX: warning: '%s': %s",
				   ownerbuf, namebuf,
				   dns_result_totext(DNS_R_MXISADDRESS));
		}

		/*
		 * Check zone integrity checks.
		 */
		if ((options & DNS_ZONEOPT_CHECKINTEGRITY) == 0)
			continue;
		result = dns_db_find(db, &mx.mx, newver, dns_rdatatype_a,
				     0, 0, NULL, foundname, NULL, NULL);
		if (result == ISC_R_SUCCESS)
			continue;

		if (result == DNS_R_NXRRSET) {
			result = dns_db_find(db, &mx.mx, newver,
					     dns_rdatatype_aaaa,
					     0, 0, NULL, foundname,
					     NULL, NULL);
			if (result == ISC_R_SUCCESS)
				continue;
		}

		if (result == DNS_R_NXRRSET || result == DNS_R_NXDOMAIN) {
			update_log(client, zone, ISC_LOG_ERROR,
				   "%s/MX '%s' has no address records "
				   "(A or AAAA)", ownerbuf, namebuf);
			ok = ISC_FALSE;
		} else if (result == DNS_R_CNAME) {
			update_log(client, zone, ISC_LOG_ERROR,
				   "%s/MX '%s' is a CNAME (illegal)",
				   ownerbuf, namebuf);
			ok = ISC_FALSE;
		} else if (result == DNS_R_DNAME) {
			dns_name_format(foundname, altbuf, sizeof altbuf);
			update_log(client, zone, ISC_LOG_ERROR,
				   "%s/MX '%s' is below a DNAME '%s' (illegal)",
				   ownerbuf, namebuf, altbuf);
			ok = ISC_FALSE;
		}
	}
	return (ok ? ISC_R_SUCCESS : DNS_R_REFUSED);
}

static isc_result_t
rr_exists(dns_db_t *db, dns_dbversion_t *ver, dns_name_t *name,
	  const dns_rdata_t *rdata, isc_boolean_t *flag)
{
	dns_rdataset_t rdataset;
	dns_dbnode_t *node = NULL;
	isc_result_t result;

	dns_rdataset_init(&rdataset);
	if (rdata->type == dns_rdatatype_nsec3)
		CHECK(dns_db_findnsec3node(db, name, ISC_FALSE, &node));
	else
		CHECK(dns_db_findnode(db, name, ISC_FALSE, &node));
	result = dns_db_findrdataset(db, node, ver, rdata->type, 0,
				     (isc_stdtime_t) 0, &rdataset, NULL);
	if (result == ISC_R_NOTFOUND) {
		*flag = ISC_FALSE;
		result = ISC_R_SUCCESS;
		goto failure;
	}

	for (result = dns_rdataset_first(&rdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(&rdataset)) {
		dns_rdata_t myrdata = DNS_RDATA_INIT;
		dns_rdataset_current(&rdataset, &myrdata);
		if (!dns_rdata_casecompare(&myrdata, rdata))
			break;
	}
	dns_rdataset_disassociate(&rdataset);
	if (result == ISC_R_SUCCESS) {
		*flag = ISC_TRUE;
	} else if (result == ISC_R_NOMORE) {
		*flag = ISC_FALSE;
		result = ISC_R_SUCCESS;
	}

 failure:
	if (node != NULL)
		dns_db_detachnode(db, &node);
	return (result);
}

static isc_result_t
get_iterations(dns_db_t *db, dns_dbversion_t *ver, dns_rdatatype_t privatetype,
	       unsigned int *iterationsp)
{
	dns_dbnode_t *node = NULL;
	dns_rdata_nsec3param_t nsec3param;
	dns_rdataset_t rdataset;
	isc_result_t result;
	unsigned int iterations = 0;

	dns_rdataset_init(&rdataset);

	result = dns_db_getoriginnode(db, &node);
	if (result != ISC_R_SUCCESS)
		return (result);
	result = dns_db_findrdataset(db, node, ver, dns_rdatatype_nsec3param,
				     0, (isc_stdtime_t) 0, &rdataset, NULL);
	if (result == ISC_R_NOTFOUND)
		goto try_private;
	if (result != ISC_R_SUCCESS)
		goto failure;

	for (result = dns_rdataset_first(&rdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(&rdataset)) {
		dns_rdata_t rdata = DNS_RDATA_INIT;
		dns_rdataset_current(&rdataset, &rdata);
		CHECK(dns_rdata_tostruct(&rdata, &nsec3param, NULL));
		if ((nsec3param.flags & DNS_NSEC3FLAG_REMOVE) != 0)
			continue;
		if (nsec3param.iterations > iterations)
			iterations = nsec3param.iterations;
	}
	if (result != ISC_R_NOMORE)
		goto failure;

	dns_rdataset_disassociate(&rdataset);

 try_private:
	if (privatetype == 0)
		goto success;

	result = dns_db_findrdataset(db, node, ver, privatetype,
				     0, (isc_stdtime_t) 0, &rdataset, NULL);
	if (result == ISC_R_NOTFOUND)
		goto success;
	if (result != ISC_R_SUCCESS)
		goto failure;

	for (result = dns_rdataset_first(&rdataset);
	     result == ISC_R_SUCCESS;
	     result = dns_rdataset_next(&rdataset)) {
		unsigned char buf[DNS_NSEC3PARAM_BUFFERSIZE];
		dns_rdata_t private = DNS_RDATA_INIT;
		dns_rdata_t rdata = DNS_RDATA_INIT;

		dns_rdataset_current(&rdataset, &rdata);
		if (!dns_nsec3param_fromprivate(&private, &rdata,
						buf, sizeof(buf)))
			continue;
		CHECK(dns_rdata_tostruct(&rdata, &nsec3param, NULL));
		if ((nsec3param.flags & DNS_NSEC3FLAG_REMOVE) != 0)
			continue;
		if (nsec3param.iterations > iterations)
			iterations = nsec3param.iterations;
	}
	if (result != ISC_R_NOMORE)
		goto failure;

 success:
	*iterationsp = iterations;
	result = ISC_R_SUCCESS;

 failure:
	if (node != NULL)
		dns_db_detachnode(db, &node);
	if (dns_rdataset_isassociated(&rdataset))
		dns_rdataset_disassociate(&rdataset);
	return (result);
}

/*
 * Prevent the zone entering a inconsistent state where
 * NSEC only DNSKEYs are present with NSEC3 chains.
 */
static isc_result_t
check_dnssec(ns_client_t *client, dns_zone_t *zone, dns_db_t *db,
	     dns_dbversion_t *ver, dns_diff_t *diff)
{
	dns_difftuple_t *tuple;
	isc_boolean_t nseconly = ISC_FALSE, nsec3 = ISC_FALSE;
	isc_result_t result;
	unsigned int iterations = 0, max;
	dns_rdatatype_t privatetype = dns_zone_getprivatetype(zone);

	/* Scan the tuples for an NSEC-only DNSKEY or an NSEC3PARAM */
	for (tuple = ISC_LIST_HEAD(diff->tuples);
	     tuple != NULL;
	     tuple = ISC_LIST_NEXT(tuple, link)) {
		if (tuple->op != DNS_DIFFOP_ADD)
			continue;

		if (tuple->rdata.type == dns_rdatatype_dnskey) {
			isc_uint8_t alg;
			alg = tuple->rdata.data[3];
			if (alg == DST_ALG_RSAMD5 || alg == DST_ALG_RSASHA1 ||
			    alg == DST_ALG_DSA || alg == DST_ALG_ECC) {
				nseconly = ISC_TRUE;
				break;
			}
		} else if (tuple->rdata.type == dns_rdatatype_nsec3param) {
			nsec3 = ISC_TRUE;
			break;
		}
	}

	/* Check existing DB for NSEC-only DNSKEY */
	if (!nseconly) {
		result = dns_nsec_nseconly(db, ver, &nseconly);

		/*
		 * An NSEC3PARAM update can proceed without a DNSKEY (it
		 * will trigger a delayed change), so we can ignore
		 * ISC_R_NOTFOUND here.
		 */
		if (result == ISC_R_NOTFOUND)
			result = ISC_R_SUCCESS;

		CHECK(result);
	}

	/* Check existing DB for NSEC3 */
	if (!nsec3)
		CHECK(dns_nsec3_activex(db, ver, ISC_FALSE,
					privatetype, &nsec3));

	/* Refuse to allow NSEC3 with NSEC-only keys */
	if (nseconly && nsec3) {
		update_log(client, zone, ISC_LOG_ERROR,
			   "NSEC only DNSKEYs and NSEC3 chains not allowed");
		result = DNS_R_REFUSED;
		goto failure;
	}

	/* Verify NSEC3 params */
	CHECK(get_iterations(db, ver, privatetype, &iterations));
	CHECK(dns_nsec3_maxiterations(db, ver, client->mctx, &max));
	if (max != 0 && iterations > max) {
		update_log(client, zone, ISC_LOG_ERROR,
			   "too many NSEC3 iterations (%u) for "
			   "weakest DNSKEY (%u)", iterations, max);
		result = DNS_R_REFUSED;
		goto failure;
	}

 failure:
	return (result);
}

/*
 * Delay NSEC3PARAM changes as they need to be applied to the whole zone.
 */
static isc_result_t
add_nsec3param_records(ns_client_t *client, dns_zone_t *zone, dns_db_t *db,
		       dns_dbversion_t *ver, dns_diff_t *diff)
{
	isc_result_t result = ISC_R_SUCCESS;
	dns_difftuple_t *tuple, *newtuple = NULL, *next;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	unsigned char buf[DNS_NSEC3PARAM_BUFFERSIZE + 1];
	dns_diff_t temp_diff;
	dns_diffop_t op;
	isc_boolean_t flag;
	dns_name_t *name = dns_zone_getorigin(zone);
	dns_rdatatype_t privatetype = dns_zone_getprivatetype(zone);
	isc_uint32_t ttl = 0;
	isc_boolean_t ttl_good = ISC_FALSE;

	update_log(client, zone, ISC_LOG_DEBUG(3),
		    "checking for NSEC3PARAM changes");

	dns_diff_init(diff->mctx, &temp_diff);

	/*
	 * Extract NSEC3PARAM tuples from list.
	 */
	for (tuple = ISC_LIST_HEAD(diff->tuples);
	     tuple != NULL;
	     tuple = next) {

		next = ISC_LIST_NEXT(tuple, link);

		if (tuple->rdata.type != dns_rdatatype_nsec3param ||
		    !dns_name_equal(name, &tuple->name))
			continue;
		ISC_LIST_UNLINK(diff->tuples, tuple, link);
		ISC_LIST_APPEND(temp_diff.tuples, tuple, link);
	}

	/*
	 * Extract TTL changes pairs, we don't need to convert these to
	 * delayed changes.
	 */
	for (tuple = ISC_LIST_HEAD(temp_diff.tuples);
	     tuple != NULL; tuple = next) {
		if (tuple->op == DNS_DIFFOP_ADD) {
			if (!ttl_good) {
				/*
				 * Any adds here will contain the final
				 * NSEC3PARAM RRset TTL.
				 */
				ttl = tuple->ttl;
				ttl_good = ISC_TRUE;
			}
			/*
			 * Walk the temp_diff list looking for the
			 * corresponding delete.
			 */
			next = ISC_LIST_HEAD(temp_diff.tuples);
			while (next != NULL) {
				unsigned char *next_data = next->rdata.data;
				unsigned char *tuple_data = tuple->rdata.data;
				if (next->op == DNS_DIFFOP_DEL &&
				    next->rdata.length == tuple->rdata.length &&
				    !memcmp(next_data, tuple_data,
					    next->rdata.length)) {
					ISC_LIST_UNLINK(temp_diff.tuples, next,
							link);
					ISC_LIST_APPEND(diff->tuples, next,
							link);
					break;
				}
				next = ISC_LIST_NEXT(next, link);
			}
			/*
			 * If we have not found a pair move onto the next
			 * tuple.
			 */
			if (next == NULL) {
				next = ISC_LIST_NEXT(tuple, link);
				continue;
			}
			/*
			 * Find the next tuple to be processed before
			 * unlinking then complete moving the pair to 'diff'.
			 */
			next = ISC_LIST_NEXT(tuple, link);
			ISC_LIST_UNLINK(temp_diff.tuples, tuple, link);
			ISC_LIST_APPEND(diff->tuples, tuple, link);
		} else
			next = ISC_LIST_NEXT(tuple, link);
	}

	/*
	 * Preserve any ongoing changes from a BIND 9.6.x upgrade.
	 *
	 * Any NSEC3PARAM records with flags other than OPTOUT named
	 * in managing and should not be touched so revert such changes
	 * taking into account any TTL change of the NSEC3PARAM RRset.
	 */
	for (tuple = ISC_LIST_HEAD(temp_diff.tuples);
	     tuple != NULL; tuple = next) {
		next = ISC_LIST_NEXT(tuple, link);
		if ((tuple->rdata.data[1] & ~DNS_NSEC3FLAG_OPTOUT) != 0) {
			/*
			 * If we havn't had any adds then the tuple->ttl must
			 * be the original ttl and should be used for any
			 * future changes.
			 */
			if (!ttl_good) {
				ttl = tuple->ttl;
				ttl_good = ISC_TRUE;
			}
			op = (tuple->op == DNS_DIFFOP_DEL) ?
			     DNS_DIFFOP_ADD : DNS_DIFFOP_DEL;
			CHECK(dns_difftuple_create(diff->mctx, op, name,
						   ttl, &tuple->rdata,
						   &newtuple));
			CHECK(do_one_tuple(&newtuple, db, ver, diff));
			ISC_LIST_UNLINK(temp_diff.tuples, tuple, link);
			dns_diff_appendminimal(diff, &tuple);
		}
	}

	/*
	 * We now have just the actual changes to the NSEC3PARAM RRset.
	 * Convert the adds to delayed adds and the deletions into delayed
	 * deletions.
	 */
	for (tuple = ISC_LIST_HEAD(temp_diff.tuples);
	     tuple != NULL; tuple = next) {
		/*
		 * If we havn't had any adds then the tuple->ttl must be the
		 * original ttl and should be used for any future changes.
		 */
		if (!ttl_good) {
			ttl = tuple->ttl;
			ttl_good = ISC_TRUE;
		}
		if (tuple->op == DNS_DIFFOP_ADD) {
			isc_boolean_t nseconly = ISC_FALSE;

			/*
			 * Look for any deletes which match this ADD ignoring
			 * flags.  We don't need to explictly remove them as
			 * they will be removed a side effect of processing
			 * the add.
			 */
			next = ISC_LIST_HEAD(temp_diff.tuples);
			while (next != NULL) {
				unsigned char *next_data = next->rdata.data;
				unsigned char *tuple_data = tuple->rdata.data;
				if (next->op != DNS_DIFFOP_DEL ||
				    next->rdata.length != tuple->rdata.length ||
				    next_data[0] != tuple_data[0] ||
				    next_data[2] != tuple_data[2] ||
				    next_data[3] != tuple_data[3] ||
				    memcmp(next_data + 4, tuple_data + 4,
					   tuple->rdata.length - 4)) {
					next = ISC_LIST_NEXT(next, link);
					continue;
				}
				ISC_LIST_UNLINK(temp_diff.tuples, next, link);
				ISC_LIST_APPEND(diff->tuples, next, link);
				next = ISC_LIST_HEAD(temp_diff.tuples);
			}

			/*
			 * Create a private-type record to signal that
			 * we want a delayed NSEC3 chain add/delete
			 */
			dns_nsec3param_toprivate(&tuple->rdata, &rdata,
						 privatetype, buf, sizeof(buf));
			buf[2] |= DNS_NSEC3FLAG_CREATE;

			/*
			 * If the zone is not currently capable of
			 * supporting an NSEC3 chain, then we set the
			 * INITIAL flag to indicate that these parameters
			 * are to be used later.
			 */
			result = dns_nsec_nseconly(db, ver, &nseconly);
			if (result == ISC_R_NOTFOUND || nseconly)
				buf[2] |= DNS_NSEC3FLAG_INITIAL;

			/*
			 * See if this CREATE request already exists.
			 */
			CHECK(rr_exists(db, ver, name, &rdata, &flag));

			if (!flag) {
				CHECK(dns_difftuple_create(diff->mctx,
							   DNS_DIFFOP_ADD,
							   name, 0, &rdata,
							   &newtuple));
				CHECK(do_one_tuple(&newtuple, db, ver, diff));
			}

			/*
			 * Remove any existing CREATE request to add an
			 * otherwise indentical chain with a reversed
			 * OPTOUT state.
			 */
			buf[2] ^= DNS_NSEC3FLAG_OPTOUT;
			CHECK(rr_exists(db, ver, name, &rdata, &flag));

			if (flag) {
				CHECK(dns_difftuple_create(diff->mctx,
							   DNS_DIFFOP_DEL,
							   name, 0, &rdata,
							   &newtuple));
				CHECK(do_one_tuple(&newtuple, db, ver, diff));
			}

			/*
			 * Find the next tuple to be processed and remove the
			 * temporary add record.
			 */
			next = ISC_LIST_NEXT(tuple, link);
			CHECK(dns_difftuple_create(diff->mctx, DNS_DIFFOP_DEL,
						   name, ttl, &tuple->rdata,
						   &newtuple));
			CHECK(do_one_tuple(&newtuple, db, ver, diff));
			ISC_LIST_UNLINK(temp_diff.tuples, tuple, link);
			dns_diff_appendminimal(diff, &tuple);
			dns_rdata_reset(&rdata);
		} else
			next = ISC_LIST_NEXT(tuple, link);
	}

	for (tuple = ISC_LIST_HEAD(temp_diff.tuples);
	     tuple != NULL; tuple = next) {

		INSIST(ttl_good);

		next = ISC_LIST_NEXT(tuple, link);
		/*
		 * See if we already have a REMOVE request in progress.
		 */
		dns_nsec3param_toprivate(&tuple->rdata, &rdata, privatetype,
					 buf, sizeof(buf));

		buf[2] |= DNS_NSEC3FLAG_REMOVE | DNS_NSEC3FLAG_NONSEC;

		CHECK(rr_exists(db, ver, name, &rdata, &flag));
		if (!flag) {
			buf[2] &= ~DNS_NSEC3FLAG_NONSEC;
			CHECK(rr_exists(db, ver, name, &rdata, &flag));
		}

		if (!flag) {
			CHECK(dns_difftuple_create(diff->mctx, DNS_DIFFOP_ADD,
						   name, 0, &rdata, &newtuple));
			CHECK(do_one_tuple(&newtuple, db, ver, diff));
		}
		CHECK(dns_difftuple_create(diff->mctx, DNS_DIFFOP_ADD, name,
					   ttl, &tuple->rdata, &newtuple));
		CHECK(do_one_tuple(&newtuple, db, ver, diff));
		ISC_LIST_UNLINK(temp_diff.tuples, tuple, link);
		dns_diff_appendminimal(diff, &tuple);
		dns_rdata_reset(&rdata);
	}

	result = ISC_R_SUCCESS;
 failure:
	dns_diff_clear(&temp_diff);
	return (result);
}

static isc_result_t
rollback_private(dns_db_t *db, dns_rdatatype_t privatetype,
		 dns_dbversion_t *ver, dns_diff_t *diff)
{
	dns_diff_t temp_diff;
	dns_diffop_t op;
	dns_difftuple_t *tuple, *newtuple = NULL, *next;
	dns_name_t *name = dns_db_origin(db);
	isc_mem_t *mctx = diff->mctx;
	isc_result_t result;

	if (privatetype == 0)
		return (ISC_R_SUCCESS);

	dns_diff_init(mctx, &temp_diff);

	/*
	 * Extract the changes to be rolled back.
	 */
	for (tuple = ISC_LIST_HEAD(diff->tuples);
	     tuple != NULL; tuple = next) {

		next = ISC_LIST_NEXT(tuple, link);

		if (tuple->rdata.type != privatetype ||
		    !dns_name_equal(name, &tuple->name))
			continue;

		/*
		 * Allow records which indicate that a zone has been
		 * signed with a DNSKEY to be removed.
		 */
		if (tuple->op == DNS_DIFFOP_DEL &&
		    tuple->rdata.length == 5 &&
		    tuple->rdata.data[0] != 0 &&
		    tuple->rdata.data[4] != 0)
			continue;

		ISC_LIST_UNLINK(diff->tuples, tuple, link);
		ISC_LIST_PREPEND(temp_diff.tuples, tuple, link);
	}

	/*
	 * Rollback the changes.
	 */
	while ((tuple = ISC_LIST_HEAD(temp_diff.tuples)) != NULL) {
		op = (tuple->op == DNS_DIFFOP_DEL) ?
		      DNS_DIFFOP_ADD : DNS_DIFFOP_DEL;
		CHECK(dns_difftuple_create(mctx, op, name, tuple->ttl,
					   &tuple->rdata, &newtuple));
		CHECK(do_one_tuple(&newtuple, db, ver, &temp_diff));
	}
	result = ISC_R_SUCCESS;

 failure:
	dns_diff_clear(&temp_diff);
	return (result);
}

/*
 * Add records to cause the delayed signing of the zone by added DNSKEY
 * to remove the RRSIG records generated by a deleted DNSKEY.
 */
static isc_result_t
add_signing_records(dns_db_t *db, dns_rdatatype_t privatetype,
		    dns_dbversion_t *ver, dns_diff_t *diff)
{
	dns_difftuple_t *tuple, *newtuple = NULL, *next;
	dns_rdata_dnskey_t dnskey;
	dns_rdata_t rdata = DNS_RDATA_INIT;
	isc_boolean_t flag;
	isc_region_t r;
	isc_result_t result = ISC_R_SUCCESS;
	isc_uint16_t keyid;
	unsigned char buf[5];
	dns_name_t *name = dns_db_origin(db);
	dns_diff_t temp_diff;

	dns_diff_init(diff->mctx, &temp_diff);

	/*
	 * Extract the DNSKEY tuples from the list.
	 */
	for (tuple = ISC_LIST_HEAD(diff->tuples);
	     tuple != NULL; tuple = next) {

		next = ISC_LIST_NEXT(tuple, link);

		if (tuple->rdata.type != dns_rdatatype_dnskey)
			continue;

		ISC_LIST_UNLINK(diff->tuples, tuple, link);
		ISC_LIST_APPEND(temp_diff.tuples, tuple, link);
	}

	/*
	 * Extract TTL changes pairs, we don't need signing records for these.
	 */
	for (tuple = ISC_LIST_HEAD(temp_diff.tuples);
	     tuple != NULL; tuple = next) {
		if (tuple->op == DNS_DIFFOP_ADD) {
			/*
			 * Walk the temp_diff list looking for the
			 * corresponding delete.
			 */
			next = ISC_LIST_HEAD(temp_diff.tuples);
			while (next != NULL) {
				unsigned char *next_data = next->rdata.data;
				unsigned char *tuple_data = tuple->rdata.data;
				if (next->op == DNS_DIFFOP_DEL &&
				    dns_name_equal(&tuple->name, &next->name) &&
				    next->rdata.length == tuple->rdata.length &&
				    !memcmp(next_data, tuple_data,
					    next->rdata.length)) {
					ISC_LIST_UNLINK(temp_diff.tuples, next,
							link);
					ISC_LIST_APPEND(diff->tuples, next,
							link);
					break;
				}
				next = ISC_LIST_NEXT(next, link);
			}
			/*
			 * If we have not found a pair move onto the next
			 * tuple.
			 */
			if (next == NULL) {
				next = ISC_LIST_NEXT(tuple, link);
				continue;
			}
			/*
			 * Find the next tuple to be processed before
			 * unlinking then complete moving the pair to 'diff'.
			 */
			next = ISC_LIST_NEXT(tuple, link);
			ISC_LIST_UNLINK(temp_diff.tuples, tuple, link);
			ISC_LIST_APPEND(diff->tuples, tuple, link);
		} else
			next = ISC_LIST_NEXT(tuple, link);
	}

	/*
	 * Process the remaining DNSKEY entries.
	 */
	for (tuple = ISC_LIST_HEAD(temp_diff.tuples);
	     tuple != NULL;
	     tuple = ISC_LIST_HEAD(temp_diff.tuples)) {

		ISC_LIST_UNLINK(temp_diff.tuples, tuple, link);
		ISC_LIST_APPEND(diff->tuples, tuple, link);

		result = dns_rdata_tostruct(&tuple->rdata, &dnskey, NULL);
		RUNTIME_CHECK(result == ISC_R_SUCCESS);
		if ((dnskey.flags &
		     (DNS_KEYFLAG_OWNERMASK|DNS_KEYTYPE_NOAUTH))
			 != DNS_KEYOWNER_ZONE)
			continue;

		dns_rdata_toregion(&tuple->rdata, &r);

		keyid = dst_region_computeid(&r, dnskey.algorithm);

		buf[0] = dnskey.algorithm;
		buf[1] = (keyid & 0xff00) >> 8;
		buf[2] = (keyid & 0xff);
		buf[3] = (tuple->op == DNS_DIFFOP_ADD) ? 0 : 1;
		buf[4] = 0;
		rdata.data = buf;
		rdata.length = sizeof(buf);
		rdata.type = privatetype;
		rdata.rdclass = tuple->rdata.rdclass;

		CHECK(rr_exists(db, ver, name, &rdata, &flag));
		if (flag)
			continue;
		CHECK(dns_difftuple_create(diff->mctx, DNS_DIFFOP_ADD,
					   name, 0, &rdata, &newtuple));
		CHECK(do_one_tuple(&newtuple, db, ver, diff));
		INSIST(newtuple == NULL);
		/*
		 * Remove any record which says this operation has already
		 * completed.
		 */
		buf[4] = 1;
		CHECK(rr_exists(db, ver, name, &rdata, &flag));
		if (flag) {
			CHECK(dns_difftuple_create(diff->mctx, DNS_DIFFOP_DEL,
						   name, 0, &rdata, &newtuple));
			CHECK(do_one_tuple(&newtuple, db, ver, diff));
			INSIST(newtuple == NULL);
		}
	}

 failure:
	dns_diff_clear(&temp_diff);
	return (result);
}

#ifdef ORIGINAL_ISC_CODE
#else
// The DHCID in a TXT record is two bytes of type, plus two times MD5_DIGEST_LENGTH (which is 16),
// for a total of 2+2*16=34. It contains only [0123456789abcdef].
#define DHCID_LEN 34

typedef struct {
  unsigned found_dhcid;
  unsigned deleted_dhcid;
  isc_result_t result;
  dns_db_t *dhcid_db;
  dns_dbversion_t *dhcid_ver;
  dns_name_t *dhcid_name;
  dns_diff_t *dhcid_diff;
  dns_zone_t *dhcid_zone;
} infoblox_dhcid_match_t;

static isc_result_t
ib_is_dhcid_txt(rr_t *rr, isc_boolean_t *is_dhcid_txt) {
  if (rr == NULL || is_dhcid_txt == NULL)
    return (ISC_R_FAILURE);

  if (rr->rdata.type == dns_rdatatype_txt) {
    char buffer[8192];
    isc_buffer_t b;
    unsigned i;
    isc_result_t result;

    // rr->rdata is an (dns_rdata_t), and contains the possible DHCID data
    isc_buffer_init (&b, buffer, sizeof (buffer));
    result = dns_rdata_totext (&rr->rdata, NULL, &b);
    if (result != ISC_R_SUCCESS)
      return (result);

    // If the length isn't the correct one for a DHCID, we can return now.
    // Keep in mind that the TXT data will have leading and trailing double
    // quotes.
    if (b.used != DHCID_LEN+2) {
      *is_dhcid_txt = ISC_FALSE;
      return (ISC_R_SUCCESS);
    }

    for (i = 1; i < b.used - 1; i++) {
      // If there's a byte outside [0123456789abcdef], we can return
      if (! ((buffer[i] >= '0' && buffer[i] <= '9') ||
	     (buffer[i] >= 'a' && buffer[i] <= 'f'))) {
	*is_dhcid_txt = ISC_FALSE;
	return (ISC_R_SUCCESS);
      }
    }

    *is_dhcid_txt = ISC_TRUE;
  } else {
    *is_dhcid_txt = ISC_FALSE;
  }

  return (ISC_R_SUCCESS);
}

static isc_result_t
find_and_delete_dhcid (void *data, rr_t *rr)
{
  infoblox_dhcid_match_t *match = data;
  isc_result_t result;

  if (match == NULL)
    return (ISC_R_FAILURE);

  // For dhcid data in TXT records, we need to avoid deleting non-dhcid records.
  // DHCID records are only used to contain dhcid data, so we can delete regardless.
  if (rr->rdata.type == dns_rdatatype_txt) {
    isc_boolean_t is_dhcid_txt = ISC_FALSE;

    result = ib_is_dhcid_txt(rr, &is_dhcid_txt);
    if (result != ISC_R_SUCCESS)
      return (result);

    // We know it's a TXT, but if it isn't a TXT/dhcid, we just return success
    if (! is_dhcid_txt)
      return (ISC_R_SUCCESS);
  }

  match->found_dhcid += 1;
  match->result = delete_if (rr_equal_p, match->dhcid_db, match->dhcid_ver, match->dhcid_name,
			     rr->rdata.type, 0, &rr->rdata, match->dhcid_diff, match->dhcid_zone);
  if (match->result == ISC_R_SUCCESS)
    match->deleted_dhcid += 1;

  // We can stop looking now
  return (ISC_R_NOMORE);
}

// Return ISC_TRUE if the update section contains an add of an A or AAAA RR
static isc_boolean_t
update_section_has_a_or_aaaa(dns_message_t *request, dns_rdataclass_t zoneclass,
			     dns_ttl_t *address_ttl_p) {
	// Internal support function, assume sane arguments
	isc_boolean_t dhcid_check_add_special = ISC_FALSE;
	isc_result_t mr;
	for (mr = dns_message_firstname(request, DNS_SECTION_UPDATE);
	     mr == ISC_R_SUCCESS;
	     mr = dns_message_nextname(request, DNS_SECTION_UPDATE)) {
		dns_name_t *name = NULL;
		dns_rdata_t rdata = DNS_RDATA_INIT;
		dns_rdatatype_t covers;
		dns_ttl_t ttl;
		dns_rdataclass_t update_class;
		get_current_rr(request, DNS_SECTION_UPDATE, zoneclass,
			       &name, &rdata, &covers, &ttl, &update_class);
		if (update_class == zoneclass &&
		    (rdata.type == dns_rdatatype_a ||
		     rdata.type == dns_rdatatype_aaaa)) {
			dhcid_check_add_special = ISC_TRUE;
			if (address_ttl_p)
				*address_ttl_p = ttl;
			break;
		}
	}
	return (dhcid_check_add_special);
}

#ifdef INFOBLOX_INCREMENTAL_SIGNING
#else
static isc_boolean_t
infoblox_setup_last_queried(dns_zone_t *zone, isc_boolean_t queue) {
  isc_boolean_t reset_last_queried = ISC_FALSE;
  isc_boolean_t last_queried_zone = ISC_FALSE;
  isc_boolean_t last_queried_rr = ISC_FALSE;
  u_int64_t last_queried_rr_start = 0;

  infoblox_dns_zone_get_last_queried(zone, &last_queried_zone, &last_queried_rr,
				     &last_queried_rr_start);
  if (last_queried_zone || last_queried_rr) {
    infoblox_set_last_queried(last_queried_rr, last_queried_rr_start,
			      queue && last_queried_rr, last_queried_zone, zone);
    reset_last_queried = ISC_TRUE;
  }

  return (reset_last_queried);
}
#endif
#endif

static void
update_action(isc_task_t *task, isc_event_t *event) {
	update_event_t *uev = (update_event_t *) event;
#ifdef ORIGINAL_ISC_CODE
	dns_zone_t *zone = uev->zone;
#else
#define zone   (zones[nz])
#endif
	ns_client_t *client = (ns_client_t *)event->ev_arg;
	isc_result_t result;
#ifdef ORIGINAL_ISC_CODE
	dns_db_t *db = NULL;
	dns_dbversion_t *oldver = NULL;
	dns_dbversion_t *ver = NULL;
	dns_diff_t diff;	/* Pending updates. */
#else
#define db     (dbs[nz])
#define oldver (oldvers[nz])
#define ver    (vers[nz])
#define diff   (diffs[nz])
#endif
	dns_diff_t temp;	/* Pending RR existence assertions. */
#ifdef ORIGINAL_ISC_CODE
	isc_boolean_t soa_serial_changed = ISC_FALSE;
#else
#define soa_serial_changed (soa_serial_changeds[nz])
#endif
	isc_mem_t *mctx = client->mctx;
	dns_rdatatype_t covers;
	dns_message_t *request = client->message;
#ifdef ORIGINAL_ISC_CODE
	dns_rdataclass_t zoneclass;
	dns_name_t *zonename;
	dns_ssutable_t *ssutable = NULL;
#else
#define zoneclass (zoneclasses[nz])
#define zonename  (zonenames[nz])
#define ssutable  (ssutables[nz])
#endif
	dns_fixedname_t tmpnamefixed;
	dns_name_t *tmpname = NULL;
	unsigned int options, options2;
	dns_difftuple_t *tuple;
	dns_rdata_dnskey_t dnskey;
#ifdef ORIGINAL_ISC_CODE
	isc_boolean_t had_dnskey;
	dns_rdatatype_t privatetype = dns_zone_getprivatetype(zone);
#else
#ifdef INFOBLOX_INCREMENTAL_SIGNING
#else
	isc_boolean_t reset_last_queried = ISC_FALSE;
	const infoblox_sdu_patterns_t *sdu_pattern;
#endif
	/* For performance reasons, we do DNSSEC work only for signed zones. */
	isc_boolean_t perform_dnssec_tasks = ISC_FALSE;
	isc_boolean_t forced_creation_timestamp_update = ISC_FALSE;
	unsigned db_open = 0;
	unsigned zrq_drain_locked = 0;
	int commit_to_db = DB_END_ABORT;
	dns_zone_t *tmpzone = NULL;
#define MAX_ZONE_IN_TXN 2
	unsigned nz, num_zone;
	dns_zone_t *zones[MAX_ZONE_IN_TXN] = {NULL};
	dns_db_t *dbs[MAX_ZONE_IN_TXN] = {NULL};
	dns_dbversion_t *oldvers[MAX_ZONE_IN_TXN] = {NULL};
	dns_dbversion_t *vers[MAX_ZONE_IN_TXN] = {NULL};
	dns_diff_t diffs[MAX_ZONE_IN_TXN];
	isc_boolean_t soa_serial_changeds[MAX_ZONE_IN_TXN];
	dns_rdataclass_t zoneclasses[MAX_ZONE_IN_TXN];
	dns_name_t *zonenames[MAX_ZONE_IN_TXN] = {NULL};
	dns_ssutable_t *ssutables[MAX_ZONE_IN_TXN] = {NULL};
	isc_boolean_t send_notification[MAX_ZONE_IN_TXN];
	// In general, the 'dhcid_add_*' variables are used when checking
	// dhcid prerequisites and auto-adding dhcid RRs, while the
	// 'dhcid_*' variables are used for auto-removal of dhcid RRs.
	// Some 'dhcid_*' variables are also used when adding dhcid RRs.
	dns_name_t *dhcid_name = NULL;
	dns_name_t *dhcid_add_name = NULL;
	dns_ttl_t dhcid_add_ttl = 0;
	dns_rdata_t dhcid_add_rdata;
	unsigned dhcid_nz = 0;
	dns_rdatatype_t dhcid_type = dns_rdatatype_none;
	dns_rdatatype_t dhcid_address_type = dns_rdatatype_none;
	isc_boolean_t dhcid_check_add_special = FALSE;

	nz = 0;
	num_zone = 1;
#undef zone
	zones[nz] = uev->zone;
#define zone (zones[nz])
	soa_serial_changeds[nz] = ISC_FALSE;
	send_notification[nz] = ISC_FALSE;

	dns_rdatatype_t privatetype = dns_zone_getprivatetype(zone);
#endif
	dns_ttl_t maxttl = 0;
	isc_uint32_t maxrecords;
	isc_uint64_t records;

	INSIST(event->ev_type == DNS_EVENT_UPDATE);

	dns_diff_init(mctx, &diff);
	dns_diff_init(mctx, &temp);

	CHECK(dns_zone_getdb(zone, &db));
	zonename = dns_db_origin(db);
	zoneclass = dns_db_class(db);
	dns_zone_getssutable(zone, &ssutable);

	/*
	 * Update message processing can leak record existance information
	 * so check that we are allowed to query this zone.  Additionally
	 * if we would refuse all updates for this zone we bail out here.
	 */
#ifdef ORIGINAL_ISC_CODE
#else
	infoblox_set_required_signing_state(INFOBLOX_IS_SIGNED_ZONE(zone) ? rss_signed : rss_unsigned);
	perform_dnssec_tasks = INFOBLOX_IS_SIGNED_ZONE(zone);
	if (infoblox_authenticated_via_gss_tsig(client)) {
		if (client->ib_principal) {
			infoblox_set_ddns_principal(client->ib_principal);
		} else {
			FAILC(DNS_R_SERVFAIL, "Update authenticated via GSS-TSIG, but no known principal");
		}
	} else {
		infoblox_set_ddns_principal(""); // Delete any existing principal
	}
#endif
	CHECK(checkqueryacl(client, dns_zone_getqueryacl(zone), zonename,
			    dns_zone_getupdateacl(zone), ssutable));

#ifdef ORIGINAL_ISC_CODE
#else
	// Check that the zone has loaded. We will repeat this
	// test when creating the new DB version, but this is
	// a pre-check to avoid the overhead of OneDB transaction
	// in case the zone hasn't been loaded for a certain period.
	// Note that we don't have to prioritize the zone loading at this
	// point; it's done in ns_update_start().
	if (!infoblox_dns_zone_loaded(zone)) {
		FAILC(DNS_R_REFUSED, "Zone not loaded");
	}

	// We did not want to start a txn before checking the ACLs. Now that
	// the preliminary permissions check is done, we must preserve lock
	// ordering by opening the DB before possibly locking
	// node trees. Hence, detach from the 'db' and 'ssutable', start a DB txn,
	// then acquire the 'db' and 'ssutable' again.
	if (db != NULL)
		dns_db_detach(&db);
	if (ssutable != NULL)
		dns_ssutable_detach(&ssutable);
	zrq_consumer_drain_lock();
	zrq_drain_locked = 1;
	result = infoblox_db_operation_begin(DB_BEGIN_READ_WRITE);
	if (result != ISC_R_SUCCESS)
	  {
	    FAILC (DNS_R_SERVFAIL, "Error starting DB transaction");
	  }
	db_open = 1;
	CHECK(dns_zone_getdb(zone, &db));
	zonename = dns_db_origin(db);
	zoneclass = dns_db_class(db);
	dns_zone_getssutable(zone, &ssutable);
	infoblox_set_ddns_zone(zone);
#endif

	/*
	 * Get old and new versions now that queryacl has been checked.
	 */
	dns_db_currentversion(db, &oldver);
	CHECK(dns_db_newversion(db, &ver));

#ifdef	ORIGINAL_ISC_CODE
#else
	/* Disallow updates to read-only ZDB zones. The zone itself
	 * would prevent the update from being applied, but checking
	 * up front avoids both unnecessary work and spurious errors
	 * from trying to access non-existent OneDB zones (the ZDB
	 * zone may exist after the OneDB zone has been deleted.)
	 *
	 * This check is after newversion() so that the ZRQ will have
	 * been drained and the zone state will be current.
	 */
	if (dns_zone_is_zdb(zone) && infoblox_zdb_is_readonly(db)) {
		FAILC(ISC_R_NOPERM, "Zone is read-only");
	}

	if (zone != NULL)
          {
            dns_view_t *view = dns_zone_getview (zone);
            INFOBLOX_SET_VIEW(view);
          }
#endif

	/*
	 * Check prerequisites.
	 */

#ifdef	ORIGINAL_ISC_CODE
#else
        infoblox_prereq_check_set (1);
#ifdef INFOBLOX_INCREMENTAL_SIGNING
#else
	if (infoblox_perhaps_last_queried()) {
	     reset_last_queried = infoblox_setup_last_queried(zone, ISC_TRUE);
	}
#endif
#endif
	for (result = dns_message_firstname(request, DNS_SECTION_PREREQUISITE);
	     result == ISC_R_SUCCESS;
	     result = dns_message_nextname(request, DNS_SECTION_PREREQUISITE))
	{
		dns_name_t *name = NULL;
		dns_rdata_t rdata = DNS_RDATA_INIT;
		dns_ttl_t ttl;
		dns_rdataclass_t update_class;
		isc_boolean_t flag;

		get_current_rr(request, DNS_SECTION_PREREQUISITE, zoneclass,
			       &name, &rdata, &covers, &ttl, &update_class);

		if (ttl != 0)
			PREREQFAILC(DNS_R_FORMERR,
				    "prerequisite TTL is not zero");

		if (! dns_name_issubdomain(name, zonename))
			PREREQFAILN(DNS_R_NOTZONE, name,
				    "prerequisite name is out of zone");

		if (update_class == dns_rdataclass_any) {
			if (rdata.length != 0)
				PREREQFAILC(DNS_R_FORMERR,
				      "class ANY prerequisite "
				      "RDATA is not empty");
			if (rdata.type == dns_rdatatype_any) {
				CHECK(name_exists(db, ver, name, &flag));
				if (! flag) {
					PREREQFAILN(DNS_R_NXDOMAIN, name,
						    "'name in use' "
						    "prerequisite not "
						    "satisfied");
				}
			} else {
				CHECK(rrset_exists(db, ver, name,
						   rdata.type, covers, &flag));
#ifdef ORIGINAL_ISC_CODE
#else
				/* Assume that any "RRset exists (value
				 * independent)" prerequisite for TXT or DHCID
				 * from our dhcpd is a dhcid prereq and
				 * remember the type for dhcid processing. */
				if (client->is_infoblox_dhcpd &&
				    (rdata.type == dns_rdatatype_txt ||
				     rdata.type == dns_rdatatype_dhcid)) {
					if (dhcid_type != dns_rdatatype_none && dhcid_type != rdata.type) {
						FAILC(ISC_R_UNEXPECTED,
						      "prerequisites for both TXT and DHCID (YXRRSET) are unexpected");
					}
					dhcid_type = rdata.type;
					// If the prerequisite check failed, look in the update section
					// to see if there are adds for A or AAAA RRs, in which case we
					// may override the prerequisite failure.
					if (! flag) {
						// Assume there's just one prerequisite that needs checking,
						// so that dhcid_check_add_special==ISC_FALSE means we haven't
						// looked in the update section already.
						if (! dhcid_check_add_special)
							dhcid_check_add_special = update_section_has_a_or_aaaa(
											request, zoneclass,
											&dhcid_add_ttl);
						// If we are adding an A or AAAA, we'll do additional checks to
						// see if the update can go ahead even though prerequisite
						// checking failed. Thus, pretend that the prerequisite check
						// succeeded.
						if (dhcid_check_add_special)
							flag = ISC_TRUE;
					}
				}
#endif
				if (! flag) {
					/* RRset does not exist. */
					PREREQFAILNT(DNS_R_NXRRSET, name, rdata.type,
					"'rrset exists (value independent)' "
					"prerequisite not satisfied");
				}
			}
		} else if (update_class == dns_rdataclass_none) {
			if (rdata.length != 0)
				PREREQFAILC(DNS_R_FORMERR,
					    "class NONE prerequisite "
					    "RDATA is not empty");
			if (rdata.type == dns_rdatatype_any) {
				CHECK(name_exists(db, ver, name, &flag));
				if (flag) {
					PREREQFAILN(DNS_R_YXDOMAIN, name,
						    "'name not in use' "
						    "prerequisite not "
						    "satisfied");
				}
			} else {
				CHECK(rrset_exists(db, ver, name,
						   rdata.type, covers, &flag));
				if (flag) {
					/* RRset exists. */
					PREREQFAILNT(DNS_R_YXRRSET, name,
						     rdata.type,
						     "'rrset does not exist' "
						     "prerequisite not "
						     "satisfied");
				}
			}
		} else if (update_class == zoneclass) {
#ifdef ORIGINAL_ISC_CODE
#else
			/* Assume that any "RRset exists (value
			 * dependent)" prerequisite for TXT or DHCID
			 * from our dhcpd is a dhcid prereq and
			 * remember the type for dhcid processing. */
			if (client->is_infoblox_dhcpd &&
			    (rdata.type == dns_rdatatype_txt ||
			     rdata.type == dns_rdatatype_dhcid)) {
				if (dhcid_type != dns_rdatatype_none && dhcid_type != rdata.type) {
					FAILC(ISC_R_UNEXPECTED, "prerequisites for both TXT and DHCID are unexpected");
				}
				dhcid_type = rdata.type;
				dhcid_add_name = name;
				dhcid_add_rdata = rdata;
				dhcid_nz = nz;
			}
#endif

			/* "temp<rr.name, rr.type> += rr;" */
			result = temp_append(&temp, name, &rdata);
			if (result != ISC_R_SUCCESS) {
				UNEXPECTED_ERROR(__FILE__, __LINE__,
					 "temp entry creation failed: %s",
						 dns_result_totext(result));
				FAIL(ISC_R_UNEXPECTED);
			}
		} else {
			PREREQFAILC(DNS_R_FORMERR, "malformed prerequisite");
		}
	}
	if (result != ISC_R_NOMORE)
		FAIL(result);

	/*
	 * Perform the final check of the "rrset exists (value dependent)"
	 * prerequisites.
	 */
	if (ISC_LIST_HEAD(temp.tuples) != NULL) {
		dns_rdatatype_t type;
#ifdef ORIGINAL_ISC_CODE
#else
		type = dns_rdatatype_none;
#endif

		/*
		 * Sort the prerequisite records by owner name,
		 * type, and rdata.
		 */
		result = dns_diff_sort(&temp, temp_order);
		if (result != ISC_R_SUCCESS)
			FAILC(result, "'RRset exists (value dependent)' "
			      "prerequisite not satisfied");

		dns_fixedname_init(&tmpnamefixed);
		tmpname = dns_fixedname_name(&tmpnamefixed);
		result = temp_check(mctx, &temp, db, ver, tmpname, &type);
#ifdef ORIGINAL_ISC_CODE
#else
		/* If the update is from our dhcpd, it had a dhcid
		 * existence prerequisite, and is adding an address
		 * record, then ignore prerequisite failure here in
		 * favor of special handling below.
		 *
		 * This is a hack that we only get away with because
		 * we know what sorts of updates we're sending
		 * ourselves.
		 */
		if (result == DNS_R_NXRRSET && client->is_infoblox_dhcpd &&
		    dhcid_type != dns_rdatatype_none) {
			// Have we already checked the update section ?
			// Note: If we already checked, and did not find an A or AAAA, we will
			// (unnecessarily) check again here. This will only happen if the update
			// contains both a value dependent _and_ a value independent prerequisite,
			// something which we do not expect ever to happen. Thus, we accept the
			// inefficiency if it does happen, saving ourselves setting and checking
			// yet another boolean (have_checked_update_section).
			if (! dhcid_check_add_special)
				dhcid_check_add_special = update_section_has_a_or_aaaa(request, zoneclass,
										       &dhcid_add_ttl);
			if (dhcid_check_add_special)
				result = ISC_R_SUCCESS;
		} else if (result == ISC_R_SUCCESS && client->is_infoblox_dhcpd &&
			   dhcid_type != dns_rdatatype_none) {
			// The prerequisite was satisfied, so we know that the dhcid exists, and
			// there's no need for us to add it.
			dhcid_add_name = NULL;
		}
#endif
		if (result != ISC_R_SUCCESS)
			FAILNT(result, tmpname, type,
			       "'RRset exists (value dependent)' "
			       "prerequisite not satisfied");
	}

	update_log(client, zone, LOGLEVEL_DEBUG,
		   "prerequisites are OK");

#ifdef	ORIGINAL_ISC_CODE
#else
#ifdef INFOBLOX_INCREMENTAL_SIGNING
#else
	if (reset_last_queried) {
		infoblox_reset_last_queried();
		reset_last_queried = ISC_FALSE;
	}
#endif
        infoblox_prereq_check_set (0);
#endif
	/*
	 * Check Requestor's Permissions.  It seems a bit silly to do this
	 * only after prerequisite testing, but that is what RFC2136 says.
	 */
	if (ssutable == NULL)
		CHECK(checkupdateacl(client, dns_zone_getupdateacl(zone),
				     "update", zonename, ISC_FALSE, ISC_FALSE));
	else if (client->signer == NULL && !TCPCLIENT(client))
		CHECK(checkupdateacl(client, NULL, "update", zonename,
				     ISC_FALSE, ISC_TRUE));

	if (dns_zone_getupdatedisabled(zone))
		FAILC(DNS_R_REFUSED, "dynamic update temporarily disabled "
				     "because the zone is frozen.  Use "
				     "'rndc thaw' to re-enable updates.");
#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * Get restrict patterns for the zone.  They will apply for each
	 * update name below.
	 */
	sdu_pattern = NULL;
	if (infoblox_zone_get_ddns_restrict_pattern(zone))
		sdu_pattern = infoblox_zone_get_sdu_patterns(zone);
#endif

	/*
	 * Perform the Update Section Prescan.
	 */

	for (result = dns_message_firstname(request, DNS_SECTION_UPDATE);
	     result == ISC_R_SUCCESS;
	     result = dns_message_nextname(request, DNS_SECTION_UPDATE))
	{
		dns_name_t *name = NULL;
		dns_rdata_t rdata = DNS_RDATA_INIT;
		dns_ttl_t ttl;
		dns_rdataclass_t update_class;

#ifdef ORIGINAL_ISC_CODE
#else
		// get_current_rr() sets rdata.rdclass equal to 'zoneclass',
		// so we need to give it a suitable default. If the zone is
		// something other than zones[0], we'll fix up rdata.rdclass.
		nz = 0;
#endif
		get_current_rr(request, DNS_SECTION_UPDATE, zoneclass,
			       &name, &rdata, &covers, &ttl, &update_class);

#ifdef ORIGINAL_ISC_CODE
		if (! dns_name_issubdomain(name, zonename))
			FAILC(DNS_R_NOTZONE,
			      "update RR is outside zone");
#else
		// Find the appropriate zone. Note that we make the assumption
		// that if 'name' is a subdomain of 'zonenames[nz]', the client
		// wants to update the zone 'zonenames[nz]'. This is fine for
		// our intended audience, where there may be at most two different
		// zones, and they aren't sub-zones of each other.
		for (nz = 0; nz < num_zone; nz++)
		  {
		    if (dns_name_issubdomain(name, zonenames[nz]))
		      break;
		  }

		if (nz < num_zone)
		  {
		    // OK, no action necessary, other than to perhaps fix up
		    // rdata.rdclass.
		    if (nz != 0)
		      rdata.rdclass = zoneclass;
		  }
		else
		  {
		    // Not a zone we've encountered previously while processing
		    // this update. Look for a match in the zone table.
		    if (num_zone >= MAX_ZONE_IN_TXN ||
			! client->is_infoblox_dhcpd ||
			! infoblox_allow_multi_zone_ddns ())
		      {
			// Too many zones, or the client isn't an Infoblox dhcpd,
			// or we're simply not allowing multi-zone updates.
			nz = 0;
			FAILC(DNS_R_NOTZONE,
			      "update RR is outside zone");
		      }

		    result = dns_zt_find (client->view->zonetable, name, 0, NULL, &tmpzone);
		    if (result != ISC_R_SUCCESS && result != DNS_R_PARTIALMATCH)
		      {
			if (tmpzone != NULL)
			  dns_zone_detach (&tmpzone);
			// The FAILC macro refers to 'zone' (i.e., to 'zones[nz]'), so
			// give it a suitable default for its messages.
			nz = 0;
			FAILC(DNS_R_NOTZONE,
                              "update RR is outside zone");
		      }
		    else if (dns_zone_gettype(tmpzone) != dns_zone_master)
		      {
			// We're going to print an error message and leave this function.
			// To make the offending zone show up in the message, we add it
			// to the 'zones' array. Note that we detach from the zone before
			// invoking the FAILC macro; that's safe as long as we effectively
			// hold an exclusive lock (via the OneDB lock) on 'named' activity,
			// but becomes an XXXXXX if that restriction is removed.
			nz = num_zone;
			zones[nz] = tmpzone;
			if (tmpzone != NULL)
			  dns_zone_detach (&tmpzone);
			FAILC(DNS_R_NOTAUTH,
                              "not authoritative for update zone");
		      }

		    nz = num_zone;
		    num_zone += 1;

		    zones[nz] = tmpzone;
		    tmpzone = NULL;

		    // XXXXXX When upgrading BIND, compare to code at start of function.
		    // Initialize values for the new zone. The following is
		    // essentially a repeat of what the original code does
		    // for the primary update zone (see above at the start
		    // of this function), so if you're upgrading the BIND
		    // code, it's a good idea to compare the initializations
		    // for additions or removals.
		    soa_serial_changeds[nz] = ISC_FALSE;
		    send_notification[nz] = ISC_FALSE;
		    dns_diff_init (mctx, &diffs[nz]);
		    if (!infoblox_zone_isdbinitialized(zones[nz])) {
			    /* See ns_update_start() for failing with DROP */
			    infoblox_dns_zone_make_last_requested(
				    zones[nz], "update(multi)");
			    FAILC(DNS_R_DROP, "Zone not loaded (multi)");
		    }
		    CHECK (dns_zone_getdb (zones[nz], &dbs[nz]));
		    zonenames[nz] = dns_db_origin (dbs[nz]);
		    zoneclasses[nz] = dns_db_class (dbs[nz]);
		    dns_zone_getssutable (zones[nz], &ssutables[nz]);
		    dns_db_currentversion (dbs[nz], &oldvers[nz]);
		    sdu_pattern = infoblox_zone_get_sdu_patterns(zones[nz]);
		    // Unset the DDNS zone, so that newversion() doesn't
		    // waste time draining the ZRQ again (we already did
		    // that in the context of the newversion() call for
		    // the first zone).
		    infoblox_set_ddns_zone(NULL);
		    // Since we set the DDNS zone to NULL, newversion()
		    // will not check that the zone is loaded, so do
		    // that here. (The infoblox_dns_zone_loaded() call
		    // acquires the zone lock, so to preserve lock order,
		    // we need to make it before creating a new version.)
		    if (! infoblox_dns_zone_loaded(zones[nz])) {
			FAILC(DNS_R_REFUSED, "Zone not loaded (multi)");
		    }
		    if (dns_zone_is_zdb(zones[nz]) &&  infoblox_zdb_is_readonly(dbs[nz])) {
			    FAILC(ISC_R_NOPERM, "Zone is read-only (multi)");
		    }
		    CHECK (dns_db_newversion (dbs[nz], &vers[nz]));
		    // Now we can set the DDNS zone
		    infoblox_set_ddns_zone(zones[nz]);

		    infoblox_set_required_signing_state(INFOBLOX_IS_SIGNED_ZONE(zones[nz]) ?
							rss_signed: rss_unsigned);
                    perform_dnssec_tasks = INFOBLOX_IS_SIGNED_ZONE(zones[nz]);

		    // Check requestor's permissions for this zone
		    result = ISC_R_SUCCESS;
		    if (ssutable == NULL)
		      CHECK(checkupdateacl(client, dns_zone_getupdateacl(zone),
					   "update", zonename, ISC_FALSE, ISC_FALSE));
		    else if (client->signer == NULL)
		      CHECK(checkupdateacl(client, NULL, "update", zonename,
					   ISC_FALSE, ISC_TRUE));

		    if (dns_zone_getupdatedisabled(zone))
		      FAILC(DNS_R_REFUSED, "dynamic update temporarily disabled");

#ifdef INFOBLOX_INCREMENTAL_SIGNING
#else
		    if (infoblox_perhaps_last_queried() && zone) {
		      reset_last_queried = infoblox_setup_last_queried(zone, ISC_TRUE);
		    }
#endif
		  }
#endif
		if (update_class == zoneclass) {
			/*
			 * Check for meta-RRs.  The RFC2136 pseudocode says
			 * check for ANY|AXFR|MAILA|MAILB, but the text adds
			 * "or any other QUERY metatype"
			 */
			if (dns_rdatatype_ismeta(rdata.type)) {
				FAILC(DNS_R_FORMERR,
				      "meta-RR in update");
			}
			result = dns_zone_checknames(zone, name, &rdata);
			if (result != ISC_R_SUCCESS)
#ifdef ORIGINAL_ISC_CODE
#else
			{
				char namebuf[DNS_NAME_FORMATSIZE];
				dns_name_format(name, namebuf, sizeof(namebuf));
				update_log(client, zone, ISC_LOG_ERROR,
					   "DDNS update failed due to host "
					   "checking policy on '%s'", namebuf);
#endif
				FAIL(DNS_R_REFUSED);
#ifdef ORIGINAL_ISC_CODE
#else
                        }
#endif
		} else if (update_class == dns_rdataclass_any) {
			if (ttl != 0 || rdata.length != 0 ||
			    (dns_rdatatype_ismeta(rdata.type) &&
			     rdata.type != dns_rdatatype_any))
				FAILC(DNS_R_FORMERR,
				      "meta-RR in update");
		} else if (update_class == dns_rdataclass_none) {
			if (ttl != 0 ||
			    dns_rdatatype_ismeta(rdata.type))
				FAILC(DNS_R_FORMERR,
				      "meta-RR in update");
		} else {
			update_log(client, zone, ISC_LOG_WARNING,
				   "update RR has incorrect class %d",
				   update_class);
			FAIL(DNS_R_FORMERR);
		}

#ifdef ORIGINAL_ISC_CODE
#else
		if (perform_dnssec_tasks) {
#endif
		/*
		 * draft-ietf-dnsind-simple-secure-update-01 says
		 * "Unlike traditional dynamic update, the client
		 * is forbidden from updating NSEC records."
		 */
		if (rdata.type == dns_rdatatype_nsec3) {
			FAILC(DNS_R_REFUSED,
			      "explicit NSEC3 updates are not allowed "
			      "in secure zones");
		} else if (rdata.type == dns_rdatatype_nsec) {
			FAILC(DNS_R_REFUSED,
			      "explicit NSEC updates are not allowed "
			      "in secure zones");
#ifdef ORIGINAL_ISC_CODE
		} else if (rdata.type == dns_rdatatype_rrsig &&
			   !dns_name_equal(name, zonename)) {
			FAILC(DNS_R_REFUSED,
			      "explicit RRSIG updates are currently "
			      "not supported in secure zones except "
			      "at the apex");
		}
#else
		} else if (rdata.type == dns_rdatatype_rrsig) {
			FAILC(DNS_R_REFUSED,
			      "explicit RRSIG updates are currently "
			      "not supported in secure zones");
		} else if (rdata.type == dns_rdatatype_dnskey) {
			FAILC(DNS_R_REFUSED,
			      "explicit DNSKEY updates are currently "
			      "not supported in secure zones");
		}
		} // if (perform_dnssec_tasks)
#endif

		if (ssutable != NULL) {
			isc_netaddr_t netaddr;
			dst_key_t *tsigkey = NULL;
			isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);

			if (client->message->tsigkey != NULL)
				tsigkey = client->message->tsigkey->key;

			if (rdata.type != dns_rdatatype_any) {
				if (!dns_ssutable_checkrules2
				    (ssutable, client->signer, name, &netaddr,
				     ISC_TF(TCPCLIENT(client)),
				     &ns_g_server->aclenv,
				     rdata.type, tsigkey))
				{
					FAILC(DNS_R_REFUSED,
					      "rejected by secure update");
				}
			} else {
				if (!ssu_checkall(db, ver, name, ssutable,
						  client->signer,
						  &netaddr,
						  ISC_TF(TCPCLIENT(client)),
						  tsigkey))
				{
					FAILC(DNS_R_REFUSED,
					      "rejected by secure update");
				}
			}
		}
#ifdef ORIGINAL_ISC_CODE
#else
		/*
		 * Check if the update name matches a restrict pattern; if it
		 * does, the update should be refused.
		 */
		if (sdu_pattern != NULL) {
			dns_name_t match;
			dns_name_init(&match, NULL);
			result = infoblox_sdu_patterns_match(sdu_pattern, name,
							     &match);
			if (result == ISC_R_SUCCESS) {
				char mbuf[DNS_NAME_FORMATSIZE];
				char ubuf[DNS_NAME_FORMATSIZE];

				result = DNS_R_REFUSED;
				dns_name_format(name, ubuf, sizeof(ubuf));
				dns_name_format(&match, mbuf, sizeof(mbuf));
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "updating '%s' rejected by pattern "
					   "'%s' (%s)", ubuf, mbuf,
					   isc_result_totext(result));
				goto failure;
			} else if (result != ISC_R_NOTFOUND) {
				/*
				 * This shouldn't happen since
				 * infoblox_sdu_patterns_match() shouldn't
				 * fail run-time (see its description).
				 */
				update_log(client, zone, ISC_LOG_ERROR,
					   "unexpected failure in SDU pattern "
					   "match: %s",
					   isc_result_totext(result));
				goto failure;
			}
		}

		/* Special dhcid checking for dual-stack updates.
		 * Occurs only for updates from our dhcp that are
		 * adding records, supplied an dhcid existence
		 * prerequisite, and had their prerequisite
		 * check fail.
		 *
		 * Assumes that only an A or AAAA, not both, are being
		 * added. Which should be a safe assumption since we
		 * control the client.
		 */
		if (dhcid_check_add_special && update_class == zoneclass &&
		    (rdata.type == dns_rdatatype_a ||
		     rdata.type == dns_rdatatype_aaaa)) {
			dhcpd_types_t exists = { 0,0,0,0,0 };
			result = rrset_dhcpd_types(db, ver, name, &exists);
			if (result != ISC_R_SUCCESS) {
				/* This shouldn't happen. */
				update_log(client, zone, ISC_LOG_ERROR,
					   "dhcid add validation failed: internal error (%s)",
					   isc_result_totext(result));
				// Error result already set.
				goto failure;
			}

			const char *dhcid_type_str = (dhcid_address_type == dns_rdatatype_txt ? "TXT" : "DHCID");

			/* Require non-existence of the dhcid. If it
			 * exists then the prerequisite wanted a
			 * specific value, which wasn't found, and
			 * that indicates a conflict. */
			if ((dhcid_type == dns_rdatatype_dhcid && exists.dhcid) ||
			    (dhcid_type == dns_rdatatype_txt && exists.txt)) {
				update_log(client, zone, ISC_LOG_ERROR,
					   "dhcid add validation failed: %s exists with unexpected value",
					   dhcid_type_str);
				result = DNS_R_NXRRSET;
				goto failure;
			}

			/* Require non-existence of address record
			 * (since there was no (correct) dhcid, the
			 * address record must not exist). */
			if ((rdata.type == dns_rdatatype_a && exists.a) ||
			    (rdata.type == dns_rdatatype_aaaa && exists.aaaa)) {
				update_log(client, zone, ISC_LOG_ERROR,
					   "dhcid add validation failed: no %s but address record exists",
					   dhcid_type_str);
				result = DNS_R_NXRRSET;
				goto failure;
			}

			if ((rdata.type == dns_rdatatype_a && exists.aaaa) ||
			    (rdata.type == dns_rdatatype_aaaa && exists.a)) {
				/* An address record of the other type
				 * exists. */

				/* If the dhcid type is DHCID, require
				 * that a TXT/dhcid RR exists. If it
				 * does, we conclude that the address
				 * record of the other type also is
				 * managed by a dhcpd, and we can add
				 * the DHCID and address record.
				 *
				 * If there is no TXT/dhcid, then
				 * while it is possible that the
				 * address record of the other type is
				 * managed by a dhcpd in MS mode, it
				 * is also possible that it is an
				 * unmanaged record that we must not
				 * touch. Hence, fail the update.
				 */
				if (dhcid_type == dns_rdatatype_dhcid) {
					if (!exists.txt) {
						update_log(client, zone, ISC_LOG_ERROR,
							   "dhcid add validation failed: complementing TXT RR not found");
						result = DNS_R_NXRRSET;
						goto failure;

					}
				}

				/* If the dhcid type is TXT, require
				 * that a DHCID RR exists. The
				 * reasoning is the same.
				 */
				if (dhcid_type == dns_rdatatype_txt) {
					if (!exists.dhcid) {
						update_log(client, zone, ISC_LOG_ERROR,
							   "dhcid add validation failed: complementing DHCID RR not found");
						result = DNS_R_NXRRSET;
						goto failure;
					}
				}

				/* Also require that there is no
				 * non-address, non-dhcid, RR for the
				 * name. If there is, we have an
				 * unexpected situation, and caution
				 * demands that we fail the update. */
				if (exists.other) {
					update_log(client, zone, ISC_LOG_ERROR,
						   "dhcid add validation failed: non-address and non-dhcid RR exists for name");
					result = DNS_R_NXRRSET;
					goto failure;
				}
			} else {
				/* No address record of the other type exists. */
				update_log(client, zone, ISC_LOG_ERROR,
					   "dhcid add validation failed: no complementing address record exists for name");
				result = DNS_R_NXRRSET;
				goto failure;
			}

			/* If we've gotten here, then the special
			 * checks passed and we'll allow the update to
			 * proceed despite the prerequisite failure. */
			dhcid_check_add_special = ISC_FALSE;
			update_log(client, zone, LOGLEVEL_PROTOCOL,
				   "dhcid add prerequisite processing successful");
		}
#endif
	}
	if (result != ISC_R_NOMORE)
		FAIL(result);

#ifdef ORIGINAL_ISC_CODE
#else
#ifdef INFOBLOX_INCREMENTAL_SIGNING
#else
	if (reset_last_queried) {
		infoblox_reset_last_queried();
		reset_last_queried = ISC_FALSE;
	}
#endif

	// Use the "primary" zone in the log message below
	nz = 0;
#endif
	update_log(client, zone, LOGLEVEL_DEBUG,
		   "update section prescan OK");

	/*
	 * Process the Update Section.
	 */

#ifdef ORIGINAL_ISC_CODE
	options = dns_zone_getoptions(zone);
	options2 = dns_zone_getoptions2(zone);
#else
	// Obtain options inside the loop over the zones

	// Keep track of whether or not we see an update to an MX record
	int need_check_mx = 0;
	/* Track if there is an attempt to apply changes to zone database. We can't
	 * tell if there is no change to ZDB by checking ISC_LIST_EMPTY(diff.tuples).
	 * only Especially there will be no change to database if applying duplicate
	 * records to the same dns name.
	 */
	int apply_attempt_count = 0;
#endif
	for (result = dns_message_firstname(request, DNS_SECTION_UPDATE);
	     result == ISC_R_SUCCESS;
	     result = dns_message_nextname(request, DNS_SECTION_UPDATE))
	{
		dns_name_t *name = NULL;
		dns_rdata_t rdata = DNS_RDATA_INIT;
		dns_ttl_t ttl;
		dns_rdataclass_t update_class;
		isc_boolean_t flag;

#ifdef ORIGINAL_ISC_CODE
#else
		nz = 0;
#endif
		get_current_rr(request, DNS_SECTION_UPDATE, zoneclass,
			       &name, &rdata, &covers, &ttl, &update_class);

#ifdef ORIGINAL_ISC_CODE
#else
		if (rdata.type == dns_rdatatype_mx)
			need_check_mx = 1;

		// Find the zone
		for (nz = 0; nz < num_zone; nz++)
                  {
                    if (dns_name_issubdomain(name, zonenames[nz]))
                      break;
                  }

                if (nz >= num_zone)
		  {
		    // We've already scanned for zones, so this shouldn't
		    // happen...
		    nz = 0;
		    FAILC(DNS_R_NOTZONE,
                          "update RR is outside zone");
		  }

		infoblox_set_ddns_zone(zone);
		if (nz != 0)
		  {
		    // In the get_current_rr(), we assumed nz=0, but now we
		    // know better, and need to fix up rdata.rdclass. Recall
		    // that 'zoneclass' is a macro, and varies with 'nz'.
		    rdata.rdclass = zoneclass;
		  }

		// Get the options now that we know which zone we're working on.
		options = dns_zone_getoptions(zone);
		options2 = dns_zone_getoptions2(zone);

#ifdef INFOBLOX_INCREMENTAL_SIGNING
#else
		if (infoblox_perhaps_last_queried()) {
		  reset_last_queried = infoblox_setup_last_queried(zone, ISC_FALSE);
		}
#endif
		forced_creation_timestamp_update =
			infoblox_zone_get_ddns_force_creation_timestamp_update(zone);
#endif

		if (update_class == zoneclass) {
			/*
			 * RFC1123 doesn't allow MF and MD in master zones.
			 */
			if (rdata.type == dns_rdatatype_md ||
			    rdata.type == dns_rdatatype_mf) {
				char typebuf[DNS_RDATATYPE_FORMATSIZE];

				dns_rdatatype_format(rdata.type, typebuf,
						     sizeof(typebuf));
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "attempt to add %s ignored",
					   typebuf);
				continue;
			}
			if ((rdata.type == dns_rdatatype_ns ||
			     rdata.type == dns_rdatatype_dname) &&
			    dns_name_iswildcard(name)) {
				char typebuf[DNS_RDATATYPE_FORMATSIZE];

				dns_rdatatype_format(rdata.type, typebuf,
						     sizeof(typebuf));
				update_log(client, zone,
					   LOGLEVEL_PROTOCOL,
					   "attempt to add wildcard %s record "
					   "ignored", typebuf);
				continue;
			}
			if (rdata.type == dns_rdatatype_cname) {
				CHECK(cname_incompatible_rrset_exists(db, ver,
								      name,
								      &flag));
				if (flag) {
					update_log(client, zone,
						   LOGLEVEL_PROTOCOL,
						   "attempt to add CNAME "
						   "alongside non-CNAME "
						   "ignored");
					continue;
				}
			} else {
				CHECK(rrset_exists(db, ver, name,
						   dns_rdatatype_cname, 0,
						   &flag));
				if (flag &&
				    ! dns_rdatatype_isdnssec(rdata.type))
				{
					update_log(client, zone,
						   LOGLEVEL_PROTOCOL,
						   "attempt to add non-CNAME "
						   "alongside CNAME ignored");
					continue;
				}
			}
			if (rdata.type == dns_rdatatype_soa) {
				isc_boolean_t ok;
				CHECK(rrset_exists(db, ver, name,
						   dns_rdatatype_soa, 0,
						   &flag));
				if (! flag) {
					update_log(client, zone,
						   LOGLEVEL_PROTOCOL,
						   "attempt to create 2nd "
						   "SOA ignored");
					continue;
				}
				CHECK(check_soa_increment(db, ver, &rdata,
							  &ok));
				if (! ok) {
					update_log(client, zone,
						   LOGLEVEL_PROTOCOL,
						   "SOA update failed to "
						   "increment serial, "
						   "ignoring it");
					continue;
				}
				soa_serial_changed = ISC_TRUE;
			}

			if (rdata.type == privatetype) {
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "attempt to add a private type "
					   "(%u) record rejected internal "
					   "use only", privatetype);
				continue;
			}

			if (rdata.type == dns_rdatatype_nsec3param) {
#ifdef ORIGINAL_ISC_CODE
				/*
				 * Ignore attempts to add NSEC3PARAM records
				 * with any flags other than OPTOUT.
				 */
				if ((rdata.data[1] &
				     ~DNS_NSEC3FLAG_OPTOUT) != 0)
				{
					update_log(client, zone,
						   LOGLEVEL_PROTOCOL,
						   "attempt to add NSEC3PARAM "
						   "record with non OPTOUT "
						   "flag");
					continue;
				}
#else
				// We can't handle changing NSEC3 on the fly, so refuse
				// the update.
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "attempt to add NSEC3PARAM "
					   "record ignored");
				continue;
#endif
			}
#ifdef ORIGINAL_ISC_CODE
#else
			// If the client is adding a dhcid RR, then there's no need for us to
			// auto-add the dhcid. We're making the simplifying assumption that any
			// TXT added by the dhcpd is a dhcid one.
			if (client->is_infoblox_dhcpd) {
				if (rdata.type == dns_rdatatype_txt ||
				    rdata.type == dns_rdatatype_dhcid) {
					dhcid_add_name = NULL;
				}
			}
#endif

			if ((options & DNS_ZONEOPT_CHECKWILDCARD) != 0 &&
			    dns_name_internalwildcard(name)) {
				char namestr[DNS_NAME_FORMATSIZE];
				dns_name_format(name, namestr,
						sizeof(namestr));
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "warning: ownername '%s' contains "
					   "a non-terminal wildcard", namestr);
			}

			if ((options2 & DNS_ZONEOPT2_CHECKTTL) != 0) {
				maxttl = dns_zone_getmaxttl(zone);
				if (ttl > maxttl) {
					ttl = maxttl;
					update_log(client, zone,
						   LOGLEVEL_PROTOCOL,
						   "reducing TTL to the "
						   "configured max-zone-ttl %d",
						   maxttl);
				}
			}

			if (isc_log_wouldlog(ns_g_lctx, LOGLEVEL_PROTOCOL)) {
				char namestr[DNS_NAME_FORMATSIZE];
				char typestr[DNS_RDATATYPE_FORMATSIZE];
				char rdstr[2048];
				isc_buffer_t buf;
				int len = 0;
				const char *truncated = "";

				dns_name_format(name, namestr, sizeof(namestr));
				dns_rdatatype_format(rdata.type, typestr,
						     sizeof(typestr));
				isc_buffer_init(&buf, rdstr, sizeof(rdstr));
				result = dns_rdata_totext(&rdata, NULL, &buf);
				if (result == ISC_R_NOSPACE) {
					len = (int)isc_buffer_usedlength(&buf);
					truncated = " [TRUNCATED]";
				} else if (result != ISC_R_SUCCESS) {
					snprintf(rdstr, sizeof(rdstr), "[dns_"
						 "rdata_totext failed: %s]",
						 dns_result_totext(result));
					len = strlen(rdstr);
				} else
					len = (int)isc_buffer_usedlength(&buf);
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "adding an RR at '%s' %s %.*s%s",
					   namestr, typestr, len, rdstr,
					   truncated);
			}

			/* Prepare the affected RRset for the addition. */
			{
				add_rr_prepare_ctx_t ctx;
#ifdef ORIGINAL_ISC_CODE
				ctx.db = db;
				ctx.ver = ver;
				ctx.diff = &diff;
#else
#undef db
#undef ver
#undef diff
				ctx.db = dbs[nz];
				ctx.ver = vers[nz];
				ctx.diff = &diffs[nz];
				ctx.update_creation_timestamp_on_unchanged = forced_creation_timestamp_update;
#define db   (dbs[nz])
#define ver  (vers[nz])
#define diff (diffs[nz])
#endif
				ctx.name = name;
				ctx.oldname = name;
				ctx.update_rr = &rdata;
				ctx.update_rr_ttl = ttl;
				ctx.ignore_add = ISC_FALSE;
				dns_diff_init(mctx, &ctx.del_diff);
				dns_diff_init(mctx, &ctx.add_diff);
				CHECK(foreach_rr(db, ver, name, rdata.type,
						 covers, add_rr_prepare_action,
						 &ctx));
#ifdef ORIGINAL_ISC_CODE
#else
				if (ctx.ignore_add && ctx.update_creation_timestamp_on_unchanged) {
					commit_to_db = DB_END_COMMIT;
				}
#endif

				if (ctx.ignore_add) {
					dns_diff_clear(&ctx.del_diff);
					dns_diff_clear(&ctx.add_diff);
				} else {
#ifdef ORIGINAL_ISC_CODE
#else
					// Set the client and zone, to be used in do_diff()
					// for an error message. Reset after the 'failure'
					// label.
					infoblox_update_client = client;
					infoblox_update_zone = zone;
					apply_attempt_count++;
#endif
					result = do_diff(&ctx.del_diff, db, ver,
							 &diff);
					if (result == ISC_R_SUCCESS) {
						result = do_diff(&ctx.add_diff,
								 db, ver,
								 &diff);
					}
					if (result != ISC_R_SUCCESS) {
						dns_diff_clear(&ctx.del_diff);
						dns_diff_clear(&ctx.add_diff);
						goto failure;
					}
					CHECK(update_one_rr(db, ver, &diff,
							    DNS_DIFFOP_ADD,
							    name, ttl, &rdata));
				}
			}
		} else if (update_class == dns_rdataclass_any) {
			if (rdata.type == dns_rdatatype_any) {
				if (isc_log_wouldlog(ns_g_lctx,
						     LOGLEVEL_PROTOCOL))
				{
					char namestr[DNS_NAME_FORMATSIZE];
					dns_name_format(name, namestr,
							sizeof(namestr));
					update_log(client, zone,
						   LOGLEVEL_PROTOCOL,
						   "delete all rrsets from "
						   "name '%s'", namestr);
				}
				if (dns_name_equal(name, zonename)) {
					CHECK(delete_if(type_not_soa_nor_ns_p,
							db, ver, name,
							dns_rdatatype_any, 0,
#ifdef ORIGINAL_ISC_CODE
							&rdata, &diff));
#else
							&rdata, &diff, NULL));
					apply_attempt_count++;
#endif
				} else {
					CHECK(delete_if(type_not_dnssec,
							db, ver, name,
							dns_rdatatype_any, 0,
#ifdef ORIGINAL_ISC_CODE
							&rdata, &diff));
#else
							&rdata, &diff, NULL));
					apply_attempt_count++;
#endif
				}
#ifdef ORIGINAL_ISC_CODE
#else
			} else if (rdata.type == dns_rdatatype_nsec3param) {
				// We can't handle changing NSEC3 on the fly, so refuse
				// the update.
				update_log(client, zone, LOGLEVEL_PROTOCOL,
                                           "attempt to delete an NSEC3PARAM "
					   "record ignored");
				continue;
#endif
			} else if (dns_name_equal(name, zonename) &&
				   (rdata.type == dns_rdatatype_soa ||
				    rdata.type == dns_rdatatype_ns)) {
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "attempt to delete all SOA "
					   "or NS records ignored");
				continue;
			} else {
				if (isc_log_wouldlog(ns_g_lctx,
						     LOGLEVEL_PROTOCOL))
				{
					char namestr[DNS_NAME_FORMATSIZE];
					char typestr[DNS_RDATATYPE_FORMATSIZE];
					dns_name_format(name, namestr,
							sizeof(namestr));
					dns_rdatatype_format(rdata.type,
							     typestr,
							     sizeof(typestr));
					update_log(client, zone,
						   LOGLEVEL_PROTOCOL,
						   "deleting rrset at '%s' %s",
						   namestr, typestr);
				}
				CHECK(delete_if(true_p, db, ver, name,
						rdata.type, covers, &rdata,
#ifdef ORIGINAL_ISC_CODE
						&diff));
#else
						&diff, NULL));
				apply_attempt_count++;
#endif
			}
#ifdef ORIGINAL_ISC_CODE
#else
			if ((rdata.type == dns_rdatatype_a ||
			     rdata.type == dns_rdatatype_aaaa) && dhcid_name == NULL) {
			  // For the situation in which we're interested (dhcpd
			  // removing an A/AAAA record), it's the first name we see
			  // that should be watched for possible DHCID/TXT removal.
                          dhcid_address_type = rdata.type;
                          dhcid_name = name;
			  dhcid_nz = nz;
                        }
#endif
		} else if (update_class == dns_rdataclass_none) {
			char namestr[DNS_NAME_FORMATSIZE];
			char typestr[DNS_RDATATYPE_FORMATSIZE];

			/*
			 * The (name == zonename) condition appears in
			 * RFC2136 ******* but is missing from the pseudocode.
			 */
			if (dns_name_equal(name, zonename)) {
				if (rdata.type == dns_rdatatype_soa) {
					update_log(client, zone,
						   LOGLEVEL_PROTOCOL,
						   "attempt to delete SOA "
						   "ignored");
					continue;
				}
				if (rdata.type == dns_rdatatype_ns) {
					int count;
					CHECK(rr_count(db, ver, name,
						       dns_rdatatype_ns,
						       0, &count));
					if (count == 1) {
						update_log(client, zone,
							   LOGLEVEL_PROTOCOL,
							   "attempt to "
							   "delete last "
							   "NS ignored");
						continue;
					}
				}
			}
			dns_name_format(name, namestr, sizeof(namestr));
			dns_rdatatype_format(rdata.type, typestr,
					     sizeof(typestr));
			update_log(client, zone, LOGLEVEL_PROTOCOL,
				   "deleting an RR at %s %s", namestr, typestr);
			CHECK(delete_if(rr_equal_p, db, ver, name, rdata.type,
#ifdef ORIGINAL_ISC_CODE
					covers, &rdata, &diff));
#else
					covers, &rdata, &diff, zone));
			apply_attempt_count++;
#endif
#ifdef ORIGINAL_ISC_CODE
#else
			if ((rdata.type == dns_rdatatype_a ||
			     rdata.type == dns_rdatatype_aaaa) && dhcid_name == NULL) {
				// For the situation in which we're interested (dhcpd
				// removing an A/AAAA record), it's the first name we see
				// that should be watched for possible DHCID/TXT removal.
				dhcid_address_type = rdata.type;
				dhcid_name = name;
				dhcid_nz = nz;
			}
#endif
		}
	}
	if (result != ISC_R_NOMORE)
		FAIL(result);

#ifdef ORIGINAL_ISC_CODE
#else
	// Should we add a dhcid RR ?  If we should, we know that there isn't
	// one already in existence, so we don't need to check for existence or
	// for a TTL change.
	if (dhcid_add_name) {
		if (dhcid_add_ttl == 0) {
			update_log(client, zone, ISC_LOG_ERROR,
				   "dhcid add error: missing TTL");
			result = ISC_R_FAILURE;
			goto failure;
		}
		nz = dhcid_nz;
		if (isc_log_wouldlog(ns_g_lctx, LOGLEVEL_PROTOCOL)) {
			char namestr[DNS_NAME_FORMATSIZE];
			char typestr[DNS_RDATATYPE_FORMATSIZE];
			char rdstr[2048];
			isc_buffer_t buf;
			int len = 0;
			const char *truncated = "";

			dns_name_format(dhcid_add_name, namestr, sizeof(namestr));
			dns_rdatatype_format(dhcid_add_rdata.type, typestr,
					     sizeof(typestr));
			isc_buffer_init(&buf, rdstr, sizeof(rdstr));
			result = dns_rdata_totext(&dhcid_add_rdata, NULL, &buf);
			if (result == ISC_R_NOSPACE) {
				len = (int)isc_buffer_usedlength(&buf);
				truncated = " [TRUNCATED]";
			} else if (result != ISC_R_SUCCESS) {
				snprintf(rdstr, sizeof(rdstr), "[dns_"
					 "rdata_totext failed: %s]",
					 dns_result_totext(result));
				len = strlen(rdstr);
			} else {
				len = (int)isc_buffer_usedlength(&buf);
			}
			update_log(client, zone, LOGLEVEL_PROTOCOL,
				   "dhcid: adding an RR at '%s' %s %.*s%s",
				   namestr, typestr, len, rdstr, truncated);
		}
		CHECK(update_one_rr(db, ver, &diff,
				   DNS_DIFFOP_ADD,
				   dhcid_add_name, dhcid_add_ttl, &dhcid_add_rdata));
	}
#ifdef INFOBLOX_INCREMENTAL_SIGNING
#else
	if (reset_last_queried) {
		infoblox_reset_last_queried();
		reset_last_queried = ISC_FALSE;
	}
#endif

	if (client->is_infoblox_dhcpd &&
	    dhcid_name != NULL) {
		/* This is an update from our own dhcpd and we have
		 * removed at least one A/AAAA record for
		 * 'dhcid_name'. Based on what address records remain,
		 * we may now automatically remove the TXT or DHCID.
		 *
		 * Such processing has two purposes. First, it lets
		 * our dhcpd omit the additional explicit DDNS
		 * transaction that removes the TXT or DHCID if there
		 * are no address records left. Secondly, it lets us
		 * support dual stack clients where v4 and v6 use a
		 * combination of TXT and DHCID RRs.
		 *
		 * We don't let a failure here spoil the entire
		 * update. It's better to delete the A/AAAA/PTR, and
		 * leave the DHCID behind, than it is to leave all
		 * records in DNS.
		 */

		nz = dhcid_nz;
		infoblox_set_ddns_zone(zone);

		/* If remove_dhcid is set then the remove_dhcid_type
		 * will be removed, both TXT and DHCID if type is
		 * any. */
		isc_boolean_t remove_dhcid = ISC_FALSE;
		isc_boolean_t remove_dhcid_type = dns_rdatatype_none;

		const char *type_str = (dhcid_address_type == dns_rdatatype_a ? "A" : "AAAA");
		const char *other_str = (dhcid_address_type == dns_rdatatype_a ? "AAAA" : "A");

		dhcpd_types_t exists = { 0,0,0,0,0 };
		result = rrset_dhcpd_types(db, ver, dhcid_name, &exists);
		if (result != ISC_R_SUCCESS) {
			/* This shouldn't happen. */
			update_log(client, zone, ISC_LOG_ERROR,
				   "dhcid auto-removal failed: internal error (%s)",
				   isc_result_totext(result));
			remove_dhcid = ISC_FALSE;
		} else if (exists.other) {
			update_log(client, zone, ISC_LOG_ERROR,
				   "dhcid auto-removal failed: non-address, non-dhcid records exist");
			remove_dhcid = ISC_FALSE;
		} else if (!exists.a && !exists.aaaa) {
			/* There are neither A nor AAAA records,
			 * remove any dhcid (TXT and/or DHCID). */
			remove_dhcid = ISC_TRUE;
			remove_dhcid_type = dns_rdatatype_any;
			update_log(client, zone, LOGLEVEL_PROTOCOL,
				   "dhcid auto-removal enabled: no address records exist, all dhcid will be removed");
		} else if (exists.a && exists.aaaa) {
			/* There are both A and AAAA records,
			 * do not remove the dhcid. */
			remove_dhcid = ISC_FALSE;
			update_log(client, zone, LOGLEVEL_PROTOCOL,
				   "dhcid auto-removal disabled: address records of both types exist");
		} else if ((dhcid_address_type == dns_rdatatype_a && exists.a) ||
			   (dhcid_address_type == dns_rdatatype_aaaa && exists.aaaa)) {
			/* There are RRs left of the address record
			 * type in this update, do not remove the
			 * dhcid. */
			remove_dhcid = ISC_FALSE;
			update_log(client, zone, LOGLEVEL_PROTOCOL,
				   "dhcid auto-removal disabled: address record of update type (%s) remains",
				   type_str);
		} else {
			/* At least one other address record of the
			 * type not removed by this update still
			 * exists. */

			if (dhcid_type == dns_rdatatype_none) {
				/* There was no dhcid prerequisite.
				 * This is not supported for
				 * dual-stack mode. */
				remove_dhcid = ISC_FALSE;
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "dhcid auto-removal disabled: address record of other type (%s) exists and no dhcid prerequisite was supplied",
					   other_str);
			} else if (dhcid_type == dns_rdatatype_txt) {
				/* The dhcid type is TXT so we know
				 * that the other address record
				 * cannot be using TXT/dhcid (since
				 * those do not allow v4 and v6
				 * clients to share the same dhcid).
				 * Hence, remove the TXT. */
				remove_dhcid = ISC_TRUE;
				remove_dhcid_type = dns_rdatatype_txt;
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "dhcid auto-removal enabled for TXT: other address record type (%s) exists",
					   other_str);
			} else {
				/* The dhcid type is DHCID, so it is
				 * possible that the other dhcpd also
				 * is using DHCID. In which case we
				 * cannot remove the DHCID. To see if
				 * that might be the case, check for
				 * the existence of a TXT/dhcid. If we
				 * find one, then we assume that the
				 * other dhcpd is using TXT/dhcid, and
				 * we can remove the DHCID. */
				if (!exists.txt) {
					remove_dhcid = ISC_FALSE;
					update_log(client, zone, LOGLEVEL_PROTOCOL,
						   "dhcid auto-removal disabled: other address record type (%s) exists without TXT",
						   other_str);
				} else {
					remove_dhcid = ISC_TRUE;
					remove_dhcid_type = dns_rdatatype_dhcid;
					update_log(client, zone, LOGLEVEL_PROTOCOL,
						   "dhcid auto-removal enabled for DHCID: other address record type (%s) exists with TXT",
						   other_str);
				}
			}
		}

		if (remove_dhcid) {
			char namestr[DNS_NAME_FORMATSIZE];
			dns_name_format(dhcid_name, namestr, sizeof(namestr));

			infoblox_dhcid_match_t match;
			unsigned deleted_dhcid = 0;
			unsigned deleted_txt = 0;
			isc_result_t dhcid_result = ISC_R_SUCCESS;
			isc_result_t txt_result = ISC_R_SUCCESS;
			match.found_dhcid = 0;
			match.deleted_dhcid = 0;
			match.result = ISC_R_SUCCESS;
			match.dhcid_db = db;
			match.dhcid_ver = ver;
			match.dhcid_name = dhcid_name;
			match.dhcid_diff = &diff;
			match.dhcid_zone = zone;

			if (remove_dhcid_type == dns_rdatatype_txt ||
			    remove_dhcid_type == dns_rdatatype_any) {
				result = foreach_rr(db, ver, dhcid_name, dns_rdatatype_txt, 0,
						    find_and_delete_dhcid, &match);
				deleted_txt = match.deleted_dhcid;
				txt_result = (result == ISC_R_SUCCESS || result == ISC_R_NOMORE) ? match.result :
				  result;
				match.deleted_dhcid = 0;
				match.result = ISC_R_SUCCESS;
			}
			if (remove_dhcid_type == dns_rdatatype_dhcid ||
			    remove_dhcid_type == dns_rdatatype_any) {
				result = foreach_rr(db, ver, dhcid_name, dns_rdatatype_dhcid, 0,
						    find_and_delete_dhcid, &match);
				deleted_dhcid = match.deleted_dhcid;
				dhcid_result = (result == ISC_R_SUCCESS || result == ISC_R_NOMORE) ? match.result :
				  result;
			}

			/* Increment counter without caring about the
			 * returned result. The worst case is there is
			 * no change to database and this variable
			 * incremented. */
			apply_attempt_count++;

			if (isc_log_wouldlog(ns_g_lctx, LOGLEVEL_PROTOCOL)) {
				if (txt_result == ISC_R_SUCCESS) {
					if (deleted_txt > 0) {
						update_log (client, zone, LOGLEVEL_PROTOCOL,
							    "dhcid: deleting rrset at '%s' %s",
							    namestr, "TXT");
					}
				} else {
					update_log (client, zone, LOGLEVEL_PROTOCOL,
						    "dhcid: error deleting %s at '%s': %s",
						    "TXT", namestr, isc_result_totext (txt_result));
				}
				if (dhcid_result == ISC_R_SUCCESS) {
					if (deleted_dhcid > 0) {
						update_log (client, zone, LOGLEVEL_PROTOCOL,
							    "dhcid: deleting rrset at '%s' %s",
							    namestr, "DHCID");
					}
				} else {
					update_log (client, zone, LOGLEVEL_PROTOCOL,
						    "dhcid: error deleting %s at '%s': %s",
						    "DHCID", namestr, isc_result_totext (dhcid_result));
				}
			}
		}
	}

	for (nz = 0; nz < num_zone; nz++)
	  {
	    infoblox_set_ddns_zone(zone);
#endif

#ifdef ORIGINAL_ISC_CODE
#else
	infoblox_set_required_signing_state(INFOBLOX_IS_SIGNED_ZONE(zone) ? rss_signed: rss_unsigned);
	perform_dnssec_tasks = INFOBLOX_IS_SIGNED_ZONE(zone);
	if (perform_dnssec_tasks) {
#endif
#ifdef ORIGINAL_ISC_CODE
	/*
	 * Check that any changes to DNSKEY/NSEC3PARAM records make sense.
	 * If they don't then back out all changes to DNSKEY/NSEC3PARAM
	 * records.
	 */
	if (! ISC_LIST_EMPTY(diff.tuples))
		CHECK(check_dnssec(client, zone, db, ver, &diff));
#else
	// Updates to DNSKEY/NSEC3PARAM are not permitted so no check is required.
	UNUSED(check_dnssec);
	} // if (perform_dnssec_tasks)
#endif

#ifdef ORIGINAL_ISC_CODE
	if (! ISC_LIST_EMPTY(diff.tuples)) {
		unsigned int errors = 0;
		CHECK(dns_zone_nscheck(zone, db, ver, &errors));
		if (errors != 0) {
			update_log(client, zone, LOGLEVEL_PROTOCOL,
				   "update rejected: post update name server "
				   "sanity check failed");
			result = DNS_R_REFUSED;
			goto failure;
		}
	}
#else
	// Sanity checking the NS records costs performance.
#endif
	if (! ISC_LIST_EMPTY(diff.tuples)) {
		result = dns_zone_cdscheck(zone, db, ver);
		if (result == DNS_R_BADCDS || result == DNS_R_BADCDNSKEY) {
			update_log(client, zone, LOGLEVEL_PROTOCOL,
				   "update rejected: bad %s RRset",
				   result == DNS_R_BADCDS ? "CDS" : "CDNSKEY");
			result = DNS_R_REFUSED;
			goto failure;
		}
		if (result != ISC_R_SUCCESS)
			goto failure;

	}

	/*
	 * If any changes were made, increment the SOA serial number,
	 * update RRSIGs and NSECs (if zone is secure), and write the update
	 * to the journal.
	 */
#ifdef ORIGINAL_ISC_CODE
	if (! ISC_LIST_EMPTY(diff.tuples)) {
#else
	// For the ZDB, we may get here with an empty diff.tuples list because
	// we have the following situation:
	// (1) There was a delete/add of the exact same item.
	// (2) The delete and add were applied to the RBT and to the OneDB.
	// (3) The delete and add were removed from the diff because they
	//     cancel out, leaving an empty diff.
	// (4) Since the diff is empty, there will be no change to the SOA
	//     serial from the code below.
	// (5) However, the delete/add have been applied to the OneDB, and
	//     the MMDNS OneDB code will detect the "missing" SOA serial
	//     number change, and apply one on its own.
	// (6) The SOA serial number in the RBT/zone file/journal file will
	//     differ from the one in the OneDB, which is not good, and
	//     likely causes a zone reload from the OneDB when detected.
	//
	// There are a number of possible ways to fix this:
	// (a) Add code to the OneDB to detect changes that cancel out,
	//     and don't insist on an SOA update if that occurs.
	// (b) Don't apply diff optimization for ZDB zones.
	// (c) Defer the OneDB update until after the diff optimization
	//     has been done.
	// (d) Always update the SOA serial for a ZDB zone, even if the
	//     diff.tuples is empty at this point.
	// (e) Track the attempt of changes for a ZDB zone and update SOA
	//     if there is any attempt to change ZDB.
	//
	// Of the above, (a) and (c) are relatively complicated in terms
	// of coding. Alternative (c) in particular implies a new way of
	// applying changes to the OneDB, since the current method (which
	// relies on the ISC code to work out exactly which change to make,
	// something it already does in the code path that modifies the RBT)
	// wouldn't work.
	// Alternative (b) is relatively simple, but carries the problem
	// of generating journal entries (and hence IXFR data) that contain
	// deletes/adds that cancel out, which may cause log messages
	// on slaves.
	// Alternative (d) is the simplest one in terms of coding. The
	// effect will be to unnecessarily increment the SOA serial number,
	// and hence to generate a journal entry that just updates the
	// SOA.
	// Alternative (e) is a compromise one among above solutions. The procedure
	// in this function performs pre-condition check for ignorable
	// changes in incoming update request. There is "no change" applied if all
	// updates are ignored (eg, duplicate update to the same dns name will be
	// ignored and update returns successfully). Here apply_attempt_count is used
	// to track if there is any attempt to change zone database, and skip
	// SOA serial update if no change happens. Note "no change" detection in (e)
	// may not complete considering we are not tracking routines below the
	// function. Moreover, in the presence of multiple zones in incoming update,
	// all zones' SOA will be updated if any zone has an attempt of change.
	//
	if (! ISC_LIST_EMPTY(diff.tuples) || (dns_zone_is_zdb(zone) && apply_attempt_count)) {
		commit_to_db = DB_END_COMMIT;
#endif
		char *journalfile;
		dns_journal_t *journal;
		isc_boolean_t has_dnskey;

		/*
		 * Increment the SOA serial, but only if it was not
		 * changed as a result of an update operation.
		 */
		if (! soa_serial_changed) {
#ifdef ORIGINAL_ISC_CODE
#else
			// Tell infoblox_dns_findnode() that we're incrementing
			// the SOA serial, so that it is allowed to use a cached
			// zone apex node.
			infoblox_incrementing_soa_serial_set (1);
			// We now use the SOA serial number update to trigger
			// update of the last_queried member of the OneDB
			// zone structure.
			if (infoblox_perhaps_last_queried()) {
				reset_last_queried = infoblox_setup_last_queried(zone, ISC_FALSE);
			}
#endif
			CHECK(update_soa_serial(db, ver, &diff, mctx,
				       dns_zone_getserialupdatemethod(zone)));
#ifdef ORIGINAL_ISC_CODE
#else
			if (reset_last_queried) {
				infoblox_reset_last_queried();
				reset_last_queried = ISC_FALSE;
			}
			infoblox_incrementing_soa_serial_set (0);
#endif
		}

#ifdef ORIGINAL_ISC_CODE
#else
		if (need_check_mx)
#endif
		CHECK(check_mx(client, zone, db, ver, &diff));

#ifdef ORIGINAL_ISC_CODE
#else
		if (perform_dnssec_tasks) {
#endif
		CHECK(remove_orphaned_ds(db, ver, &diff));

#ifdef ORIGINAL_ISC_CODE
		CHECK(rrset_exists(db, ver, zonename, dns_rdatatype_dnskey,
				   0, &has_dnskey));

#define ALLOW_SECURE_TO_INSECURE(zone) \
	((dns_zone_getoptions(zone) & DNS_ZONEOPT_SECURETOINSECURE) != 0)

		CHECK(rrset_exists(db, oldver, zonename, dns_rdatatype_dnskey,
				   0, &had_dnskey));
		if (!ALLOW_SECURE_TO_INSECURE(zone)) {
			if (had_dnskey && !has_dnskey) {
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "update rejected: all DNSKEY "
					   "records removed and "
					   "'dnssec-secure-to-insecure' "
					   "not set");
				result = DNS_R_REFUSED;
				goto failure;
			}
		}

		CHECK(rollback_private(db, privatetype, ver, &diff));

		CHECK(add_signing_records(db, privatetype, ver, &diff));

		CHECK(add_nsec3param_records(client, zone, db, ver, &diff));

		if (had_dnskey && !has_dnskey) {
			/*
			 * We are transitioning from secure to insecure.
			 * Cause all NSEC3 chains to be deleted.  When the
			 * the last signature for the DNSKEY records are
			 * remove any NSEC chain present will also be removed.
			 */
			 CHECK(dns_nsec3param_deletechains(db, ver, zone,
							   ISC_TRUE, &diff));
		} else if (has_dnskey && isdnssec(db, ver, privatetype)) {
#else
		// Updates to DNSKEY/NSEC3PARAM are not permitted.
		UNUSED(rollback_private);
		UNUSED(add_signing_records);
		UNUSED(add_nsec3param_records);
		UNUSED(has_dnskey);
		if (ISC_TRUE) {
#endif

#if defined(ORIGINAL_ISC_CODE) || !defined(INFOBLOX_INCREMENTAL_SIGNING)
			isc_uint32_t interval;
			dns_update_log_t log;

			interval = dns_zone_getsigvalidityinterval(zone);
			log.func = update_log_cb;
			log.arg = client;
			result = dns_update_signatures(&log, zone, db, oldver,
						       ver, &diff, interval);
#else
                        result = ISC_R_UNEXPECTED; /* never get here */
#endif
			if (result != ISC_R_SUCCESS) {
				update_log(client, zone,
					   ISC_LOG_ERROR,
					   "RRSIG/NSEC/NSEC3 update failed: %s",
					   isc_result_totext(result));
				goto failure;
			}
		}
#ifdef ORIGINAL_ISC_CODE
#else
		} // if (perform_dnssec_tasks)

		if (dns_zone_is_zdb(zone)) {
			result = infoblox_zdb_update_ibzone(db, ver, &diff,
							    NULL, ISC_FALSE);
			if (result != ISC_R_SUCCESS)
				goto failure;
		}
#endif

		maxrecords = dns_zone_getmaxrecords(zone);
		if (maxrecords != 0U) {
			result = dns_db_getsize(db, ver, &records, NULL);
			if (result == ISC_R_SUCCESS && records > maxrecords) {
				update_log(client, zone, ISC_LOG_ERROR,
					   "records in zone (%"
					   ISC_PRINT_QUADFORMAT
					   "u) exceeds max-records (%u)",
					   records, maxrecords);
				result = DNS_R_TOOMANYRECORDS;
				goto failure;
			}
		}

		journalfile = dns_zone_getjournal(zone);
		if (journalfile != NULL) {
			update_log(client, zone, LOGLEVEL_DEBUG,
				   "writing journal %s", journalfile);

#ifdef ORIGINAL_ISC_CODE
#else
			/* Update diff->tuples with diff->bhs_tuples */
			if (!ISC_LIST_EMPTY(diff.bhs_tuples)) {
				infoblox_update_diff(&diff);
			}
			{
			  unsigned try_again = 1;

try_journal_write_again:
#endif
			journal = NULL;
			result = dns_journal_open(mctx, journalfile,
						  DNS_JOURNAL_CREATE, &journal);
			if (result != ISC_R_SUCCESS)
#ifdef ORIGINAL_ISC_CODE
#else
			{
				/*
				 * possibly we just lost access to filesystem;
				 * skip journal update but still accept update
				 */
				if (result == ISC_R_NOPERM) {
					infoblox_dns_zone_disable_journal_and_masterfile_updates(zone);
					result = ISC_R_SUCCESS; // just in case
					update_log(client, zone, ISC_LOG_WARNING,
						   "failed to open journal %s due to insufficient file permissions; "
						   "journal and IXFR will be disabled; "
						   "try to restart DNS service to fix the issue",
						   journalfile);
					goto skip_journal;
				} else
#endif
				FAILS(result, "journal open failed");
#ifdef ORIGINAL_ISC_CODE
#else
			}
#endif

			result = dns_journal_write_transaction(journal, &diff);
#ifdef ORIGINAL_ISC_CODE
#else
			if ((result == ISC_R_RANGE ||
                             result == ISC_R_NOSPC ||    
                             result == ISC_R_FBIG) && try_again)
			  {
			    int ret;
			    // A non-DDNS update has incremented the SOA
			    // serial, so our journal doesn't have the
			    // information we need for IXFR anymore. That
			    // means the journal is useless to us, so delete
			    // it and try again.
			    dns_journal_destroy(&journal);
			    // Remove the journal file
			    ret = unlink (journalfile);
			    if (ret == 0)
			      {
				try_again = 0;
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "Resetting journal \"%s\"",
					   journalfile);
				goto try_journal_write_again;
			      }
			    else
			      {
				update_log(client, zone, LOGLEVEL_PROTOCOL,
					   "Error unlinking journal file \"%s\" "
					   "for reset: %s",
					   journalfile, strerror (errno));
				// Restore original failure code from
				// dns_journal_write_transaction()
				result = ISC_R_UNEXPECTED;
			      }
			  }
			}
#endif
			if (result != ISC_R_SUCCESS) {
#ifdef ORIGINAL_ISC_CODE
#else
			  if (journal != NULL)
#endif
				dns_journal_destroy(&journal);
				FAILS(result, "journal write failed");
			}

			dns_journal_destroy(&journal);
		}

#ifdef ORIGINAL_ISC_CODE
#else
skip_journal:
#endif
		/*
		 * XXXRTH  Just a note that this committing code will have
		 *	   to change to handle databases that need two-phase
		 *	   commit, but this isn't a priority.
		 */
		update_log(client, zone, LOGLEVEL_DEBUG,
			   "committing update transaction");

		dns_db_closeversion(db, &ver, ISC_TRUE);

		/*
		 * Mark the zone as dirty so that it will be written to disk.
		 */
		dns_zone_markdirty(zone);

		/*
		 * Notify slaves of the change we just made.
		 */
#ifdef ORIGINAL_ISC_CODE
		dns_zone_notify(zone);
#else
		// Don't send notification until we've committed the
		// write transaction. Otherwise, it would be possible
		// for a client to receive notification, and request
		// and AXFR (or IXFR) that wouldn't contain the new
		// (or all of the new, in the case of an IXFR) data.
		send_notification[nz] = ISC_TRUE;
#endif

#ifdef ORIGINAL_ISC_CODE
#else
		if (perform_dnssec_tasks) {
#endif
		/*
		 * Cause the zone to be signed with the key that we
		 * have just added or have the corresponding signatures
		 * deleted.
		 *
		 * Note: we are already committed to this course of action.
		 */
		for (tuple = ISC_LIST_HEAD(diff.tuples);
		     tuple != NULL;
		     tuple = ISC_LIST_NEXT(tuple, link)) {
			isc_region_t r;
			dns_secalg_t algorithm;
			isc_uint16_t keyid;

			if (tuple->rdata.type != dns_rdatatype_dnskey)
				continue;

			dns_rdata_tostruct(&tuple->rdata, &dnskey, NULL);
			if ((dnskey.flags &
			     (DNS_KEYFLAG_OWNERMASK|DNS_KEYTYPE_NOAUTH))
				 != DNS_KEYOWNER_ZONE)
				continue;

			dns_rdata_toregion(&tuple->rdata, &r);
			algorithm = dnskey.algorithm;
			keyid = dst_region_computeid(&r, algorithm);

			result = dns_zone_signwithkey(zone, algorithm, keyid,
					ISC_TF(tuple->op == DNS_DIFFOP_DEL));
			if (result != ISC_R_SUCCESS) {
				update_log(client, zone, ISC_LOG_ERROR,
					   "dns_zone_signwithkey failed: %s",
					   dns_result_totext(result));
			}
		}

		/*
		 * Cause the zone to add/delete NSEC3 chains for the
		 * deferred NSEC3PARAM changes.
		 *
		 * Note: we are already committed to this course of action.
		 */
		for (tuple = ISC_LIST_HEAD(diff.tuples);
		     tuple != NULL;
		     tuple = ISC_LIST_NEXT(tuple, link)) {
			unsigned char buf[DNS_NSEC3PARAM_BUFFERSIZE];
			dns_rdata_t rdata = DNS_RDATA_INIT;
			dns_rdata_nsec3param_t nsec3param;

			if (tuple->rdata.type != privatetype ||
			    tuple->op != DNS_DIFFOP_ADD)
				continue;

			if (!dns_nsec3param_fromprivate(&tuple->rdata, &rdata,
						   buf, sizeof(buf)))
				continue;
			dns_rdata_tostruct(&rdata, &nsec3param, NULL);
			if (nsec3param.flags == 0)
				continue;

			result = dns_zone_addnsec3chain(zone, &nsec3param);
			if (result != ISC_R_SUCCESS) {
				update_log(client, zone, ISC_LOG_ERROR,
					   "dns_zone_addnsec3chain failed: %s",
					   dns_result_totext(result));
			}
		}
#ifdef ORIGINAL_ISC_CODE
#else
		} // if (perform_dnssec_tasks)
#endif
	} else {
		update_log(client, zone, LOGLEVEL_DEBUG, "redundant request");
		dns_db_closeversion(db, &ver, ISC_TRUE);
	}
#ifdef ORIGINAL_ISC_CODE
#else
		// End loop over zones 
	  }
	if (db_open) {
		result = infoblox_db_operation_end (commit_to_db);
		db_open = 0;
		if (result != ISC_R_SUCCESS) {
			if (commit_to_db == DB_END_COMMIT) {
				isc_result_t aresult;
				infoblox_log(1, "Update action: Error committing DB transaction");
				/* Change to the zone has already been committed and we can not just
				 * roll it back. Reloading zone from database is a way to recover the
				 * zone at the cost that the change made to the zone in this DDNS
				 * update is lost.
				 */
				for(nz = 0; nz < num_zone; nz++) {
					if (dns_zone_is_zdb(zone)) {
						update_log(client, zone, ISC_LOG_ERROR,
								      "Request reloading zone due to committing DB failure");
						/* Force zone reloading request which should succeed execpt for
						 * failure of event allocation which indicates the system is tight
						 * in memory.
						 */
						(void)infoblox_zdb_full_reload(zone, ISC_TRUE);
						update_log(client, zone, ISC_LOG_ERROR, "Failed to request reloading zone");
					}
				}
				aresult = infoblox_db_operation_end(DB_END_ABORT);
				/* We don't expect ABORT to ever fail.
				 * And, if even aborting failed, keeping going
				 * with the open transaction would even cause
				 * bigger system-wide troubles. */
				RUNTIME_CHECK(aresult == ISC_R_SUCCESS);
			}
			else {
				infoblox_log(1, "Update action: Warning aborting DB transaction");
			}
			CHECK(result);
		}
	}
	if (zrq_drain_locked) {
		zrq_consumer_drain_unlock();
		zrq_drain_locked = 0;
	}

	// Now that we've committed the update(s) to the DB, we need to spin
	// through the names being updated, and mark them expired. We couldn't
	// usefully do this before committing, since a concurrent lookup might
	// have brought the (pre-update) data back in, and marked the node up
	// to date.
	//
	// Note that there still is a race condition in the following case:
	// * Thread A is updating an item with the domain name D.
	// * Thread B has a read txn active.
	// * Thread A commits its write txn, and marks item D expired
	// * Thread B brings in item D, and marks it up to date. This
	//   will be the pre-update item D, and that's fine for the
	//   current DNS operation for B.
	// * A new DNS request for D will be satisfied out of the cache,
	//   and will continue to be the pre-update data, at worst until
	//   the fail-safe timer expires (60 seconds).
	//
	// There is no race for zone transfers, since they get all their
	// data directly from the DB, or from the journal.
	//
	// This race could be resolved, but at the cost of a fair bit of
	// extra code. The txn_serial (see infoblox_db.c) could be changed
	// to only increment when a write txn commits, and a txn_serial field
	// added to the handle for each thread, and set equal to the current
	// txn_serial. When a thread wants to refresh a node, it would only be
	// allowed to do so provided that its handle txn_serial matches the
	// current global one (indicating that the read txn was started
	// after the most recent write txn committed). Otherwise, the
	// thread would either use the stale node, or obtain data from the
	// OneDB, but not update the node.
	//
	// On the whole, the coding complexity doesn't seem worth plugging
	// this small hole, which requires a concurrent lookup and update
	// for the same item. A sequence where an update for D is followed
	// by a lookup for D will work fine, as long as there's no third
	// party involved that looks up D during the update.
	for (result = dns_message_firstname(request, DNS_SECTION_UPDATE);
             result == ISC_R_SUCCESS;
             result = dns_message_nextname(request, DNS_SECTION_UPDATE))
	  {
	    dns_name_t *name = NULL;
	    dns_rdata_t rdata = DNS_RDATA_INIT;
	    dns_ttl_t ttl;
	    dns_rdataclass_t update_class;

	    nz = 0;
	    get_current_rr(request, DNS_SECTION_UPDATE, zoneclass,
			   &name, &rdata, &covers, &ttl, &update_class);

	    // Find the zone
	    for (nz = 0; nz < num_zone; nz++)
	      {
		if (dns_name_issubdomain(name, zonenames[nz]))
		  break;
	      }

	    if (nz >= num_zone)
	      {
		// We've already scanned for zones, so this shouldn't
		// happen...
		nz = 0;
		FAILC(DNS_R_NOTZONE,
		      "update RR is outside zone");
	      }

	    infoblox_set_ddns_zone(zone);
	    if (nz != 0)
	      {
		// In the get_current_rr(), we assumed nz=0, but now we
		// know better, and need to fix up rdata.rdclass. Recall
		// that 'zoneclass' is a macro, and varies with 'nz'.
		rdata.rdclass = zoneclass;
	      }
	  }

	// Now we can send notifications.
	for (nz = 0; nz < num_zone; nz++)
	  {
	    infoblox_set_ddns_zone(zone);
	    if (send_notification[nz])
	      {
		dns_zone_notify(zone);
	      }
	  }
#endif
	result = ISC_R_SUCCESS;
	goto common;

 failure:
#ifdef ORIGINAL__ISC_CODE
#else
	infoblox_update_client = NULL;
	infoblox_update_zone = NULL;

	// Loop over versions
	for (nz = 0; nz < num_zone; nz++)
	  {
	    infoblox_set_ddns_zone(zone);
#endif
	/*
	 * The reason for failure should have been logged at this point.
	 */
	if (ver != NULL) {
		update_log(client, zone, LOGLEVEL_DEBUG,
			   "rolling back");
		dns_db_closeversion(db, &ver, ISC_FALSE);
	}
#ifdef  ORIGINAL_ISC_CODE
#else
	  }
#endif

 common:
	dns_diff_clear(&temp);
#ifdef	ORIGINAL_ISC_CODE
	dns_diff_clear(&diff);

	if (oldver != NULL)
		dns_db_closeversion(db, &oldver, ISC_FALSE);

#ifdef INFOBLOX_INCREMENTAL_SIGNING
#else
	if (reset_last_queried)
		infoblox_reset_last_queried();
#endif
#else
	infoblox_set_required_signing_state(rss_dont_care);
	infoblox_prereq_check_set (0);
	infoblox_set_ddns_principal(NULL);

	for (nz = 0; nz < num_zone; nz++)
	  {
	    infoblox_set_ddns_zone(zone);
	    dns_diff_clear(&diff);
	    if (oldver != NULL)
	      dns_db_closeversion(db, &oldver, ISC_FALSE);
#endif

	if (db != NULL)
		dns_db_detach(&db);

	if (ssutable != NULL)
		dns_ssutable_detach(&ssutable);

#ifdef ORIGINAL_ISC_CODE
#else
		if (nz != 0)
			dns_zone_detach(&zone);
	  }
	infoblox_set_ddns_zone(NULL);

	if (db_open)
	  {
	    (void) infoblox_db_operation_end(DB_END_ABORT);
	    db_open = 0;
	  }
	if (zrq_drain_locked) {
		zrq_consumer_drain_unlock();
		zrq_drain_locked = 0;
	}
#endif

	isc_task_detach(&task);
	uev->result = result;
#ifdef ORIGINAL_ISC_CODE
	if (zone != NULL)
		INSIST(uev->zone == zone); /* we use this later */
#else
#undef zone
	if (zones[0] != NULL)
		INSIST(uev->zone == zones[0]); /* we use this later */
#define zone (zones[nz])
#endif
	uev->ev_type = DNS_EVENT_UPDATEDONE;
	uev->ev_action = updatedone_action;
	isc_task_send(client->task, &event);

#ifdef ORIGINAL_ISC_CODE
	INSIST(ver == NULL);
#else
	for (nz = 0; nz < num_zone; nz++) {
		INSIST(vers[nz] == NULL);
	}
#endif
	INSIST(event == NULL);
}

#ifdef ORIGINAL_ISC_CODE
#else
#undef zone
#undef db
#undef oldver
#undef ver
#undef diff
#undef soa_serial_changed
#undef zoneclass
#undef zonename
#undef ssutable
#endif

static void
updatedone_action(isc_task_t *task, isc_event_t *event) {
	update_event_t *uev = (update_event_t *) event;
	ns_client_t *client = (ns_client_t *) event->ev_arg;

	UNUSED(task);

	INSIST(event->ev_type == DNS_EVENT_UPDATEDONE);
	INSIST(task == client->task);

	INSIST(client->nupdates > 0);
	switch (uev->result) {
	case ISC_R_SUCCESS:
		inc_stats(uev->zone, dns_nsstatscounter_updatedone);
		break;
	case DNS_R_REFUSED:
		inc_stats(uev->zone, dns_nsstatscounter_updaterej);
		break;
	default:
		inc_stats(uev->zone, dns_nsstatscounter_updatefail);
		break;
	}
	if (uev->zone != NULL)
		dns_zone_detach(&uev->zone);
	client->nupdates--;
#ifdef ORIGINAL_ISC_CODE
	respond(client, uev->result);
#else
	/*
	 * The update action results in DNS_R_DROP if it contains an additional
	 * zone to update but the zone hasn't completed initial loading.  In
	 * this case we should drop the request, rather than returning an error,
	 * hoping the client will resend it while we are prioritizing the load
	 * of the zone.
	 * Note: we assume DNS_R_DROP can happen only in that case.  If this
	 * is not going to be the case we'll probably need to introduce our
	 * own result code for this.
	 */
        if (uev->result == DNS_R_DROP)
                ns_client_next(client, uev->result);
        else
                respond(client, uev->result);
	// Unset the flag we set in send_update_event()
	(void) pthread_mutex_lock (&client->view_lock);
	client->retain_view = 0;
	(void) pthread_mutex_unlock (&client->view_lock);
#endif
	isc_event_free(&event);
	ns_client_detach(&client);
}

/*%
 * Update forwarding support.
 */

static void
forward_fail(isc_task_t *task, isc_event_t *event) {
	ns_client_t *client = (ns_client_t *)event->ev_arg;

	UNUSED(task);

	INSIST(client->nupdates > 0);
	client->nupdates--;
	respond(client, DNS_R_SERVFAIL);
	isc_event_free(&event);
	ns_client_detach(&client);
}


static void
forward_callback(void *arg, isc_result_t result, dns_message_t *answer) {
	update_event_t *uev = arg;
	ns_client_t *client = uev->ev_arg;
	dns_zone_t *zone = uev->zone;

	if (result != ISC_R_SUCCESS) {
		INSIST(answer == NULL);
		uev->ev_type = DNS_EVENT_UPDATEDONE;
		uev->ev_action = forward_fail;
		inc_stats(zone, dns_nsstatscounter_updatefwdfail);
	} else {
		uev->ev_type = DNS_EVENT_UPDATEDONE;
		uev->ev_action = forward_done;
		uev->answer = answer;
		inc_stats(zone, dns_nsstatscounter_updaterespfwd);
	}
	isc_task_send(client->task, ISC_EVENT_PTR(&uev));
	dns_zone_detach(&zone);
}

static void
forward_done(isc_task_t *task, isc_event_t *event) {
	update_event_t *uev = (update_event_t *) event;
	ns_client_t *client = (ns_client_t *)event->ev_arg;

	UNUSED(task);

	INSIST(client->nupdates > 0);
	client->nupdates--;
	ns_client_sendraw(client, uev->answer);
	dns_message_destroy(&uev->answer);
	isc_event_free(&event);
	ns_client_detach(&client);
}

static void
forward_action(isc_task_t *task, isc_event_t *event) {
	update_event_t *uev = (update_event_t *) event;
	dns_zone_t *zone = uev->zone;
	ns_client_t *client = (ns_client_t *)event->ev_arg;
	isc_result_t result;

	result = dns_zone_forwardupdate(zone, client->message,
					forward_callback, event);
	if (result != ISC_R_SUCCESS) {
		uev->ev_type = DNS_EVENT_UPDATEDONE;
		uev->ev_action = forward_fail;
		isc_task_send(client->task, &event);
		inc_stats(zone, dns_nsstatscounter_updatefwdfail);
		dns_zone_detach(&zone);
	} else
		inc_stats(zone, dns_nsstatscounter_updatereqfwd);
	isc_task_detach(&task);
}

static isc_result_t
send_forward_event(ns_client_t *client, dns_zone_t *zone) {
	char namebuf[DNS_NAME_FORMATSIZE];
	char classbuf[DNS_RDATACLASS_FORMATSIZE];
	isc_result_t result = ISC_R_SUCCESS;
	update_event_t *event = NULL;
	isc_task_t *zonetask = NULL;
	ns_client_t *evclient;

	/*
	 * This may take some time so replace this client.
	 */
	if (!client->mortal && (client->attributes & NS_CLIENTATTR_TCP) == 0)
		CHECK(ns_client_replace(client));

	event = (update_event_t *)
		isc_event_allocate(client->mctx, client, DNS_EVENT_UPDATE,
				   forward_action, NULL, sizeof(*event));
	if (event == NULL)
		FAIL(ISC_R_NOMEMORY);
	event->zone = zone;
	event->result = ISC_R_SUCCESS;

	evclient = NULL;
	ns_client_attach(client, &evclient);
	INSIST(client->nupdates == 0);
	client->nupdates++;
	event->ev_arg = evclient;

	dns_name_format(dns_zone_getorigin(zone), namebuf,
			sizeof(namebuf));
	dns_rdataclass_format(dns_zone_getclass(zone), classbuf,
			      sizeof(classbuf));

	ns_client_log(client, NS_LOGCATEGORY_UPDATE, NS_LOGMODULE_UPDATE,
		      LOGLEVEL_PROTOCOL, "forwarding update for zone '%s/%s'",
		      namebuf, classbuf);

	dns_zone_gettask(zone, &zonetask);
	isc_task_send(zonetask, ISC_EVENT_PTR(&event));

 failure:
	if (event != NULL)
		isc_event_free(ISC_EVENT_PTR(&event));
	return (result);
}
