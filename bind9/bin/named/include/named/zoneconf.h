/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

/* $Id$ */

#ifndef NS_ZONECONF_H
#define NS_ZONECONF_H 1

/*! \file */

#include <isc/lang.h>
#include <isc/types.h>

#include <isccfg/aclconf.h>
#include <isccfg/cfg.h>

ISC_LANG_BEGINDECLS

#ifdef	ORIGINAL_ISC_CODE
#else
/*
 * 5 (currently) leaves room for the three configs, defaults, and a NULL.
 */
#define INFOBLOX_NS_ZONE_MAKE_MAPS_SIZE (5)
/*
 * Create the maps array for use in ns_config functions.
 * This code is a mirror of the code in ns_zone_configure()
 * so as to reduce the impact on the function signature.
 *
 * maps must be an array of INFOBLOX_NS_ZONE_MAKE_MAPS_SIZE.
 *
 * Implementation note: This function is only used in one place in server.c
 * however it is in zoneconf.c to make it closer to the code it is mirroring
 * in ns_zone_configure().
 */
void
infoblox_ns_zone_make_maps(cfg_obj_t const *maps[],
                           cfg_obj_t const *const config,
                           cfg_obj_t const *const vconfig,
                           cfg_obj_t const *const zconfig);
#endif

isc_result_t
ns_zone_configure(const cfg_obj_t *config, const cfg_obj_t *vconfig,
		  const cfg_obj_t *zconfig, cfg_aclconfctx_t *ac,
		  dns_zone_t *zone, dns_zone_t *raw);
/*%<
 * Configure or reconfigure a zone according to the named.conf
 * data in 'cctx' and 'czone'.
 *
 * The zone origin is not configured, it is assumed to have been set
 * at zone creation time.
 *
 * Require:
 * \li	'lctx' to be initialized or NULL.
 * \li	'cctx' to be initialized or NULL.
 * \li	'ac' to point to an initialized ns_aclconfctx_t.
 * \li	'czone' to be initialized.
 * \li	'zone' to be initialized.
 */

isc_boolean_t
ns_zone_reusable(dns_zone_t *zone, const cfg_obj_t *zconfig);
/*%<
 * If 'zone' can be safely reconfigured according to the configuration
 * data in 'zconfig', return ISC_TRUE.  If the configuration data is so
 * different from the current zone state that the zone needs to be destroyed
 * and recreated, return ISC_FALSE.
 */

isc_result_t
ns_zone_configure_writeable_dlz(dns_dlzdb_t *dlzdatabase, dns_zone_t *zone,
				dns_rdataclass_t rdclass, dns_name_t *name);
/*%>
 * configure a DLZ zone, setting up the database methods and calling
 * postload to load the origin values
 *
 * Require:
 * \li	'dlzdatabase' to be a valid dlz database
 * \li	'zone' to be initialized.
 * \li	'rdclass' to be a valid rdataclass
 * \li	'name' to be a valid zone origin name
 */

ISC_LANG_ENDDECLS

#endif /* NS_ZONECONF_H */
