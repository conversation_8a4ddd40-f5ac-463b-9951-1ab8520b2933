/*
 * Copyright (c) 2017 Infoblox Inc. All Rights Reserved.
 */

#include <isc/types.h>
#include <isccfg/namedconf.h>
#include <dns/infoblox_rpz_hitrate.h>

isc_result_t
infoblox_config_rpz_hitrate(const cfg_obj_t *maps[3], isc_mem_t *mctx,
			    isc_task_t *task, isc_timermgr_t *timermgr,
			    infoblox_threshold_monitor_t **monitorp);

isc_result_t
infoblox_configure_smartcache(const cfg_obj_t *maps[3]);
