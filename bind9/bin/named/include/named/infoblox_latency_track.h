/*
 * Copyright (c) 2013 Infoblox Inc. All Rights Reserved.
 */

#ifndef INFOBLOX_LATENCY_TRACK_H
#define INFOBLOX_LATENCY_TRACK_H

#include <isc/types.h>
#include <isc/time.h>
#include <dns/types.h>
#include <dns/infoblox_latency_tree.h>

typedef struct infoblox_lt_mgr infoblox_lt_mgr_t;

extern infoblox_lt_mgr_t *ib_ltmgr;

isc_result_t
infoblox_lt_mgr_create(isc_mem_t *mctx, infoblox_lt_tree_t *tree,
		       isc_taskmgr_t *taskmgr, dns_requestmgr_t *requestmgr,
		       infoblox_lt_mgr_t **mgrp);
void infoblox_lt_mgr_destroy(infoblox_lt_mgr_t **mgrp);

isc_result_t infoblox_dump_server_latencies(infoblox_lt_mgr_t *mgr);

void infoblox_lt_mgr_set_requestmgr(infoblox_lt_mgr_t *mgr, dns_requestmgr_t *requestmgr);
isc_result_t ibtest_update_latency_wrapper(infoblox_lt_mgr_t *mgr,
					   infoblox_lt_info_t *info);
isc_task_t *ibtest_lt_mgr_gettask(infoblox_lt_mgr_t *mgr);
isc_uint64_t infoblox_latency_calc(const isc_time_t *start,
				   const isc_time_t *end,
				   isc_uint64_t old, isc_boolean_t penalize);
#endif
