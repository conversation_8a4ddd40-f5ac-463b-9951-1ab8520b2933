/*
 * Copyright (c) 2013 Infoblox Inc. All Rights Reserved.
 */

#ifndef NAMED_INFOBLOX_QUERY_UTIL_H
#define NAMED_INFOBLOX_QUERY_UTIL_H 1

/*! \file */

#include <isc/buffer.h>
#include <isc/mem.h>
#include <isc/netaddr.h>
#include <isc/region.h>
#include <isc/result.h>
#include <isc/sockaddr.h>
#include <isc/util.h>

/* Construct the label to be prepended to the original qname. */
isc_result_t
infoblox_make_rewrite_label(isc_mem_t *mctx, isc_buffer_t **bufp,
			    const char *prefix, unsigned int prefixlen,
			    isc_sockaddr_t *client_addr);

#endif	/* NAMED_INFOBLOX_QUERY_UTIL_H */
