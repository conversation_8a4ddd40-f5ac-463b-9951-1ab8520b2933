/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

#ifndef NAMED_QUERY_H
#define NAMED_QUERY_H 1

/*! \file */

#include <isc/types.h>
#include <isc/buffer.h>
#include <isc/netaddr.h>

#include <dns/rdataset.h>
#include <dns/rpz.h>
#include <dns/types.h>

#include <named/types.h>

/*% nameserver database version structure */
typedef struct ns_dbversion {
	dns_db_t			*db;
	dns_dbversion_t			*version;
	isc_boolean_t			acl_checked;
	isc_boolean_t			queryok;
	ISC_LINK(struct ns_dbversion)	link;
} ns_dbversion_t;

#ifdef ORIGINAL_ISC_CODE
#else
typedef struct ib_prefetchinfo {
	dns_name_t                      	ib_prefetchname;
	dns_rdatatype_t                 	ib_prefetchtype;
	ISC_LINK(struct ib_prefetchinfo)     	link;
} ib_prefetchinfo_t;
#endif

/*% nameserver query structure */
struct ns_query {
	unsigned int			attributes;
	unsigned int			restarts;
	isc_boolean_t			timerset;
	dns_name_t *			qname;
	dns_name_t *			origqname;
	dns_rdatatype_t			qtype;
#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * These names are used for query rewriting.  Both will be created
	 * from the message tempname pool during query processing.
	 * ib_orig_qname is the original qname before rewriting.  It can be
	 * set in the message as part of the final answer, in which case
	 * the message is responsible for releasing it; otherwise the query
	 * module has to release it.  The ORIGQNAMESTORED attribute controls
	 * this condition.
	 * ib_qname is the rewritten qname.  It will be set in qname and will
	 * be automatically released at the end of query processing (or every
	 * time the query name is changed).
	 */
	dns_name_t *			ib_orig_qname;
	dns_name_t *			ib_qname;

	/* This name is used for first domain name containing IBALIAS record.
	 * It's created during processing first found IBALIAS record.
	 * It can be set in the message as part of the final answer, in which
	 * case the message is responsible for releasing it; otherwise the
	 * query module has to release it.
	 * The IBALIAS_ORIGQNAMESTORED attribute controls this condition.
	 */
	dns_name_t *			ibalias_orig_qname;

	/*
	 * Store minimum target TTL value among all found IBALIAS records.
	 */
	dns_ttl_t			ibalias_ttl;
#endif
	unsigned int			dboptions;
	unsigned int			fetchoptions;
	dns_db_t *			gluedb;
	dns_db_t *			authdb;
	dns_zone_t *			authzone;
	isc_boolean_t			authdbset;
	isc_boolean_t			isreferral;
	isc_mutex_t			fetchlock;
	dns_fetch_t *			fetch;
	dns_fetch_t *			prefetch;
	dns_rpz_st_t *			rpz_st;
	isc_bufferlist_t		namebufs;
	ISC_LIST(ns_dbversion_t)	activeversions;
	ISC_LIST(ns_dbversion_t)	freeversions;
	dns_rdataset_t *		dns64_aaaa;
	dns_rdataset_t *		dns64_sigaaaa;
	isc_boolean_t *			dns64_aaaaok;
	unsigned int			dns64_aaaaoklen;
	unsigned int			dns64_options;
	unsigned int			dns64_ttl;
	struct {
		dns_db_t *      	db;
		dns_zone_t *      	zone;
		dns_dbnode_t *      	node;
		dns_rdatatype_t   	qtype;
		dns_name_t *		fname;
		dns_fixedname_t		fixed;
		isc_result_t		result;
		dns_rdataset_t *	rdataset;
		dns_rdataset_t *	sigrdataset;
		isc_boolean_t		authoritative;
		isc_boolean_t		is_zone;
	} redirect;

	isc_result_t			resp_result;
	dns_fixedname_t			fname;
#ifdef ORIGINAL_ISC_CODE
#else
	/* This params are used be infoblox_should_log_dns_data() function to
	 * optimize search within log / not log tables.
	 * infoblox_check_perhaps_log_query_or_response_done - indicates whether
	 * search of this particular query has already been performed.
	 * infoblox_perhaps_log_query_or_response - search results if search is done.
	 */
	isc_boolean_t			infoblox_check_perhaps_log_query_or_response_done;
	isc_boolean_t			infoblox_perhaps_log_query_or_response;
	/* Using this additional qname buffer for query/response logging to preserve
	 * original qname, because it could be replaced during query_find
	 * (e.g.: for CNAME lookup). Buffer size is chosen to allow at least
	 * DNS_NAME_FORMATSIZE + 100 bytes for additional stuff like label,
	 * classname, typename.
	 */
#define INFOBLOX_FORMATTED_QNAME_LENGTH (DNS_NAME_FORMATSIZE + 100)
	unsigned char			infoblox_formatted_qname[INFOBLOX_FORMATTED_QNAME_LENGTH];
	isc_buffer_t			infoblox_formatted_qname_buffer;
	isc_boolean_t			infoblox_have_formatted_qname;

	isc_boolean_t		infoblox_rpz_found_white_list;
	isc_boolean_t		infoblox_rpz_rewrite_candidate;
	isc_boolean_t		infoblox_rpz_rewritten;
	/*
	 * This list is used to gather stale records names/types requiring prefetch, 
	 * so that they can be refreshed one by one. List is maintained only when 
	 * fault tolerant caching feature is enabled.
	 */
	ISC_LIST(ib_prefetchinfo_t)	prefetchinfo;
#endif
};

#define NS_QUERYATTR_RECURSIONOK	0x0001
#define NS_QUERYATTR_CACHEOK		0x0002
#define NS_QUERYATTR_PARTIALANSWER	0x0004
#define NS_QUERYATTR_NAMEBUFUSED	0x0008
#define NS_QUERYATTR_RECURSING		0x0010
#define NS_QUERYATTR_CACHEGLUEOK	0x0020
#define NS_QUERYATTR_QUERYOKVALID	0x0040
#define NS_QUERYATTR_QUERYOK		0x0080
#define NS_QUERYATTR_WANTRECURSION	0x0100
#define NS_QUERYATTR_SECURE		0x0200
#define NS_QUERYATTR_NOAUTHORITY	0x0400
#define NS_QUERYATTR_NOADDITIONAL	0x0800
#define NS_QUERYATTR_CACHEACLOKVALID	0x1000
#define NS_QUERYATTR_CACHEACLOK		0x2000
#define NS_QUERYATTR_DNS64		0x4000
#define NS_QUERYATTR_DNS64EXCLUDE	0x8000
#define NS_QUERYATTR_RRL_CHECKED	0x10000
#define NS_QUERYATTR_REDIRECT		0x20000
#ifdef ORIGINAL_ISC_CODE
#else
// Watch for collision with the ISC NS_QUERYATTR_* flags. We'll
// use a value that leaves a bit of space for ISC to expand.
#define	NS_QUERYATTR_IB_REDOINGRPZ		0x100000
#define	NS_QUERYATTR_SKIPNXDOMAINREDIRECT	0x200000
#define	NS_QUERYATTR_SKIPRPZ			0x400000
#define NS_QUERYATTR_QNAMEPROCESSED		0x800000
#define NS_QUERYATTR_QNAMEREWRITTEN		0x1000000
#define NS_QUERYATTR_ORIGQNAMESTORED		0x2000000
#define NS_QUERYATTR_CACHE_REFRESH		0x4000000
#define NS_QUERYATTR_PC_WHITELIST		0x8000000
#define NS_QUERYATTR_PC_CNAME			0x10000000
#define NS_QUERYATTR_PC_ASYNC			0x20000000
#define NS_QUERYATTR_IBALIAS_INITIATED		0x40000000
#define NS_QUERYATTR_IBALIAS_ORIGQNAMESTORED	0x80000000
#endif

isc_result_t
ns_query_init(ns_client_t *client);

void
ns_query_free(ns_client_t *client);

void
ns_query_start(ns_client_t *client);

void
ns_query_cancel(ns_client_t *client);

void
ns_query_incstats(ns_client_t *client, isc_statscounter_t counter);

#ifdef ORIGINAL_ISC_CODE
#else
/* Search query inside log & not log tables. Returns true if query should be
 * logged and false otherwise. Can be used by both query and response logging
 * features.
 */
isc_boolean_t
infoblox_should_log_dns_data(ns_client_t *client);

/* Format query info. Formatted string contains next info:
 * "query: <queried domain name> <class name> <type name>"
 * e.g.: query: infoblox.com IN A
 * Formatted output is concatenated with previous buffer content.
 * The caller is responsible for putting '\0' to the formatted string.
 */
void
infoblox_format_query_info(ns_client_t *client, isc_buffer_t *target);
#endif

#endif /* NAMED_QUERY_H */
