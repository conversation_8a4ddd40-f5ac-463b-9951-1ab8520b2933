/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

#include <config.h>

#include <isc/aes.h>
#include <isc/formatcheck.h>
#include <isc/hmacsha.h>
#include <isc/mutex.h>
#include <isc/once.h>
#include <isc/platform.h>
#include <isc/print.h>
#include <isc/queue.h>
#include <isc/random.h>
#include <isc/safe.h>
#include <isc/serial.h>
#include <isc/stats.h>
#include <isc/stdio.h>
#include <isc/string.h>
#include <isc/task.h>
#include <isc/timer.h>
#include <isc/util.h>

#include <dns/adb.h>
#include <dns/badcache.h>
#include <dns/db.h>
#include <dns/dispatch.h>
#include <dns/dnstap.h>
#include <dns/cache.h>
#include <dns/edns.h>
#include <dns/events.h>
#include <dns/message.h>
#include <dns/peer.h>
#include <dns/rcode.h>
#include <dns/rdata.h>
#include <dns/rdataclass.h>
#include <dns/rdatalist.h>
#include <dns/rdataset.h>
#include <dns/resolver.h>
#include <dns/stats.h>
#include <dns/tsig.h>
#include <dns/view.h>
#include <dns/zone.h>

#include <named/fuzz.h>
#include <named/interfacemgr.h>
#include <named/log.h>
#include <named/notify.h>
#include <named/os.h>
#include <named/server.h>
#include <named/update.h>

#ifdef ORIGINAL_ISC_CODE
#else
#include <stdlib.h>
#include <dns/infoblox_onedb.h>
#include <dns/infoblox_db_if.h>
#include <infoblox/dns/response_logging_data.h>
#include <named/infoblox_response_log.h>
#include <isc/strerror.h>
#include <dns/infoblox_edns0.h>
static void ns_client_name(ns_client_t *client, char *peerbuf, size_t len);
#endif

/***
 *** Client
 ***/

/*! \file
 * Client Routines
 *
 * Important note!
 *
 * All client state changes, other than that from idle to listening, occur
 * as a result of events.  This guarantees serialization and avoids the
 * need for locking.
 *
 * If a routine is ever created that allows someone other than the client's
 * task to change the client, then the client will have to be locked.
 */

#define NS_CLIENT_TRACE
#ifdef NS_CLIENT_TRACE
#define CTRACE(m)	ns_client_log(client, \
				      NS_LOGCATEGORY_CLIENT, \
				      NS_LOGMODULE_CLIENT, \
				      ISC_LOG_DEBUG(3), \
				      "%s", (m))
#define MTRACE(m)	isc_log_write(ns_g_lctx, \
				      NS_LOGCATEGORY_GENERAL, \
				      NS_LOGMODULE_CLIENT, \
				      ISC_LOG_DEBUG(3), \
				      "clientmgr @%p: %s", manager, (m))
#else
#define CTRACE(m)	((void)(m))
#define MTRACE(m)	((void)(m))
#endif

#define TCP_CLIENT(c)	(((c)->attributes & NS_CLIENTATTR_TCP) != 0)
#define ECS_RECEIVED(c)	(((c)->attributes & NS_CLIENTATTR_ECSRECEIVED) != 0)
#define WANTNSID(c)	(((c)->attributes & NS_CLIENTATTR_WANTNSID) != 0)
#define WANTEXPIRE(c)	(((c)->attributes & NS_CLIENTATTR_WANTEXPIRE) != 0)
#define WANTPAD(x) 	(((x)->attributes & NS_CLIENTATTR_WANTPAD) != 0)
#define USEKEEPALIVE(x) (((x)->attributes & NS_CLIENTATTR_USEKEEPALIVE) != 0)

#ifdef ORIGINAL_ISC_CODE
#else
#define WANTDESTADDR(x) (((x)->attributes & NS_CLIENTATTR_WANTDESTADDR) != 0)
#endif

#define TCP_BUFFER_SIZE			(65535 + 2)
#define SEND_BUFFER_SIZE		4096
#define RECV_BUFFER_SIZE		4096

#define TCP_CLIENTS_PER_CONN		23
/*%<
 * Number of simultaneous ns_clients_t (queries in flight) for one
 * TCP connection.  The number was arbitrarily picked and might be
 * changed in the future.
 */

#ifdef ISC_PLATFORM_USETHREADS

#define NMCTXS				100
/*%<
 * Number of 'mctx pools' for clients. (Should this be configurable?)
 * When enabling threads, we use a pool of memory contexts shared by
 * client objects, since concurrent access to a shared context would cause
 * heavy contentions.  The above constant is expected to be enough for
 * completely avoiding contentions among threads for an authoritative-only
 * server.
 */
#else
#define NMCTXS				0
/*%<
 * If named with built without thread, simply share manager's context.  Using
 * a separate context in this case would simply waste memory.
 */
#endif

#define COOKIE_SIZE 24U /* 8 + 4 + 4 + 8 */
#define ECS_SIZE 20U /* 2 + 1 + 1 + [0..16] */

/*% nameserver client manager structure */
struct ns_clientmgr {
	/* Unlocked. */
	unsigned int			magic;

	/* The queue object has its own locks */
	client_queue_t			inactive;     /*%< To be recycled */

	isc_mem_t *			mctx;
	isc_taskmgr_t *			taskmgr;
	isc_timermgr_t *		timermgr;

	/* Lock covers manager state. */
	isc_mutex_t			lock;
	isc_boolean_t			exiting;

	/* Lock covers the clients list */
	isc_mutex_t			listlock;
	client_list_t			clients;      /*%< All active clients */

	/* Lock covers the recursing list */
	isc_mutex_t			reclock;
	client_list_t			recursing;    /*%< Recursing clients */
#ifdef ORIGINAL_ISC_CODE
#else
	client_list_t			recursing_low_prio; // Protected by reclock
#endif

#if NMCTXS > 0
	/*%< mctx pool for clients. */
	unsigned int			nextmctx;
	isc_mem_t *			mctxpool[NMCTXS];
#endif
};

#define MANAGER_MAGIC			ISC_MAGIC('N', 'S', 'C', 'm')
#define VALID_MANAGER(m)		ISC_MAGIC_VALID(m, MANAGER_MAGIC)

/*!
 * Client object states.  Ordering is significant: higher-numbered
 * states are generally "more active", meaning that the client can
 * have more dynamically allocated data, outstanding events, etc.
 * In the list below, any such properties listed for state N
 * also apply to any state > N.
 *
 * To force the client into a less active state, set client->newstate
 * to that state and call exit_check().  This will cause any
 * activities defined for higher-numbered states to be aborted.
 */

#define NS_CLIENTSTATE_FREED    0
/*%<
 * The client object no longer exists.
 */

#define NS_CLIENTSTATE_INACTIVE 1
/*%<
 * The client object exists and has a task and timer.
 * Its "query" struct and sendbuf are initialized.
 * It is on the client manager's list of inactive clients.
 * It has a message and OPT, both in the reset state.
 */

#define NS_CLIENTSTATE_READY    2
/*%<
 * The client object is either a TCP or a UDP one, and
 * it is associated with a network interface.  It is on the
 * client manager's list of active clients.
 *
 * If it is a TCP client object, it has a TCP listener socket
 * and an outstanding TCP listen request.
 *
 * If it is a UDP client object, it has a UDP listener socket
 * and an outstanding UDP receive request.
 */

#define NS_CLIENTSTATE_READING  3
/*%<
 * The client object is a TCP client object that has received
 * a connection.  It has a tcpsocket, tcpmsg, TCP quota, and an
 * outstanding TCP read request.  This state is not used for
 * UDP client objects.
 */

#define NS_CLIENTSTATE_WORKING  4
/*%<
 * The client object has received a request and is working
 * on it.  It has a view, and it may have any of a non-reset OPT,
 * recursion quota, and an outstanding write request.
 */

#define NS_CLIENTSTATE_RECURSING  5
/*%<
 * The client object is recursing.  It will be on the 'recursing'
 * list.
 */

#define NS_CLIENTSTATE_MAX      9
/*%<
 * Sentinel value used to indicate "no state".  When client->newstate
 * has this value, we are not attempting to exit the current state.
 * Must be greater than any valid state.
 */

/*
 * Enable ns_client_dropport() by default.
 */
#ifndef NS_CLIENT_DROPPORT
#define NS_CLIENT_DROPPORT 1
#endif

unsigned int ns_client_requests;

static void read_settimeout(ns_client_t *client, isc_boolean_t newconn);
static void client_read(ns_client_t *client, isc_boolean_t newconn);

#ifdef ORIGINAL_ISC_CODE
#else
/*
 * Name for GSS-TSIG key
 */
#include "../../../common/server/include/infoblox/kerberos.h"
#include <pthread.h>

static pthread_mutex_t gss_tsig_name_lock = PTHREAD_MUTEX_INITIALIZER;
static int gss_tsig_signer_exists = 0;
static unsigned char gss_tsig_key_name_ndata[256 + 1];
static dns_name_t gss_tsig_signer;
static isc_constregion_t gss_tsig_signer_region;

static isc_result_t
infoblox_create_gss_tsig_key_name (void)
{
  int len, ret;

  (void) pthread_mutex_lock (&gss_tsig_name_lock);

  if (gss_tsig_signer_exists)
    {
      (void) pthread_mutex_unlock (&gss_tsig_name_lock);
      return (ISC_R_SUCCESS);
    }

  len = strlen (INFOBLOX_GSS_TSIG_PSEUDO_KEY_NAME);
  if (len > (int)(sizeof (gss_tsig_key_name_ndata) - 2))
    {
      return (ISC_R_NOSPACE);
    }

  ret = snprintf ((char *)gss_tsig_key_name_ndata, sizeof (gss_tsig_key_name_ndata),
		  "%c%s", len, INFOBLOX_GSS_TSIG_PSEUDO_KEY_NAME);
  if (ret <= 0 || ret >= (int)sizeof (gss_tsig_key_name_ndata))
    {
      return (ISC_R_NOSPACE);
    }

  dns_name_init (&gss_tsig_signer, NULL);
  gss_tsig_signer_region.base = gss_tsig_key_name_ndata;
  gss_tsig_signer_region.length = len + 2;
  dns_name_fromregion (&gss_tsig_signer, (isc_region_t *)&gss_tsig_signer_region);

  gss_tsig_signer_exists = 1;

  (void) pthread_mutex_unlock (&gss_tsig_name_lock);

  return (ISC_R_SUCCESS);
}
#endif

static void client_accept(ns_client_t *client);
static void client_udprecv(ns_client_t *client);
static void clientmgr_destroy(ns_clientmgr_t *manager);
static isc_boolean_t exit_check(ns_client_t *client);
static void ns_client_endrequest(ns_client_t *client);
static void client_start(isc_task_t *task, isc_event_t *event);
static void client_request(isc_task_t *task, isc_event_t *event);
static void ns_client_dumpmessage(ns_client_t *client, const char *reason);
static isc_result_t get_client(ns_clientmgr_t *manager, ns_interface_t *ifp,
#ifdef ORIGINAL_ISC_CODE
			       dns_dispatch_t *disp, isc_boolean_t tcp);
#else
			       dns_dispatch_t *disp, isc_boolean_t tcp, int disp_num, int shadow_fd);
#endif
static isc_result_t get_worker(ns_clientmgr_t *manager, ns_interface_t *ifp,
			       isc_socket_t *sock, ns_client_t *oldclient);
static inline isc_boolean_t
#ifdef ORIGINAL_ISC_CODE
allowed(isc_netaddr_t *addr, dns_name_t *signer,
	dns_ecs_t *ecs, dns_acl_t *acl);
#else
allowed(isc_netaddr_t *addr, dns_name_t *signer,
	dns_ecs_t *ecs, dns_acl_t *acl,
	dns_view_t *view, ns_client_t *client);
#endif
static void compute_cookie(ns_client_t *client, isc_uint32_t when,
			   isc_uint32_t nonce, isc_buffer_t *buf);

void
ns_client_recursing(ns_client_t *client) {
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(client->state == NS_CLIENTSTATE_WORKING);

	LOCK(&client->manager->reclock);
	client->newstate = client->state = NS_CLIENTSTATE_RECURSING;
#ifdef ORIGINAL_ISC_CODE
	ISC_LIST_APPEND(client->manager->recursing, client, rlink);
#else
	// Always on the recursing list
	ISC_LIST_APPEND(client->manager->recursing, client, rlink);
	// If this is _not_ work for a cache refresh, also add the client
	// to the low priority recursing list.
	if ((client->query.attributes & NS_QUERYATTR_CACHE_REFRESH) == 0 &&
	    infoblox_multi_tier_recursive_client()) {
		ISC_LIST_APPEND(client->manager->recursing_low_prio, client, rlplink);
		isc_atomic_xadd(&ns_g_server->infoblox_recursing_low_prio_count, 1);
	}
#endif
	UNLOCK(&client->manager->reclock);
}

void
ns_client_killoldestquery(ns_client_t *client) {
	ns_client_t *oldest;
	REQUIRE(NS_CLIENT_VALID(client));

	LOCK(&client->manager->reclock);
#ifdef ORIGINAL_ISC_CODE
	oldest = ISC_LIST_HEAD(client->manager->recursing);
#else
	// Prefer to remove a low priority client.
	oldest = ISC_LIST_HEAD(client->manager->recursing_low_prio);
	if (oldest != NULL) {
		// Remove from the low priority list, then go on to the normal processing,
		// which will remove the entry from the full list as well.
		ISC_LIST_UNLINK(client->manager->recursing_low_prio, oldest, rlplink);
		isc_atomic_xadd(&ns_g_server->infoblox_recursing_low_prio_count, -1);
	} else {
		oldest = ISC_LIST_HEAD(client->manager->recursing);
	}
#endif
	if (oldest != NULL) {
		ISC_LIST_UNLINK(client->manager->recursing, oldest, rlink);
		UNLOCK(&client->manager->reclock);
		ns_query_cancel(oldest);
	} else
		UNLOCK(&client->manager->reclock);
}

void
ns_client_settimeout(ns_client_t *client, unsigned int seconds) {
	isc_result_t result;
	isc_interval_t interval;

	isc_interval_set(&interval, seconds, 0);
	result = isc_timer_reset(client->timer, isc_timertype_once, NULL,
				 &interval, ISC_FALSE);
	client->timerset = ISC_TRUE;
	if (result != ISC_R_SUCCESS) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
			      "setting timeout: %s",
			      isc_result_totext(result));
		/* Continue anyway. */
	}
}

static void
read_settimeout(ns_client_t *client, isc_boolean_t newconn) {
	isc_result_t result;
	isc_interval_t interval;
	unsigned int ds;

	if (newconn)
		ds = ns_g_initialtimo;
	else if (USEKEEPALIVE(client))
		ds = ns_g_keepalivetimo;
	else
		ds = ns_g_idletimo;

	isc_interval_set(&interval, ds / 10, 100000000 * (ds % 10));
	result = isc_timer_reset(client->timer, isc_timertype_once, NULL,
				 &interval, ISC_FALSE);
	client->timerset = ISC_TRUE;
	if (result != ISC_R_SUCCESS) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
			      "setting timeout: %s",
			      isc_result_totext(result));
		/* Continue anyway. */
	}
}

 /*%
 * Allocate a reference-counted object that will maintain a single pointer to
 * the (also reference-counted) TCP client quota, shared between all the
 * clients processing queries on a single TCP connection, so that all
 * clients sharing the one socket will together consume only one slot in
 * the 'tcp-clients' quota.
 */
static isc_result_t
tcpconn_init(ns_client_t *client, isc_boolean_t force) {
	isc_result_t result;
	isc_quota_t *quota = NULL;
	ns_tcpconn_t *tconn = NULL;

	REQUIRE(client->tcpconn == NULL);

	/*
	 * Try to attach to the quota first, so we won't pointlessly
	 * allocate memory for a tcpconn object if we can't get one.
	 */
	if (force) {
		result = isc_quota_force(&ns_g_server->tcpquota, &quota);
	} else {
		result = isc_quota_attach(&ns_g_server->tcpquota, &quota);
	}
	if (result != ISC_R_SUCCESS) {
		return (result);
	}

	/*
	 * A global memory context is used for the allocation as different
	 * client structures may have different memory contexts assigned and a
	 * reference counter allocated here might need to be freed by a
	 * different client.  The performance impact caused by memory context
	 * contention here is expected to be negligible, given that this code
	 * is only executed for TCP connections.
	 */
	tconn = isc_mem_allocate(ns_g_mctx, sizeof(*tconn));

	isc_refcount_init(&tconn->clients, 1);	/* Current client */
	tconn->tcpquota = quota;
	quota = NULL;
	tconn->pipelined = ISC_FALSE;

	client->tcpconn = tconn;

	return (ISC_R_SUCCESS);
}

/*%
 * Increase the count of client structures sharing the TCP connection
 * that 'source' is associated with; add a pointer to the same tcpconn
 * to 'target', thus associating it with the same TCP connection.
 */
static void
tcpconn_attach(ns_client_t *source, ns_client_t *target) {
	int old_clients;

	REQUIRE(source->tcpconn != NULL);
	REQUIRE(target->tcpconn == NULL);
	REQUIRE(source->tcpconn->pipelined);

	isc_refcount_increment(&source->tcpconn->clients, &old_clients);
	INSIST(old_clients > 1);
	target->tcpconn = source->tcpconn;
}

/*%
 * Decrease the count of client structures sharing the TCP connection that
 * 'client' is associated with.  If this is the last client using this TCP
 * connection, we detach from the TCP quota and free the tcpconn
 * object. Either way, client->tcpconn is set to NULL.
 */
static void
tcpconn_detach(ns_client_t *client) {
	ns_tcpconn_t *tconn = NULL;
	int old_clients;

	REQUIRE(client->tcpconn != NULL);

	tconn = client->tcpconn;
	client->tcpconn = NULL;

	isc_refcount_decrement(&tconn->clients, &old_clients);
	if (old_clients == 0) {
		isc_quota_detach(&tconn->tcpquota);
		isc_mem_free(ns_g_mctx, tconn);
	}
}

/*%
 * Mark a client as active and increment the interface's 'ntcpactive'
 * counter, as a signal that there is at least one client servicing
 * TCP queries for the interface. If we reach the TCP client quota at
 * some point, this will be used to determine whether a quota overrun
 * should be permitted.
 *
 * Marking the client active with the 'tcpactive' flag ensures proper
 * accounting, by preventing us from incrementing or decrementing
 * 'ntcpactive' more than once per client.
 */
static void
mark_tcp_active(ns_client_t *client, isc_boolean_t active) {
	if (active && !client->tcpactive) {
		isc_atomic_xadd(&client->interface->ntcpactive, 1);
		client->tcpactive = active;
	} else if (!active && client->tcpactive) {
		uint32_t old =
			isc_atomic_xadd(&client->interface->ntcpactive, -1);
		INSIST(old > 0);
		client->tcpactive = active;
	}
}

/*%
 * Check for a deactivation or shutdown request and take appropriate
 * action.  Returns ISC_TRUE if either is in progress; in this case
 * the caller must no longer use the client object as it may have been
 * freed.
 */
static isc_boolean_t
exit_check(ns_client_t *client) {
	isc_boolean_t destroy_manager = ISC_FALSE;
	ns_clientmgr_t *manager = NULL;

	REQUIRE(NS_CLIENT_VALID(client));
	manager = client->manager;

	if (client->state <= client->newstate)
		return (ISC_FALSE); /* Business as usual. */

	INSIST(client->newstate < NS_CLIENTSTATE_RECURSING);

	/*
	 * We need to detach from the view early when shutting down
	 * the server to break the following vicious circle:
	 *
	 *  - The resolver will not shut down until the view refcount is zero
	 *  - The view refcount does not go to zero until all clients detach
	 *  - The client does not detach from the view until references is zero
	 *  - references does not go to zero until the resolver has shut down
	 *
	 * Keep the view attached until any outstanding updates complete.
	 */
	if (client->nupdates == 0 &&
#ifdef ORIGINAL_ISC_CODE
	    client->newstate == NS_CLIENTSTATE_FREED && client->view != NULL)
		dns_view_detach(&client->view);
#else
	    client->newstate == NS_CLIENTSTATE_FREED)
	{
		// If client->retain_view is set, there's an update_action() task active,
		// and we can't remove the client->view pointer until it's done (at which
		// time _it_ will detach the view and set client->view=NULL). Yes, this
		// means we may have to wait a bit (for the view ref count to drop to zero),
		// but the alternative is a potential core dump in update_action().
		(void) pthread_mutex_lock (&client->view_lock);
		if (client->view != NULL && ! client->retain_view)
			dns_view_detach(&client->view);
		(void) pthread_mutex_unlock (&client->view_lock);
	}
#endif

	if (client->state == NS_CLIENTSTATE_WORKING ||
	    client->state == NS_CLIENTSTATE_RECURSING)
	{
		INSIST(client->newstate <= NS_CLIENTSTATE_READING);
		/*
		 * Let the update processing complete.
		 */
		if (client->nupdates > 0)
			return (ISC_TRUE);

		/*
		 * We are trying to abort request processing.
		 */
		if (client->nsends > 0) {
			isc_socket_t *sock;
			if (TCP_CLIENT(client))
				sock = client->tcpsocket;
			else
				sock = client->udpsocket;
			isc_socket_cancel(sock, client->task,
					  ISC_SOCKCANCEL_SEND);
		}

		if (! (client->nsends == 0 && client->nrecvs == 0 &&
		       client->references == 0))
		{
			/*
			 * Still waiting for I/O cancel completion.
			 * or lingering references.
			 */
			return (ISC_TRUE);
		}

		/*
		 * I/O cancel is complete.  Burn down all state
		 * related to the current request.  Ensure that
		 * the client is no longer on the recursing list.
		 *
		 * We need to check whether the client is still linked,
		 * because it may already have been removed from the
		 * recursing list by ns_client_killoldestquery()
		 */
		if (client->state == NS_CLIENTSTATE_RECURSING) {
			LOCK(&manager->reclock);
			if (ISC_LINK_LINKED(client, rlink))
				ISC_LIST_UNLINK(manager->recursing,
						client, rlink);
#ifdef ORIGINAL_ISC_CODE
#else
			if (ISC_LINK_LINKED(client, rlplink)) {
				ISC_LIST_UNLINK(manager->recursing_low_prio,
						client, rlplink);
				isc_atomic_xadd(&ns_g_server->infoblox_recursing_low_prio_count, -1);
			}
#endif
			UNLOCK(&manager->reclock);
		}
		ns_client_endrequest(client);

		client->state = NS_CLIENTSTATE_READING;
		INSIST(client->recursionquota == NULL);

		if (NS_CLIENTSTATE_READING == client->newstate) {
			INSIST(client->tcpconn != NULL);
			if (!client->tcpconn->pipelined) {
				client_read(client, ISC_FALSE);
				client->newstate = NS_CLIENTSTATE_MAX;
				return (ISC_TRUE); /* We're done. */
			} else if (client->mortal) {
				client->newstate = NS_CLIENTSTATE_INACTIVE;
			} else
				return (ISC_FALSE);
		}
	}

	if (client->state == NS_CLIENTSTATE_READING) {
		/*
		 * We are trying to abort the current TCP connection,
		 * if any.
		 */
		INSIST(client->recursionquota == NULL);
		INSIST(client->newstate <= NS_CLIENTSTATE_READY);

		if (client->nreads > 0) {
			dns_tcpmsg_cancelread(&client->tcpmsg);
		}

		/* Still waiting for read cancel completion. */
		if (client->nreads > 0) {
			return (ISC_TRUE);
		}

		if (client->tcpmsg_valid) {
			dns_tcpmsg_invalidate(&client->tcpmsg);
			client->tcpmsg_valid = ISC_FALSE;
		}

		/*
		 * Soon the client will be ready to accept a new TCP
		 * connection or UDP request, but we may have enough
		 * clients doing that already.  Check whether this client
		 * needs to remain active and allow it go inactive if
		 * not.
		 *
		 * UDP clients always go inactive at this point, but a TCP
		 * client may need to stay active and return to READY
		 * state if no other clients are available to listen
		 * for TCP requests on this interface.
		 *
		 * Regardless, if we're going to FREED state, that means
		 * the system is shutting down and we don't need to
		 * retain clients.
		 */
		if (client->mortal && TCP_CLIENT(client) &&
		    client->newstate != NS_CLIENTSTATE_FREED &&
		    !ns_g_clienttest &&
		    isc_atomic_xadd(&client->interface->ntcpaccepting, 0) == 0)
		{
			/* Nobody else is accepting */
			client->mortal = ISC_FALSE;
			client->newstate = NS_CLIENTSTATE_READY;
		}

		/*
		 * Detach from TCP connection and TCP client quota,
		 * if appropriate. If this is the last reference to
		 * the TCP connection in our pipeline group, the
		 * TCP quota slot will be released.
		 */
		if (client->tcpconn) {
			tcpconn_detach(client);
		}

		if (client->tcpsocket != NULL) {
			CTRACE("closetcp");
			isc_socket_detach(&client->tcpsocket);
			mark_tcp_active(client, ISC_FALSE);
		}

		if (client->timerset) {
			(void)isc_timer_reset(client->timer,
					      isc_timertype_inactive,
					      NULL, NULL, ISC_TRUE);
			client->timerset = ISC_FALSE;
		}

		client->peeraddr_valid = ISC_FALSE;

		client->state = NS_CLIENTSTATE_READY;

		/*
		 * We don't need the client; send it to the inactive
		 * queue for recycling.
		 */
		if (client->mortal) {
			if (client->newstate > NS_CLIENTSTATE_INACTIVE) {
				client->newstate = NS_CLIENTSTATE_INACTIVE;
			}
		}

		if (NS_CLIENTSTATE_READY == client->newstate) {
			if (TCP_CLIENT(client)) {
				client_accept(client);
			} else {
				client_udprecv(client);
			}
			client->newstate = NS_CLIENTSTATE_MAX;
			return (ISC_TRUE);
		}
	}

	if (client->state == NS_CLIENTSTATE_READY) {
		INSIST(client->newstate <= NS_CLIENTSTATE_INACTIVE);

		/*
		 * We are trying to enter the inactive state.
		 */
		if (client->naccepts > 0) {
			isc_socket_cancel(client->tcplistener, client->task,
					  ISC_SOCKCANCEL_ACCEPT);
		}

		/* Still waiting for accept cancel completion. */
		if (client->naccepts > 0) {
			return (ISC_TRUE);
		}

		/* Accept cancel is complete. */
		if (client->nrecvs > 0) {
			isc_socket_cancel(client->udpsocket, client->task,
					  ISC_SOCKCANCEL_RECV);
		}

		/* Still waiting for recv cancel completion. */
		if (client->nrecvs > 0) {
			return (ISC_TRUE);
		}

		/* Still waiting for control event to be delivered */
		if (client->nctls > 0) {
			return (ISC_TRUE);
		}

		INSIST(client->naccepts == 0);
		INSIST(client->recursionquota == NULL);
		if (client->tcplistener != NULL) {
			isc_socket_detach(&client->tcplistener);
			mark_tcp_active(client, ISC_FALSE);
		}
		if (client->udpsocket != NULL) {
			isc_socket_detach(&client->udpsocket);
		}

		/* Deactivate the client. */
		if (client->interface != NULL) {
			ns_interface_detach(&client->interface);
		}

		if (client->dispatch != NULL) {
			dns_dispatch_detach(&client->dispatch);
		}

		client->attributes = 0;
		client->mortal = ISC_FALSE;
#ifdef ORIGINAL_ISC_CODE
#else
		/*
		 * At this point seen_soa_query must be always false,
		 * since it can be only set to true in query handling, and
		 * immediately reset to false in ns_client_next().  But since
		 * code flow is very complicated we INSIST it only in the
		 * debug mode.
		 */
		if (infoblox_diag_is_enabled()) {
			INSIST(!client->seen_soa_query);
		} else {
			client->seen_soa_query = ISC_FALSE;
		}
#endif

		if (client->keytag != NULL) {
			isc_mem_put(client->mctx, client->keytag,
				    client->keytag_len);
			client->keytag_len = 0;
		}

		/*
		 * Put the client on the inactive list.  If we are aiming for
		 * the "freed" state, it will be removed from the inactive
		 * list shortly, and we need to keep the manager locked until
		 * that has been done, lest the manager decide to reactivate
		 * the dying client inbetween.
		 */
		client->state = NS_CLIENTSTATE_INACTIVE;
		INSIST(client->recursionquota == NULL);

		if (client->state == client->newstate) {
			client->newstate = NS_CLIENTSTATE_MAX;
			if (!ns_g_clienttest && manager != NULL &&
			    !manager->exiting)
			{
				ISC_QUEUE_PUSH(manager->inactive, client,
					       ilink);
			}
			if (client->needshutdown) {
				isc_task_shutdown(client->task);
			}
			return (ISC_TRUE);
		}
	}

	if (client->state == NS_CLIENTSTATE_INACTIVE) {
		INSIST(client->newstate == NS_CLIENTSTATE_FREED);
		/*
		 * We are trying to free the client.
		 *
		 * When "shuttingdown" is true, either the task has received
		 * its shutdown event or no shutdown event has ever been
		 * set up.  Thus, we have no outstanding shutdown
		 * event at this point.
		 */
		REQUIRE(client->state == NS_CLIENTSTATE_INACTIVE);

		INSIST(client->recursionquota == NULL);
		INSIST(!ISC_QLINK_LINKED(client, ilink));

		if (manager != NULL) {
			LOCK(&manager->listlock);
			ISC_LIST_UNLINK(manager->clients, client, link);
			LOCK(&manager->lock);
			if (manager->exiting &&
			    ISC_LIST_EMPTY(manager->clients))
				destroy_manager = ISC_TRUE;
			UNLOCK(&manager->lock);
			UNLOCK(&manager->listlock);
		}

		ns_query_free(client);
		isc_mem_put(client->mctx, client->recvbuf, RECV_BUFFER_SIZE);
		isc_event_free((isc_event_t **)&client->sendevent);
		isc_event_free((isc_event_t **)&client->recvevent);
		isc_timer_detach(&client->timer);
		if (client->delaytimer != NULL)
			isc_timer_detach(&client->delaytimer);

		if (client->tcpbuf != NULL)
			isc_mem_put(client->mctx, client->tcpbuf,
				    TCP_BUFFER_SIZE);
		if (client->opt != NULL) {
			INSIST(dns_rdataset_isassociated(client->opt));
			dns_rdataset_disassociate(client->opt);
			dns_message_puttemprdataset(client->message,
						    &client->opt);
		}
		if (client->keytag != NULL) {
			isc_mem_put(client->mctx, client->keytag,
				    client->keytag_len);
			client->keytag_len = 0;
		}

		dns_message_destroy(&client->message);

#ifdef ORIGINAL_ISC_CODE
#else
		if (client->ib_principal != NULL) {
			isc_mem_put(client->mctx, client->ib_principal, strlen(client->ib_principal) + 1);
			client->ib_principal = NULL;
		}
#endif

		/*
		 * Detaching the task must be done after unlinking from
		 * the manager's lists because the manager accesses
		 * client->task.
		 */
		if (client->task != NULL)
			isc_task_detach(&client->task);

		CTRACE("free");
		client->magic = 0;

		/*
		 * Check that there are no other external references to
		 * the memory context.
		 */
		if (ns_g_clienttest && isc_mem_references(client->mctx) != 1) {
			isc_mem_stats(client->mctx, stderr);
			INSIST(0);
		}

		/*
		 * Destroy the fetchlock mutex that was created in
		 * ns_query_init().
		 */
		DESTROYLOCK(&client->query.fetchlock);

		isc_mem_putanddetach(&client->mctx, client, sizeof(*client));
	}

	if (destroy_manager && manager != NULL)
		clientmgr_destroy(manager);

	return (ISC_TRUE);
}

/*%
 * The client's task has received the client's control event
 * as part of the startup process.
 */
static void
client_start(isc_task_t *task, isc_event_t *event) {
	ns_client_t *client = (ns_client_t *) event->ev_arg;

	INSIST(task == client->task);

	UNUSED(task);

	INSIST(client->nctls == 1);
	client->nctls--;

	if (exit_check(client))
		return;

	if (TCP_CLIENT(client)) {
		if (client->tcpconn != NULL) {
			client_read(client, ISC_FALSE);
		} else {
			client_accept(client);
		}
	} else {
		client_udprecv(client);
	}
}

/*%
 * The client's task has received a shutdown event.
 */
static void
client_shutdown(isc_task_t *task, isc_event_t *event) {
	ns_client_t *client;

	REQUIRE(event != NULL);
	REQUIRE(event->ev_type == ISC_TASKEVENT_SHUTDOWN);
	client = event->ev_arg;
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(task == client->task);

	UNUSED(task);

	CTRACE("shutdown");

	isc_event_free(&event);

	if (client->shutdown != NULL) {
		(client->shutdown)(client->shutdown_arg, ISC_R_SHUTTINGDOWN);
		client->shutdown = NULL;
		client->shutdown_arg = NULL;
	}

	if (ISC_QLINK_LINKED(client, ilink))
		ISC_QUEUE_UNLINK(client->manager->inactive, client, ilink);

	client->newstate = NS_CLIENTSTATE_FREED;
	client->needshutdown = ISC_FALSE;
	(void)exit_check(client);
}

static void
ns_client_endrequest(ns_client_t *client) {
	INSIST(client->naccepts == 0);
	INSIST(client->nreads == 0);
	INSIST(client->nsends == 0);
	INSIST(client->nrecvs == 0);
	INSIST(client->nupdates == 0);
	INSIST(client->state == NS_CLIENTSTATE_WORKING ||
	       client->state == NS_CLIENTSTATE_RECURSING);

	CTRACE("endrequest");

	if (client->next != NULL) {
		(client->next)(client);
		client->next = NULL;
	}

	if (client->view != NULL) {
#ifdef ENABLE_AFL
		if (ns_g_fuzz_type == ns_fuzz_resolver) {
			dns_cache_clean(client->view->cache, INT_MAX);
			dns_adb_flush(client->view->adb);
		}
#endif
		dns_view_detach(&client->view);
	}

#ifdef ORIGINAL_ISC_CODE
#else
	/* Performing this check here to ensure that client->response_logging_view is
	 * detached. This is a kind of double check if view wasn't detached after
	 * logging the response. This situation might happen if exit_check() has
	 * been called in some place before response logging so logging
	 * itself wouldn't be performed.
	 */
	if (client->response_logging_view != NULL) {
		dns_view_detach(&client->response_logging_view);
	}
#endif

	if (client->opt != NULL) {
		INSIST(dns_rdataset_isassociated(client->opt));
		dns_rdataset_disassociate(client->opt);
		dns_message_puttemprdataset(client->message, &client->opt);
	}

	client->signer = NULL;
#ifdef ORIGINAL_ISC_CODE
#else
	if (client->ib_principal != NULL) {
		isc_mem_put(client->mctx, client->ib_principal, strlen(client->ib_principal) + 1);
		client->ib_principal = NULL;
	}

	/* client->ecs_opt is created when client is in NS_CLIENTSTATE_WORKING
	 * state.
	 * Since this function is invoked when client state is no less than
	 * NS_CLIENTSTATE_WORKING in exit_check(), it's reasonable to check
	 * and free client->ecs_opt here.
	 */
	if (client->ecs_opt) {
		isc_mem_put(client->mctx, client->ecs_opt,
			    sizeof(ib_edns_client_subnet_t));
		client->ecs_opt = NULL;
	}

	if (client->subscriber != NULL)
		infoblox_free_subscriber_data(client->mctx,
					      &client->subscriber);

	client->warning_vip = FALSE;
	client->rated_category = 0U;

	if (client->ibsppc_orig_rdataset != NULL) {
		if (dns_rdataset_isassociated(client->ibsppc_orig_rdataset))
			dns_rdataset_disassociate(client->ibsppc_orig_rdataset);
		dns_message_puttemprdataset(client->message,
					    &client->ibsppc_orig_rdataset);
	}

	if (client->ibsppc_orig_sigrdataset != NULL) {
		if (dns_rdataset_isassociated(client->ibsppc_orig_sigrdataset))
			dns_rdataset_disassociate(client->ibsppc_orig_sigrdataset);
		dns_message_puttemprdataset(client->message,
					    &client->ibsppc_orig_sigrdataset);
	}

	if (client->ib_client_id != NULL) {
		isc_mem_put(client->mctx, client->ib_client_id,
			    sizeof(*client->ib_client_id) +
			    client->ib_client_id->idlen);
		client->ib_client_id = NULL;
	}

	if (client->infoblox_addropts.view_name != NULL) {
		isc_mem_put(client->mctx, client->infoblox_addropts.view_name,
			    client->infoblox_addropts.view_name_len);
		client->infoblox_addropts.view_name = NULL;
	}

	client->attributes &= ~(NS_CLIENTATTR_WANTDESTADDR);
#endif
	client->udpsize = 512;
	client->extflags = 0;
	client->ednsversion = -1;
	dns_ecs_init(&client->ecs);
	dns_message_reset(client->message, DNS_MESSAGE_INTENTPARSE);

	if (client->recursionquota != NULL) {
		isc_quota_detach(&client->recursionquota);
		isc_stats_decrement(ns_g_server->nsstats,
				    dns_nsstatscounter_recursclients);
	}

	/*
	 * Clear all client attributes that are specific to
	 * the request; that's all except the TCP flag.
	 */
	client->attributes &= NS_CLIENTATTR_TCP;
#ifdef ENABLE_AFL
	if (ns_g_fuzz_type == ns_fuzz_client ||
	    ns_g_fuzz_type == ns_fuzz_tcpclient ||
	    ns_g_fuzz_type == ns_fuzz_resolver) {
		named_fuzz_notify();
	}
#endif /* ENABLE_AFL */

}

void
ns_client_next(ns_client_t *client, isc_result_t result) {
	int newstate;

	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(client->state == NS_CLIENTSTATE_WORKING ||
		client->state == NS_CLIENTSTATE_RECURSING ||
		client->state == NS_CLIENTSTATE_READING);

	CTRACE("next");

	if (result != ISC_R_SUCCESS)
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(3),
			      "request failed: %s", isc_result_totext(result));

	/*
	 * An error processing a TCP request may have left
	 * the connection out of sync.  To be safe, we always
	 * sever the connection when result != ISC_R_SUCCESS.
	 */
#ifdef ORIGINAL_ISC_CODE
	if (result == ISC_R_SUCCESS && TCP_CLIENT(client))
#else
	/*
	 * If we restrict multiple TCP requests, we close the connection unless
	 * we're keeping handling SOA queries that could be followed by a zone
	 * transfer (marked as seen_soa_query).  In that case we'll close the
	 * connection after responding to the first request that is not deemed
	 * to be a pre-transfer SOA query, so we'll reset seen_soa_query before
	 * handling the next request.
	 */
	if (result == ISC_R_SUCCESS && TCP_CLIENT(client) &&
	    (!ns_g_server->infoblox_disable_multiple_dns_tcp_request ||
	     client->seen_soa_query))
#endif
		newstate = NS_CLIENTSTATE_READING;
	else
		newstate = NS_CLIENTSTATE_READY;
#ifdef ORIGINAL_ISC_CODE
#else
	/*
	 * Log it if we're closing the TCP connection at the server side
	 * due to the disable_multiple_dns_tcp_request option (and wouldn't be
	 * closed otherwise)
	 */
	if (result == ISC_R_SUCCESS && TCP_CLIENT(client) &&
	    newstate == NS_CLIENTSTATE_READY) {
		char peerbuf[ISC_SOCKADDR_FORMATSIZE];
		ns_client_name(client, peerbuf, sizeof(peerbuf));
		IDEBUG("terminating TCP client: %s", peerbuf);
	}

	client->seen_soa_query = ISC_FALSE;
#endif

	if (client->newstate > newstate)
		client->newstate = newstate;
	(void)exit_check(client);
}


static void
client_senddone(isc_task_t *task, isc_event_t *event) {
	ns_client_t *client;
	isc_socketevent_t *sevent = (isc_socketevent_t *) event;

	REQUIRE(sevent != NULL);
	REQUIRE(sevent->ev_type == ISC_SOCKEVENT_SENDDONE);
	client = sevent->ev_arg;
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(task == client->task);
	REQUIRE(sevent == client->sendevent);

	UNUSED(task);

	CTRACE("senddone");

	if (sevent->result != ISC_R_SUCCESS)
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_WARNING,
			      "error sending response: %s",
			      isc_result_totext(sevent->result));

	INSIST(client->nsends > 0);
	client->nsends--;

	if (client->tcpbuf != NULL) {
		INSIST(TCP_CLIENT(client));
		isc_mem_put(client->mctx, client->tcpbuf, TCP_BUFFER_SIZE);
		client->tcpbuf = NULL;
	}

	ns_client_next(client, ISC_R_SUCCESS);
}

/*%
 * We only want to fail with ISC_R_NOSPACE when called from
 * ns_client_sendraw() and not when called from ns_client_send(),
 * tcpbuffer is NULL when called from ns_client_sendraw() and
 * length != 0.  tcpbuffer != NULL when called from ns_client_send()
 * and length == 0.
 */

static isc_result_t
client_allocsendbuf(ns_client_t *client, isc_buffer_t *buffer,
		    isc_buffer_t *tcpbuffer, isc_uint32_t length,
		    unsigned char *sendbuf, unsigned char **datap)
{
	unsigned char *data;
	isc_uint32_t bufsize;
	isc_result_t result;

	INSIST(datap != NULL);
	INSIST((tcpbuffer == NULL && length != 0) ||
	       (tcpbuffer != NULL && length == 0));

	if (TCP_CLIENT(client)) {
		INSIST(client->tcpbuf == NULL);
		if (length + 2 > TCP_BUFFER_SIZE) {
			result = ISC_R_NOSPACE;
			goto done;
		}
		client->tcpbuf = isc_mem_get(client->mctx, TCP_BUFFER_SIZE);
		if (client->tcpbuf == NULL) {
			result = ISC_R_NOMEMORY;
			goto done;
		}
		data = client->tcpbuf;
		if (tcpbuffer != NULL) {
			isc_buffer_init(tcpbuffer, data, TCP_BUFFER_SIZE);
			isc_buffer_init(buffer, data + 2, TCP_BUFFER_SIZE - 2);
		} else {
			isc_buffer_init(buffer, data, TCP_BUFFER_SIZE);
			INSIST(length <= 0xffff);
			isc_buffer_putuint16(buffer, (isc_uint16_t)length);
		}
	} else {
		data = sendbuf;
		if ((client->attributes & NS_CLIENTATTR_HAVECOOKIE) == 0) {
			if (client->view != NULL)
				bufsize = client->view->nocookieudp;
			else
				bufsize = 512;
		} else
			bufsize = client->udpsize;
		if (bufsize > client->udpsize)
			bufsize = client->udpsize;
		if (bufsize > SEND_BUFFER_SIZE)
			bufsize = SEND_BUFFER_SIZE;
		if (length > bufsize) {
			result = ISC_R_NOSPACE;
			goto done;
		}
		isc_buffer_init(buffer, data, bufsize);
	}
	*datap = data;
	result = ISC_R_SUCCESS;

 done:
	return (result);
}

static isc_result_t
client_sendpkg(ns_client_t *client, isc_buffer_t *buffer) {
	struct in6_pktinfo *pktinfo;
	isc_result_t result;
	isc_region_t r;
	isc_sockaddr_t *address;
	isc_socket_t *sock;
	isc_netaddr_t netaddr;
	int match;
	unsigned int sockflags = ISC_SOCKFLAG_IMMEDIATE;
	isc_dscp_t dispdscp = -1;

	if (TCP_CLIENT(client)) {
		sock = client->tcpsocket;
		address = NULL;
	} else {
		sock = client->udpsocket;
		address = &client->peeraddr;

		isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);
		if (ns_g_server->blackholeacl != NULL &&
		    dns_acl_match(&netaddr, NULL,
				  ns_g_server->blackholeacl,
				  &ns_g_server->aclenv,
				  &match, NULL) == ISC_R_SUCCESS &&
		    match > 0)
			return (DNS_R_BLACKHOLED);
		sockflags |= ISC_SOCKFLAG_NORETRY;
	}

	if ((client->attributes & NS_CLIENTATTR_PKTINFO) != 0 &&
	    (client->attributes & NS_CLIENTATTR_MULTICAST) == 0)
		pktinfo = &client->pktinfo;
	else
		pktinfo = NULL;

	if (client->dispatch != NULL) {
		dispdscp = dns_dispatch_getdscp(client->dispatch);
		if (dispdscp != -1)
			client->dscp = dispdscp;
	}

	if (client->dscp == -1) {
		client->sendevent->attributes &= ~ISC_SOCKEVENTATTR_DSCP;
		client->sendevent->dscp = 0;
	} else {
		client->sendevent->attributes |= ISC_SOCKEVENTATTR_DSCP;
		client->sendevent->dscp = client->dscp;
	}

	isc_buffer_usedregion(buffer, &r);

	/*
	 * If this is a UDP client and the IPv6 packet can't be
	 * encapsulated without generating a PTB on a 1500 octet
	 * MTU link force fragmentation at 1280 if it is a IPv6
	 * response.
	 */
	client->sendevent->attributes &= ~ISC_SOCKEVENTATTR_USEMINMTU;
	if (!TCP_CLIENT(client) && r.length > 1432)
		client->sendevent->attributes |= ISC_SOCKEVENTATTR_USEMINMTU;

	CTRACE("sendto");

#ifdef ORIGINAL_ISC_CODE
#else
	// Establish the shadow fd for the socket
	infoblox_socket_set_shadow_fd(sock, client->shadow_sock_fd);
#endif

	result = isc_socket_sendto2(sock, &r, client->task,
				    address, pktinfo,
				    client->sendevent, sockflags);
	if (result == ISC_R_SUCCESS || result == ISC_R_INPROGRESS) {
		client->nsends++;
		if (result == ISC_R_SUCCESS)
			client_senddone(client->task,
					(isc_event_t *)client->sendevent);
		result = ISC_R_SUCCESS;
	}
	return (result);
}

void
ns_client_sendraw(ns_client_t *client, dns_message_t *message) {
	isc_result_t result;
	unsigned char *data;
	isc_buffer_t buffer;
	isc_region_t r;
	isc_region_t *mr;
	unsigned char sendbuf[SEND_BUFFER_SIZE];

	REQUIRE(NS_CLIENT_VALID(client));

	CTRACE("sendraw");

	mr = dns_message_getrawmessage(message);
	if (mr == NULL) {
		result = ISC_R_UNEXPECTEDEND;
		goto done;
	}

	result = client_allocsendbuf(client, &buffer, NULL, mr->length,
				     sendbuf, &data);
	if (result != ISC_R_SUCCESS)
		goto done;

	/*
	 * Copy message to buffer and fixup id.
	 */
	isc_buffer_availableregion(&buffer, &r);
	result = isc_buffer_copyregion(&buffer, mr);
	if (result != ISC_R_SUCCESS)
		goto done;
	r.base[0] = (client->message->id >> 8) & 0xff;
	r.base[1] = client->message->id & 0xff;

	result = client_sendpkg(client, &buffer);
	if (result == ISC_R_SUCCESS)
		return;

 done:
	if (client->tcpbuf != NULL) {
		isc_mem_put(client->mctx, client->tcpbuf, TCP_BUFFER_SIZE);
		client->tcpbuf = NULL;
	}
	ns_client_next(client, result);
}

#ifdef ORIGINAL_ISC_CODE
#else

static pthread_mutex_t response_lock = PTHREAD_MUTEX_INITIALIZER;

static void
infoblox_log_response_data(ns_client_t *client, response_log_entry_t *log_entry) {
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(log_entry != NULL);

	struct timeval tv;

	if(client->message->rcode == dns_rcode_noerror &&
			!ISC_LIST_EMPTY(client->message->sections[DNS_SECTION_ANSWER])) {
		/* getting info about sender ip */
		if(client->destaddr.family == AF_INET) {
			log_entry->sender_ip.family = AF_INET;
			memmove(&log_entry->sender_ip.addr.in,
	 			&client->destaddr.type.in,
				sizeof(log_entry->sender_ip.addr.in));
		} else if(client->destaddr.family == AF_INET6) {
			log_entry->sender_ip.family = AF_INET6;
			memmove(&log_entry->sender_ip.addr.in6,
				&client->destaddr.type.in6,
				sizeof(log_entry->sender_ip.addr.in6));
		} else {
			goto logdone;
		}

		/* getting time */
		(void)gettimeofday(&tv, NULL);
		log_entry->time = tv.tv_sec;

		/* checking if file exists */
		if(ns_g_server->infoblox_response_log_file == NULL) {
			pthread_mutex_lock(&response_lock);
			if(ns_g_server->infoblox_response_log_file == NULL) {
				/* now we don't have valid opened file to write logs to either
				 * because it is the first query that should be logged or
				 * because something went wrong earlier and new log file wasn't
				 * created so we should try to open new file now.
				 */
				if(infoblox_open_response_log_file(&ns_g_server->infoblox_response_log_file,
							&ns_g_server->infoblox_response_log_file_opened_at) != ISC_R_SUCCESS) {
					pthread_mutex_unlock(&response_lock);
					goto logdone;
				}
			}
			pthread_mutex_unlock(&response_lock);
		}

		/* checking log file size and interval */
		if(ns_g_server->infoblox_response_log_file_size >=
				ns_g_server->infoblox_response_log_file_size_limit ||
				tv.tv_sec - ns_g_server->infoblox_response_log_file_time_limit >=
				ns_g_server->infoblox_response_log_file_opened_at.tv_sec) {
			(void)pthread_mutex_lock(&response_lock);
			/* checking file size and interval again to check if this thread should
			 * create new file or the other one has already created it
			 */
			if(ns_g_server->infoblox_response_log_file_size
					>= ns_g_server->infoblox_response_log_file_size_limit ||
					tv.tv_sec - ns_g_server->infoblox_response_log_file_time_limit >=
					ns_g_server->infoblox_response_log_file_opened_at.tv_sec) {
				FILE *old_file = ns_g_server->infoblox_response_log_file;
				FILE *new_file = NULL;
				struct timeval tval;
				if(infoblox_open_response_log_file(&new_file, &tval) != ISC_R_SUCCESS) {
					/* Don't close old file until new file is created so we
					 * guarantee there always will be valid file pointer on any fwrite.
					 */
					pthread_mutex_unlock(&response_lock);
					goto logdone;
				}
				/* replacing old file with new before closing old file */
				ns_g_server->infoblox_response_log_file = new_file;
				/* here some file size increments can possibly be lost, but using 
				 * fstat() to determine exact file size won't have any effect
				 * until buffers are flushed to disc, so assuming some
				 * inaccuracy in determening file size isn't fatal
				 */
				ns_g_server->infoblox_response_log_file_size = 0;
				if(infoblox_close_response_log_file(&old_file) != ISC_R_SUCCESS) {
					pthread_mutex_unlock(&response_lock);
					goto logdone;
				}
				ns_g_server->infoblox_response_log_file_opened_at = tval;
			}
			pthread_mutex_unlock(&response_lock);
		}

		/* Finally writing to log.
		 * We still need to hold lock on fwrite to prevent the next race condition:
		 * - thread 1 called fwrite, file pointer was passed to fwrite, but
		 *   thread was interrupted before acquiring file lock (inside fwrite)
		 * - thread 2 called fclose and acquired file lock (inside fclose) and
		 *   closed the file, therefore thread 1 now have pointer to closed file.
		 */
		size_t result = 0;
		pthread_mutex_lock(&response_lock);
		/* Using fwrite_unlocked as we are relying on response_lock to protect fwrite */
		result = fwrite_unlocked(log_entry,
				sizeof(response_log_entry_t),
				1,
				ns_g_server->infoblox_response_log_file);
		pthread_mutex_unlock(&response_lock);
		if(result == 1) {
#if defined(ISC_PLATFORM_HAVEXADD)
			(void)isc_atomic_xadd((isc_int32_t*)&ns_g_server->infoblox_response_log_file_size,
								   sizeof(log_entry));
#else
#error "Atomic increment required to build BIND. Please, check your platform settings."
#endif
		} else {
			char strbuf[ISC_STRERRORSIZE];
			isc__strerror(errno, strbuf, sizeof(strbuf));
			infoblox_log(1, "Failed to write log entry to file: %s", strbuf);
		}
	}

logdone:
	return;
}

void
infoblox_format_client_info(ns_client_t *client, isc_buffer_t *target) {
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(ISC_BUFFER_VALID(target));

	// Client formatting from client.c:ns_client_logv
	char peerbuf[ISC_SOCKADDR_FORMATSIZE];
	const char *name = "";
	const char *sep = "";

	// Lifted from client.c:ns_client_name
	if (client->peeraddr_valid)
		isc_sockaddr_format(&client->peeraddr, peerbuf, sizeof(peerbuf));
	else
		snprintf(peerbuf, sizeof(peerbuf), "@%p", client);

	if (client->view != NULL && strcmp(client->view->name, "_bind") != 0 &&
			strcmp(client->view->name, "_default") != 0) {
		name = client->view->name;
		sep = ": view ";
	}

#define CLIENT_LABEL "client "
	infoblox_isc_buffer_putmem(target, (const unsigned char*)CLIENT_LABEL, sizeof(CLIENT_LABEL) - 1);
	infoblox_isc_buffer_putmem(target, (const unsigned char*)peerbuf, strlen(peerbuf));
	infoblox_isc_buffer_putmem(target, (const unsigned char*)sep, strlen(sep));
	infoblox_isc_buffer_putmem(target, (const unsigned char*)name, strlen(name));
	infoblox_isc_buffer_putmem(target, (const unsigned char*)":", 1);
}

isc_result_t
infoblox_get_timestamp(isc_buffer_t *target) {
	REQUIRE(ISC_BUFFER_VALID(target));

	static isc_time_t prevtime = {0, 0};
	static char time_string[64];
	static pthread_mutex_t time_mutex = PTHREAD_MUTEX_INITIALIZER;

	isc_boolean_t have_time = ISC_FALSE;
	isc_time_t nowtime;
	TIME_NOW(&nowtime);

	pthread_mutex_lock(&time_mutex);
	if (nowtime.seconds == prevtime.seconds) {
		unsigned long ms;
		// Leading part (date, hours, minutes, seconds) can be
		// re-used. Are the milliseconds the same as well ?
		if ((ms = nowtime.nanoseconds / 1000000) == prevtime.nanoseconds / 1000000) {
			// Yes, formatted time can be re-used as is
			have_time = ISC_TRUE;
		} else {
			// No, we will need to supply our own milliseconds
			char *dot = strrchr(time_string, '.');
			if (dot) {
				// The milliseconds start at dot+1
				size_t remaining = sizeof(time_string) - (dot - time_string) - 1;
				int ret = snprintf(dot + 1, remaining, "%03lu", ms);
				if (ret > 0 && (size_t)ret < remaining) {
					prevtime = nowtime;
					have_time = ISC_TRUE;
				}
				else {
					pthread_mutex_unlock(&time_mutex);
					infoblox_log(1, "Failed to format timestamp milliseconds");
					return ISC_R_FAILURE;
				}
			}
		}
	}
	if (have_time == ISC_FALSE) {
		isc_time_formattimestamp(&nowtime, time_string, sizeof(time_string));
		/*
		 * as isc_time_formattimestamp does not guarantee a null
		 * terminated string, we need to make that guarantee here.
		 * Otherwise, a subsequent call to strrchr() above could
		 * return an out-of-range pointer and potentially cause a
		 * buffer overrun.
		 */
		time_string[sizeof(time_string) - 1] = '\0';
		prevtime = nowtime;
	}
	infoblox_isc_buffer_putmem(target, (const unsigned char*)time_string, strlen(time_string));
	pthread_mutex_unlock(&time_mutex);

	return ISC_R_SUCCESS;
}

/*
 * A helper for response logging that checks if the given DNS message has any
 * GSLB-synthesized record in its answer section, and returns true/false
 * accordingly.  It examines all RRsets of the answer section to see if
 * any of them has been GSLB-synthesized.  This is not very efficient, but
 * our assumption is that response logging is rarely enabled (and expensive
 * itself anyway) and the overhead is accepted.  Note also that xfrout
 * (with many answer records) render the message separately, so this function
 * won't be called in that context.
 */
static isc_boolean_t
infoblox_has_gslb_answer(dns_message_t *message) {
	isc_result_t result;
	dns_rdataset_t *rdataset;
	dns_name_t *name;

	for (result = dns_message_firstname(message, DNS_SECTION_ANSWER);
	     result == ISC_R_SUCCESS;
	     result = dns_message_nextname(message, DNS_SECTION_ANSWER))
	{
		name = NULL;
		dns_message_currentname(message, DNS_SECTION_ANSWER, &name);
		for (rdataset = ISC_LIST_HEAD(name->list);
		     rdataset != NULL;
		     rdataset = ISC_LIST_NEXT(rdataset, link)) {
			if ((rdataset->attributes & DNS_RDATASETATTR_GSLBANSWER)
			    != 0) {
				return (ISC_TRUE);
			}
		}
	}
	return (ISC_FALSE);
}

/* Setting buffer length to this value is just general assumption. In the case,
 * response is larger than this buffer, only content up to buffer length will
 * be logged.
 */
#define INFOBLOX_RESPONSE_BUFFER_LENGTH 8192
#endif

static void
client_send(ns_client_t *client) {
	isc_result_t result;
	unsigned char *data;
	isc_buffer_t buffer;
	isc_buffer_t tcpbuffer;
	isc_region_t r;
	dns_compress_t cctx;
	isc_boolean_t cleanup_cctx = ISC_FALSE;
	unsigned char sendbuf[SEND_BUFFER_SIZE];
	unsigned int render_opts;
	unsigned int preferred_glue;
	isc_boolean_t opt_included = ISC_FALSE;
	size_t respsize;
	isc_boolean_t truncate;
#ifdef HAVE_DNSTAP
	unsigned char zone[DNS_NAME_MAXWIRE];
	dns_dtmsgtype_t dtmsgtype;
	isc_region_t zr;
#endif /* HAVE_DNSTAP */

#ifdef ORIGINAL_ISC_CODE
#else
	isc_boolean_t log_response = ISC_FALSE;
	response_log_entry_t log_entry;
	isc_boolean_t detailed_log_response = ISC_FALSE;
	isc_boolean_t detailed_log_response_tosyslog = ISC_FALSE;
	char response_buffer[INFOBLOX_RESPONSE_BUFFER_LENGTH];
	isc_buffer_t isc_response_buffer;
	/* We need this additional buffer here because functions that are used
	 * (dns_rdata_totext, dns_name_totext, dns_rdataclass_totext, dns_rdatatype_totext)
	 * for formatiing response don't take char* argument, but isc_buffer_t*.
	 */
#endif

	REQUIRE(NS_CLIENT_VALID(client));

	CTRACE("send");

	if (client->message->opcode == dns_opcode_query &&
	    (client->attributes & NS_CLIENTATTR_RA) != 0)
		client->message->flags |= DNS_MESSAGEFLAG_RA;

	if ((client->attributes & NS_CLIENTATTR_WANTDNSSEC) != 0)
		render_opts = 0;
	else
		render_opts = DNS_MESSAGERENDER_OMITDNSSEC;

	preferred_glue = 0;
	if (client->view != NULL) {
		if (client->view->preferred_glue == dns_rdatatype_a)
			preferred_glue = DNS_MESSAGERENDER_PREFER_A;
		else if (client->view->preferred_glue == dns_rdatatype_aaaa)
			preferred_glue = DNS_MESSAGERENDER_PREFER_AAAA;
	}
	if (preferred_glue == 0) {
		if (isc_sockaddr_pf(&client->peeraddr) == AF_INET)
			preferred_glue = DNS_MESSAGERENDER_PREFER_A;
		else
			preferred_glue = DNS_MESSAGERENDER_PREFER_AAAA;
	}

#ifdef ALLOW_FILTER_AAAA
	/*
	 * filter-aaaa-on-v4 yes or break-dnssec option to suppress
	 * AAAA records.
	 *
	 * We already know that request came via IPv4,
	 * that we have both AAAA and A records,
	 * and that we either have no signatures that the client wants
	 * or we are supposed to break DNSSEC.
	 *
	 * Override preferred glue if necessary.
	 */
	if ((client->attributes & NS_CLIENTATTR_FILTER_AAAA) != 0) {
		render_opts |= DNS_MESSAGERENDER_FILTER_AAAA;
		if (preferred_glue == DNS_MESSAGERENDER_PREFER_AAAA)
			preferred_glue = DNS_MESSAGERENDER_PREFER_A;
	}
#endif

	/*
	 * Create an OPT for our reply.
	 */
	if ((client->attributes & NS_CLIENTATTR_WANTOPT) != 0) {
		result = ns_client_addopt(client, client->message,
					  &client->opt);
		if (result != ISC_R_SUCCESS)
			goto done;
	}

	/*
	 * XXXRTH  The following doesn't deal with TCP buffer resizing.
	 */
	result = client_allocsendbuf(client, &buffer, &tcpbuffer, 0,
				     sendbuf, &data);
	if (result != ISC_R_SUCCESS)
		goto done;

	/*
	 * Stop after the question if TC was set for rate limiting.
	 */
	truncate = ISC_TF((client->message->flags & DNS_MESSAGEFLAG_TC) != 0);

 again:
	result = dns_compress_init(&cctx, -1, client->mctx);
	if (result != ISC_R_SUCCESS)
		goto done;
#ifdef ORIGINAL_ISC_CODE
	if (client->peeraddr_valid && client->view != NULL) {
		isc_netaddr_t netaddr;
		dns_name_t *name = NULL;

		isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);
		if (client->message->tsigkey != NULL)
			name = &client->message->tsigkey->name;

		if (client->view->nocasecompress == NULL ||
		    !allowed(&netaddr, name, NULL,
			     client->view->nocasecompress))
		{
			dns_compress_setsensitive(&cctx, ISC_TRUE);
		}

		if (client->view->msgcompression == ISC_FALSE) {
			dns_compress_disable(&cctx);
		}
	}
#else
	/*
	 * BIND 9.10 changes the compression policy so case sensitive
	 * compression will be used by default (see change #3645) for unclear
	 * reason.  Until/unless we know the rationale and understand it's
	 * really necessary, we'll keep the previous behavior.
	 */
#endif
	cleanup_cctx = ISC_TRUE;

	result = dns_message_renderbegin(client->message, &cctx, &buffer);
	if (result != ISC_R_SUCCESS)
		goto done;

	if (client->opt != NULL) {
		result = dns_message_setopt(client->message, client->opt);
		opt_included = ISC_TRUE;
		client->opt = NULL;
		if (result != ISC_R_SUCCESS)
			goto done;
	}
	result = dns_message_rendersection(client->message,
					   DNS_SECTION_QUESTION, 0);
	if (result == ISC_R_NOSPACE) {
		client->message->flags |= DNS_MESSAGEFLAG_TC;
		goto renderend;
	}
	if (result != ISC_R_SUCCESS)
		goto done;

	/*
	 * Stop after the question if TC was set for rate limiting.
	 */
	if (truncate)
		goto renderend;

#ifdef ORIGINAL_ISC_CODE
#else
	/* EDNSL project */
	if (infoblox_g_capture_dns_responses == ISC_TRUE) {
		if (client->response_logging_view != NULL) {
			if (client->response_logging_view->recursion == ISC_TRUE) {
				memset(&log_entry, 0, sizeof(log_entry));
				log_entry.resolved_ip.family = AF_UNSPEC;
				if (infoblox_set_response_log_entry(&log_entry) == ISC_R_SUCCESS) {
					log_response = ISC_TRUE;
				}
			} else {
				/* if response logging is enabled and recursion is disabled on
				 * a view, set log_entry to NULL, that means this particular
				 * response shouldn't be logged
				 */
				infoblox_set_response_log_entry(NULL);
			}
			dns_view_detach(&client->response_logging_view);
		} else if (client->message->opcode == dns_opcode_query) {
			ns_client_log(client, NS_LOGCATEGORY_CLIENT,
					NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
					"Unable to determine view to log response.");
		}
	}

	/* DNSRL project */
	if (infoblox_g_detailed_response_logging == ISC_TRUE  &&
			client->message->opcode == dns_opcode_query) {
		/* not logging updates */
		if (ns_g_server->infoblox_detailed_response_logging_tosyslog == ISC_TRUE) {
			detailed_log_response_tosyslog = ISC_TRUE;
		}
		if (ns_g_server->infoblox_detailed_response_logging == ISC_TRUE &&
				infoblox_should_log_dns_data(client) == ISC_TRUE) {
			/* We need to filter response through include/exclude lists only if
			 * logging it to reporting. Filtering isn't applied to syslog.
			 */
			detailed_log_response = ISC_TRUE;
		}
		if (detailed_log_response == ISC_TRUE ||
				detailed_log_response_tosyslog == ISC_TRUE) {
			isc_buffer_init(&isc_response_buffer,
					response_buffer,
					INFOBLOX_RESPONSE_BUFFER_LENGTH - 1);
			if (infoblox_get_timestamp(&isc_response_buffer) != ISC_R_SUCCESS) {
#define FAILTMMSG "<Failed to get timestamp>"
				infoblox_isc_buffer_putmem(&isc_response_buffer,
						(const unsigned char*)FAILTMMSG,
						sizeof(FAILTMMSG) - 1);
			}
			infoblox_isc_buffer_putmem(&isc_response_buffer, (const unsigned char*)" ", 1);
			infoblox_format_client_info(client, &isc_response_buffer);
			if (TCP_CLIENT(client)) {
#define TCP_LABEL " TCP: "
				infoblox_isc_buffer_putmem(&isc_response_buffer,
						(const unsigned char*)TCP_LABEL,
						sizeof(TCP_LABEL) - 1);
			} else {
#define UDP_LABEL " UDP: "
				infoblox_isc_buffer_putmem(&isc_response_buffer,
						(const unsigned char*)UDP_LABEL,
						sizeof(UDP_LABEL) - 1);
			}
			infoblox_format_query_info(client, &isc_response_buffer);
#define RESPONSE_LABEL " response: "
			infoblox_isc_buffer_putmem(&isc_response_buffer,
					(const unsigned char*)RESPONSE_LABEL,
					sizeof(RESPONSE_LABEL) - 1);
			dns_rcode_totext(client->message->rcode, &isc_response_buffer);
			if ((client->message->flags & DNS_MESSAGEFLAG_RA) != 0)
				infoblox_isc_buffer_putmem(&isc_response_buffer, (const unsigned char*)" +", 2);
			else
				infoblox_isc_buffer_putmem(&isc_response_buffer, (const unsigned char*)" -", 2);
			if ((client->message->flags & DNS_MESSAGEFLAG_AA) != 0)
				infoblox_isc_buffer_putmem(&isc_response_buffer, (const unsigned char*)"A", 1);
			if ((client->message->flags & DNS_MESSAGEFLAG_TC) != 0)
				infoblox_isc_buffer_putmem(&isc_response_buffer, (const unsigned char*)"t", 1);
			dns_rdataset_t *opts = dns_message_getopt(client->message);
			if (opts != NULL) {
				infoblox_isc_buffer_putmem(&isc_response_buffer, (const unsigned char*)"E", 1);
				/* lifted from dns_message_pseudosectiontotext() */
				if ((opts->ttl & DNS_MESSAGEEXTFLAG_DO) != 0) {
					infoblox_isc_buffer_putmem(&isc_response_buffer, (const unsigned char*)"D", 1);
				}
			}
			if ((client->message->flags & DNS_MESSAGEFLAG_AD) != 0)
				infoblox_isc_buffer_putmem(&isc_response_buffer, (const unsigned char*)"V", 1);
			if (infoblox_has_gslb_answer(client->message)) {
				infoblox_isc_buffer_putmem(&isc_response_buffer,
							   (const unsigned char *)"L", 1);
			}
			/* Here we set TSD data only for noerror case as only content of
			 * noerror responses should be logged.
			 */
			if (client->message->rcode == dns_rcode_noerror) {
				infoblox_isc_buffer_putmem(&isc_response_buffer, (const unsigned char*)" ", 1);
				infoblox_set_response_log_buffer(&isc_response_buffer);
			} else {
				infoblox_set_response_log_buffer(NULL);
			}
		} else {
			infoblox_set_response_log_buffer(NULL);
		}
	}
#endif

	result = dns_message_rendersection(client->message,
					   DNS_SECTION_ANSWER,
					   DNS_MESSAGERENDER_PARTIAL |
					   render_opts);
#ifdef ORIGINAL_ISC_CODE
#else
	/* Setting TSD data to zero here to minimize impact on rendering of
	 * authority and additional sections if neither goto done nor
	 * goto renderend was triggered after rendering of answer section.
	 */
	if (log_response)
		infoblox_set_response_log_entry(NULL);
	if (detailed_log_response || detailed_log_response_tosyslog)
		infoblox_set_response_log_buffer(NULL);
#endif
	if (result == ISC_R_NOSPACE) {
		client->message->flags |= DNS_MESSAGEFLAG_TC;
		goto renderend;
	}
	if (result != ISC_R_SUCCESS)
		goto done;
	result = dns_message_rendersection(client->message,
					   DNS_SECTION_AUTHORITY,
					   DNS_MESSAGERENDER_PARTIAL |
					   render_opts);
	if (result == ISC_R_NOSPACE) {
		client->message->flags |= DNS_MESSAGEFLAG_TC;
		goto renderend;
	}
	if (result != ISC_R_SUCCESS)
		goto done;
	result = dns_message_rendersection(client->message,
					   DNS_SECTION_ADDITIONAL,
					   preferred_glue | render_opts);
	if (result != ISC_R_SUCCESS && result != ISC_R_NOSPACE)
		goto done;
 renderend:
	result = dns_message_renderend(client->message);

	if (result != ISC_R_SUCCESS)
		goto done;

#ifdef HAVE_DNSTAP
	memset(&zr, 0, sizeof(zr));
	if (((client->message->flags & DNS_MESSAGEFLAG_AA) != 0) &&
	    (client->query.authzone != NULL))
	{
		isc_buffer_t b;
		dns_name_t *zo =
			dns_zone_getorigin(client->query.authzone);

		isc_buffer_init(&b, zone, sizeof(zone));
		dns_compress_setmethods(&cctx, DNS_COMPRESS_NONE);
		result = dns_name_towire(zo, &cctx, &b);
		if (result == ISC_R_SUCCESS)
			isc_buffer_usedregion(&b, &zr);
	}

	if ((client->message->flags & DNS_MESSAGEFLAG_RD) != 0)
		dtmsgtype = DNS_DTTYPE_CR;
	else
		dtmsgtype = DNS_DTTYPE_AR;
#endif /* HAVE_DNSTAP */

	if (cleanup_cctx) {
		dns_compress_invalidate(&cctx);
		cleanup_cctx = ISC_FALSE;
	}

	if (!truncate && client->view != NULL && client->view->rrls != NULL &&
	    client->query.resp_result == ISC_R_SUCCESS) {
		isc_boolean_t wouldlog;
		char log_buf[DNS_RRL_LOG_BUF_LEN];
		dns_rrl_result_t rrl_result;
		unsigned len = isc_buffer_usedlength(&buffer);
		isc_uint32_t ratio;

		if (client->requestsize > 0)
			ratio = len * 100 / client->requestsize;
		else
			ratio = 0;

		wouldlog = isc_log_wouldlog(ns_g_lctx, DNS_RRL_LOG_DROP);
		rrl_result = dns_rrl(client->view, &client->peeraddr,
				     TCP_CLIENT(client), client->view->rdclass,
				     client->query.qtype,
				     dns_fixedname_name(&client->query.fname),
				     ISC_R_SUCCESS, client->now, ratio, len,
				     wouldlog, ISC_FALSE, log_buf,
				     sizeof(log_buf));
		if (rrl_result != DNS_RRL_RESULT_OK) {
			/*
			 * Log dropped errors in the query category
			 * so that they are not lost in silence.
			 * Starts of rate-limited bursts are logged in
			 * NS_LOGCATEGORY_RRL.
			 */
			if (wouldlog) {
				ns_client_log(client,
					      NS_LOGCATEGORY_QUERY_ERRORS,
					      NS_LOGMODULE_CLIENT,
					      DNS_RRL_LOG_DROP,
					      "%s", log_buf);
			}
			if (rrl_result == DNS_RRL_RESULT_LOGONLY)
				goto log_only;

			if (rrl_result == DNS_RRL_RESULT_DROP) {
				ns_query_incstats(client,
					       dns_nsstatscounter_ratedropped);
				result = DNS_R_DROP;
				goto done;
			}
			/*
			 * Regenerate the response to set TC=1 without any
			 * amplification.
			 */
			ns_query_incstats(client,
					  dns_nsstatscounter_rateslipped);
			dns_message_renderreset(client->message);
			client->message->flags |= DNS_MESSAGEFLAG_TC;
			truncate = ISC_TRUE;
			goto again;
		}
	}

 log_only:
	if (TCP_CLIENT(client)) {
		isc_buffer_usedregion(&buffer, &r);
		isc_buffer_putuint16(&tcpbuffer, (isc_uint16_t) r.length);
		isc_buffer_add(&tcpbuffer, r.length);
#ifdef HAVE_DNSTAP
		if (client->view != NULL) {
			dns_dt_send(client->view, dtmsgtype,
				    &client->peeraddr, &client->interface->addr,
				    ISC_TRUE, &zr, &client->requesttime, NULL,
				    &buffer);
		}
#endif /* HAVE_DNSTAP */

		/* don't count the 2-octet length header */
		respsize = isc_buffer_usedlength(&tcpbuffer) - 2;
		result = client_sendpkg(client, &tcpbuffer);

		switch (isc_sockaddr_pf(&client->peeraddr)) {
		case AF_INET:
			isc_stats_increment(ns_g_server->tcpoutstats4,
					    ISC_MIN((int)respsize / 16, 256));
			break;
		case AF_INET6:
			isc_stats_increment(ns_g_server->tcpoutstats6,
					    ISC_MIN((int)respsize / 16, 256));
			break;
		default:
			INSIST(0);
			break;
		}
	} else {
		respsize = isc_buffer_usedlength(&buffer);
		result = client_sendpkg(client, &buffer);
#ifdef HAVE_DNSTAP
		if (client->view != NULL) {
			dns_dt_send(client->view, dtmsgtype,
				    &client->peeraddr,
				    &client->interface->addr,
				    ISC_FALSE, &zr,
				    &client->requesttime, NULL, &buffer);
		}
#endif /* HAVE_DNSTAP */

		switch (isc_sockaddr_pf(&client->peeraddr)) {
		case AF_INET:
			isc_stats_increment(ns_g_server->udpoutstats4,
					    ISC_MIN((int)respsize / 16, 256));
			break;
		case AF_INET6:
			isc_stats_increment(ns_g_server->udpoutstats6,
					    ISC_MIN((int)respsize / 16, 256));
			break;
		default:
			INSIST(0);
			break;
		}
	}

#ifdef ORIGINAL_ISC_CODE
#else
	/* Finally writting logs after answer has been sent to client.
	 * Pay special attention to the fact that 'client' variable should not
	 * be used after client_sendpkg() call as some of its' fields are getting
	 * invalidated after this call.
	 */
	
	/* EDNSL project
	 * We should log response only if we've found at least single a/aaaa record
	 * therefore checking resolved_ip.family field. If it's still AF_UNSPEC,
	 * then no a/aaaa record was found.
	 */
	if (log_response == ISC_TRUE &&
			result == ISC_R_SUCCESS &&
			log_entry.resolved_ip.family != AF_UNSPEC) {
		infoblox_log_response_data(client, &log_entry);
		infoblox_set_response_log_entry(NULL);
	}

	/* DNSRL project */
	if ((detailed_log_response == ISC_TRUE ||
	     detailed_log_response_tosyslog == ISC_TRUE) &&
	    result == ISC_R_SUCCESS) {
		isc_region_t r;

		/*
		 * NL-terminate the (isc) buffer, and nul-terminate the
		 * underlying char-buffer.  The INSIST must hold since there
		 * must be at least a '\n', and the size of  response_buffer
		 * is larger than the max length of the ISC buffer.
		 */
		infoblox_response_log_finish(&isc_response_buffer);
		isc_buffer_usedregion(&isc_response_buffer, &r);
		INSIST(r.length > 0 && r.length < sizeof(response_buffer));
		response_buffer[r.length] = '\0';
		
        if (detailed_log_response_tosyslog == ISC_TRUE) {
			/* using isc_log_write instead of ns_client_log because client info
			 * has been already formatted.  We should exclude the
			 * NL from this log. */
			isc_log_write(ns_g_lctx,
				      NS_LOGCATEGORY_IB_RESPONSES,
				      NS_LOGMODULE_CLIENT,
				      ISC_LOG_INFO,
				      "%.*s", r.length - 1, r.base);
		}
		/* checking include/exclude lists for reporting logging */
		if (detailed_log_response == ISC_TRUE) {
			/* no explicit handling of any error required here as
			 * log_dns_entry() produces all required error messages to syslog
			 */
			(void)infoblox_log_dns_entry(response_buffer);
		}
		infoblox_set_response_log_buffer(NULL);
		isc_buffer_invalidate(&isc_response_buffer);
	}
#endif

	/* update statistics (XXXJT: is it okay to access message->xxxkey?) */
	isc_stats_increment(ns_g_server->nsstats, dns_nsstatscounter_response);

	dns_rcodestats_increment(ns_g_server->rcodestats,
				 client->message->rcode);
	if (opt_included) {
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_edns0out);
	}
	if (client->message->tsigkey != NULL) {
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_tsigout);
	}
	if (client->message->sig0key != NULL) {
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_sig0out);
	}
	if ((client->message->flags & DNS_MESSAGEFLAG_TC) != 0)
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_truncatedresp);

	if (result == ISC_R_SUCCESS)
		return;

 done:

#ifdef ORIGINAL_ISC_CODE
#else
	if (log_response == ISC_TRUE) {
		infoblox_set_response_log_entry(NULL);
	}
	if (detailed_log_response == ISC_TRUE ||
			detailed_log_response_tosyslog == ISC_TRUE) {
		infoblox_set_response_log_buffer(NULL);
		if (ISC_BUFFER_VALID(&isc_response_buffer)) {
			isc_buffer_invalidate(&isc_response_buffer);
		}
	}
#endif

	if (client->tcpbuf != NULL) {
		isc_mem_put(client->mctx, client->tcpbuf, TCP_BUFFER_SIZE);
		client->tcpbuf = NULL;
	}

	if (cleanup_cctx)
		dns_compress_invalidate(&cctx);

	ns_client_next(client, result);
}

/*
 * Completes the sending of a delayed client response.
 */
static void
client_delay(isc_task_t *task, isc_event_t *event) {
	ns_client_t *client;

	REQUIRE(event != NULL);
	REQUIRE(event->ev_type == ISC_TIMEREVENT_LIFE ||
		event->ev_type == ISC_TIMEREVENT_IDLE);
	client = event->ev_arg;
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(task == client->task);
	REQUIRE(client->delaytimer != NULL);

	UNUSED(task);

	CTRACE("client_delay");

	isc_event_free(&event);
	isc_timer_detach(&client->delaytimer);

	client_send(client);
	ns_client_detach(&client);
}

void
ns_client_send(ns_client_t *client) {

	/*
	 * Delay the response by ns_g_delay ms.
	 */
	if (ns_g_delay != 0) {
		ns_client_t *dummy = NULL;
		isc_result_t result;
		isc_interval_t interval;

		/*
		 * Replace ourselves if we have not already been replaced.
		 */
		if (!client->mortal) {
			result = ns_client_replace(client);
			if (result != ISC_R_SUCCESS)
				goto nodelay;
		}

		ns_client_attach(client, &dummy);
		if (ns_g_delay >= 1000)
			isc_interval_set(&interval, ns_g_delay / 1000,
					 (ns_g_delay % 1000) * 1000000);
		else
			isc_interval_set(&interval, 0, ns_g_delay * 1000000);
		result = isc_timer_create(client->manager->timermgr,
					  isc_timertype_once, NULL, &interval,
					  client->task, client_delay,
					  client, &client->delaytimer);
		if (result == ISC_R_SUCCESS)
			return;

		ns_client_detach(&dummy);
	}

 nodelay:
	client_send(client);
}

#if NS_CLIENT_DROPPORT
#define DROPPORT_NO		0
#define DROPPORT_REQUEST	1
#define DROPPORT_RESPONSE	2
/*%
 * ns_client_dropport determines if certain requests / responses
 * should be dropped based on the port number.
 *
 * Returns:
 * \li	0:	Don't drop.
 * \li	1:	Drop request.
 * \li	2:	Drop (error) response.
 */
static int
ns_client_dropport(in_port_t port) {
	switch (port) {
	case 7: /* echo */
	case 13: /* daytime */
	case 19: /* chargen */
	case 37: /* time */
		return (DROPPORT_REQUEST);
	case 464: /* kpasswd */
		return (DROPPORT_RESPONSE);
	}
	return (DROPPORT_NO);
}
#endif

void
ns_client_error(ns_client_t *client, isc_result_t result) {
	dns_rcode_t rcode;
	dns_message_t *message;

	REQUIRE(NS_CLIENT_VALID(client));

	CTRACE("error");

	message = client->message;
	rcode = dns_result_torcode(result);

#if NS_CLIENT_DROPPORT
	/*
	 * Don't send FORMERR to ports on the drop port list.
	 */
	if (rcode == dns_rcode_formerr &&
	    ns_client_dropport(isc_sockaddr_getport(&client->peeraddr)) !=
	    DROPPORT_NO) {
		char buf[64];
		isc_buffer_t b;

		isc_buffer_init(&b, buf, sizeof(buf) - 1);
		if (dns_rcode_totext(rcode, &b) != ISC_R_SUCCESS)
			isc_buffer_putstr(&b, "UNKNOWN RCODE");
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(10),
			      "dropped error (%.*s) response: suspicious port",
			      (int)isc_buffer_usedlength(&b), buf);
		ns_client_next(client, ISC_R_SUCCESS);
		return;
	}
#endif

	/*
	 * Try to rate limit error responses.
	 */
	if (client->view != NULL && client->view->rrls != NULL) {
		isc_boolean_t wouldlog;
		char log_buf[DNS_RRL_LOG_BUF_LEN];
		dns_rrl_result_t rrl_result;
		int loglevel;

		INSIST(rcode != dns_rcode_noerror &&
		       rcode != dns_rcode_nxdomain);
		if (ns_g_server->log_queries)
			loglevel = DNS_RRL_LOG_DROP;
		else
			loglevel = ISC_LOG_DEBUG(1);
		wouldlog = isc_log_wouldlog(ns_g_lctx, loglevel);
		rrl_result = dns_rrl(client->view, &client->peeraddr,
				     TCP_CLIENT(client),
				     dns_rdataclass_in, dns_rdatatype_none,
				     NULL, result, client->now, 0, 0,
				     wouldlog, ISC_TRUE,
				     log_buf, sizeof(log_buf));
		if (rrl_result != DNS_RRL_RESULT_OK) {
			/*
			 * Log dropped errors in the query category
			 * so that they are not lost in silence.
			 * Starts of rate-limited bursts are logged in
			 * NS_LOGCATEGORY_RRL.
			 */
			if (wouldlog) {
				ns_client_log(client,
					      NS_LOGCATEGORY_QUERY_ERRORS,
					      NS_LOGMODULE_CLIENT,
					      loglevel,
					      "%s", log_buf);
			}

			/* This should never return SLIP */
			if (rrl_result != DNS_RRL_RESULT_LOGONLY) {
				isc_stats_increment(ns_g_server->nsstats,
						dns_nsstatscounter_ratedropped);
				isc_stats_increment(ns_g_server->nsstats,
						dns_nsstatscounter_dropped);
				ns_client_next(client, DNS_R_DROP);
				return;
			}
		}
	}

	/*
	 * Message may be an in-progress reply that we had trouble
	 * with, in which case QR will be set.  We need to clear QR before
	 * calling dns_message_reply() to avoid triggering an assertion.
	 */
	message->flags &= ~DNS_MESSAGEFLAG_QR;
	/*
	 * AA and AD shouldn't be set.
	 */
	message->flags &= ~(DNS_MESSAGEFLAG_AA | DNS_MESSAGEFLAG_AD);
	result = dns_message_reply(message, ISC_TRUE);
	if (result != ISC_R_SUCCESS) {
		/*
		 * It could be that we've got a query with a good header,
		 * but a bad question section, so we try again with
		 * want_question_section set to ISC_FALSE.
		 */
		result = dns_message_reply(message, ISC_FALSE);
		if (result != ISC_R_SUCCESS) {
			ns_client_next(client, result);
			return;
		}
	}
	message->rcode = rcode;

	if (rcode == dns_rcode_formerr) {
		/*
		 * FORMERR loop avoidance:  If we sent a FORMERR message
		 * with the same ID to the same client less than two
		 * seconds ago, assume that we are in an infinite error
		 * packet dialog with a server for some protocol whose
		 * error responses look enough like DNS queries to
		 * elicit a FORMERR response.  Drop a packet to break
		 * the loop.
		 */
		if (isc_sockaddr_equal(&client->peeraddr,
				       &client->formerrcache.addr) &&
		    message->id == client->formerrcache.id &&
		    (isc_time_seconds(&client->requesttime) -
		     client->formerrcache.time) < 2)
		{
			/* Drop packet. */
			ns_client_log(client, NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(1),
				      "possible error packet loop, "
				      "FORMERR dropped");
			ns_client_next(client, result);
			return;
		}
		client->formerrcache.addr = client->peeraddr;
		client->formerrcache.time =
			isc_time_seconds(&client->requesttime);
		client->formerrcache.id = message->id;
	} else if (rcode == dns_rcode_servfail && client->query.qname != NULL &&
		   client->view != NULL && client->view->fail_ttl != 0 &&
		   ((client->attributes & NS_CLIENTATTR_NOSETFC) == 0))
	{
		/*
		 * SERVFAIL caching: store qname/qtype of failed queries
		 */
		isc_time_t expire;
		isc_interval_t i;
		isc_uint32_t flags = 0;

		if ((message->flags & DNS_MESSAGEFLAG_CD) != 0)
			flags = NS_FAILCACHE_CD;

		isc_interval_set(&i, client->view->fail_ttl, 0);
		result = isc_time_nowplusinterval(&expire, &i);
		if (result == ISC_R_SUCCESS)
			dns_badcache_add(client->view->failcache,
					 client->query.qname,
					 client->query.qtype,
					 ISC_TRUE, flags, &expire);
	}
	ns_client_send(client);
}

isc_result_t
ns_client_addopt(ns_client_t *client, dns_message_t *message,
		 dns_rdataset_t **opt)
{
	unsigned char ecs[ECS_SIZE];
	char nsid[BUFSIZ], *nsidp;
	unsigned char cookie[COOKIE_SIZE];
	isc_result_t result;
	dns_view_t *view;
	dns_resolver_t *resolver;
	isc_uint16_t udpsize;
	dns_ednsopt_t ednsopts[DNS_EDNSOPTIONS];
	int count = 0;
	unsigned int flags;
	unsigned char expire[4];
	unsigned char advtimo[2];

	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(opt != NULL && *opt == NULL);
	REQUIRE(message != NULL);

	view = client->view;
	resolver = (view != NULL) ? view->resolver : NULL;
	if (resolver != NULL)
		udpsize = dns_resolver_getudpsize(resolver);
	else
		udpsize = ns_g_udpsize;

	flags = client->extflags & DNS_MESSAGEEXTFLAG_REPLYPRESERVE;

	/* Set EDNS options if applicable */
	if (WANTNSID(client) &&
	    (ns_g_server->server_id != NULL ||
	     ns_g_server->server_usehostname))
	{
		if (ns_g_server->server_usehostname) {
			result = ns_os_gethostname(nsid, sizeof(nsid));
			if (result != ISC_R_SUCCESS) {
				goto no_nsid;
			}
			nsidp = nsid;
		} else
			nsidp = ns_g_server->server_id;

		INSIST(count < DNS_EDNSOPTIONS);
		ednsopts[count].code = DNS_OPT_NSID;
		ednsopts[count].length = (isc_uint16_t)strlen(nsidp);
		ednsopts[count].value = (unsigned char *)nsidp;
		count++;
	}
 no_nsid:
	if ((client->attributes & NS_CLIENTATTR_WANTCOOKIE) != 0) {
		isc_buffer_t buf;
		isc_stdtime_t now;
		isc_uint32_t nonce;

		isc_buffer_init(&buf, cookie, sizeof(cookie));
		isc_stdtime_get(&now);
		isc_random_get(&nonce);

		compute_cookie(client, now, nonce, &buf);

		INSIST(count < DNS_EDNSOPTIONS);
		ednsopts[count].code = DNS_OPT_COOKIE;
		ednsopts[count].length = COOKIE_SIZE;
		ednsopts[count].value = cookie;
		count++;
	}
	if ((client->attributes & NS_CLIENTATTR_HAVEEXPIRE) != 0) {
		isc_buffer_t buf;

		INSIST(count < DNS_EDNSOPTIONS);

		isc_buffer_init(&buf, expire, sizeof(expire));
		isc_buffer_putuint32(&buf, client->expire);
		ednsopts[count].code = DNS_OPT_EXPIRE;
		ednsopts[count].length = 4;
		ednsopts[count].value = expire;
		count++;
	}
	if (ECS_RECEIVED(client) &&
#ifdef ORIGINAL_ISC_CODE
#else
	    (infoblox_client_subnet_recursive_enabled() ||
	     (client->ecs_opt && client->ecs_opt->enabled)) &&
#endif
	    (client->ecs.addr.family == AF_INET ||
	     client->ecs.addr.family == AF_INET6 ||
	     client->ecs.addr.family == AF_UNSPEC))
	{
		isc_buffer_t buf;
		isc_uint8_t addr[16];
		isc_uint32_t plen, addrl;
		isc_uint16_t family;

		/* Add CLIENT-SUBNET option. */

		plen = client->ecs.source;

		/* Round up prefix len to a multiple of 8 */
		addrl = (plen + 7) / 8;

		/* This must be enforced elsewhere in code. */
		INSIST(client->ecs.addr.family != AF_UNSPEC ||
		       client->ecs.source == 0);

		/*
		 * XXXMUKS If nothing set the scope, then it might be a
		 * non-ECS answer, but we have to return the ECS option
		 * in the reply. So set it to 0 here. This is better
		 * handled differently, i.e., we ensure that in the case
		 * of non-ECS answers, the caller sets scope = 0
		 * somewhere.
		 */
		if (client->ecs.scope == 0xff)
			client->ecs.scope = 0;

		switch (client->ecs.addr.family) {
		case AF_UNSPEC:
			INSIST(plen == 0);
			family = 0;
			break;
		case AF_INET:
			INSIST(plen <= 32);
			family = 1;
			memmove(addr, &client->ecs.addr.type, addrl);
			break;
		case AF_INET6:
			INSIST(plen <= 128);
			family = 2;
			memmove(addr, &client->ecs.addr.type, addrl);
			break;
		default:
			INSIST(0);
		}

		isc_buffer_init(&buf, ecs, sizeof(ecs));
		/* family */
		isc_buffer_putuint16(&buf, family);
		/* source prefix-length */
		isc_buffer_putuint8(&buf, plen);
		/* scope prefix-length */
#ifdef ORIGINAL_ISC_CODE
		isc_buffer_putuint8(&buf, client->ecs.scope);
#else
		if (client->ecs_opt->enabled) // DTC
			isc_buffer_putuint8(&buf, client->ecs_opt->scope_prefix_length);
		else
			isc_buffer_putuint8(&buf, client->ecs.scope);
#endif
		/* address */
		if (addrl > 0) {
			/* Mask off last address byte */
			if ((plen % 8) != 0)
				addr[addrl - 1] &=
					~0U << (8 - (plen % 8));
			isc_buffer_putmem(&buf, addr,
					  (unsigned) addrl);
		}

		ednsopts[count].code = DNS_OPT_CLIENT_SUBNET;
		ednsopts[count].length = addrl + 4;
		ednsopts[count].value = ecs;
		count++;
	}
	if (TCP_CLIENT(client) && USEKEEPALIVE(client)) {
		isc_buffer_t buf;

		INSIST(count < DNS_EDNSOPTIONS);

		isc_buffer_init(&buf, advtimo, sizeof(advtimo));
		isc_buffer_putuint16(&buf, (isc_uint16_t) ns_g_advertisedtimo);
		ednsopts[count].code = DNS_OPT_TCP_KEEPALIVE;
		ednsopts[count].length = 2;
		ednsopts[count].value = advtimo;
		count++;
	}
#ifdef ORIGINAL_ISC_CODE
#else
	unsigned char catbuf[16]; /* must be valid throughout the function */
	if (client->subscriber != NULL &&
	    (client->subscriber->edns_category_opt ==
	     DNS_OPT_IB_SUBSCRIBER_CATEGORY1 ||
	     client->subscriber->edns_category_opt ==
	     DNS_OPT_IB_SUBSCRIBER_CATEGORY2)) {
		isc_buffer_t buf;
		int i;

		INSIST(count < DNS_EDNSOPTIONS);

		/* CATEGORY1 is supposed to be used only when OPT RR would not
		 * be included otherwise, so there shouldn't be other options.
		 * If the assumption is broken it's a bug and should be fixed.
		 * So we'll drop the query. */
		if (client->subscriber->edns_category_opt ==
		    DNS_OPT_IB_SUBSCRIBER_CATEGORY1 && count != 0) {
			ns_client_log(client, NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
				      "found IB subscriber category and "
				      "other option");
			return (ISC_R_UNEXPECTED);
		}

		/* Likewise, for responses over TCP or with TSIG variants
		 * shouldn't contain these options.  This should be ensured
		 * in the query handling logic. */
		if (TCP_CLIENT(client) || client->signer != NULL) {
			ns_client_log(client, NS_LOGCATEGORY_CLIENT,
				      NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
				      "found IB subscriber category for "
				      "wrong response over %s",
				      TCP_CLIENT(client) ? "TCP" : "UDP");
			return (ISC_R_UNEXPECTED);
		}

		/* convert the matched category in net byte order */
		ib_categories_t mcategories =
			client->subscriber->matched_categories;
		isc_buffer_init(&buf, catbuf, sizeof(catbuf));
		for (i = sizeof(catbuf) - 1; i >= 0; i--) {
			isc_buffer_putuint8(&buf,
					    (mcategories >> (i * 8)) & 0xff);
		}
		ednsopts[count].code = client->subscriber->edns_category_opt;
		ednsopts[count].length = sizeof(catbuf);
		ednsopts[count].value = catbuf;
        count++;
	}
#endif

	/* Padding must be added last */
#ifdef ORIGINAL_ISC_CODE
	if ((view != NULL) && (view->padding > 0) && WANTPAD(client) &&
	    (TCP_CLIENT(client) ||
	     ((client->attributes & NS_CLIENTATTR_HAVECOOKIE) != 0)))
#else
	if ((view != NULL) && (view->padding > 0) && WANTPAD(client))
#endif
	{
		isc_netaddr_t netaddr;
		int match;

		isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);
		result = dns_acl_match(&netaddr, NULL,
				       view->pad_acl,
				       &ns_g_server->aclenv,
				       &match, NULL);
		if (result == ISC_R_SUCCESS && match > 0) {
			INSIST(count < DNS_EDNSOPTIONS);

			ednsopts[count].code = DNS_OPT_PAD;
			ednsopts[count].length = 0;
			ednsopts[count].value = NULL;
			count++;

			dns_message_setpadding(message, view->padding);
		}
	}

	result = dns_message_buildopt(message, opt, 0, udpsize, flags,
				      ednsopts, count);
	return (result);
}

#ifdef ORIGINAL_ISC_CODE
#else
static isc_boolean_t
special_loopback_address (isc_netaddr_t *addr)
{
  if (addr == NULL || addr->family != AF_INET)
    return (ISC_FALSE);

  // Address should be of the form 127.x.y.z, where y >= 1.
  if ((htonl (addr->type.in.s_addr) >> IN_CLASSA_NSHIFT) != IN_LOOPBACKNET)
    return (ISC_FALSE);
  if (((htonl (addr->type.in.s_addr) >> IN_CLASSC_NSHIFT) & 0xff) < 1)
    return (ISC_FALSE);

  return (ISC_TRUE);
}

static isc_boolean_t
loopback_address_matches_view (isc_netaddr_t *addr, dns_view_t *view)
{
  if (view == NULL || view->name == NULL || special_loopback_address (addr) == ISC_FALSE)
    return (ISC_FALSE);

  if (compatible_view_and_source_address (view->name, addr->type.in.s_addr))
    {
      return (ISC_TRUE);
    }
  else
    {
      return (ISC_FALSE);
    }
}
#endif

static inline isc_boolean_t
#ifdef ORIGINAL_ISC_CODE
allowed(isc_netaddr_t *addr, dns_name_t *signer,
	dns_ecs_t *ecs, dns_acl_t *acl)
#else
allowed(isc_netaddr_t *addr, dns_name_t *signer,
	dns_ecs_t *ecs, dns_acl_t *acl,
	dns_view_t *view, ns_client_t *client)
#endif
{
	int match;
	isc_result_t result;

#ifdef ORIGINAL_ISC_CODE
#else
	if (view != NULL && client != NULL && special_loopback_address (addr)) {
		if (loopback_address_matches_view(addr, view)) {
			char addrbuf[sizeof("***************")];
			isc_log_write(ns_g_lctx, NS_LOGCATEGORY_CLIENT, NS_LOGMODULE_CLIENT, ISC_LOG_INFO,
				      "Allow access since the client is from special source address \"%s\" "
				      "that matches view \"%s\" ",
				      inet_ntop(AF_INET, (void *)&addr->type.in, addrbuf, sizeof(addrbuf)),
				      view->name);
			return (ISC_TRUE);
		} else {
			return (ISC_FALSE);
		}
	}
#endif

	if (acl == NULL)
		return (ISC_TRUE);
	result = dns_acl_matchx(addr, signer, ecs, acl,
				&ns_g_server->aclenv, &match, NULL);
	if (result == ISC_R_SUCCESS && match > 0)
		return (ISC_TRUE);
	return (ISC_FALSE);
}

#ifdef ORIGINAL_ISC_CODE
#else
isc_boolean_t
infoblox_authenticated_via_gss_tsig (ns_client_t *client)
{
  isc_boolean_t via_gss_tsig = ISC_FALSE;

  if (client && client->message && client->message->verified_sig)
    {
      if (client->message->tsigkey)
	{
	  if (dns_name_equal(client->message->tsigkey->algorithm, DNS_TSIG_GSSAPI_NAME) ||
	      dns_name_equal(client->message->tsigkey->algorithm, DNS_TSIG_GSSAPIMS_NAME))
            {
	      via_gss_tsig = ISC_TRUE;
            }
	}
    }

  return (via_gss_tsig);
}
#endif

/*
 * Callback to see if a non-recursive query coming from 'srcaddr' to
 * 'destaddr', with optional key 'mykey' for class 'rdclass' would be
 * delivered to 'myview'.
 *
 * We run this unlocked as both the view list and the interface list
 * are updated when the appropriate task has exclusivity.
 */
isc_boolean_t
ns_client_isself(dns_view_t *myview, dns_tsigkey_t *mykey,
		 isc_sockaddr_t *srcaddr, isc_sockaddr_t *dstaddr,
		 dns_rdataclass_t rdclass, void *arg)
{
	dns_view_t *view;
	dns_tsigkey_t *key = NULL;
	dns_name_t *tsig = NULL;
	isc_netaddr_t netsrc;
	isc_netaddr_t netdst;

	UNUSED(arg);

	/*
	 * ns_g_server->interfacemgr is task exclusive locked.
	 */
	if (ns_g_server->interfacemgr == NULL)
		return (ISC_TRUE);

	if (!ns_interfacemgr_listeningon(ns_g_server->interfacemgr, dstaddr))
		return (ISC_FALSE);

	isc_netaddr_fromsockaddr(&netsrc, srcaddr);
	isc_netaddr_fromsockaddr(&netdst, dstaddr);

	for (view = ISC_LIST_HEAD(ns_g_server->viewlist);
	     view != NULL;
	     view = ISC_LIST_NEXT(view, link)) {

		if (view->matchrecursiveonly)
			continue;

		if (rdclass != view->rdclass)
			continue;

		if (mykey != NULL) {
			isc_boolean_t match;
			isc_result_t result;

			result = dns_view_gettsig(view, &mykey->name, &key);
			if (result != ISC_R_SUCCESS)
				continue;
			match = dst_key_compare(mykey->key, key->key);
			dns_tsigkey_detach(&key);
			if (!match)
				continue;
			tsig = dns_tsigkey_identity(mykey);
		}

#ifdef ORIGINAL_ISC_CODE
		if (allowed(&netsrc, tsig, NULL, view->matchclients) &&
		    allowed(&netdst, tsig, NULL, view->matchdestinations))
			break;
#else
		if (allowed(&netsrc, tsig, NULL, view->matchclients,
			    NULL, NULL) &&
		    allowed(&netdst, tsig, NULL, view->matchdestinations,
			    NULL, NULL))
			break;
#endif
	}
	return (ISC_TF(view == myview));
}

static void
compute_cookie(ns_client_t *client, isc_uint32_t when, isc_uint32_t nonce,
	       isc_buffer_t *buf)
{
	switch (ns_g_server->cookiealg) {
#if defined(HAVE_OPENSSL_AES) || defined(HAVE_OPENSSL_EVP_AES)
	case ns_cookiealg_aes: {
		unsigned char digest[ISC_AES_BLOCK_LENGTH];
		unsigned char input[4 + 4 + 16];
		isc_netaddr_t netaddr;
		unsigned char *cp;
		unsigned int i;

		memset(input, 0, sizeof(input));
		cp = isc_buffer_used(buf);
		isc_buffer_putmem(buf, client->cookie, 8);
		isc_buffer_putuint32(buf, nonce);
		isc_buffer_putuint32(buf, when);
		memmove(input, cp, 16);
		isc_aes128_crypt(ns_g_server->secret, input, digest);
		for (i = 0; i < 8; i++)
			input[i] = digest[i] ^ digest[i + 8];
		isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);
		switch (netaddr.family) {
		case AF_INET:
			cp = (unsigned char *)&netaddr.type.in;
			memmove(input + 8, cp, 4);
			memset(input + 12, 0, 4);
			isc_aes128_crypt(ns_g_server->secret, input, digest);
			break;
		case AF_INET6:
			cp = (unsigned char *)&netaddr.type.in6;
			memmove(input + 8, cp, 16);
			isc_aes128_crypt(ns_g_server->secret, input, digest);
			for (i = 0; i < 8; i++)
				input[i + 8] = digest[i] ^ digest[i + 8];
			isc_aes128_crypt(ns_g_server->secret, input + 8,
					 digest);
			break;
		}
		for (i = 0; i < 8; i++)
			digest[i] ^= digest[i + 8];
		isc_buffer_putmem(buf, digest, 8);
		break;
	}
#endif

	case ns_cookiealg_sha1: {
		unsigned char digest[ISC_SHA1_DIGESTLENGTH];
		isc_netaddr_t netaddr;
		unsigned char *cp;
		isc_hmacsha1_t hmacsha1;
		unsigned int length;

		cp = isc_buffer_used(buf);
		isc_buffer_putmem(buf, client->cookie, 8);
		isc_buffer_putuint32(buf, nonce);
		isc_buffer_putuint32(buf, when);

		isc_hmacsha1_init(&hmacsha1,
				  ns_g_server->secret,
				  ISC_SHA1_DIGESTLENGTH);
		isc_hmacsha1_update(&hmacsha1, cp, 16);
		isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);
		switch (netaddr.family) {
		case AF_INET:
			cp = (unsigned char *)&netaddr.type.in;
			length = 4;
			break;
		case AF_INET6:
			cp = (unsigned char *)&netaddr.type.in6;
			length = 16;
			break;
		default:
			INSIST(0);
		}
		isc_hmacsha1_update(&hmacsha1, cp, length);
		isc_hmacsha1_update(&hmacsha1, client->cookie,
				    sizeof(client->cookie));
		isc_hmacsha1_sign(&hmacsha1, digest, sizeof(digest));
		isc_buffer_putmem(buf, digest, 8);
		isc_hmacsha1_invalidate(&hmacsha1);
		break;
	}

	case ns_cookiealg_sha256: {
		unsigned char digest[ISC_SHA256_DIGESTLENGTH];
		isc_netaddr_t netaddr;
		unsigned char *cp;
		isc_hmacsha256_t hmacsha256;
		unsigned int length;

		cp = isc_buffer_used(buf);
		isc_buffer_putmem(buf, client->cookie, 8);
		isc_buffer_putuint32(buf, nonce);
		isc_buffer_putuint32(buf, when);

		isc_hmacsha256_init(&hmacsha256,
				    ns_g_server->secret,
				    ISC_SHA256_DIGESTLENGTH);
		isc_hmacsha256_update(&hmacsha256, cp, 16);
		isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);
		switch (netaddr.family) {
		case AF_INET:
			cp = (unsigned char *)&netaddr.type.in;
			length = 4;
			break;
		case AF_INET6:
			cp = (unsigned char *)&netaddr.type.in6;
			length = 16;
			break;
		default:
			INSIST(0);
		}
		isc_hmacsha256_update(&hmacsha256, cp, length);
		isc_hmacsha256_update(&hmacsha256, client->cookie,
				      sizeof(client->cookie));
		isc_hmacsha256_sign(&hmacsha256, digest, sizeof(digest));
		isc_buffer_putmem(buf, digest, 8);
		isc_hmacsha256_invalidate(&hmacsha256);
		break;
	}
	default:
		INSIST(0);
	}
}

static void
process_cookie(ns_client_t *client, isc_buffer_t *buf, size_t optlen) {
	unsigned char dbuf[COOKIE_SIZE];
	unsigned char *old;
	isc_stdtime_t now;
	isc_uint32_t when;
	isc_uint32_t nonce;
	isc_buffer_t db;

	/*
	 * If we have already seen a cookie option skip this cookie option.
	 */
	if ((client->attributes & NS_CLIENTATTR_WANTCOOKIE) != 0) {
		isc_buffer_forward(buf, (unsigned int)optlen);
		return;
	}

	client->attributes |= NS_CLIENTATTR_WANTCOOKIE;

	isc_stats_increment(ns_g_server->nsstats, dns_nsstatscounter_cookiein);

	if (optlen != COOKIE_SIZE) {
		/*
		 * Not our token.
		 */
		INSIST(optlen >= 8U);
		memmove(client->cookie, isc_buffer_current(buf), 8);
		isc_buffer_forward(buf, (unsigned int)optlen);

		if (optlen == 8U)
			isc_stats_increment(ns_g_server->nsstats,
					    dns_nsstatscounter_cookienew);
		else
			isc_stats_increment(ns_g_server->nsstats,
					    dns_nsstatscounter_cookiebadsize);
		return;
	}

	/*
	 * Process all of the incoming buffer.
	 */
	old = isc_buffer_current(buf);
	memmove(client->cookie, old, 8);
	isc_buffer_forward(buf, 8);
	nonce = isc_buffer_getuint32(buf);
	when = isc_buffer_getuint32(buf);
	isc_buffer_forward(buf, 8);

	/*
	 * Allow for a 5 minute clock skew between servers sharing a secret.
	 * Only accept COOKIE if we have talked to the client in the last hour.
	 */
	isc_stdtime_get(&now);
	if (isc_serial_gt(when, (now + 300)) ||		/* In the future. */
	    isc_serial_lt(when, (now - 3600))) {	/* In the past. */
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_cookiebadtime);
		return;
	}

	isc_buffer_init(&db, dbuf, sizeof(dbuf));
	compute_cookie(client, when, nonce, &db);

	if (!isc_safe_memequal(old, dbuf, COOKIE_SIZE)) {
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_cookienomatch);
		return;
	}

	isc_stats_increment(ns_g_server->nsstats,
			    dns_nsstatscounter_cookiematch);
	client->attributes |= NS_CLIENTATTR_HAVECOOKIE;
}

static isc_result_t
process_ecs(ns_client_t *client, isc_buffer_t *buf, size_t optlen) {
	isc_uint16_t family;
	unsigned int addrbytes;
	isc_uint8_t addrlen, scope, *paddr;
	isc_netaddr_t caddr;

#ifdef ORIGINAL_ISC_CODE
	/*
	 * If we have already seen a ECS option skip this ECS option.
	 */
	if (ECS_RECEIVED(client)) {
		isc_buffer_forward(buf, optlen);
		return (ISC_R_SUCCESS);
	}
#else
	/* The REF doesn't mention how to handle multiple edns-client-subnet
	 * options in the same query. Here we choose rejecting such query
	 * with DNS_R_FORMERR.
	 */
	if (ECS_RECEIVED(client)) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(2),
			      "Received multiple EDNS client subnet options");
		return (DNS_R_FORMERR);
	}
#endif

	family = isc_buffer_getuint16(buf);
	addrlen = isc_buffer_getuint8(buf);
	scope = isc_buffer_getuint8(buf);
	optlen -= 4;

	/*
	 * Many of the ECS field values are already validated when
	 * parsing the client message. We only check SCOPE PREFIX-LENGTH
	 * here.
	 */
	if (scope != 0U) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(2),
			      "EDNS client-subnet option: invalid scope");
		return (DNS_R_OPTERR);
	}

	memset(&caddr, 0, sizeof(caddr));
	switch (family) {
	case 0:
		/*
		 * In queries, if FAMILY is set to 0, then SOURCE
		 * PREFIX-LENGTH must also be 0 and ADDRESS should not be
		 * present, as the address and prefix lengths don't make
		 * sense because the family is unknown.
		 */
		caddr.family = AF_UNSPEC;
		break;
	case 1:
		caddr.family = AF_INET;
		break;
	case 2:
		caddr.family = AF_INET6;
		break;
	default:
		INSIST(0);
	}

	addrbytes = (addrlen + 7) / 8;
	paddr = (isc_uint8_t *) &caddr.type;
	if (addrbytes != 0U) {
		memmove(paddr, isc_buffer_current(buf), addrbytes);
		isc_buffer_forward(buf, addrbytes);
		optlen -= addrbytes;

		if ((addrlen % 8) != 0) {
			isc_uint8_t bits = ~0U << (8 - (addrlen % 8));
			bits &= paddr[addrbytes - 1];
			if (bits != paddr[addrbytes - 1])
				return (DNS_R_OPTERR);
		}
	}

	isc_buffer_forward(buf, (unsigned int) optlen);

	memmove(&client->ecs.addr, &caddr, sizeof(caddr));
	client->ecs.source = addrlen;
	/*
	 * Start with scope set to 0xff that indicates that it is
	 * unset. It must be set to something else before a reply is
	 * returned to the client.
	 */
	client->ecs.scope = 0xff;

	client->attributes |= NS_CLIENTATTR_ECSRECEIVED;

#ifdef ORIGINAL_ISC_CODE
#else
	client->ecs_opt = isc_mem_get(client->mctx,
				      sizeof(ib_edns_client_subnet_t));
	if (client->ecs_opt == NULL) {
		isc_log_write(ns_g_lctx, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
			      "Failed to allocate buffer to"
			      " EDNS client subnet option");
		return (ISC_R_NOMEMORY);
	}
	client->ecs_opt->enabled = ISC_FALSE;
	client->ecs_opt->family = family;
	client->ecs_opt->source_prefix_length = addrlen;
	client->ecs_opt->addrbytes = addrbytes;
	client->ecs_opt->scope_prefix_length = scope;
	client->ecs_opt->address = &client->ecs.addr.type;
	isc_stats_increment(ns_g_server->nsstats,
			    dns_nsstatscounter_ednsclientsubnet);
#endif

	return (ISC_R_SUCCESS);
}

#ifdef ORIGINAL_ISC_CODE
#else
static isc_result_t
process_ib_client_id(ns_client_t *client, isc_buffer_t *buf, size_t optlen) {
	/* For now, we ignore any client ID option except the first one.
	 * Note that draft-tale-dnsop-edns0-clientid-01 allows including
	 * multiple client ID options with different types, so we may have to
	 * loosen it in future (which may be moot anyway as long as we keep
	 * only supporting the Nominum-variant. */
	if (client->ib_client_id != NULL) {
		isc_buffer_forward(buf, optlen);
		return (ISC_R_SUCCESS);
	}

	/*
	 * On parsing the wire-format option we confirmed the option at leat
	 * has 2-octet type field.  'named' is not a direct user of the option
	 * and is agnostic about further validity.  For example, it doesn't
	 * care even if the type suggests the ID is an IPv6 address but the
	 * length is not 128 bits (again, it's even more moot with
	 * Nominum-variant).
	 */
	client->ib_client_id = isc_mem_get(client->mctx,
					   sizeof(*client->ib_client_id) +
					   optlen);
	if (client->ib_client_id == NULL)
		return (ISC_R_NOMEMORY);
	if (optlen > 0) {
		memmove(client->ib_client_id + 1, isc_buffer_current(buf),
			optlen);
		isc_buffer_forward(buf, optlen);
	}
	/* We use the EDNS option code as 'idtype' (arbitrary choice) */
	client->ib_client_id->idtype = DNS_OPT_NOMINUM_CLIENT_IDENTIFIER;
	client->ib_client_id->idlen = optlen;

	return (ISC_R_SUCCESS);
}

static isc_result_t
process_outgoing_cview(ns_client_t *client, isc_buffer_t *buf,
		       size_t optlen, infoblox_addropts_t *addropts)
{
	if (optlen == 0 || optlen > UINT8_MAX) {
		return (DNS_R_FORMERR);
	}

	/* rejecting the query with DNS_R_FORMERR, if the query
	 * contains multiple client outgoing views options.
	 */
	if (addropts->view_name != NULL) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(2),
			      "Received multiple EDNS client outgoing "
			      "view options");
		return (DNS_R_FORMERR);
	}

	addropts->view_name_len = optlen;
	addropts->view_name = isc_mem_get(client->mctx,
					  addropts->view_name_len);
	if (addropts->view_name == NULL) {
		isc_log_write(ns_g_lctx, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
			      "Failed to allocate buffer to "
			      "EDNS client outgoing view option");
		return (ISC_R_NOMEMORY);
	}

	memmove(addropts->view_name, isc_buffer_current(buf),
		addropts->view_name_len);
	isc_buffer_forward(buf, (unsigned int)optlen);
	return (ISC_R_SUCCESS);
}

#endif

static isc_result_t
process_keytag(ns_client_t *client, isc_buffer_t *buf, size_t optlen) {
	if (optlen == 0 || (optlen % 2) != 0) {
		isc_buffer_forward(buf, (unsigned int)optlen);
		return (DNS_R_OPTERR);
	}

	/* Silently drop additional keytag options. */
	if (client->keytag != NULL) {
		isc_buffer_forward(buf, (unsigned int)optlen);
		return (ISC_R_SUCCESS);
	}

	client->keytag = isc_mem_get(client->mctx, optlen);
	if (client->keytag != NULL) {
		client->keytag_len = (isc_uint16_t)optlen;
		memmove(client->keytag, isc_buffer_current(buf), optlen);
	}
	isc_buffer_forward(buf, (unsigned int)optlen);
	return (ISC_R_SUCCESS);
}

static isc_result_t
process_opt(ns_client_t *client, dns_rdataset_t *opt) {
	dns_rdata_t rdata;
	isc_buffer_t optbuf;
	isc_result_t result;
	isc_uint16_t optcode;
	isc_uint16_t optlen;

	/*
	 * Set the client's UDP buffer size.
	 */
	client->udpsize = opt->rdclass;

	/*
	 * If the requested UDP buffer size is less than 512,
	 * ignore it and use 512.
	 */
	if (client->udpsize < 512)
		client->udpsize = 512;

	/*
	 * Get the flags out of the OPT record.
	 */
	client->extflags = (isc_uint16_t)(opt->ttl & 0xFFFF);

	/*
	 * Do we understand this version of EDNS?
	 *
	 * XXXRTH need library support for this!
	 */
	client->ednsversion = (opt->ttl & 0x00FF0000) >> 16;
	if (client->ednsversion > DNS_EDNS_VERSION) {
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_badednsver);
		result = ns_client_addopt(client, client->message,
					  &client->opt);
		if (result == ISC_R_SUCCESS)
			result = DNS_R_BADVERS;
		ns_client_error(client, result);
		goto cleanup;
	}

	result = dns_rdataset_first(opt);
	if (result == ISC_R_SUCCESS) {
		dns_rdata_init(&rdata);
		dns_rdataset_current(opt, &rdata);
		isc_buffer_init(&optbuf, rdata.data, rdata.length);
		isc_buffer_add(&optbuf, rdata.length);
#ifdef ORIGINAL_ISC_CODE
#else
		infoblox_addropts_t *addropts = &client->infoblox_addropts;
#endif
		while (isc_buffer_remaininglength(&optbuf) >= 4) {
			optcode = isc_buffer_getuint16(&optbuf);
			optlen = isc_buffer_getuint16(&optbuf);
			switch (optcode) {
			case DNS_OPT_NSID:
				if (!WANTNSID(client))
					isc_stats_increment(
						    ns_g_server->nsstats,
						    dns_nsstatscounter_nsidopt);
				client->attributes |= NS_CLIENTATTR_WANTNSID;
				isc_buffer_forward(&optbuf, optlen);
				break;
			case DNS_OPT_COOKIE:
				process_cookie(client, &optbuf, optlen);
				break;
			case DNS_OPT_EXPIRE:
				if (!WANTEXPIRE(client))
					isc_stats_increment(
						  ns_g_server->nsstats,
						  dns_nsstatscounter_expireopt);
				client->attributes |= NS_CLIENTATTR_WANTEXPIRE;
				isc_buffer_forward(&optbuf, optlen);
				break;
			case DNS_OPT_CLIENT_SUBNET:
				result = process_ecs(client, &optbuf, optlen);
				if (result != ISC_R_SUCCESS) {
					ns_client_error(client, result);
					goto cleanup;
				}

				isc_stats_increment(ns_g_server->nsstats,
						  dns_nsstatscounter_ecsopt);
				break;
#ifdef ORIGINAL_ISC_CODE
#else
			case DNS_OPT_NOMINUM_CLIENT_IDENTIFIER:
				result = process_ib_client_id(client, &optbuf,
							      optlen);
				if (result != ISC_R_SUCCESS) {
					ns_client_error(client, result);
					goto cleanup;
				}
				break;
			case DNS_OPT_OG_CADDR:
				if (optlen != sizeof(struct in6_addr) &&
				    optlen != sizeof(struct in_addr)) {
					result = DNS_R_FORMERR;
					ns_client_error(client, result);
					goto cleanup;
				}
				addropts->ip_addr_len = optlen;
				memcpy(addropts->ip_addr,
				       isc_buffer_current(&optbuf),
				       addropts->ip_addr_len);
				isc_buffer_forward(&optbuf, optlen);
				break;
			case DNS_OPT_OG_CMACADDR:
				if (optlen != sizeof(addropts->mac_addr)) {
					result = DNS_R_FORMERR;
					ns_client_error(client, result);
					goto cleanup;
				}
				addropts->mac_addr_len = optlen;
				memcpy(addropts->mac_addr,
				       isc_buffer_current(&optbuf),
				       addropts->mac_addr_len);
				isc_buffer_forward(&optbuf, optlen);
				break;
			case DNS_OPT_OG_CVIEW:
				result = process_outgoing_cview(client,
								&optbuf,
								optlen,
								addropts);
				if (result != ISC_R_SUCCESS) {
					ns_client_error(client, result);
					goto cleanup;
				}
				break;
			case DNS_OPT_IB_DESTINATION_ADDRESS:
				if (optlen == sizeof(struct in_addr)) {
					struct in_addr dest_addr4;
					memmove(&dest_addr4.s_addr, isc_buffer_current(&optbuf), optlen);
					isc_netaddr_fromin(&client->ib_edns0_dest_addr, &dest_addr4);
					client->attributes |= NS_CLIENTATTR_WANTDESTADDR;
				} else {
					struct in6_addr dest_addr6;
					memmove(&dest_addr6.s6_addr32, isc_buffer_current(&optbuf), optlen);
					isc_netaddr_fromin6(&client->ib_edns0_dest_addr, &dest_addr6);
					client->attributes |= NS_CLIENTATTR_WANTDESTADDR;
				}
				isc_buffer_forward(&optbuf, optlen);
				break;

#endif
			case DNS_OPT_KEY_TAG:
				result = process_keytag(client, &optbuf,
							optlen);
				if (result != ISC_R_SUCCESS) {
					ns_client_error(client, result);
					return (result);
				}
				isc_stats_increment(ns_g_server->nsstats,
						 dns_nsstatscounter_keytagopt);
				break;
			case DNS_OPT_TCP_KEEPALIVE:
				if (!USEKEEPALIVE(client))
					isc_stats_increment(
					       ns_g_server->nsstats,
					       dns_nsstatscounter_keepaliveopt);
				client->attributes |=
					NS_CLIENTATTR_USEKEEPALIVE;
				isc_buffer_forward(&optbuf, optlen);
				break;
			case DNS_OPT_PAD:
				client->attributes |= NS_CLIENTATTR_WANTPAD;
				isc_stats_increment(ns_g_server->nsstats,
						  dns_nsstatscounter_padopt);
				isc_buffer_forward(&optbuf, optlen);
				break;
			default:
				isc_stats_increment(ns_g_server->nsstats,
						  dns_nsstatscounter_otheropt);
				isc_buffer_forward(&optbuf, optlen);
				break;
			}
		}
	}

	isc_stats_increment(ns_g_server->nsstats, dns_nsstatscounter_edns0in);
	client->attributes |= NS_CLIENTATTR_WANTOPT;

 cleanup:
	return (result);
}

/*
 * Handle an incoming request event from the socket (UDP case)
 * or tcpmsg (TCP case).
 */
static void
client_request(isc_task_t *task, isc_event_t *event) {
	ns_client_t *client;
	isc_socketevent_t *sevent;
	isc_result_t result;
	isc_result_t sigresult = ISC_R_SUCCESS;
	isc_buffer_t *buffer;
	isc_buffer_t tbuffer;
	dns_view_t *view;
	dns_rdataset_t *opt;
	dns_name_t *signame;
	isc_boolean_t ra;	/* Recursion available. */
	isc_netaddr_t netaddr;
	int match;
	dns_messageid_t id;
	unsigned int flags;
	isc_boolean_t notimp;
	size_t reqsize;
#ifdef HAVE_DNSTAP
	dns_dtmsgtype_t dtmsgtype;
#endif

	REQUIRE(event != NULL);
	client = event->ev_arg;
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(task == client->task);

	INSIST(client->recursionquota == NULL);

	INSIST(client->state == (TCP_CLIENT(client) ?
				       NS_CLIENTSTATE_READING :
				       NS_CLIENTSTATE_READY));

#ifdef ORIGINAL_ISC_CODE
#else
	// Clear the flag before processing incoming request to avoid
	// using invalid buffer in infoblox_format_query_info.
	client->query.infoblox_have_formatted_qname = ISC_FALSE;
#endif
	ns_client_requests++;

	if (event->ev_type == ISC_SOCKEVENT_RECVDONE) {
		INSIST(!TCP_CLIENT(client));
		sevent = (isc_socketevent_t *)event;
		REQUIRE(sevent == client->recvevent);
		isc_buffer_init(&tbuffer, sevent->region.base, sevent->n);
		isc_buffer_add(&tbuffer, sevent->n);
		buffer = &tbuffer;
		result = sevent->result;
		if (result == ISC_R_SUCCESS) {
			client->peeraddr = sevent->address;
			client->peeraddr_valid = ISC_TRUE;
		}
		if ((sevent->attributes & ISC_SOCKEVENTATTR_DSCP) != 0) {
			ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(90),
			      "received DSCP %d", sevent->dscp);
			if (client->dscp == -1)
				client->dscp = sevent->dscp;
		}
		if ((sevent->attributes & ISC_SOCKEVENTATTR_PKTINFO) != 0) {
			client->attributes |= NS_CLIENTATTR_PKTINFO;
			client->pktinfo = sevent->pktinfo;
		}
		if ((sevent->attributes & ISC_SOCKEVENTATTR_MULTICAST) != 0)
			client->attributes |= NS_CLIENTATTR_MULTICAST;
		client->nrecvs--;
	} else {
		INSIST(TCP_CLIENT(client));
		INSIST(client->tcpconn != NULL);
		REQUIRE(event->ev_type == DNS_EVENT_TCPMSG);
		REQUIRE(event->ev_sender == &client->tcpmsg);
		buffer = &client->tcpmsg.buffer;
		result = client->tcpmsg.result;
		INSIST(client->nreads == 1);
		/*
		 * client->peeraddr was set when the connection was accepted.
		 */
		client->nreads--;
	}

	reqsize = client->requestsize = isc_buffer_usedlength(buffer);

	/*
	 * Don't count the length header for stats purposes, but
	 * do for RRL classification.
	 */
	if (TCP_CLIENT(client)) {
		reqsize -= 2;
	}

	if (exit_check(client))
		goto cleanup;
	client->state = client->newstate = NS_CLIENTSTATE_WORKING;

	isc_task_getcurrenttimex(task, &client->requesttime);
	client->tnow = client->requesttime;
	client->now = isc_time_seconds(&client->tnow);

	if (result != ISC_R_SUCCESS) {
		if (TCP_CLIENT(client)) {
			ns_client_next(client, result);
		} else {
			if  (result != ISC_R_CANCELED)
				isc_log_write(ns_g_lctx, NS_LOGCATEGORY_CLIENT,
					      NS_LOGMODULE_CLIENT,
					      ISC_LOG_ERROR,
					      "UDP client handler shutting "
					      "down due to fatal receive "
					      "error: %s",
					      isc_result_totext(result));
			isc_task_shutdown(client->task);
		}
		goto cleanup;
	}

	isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);

#if NS_CLIENT_DROPPORT
	if (ns_client_dropport(isc_sockaddr_getport(&client->peeraddr)) ==
	    DROPPORT_REQUEST) {
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(10),
			      "dropped request: suspicious port");
		ns_client_next(client, ISC_R_SUCCESS);
		goto cleanup;
	}
#endif

	ns_client_log(client, NS_LOGCATEGORY_CLIENT,
		      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(3),
		      "%s request",
		      TCP_CLIENT(client) ? "TCP" : "UDP");

	/*
	 * Check the blackhole ACL for UDP only, since TCP is done in
	 * client_newconn.
	 */
	if (!TCP_CLIENT(client)) {
		if (ns_g_server->blackholeacl != NULL &&
		    dns_acl_match(&netaddr, NULL, ns_g_server->blackholeacl,
				  &ns_g_server->aclenv,
				  &match, NULL) == ISC_R_SUCCESS &&
		    match > 0)
		{
			ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
				      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(10),
				      "blackholed UDP datagram");
			ns_client_next(client, ISC_R_SUCCESS);
			goto cleanup;
		}
	}

	/*
	 * Silently drop multicast requests for the present.
	 * XXXMPA revisit this as mDNS spec was published.
	 */
	if ((client->attributes & NS_CLIENTATTR_MULTICAST) != 0) {
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(2),
			      "dropping multicast request");
		ns_client_next(client, DNS_R_REFUSED);
		goto cleanup;
	}

	result = dns_message_peekheader(buffer, &id, &flags);
	if (result != ISC_R_SUCCESS) {
		/*
		 * There isn't enough header to determine whether
		 * this was a request or a response.  Drop it.
		 */
		ns_client_next(client, result);
		goto cleanup;
	}

	/*
	 * The client object handles requests, not responses.
	 * If this is a UDP response, forward it to the dispatcher.
	 * If it's a TCP response, discard it here.
	 */
	if ((flags & DNS_MESSAGEFLAG_QR) != 0) {
		if (TCP_CLIENT(client)) {
			CTRACE("unexpected response");
			ns_client_next(client, DNS_R_FORMERR);
			goto cleanup;
		} else {
			dns_dispatch_importrecv(client->dispatch, event);
			ns_client_next(client, ISC_R_SUCCESS);
			goto cleanup;
		}
	}

	/*
	 * Update some statistics counters.  Don't count responses.
	 */
	if (isc_sockaddr_pf(&client->peeraddr) == PF_INET) {
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_requestv4);
	} else {
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_requestv6);
	}
	if (TCP_CLIENT(client)) {
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_requesttcp);
		switch (isc_sockaddr_pf(&client->peeraddr)) {
		case AF_INET:
			isc_stats_increment(ns_g_server->tcpinstats4,
					    ISC_MIN((int)reqsize / 16, 18));
			break;
		case AF_INET6:
			isc_stats_increment(ns_g_server->tcpinstats6,
					    ISC_MIN((int)reqsize / 16, 18));
			break;
		default:
			INSIST(0);
			break;
		}
	} else {
		switch (isc_sockaddr_pf(&client->peeraddr)) {
		case AF_INET:
			isc_stats_increment(ns_g_server->udpinstats4,
					    ISC_MIN((int)reqsize / 16, 18));
			break;
		case AF_INET6:
			isc_stats_increment(ns_g_server->udpinstats6,
					    ISC_MIN((int)reqsize / 16, 18));
			break;
		default:
			INSIST(0);
			break;
		}
	}

	/*
	 * It's a request.  Parse it.
	 */
	result = dns_message_parse(client->message, buffer, 0);
	if (result != ISC_R_SUCCESS) {
		/*
		 * Parsing the request failed.  Send a response
		 * (typically FORMERR or SERVFAIL).
		 */
		if (result == DNS_R_OPTERR)
			(void)ns_client_addopt(client, client->message,
					       &client->opt);

		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(1),
			      "message parsing failed: %s",
			      isc_result_totext(result));
		ns_client_error(client, result);
		goto cleanup;
	}
#ifdef ORIGINAL_ISC_CODE
#else
        if (client->message->want_client_name) {
		ns_client_name(client, client->message->client_name,
			       sizeof(client->message->client_name));
        }
#endif

	/*
	 * Pipeline TCP query processing.
	 */
	if (TCP_CLIENT(client)) {
		if (client->message->opcode != dns_opcode_query) {
			client->tcpconn->pipelined = ISC_FALSE;
		}

		/*
		 * Limit the maximum number of simultaneous pipelined
		 * queries on TCP connection to TCP_CLIENTS_PER_CONN.
		 */
		if ((isc_refcount_current(&client->tcpconn->clients)
			    > TCP_CLIENTS_PER_CONN))
		{
			client->tcpconn->pipelined = ISC_FALSE;
		}

		if (client->tcpconn->pipelined) {
			/*
			 * We're pipelining. Replace the client; the
			 * replacement can read the TCP socket looking
			 * for new messages and this one can process the
			 * current message asynchronously.
			 *
			 * There will now be at least three clients using this
			 * TCP socket - one accepting new connections,
			 * one reading an existing connection to get new
			 * messages, and one answering the message already
			 * received.
			 */
			result = ns_client_replace(client);
			if (result != ISC_R_SUCCESS) {
				client->tcpconn->pipelined = ISC_FALSE;
			}
		}
	}

	dns_opcodestats_increment(ns_g_server->opcodestats,
				  client->message->opcode);
	switch (client->message->opcode) {
	case dns_opcode_query:
	case dns_opcode_update:
	case dns_opcode_notify:
		notimp = ISC_FALSE;
		break;
	case dns_opcode_iquery:
	default:
		notimp = ISC_TRUE;
		break;
	}

	client->message->rcode = dns_rcode_noerror;

	/* RFC1123 section ******* */
	if ((client->attributes & NS_CLIENTATTR_MULTICAST) != 0)
		client->message->flags &= ~DNS_MESSAGEFLAG_RD;

	/*
	 * Deal with EDNS.
	 */
#ifdef ORIGINAL_ISC_CODE
#else
	memset(&client->infoblox_addropts, 0, sizeof(client->infoblox_addropts));
#endif
	if (ns_g_noedns)
		opt = NULL;
	else
		opt = dns_message_getopt(client->message);

	if (opt != NULL) {
		/*
		 * Are we dropping all EDNS queries?
		 */
		if (ns_g_dropedns) {
			ns_client_next(client, ISC_R_SUCCESS);
			goto cleanup;
		}
		result = process_opt(client, opt);
		if (result != ISC_R_SUCCESS)
			goto cleanup;
	}

	if (client->message->rdclass == 0) {
		if ((client->attributes & NS_CLIENTATTR_WANTCOOKIE) != 0 ||
		    (client->message->opcode == dns_opcode_query &&
		     client->message->counts[DNS_SECTION_QUESTION] == 0U)) {
			result = dns_message_reply(client->message, ISC_TRUE);
			if (result != ISC_R_SUCCESS) {
				ns_client_error(client, result);
				return;
			}
			if (notimp)
				client->message->rcode = dns_rcode_notimp;
			ns_client_send(client);
			return;
		}
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(1),
			      "message class could not be determined");
		ns_client_dumpmessage(client,
				      "message class could not be determined");
		ns_client_error(client, notimp ? DNS_R_NOTIMP : DNS_R_FORMERR);
		goto cleanup;
	}

	/*
	 * Determine the destination address.  If the receiving interface is
	 * bound to a specific address, we simply use it regardless of the
	 * address family.  All IPv4 queries should fall into this case.
	 * Otherwise, if this is a TCP query, get the address from the
	 * receiving socket (this needs a system call and can be heavy).
	 * For IPv6 UDP queries, we get this from the pktinfo structure (if
	 * supported).
	 * If all the attempts fail (this can happen due to memory shortage,
	 * etc), we regard this as an error for safety.
	 */
	if ((client->interface->flags & NS_INTERFACEFLAG_ANYADDR) == 0)
		isc_netaddr_fromsockaddr(&client->destaddr,
					 &client->interface->addr);
	else {
		isc_sockaddr_t sockaddr;
		result = ISC_R_FAILURE;

		if (TCP_CLIENT(client))
			result = isc_socket_getsockname(client->tcpsocket,
							&sockaddr);
		if (result == ISC_R_SUCCESS)
			isc_netaddr_fromsockaddr(&client->destaddr, &sockaddr);
		if (result != ISC_R_SUCCESS &&
		    client->interface->addr.type.sa.sa_family == AF_INET6 &&
		    (client->attributes & NS_CLIENTATTR_PKTINFO) != 0) {
			/*
			 * XXXJT technically, we should convert the receiving
			 * interface ID to a proper scope zone ID.  However,
			 * due to the fact there is no standard API for this,
			 * we only handle link-local addresses and use the
			 * interface index as link ID.  Despite the assumption,
			 * it should cover most typical cases.
			 */
			isc_netaddr_fromin6(&client->destaddr,
					    &client->pktinfo.ipi6_addr);
			if (IN6_IS_ADDR_LINKLOCAL(&client->pktinfo.ipi6_addr))
				isc_netaddr_setzone(&client->destaddr,
						client->pktinfo.ipi6_ifindex);
			result = ISC_R_SUCCESS;
		}
		if (result != ISC_R_SUCCESS) {
			UNEXPECTED_ERROR(__FILE__, __LINE__,
					 "failed to get request's "
					 "destination: %s",
					 isc_result_totext(result));
			ns_client_next(client, ISC_R_SUCCESS);
			goto cleanup;
		}
	}

	isc_sockaddr_fromnetaddr(&client->destsockaddr, &client->destaddr, 0);

#ifdef ORIGINAL_ISC_CODE
#else
	client->is_infoblox_dhcpd = 0;

	/*
	 * If the client->destaddr is in the infoblox-process-edns0-destination-address
	 * ACL, use the address provided in the EDNS0 option to match
	 * destinations instead.
	 */
	dns_acl_t *acl;
	acl = ns_g_server->infoblox_process_edns0_destination_address_acl;
	/* If the ACL is empty, ignore the EDNS0 destination address option */
	if (acl != NULL && WANTDESTADDR(client)) {
		result = dns_acl_matchx(&client->destaddr, NULL, NULL, acl,
				&ns_g_server->aclenv, &match, NULL);
		if (result == ISC_R_SUCCESS && match > 0) {
			client->destaddr = client->ib_edns0_dest_addr;
		} else {
			ns_client_error(client, DNS_R_REFUSED);
			goto cleanup;
		}
	}
#endif

	/*
	 * Find a view that matches the client's source address.
	 */
	for (view = ISC_LIST_HEAD(ns_g_server->viewlist);
	     view != NULL;
	     view = ISC_LIST_NEXT(view, link)) {
		if (client->message->rdclass == view->rdclass ||
		    client->message->rdclass == dns_rdataclass_any)
		{
			dns_name_t *tsig = NULL;
			dns_ecs_t *ecs = NULL;

			sigresult = dns_message_rechecksig(client->message,
							   view);
			if (sigresult == ISC_R_SUCCESS) {
				dns_tsigkey_t *tsigkey;
				tsigkey = client->message->tsigkey;
				tsig = dns_tsigkey_identity(tsigkey);
			}
#ifdef ORIGINAL_ISC_CODE
#else
			else if (infoblox_authenticated_via_gss_tsig (client)) {
				isc_result_t gss_tsig_result;

				gss_tsig_result =
					infoblox_create_gss_tsig_key_name ();
				if (gss_tsig_result == ISC_R_SUCCESS) {
					tsig = &gss_tsig_signer;
					client->message->tsigstatus =
						dns_rcode_noerror;
					sigresult = ISC_R_SUCCESS;
				} else {
					ns_client_log(client,
						      DNS_LOGCATEGORY_SECURITY,
						      NS_LOGMODULE_CLIENT,
						      ISC_LOG_ERROR,
						      "Error creating GSS-TSIG signer name: %s",
						      isc_result_totext(gss_tsig_result));
					ns_client_dumpmessage(client,
							      "Error creating GSS-TSIG signer name");
					ns_client_error(client,
							gss_tsig_result);
					goto cleanup;
				}
			}
#endif

			if (ECS_RECEIVED(client))
				ecs = &client->ecs;

#ifdef ORIGINAL_ISC_CODE
			if (allowed(&netaddr, tsig, ecs, view->matchclients) &&
			    allowed(&client->destaddr, tsig, NULL,
				    view->matchdestinations) &&
#else
			if (allowed(&netaddr, tsig, ecs, view->matchclients,
					view, client) &&
				allowed(&client->destaddr, tsig, NULL,
					view->matchdestinations, view, client) &&
#endif
			    !(view->matchrecursiveonly &&
			    (client->message->flags & DNS_MESSAGEFLAG_RD) == 0))
			{
				dns_view_attach(view, &client->view);
				break;
			}
		}
	}

	if (view == NULL) {
		char classname[DNS_RDATACLASS_FORMATSIZE];

		/*
		 * Do a dummy TSIG verification attempt so that the
		 * response will have a TSIG if the query did, as
		 * required by RFC2845.
		 */
		isc_buffer_t b;
		isc_region_t *r;

		dns_message_resetsig(client->message);

		r = dns_message_getrawmessage(client->message);
		isc_buffer_init(&b, r->base, r->length);
		isc_buffer_add(&b, r->length);
		(void)dns_tsig_verify(&b, client->message, NULL, NULL);

		dns_rdataclass_format(client->message->rdclass, classname,
				      sizeof(classname));
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(1),
			      "no matching view in class '%s'", classname);
		ns_client_dumpmessage(client, "no matching view in class");
		ns_client_error(client, notimp ? DNS_R_NOTIMP : DNS_R_REFUSED);
		goto cleanup;
	}

	ns_client_log(client, NS_LOGCATEGORY_CLIENT,
		      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(5),
		      "using view '%s'", view->name);

	/*
	 * Check for a signature.  We log bad signatures regardless of
	 * whether they ultimately cause the request to be rejected or
	 * not.  We do not log the lack of a signature unless we are
	 * debugging.
	 */
	client->signer = NULL;
#ifdef ORIGINAL_ISC_CODE
#else
	if (client->ib_principal != NULL) {
		isc_mem_put(client->mctx, client->ib_principal, strlen(client->ib_principal) + 1);
		client->ib_principal = NULL;
        }
#endif
	dns_name_init(&client->signername, NULL);
#ifdef ORIGINAL_ISC_CODE
	result = dns_message_signer(client->message, &client->signername);
#else
	char *ib_principal = NULL;
	result = ib_dns_message_signer(client->message, &client->signername,
				       &ib_principal);
#endif
	if (result != ISC_R_NOTFOUND) {
		signame = NULL;
		if (dns_message_gettsig(client->message, &signame) != NULL) {
			isc_stats_increment(ns_g_server->nsstats,
					    dns_nsstatscounter_tsigin);
		} else {
			isc_stats_increment(ns_g_server->nsstats,
					    dns_nsstatscounter_sig0in);
		}

	}
	if (result == ISC_R_SUCCESS) {
		char namebuf[DNS_NAME_FORMATSIZE];
		dns_name_format(&client->signername, namebuf, sizeof(namebuf));
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(3),
			      "request has valid signature: %s", namebuf);
		client->signer = &client->signername;
#ifdef ORIGINAL_ISC_CODE
#else
		if (ib_principal) {
			size_t plen = strlen(ib_principal);
			client->ib_principal = isc_mem_get(client->mctx, plen + 1);
			if (client->ib_principal != NULL) {
				(void)memcpy(client->ib_principal, ib_principal, plen + 1);
			} else {
				ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
					      NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
					      "Error allocating memory for principal name");
				// Expect update_action() to throw an error
			}
		}
#endif
	} else if (result == ISC_R_NOTFOUND) {
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(3),
			      "request is not signed");
	} else if (result == DNS_R_NOIDENTITY) {
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(3),
			      "request is signed by a nonauthoritative key");
	} else if (result == DNS_R_NOTVERIFIEDYET &&
		   client->message->sig0 != NULL) {
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(3),
			      "request has a SIG(0) signature but its support "
			      "was removed (CVE-2024-1975)");
	} else {
		char tsigrcode[64];
		isc_buffer_t b;
		dns_rcode_t status;
		isc_result_t tresult;

		/* There is a signature, but it is bad. */
		isc_stats_increment(ns_g_server->nsstats,
				    dns_nsstatscounter_invalidsig);
		signame = NULL;
		if (dns_message_gettsig(client->message, &signame) != NULL) {
			char namebuf[DNS_NAME_FORMATSIZE];
			char cnamebuf[DNS_NAME_FORMATSIZE];
			dns_name_format(signame, namebuf, sizeof(namebuf));
			status = client->message->tsigstatus;
			isc_buffer_init(&b, tsigrcode, sizeof(tsigrcode) - 1);
			tresult = dns_tsigrcode_totext(status, &b);
			INSIST(tresult == ISC_R_SUCCESS);
			tsigrcode[isc_buffer_usedlength(&b)] = '\0';
#ifdef ORIGINAL_ISC_CODE
			if (client->message->tsigkey->generated) {
#else
			if (client->message->tsigkey != NULL &&
			    client->message->tsigkey->generated) {
#endif
				dns_name_format(client->message->tsigkey->creator,
						cnamebuf, sizeof(cnamebuf));
				ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
					      NS_LOGMODULE_CLIENT,
					      ISC_LOG_ERROR,
					      "request has invalid signature: "
					      "TSIG %s (%s): %s (%s)", namebuf,
					      cnamebuf,
					      isc_result_totext(result),
					      tsigrcode);
			} else {
				ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
					      NS_LOGMODULE_CLIENT,
					      ISC_LOG_ERROR,
					      "request has invalid signature: "
					      "TSIG %s: %s (%s)", namebuf,
					      isc_result_totext(result),
					      tsigrcode);
			}
		} else {
			status = client->message->sig0status;
			isc_buffer_init(&b, tsigrcode, sizeof(tsigrcode) - 1);
			tresult = dns_tsigrcode_totext(status, &b);
			INSIST(tresult == ISC_R_SUCCESS);
			tsigrcode[isc_buffer_usedlength(&b)] = '\0';
			ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
				      NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
				      "request has invalid signature: %s (%s)",
				      isc_result_totext(result), tsigrcode);
		}
		/*
		 * Accept update messages signed by unknown keys so that
		 * update forwarding works transparently through slaves
		 * that don't have all the same keys as the master.
		 */
		if (!(client->message->tsigstatus == dns_tsigerror_badkey &&
		      client->message->opcode == dns_opcode_update)) {
			ns_client_error(client, sigresult);
			goto cleanup;
		}
	}

	/*
	 * Decide whether recursive service is available to this client.
	 * We do this here rather than in the query code so that we can
	 * set the RA bit correctly on all kinds of responses, not just
	 * responses to ordinary queries.  Note if you can't query the
	 * cache there is no point in setting RA.
	 */
	ra = ISC_FALSE;
#ifdef ORIGINAL_ISC_CODE
	if (client->view->resolver != NULL &&
	    client->view->recursion == ISC_TRUE &&
	    ns_client_checkaclsilent(client, NULL,
				     client->view->recursionacl,
				     ISC_TRUE) == ISC_R_SUCCESS &&
	    ns_client_checkaclsilent(client, NULL,
				     client->view->cacheacl,
				     ISC_TRUE) == ISC_R_SUCCESS &&
	    ns_client_checkaclsilent(client, &client->destaddr,
				     client->view->recursiononacl,
				     ISC_TRUE) == ISC_R_SUCCESS &&
	    ns_client_checkaclsilent(client, &client->destaddr,
				     client->view->cacheonacl,
				     ISC_TRUE) == ISC_R_SUCCESS)
		ra = ISC_TRUE;
#else
	if (client->view->resolver != NULL &&
	    client->view->recursion == ISC_TRUE) {
		isc_result_t qaclres = ns_client_checkaclsilent(client, NULL,
								client->view->cacheacl,
								ISC_TRUE);

		// Since the default for both "allow-query-cache-on" and
		// "allow-recursion-on" is "any", and we don't offer any
		// way to change that, we don't need to check either the
		// client->view->recursiononacl or client->view->cacheonacl.

		if (qaclres == ISC_R_SUCCESS) {
			// We were allowed by the default "allow-query" ACL.
			// Remember this so we don't have to check again.
			client->query.attributes |= NS_QUERYATTR_QUERYOK;
			if (ns_client_checkaclsilent(client, NULL,
						     client->view->recursionacl,
						     ISC_TRUE) == ISC_R_SUCCESS)
				ra = ISC_TRUE;
		}
		// Remember that we've evaluated the view's query ACL. This
		// saves a call to ns_client_checkaclsilent() in query_getcachedb().
		client->query.attributes |= NS_QUERYATTR_QUERYOKVALID;
	}
#endif

	if (ra == ISC_TRUE)
		client->attributes |= NS_CLIENTATTR_RA;

	ns_client_log(client, DNS_LOGCATEGORY_SECURITY, NS_LOGMODULE_CLIENT,
		      ISC_LOG_DEBUG(3), ra ? "recursion available" :
					     "recursion not available");

	/*
	 * Are we able to forward ECS options in recursive queries
	 * for this client?
	 */
	if ((client->attributes & NS_CLIENTATTR_RA) != 0 &&
	    ECS_RECEIVED(client))
	{
		isc_boolean_t ecsfwd = ISC_FALSE;
		if (client->ecs.source == 0) {
			client->attributes |= NS_CLIENTATTR_ECSFORWARD;
			ecsfwd = ISC_TRUE;
		} else {
			isc_netaddr_t peeraddr;
			isc_netaddr_fromsockaddr(&peeraddr, &client->peeraddr);
			if (client->view->ecsforward != NULL &&
#ifdef ORIGINAL_ISC_CODE
			    allowed(&peeraddr, NULL, NULL,
				    client->view->ecsforward))
#else
			    allowed(&peeraddr, NULL, NULL,
				    client->view->ecsforward, view, client))
#endif
			{
				client->attributes |= NS_CLIENTATTR_ECSFORWARD;
				ecsfwd = ISC_TRUE;
			}
		}

		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(3),
			      "ECS forwarding %savailable",
			      ecsfwd ? "" : "not ");
	}

	/*
	 * Adjust maximum UDP response size for this client.
	 */
	if (client->udpsize > 512) {
		dns_peer_t *peer = NULL;
		isc_uint16_t udpsize = view->maxudp;
		(void) dns_peerlist_peerbyaddr(view->peers, &netaddr, &peer);
		if (peer != NULL)
			dns_peer_getmaxudp(peer, &udpsize);
		if (client->udpsize > udpsize)
			client->udpsize = udpsize;
	}

	/*
	 * Dispatch the request.
	 */
	switch (client->message->opcode) {
	case dns_opcode_query:
		CTRACE("query");
#ifdef HAVE_DNSTAP
		if ((client->message->flags & DNS_MESSAGEFLAG_RD) != 0)
			dtmsgtype = DNS_DTTYPE_CQ;
		else
			dtmsgtype = DNS_DTTYPE_AQ;

		dns_dt_send(view, dtmsgtype, &client->peeraddr,
			    &client->interface->addr, TCP_CLIENT(client), NULL,
			    &client->requesttime, NULL, buffer);
#endif /* HAVE_DNSTAP */
#ifdef ORIGINAL_ISC_CODE
#else
		/* This is required to guarantee that client have valid
		 * view when response logging is actually performed.
		 * We are using additional view ptr for response logging purposes
		 * because it's hard to predict lifetime of client->view. General intent
		 * is not to detach this additional response_logging_view in exit_check()
		 * function, but detach it straight after logging response.
		 */
		if (infoblox_g_capture_dns_responses == ISC_TRUE) {
			dns_view_attach(view, &client->response_logging_view);
		}
#endif
		ns_query_start(client);
		break;
	case dns_opcode_update:
		CTRACE("update");
		ns_client_settimeout(client, 60);
		ns_update_start(client, sigresult);
		break;
	case dns_opcode_notify:
		CTRACE("notify");
		ns_client_settimeout(client, 60);
		ns_notify_start(client);
		break;
	case dns_opcode_iquery:
		CTRACE("iquery");
		ns_client_error(client, DNS_R_NOTIMP);
		break;
	default:
		CTRACE("unknown opcode");
		ns_client_error(client, DNS_R_NOTIMP);
	}

 cleanup:
	return;
}

static void
client_timeout(isc_task_t *task, isc_event_t *event) {
	ns_client_t *client;

	REQUIRE(event != NULL);
	REQUIRE(event->ev_type == ISC_TIMEREVENT_LIFE ||
		event->ev_type == ISC_TIMEREVENT_IDLE);
	client = event->ev_arg;
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(task == client->task);
	REQUIRE(client->timer != NULL);

	UNUSED(task);

	CTRACE("timeout");

	isc_event_free(&event);

	if (client->shutdown != NULL) {
		(client->shutdown)(client->shutdown_arg, ISC_R_TIMEDOUT);
		client->shutdown = NULL;
		client->shutdown_arg = NULL;
	}

	if (client->newstate > NS_CLIENTSTATE_READY)
		client->newstate = NS_CLIENTSTATE_READY;
	(void)exit_check(client);
}

static isc_result_t
get_clientmctx(ns_clientmgr_t *manager, isc_mem_t **mctxp) {
	isc_mem_t *clientmctx;
	isc_result_t result;
#if NMCTXS > 0
	unsigned int nextmctx;
#endif

	MTRACE("clientmctx");

	/*
	 * Caller must be holding the manager lock.
	 */
	if (ns_g_clienttest) {
		result = isc_mem_create(0, 0, mctxp);
		if (result == ISC_R_SUCCESS)
			isc_mem_setname(*mctxp, "client", NULL);
		return (result);
	}
#if NMCTXS > 0
	nextmctx = manager->nextmctx++;
	if (manager->nextmctx == NMCTXS)
		manager->nextmctx = 0;

	INSIST(nextmctx < NMCTXS);

	clientmctx = manager->mctxpool[nextmctx];
	if (clientmctx == NULL) {
		result = isc_mem_create(0, 0, &clientmctx);
		if (result != ISC_R_SUCCESS)
			return (result);
		isc_mem_setname(clientmctx, "client", NULL);

		manager->mctxpool[nextmctx] = clientmctx;
	}
#else
	clientmctx = manager->mctx;
#endif

	isc_mem_attach(clientmctx, mctxp);

	return (ISC_R_SUCCESS);
}

static isc_result_t
client_create(ns_clientmgr_t *manager, ns_client_t **clientp) {
	ns_client_t *client;
	isc_result_t result;
	isc_mem_t *mctx = NULL;

	/*
	 * Caller must be holding the manager lock.
	 *
	 * Note: creating a client does not add the client to the
	 * manager's client list or set the client's manager pointer.
	 * The caller is responsible for that.
	 */

	REQUIRE(clientp != NULL && *clientp == NULL);

	result = get_clientmctx(manager, &mctx);
	if (result != ISC_R_SUCCESS)
		return (result);

	client = isc_mem_get(mctx, sizeof(*client));
	if (client == NULL) {
		isc_mem_detach(&mctx);
		return (ISC_R_NOMEMORY);
	}
	client->mctx = mctx;

	client->task = NULL;
	result = isc_task_create(manager->taskmgr, 0, &client->task);
	if (result != ISC_R_SUCCESS)
		goto cleanup_client;
	isc_task_setname(client->task, "client", client);

	client->timer = NULL;
	result = isc_timer_create(manager->timermgr, isc_timertype_inactive,
				  NULL, NULL, client->task, client_timeout,
				  client, &client->timer);
	if (result != ISC_R_SUCCESS)
		goto cleanup_task;
	client->timerset = ISC_FALSE;

	client->delaytimer = NULL;

	client->message = NULL;
	result = dns_message_create(client->mctx, DNS_MESSAGE_INTENTPARSE,
				    &client->message);
	if (result != ISC_R_SUCCESS)
		goto cleanup_timer;

	/* XXXRTH  Hardwired constants */

	client->sendevent = isc_socket_socketevent(client->mctx, client,
						   ISC_SOCKEVENT_SENDDONE,
						   client_senddone, client);
	if (client->sendevent == NULL) {
		result = ISC_R_NOMEMORY;
		goto cleanup_message;
	}

	client->recvbuf = isc_mem_get(client->mctx, RECV_BUFFER_SIZE);
	if  (client->recvbuf == NULL) {
		result = ISC_R_NOMEMORY;
		goto cleanup_sendevent;
	}

	client->recvevent = isc_socket_socketevent(client->mctx, client,
						   ISC_SOCKEVENT_RECVDONE,
						   client_request, client);
	if (client->recvevent == NULL) {
		result = ISC_R_NOMEMORY;
		goto cleanup_recvbuf;
	}

	client->magic = NS_CLIENT_MAGIC;
	client->manager = NULL;
	client->state = NS_CLIENTSTATE_INACTIVE;
	client->newstate = NS_CLIENTSTATE_MAX;
	client->naccepts = 0;
	client->nreads = 0;
	client->nsends = 0;
	client->nrecvs = 0;
	client->nupdates = 0;
	client->nctls = 0;
	client->references = 0;
	client->attributes = 0;
#ifdef ORIGINAL_ISC_CODE
#else
	(void) pthread_mutex_init (&client->view_lock, NULL);
	client->retain_view = 0;
	client->seen_soa_query = ISC_FALSE;
	client->response_logging_view = NULL;
	client->ecs_opt = NULL;
	client->subscriber = NULL;
	client->warning_vip = FALSE;
	client->rated_category = 0U;
	client->ibsppc_orig_rdataset = NULL;
	client->ibsppc_orig_sigrdataset = NULL;
	client->ib_client_id = NULL;
	memset(&client->ib_edns0_dest_addr, 0, sizeof(client->ib_edns0_dest_addr));
	client->infoblox_addropts.view_name = NULL;
#endif
	client->view = NULL;
	client->dispatch = NULL;
	client->udpsocket = NULL;
	client->tcplistener = NULL;
	client->tcpsocket = NULL;
	client->tcpmsg_valid = ISC_FALSE;
	client->tcpbuf = NULL;
	client->opt = NULL;
	client->udpsize = 512;
	client->dscp = -1;
	client->extflags = 0;
	client->ednsversion = -1;
	client->next = NULL;
	client->shutdown = NULL;
	client->shutdown_arg = NULL;
	client->signer = NULL;
	dns_name_init(&client->signername, NULL);
#ifdef ORIGINAL_ISC_CODE
#else
	client->ib_principal = NULL;
#endif
	client->mortal = ISC_FALSE;
	client->tcpconn = NULL;
	client->recursionquota = NULL;
	client->interface = NULL;
	client->peeraddr_valid = ISC_FALSE;

	dns_ecs_init(&client->ecs);

#ifdef ALLOW_FILTER_AAAA
	client->filter_aaaa = dns_aaaa_ok;
#endif
	client->needshutdown = ns_g_clienttest;
	client->tcpactive = ISC_FALSE;

	ISC_EVENT_INIT(&client->ctlevent, sizeof(client->ctlevent), 0, NULL,
		       NS_EVENT_CLIENTCONTROL, client_start, client, client,
		       NULL, NULL);
	/*
	 * Initialize FORMERR cache to sentinel value that will not match
	 * any actual FORMERR response.
	 */
	isc_sockaddr_any(&client->formerrcache.addr);
	client->formerrcache.time = 0;
	client->formerrcache.id = 0;
	ISC_LINK_INIT(client, link);
	ISC_LINK_INIT(client, rlink);
#ifdef ORIGINAL_ISC_CODE
#else
	ISC_LINK_INIT(client, rlplink);
#endif
	ISC_QLINK_INIT(client, ilink);
	client->keytag = NULL;
	client->keytag_len = 0;

	/*
	 * We call the init routines for the various kinds of client here,
	 * after we have created an otherwise valid client, because some
	 * of them call routines that REQUIRE(NS_CLIENT_VALID(client)).
	 */
	result = ns_query_init(client);
	if (result != ISC_R_SUCCESS)
		goto cleanup_recvevent;

	result = isc_task_onshutdown(client->task, client_shutdown, client);
	if (result != ISC_R_SUCCESS)
		goto cleanup_query;

	CTRACE("create");

	*clientp = client;

	return (ISC_R_SUCCESS);

 cleanup_query:
	ns_query_free(client);

 cleanup_recvevent:
	isc_event_free((isc_event_t **)&client->recvevent);

 cleanup_recvbuf:
	isc_mem_put(client->mctx, client->recvbuf, RECV_BUFFER_SIZE);

 cleanup_sendevent:
	isc_event_free((isc_event_t **)&client->sendevent);

	client->magic = 0;

 cleanup_message:
	dns_message_destroy(&client->message);

 cleanup_timer:
	isc_timer_detach(&client->timer);

 cleanup_task:
	isc_task_detach(&client->task);

 cleanup_client:
	isc_mem_putanddetach(&client->mctx, client, sizeof(*client));

	return (result);
}

static void
client_read(ns_client_t *client, isc_boolean_t newconn) {
	isc_result_t result;

	CTRACE("read");

	result = dns_tcpmsg_readmessage(&client->tcpmsg, client->task,
					client_request, client);
	if (result != ISC_R_SUCCESS)
		goto fail;

	/*
	 * Set a timeout to limit the amount of time we will wait
	 * for a request on this TCP connection.
	 */
	read_settimeout(client, newconn);

	client->state = client->newstate = NS_CLIENTSTATE_READING;
	INSIST(client->nreads == 0);
	INSIST(client->recursionquota == NULL);
	client->nreads++;

	return;
 fail:
	ns_client_next(client, result);
}

static void
client_newconn(isc_task_t *task, isc_event_t *event) {
	isc_result_t result;
	ns_client_t *client = event->ev_arg;
	isc_socket_newconnev_t *nevent = (isc_socket_newconnev_t *)event;
	uint32_t old;

	REQUIRE(event->ev_type == ISC_SOCKEVENT_NEWCONN);
	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(client->task == task);

	UNUSED(task);

	INSIST(client->state == NS_CLIENTSTATE_READY);

	/*
	 * The accept() was successful and we're now establishing a new
	 * connection. We need to make note of it in the client and
	 * interface objects so client objects can do the right thing
	 * when going inactive in exit_check() (see comments in
	 * client_accept() for details).
	 */
	INSIST(client->naccepts == 1);
	client->naccepts--;

	old = isc_atomic_xadd(&client->interface->ntcpaccepting, -1);
	INSIST(old > 0);

	/*
	 * We must take ownership of the new socket before the exit
	 * check to make sure it gets destroyed if we decide to exit.
	 */
	if (nevent->result == ISC_R_SUCCESS) {
		client->tcpsocket = nevent->newsocket;
		isc_socket_setname(client->tcpsocket, "client-tcp", NULL);
		client->state = NS_CLIENTSTATE_READING;
		INSIST(client->recursionquota == NULL);

		(void)isc_socket_getpeername(client->tcpsocket,
					     &client->peeraddr);
		client->peeraddr_valid = ISC_TRUE;
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			   NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(3),
			   "new TCP connection");
	} else {
		/*
		 * XXXRTH  What should we do?  We're trying to accept but
		 *	   it didn't work.  If we just give up, then TCP
		 *	   service may eventually stop.
		 *
		 *	   For now, we just go idle.
		 *
		 *	   Going idle is probably the right thing if the
		 *	   I/O was canceled.
		 */
		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(3),
			      "accept failed: %s",
			      isc_result_totext(nevent->result));
		tcpconn_detach(client);
	}

	if (exit_check(client))
		goto freeevent;

	if (nevent->result == ISC_R_SUCCESS) {
		int match;
		isc_netaddr_t netaddr;

		isc_netaddr_fromsockaddr(&netaddr, &client->peeraddr);

		if (ns_g_server->blackholeacl != NULL &&
		    dns_acl_match(&netaddr, NULL,
				  ns_g_server->blackholeacl,
				  &ns_g_server->aclenv,
				  &match, NULL) == ISC_R_SUCCESS &&
		    match > 0)
		{
			ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
				      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(10),
				      "blackholed connection attempt");
			client->newstate = NS_CLIENTSTATE_READY;
			(void)exit_check(client);
			goto freeevent;
		}

		INSIST(client->tcpmsg_valid == ISC_FALSE);
		dns_tcpmsg_init(client->mctx, client->tcpsocket,
				&client->tcpmsg);
		client->tcpmsg_valid = ISC_TRUE;

		/*
		 * Let a new client take our place immediately, before
		 * we wait for a request packet.  If we don't,
		 * telnetting to port 53 (once per CPU) will
		 * deny service to legitimate TCP clients.
		 */
		result = ns_client_replace(client);
		if (result == ISC_R_SUCCESS &&
		    (ns_g_server->keepresporder == NULL ||
#ifdef ORIGINAL_ISC_CODE
			   !allowed(&netaddr, NULL, NULL,
				    ns_g_server->keepresporder)))
#else
			   !allowed(&netaddr, NULL, NULL,
				    ns_g_server->keepresporder, NULL, client)))
#endif
		{
			client->tcpconn->pipelined = ISC_TRUE;
		}

		client_read(client, ISC_TRUE);
	}

 freeevent:
	isc_event_free(&event);
}

static void
client_accept(ns_client_t *client) {
	isc_result_t result;

	CTRACE("accept");

	/*
	 * Set up a new TCP connection. This means try to attach to the
	 * TCP client quota (tcp-clients), but fail if we're over quota.
	 */
	result = tcpconn_init(client, ISC_FALSE);
	if (result != ISC_R_SUCCESS) {
		isc_boolean_t exit;

		ns_client_log(client, NS_LOGCATEGORY_CLIENT,
			      NS_LOGMODULE_CLIENT, ISC_LOG_WARNING,
			      "TCP client quota reached: %s",
			      isc_result_totext(result));

		/*
		 * We have exceeded the system-wide TCP client quota.  But,
		 * we can't just block this accept in all cases, because if
		 * we did, a heavy TCP load on other interfaces might cause
		 * this interface to be starved, with no clients able to
		 * accept new connections.
		 *
		 * So, we check here to see if any other clients are
		 * already servicing TCP queries on this interface (whether
		 * accepting, reading, or processing). If we find that at
		 * least one client other than this one is active, then
		 * it's okay *not* to call accept - we can let this
		 * client go inactive and another will take over when it's
		 * done.
		 *
		 * If there aren't enough active clients on the interface,
		 * then we can be a little bit flexible about the quota.
		 * We'll allow *one* extra client through to ensure we're
		 * listening on every interface; we do this by setting the
		 * 'force' option to tcpconn_init().
		 *
		 * (Note: In practice this means that the real TCP client
		 * quota is tcp-clients plus the number of listening
		 * interfaces plus 1.)
		 */
		exit = (isc_atomic_xadd(&client->interface->ntcpactive, 0) >
			(client->tcpactive ? 1 : 0));
		if (exit) {
			client->newstate = NS_CLIENTSTATE_INACTIVE;
			(void)exit_check(client);
			return;
		}

		result = tcpconn_init(client, ISC_TRUE);
		RUNTIME_CHECK(result == ISC_R_SUCCESS);
	}

	/*
	 * If this client was set up using get_client() or get_worker(),
	 * then TCP is already marked active. However, if it was restarted
	 * from exit_check(), it might not be, so we take care of it now.
	 */
	mark_tcp_active(client, ISC_TRUE);

	result = isc_socket_accept(client->tcplistener, client->task,
				   client_newconn, client);
	if (result != ISC_R_SUCCESS) {
#ifdef ORIGINAL_ISC_CODE
#else
		if (result != ISC_R_SHUTTINGDOWN)
#endif
		/*
		 * XXXRTH  What should we do?  We're trying to accept but
		 *	   it didn't work.  If we just give up, then TCP
		 *	   service may eventually stop.
		 *
		 *	   For now, we just go idle.
		 */
		UNEXPECTED_ERROR(__FILE__, __LINE__,
				 "isc_socket_accept() failed: %s",
				 isc_result_totext(result));

		tcpconn_detach(client);
		mark_tcp_active(client, ISC_FALSE);
		return;
	}

	/*
	 * The client's 'naccepts' counter indicates that this client has
	 * called accept() and is waiting for a new connection. It should
	 * never exceed 1.
	 */
	INSIST(client->naccepts == 0);
	client->naccepts++;

	/*
	 * The interface's 'ntcpaccepting' counter is incremented when
	 * any client calls accept(), and decremented in client_newconn()
	 * once the connection is established.
	 *
	 * When the client object is shutting down after handling a TCP
	 * request (see exit_check()), if this value is at least one, that
	 * means another client has called accept() and is waiting to
	 * establish the next connection. That means the client may be
	 * be free to become inactive; otherwise it may need to start
	 * listening for connections itself to prevent the interface
	 * going dead.
	 */
	isc_atomic_xadd(&client->interface->ntcpaccepting, 1);
}

static void
client_udprecv(ns_client_t *client) {
	isc_result_t result;
	isc_region_t r;

	CTRACE("udprecv");

	r.base = client->recvbuf;
	r.length = RECV_BUFFER_SIZE;
	result = isc_socket_recv2(client->udpsocket, &r, 1,
				  client->task, client->recvevent, 0);
	if (result != ISC_R_SUCCESS) {
		UNEXPECTED_ERROR(__FILE__, __LINE__,
				 "isc_socket_recv2() failed: %s",
				 isc_result_totext(result));
		/*
		 * This cannot happen in the current implementation, since
		 * isc_socket_recv2() cannot fail if flags == 0.
		 *
		 * If this does fail, we just go idle.
		 */
		return;
	}
	INSIST(client->nrecvs == 0);
	client->nrecvs++;
}

void
ns_client_attach(ns_client_t *source, ns_client_t **targetp) {
	REQUIRE(NS_CLIENT_VALID(source));
	REQUIRE(targetp != NULL && *targetp == NULL);

	source->references++;
	ns_client_log(source, NS_LOGCATEGORY_CLIENT,
		      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(10),
		      "ns_client_attach: ref = %d", source->references);
	*targetp = source;
}

void
ns_client_detach(ns_client_t **clientp) {
	ns_client_t *client = *clientp;

	client->references--;
	INSIST(client->references >= 0);
	*clientp = NULL;
	ns_client_log(client, NS_LOGCATEGORY_CLIENT,
		      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(10),
		      "ns_client_detach: ref = %d", client->references);
	(void)exit_check(client);
}

isc_boolean_t
ns_client_shuttingdown(ns_client_t *client) {
	return (ISC_TF(client->newstate == NS_CLIENTSTATE_FREED));
}

isc_result_t
ns_client_replace(ns_client_t *client) {
	isc_result_t result;
	isc_boolean_t tcp;

	CTRACE("replace");

	REQUIRE(client != NULL);
	REQUIRE(client->manager != NULL);

	tcp = TCP_CLIENT(client);
	if (tcp && client->tcpconn != NULL && client->tcpconn->pipelined) {
		result = get_worker(client->manager, client->interface,
				    client->tcpsocket, client);
	} else {
#ifdef ORIGINAL_ISC_CODE
		result = get_client(client->manager, client->interface,
				    client->dispatch, tcp);
#else
		result = get_client(client->manager, client->interface,
				    client->dispatch, tcp, 0 /* not used */,
				    client->shadow_sock_fd);
#endif
	}
	if (result != ISC_R_SUCCESS) {
		return (result);
	}

	/*
	 * The responsibility for listening for new requests is hereby
	 * transferred to the new client.  Therefore, the old client
	 * should refrain from listening for any more requests.
	 */
	client->mortal = ISC_TRUE;

	return (ISC_R_SUCCESS);
}

/***
 *** Client Manager
 ***/

static void
clientmgr_destroy(ns_clientmgr_t *manager) {
#if NMCTXS > 0
	int i;
#endif

	REQUIRE(ISC_LIST_EMPTY(manager->clients));

	MTRACE("clientmgr_destroy");

#if NMCTXS > 0
	for (i = 0; i < NMCTXS; i++) {
		if (manager->mctxpool[i] != NULL)
			isc_mem_detach(&manager->mctxpool[i]);
	}
#endif

	ISC_QUEUE_DESTROY(manager->inactive);
	DESTROYLOCK(&manager->lock);
	DESTROYLOCK(&manager->listlock);
	DESTROYLOCK(&manager->reclock);
	manager->magic = 0;
	isc_mem_put(manager->mctx, manager, sizeof(*manager));
}

isc_result_t
ns_clientmgr_create(isc_mem_t *mctx, isc_taskmgr_t *taskmgr,
		    isc_timermgr_t *timermgr, ns_clientmgr_t **managerp)
{
	ns_clientmgr_t *manager;
	isc_result_t result;
#if NMCTXS > 0
	int i;
#endif

	manager = isc_mem_get(mctx, sizeof(*manager));
	if (manager == NULL)
		return (ISC_R_NOMEMORY);

	result = isc_mutex_init(&manager->lock);
	if (result != ISC_R_SUCCESS)
		goto cleanup_manager;

	result = isc_mutex_init(&manager->listlock);
	if (result != ISC_R_SUCCESS)
		goto cleanup_lock;

	result = isc_mutex_init(&manager->reclock);
	if (result != ISC_R_SUCCESS)
		goto cleanup_listlock;

	manager->mctx = mctx;
	manager->taskmgr = taskmgr;
	manager->timermgr = timermgr;
	manager->exiting = ISC_FALSE;
	ISC_LIST_INIT(manager->clients);
	ISC_LIST_INIT(manager->recursing);
#ifdef ORIGINAL_ISC_CODE
#else
	ISC_LIST_INIT(manager->recursing_low_prio);
#endif
	ISC_QUEUE_INIT(manager->inactive, ilink);
#if NMCTXS > 0
	manager->nextmctx = 0;
	for (i = 0; i < NMCTXS; i++)
		manager->mctxpool[i] = NULL; /* will be created on-demand */
#endif
	manager->magic = MANAGER_MAGIC;

	MTRACE("create");

	*managerp = manager;

	return (ISC_R_SUCCESS);

 cleanup_listlock:
	(void) isc_mutex_destroy(&manager->listlock);

 cleanup_lock:
	(void) isc_mutex_destroy(&manager->lock);

 cleanup_manager:
	isc_mem_put(manager->mctx, manager, sizeof(*manager));

	return (result);
}

void
ns_clientmgr_destroy(ns_clientmgr_t **managerp) {
	isc_result_t result;
	ns_clientmgr_t *manager;
	ns_client_t *client;
	isc_boolean_t need_destroy = ISC_FALSE, unlock = ISC_FALSE;

	REQUIRE(managerp != NULL);
	manager = *managerp;
	REQUIRE(VALID_MANAGER(manager));

	MTRACE("destroy");

	/*
	 * Check for success because we may already be task-exclusive
	 * at this point.  Only if we succeed at obtaining an exclusive
	 * lock now will we need to relinquish it later.
	 */
	result = isc_task_beginexclusive(ns_g_server->task);
	if (result == ISC_R_SUCCESS)
		unlock = ISC_TRUE;

	manager->exiting = ISC_TRUE;

	for (client = ISC_LIST_HEAD(manager->clients);
	     client != NULL;
	     client = ISC_LIST_NEXT(client, link))
		isc_task_shutdown(client->task);

	if (ISC_LIST_EMPTY(manager->clients))
		need_destroy = ISC_TRUE;

	if (unlock)
		isc_task_endexclusive(ns_g_server->task);

	if (need_destroy)
		clientmgr_destroy(manager);

	*managerp = NULL;
}

static isc_result_t
get_client(ns_clientmgr_t *manager, ns_interface_t *ifp,
#ifdef ORIGINAL_ISC_CODE
	   dns_dispatch_t *disp, isc_boolean_t tcp)
#else
	   dns_dispatch_t *disp, isc_boolean_t tcp, int disp_num, int shadow_fd)
#endif
{
	isc_result_t result = ISC_R_SUCCESS;
	isc_event_t *ev;
	ns_client_t *client;
	MTRACE("get client");

	REQUIRE(manager != NULL);

	if (manager->exiting)
		return (ISC_R_SHUTTINGDOWN);

	/*
	 * Allocate a client.  First try to get a recycled one;
	 * if that fails, make a new one.
	 */
	client = NULL;
	if (!ns_g_clienttest)
		ISC_QUEUE_POP(manager->inactive, ilink, client);

	if (client != NULL)
		MTRACE("recycle");
	else {
		MTRACE("create new");

		LOCK(&manager->lock);
		result = client_create(manager, &client);
		UNLOCK(&manager->lock);
		if (result != ISC_R_SUCCESS)
			return (result);

		LOCK(&manager->listlock);
		ISC_LIST_APPEND(manager->clients, client, link);
		UNLOCK(&manager->listlock);
	}

	client->manager = manager;
	ns_interface_attach(ifp, &client->interface);
	client->state = NS_CLIENTSTATE_READY;
	INSIST(client->recursionquota == NULL);

	client->dscp = ifp->dscp;

#ifdef ORIGINAL_ISC_CODE
#else
	client->shadow_sock_fd = -1;
#endif

	if (tcp) {
		mark_tcp_active(client, ISC_TRUE);

		client->attributes |= NS_CLIENTATTR_TCP;
		isc_socket_attach(ifp->tcpsocket,
				  &client->tcplistener);

	} else {
		isc_socket_t *sock;

		dns_dispatch_attach(disp, &client->dispatch);
		sock = dns_dispatch_getsocket(client->dispatch);
		isc_socket_attach(sock, &client->udpsocket);
#ifdef ORIGINAL_ISC_CODE
#else
		/* if we're being called by ns_client_replace(),
		 * then clone the shadow_fd */
		client->shadow_sock_fd = (shadow_fd == -1) ?
			ifp->shadowsocks[disp_num].sock_fd : shadow_fd;
#endif
	}

	INSIST(client->nctls == 0);
	client->nctls++;
	ev = &client->ctlevent;
	isc_task_send(client->task, &ev);

	return (ISC_R_SUCCESS);
}

static isc_result_t
get_worker(ns_clientmgr_t *manager, ns_interface_t *ifp, isc_socket_t *sock,
	   ns_client_t *oldclient)
{
	isc_result_t result = ISC_R_SUCCESS;
	isc_event_t *ev;
	ns_client_t *client;
	MTRACE("get worker");

	REQUIRE(manager != NULL);
	REQUIRE(oldclient != NULL);

	if (manager->exiting)
		return (ISC_R_SHUTTINGDOWN);

	/*
	 * Allocate a client.  First try to get a recycled one;
	 * if that fails, make a new one.
	 */
	client = NULL;
	if (!ns_g_clienttest)
		ISC_QUEUE_POP(manager->inactive, ilink, client);

	if (client != NULL)
		MTRACE("recycle");
	else {
		MTRACE("create new");

		LOCK(&manager->lock);
		result = client_create(manager, &client);
		UNLOCK(&manager->lock);
		if (result != ISC_R_SUCCESS)
			return (result);

		LOCK(&manager->listlock);
		ISC_LIST_APPEND(manager->clients, client, link);
		UNLOCK(&manager->listlock);
	}

	client->manager = manager;
	ns_interface_attach(ifp, &client->interface);
	client->newstate = client->state = NS_CLIENTSTATE_WORKING;
	INSIST(client->recursionquota == NULL);

	client->dscp = ifp->dscp;

	client->attributes |= NS_CLIENTATTR_TCP;
	client->mortal = ISC_TRUE;

	tcpconn_attach(oldclient, client);
	mark_tcp_active(client, ISC_TRUE);

	isc_socket_attach(ifp->tcpsocket, &client->tcplistener);
	isc_socket_attach(sock, &client->tcpsocket);
	isc_socket_setname(client->tcpsocket, "worker-tcp", NULL);
	(void)isc_socket_getpeername(client->tcpsocket, &client->peeraddr);
	client->peeraddr_valid = ISC_TRUE;

	INSIST(client->tcpmsg_valid == ISC_FALSE);
	dns_tcpmsg_init(client->mctx, client->tcpsocket, &client->tcpmsg);
	client->tcpmsg_valid = ISC_TRUE;

	INSIST(client->nctls == 0);
	client->nctls++;
	ev = &client->ctlevent;
	isc_task_send(client->task, &ev);

	return (ISC_R_SUCCESS);
}

isc_result_t
ns_clientmgr_createclients(ns_clientmgr_t *manager, unsigned int n,
			   ns_interface_t *ifp, isc_boolean_t tcp)
{
	isc_result_t result = ISC_R_SUCCESS;
	unsigned int disp;

	REQUIRE(VALID_MANAGER(manager));
	REQUIRE(n > 0);

	MTRACE("createclients");

	for (disp = 0; disp < n; disp++) {
#ifdef ORIGINAL_ISC_CODE
		result = get_client(manager, ifp, ifp->udpdispatch[disp], tcp);
#else
		result = get_client(manager, ifp, ifp->udpdispatch[disp], tcp, disp, -1);
#endif
		if (result != ISC_R_SUCCESS)
			break;
	}

	return (result);
}

isc_sockaddr_t *
ns_client_getsockaddr(ns_client_t *client) {
	return (&client->peeraddr);
}

isc_sockaddr_t *
ns_client_getdestaddr(ns_client_t *client) {
	return (&client->destsockaddr);
}


#ifdef ORIGINAL_ISC_CODE
#else
static unsigned char infoblox_dhcpd_ndata[] = "\014DHCP_UPDATER";
static unsigned char infoblox_dhcpd_offsets[] = { 0, 13 };
static dns_name_t infoblox_dhcpd = {
  DNS_NAME_MAGIC,
  infoblox_dhcpd_ndata, 14, 2,
  DNS_NAMEATTR_READONLY | DNS_NAMEATTR_ABSOLUTE,
  infoblox_dhcpd_offsets, NULL,
  {(void *)-1, (void *)-1},
  {NULL, NULL}, NULL
};
static dns_name_t *infoblox_dhcpd_name = &infoblox_dhcpd;
#endif

isc_result_t
ns_client_checkaclsilent(ns_client_t *client, isc_netaddr_t *netaddr,
			 dns_acl_t *acl, isc_boolean_t default_allow)
{
	isc_result_t result;
	isc_netaddr_t tmpnetaddr;
	dns_ecs_t *ecs = NULL;
	int match;
#ifdef ORIGINAL_ISC_CODE
#else
	isc_buffer_t  b;
	char client_keyname_buf[INFOBLOX_NAMEBUF_SIZE];
	char infoblox_keyname_buf[INFOBLOX_NAMEBUF_SIZE];
	int infoblox_keyname_len;
	isc_result_t r;

	if (netaddr == NULL) {
		isc_netaddr_fromsockaddr(&tmpnetaddr, &client->peeraddr);
		netaddr = &tmpnetaddr;
	}

	// Allow loopback addresses if of the correct form.
	// This is used to support synthetic DDNS updates.
	if (special_loopback_address (netaddr))
	  {
	    goto allow;
	  }
#endif

	if (acl == NULL) {
		if (default_allow)
			goto allow;
		else
			goto deny;
	}

//MMDNS-TODO: Either use above commented logic or use this one.
//#ifdef ORIGINAL_ISC_CODE
#if 1
	if (netaddr == NULL) {
		isc_netaddr_fromsockaddr(&tmpnetaddr, &client->peeraddr);
		netaddr = &tmpnetaddr;
	}
#else
	// Moved to occur before the acl==NULL check
#endif

	if (ECS_RECEIVED(client))
		ecs = &client->ecs;

#ifdef ORIGINAL_ISC_CODE
	result = dns_acl_matchx(netaddr, client->signer, ecs, acl,
				&ns_g_server->aclenv, &match, NULL);
#else
        if (infoblox_authenticated_via_gss_tsig (client)) {
	  result = infoblox_create_gss_tsig_key_name ();
	  if (result != ISC_R_SUCCESS)
	    {
	      ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			    NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
			    "Error creating GSS-TSIG signer name: %s",
			    isc_result_totext (result));
	      goto deny;
	    }
          result = dns_acl_matchx(netaddr, &gss_tsig_signer, ecs, acl,
				  &ns_g_server->aclenv, &match, NULL);
        } else {
          result = dns_acl_matchx(netaddr, client->signer, ecs, acl,
				  &ns_g_server->aclenv, &match, NULL);

          // Check if the client is our dhcpd
          if (result == ISC_R_SUCCESS && match > 0)
          {
            if (client->signer != NULL)
            {
              // If the client tsig key starts with 'DHCP_UPDATER', then
              // it is our dhcpd
              (void) memset (infoblox_keyname_buf, 0, sizeof(infoblox_keyname_buf));
              (void) memset (client_keyname_buf, 0, sizeof(client_keyname_buf));

              isc_buffer_init(&b, infoblox_keyname_buf, sizeof(infoblox_keyname_buf));
              r = dns_name_totext(infoblox_dhcpd_name, ISC_FALSE, &b);
              if (r != ISC_R_SUCCESS)
              {
                ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
                    NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
                    "Error converting DHCP key to string: %s",
                    isc_result_totext (r));
                goto deny;
              }

              isc_buffer_init(&b, client_keyname_buf, sizeof(client_keyname_buf));
              r = dns_name_totext(client->signer, ISC_FALSE, &b);
              if (r != ISC_R_SUCCESS)
              {
                ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
                    NS_LOGMODULE_CLIENT, ISC_LOG_ERROR,
                    "Error converting signer to string: %s",
                    isc_result_totext (r));
                goto deny;
              }

              infoblox_keyname_len = safe_strlen(infoblox_keyname_buf);

              if(strncasecmp(client_keyname_buf, infoblox_keyname_buf,
                  infoblox_keyname_len-1) == 0)
              {
                  client->is_infoblox_dhcpd = 1;
              }
            }
          }
        }
#endif

	if (result != ISC_R_SUCCESS)
		goto deny; /* Internal error, already logged. */

	if (match > 0)
		goto allow;
	goto deny; /* Negative match or no match. */

 allow:
	return (ISC_R_SUCCESS);

 deny:
	return (DNS_R_REFUSED);
}

isc_result_t
ns_client_checkacl(ns_client_t *client, isc_sockaddr_t *sockaddr,
		   const char *opname, dns_acl_t *acl,
		   isc_boolean_t default_allow, int log_level)
{
	isc_result_t result;
	isc_netaddr_t netaddr;

	if (sockaddr != NULL)
		isc_netaddr_fromsockaddr(&netaddr, sockaddr);

	result = ns_client_checkaclsilent(client, sockaddr ? &netaddr : NULL,
					  acl, default_allow);

	if (result == ISC_R_SUCCESS)
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(3),
			      "%s approved", opname);
	else
		ns_client_log(client, DNS_LOGCATEGORY_SECURITY,
			      NS_LOGMODULE_CLIENT,
			      log_level, "%s denied", opname);
	return (result);
}

static void
ns_client_name(ns_client_t *client, char *peerbuf, size_t len) {
	if (client->peeraddr_valid)
		isc_sockaddr_format(&client->peeraddr, peerbuf,
				    (unsigned int)len);
	else
		snprintf(peerbuf, len, "@%p", client);
}

void
ns_client_logv(ns_client_t *client, isc_logcategory_t *category,
	       isc_logmodule_t *module, int level, const char *fmt, va_list ap)
{
	char msgbuf[4096];
	char signerbuf[DNS_NAME_FORMATSIZE], qnamebuf[DNS_NAME_FORMATSIZE];
	char peerbuf[ISC_SOCKADDR_FORMATSIZE];
	const char *viewname = "";
	const char *sep1 = "", *sep2 = "", *sep3 = "", *sep4 = "";
	const char *signer = "", *qname = "";
	dns_name_t *q = NULL;

	vsnprintf(msgbuf, sizeof(msgbuf), fmt, ap);

	if (client->signer != NULL) {
		dns_name_format(client->signer, signerbuf, sizeof(signerbuf));
		sep1 = "/key ";
		signer = signerbuf;
	}

	q = client->query.origqname != NULL
		? client->query.origqname : client->query.qname;
	if (q != NULL) {
		dns_name_format(q, qnamebuf, sizeof(qnamebuf));
		sep2 = " (";
		sep3 = ")";
		qname = qnamebuf;
	}

	if (client->view != NULL && strcmp(client->view->name, "_bind") != 0 &&
	    strcmp(client->view->name, "_default") != 0) {
		sep4 = ": view ";
		viewname = client->view->name;
	}

	if (client->peeraddr_valid) {
		isc_sockaddr_format(&client->peeraddr,
				    peerbuf, sizeof(peerbuf));
	} else {
		snprintf(peerbuf, sizeof(peerbuf), "(no-peer)");
	}

	isc_log_write(ns_g_lctx, category, module, level,
		      "client @%p %s%s%s%s%s%s%s%s: %s",
		      client, peerbuf, sep1, signer, sep2, qname, sep3,
		      sep4, viewname, msgbuf);
}

void
ns_client_log(ns_client_t *client, isc_logcategory_t *category,
	   isc_logmodule_t *module, int level, const char *fmt, ...)
{
	va_list ap;

	if (! isc_log_wouldlog(ns_g_lctx, level))
		return;

	va_start(ap, fmt);
	ns_client_logv(client, category, module, level, fmt, ap);
	va_end(ap);
}

void
ns_client_aclmsg(const char *msg, dns_name_t *name, dns_rdatatype_t type,
		 dns_rdataclass_t rdclass, char *buf, size_t len)
{
	char namebuf[DNS_NAME_FORMATSIZE];
	char typebuf[DNS_RDATATYPE_FORMATSIZE];
	char classbuf[DNS_RDATACLASS_FORMATSIZE];

	dns_name_format(name, namebuf, sizeof(namebuf));
	dns_rdatatype_format(type, typebuf, sizeof(typebuf));
	dns_rdataclass_format(rdclass, classbuf, sizeof(classbuf));
	(void)snprintf(buf, len, "%s '%s/%s/%s'", msg, namebuf, typebuf,
		       classbuf);
}

static void
ns_client_dumpmessage(ns_client_t *client, const char *reason) {
	isc_buffer_t buffer;
	char *buf = NULL;
	int len = 1024;
	isc_result_t result;

	if (!isc_log_wouldlog(ns_g_lctx, ISC_LOG_DEBUG(1)))
		return;

	/*
	 * Note that these are multiline debug messages.  We want a newline
	 * to appear in the log after each message.
	 */

	do {
		buf = isc_mem_get(client->mctx, len);
		if (buf == NULL)
			break;
		isc_buffer_init(&buffer, buf, len);
		result = dns_message_totext(client->message,
					    &dns_master_style_debug,
					    0, &buffer);
		if (result == ISC_R_NOSPACE) {
			isc_mem_put(client->mctx, buf, len);
			len += 1024;
		} else if (result == ISC_R_SUCCESS)
			ns_client_log(client, NS_LOGCATEGORY_UNMATCHED,
				      NS_LOGMODULE_CLIENT, ISC_LOG_DEBUG(1),
				      "%s\n%.*s", reason,
				       (int)isc_buffer_usedlength(&buffer),
				       buf);
	} while (result == ISC_R_NOSPACE);

	if (buf != NULL)
		isc_mem_put(client->mctx, buf, len);
}

void
ns_client_dumprecursing(FILE *f, ns_clientmgr_t *manager) {
	ns_client_t *client;
	char namebuf[DNS_NAME_FORMATSIZE];
	char original[DNS_NAME_FORMATSIZE];
	char peerbuf[ISC_SOCKADDR_FORMATSIZE];
	char typebuf[DNS_RDATATYPE_FORMATSIZE];
	char classbuf[DNS_RDATACLASS_FORMATSIZE];
	const char *name;
	const char *sep;
	const char *origfor;
	dns_rdataset_t *rdataset;

	REQUIRE(VALID_MANAGER(manager));

	LOCK(&manager->reclock);
	client = ISC_LIST_HEAD(manager->recursing);
	while (client != NULL) {
		INSIST(client->state == NS_CLIENTSTATE_RECURSING);

		ns_client_name(client, peerbuf, sizeof(peerbuf));
		if (client->view != NULL &&
		    strcmp(client->view->name, "_bind") != 0 &&
		    strcmp(client->view->name, "_default") != 0) {
			name = client->view->name;
			sep = ": view ";
		} else {
			name = "";
			sep = "";
		}

		LOCK(&client->query.fetchlock);
		INSIST(client->query.qname != NULL);
		dns_name_format(client->query.qname, namebuf, sizeof(namebuf));
		if (client->query.qname != client->query.origqname &&
		    client->query.origqname != NULL) {
			origfor = " for ";
			dns_name_format(client->query.origqname, original,
					sizeof(original));
		} else {
			origfor = "";
			original[0] = '\0';
		}
		rdataset = ISC_LIST_HEAD(client->query.qname->list);
		if (rdataset == NULL && client->query.origqname != NULL)
			rdataset = ISC_LIST_HEAD(client->query.origqname->list);
		if (rdataset != NULL) {
			dns_rdatatype_format(rdataset->type, typebuf,
					     sizeof(typebuf));
			dns_rdataclass_format(rdataset->rdclass, classbuf,
					      sizeof(classbuf));
		} else {
			strlcpy(typebuf, "-", sizeof(typebuf));
			strlcpy(classbuf, "-", sizeof(classbuf));
		}
		UNLOCK(&client->query.fetchlock);
#ifdef ORIGINAL_ISC_CODE
		fprintf(f, "; client %s%s%s: id %u '%s/%s/%s'%s%s "
			"requesttime %u\n", peerbuf, sep, name,
			client->message->id, namebuf, typebuf, classbuf,
			origfor, original,
			isc_time_seconds(&client->requesttime));
#else
		fprintf(f, "; client %s%s%s: id %u '%s/%s/%s'%s%s "
			"requesttime %u%s\n", peerbuf, sep, name,
			client->message->id, namebuf, typebuf, classbuf,
			origfor, original,
			isc_time_seconds(&client->requesttime),
			ISC_LINK_LINKED(client, rlplink) ? " tier 2" : "");
#endif
		client = ISC_LIST_NEXT(client, rlink);
	}
	UNLOCK(&manager->reclock);
}

void
ns_client_qnamereplace(ns_client_t *client, dns_name_t *name) {
	LOCK(&client->query.fetchlock);
#ifdef ORIGINAL_ISC_CODE
	if (client->query.restarts > 0) {
#else
	if (client->query.restarts > 0 ||
	    (client->query.attributes & NS_QUERYATTR_QNAMEPROCESSED) != 0) {
#endif
		/*
		 * client->query.qname was dynamically allocated.
		 */
		dns_message_puttempname(client->message,
					&client->query.qname);
	}
	client->query.qname = name;
	client->query.attributes &= ~NS_QUERYATTR_REDIRECT;
	UNLOCK(&client->query.fetchlock);
}

isc_result_t
ns_client_sourceip(dns_clientinfo_t *ci, isc_sockaddr_t **addrp) {
	ns_client_t *client = (ns_client_t *) ci->data;

	REQUIRE(NS_CLIENT_VALID(client));
	REQUIRE(addrp != NULL);

	*addrp = &client->peeraddr;
	return (ISC_R_SUCCESS);
}

#ifdef ORIGINAL_ISC_CODE
#else
isc_result_t
ibtest_client_process_edns_client_subnet(ns_client_t *client,
					 isc_buffer_t *optbuf,
					 isc_uint16_t optlen)
{
	return (process_ecs(client, optbuf, optlen));
}


isc_result_t
ibtest_client_process_edns_outgoing_view(ns_client_t *client,
					 isc_buffer_t *optbuf,
					 isc_uint16_t optlen,
					 infoblox_addropts_t *addropts)
{
        return (process_outgoing_cview(client, optbuf, optlen, addropts));
}


isc_result_t
ibtest_client_create(ns_clientmgr_t *manager, ns_client_t **clientp) {
	return (client_create(manager, clientp));
}

void
ibtest_client_clientmgr_destroy(ns_clientmgr_t **managerp) {
	ns_clientmgr_t *manager = *managerp;
	REQUIRE(managerp != NULL);
	manager = *managerp;
	REQUIRE(VALID_MANAGER(manager));

	clientmgr_destroy(manager);
	*managerp = NULL;
}
#endif
