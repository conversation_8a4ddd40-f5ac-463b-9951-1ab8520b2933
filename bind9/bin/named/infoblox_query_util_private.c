/*
 * Copyright (c) 2013 Infoblox Inc. All Rights Reserved.
 */

#include <config.h>

#include <isc/buffer.h>
#include <isc/mem.h>
#include <isc/netaddr.h>
#include <isc/region.h>
#include <isc/result.h>
#include <isc/sockaddr.h>
#include <isc/util.h>

#include <named/infoblox_query_util_private.h>

/**
 * Construct the label to be prepended to the original qname.
 *
 * This utility function makes a DNS label to be prepended to an ordinary
 * query name for the DNS query rewriting feature from the given prefix string
 * and the IP address of the query client.  The result will be stored in
 * an isc_buffer object, which will also be created in this function.
 *
 * On successful return, *bufp will store the generated string, such as
 * "prefix-192-0-2-1", and only store that string.  The caller is responsible
 * for freeing the buffer using mctx.
 *
 * \param mctx The memory context used for memory allocation.  Must point to
 * a valid non NULL isc_mem object.
 * \param bufp Placeholder for isc_buffer to be allocated.
 * \param prefix The prefix string.  Can be NULL, in which case the prefix
 * part will be empty.
 * \param prefixlen The length of prefix, excluding any terminating nul
 * character.
 * \param client_addr The IP address to be used as part of the prepended label.
 * Currently only IPv4 addresses are supported; the behavior is undefined
 * if a different type of address is given.
 *
 * \return The result code of the creation: normally ISC_R_SUCCESS; if memory
 * allocation fails, it's ISC_R_NOMEMORY.
 */
isc_result_t
infoblox_make_rewrite_label(isc_mem_t *mctx, isc_buffer_t **bufp,
			    const char *prefix, unsigned int prefixlen,
			    isc_sockaddr_t *client_addr)
{
	isc_result_t result = ISC_R_SUCCESS;
	isc_buffer_t *buf;
	REQUIRE(bufp != NULL);
	
	const unsigned int prep_bufsize = sizeof("-255-255-255-255") +
		prefixlen;
	result = isc_buffer_allocate(mctx, bufp, prep_bufsize);
	if (result != ISC_R_SUCCESS)
		return (result);
	buf = *bufp;

	/*
	 * Due to the requirement of putmem we need a cast here, but it's safe
	 * in practice as putmem will use it as void *.
	 */
	isc_buffer_putmem(buf, (const unsigned char *)prefix, prefixlen);
	isc_buffer_putuint8(buf, '-');
	/*
	 * temporarily 'consume' the prefix part so we can look at the beginning
	 * of the address part below.
	 */
	isc_buffer_forward(buf, isc_buffer_usedlength(buf));

	/*
	 * The buffer size (allocated above) assumes it's an IPv4 address.
	 * Otherwise it could trigger an assertional failure; it's the
	 * caller's responsibility for guaranteeing the condition.
	 */
	isc_netaddr_t peer_netaddr;
	isc_netaddr_fromsockaddr(&peer_netaddr, client_addr);
	result = isc_netaddr_totext(&peer_netaddr, buf);
	RUNTIME_CHECK(result == ISC_R_SUCCESS);

	/* replace '.' with '-' in the IP address portion */
	isc_region_t r;
	unsigned int i;
	isc_buffer_remainingregion(buf, &r);
	for (i = 0; i < r.length; i++) {
		if (r.base[i] == '.')
			r.base[i] = '-';
	}
	isc_buffer_first(buf); /* make entire buffer active again */

	return (result);
}
