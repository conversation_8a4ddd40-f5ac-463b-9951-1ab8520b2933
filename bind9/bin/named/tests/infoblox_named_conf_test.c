/*
 * Copyright (c) 2017 Infoblox Inc. All Rights Reserved.
 */

#include <config.h>

#include <atf-c.h>
#include <string.h>

#include <isc/task.h>
#include <isc/hex.h>
#include <isc/util.h>

#include "../../lib/dns/tests/infoblox_dnstest.h"
#include <dns/infoblox_rpz_hitrate.h>

#include <isccfg/namedconf.h>

#include <named/config.h>
#include <named/infoblox_config.h>

/*
 * This test module intends to test some specific part of named.conf parsing.
 * To minimize dependency, it replicates some of the code logic in
 * named/server.c (such as how to build defaults and option hierarchy).
 *
 * Note: if this file is getting larger as it supports more configurations,
 * we may want to separate the test cases into per-feature files, sharing
 * the common setup/teardown and some generic utilities.
 */

/* File static variables that are expected to be used by various tests */
static cfg_parser_t *def_parser = NULL;
static cfg_obj_t *def_config = NULL;
static const cfg_obj_t *def_options = NULL;

/*
 * Common setup: read named.conf defaults and their options into def_config
 * and def_options.
 */
static void
common_setup() {
	ATF_REQUIRE_EQ(ibtest_dnstest_common_setup(), ISC_R_SUCCESS);
	ATF_REQUIRE_EQ(cfg_parser_create(ibtest_dnstest_mctx, NULL,
					 &def_parser), ISC_R_SUCCESS);
	ATF_REQUIRE_EQ(ns_config_parsedefaults(def_parser, &def_config),
		       ISC_R_SUCCESS);
	ATF_REQUIRE_EQ(cfg_map_get(def_config, "options", &def_options),
		       ISC_R_SUCCESS);
}

/* Common teardown: cleanup the stuff built in common_setup(). */
static void
common_teardown() {
	cfg_obj_destroy(def_parser, &def_config);
	cfg_parser_destroy(&def_parser);
	ibtest_dnstest_common_teardown();
}

static void
check_rpz_hitrate(const char *conf_text, isc_result_t exp_conf_result,
		  isc_boolean_t exp_create, isc_uint32_t exp_interval,
		  isc_uint32_t exp_max, isc_uint32_t exp_min,
		  isc_uint32_t exp_high, isc_uint32_t exp_low)
{
	isc_task_t *task = NULL;
	cfg_parser_t *parser = NULL;
	isc_buffer_t b;
	int i = 0;
	cfg_obj_t *test_config = NULL;
	const cfg_obj_t *options;
	const cfg_obj_t *maps[3];
	infoblox_threshold_monitor_t *monitor = NULL;

	ATF_REQUIRE_EQ(isc_task_create(ibtest_dnstest_taskmgr, 0, &task),
		       ISC_R_SUCCESS);

	/* Parse the configuration.  No syntax error assumed. */
	ATF_REQUIRE_EQ(cfg_parser_create(ibtest_dnstest_mctx, NULL,
					 &parser), ISC_R_SUCCESS);
	isc_buffer_constinit(&b, conf_text, strlen(conf_text));
	isc_buffer_add(&b, strlen(conf_text));
	ATF_REQUIRE_EQ(cfg_parse_buffer(parser, &b, &cfg_type_namedconf,
					&test_config), ISC_R_SUCCESS);

	/* Build option hierarchy: custom options followed by defaults */
	options = NULL;
	if (cfg_map_get(test_config, "options", &options) == ISC_R_SUCCESS)
		maps[i++] = options;
	maps[i++] = def_options;
	maps[i] = NULL;

	/*
	 * Build threshold monitor from the configuration.  The monitor should
	 * be created iff the configuration succeeds.
	 */
	ATF_REQUIRE_EQ(infoblox_config_rpz_hitrate(maps, ibtest_dnstest_mctx,
						   task,
						   ibtest_dnstest_timermgr,
						   &monitor),
		       exp_conf_result);
	ATF_REQUIRE_EQ(monitor != NULL, exp_create);

	/* Confirm the configured parameters */
	if (monitor != NULL) {
		infoblox_threshold_cfg_t *cfg = &monitor->alerts[0].config;
		ATF_REQUIRE_EQ(cfg->time_interval, exp_interval);
		ATF_REQUIRE_EQ(cfg->maximum_events, exp_max);
		ATF_REQUIRE_EQ(cfg->minimum_events, exp_min);
		ATF_REQUIRE_EQ(cfg->threshold_high, exp_high);
		ATF_REQUIRE_EQ(cfg->threshold_low, exp_low);
		ATF_REQUIRE_EQ(cfg->grace, 0);

		/*
		 * calling monitor shutdown not in the task exclusive mode
		 * for brevity.  It shouldn't cause a disruption in practice.
		 */
		infoblox_threshold_monitor_shutdown(monitor);
		infoblox_threshold_monitor_detach(&monitor);
	}

	/* cleanup */
	cfg_obj_destroy(parser, &test_config);
	cfg_parser_destroy(&parser);
	isc_task_detach(&task);
}

ATF_TC(rpz_hitrate);
ATF_TC_HEAD(rpz_hitrate, tc) {
	atf_tc_set_md_var(tc, "descr", "rpz-hit-rate configuration");
}
ATF_TC_BODY(rpz_hitrate, tc) {
	UNUSED(tc);

	common_setup();

	/* All-default: the hit rate monitor is disabled */
	check_rpz_hitrate("options{};", ISC_R_SUCCESS, ISC_FALSE,
			  0, 0, 0, 0, 0);
	/* Same if it's explicitly disabled */
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification no; };",
		ISC_R_SUCCESS, ISC_FALSE, 0, 0, 0, 0, 0);
	/* Explicitly enabled, then default parameters are used */
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification yes; };",
		ISC_R_SUCCESS, ISC_TRUE, 10, 100000, 1000, 10, 2);
	/* Customize the parameters to non-default values */
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification yes;"
		"infoblox-rpz-hit-rate-interval 60;"
		"infoblox-rpz-hit-rate-max-query 100;"
		"infoblox-rpz-hit-rate-min-query 10;"
		"infoblox-rpz-hit-rate-notification-trigger-value 50;"
		"infoblox-rpz-hit-rate-notification-reset-value 30; };",
		ISC_R_SUCCESS, ISC_TRUE, 60, 100, 10, 50, 30);
	/* Likewise, but exercise boundary values */
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification yes;"
		"infoblox-rpz-hit-rate-interval 86400;"
		"infoblox-rpz-hit-rate-max-query 1073741824;"
		"infoblox-rpz-hit-rate-min-query 0;"
		"infoblox-rpz-hit-rate-notification-trigger-value 100;"
		"infoblox-rpz-hit-rate-notification-reset-value 0; };",
		ISC_R_SUCCESS, ISC_TRUE, 86400, 1073741824, 0, 100, 0);
	/* Another boundary case: lowest allowable interval */
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification yes;"
		"infoblox-rpz-hit-rate-interval 1; };",
		ISC_R_SUCCESS, ISC_TRUE, 1, 100000, 1000, 10, 2);
	/* Out-of-range parameters result in a range error */
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification yes;"
		"infoblox-rpz-hit-rate-interval 86401; };",
		ISC_R_RANGE, ISC_FALSE, 0, 0, 0, 0, 0);
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification yes;"
		"infoblox-rpz-hit-rate-interval 0; };",
		ISC_R_RANGE, ISC_FALSE, 0, 0, 0, 0, 0);
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification yes;"
		"infoblox-rpz-hit-rate-notification-trigger-value 101; };",
		ISC_R_RANGE, ISC_FALSE, 0, 0, 0, 0, 0);
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification yes;"
		"infoblox-rpz-hit-rate-max-query 1073741825; };",
		ISC_R_RANGE, ISC_FALSE, 0, 0, 0, 0, 0);
	/* Invalid max-min relationship: must be max > min */
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification yes;"
		"infoblox-rpz-hit-rate-notification-reset-value 30;"
		"infoblox-rpz-hit-rate-notification-trigger-value 30; };",
		ISC_R_RANGE, ISC_FALSE, 0, 0, 0, 0, 0);
	check_rpz_hitrate(
		"options { infoblox-rpz-hit-rate-notification yes;"
		"infoblox-rpz-hit-rate-min-query 100;"
		"infoblox-rpz-hit-rate-max-query 100; };",
		ISC_R_RANGE, ISC_FALSE, 0, 0, 0, 0, 0);

	common_teardown();
}

/*
 * Main
 */
ATF_TP_ADD_TCS(tp) {
	ATF_TP_ADD_TC(tp, rpz_hitrate);
	return (atf_no_error());
}
