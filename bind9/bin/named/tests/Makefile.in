# Copyright (C) 2011, 2012  Internet Systems Consortium, Inc. ("ISC")
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
# REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
# AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
# INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
# LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
# OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.

# $Id$

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

# Attempt to disable parallel processing.
.NOTPARALLEL:
.NO_PARALLEL:

@BIND9_MAKE_INCLUDES@

include ../../../Makefile-infoblox.inc
INFOBLOX_TESTS_DIR = ${INFOBLOX_ATF_DIR}/bind9/lib/dns/tests/

CINCLUDES =	-I. -I../include ${DNS_INCLUDES} ${ISCCFG_INCLUDES} \
	${ISC_INCLUDES} @DST_OPENSSL_INC@ ${INFOBLOX_INCLUDES}
CDEFINES =	@CRYPTO@

INFOBLOX_SOURCE_ROOT	=	${top_srcdir}/..
INFOBLOX_PRODUCT_ROOT	=	$(INFOBLOX_SOURCE_ROOT)/products/dns
INFOBLOX_INCLUDES	=	-I$(INFOBLOX_SOURCE_ROOT)/common/server/include \
				-I$(INFOBLOX_PRODUCT_ROOT)/server/include \
				-I$(INFOBLOX_PRODUCT_ROOT)/../one/server/include \
				-I$(INFOBLOX_SOURCE_ROOT)/products/one/server/include/infoblox/auto_api \
				-I/opt/bloxdb/include \
				-I$(INFOBLOX_SOURCE_ROOT)/products/bind/server/include

ISCLIBS =	../../../lib/isc/libisc.@A@
ISCDEPLIBS =	../../../lib/isc/libisc.@A@
DNSLIBS =	../../../lib/dns/libdns.@A@ @DNS_CRYPTO_LIBS@
DNSDEPLIBS =	../../../lib/dns/libdns.@A@
ISCCFGLIBS =	../../../lib/isccfg/libisccfg.@A@
ISCCFGDEPLIBS =	../../../lib/isccfg/libisccfg.@A@

LIBS =		@LIBS@ @ATFLIBS@ ${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}

SRCS =		infoblox_query_util_test.c infoblox_latency_track_test.c \
		infoblox_response_log_test.c\
		infoblox_client_test.c\
		infoblox_named_conf_test.c\

SUBDIRS =
TARGETS =	infoblox_query_util_test@EXEEXT@ \
		infoblox_latency_track_test@EXEEXT@ \
		infoblox_response_log_test@EXEEXT@ \
		infoblox_client_test@EXEEXT@ \
		infoblox_named_conf_test@EXEEXT@ \

@BIND9_MAKE_RULES@

infoblox_query_util_test@EXEEXT@: infoblox_query_util_test.@O@ ../infoblox_query_util_private.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ \
			infoblox_query_util_test.@O@ ../infoblox_query_util_private.@O@ ${DNSLIBS} \
			${ISCLIBS} ${LIBS}

infoblox_latency_track_test@EXEEXT@: infoblox_latency_track_test.@O@ ../infoblox_latency_track.@O@ infoblox_namedtest.@O@ ../log.@O@ ../../../lib/dns/tests/infoblox_dnstest.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ \
			infoblox_latency_track_test.@O@ \
			../infoblox_latency_track.@O@ ../log.@O@ \
			infoblox_namedtest.@O@ \
			../../../lib/dns/tests/infoblox_dnstest.@O@ \
			${DNSLIBS} ${ISCCFGLIBS} ${ISCLIBS} ${LIBS}

infoblox_response_log_test@EXEEXT@: infoblox_response_log_test.@O@ ../infoblox_response_log.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ \
			infoblox_response_log_test.@O@ ../infoblox_response_log.@O@ ${DNSLIBS} \
			${ISCLIBS} ${LIBS}

infoblox_named_conf_test@EXEEXT@: infoblox_named_conf_test.@O@ ../config.@O@ ../infoblox_config.@O@ infoblox_namedtest.@O@ ../../../lib/dns/tests/infoblox_dnstest.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS} ${ISCCFGDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ \
			infoblox_named_conf_test.@O@ ../config.@O@ \
			../infoblox_config.@O@ infoblox_namedtest.@O@ \
			../../../lib/dns/tests/infoblox_dnstest.@O@ \
			${DNSLIBS} ${ISCCFGLIBS} ${ISCLIBS} ${LIBS}

infoblox_client_test@EXEEXT@: infoblox_client_test.@O@ \
			../client.@O@ \
			../log.@O@ \
			infoblox_namedtest.@O@ \
			../../../lib/dns/tests/infoblox_dnstest.@O@ \
			${ISCDEPLIBS} ${DNSDEPLIBS}
			${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ \
			infoblox_client_test.@O@ \
			../client.@O@ \
			../log.@O@ \
			infoblox_namedtest.@O@ \
			../../../lib/dns/tests/infoblox_dnstest.@O@ \
			${DNSLIBS} ${ISCCFGLIBS} ${ISCLIBS} ${LIBS}

unit::
	$(SHELL) ${top_srcdir}/unit/unittest.sh

install::
	mkdir -p ${INFOBLOX_TESTS_DIR}
	${INSTALL_PROGRAM} ${TARGETS} ${INFOBLOX_TESTS_DIR}

clean distclean::
	rm -f ${TARGETS}
	rm -f atf.out
