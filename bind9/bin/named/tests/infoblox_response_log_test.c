/*
 * Copyright (c) 2014 Infoblox Inc. All Rights Reserved.
 */

#include <config.h>

#include <atf-c.h>
#include <string.h>

#include <isc/buffer.h>
#include <isc/region.h>
#include <isc/util.h>

#include <named/infoblox_response_log.h>

/*
 * Parameterized helper of the 'finish' test, check the resulting string
 * of infoblox_response_log_finish() for the given input string and buffer size.
 *
 * 'buflen' is the size of isc_buffer_t that is used to render the log message.
 * this must not be larger than sizeof(target) - 2, allowing terminating nul
 * and an additional byte for overrun check.
 * 'txt' is a string to be copied into the isc_buffer before calling
 * infoblox_response_log_finish().  Its length must not be longer than
 * 'buflen'.
 * 'expected_txt' is the resulting string after calling
 * infoblox_response_log_finish().
 */
static void
check_finish(size_t buflen, const char *txt, const char *expected_txt) {
	char target[32];
	isc_buffer_t buffer;
	isc_region_t r;
	const char *cp;

	ATF_REQUIRE(buflen <= sizeof(target) - 2); /* test prerequisite */

	/* Set canary data (value arbitrarily chosen) */
	memset(target, 42, sizeof(target));

	/* Copy the given string to the buffer */
	isc_buffer_init(&buffer, target, buflen);
	isc_buffer_putstr(&buffer, txt);

	/* Finish and nul-terminate it, then check it against the expected */
	infoblox_response_log_finish(&buffer);
	isc_buffer_usedregion(&buffer, &r);
	r.base[r.length] = '\0';
	ATF_REQUIRE_STREQ(target, expected_txt);

	/*
	 * Overrun check with the canary.  target must be nul-terminated and
	 * should be intact beyond that.
	 */
	for (cp = target + strlen(expected_txt) + 1; cp < target + buflen; cp++)
		ATF_REQUIRE_EQ(*cp, 42);
}

ATF_TC(finish);
ATF_TC_HEAD(finish, tc) {
	atf_tc_set_md_var(tc, "descr", "finishing response logging buffer");
}
ATF_TC_BODY(finish, tc) {
	UNUSED(tc);

	/* Normal case: buffer has a sufficient space for NL */
	check_finish(8, "abcd", "abcd\n");

	/*
	 * No more space in buffer for NL but the original string has a space
	 * for '...'.  So the last 3 bytes of the string are replaced with it,
	 * and the 3rd '.' will then be replaced with NL.
	 */
	check_finish(4, "abcd", "a..\n");

	/*
	 * No buffer space for NL, and the original text doesn't even have
	 * enough space for '...' (wouldn't happen in production, but we cover
	 * the case to exercise all possible cases in the implementation).
	 * The last byte of the original string will be replaced with NL.
	 */
	check_finish(2, "ab", "a\n");
	check_finish(1, "a", "\n");

	/*
	 * In all cases, if the initial string has a terminating space, it
	 * will be used for the NL.
	 */
	check_finish(8, "abcd ", "abcd\n");
	check_finish(4, "abc ", "abc\n");
	check_finish(2, "a ", "a\n");
	check_finish(1, " ", "\n");
}

/*
 * Main
 */
ATF_TP_ADD_TCS(tp) {
	ATF_TP_ADD_TC(tp, finish);
	return (atf_no_error());
}
