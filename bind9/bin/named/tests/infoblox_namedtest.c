/*
 * Copyright (c) 2013 Infoblox Inc. All Rights Reserved.
 */

#include <config.h>

#include <isc/types.h>
#include <isc/time.h>

/*
 * Define common global variables.  Including globals.h after defining NS_MAIN
 * does the trick.  We also need to define some macro variables to initialize
 * the globals.
 */
#define NS_MAIN 1
#define VERSION "test"
#define PRODUCT "BIND"
#define DESCRIPTION ""
#define SRCID ""
#define CONFIGARGS "none"
#define BUILDER "make"
#define NS_SYSCONFDIR "./"
#define NS_LOCALSTATEDIR "./"

#include <named/globals.h>
