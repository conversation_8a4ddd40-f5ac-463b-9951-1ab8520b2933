/*
 * Copyright (c) 2013 Infoblox Inc. All Rights Reserved.
 */

#include <config.h>

#include <atf-c.h>

#include <unistd.h>
#include <arpa/inet.h>

#include <isc/event.h>
#include <isc/hash.h>
#include <isc/mem.h>
#include <isc/sockaddr.h>
#include <isc/socket.h>
#include <isc/task.h>
#include <isc/timer.h>
#include <isc/util.h>

#include <dns/name.h>
#include <dns/events.h>
#include <dns/view.h>
#include <dns/zone.h>
#include <dns/infoblox_latency_tree.h>

#include "../../lib/dns/tests/infoblox_dnstest.h"

#include <named/log.h>
#include <named/infoblox_latency_track.h>

static dns_view_t *view = NULL;
static infoblox_lt_tree_t *latency_tree = NULL;
static infoblox_lt_mgr_t *latency_mgr = NULL;
static const char *masters[2] = { "*********", "2001:db8::1" };

static void
insert_server_info(infoblox_lt_tree_t *tree, const char **addrs, int naddrs,
		   in_port_t port, const char *name)
{
	isc_sockaddr_t masters[16]; /* fixed number of max */
	dns_zone_t *zone = NULL;
	int i;

	REQUIRE(naddrs <= 16);	/* we enforce hardcoded max for simplicity */

	for (i = 0; i < naddrs; i++)
		ibtest_dnstest_str2sockaddr(addrs[i], port, &masters[i]);

	ATF_REQUIRE_EQ(ibtest_dnstest_create_zone(name, &zone, dns_zone_slave,
						  NULL, naddrs, masters,
						  ib_multi_master_auto,
						  NULL, NULL),
		       ISC_R_SUCCESS);
	ATF_REQUIRE_EQ(infoblox_lt_tree_add_servers(ibtest_dnstest_mctx,
						    zone, masters, naddrs,
						    &tree), ISC_R_SUCCESS);
	dns_zone_detach(&zone);
}

static infoblox_lt_info_t *
get_latency_info(infoblox_lt_tree_t *tree, const char *addr) {
	isc_sockaddr_t sa;
	isc_prefix_t prefix;
	isc_radix_node_t *node = NULL;

	/* port doesn't matter, use 0 */
	ibtest_dnstest_str2sockaddr(addr, 0, &sa);
	infoblox_prefix_from_sockaddr(&sa, &prefix);

	if (isc_radix_search(tree->radix, &node, &prefix) != ISC_R_SUCCESS)
		return (NULL);
	return (infoblox_isc_radix_getdata(node, &prefix));
}

static unsigned int
invoke_taskevent(isc_task_t *task, isc_eventtype_t ev_type) {
	unsigned int count = 0;
	isc_eventlist_t events;
	isc_event_t *ev;

	ISC_LIST_INIT(events);
	isc_task_unsend(task, NULL, ev_type, NULL, &events);

	while ((ev = HEAD(events)) != NULL) {
		UNLINK(events, ev, ev_link);
		ev->ev_action(task, ev);
		count++;
	}

	return (count);
}

static void
common_setup(in_port_t port) {
	ATF_REQUIRE_EQ(ibtest_dnstest_common_setup(), ISC_R_SUCCESS);
	ATF_REQUIRE_EQ(ibtest_dnstest_create_view(&view), ISC_R_SUCCESS);
	ATF_REQUIRE_EQ(infoblox_lt_tree_create(ibtest_dnstest_mctx,
					       &latency_tree),
		       ISC_R_SUCCESS);
	infoblox_lt_mgr_create(ibtest_dnstest_mctx, latency_tree,
			       ibtest_dnstest_taskmgr, view->requestmgr,
			       &latency_mgr);
	insert_server_info(latency_tree, masters, 2, port, "example.com");
}

static void
common_teardown() {
	if (latency_mgr != NULL)
		infoblox_lt_mgr_destroy(&latency_mgr);
	ATF_REQUIRE_EQ(latency_mgr, NULL);
	if (latency_tree != NULL) /* some test has detached it */
		infoblox_lt_tree_detach(&latency_tree);
	dns_view_detach(&view);
	ibtest_dnstest_common_teardown();
}

/*
 * Parameterized subroutine.
 * It traces task events related to latency probe request/reply exchanges.
 * 'probe_result' will be used as a faked result value of the request.
 * If 'remove_info' is true, the passed info will be removed and freed before
 * calling the request event hander to see if such an unexpected event causes
 * any disruption.
 * If 'remove_node' is true, the corresponding radix tree node for the info will
 * be removed, too.  In this case 'remove_info' must also set be true.
 */
static void
trace_latency_probe(isc_result_t probe_result, isc_boolean_t remove_info,
		    isc_boolean_t remove_node)
{
	const char * const addr = "*********";
	isc_sockaddr_t sa;
	isc_prefix_t prefix;
	isc_radix_node_t *node = NULL;
	dns_message_t *message = NULL;
	infoblox_lt_info_t *info;
	isc_uint64_t cur_rtt;

	/*
	 * We use port 0 so sending the probe request will immediately fail,
	 * making the test complete without requiring timeout.
	 */
	common_setup(0);
	info = get_latency_info(latency_tree, addr);
	ATF_REQUIRE(info != NULL);

	/* Identify radix node in case we need to tweak it in the test */
	ibtest_dnstest_str2sockaddr(addr, 0, &sa); /* port doesn't matter. */
	infoblox_prefix_from_sockaddr(&sa, &prefix);
	ATF_REQUIRE_EQ(isc_radix_search(latency_tree->radix, &node, &prefix),
		       ISC_R_SUCCESS);

	/* Below, we need to invoke zone events by hand, so pause taskmgr. */
	ibtest_dnstest_pause_taskmgr();

	ATF_REQUIRE_EQ(ibtest_update_latency_wrapper(latency_mgr, info),
		       ISC_R_SUCCESS);
	ATF_REQUIRE_EQ(invoke_taskevent(ibtest_lt_mgr_gettask(latency_mgr),
					ISC_SOCKEVENT_SENDDONE), 1);
	if (remove_info) {
		dns_name_free(&info->zone_name, info->mctx);
		isc_mem_put(info->mctx, info, sizeof(*info));
		infoblox_isc_radix_setdata(node, &prefix, NULL);
		info = NULL;
	}
	if (remove_node) {
		REQUIRE(remove_info); /* remove_node should imply remove_info */
		isc_radix_remove(latency_tree->radix, node);
	}

	if (info != NULL)
		cur_rtt = info->rtt;

	/*
	 * Fake successful probe.  The current implementation doesn't parse
	 * the response message, so we can simply create a header-only one
	 * (and can use it regardless of the probe_result value).
	 */
	message = ibtest_dnstest_createmessage(NULL, NULL, ISC_FALSE);
	ibtest_dnstest_fakerequestevent(ibtest_lt_mgr_gettask(latency_mgr),
					probe_result, message);
	dns_message_destroy(&message);

	if (info != NULL) {
		/*
		 * In case the request is expected to fail, the updated RTT
		 * is predictable.  Otherwise we cannot assume any specific
		 * condition, but it should be reasonable to assume the updated
		 * value is smaller than IBLT_INITIAL_RTT (2 seconds).
		 * Also, this master will be considered 'unreachable' unless
		 * the request's result is 'success'.
		 */
		if (probe_result != ISC_R_SUCCESS) {
			ATF_REQUIRE_EQ(info->rtt,
				       cur_rtt + IBLT_RTT_INCREASE_ON_FAILURE);
			ATF_REQUIRE_EQ(info->unreachable, ISC_TRUE);
		} else {
			ATF_REQUIRE(info->rtt < IBLT_INITIAL_RTT);
			ATF_REQUIRE_EQ(info->unreachable, ISC_FALSE);
		}
	}

	/* Trace completed, resume the task manager. */
	ibtest_dnstest_resume_taskmgr();
	common_teardown();	/* cleanup and leak check */
}

ATF_TC(update_latency);
ATF_TC_HEAD(update_latency, tc) {
	atf_tc_set_md_var(tc, "descr", "various cases of latency probe");
}
ATF_TC_BODY(update_latency, tc) {
	UNUSED(tc);

	/* Usual case, either probe succeeds or not */
	trace_latency_probe(ISC_R_SUCCESS, ISC_FALSE, ISC_FALSE);
	trace_latency_probe(ISC_R_FAILURE, ISC_FALSE, ISC_FALSE);

	/*
	 * Abnormal case, the latency info somehow disappears from the tree.
	 * Shouldn't happen in production, but we exercise the code path.
	 */
	trace_latency_probe(ISC_R_SUCCESS, ISC_TRUE, ISC_FALSE);
	trace_latency_probe(ISC_R_FAILURE, ISC_TRUE, ISC_FALSE);
	trace_latency_probe(ISC_R_SUCCESS, ISC_TRUE, ISC_TRUE);
	trace_latency_probe(ISC_R_FAILURE, ISC_TRUE, ISC_TRUE);
}

ATF_TC(update_latency_fail);
ATF_TC_HEAD(update_latency_fail, tc) {
	atf_tc_set_md_var(tc, "descr",
			  "check the case of latency probe failure");
}
ATF_TC_BODY(update_latency_fail, tc) {
	infoblox_lt_info_t *info = NULL;

	UNUSED(tc);

	common_setup(0);

	/*
	 * In our common setup the requestmgr's only configures an IPv4
	 * dispatch, so sending the request should immediately fail.  We
	 * explicitly confirm that, and also confirm such an unexpected case
	 * doesn't cause any disruption.
	 */
	info = get_latency_info(latency_tree, "2001:db8::1");
	ATF_REQUIRE(info != NULL);
	ATF_REQUIRE_EQ(ibtest_update_latency_wrapper(latency_mgr, info),
		       ISC_R_FAMILYNOSUPPORT);

	common_teardown();
}

ATF_TC(update_latency_cancel);
ATF_TC_HEAD(update_latency_cancel, tc) {
	atf_tc_set_md_var(tc, "descr", "mgr is destroyed with open probes");
}
ATF_TC_BODY(update_latency_cancel, tc) {
	infoblox_lt_info_t *info = NULL;
	isc_task_t *task;

	UNUSED(tc);

	common_setup(5300);

	info = get_latency_info(latency_tree, "*********");
	ATF_REQUIRE(info != NULL);

	ibtest_dnstest_pause_taskmgr();

	ATF_REQUIRE_EQ(ibtest_update_latency_wrapper(latency_mgr, info),
		       ISC_R_SUCCESS);
	task = ibtest_lt_mgr_gettask(latency_mgr);

	/*
	 * Now destroy the latency manager.  The query should still be
	 * outstanding, which will be quickly canceled through a sequence of
	 * events.
	 */
	infoblox_lt_mgr_destroy(&latency_mgr);
	/* lg_mgr's control event cancels the outstanding request */
	ATF_REQUIRE_EQ(invoke_taskevent(task, ISC_TASKEVENT_SHUTDOWN), 1);
	/* internal control event in the request module due to the cancel */
	ATF_REQUIRE_EQ(invoke_taskevent(task, DNS_EVENT_REQUESTCONTROL), 1);
	/* outstanding socket I/O is canceled */
	ATF_REQUIRE_EQ(invoke_taskevent(task, ISC_SOCKEVENT_SENDDONE), 1);
	/* finally, the callback is called, and the manager is destroyed. */
	ATF_REQUIRE_EQ(invoke_taskevent(task, DNS_EVENT_REQUESTDONE), 1);
	info = get_latency_info(latency_tree, "*********");
	/* The RTT shouldn't be penalized as it's internal cancel */
	ATF_REQUIRE(info->rtt !=
		    IBLT_INITIAL_RTT + IBLT_RTT_INCREASE_ON_FAILURE);

	ibtest_dnstest_resume_taskmgr();

	common_teardown();
}

ATF_TC(detach_tree_first);
ATF_TC_HEAD(detach_tree_first, tc) {
	atf_tc_set_md_var(tc, "descr", "latency tree is detached immediately");
}
ATF_TC_BODY(detach_tree_first, tc) {
	infoblox_lt_tree_t *latency_tree_clone;

	UNUSED(tc);
	common_setup(0);

	/*
	 * Immediately detach the latency tree locally, but it should be still
	 * valid in the manager.
	 */
	latency_tree_clone = latency_tree;
	infoblox_lt_tree_detach(&latency_tree_clone);
	ATF_REQUIRE(get_latency_info(latency_tree, "*********") != NULL);
	latency_tree = NULL;	/* avoid duplicate free in teardown */

	common_teardown();
}

ATF_TC(latency_calc);
ATF_TC_HEAD(latency_calc, tc) {
	atf_tc_set_md_var(tc, "descr", "");
	atf_tc_set_md_var(tc, "keywords", "mmdns");
}
ATF_TC_BODY(latency_calc, tc) {
	const unsigned int one_msec_nano = 1000000; /* 1ms in nanoseconds */
	const unsigned int one_msec_micro = 1000; /* 1ms in microseconds */
	const unsigned int one_usec_nano = 1000; /* 1us in nanoseconds */
	isc_time_t start, end;

	UNUSED(tc);

	/*
	 * check the granularity (should be usec).  the old rtt and the diff are
	 * both 1 (usec), so the adjusted latency should also be 1.
	 */
	isc_time_set(&start, 1, 500 * one_msec_nano);
	isc_time_set(&end, 1, 500 * one_msec_nano + one_usec_nano);
	ATF_REQUIRE_EQ(infoblox_latency_calc(&start, &end, 1, ISC_FALSE), 1);

	/*
	 * end - start = 600ms (= old = adjusted).  This exercises the case
	 * where end.usec < start.usec
	 */
	isc_time_set(&end, 2, 100 * one_msec_nano);
	ATF_REQUIRE_EQ(infoblox_latency_calc(&start, &end, 600 * one_msec_micro,
					     ISC_FALSE), 600 * one_msec_micro);

	/*
	 * start == end (very unlikely in practice, but could happen).
	 * 'old' will be set to 600msec, so the adjusted latency will be
	 * 600*3/10=180msec.
	 */
	end = start;
	ATF_REQUIRE_EQ(infoblox_latency_calc(&start, &end, 600 * one_msec_micro,
					     ISC_FALSE), 180 * one_msec_micro);

	/* start > end.  'old' will be kept as 'adjusted' */
	isc_time_set(&end, 1, 100 * one_msec_nano);
	ATF_REQUIRE_EQ(infoblox_latency_calc(&start, &end, 600 * one_msec_micro,
					     ISC_FALSE), 600 * one_msec_micro);

	/*
	 * penalize the latency.  usually it will be old + 200msec
	 * (intentionally hardcode the number for testing purpose).
	 */
	ATF_REQUIRE_EQ(infoblox_latency_calc(&start, &end, 600 * one_msec_micro,
					     ISC_TRUE), 800 * one_msec_micro);

	/* But not exceeding 10sec */
	ATF_REQUIRE_EQ(infoblox_latency_calc(&start, &end,
					     9801 * one_msec_micro, /* 9.801s */
					     ISC_TRUE), 10000 * one_msec_micro);
}

/*
 * Main
 */
ATF_TP_ADD_TCS(tp) {
	ATF_TP_ADD_TC(tp, update_latency);
	ATF_TP_ADD_TC(tp, update_latency_fail);
	ATF_TP_ADD_TC(tp, update_latency_cancel);
	ATF_TP_ADD_TC(tp, detach_tree_first);
	ATF_TP_ADD_TC(tp, latency_calc);
	return (atf_no_error());
}
