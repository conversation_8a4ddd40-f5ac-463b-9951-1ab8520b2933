/*
 * Copyright (c) 2019 Infoblox Inc. All Rights Reserved.
 */

#include <config.h>

#include <atf-c.h>
#include <string.h>

#include <isc/buffer.h>
#include <isc/region.h>
#include <isc/util.h>
#include <isc/os.h>

#include <dns/result.h>

#include <named/globals.h>
#include <named/server.h>
#include <named/interfacemgr.h>
#include <named/client.h>
#include <named/query.h>
#include <named/notify.h>
#include <named/update.h>
#include <named/infoblox_response_log.h>
#ifdef WIN32
#include "../win32/include/named/os.h"
#else
#include "../unix/include/named/os.h"
#endif

#include <dns/infoblox_edns0.h>
#include <dns/infoblox_parentalcontrol.h>
#include "../../lib/dns/tests/infoblox_dnstest.h"

#define MAX_TEST_OPT_LENGTH 260

ns_clientmgr_t *ibtest_ns_clientmgr = NULL;
ns_client_t *ibtest_ns_client = NULL;

////////////////////////////////////////////////////////////////////////////////
// Begin of stub function definitions
isc_boolean_t
ns_interfacemgr_listeningon(ns_interfacemgr_t *mgr, isc_sockaddr_t *addr) {
	UNUSED(mgr);
	UNUSED(addr);
	return ISC_R_SUCCESS;
}

void
ns_query_cancel(ns_client_t *client) {
	UNUSED(client);
}

void
ns_notify_start(ns_client_t *client) {
	UNUSED(client);
}

void
ns_update_start(ns_client_t *client, isc_result_t sigresult) {
	UNUSED(client);
	UNUSED(sigresult);
}

void
ns_query_start(ns_client_t *client) {
	UNUSED(client);
}

isc_result_t
ns_query_init(ns_client_t *client) {
	UNUSED(client);
	return ISC_R_SUCCESS;
}

void
ns_query_incstats(ns_client_t *client, isc_statscounter_t counter) {
	UNUSED(client);
	UNUSED(counter);
}

void
ns_interface_attach(ns_interface_t *source, ns_interface_t **target) {
	UNUSED(source);
	UNUSED(target);
}

void
ns_interface_detach(ns_interface_t **targetp) {
	UNUSED(targetp);
}

isc_boolean_t
infoblox_should_log_dns_data(ns_client_t *client) {
	UNUSED(client);
	return ISC_FALSE;
}

isc_result_t
ns_os_gethostname(char *buf, size_t len) {
	int n;

	n = gethostname(buf, len);
	return ((n == 0) ? ISC_R_SUCCESS : ISC_R_FAILURE);
}

void
ns_query_free(ns_client_t *client) {
	UNUSED(client);
}

void
infoblox_response_log_finish(isc_buffer_t *buffer) {
	UNUSED(buffer);
}

isc_result_t
infoblox_log_dns_entry(const char *entry) {
	UNUSED(entry);
	return ISC_R_SUCCESS;
}

isc_result_t
infoblox_close_response_log_file(FILE **file) {
	UNUSED(file);
	return ISC_R_SUCCESS;
}

isc_result_t
infoblox_open_response_log_file(FILE **out_file, struct timeval *tv) {
	UNUSED(out_file);
	UNUSED(tv);
	return ISC_R_SUCCESS;
}

void
infoblox_format_query_info(ns_client_t *client, isc_buffer_t *target) {
	UNUSED(client);
	UNUSED(target);
}
// End of stub function definitions
////////////////////////////////////////////////////////////////////////////////

static void
common_setup() {
	ATF_REQUIRE_EQ(ibtest_dnstest_common_setup(), ISC_R_SUCCESS);
	ns_g_server = isc_mem_get(ibtest_dnstest_mctx, sizeof(*ns_g_server));
	memset(ns_g_server, 0, sizeof(*ns_g_server));
	ATF_REQUIRE_EQ(ns_clientmgr_create(ibtest_dnstest_mctx,
					   ibtest_dnstest_taskmgr,
					   ibtest_dnstest_timermgr,
					   &ibtest_ns_clientmgr), ISC_R_SUCCESS);
	ns_client_t *client = NULL;
	ATF_REQUIRE_EQ(ibtest_client_create(ibtest_ns_clientmgr, &client),
					    ISC_R_SUCCESS);
	ATF_REQUIRE_EQ(isc_stats_create(ibtest_dnstest_mctx, &ns_g_server->nsstats,
					dns_nsstatscounter_max), ISC_R_SUCCESS);

	ns_client_attach(client, &ibtest_ns_client);
}

static void
common_teardown() {
	if (ibtest_ns_client) {
		/* Because we didn't create a full state client, destroying such
		 * client doesn't go through all paths in client.c:exit_check()
		 * and cause created client->ecs_opt not freed properly.
		 * So we free it explicitly in the UT.
		 */
		if (ibtest_ns_client->ecs_opt) {
			isc_mem_put(ibtest_ns_client->mctx, ibtest_ns_client->ecs_opt,
				    sizeof(ib_edns_client_subnet_t));
			ibtest_ns_client->ecs_opt = NULL;
		}
		if (ibtest_ns_client->infoblox_addropts.view_name) {
			isc_mem_put(ibtest_ns_client->mctx,
				    ibtest_ns_client->infoblox_addropts.view_name,
				    ibtest_ns_client->infoblox_addropts.view_name_len);
                        ibtest_ns_client->infoblox_addropts.view_name = NULL;
                }

		ns_client_detach(&ibtest_ns_client);
	}
	if (ibtest_ns_clientmgr)
		ibtest_client_clientmgr_destroy(&ibtest_ns_clientmgr);
	if (ns_g_server) {
		isc_stats_detach(&ns_g_server->nsstats);
		isc_mem_put(ibtest_dnstest_mctx, ns_g_server, sizeof(*ns_g_server));
	}
	ibtest_dnstest_common_teardown();
}

typedef struct {
	const char *description;
	unsigned char data[MAX_TEST_OPT_LENGTH]; // buffer to hold of edns client subnet option
	size_t len; // length of edns client subnet option in above buffer
} test_rdata_data_t;

static void
free_rdatalist(isc_mem_t *mctx, dns_rdatalist_t *rdatalist) {
	dns_rdata_t *rdata;
	while(!ISC_LIST_EMPTY(rdatalist->rdata))
	{
		rdata = ISC_LIST_HEAD(rdatalist->rdata);
		ISC_LIST_DEQUEUE(rdatalist->rdata, rdata, link);
		isc_mem_put(mctx, rdata, sizeof(*rdata));
	}
}

static void
build_opt_rdatalist(isc_mem_t *mctx, const test_rdata_data_t *opt_data,
		    size_t num, dns_rdatalist_t *rdatalist,
		    unsigned char *buf, size_t buf_size) {
	isc_buffer_t source, target;
	dns_rdata_t *rdata;
	dns_decompress_t dctx;

	rdatalist->rdclass = dns_rdataclass_in;
	rdatalist->type = dns_rdatatype_opt;

	size_t i;
	for (i = 0; i < num; i++) {
		isc_buffer_constinit(&source, opt_data[i].data,
				     opt_data[i].len);
		isc_buffer_add(&source, opt_data[i].len);
		isc_buffer_setactive(&source, opt_data[i].len);
		isc_buffer_init(&target, buf, buf_size);
		ATF_REQUIRE(rdata = isc_mem_get(mctx, sizeof(*rdata)));
		dns_rdata_init(rdata);
		dns_decompress_init(&dctx, -1, DNS_DECOMPRESS_ANY);
		ATF_REQUIRE_EQ(dns_rdata_fromwire(rdata, rdatalist->rdclass,
						  rdatalist->type, &source,
						  &dctx, 0, &target),
			       ISC_R_SUCCESS);
		dns_decompress_invalidate(&dctx);
		ISC_LIST_APPEND(rdatalist->rdata, rdata, link);
		rdata = NULL;
	}

	return;
}

static void
test_process_edns_client_subnet(ns_client_t *client, dns_rdataset_t *opt,
				isc_result_t exp_result) {
	isc_buffer_t optbuf;
	isc_uint16_t optcode;
	isc_uint16_t optlen;
	isc_result_t result;
	dns_rdata_t rdata;

	/* Similar to while loop in client.c::process_opt() but processing
	 * edns-client-subnet option only. */
	ATF_REQUIRE_EQ(dns_rdataset_first(opt), ISC_R_SUCCESS);
	dns_rdata_init(&rdata);
	dns_rdataset_current(opt, &rdata);
	isc_buffer_init(&optbuf, rdata.data, rdata.length);
	isc_buffer_add(&optbuf, rdata.length);
	while (isc_buffer_remaininglength(&optbuf) >= 4) {
		optcode = isc_buffer_getuint16(&optbuf);
		optlen = isc_buffer_getuint16(&optbuf);
		ATF_REQUIRE_EQ(optcode, DNS_OPT_CLIENT_SUBNET);
		result = ibtest_client_process_edns_client_subnet(client, &optbuf, optlen);
		if (result != ISC_R_SUCCESS)
			break;
	}
	ATF_REQUIRE_EQ(result, exp_result);
}

ATF_TC(ns_client_process_multiple_edns_client_subnet);
ATF_TC_HEAD(ns_client_process_multiple_edns_client_subnet, tc) {
	atf_tc_set_md_var(tc, "descr",
			  "Processing option containing two edns client subnet options fails");
}
ATF_TC_BODY(ns_client_process_multiple_edns_client_subnet, tc) {
	UNUSED(tc);

	common_setup();

	const test_rdata_data_t opt_data[] = {
		{
			"Option contains two edns client subnet options:"
			" family 1 (ipv4), source 2, scope 0, address=[0x80] and"
			" family 1 (ipv4), source 2, scope 0, address=[0x40]."
			" Name server edns client subnet check failed because multiple"
			" edns client subnet option exist.",
			{
				// First edns subnet client option
				0x00, 0x08, 0x00, 0x05,
				0x00, 0x01, 0x02, 0x00, 0x80,
				// Second edns subnet client option
				0x00, 0x08, 0x00, 0x05,
				0x00, 0x01, 0x02, 0x00, 0x40,
			}, 18
		},
	};

	dns_rdataset_t opt;
	dns_rdataset_init(&opt);

	dns_rdatalist_t rdatalist;
	dns_rdatalist_init(&rdatalist);
	unsigned char buf[MAX_TEST_OPT_LENGTH];
	build_opt_rdatalist(ibtest_dnstest_mctx,
			    opt_data, sizeof(opt_data)/sizeof(opt_data[0]),
			    &rdatalist, buf, sizeof(buf));
	ATF_REQUIRE_EQ(dns_rdatalist_tordataset(&rdatalist, &opt), ISC_R_SUCCESS);
	ns_client_t *client = ibtest_ns_client;
	ATF_REQUIRE_EQ(client->ecs_opt, NULL);
	test_process_edns_client_subnet(client, &opt, DNS_R_FORMERR);

	free_rdatalist(ibtest_dnstest_mctx, &rdatalist);
	common_teardown();
}

ATF_TC(ns_client_process_edns_client_subnet);
ATF_TC_HEAD(ns_client_process_edns_client_subnet, tc) {
	atf_tc_set_md_var(tc, "descr",
			  "Process edns client subnet option and add to response");
}
ATF_TC_BODY(ns_client_process_edns_client_subnet, tc) {
	UNUSED(tc);

	common_setup();

	const test_rdata_data_t opt_data[] = {
		{
			"Option contains single edns client subnet options:"
			" family 1 (ipv4), source 2, scope 0, address=[0x80]"
			" Name server edns client subnet check passed.",
			{
				0x00, 0x08, 0x00, 0x05,
				0x00, 0x01, 0x02, 0x00, 0x80,
			}, 9
		},
	};

	dns_rdataset_t opt;
	dns_rdataset_init(&opt);

	/* Build edns client subnet option */
	dns_rdatalist_t rdatalist;
	dns_rdatalist_init(&rdatalist);
	unsigned char buf[MAX_TEST_OPT_LENGTH];
	build_opt_rdatalist(ibtest_dnstest_mctx,
			    opt_data, sizeof(opt_data)/sizeof(opt_data[0]),
			    &rdatalist, buf, sizeof(buf));
	ATF_REQUIRE_EQ(dns_rdatalist_tordataset(&rdatalist, &opt), ISC_R_SUCCESS);
	ns_client_t *client = ibtest_ns_client;
	/* Process edns client subnet option */
	test_process_edns_client_subnet(client, &opt, ISC_R_SUCCESS);
	ATF_REQUIRE(client->ecs_opt != NULL);
	ATF_REQUIRE_EQ(client->ecs_opt->family, edns_opt_family_inet);
	ATF_REQUIRE_EQ(client->ecs_opt->source_prefix_length, 2);
	ATF_REQUIRE_EQ(client->ecs_opt->scope_prefix_length, 0);
	ATF_REQUIRE_EQ(client->ecs_opt->addrbytes, 1);
	ATF_REQUIRE_EQ(client->ecs_opt->enabled, ISC_FALSE);

	/* Set edns client subnet option in response without changing any field
	 * in edns client subnet option. The option in response should
	 * be the same as the one in query.
	 */
	client->ecs_opt->enabled = ISC_TRUE;
	dns_rdataset_t *out_opt = NULL;
	ATF_REQUIRE_EQ(ns_client_addopt(client, client->message, &out_opt), ISC_R_SUCCESS);
	ATF_REQUIRE_EQ(dns_rdataset_count(out_opt), 1);
	ATF_REQUIRE_EQ(dns_rdataset_first(out_opt), ISC_R_SUCCESS);
	dns_rdata_t rdata;
	dns_rdata_init(&rdata);
	dns_rdataset_current(out_opt, &rdata);
	ATF_REQUIRE_EQ(rdata.length, opt_data[0].len);
	ATF_REQUIRE_EQ(memcmp(rdata.data, opt_data[0].data, rdata.length), 0);
	client->message->opt = out_opt;
	dns_message_reset(client->message, DNS_MESSAGE_INTENTPARSE);

	/* Test the case where edns client subnet option having empty ADDRESS
	 * field can be added to response properly.
	 */
	client->ecs_opt->enabled = ISC_TRUE;
	/* can be either IPv6 or IPv4, but cannot be other value */
	client->ecs.addr.family = AF_INET6;
	/* SOURCE PREFIX-LENGTH must be set to 0 if ADDRESS field is 0 */
	client->ecs.source = 0;
	/* Set SCOPE PREFIX-LENGTH to specific value 0 */
	client->ecs_opt->scope_prefix_length = 0;
	client->ecs.source = 0;
	out_opt = NULL;
	ATF_REQUIRE_EQ(ns_client_addopt(client, client->message, &out_opt), ISC_R_SUCCESS);
	ATF_REQUIRE_EQ(dns_rdataset_count(out_opt), 1);
	ATF_REQUIRE_EQ(dns_rdataset_first(out_opt), ISC_R_SUCCESS);
	dns_rdata_init(&rdata);
	dns_rdataset_current(out_opt, &rdata);
	unsigned char exp_opt_data[] = {
	    0x00, 0x08, 0x00, 0x04,
	    0x00, 0x02, 0x00, 0x00
	};
	ATF_REQUIRE_EQ(rdata.length, sizeof(exp_opt_data));
	ATF_REQUIRE_EQ(memcmp(rdata.data, exp_opt_data, rdata.length), 0);
	client->message->opt = out_opt;
	dns_message_reset(client->message, DNS_MESSAGE_INTENTPARSE);

	free_rdatalist(ibtest_dnstest_mctx, &rdatalist);
	common_teardown();
}

static void
check_edns_client_subnet_failed(const test_rdata_data_t *opt_data) {
	common_setup();

	dns_rdataset_t opt;
	dns_rdataset_init(&opt);

	/* Build edns client subnet option */
	dns_rdatalist_t rdatalist;
	dns_rdatalist_init(&rdatalist);
	unsigned char buf[MAX_TEST_OPT_LENGTH];
	build_opt_rdatalist(ibtest_dnstest_mctx, opt_data, 1,
			    &rdatalist, buf, sizeof(buf));
	ATF_REQUIRE_EQ(dns_rdatalist_tordataset(&rdatalist, &opt), ISC_R_SUCCESS);
	ns_client_t *client = ibtest_ns_client;
	/* Process edns client subnet option (expected to fail) */
	test_process_edns_client_subnet(client, &opt, DNS_R_OPTERR);
	ATF_REQUIRE_EQ(client->ecs_opt, NULL);

	free_rdatalist(ibtest_dnstest_mctx, &rdatalist);
	common_teardown();
}

ATF_TC(ns_client_process_edns_client_subnet_failed);
ATF_TC_HEAD(ns_client_process_edns_client_subnet_failed, tc) {
	atf_tc_set_md_var(tc, "descr", "check validation on bad ECS options");
}
ATF_TC_BODY(ns_client_process_edns_client_subnet_failed, tc) {
	unsigned int i;

	UNUSED(tc);

	/* In case we extend more failure cases we generalize the test setup. */
	const test_rdata_data_t opt_data[] = {
		{
			"Option contains single edns client subnet options:"
			" family 1 (ipv4), source 2, scope 1, address=[0x80]"
			" Name server edns client subnet check failed because"
			" scope isn't 0.",
			{
				0x00, 0x08, 0x00, 0x05,
				0x00, 0x01, 0x02, 0x01, 0x80,
			}, 9
		},
	};

	for (i = 0; i < sizeof(opt_data) / sizeof(opt_data[0]); i++) {
		check_edns_client_subnet_failed(&opt_data[i]);
	}
}

/* test parameters for the category_opt test */
typedef struct {
	isc_boolean_t use_tcp;	/* if response is supposed to sent over TCP */
	isc_boolean_t use_nsid;	/* if NSID EDNS option should exist */
	isc_boolean_t with_signer; /* if response is to be TSIG/SIG0 signed */
	isc_uint16_t optcode;	   /* expected category option code (one of two)
				    * or 0 if it shouldn't be included. */
	isc_uint64_t upper;	/* upper 64 bits of test category bits */
	isc_uint64_t lower;	/* lower 64 bits of test category bits */
	unsigned char expected[16]; /* wire data of category opt to be sent */
} categoryopt_data_t;

/*
 * Subroutine of test_add_category_opt: inspect outgoing EDNS options ('opt').
 * If 'have_nsid' is true, NSID (another EDNS option) is supposed to be
 * included.
 * 'category_optcode' is the expected code for the added category option
 * or 0 if it's supposed to be omitted.
 * 'expected' is expected binary sequence in wire of the category option
 * (when it's supposed to be included).
 */
static void
validate_opts(dns_rdataset_t *opt, isc_boolean_t have_nsid,
	      isc_uint16_t category_optcode, const unsigned char *expected)
{
	dns_rdata_t rdata;
	isc_buffer_t optbuf;
	const unsigned char *cp;

	/* Extract the OPT RDATA */
	ATF_REQUIRE_EQ(dns_rdataset_first(opt), ISC_R_SUCCESS);
	dns_rdata_init(&rdata);
	dns_rdataset_current(opt, &rdata);
	isc_buffer_init(&optbuf, rdata.data, rdata.length);
	isc_buffer_add(&optbuf, rdata.length);

	/* Check the existence of NSID and skip it (note: we need to extract
	 * the option length into a separate variable (optlen); otherwise
	 * the inline version of isc_buffer_forward() would break optbuf. */
	if (have_nsid) {
		ATF_REQUIRE_EQ(isc_buffer_getuint16(&optbuf), DNS_OPT_NSID);
		uint16_t optlen = isc_buffer_getuint16(&optbuf);
		isc_buffer_forward(&optbuf, optlen);
	}
	/* Check the value of category option if it's expected to be added */
	if (category_optcode != 0) {
		ATF_REQUIRE_EQ(isc_buffer_getuint16(&optbuf), category_optcode);
		ATF_REQUIRE_EQ(isc_buffer_getuint16(&optbuf), 16);
		cp = isc_buffer_current(&optbuf);
		ATF_REQUIRE_EQ(memcmp(expected, cp, 16), 0);
		isc_buffer_forward(&optbuf, 16);
	}
	/* We should have gone over all possible options.  If this check
	 * fails it means some options were unexpectedly included. */
	ATF_REQUIRE_EQ(isc_buffer_remaininglength(&optbuf), 0);
}

static void
test_add_category_opt(const categoryopt_data_t *data) {
	isc_result_t expected_result = ISC_R_SUCCESS;
	ib_subscriber_t subscriber;
	isc_uint16_t expected_opcode = data->optcode;

	common_setup();
	ns_g_server->server_usehostname = ISC_TRUE;

	/* Build subscriber data */
	memset(&subscriber, 0, sizeof(subscriber));
	subscriber.matched_categories =
		(((ib_categories_t)data->upper) << 64 | data->lower);
	subscriber.edns_category_opt = data->optcode;
	ibtest_ns_client->subscriber = &subscriber;

	/* Tweak the client object for unusual conditions.  Note that
	 * these shouldn't require explicit cleanup, so we can safely call
	 * common_teardown() below. */
	if (data->use_tcp)	/* response over TCP */
		ibtest_ns_client->attributes |= NS_CLIENTATTR_TCP;
	if (data->use_nsid)	/* another option (NSID) is included */
		ibtest_ns_client->attributes |= NS_CLIENTATTR_WANTNSID;
	if (data->with_signer)	/* response is to be TSIG/SIG0 signed */
		ibtest_ns_client->signer = dns_rootname;

	/* In some cases the category option shouldn't be included, and
	 * a call to ns_client_addopt that would break the restriction
	 * should fail.  These conditions include:
	 * - CATEGORY1 option is to be included while other EDNS option
	 *   already exists (CATEGORY1 can only appear in a dedicated
	 *   EDNS OPT message).
	 * - If the response is to be sent over TCP or signed using TSIG
	 *   or SIG(0). */
	if ((data->optcode == DNS_OPT_IB_SUBSCRIBER_CATEGORY1 &&
	     data->use_nsid) ||
	    (data->optcode != 0 && (data->use_tcp || data->with_signer))) {
		expected_result = ISC_R_UNEXPECTED;
	}
	ATF_REQUIRE_EQ(ns_client_addopt(ibtest_ns_client,
					ibtest_ns_client->message,
					&ibtest_ns_client->opt),
		       expected_result);
	/* inspect the options if addopt is supposed to succeed. */
	if (expected_result == ISC_R_SUCCESS) {
		validate_opts(ibtest_ns_client->opt, data->use_nsid,
			      expected_opcode, data->expected);
	}

	common_teardown();
}

ATF_TC(add_category_opt);
ATF_TC_HEAD(add_category_opt, tc) {
	atf_tc_set_md_var(tc, "keywords", "imc");
	atf_tc_set_md_var(tc, "descr", "building subscriber category option");
}
ATF_TC_BODY(add_category_opt, tc) {
	size_t i;

	UNUSED(tc);

	/* Parameterized test data */
	const categoryopt_data_t dataset[] = {
		/* Normal cases for both CATEGORY[12].  The test option value
		 * is arbitrarily chosen so that it can reasonably prove
		 * the encoded data and byte ordering are correct. */
		{ ISC_FALSE, ISC_FALSE, ISC_FALSE,
		  DNS_OPT_IB_SUBSCRIBER_CATEGORY1,
		  0xbed3337af16bf987, 0x5364aaf56e66bf20,
		  {0xbe, 0xd3, 0x33, 0x7a, 0xf1, 0x6b, 0xf9, 0x87,
		   0x53, 0x64, 0xaa, 0xf5, 0x6e, 0x66, 0xbf, 0x20}},
		{ ISC_FALSE, ISC_FALSE, ISC_FALSE,
		  DNS_OPT_IB_SUBSCRIBER_CATEGORY2,
		  0xbed3337af16bf987, 0x5364aaf56e66bf20,
		  {0xbe, 0xd3, 0x33, 0x7a, 0xf1, 0x6b, 0xf9, 0x87,
		   0x53, 0x64, 0xaa, 0xf5, 0x6e, 0x66, 0xbf, 0x20}},
		/* case where category option isn't expected to be added. */
		{ ISC_FALSE, ISC_FALSE, ISC_FALSE, 0, 0, 0, {}},
		/* TCP response: addopt should fail. */
		{ ISC_TRUE, ISC_FALSE, ISC_FALSE,
		  DNS_OPT_IB_SUBSCRIBER_CATEGORY1, 0, 0, {}},
		{ ISC_TRUE, ISC_FALSE, ISC_FALSE,
		  DNS_OPT_IB_SUBSCRIBER_CATEGORY2, 0, 0, {}},
		/* CATEGORY1 option with another option (NSID): addopt should
		 * fail. */
		{ ISC_FALSE, ISC_TRUE, ISC_FALSE,
		  DNS_OPT_IB_SUBSCRIBER_CATEGORY1, 0, 0, {0}},
		/* CATEGORY2 can coexist with other option. */
		{ ISC_FALSE, ISC_TRUE, ISC_FALSE,
		  DNS_OPT_IB_SUBSCRIBER_CATEGORY2, 0, 0, {0}},
		/* response with TSIG/SIG0 signing: addopt should fail. */
		{ ISC_FALSE, ISC_FALSE, ISC_TRUE,
		  DNS_OPT_IB_SUBSCRIBER_CATEGORY1, 0, 0, {}},
		{ ISC_FALSE, ISC_FALSE, ISC_TRUE,
		  DNS_OPT_IB_SUBSCRIBER_CATEGORY2, 0, 0, {}}};

	for (i = 0; i < sizeof(dataset) / sizeof(dataset[0]); i++)
		test_add_category_opt(&dataset[i]);
}

static void
test_process_edns_client_outgoing_view(ns_client_t *client, dns_rdataset_t *opt,
					isc_result_t exp_result) {
	isc_buffer_t optbuf;
	isc_uint16_t optcode;
	isc_uint16_t optlen;
	isc_result_t result;
	dns_rdata_t rdata;

	/*Similar to while loop in client.c::process_opt() but processing
	 * edns-client-outgoing-view option only. */
	ATF_REQUIRE_EQ(dns_rdataset_first(opt), ISC_R_SUCCESS);
	dns_rdata_init(&rdata);
	dns_rdataset_current(opt, &rdata);
	isc_buffer_init(&optbuf, rdata.data, rdata.length);
	isc_buffer_add(&optbuf, rdata.length);
	while (isc_buffer_remaininglength(&optbuf) >= 4) {
		optcode = isc_buffer_getuint16(&optbuf);
		optlen = isc_buffer_getuint16(&optbuf);
		ATF_REQUIRE_EQ(optcode, DNS_OPT_OG_CVIEW);
		result = ibtest_client_process_edns_outgoing_view(client,
						&optbuf, optlen,
						&ibtest_ns_client->infoblox_addropts);
		if (result != ISC_R_SUCCESS)
			break;
	}
	ATF_REQUIRE_EQ(result, exp_result);
}

ATF_TC(ns_client_process_multiple_edns_client_outgoing_view);
ATF_TC_HEAD(ns_client_process_multiple_edns_client_outgoing_view, tc) {
        atf_tc_set_md_var(tc, "descr",
                "Processing option containing two edns client outgoing view options fails");
}
ATF_TC_BODY(ns_client_process_multiple_edns_client_outgoing_view, tc) {
        UNUSED(tc);

        common_setup();

        const test_rdata_data_t opt_data[] = {
                {
                        "Option contains two edns client outgoing view options:"
                        " Name server edns client outgoign view check failed because multiple"
                        " edns client outgoing view option exist.",
                        {
                                // First edns client outgoing view option
                                0xFF, 0xF6, 0x00, 0x08,
                                0x5F, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6C, 0x74,
                                // Second edns client outgoing view option
                                0xFF, 0xF6, 0x00, 0x09,
                                0x5F, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61, 0x6C,
                        },25
                },
        };

        dns_rdataset_t opt;
        dns_rdataset_init(&opt);

        dns_rdatalist_t rdatalist;
        dns_rdatalist_init(&rdatalist);
        unsigned char buf[MAX_TEST_OPT_LENGTH];
        build_opt_rdatalist(ibtest_dnstest_mctx,
                            opt_data, sizeof(opt_data)/sizeof(opt_data[0]),
                            &rdatalist, buf, sizeof(buf));
        ATF_REQUIRE_EQ(dns_rdatalist_tordataset(&rdatalist, &opt), ISC_R_SUCCESS);
        ns_client_t *client = ibtest_ns_client;

        test_process_edns_client_outgoing_view(client, &opt, DNS_R_FORMERR);

        free_rdatalist(ibtest_dnstest_mctx, &rdatalist);
        common_teardown();
}

ATF_TC(ns_client_process_edns_client_outgoing_view);
ATF_TC_HEAD(ns_client_process_edns_client_outgoing_view, tc) {
        atf_tc_set_md_var(tc, "descr",
                          "Process edns client outgoing view option");
}
ATF_TC_BODY(ns_client_process_edns_client_outgoing_view, tc) {
        UNUSED(tc);

        common_setup();

        const test_rdata_data_t opt_data[] = {
                {
                        "Option contains single edns client outgoing view options:"
                        " Name server edns client outgoing view check passed.",
                        {
                                0xFF, 0xF6, 0x00, 0x08,
                                0x5F, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6C, 0x74,
                        },12
                },
        };

        dns_rdataset_t opt;
        dns_rdataset_init(&opt);

        /* Build edns client outgoing view option */
        dns_rdatalist_t rdatalist;
        dns_rdatalist_init(&rdatalist);
        unsigned char buf[MAX_TEST_OPT_LENGTH];
        build_opt_rdatalist(ibtest_dnstest_mctx,
                            opt_data, sizeof(opt_data)/sizeof(opt_data[0]),
                            &rdatalist, buf, sizeof(buf));
        ATF_REQUIRE_EQ(dns_rdatalist_tordataset(&rdatalist, &opt), ISC_R_SUCCESS);
        ns_client_t *client = ibtest_ns_client;
        /* Process edns client outgoing view option */
        test_process_edns_client_outgoing_view(client, &opt, ISC_R_SUCCESS);

        free_rdatalist(ibtest_dnstest_mctx, &rdatalist);

        common_teardown();
}

ATF_TC(ns_client_process_optlen_edns_client_outgoing_view);
ATF_TC_HEAD(ns_client_process_optlen_edns_client_outgoing_view, tc) {
        atf_tc_set_md_var(tc, "descr",
                          "Process edns client outgoing view option, different optlen values");
}
ATF_TC_BODY(ns_client_process_optlen_edns_client_outgoing_view, tc) {
        UNUSED(tc);

        common_setup();
        /* optlen =0 */
        const test_rdata_data_t opt_data[] = {
                {
                        "Option contains single edns client outgoing view options:"
                        " Name server edns client outgoing view check failed because"
                        " optlen=0.",
                        {
                                0xFF, 0xF6, 0x00, 0x00,
                        },4
                },
        };

        dns_rdataset_t opt;
        dns_rdataset_init(&opt);

        /* Build edns client outgoing view option */
        dns_rdatalist_t rdatalist;
        dns_rdatalist_init(&rdatalist);
        unsigned char buf[MAX_TEST_OPT_LENGTH];
        build_opt_rdatalist(ibtest_dnstest_mctx,
                            opt_data, sizeof(opt_data)/sizeof(opt_data[0]),
                            &rdatalist, buf, sizeof(buf));
        ATF_REQUIRE_EQ(dns_rdatalist_tordataset(&rdatalist, &opt), ISC_R_SUCCESS);
        ns_client_t *client = ibtest_ns_client;
        /* Process edns client outgoing view option */
        test_process_edns_client_outgoing_view(client, &opt, DNS_R_FORMERR);
        free_rdatalist(ibtest_dnstest_mctx, &rdatalist);

        /* optlen =256 */
         const test_rdata_data_t opt_data1[] ={
                {
                        "Option contains single edns client outgoing view options:"
                        " Name server edns client outgoing view check failed beacuse"
                        " optlen=256.",
                        {
                                0xFF, 0xF6, 0x01, 0x00,
                                0x5F, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                        },260
                },
        };

        memset(buf, 0, sizeof(buf));
        dns_rdataset_init(&opt);
        dns_rdatalist_init(&rdatalist);
        build_opt_rdatalist(ibtest_dnstest_mctx,
                            opt_data1, sizeof(opt_data1)/sizeof(opt_data1[0]),
                            &rdatalist, buf, sizeof(buf));
        ATF_REQUIRE_EQ(dns_rdatalist_tordataset(&rdatalist, &opt), ISC_R_SUCCESS);
        client = ibtest_ns_client;
        test_process_edns_client_outgoing_view(client, &opt, DNS_R_FORMERR);

        free_rdatalist(ibtest_dnstest_mctx, &rdatalist);

        /* optlen =255 */
         const test_rdata_data_t opt_data2[] ={
                {
                        "Option contains single edns client outgoing view options:"
                        " Name server edns client outgoing view check passed.",
                        {
                                0xFF, 0xF6, 0x00, 0xFF,
                                0x5F, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E, 0x61,
                                0x6C, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x6E,
                        },259
                },
        };

        memset(buf, 0, sizeof(buf));
        dns_rdataset_init(&opt);
        dns_rdatalist_init(&rdatalist);
        build_opt_rdatalist(ibtest_dnstest_mctx,
                            opt_data2, sizeof(opt_data2)/sizeof(opt_data2[0]),
                            &rdatalist, buf, sizeof(buf));
        ATF_REQUIRE_EQ(dns_rdatalist_tordataset(&rdatalist, &opt), ISC_R_SUCCESS);
        client = ibtest_ns_client;
        test_process_edns_client_outgoing_view(client, &opt, ISC_R_SUCCESS);

        free_rdatalist(ibtest_dnstest_mctx, &rdatalist);
        common_teardown();
}

/*
 * Main
 */
ATF_TP_ADD_TCS(tp) {
	ATF_TP_ADD_TC(tp, ns_client_process_multiple_edns_client_subnet);
	ATF_TP_ADD_TC(tp, ns_client_process_edns_client_subnet);
	ATF_TP_ADD_TC(tp, ns_client_process_edns_client_subnet_failed);
	ATF_TP_ADD_TC(tp, add_category_opt);
	ATF_TP_ADD_TC(tp, ns_client_process_multiple_edns_client_outgoing_view);
	ATF_TP_ADD_TC(tp, ns_client_process_edns_client_outgoing_view);
	ATF_TP_ADD_TC(tp, ns_client_process_optlen_edns_client_outgoing_view);

	return (atf_no_error());
}
