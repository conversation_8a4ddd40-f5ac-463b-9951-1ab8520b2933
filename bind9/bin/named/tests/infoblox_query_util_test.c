/*
 * Copyright (c) 2013 Infoblox Inc. All Rights Reserved.
 */

#include <config.h>

#include <atf-c.h>

#include <unistd.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <sys/socket.h>

#include <isc/mem.h>
#include <isc/result.h>
#include <isc/sockaddr.h>
#include <isc/util.h>

#include <dns/view.h>

#include <named/client.h>
#include <named/infoblox_query_util_private.h>

#define CHECK(r) \
	do { \
		result = (r); \
		if (result != ISC_R_SUCCESS) \
			goto cleanup; \
	} while (0)

isc_mem_t *mctx = NULL;
isc_sockaddr_t client_sa;

static isc_result_t
create_sockadr(isc_sockaddr_t *sa, const char *addr_txt) {
	struct in_addr ina;	/* placeholder for IPv4 address */
	int pton_result = inet_pton(AF_INET, addr_txt, &ina);
	if (pton_result == 1) {
		isc_sockaddr_fromin(sa, &ina, 53);
		return (ISC_R_SUCCESS);
	}
	fprintf(stderr, "failed to convert IP address: %s\n", addr_txt);
	return (ISC_R_SUCCESS);
}

/*
 * Convenient shortcut to return a C-string stored in isc_buffer.  It assumes
 * the string is not nul-terminated and the buffer has a space to store a nul
 * character.
 */
static const char *
get_buffer_string(isc_buffer_t *buffer) {
	isc_region_t r;
	
	isc_buffer_putuint8(buffer, 0);
	isc_buffer_usedregion(buffer, &r);
	return ((const char *)r.base);
}

/*
 * Individual unit tests
 */

ATF_TC(make_rewrite_label);
ATF_TC_HEAD(make_rewrite_label, tc) {
  atf_tc_set_md_var(tc, "descr", "check query rewrite labels");
}
ATF_TC_BODY(make_rewrite_label, tc) {
	isc_result_t result = ISC_R_SUCCESS;
	isc_buffer_t *label_buf = NULL;

	UNUSED(tc);

	CHECK(isc_mem_create(0, 0, &mctx));

	/* Check a normal case */
	CHECK(create_sockadr(&client_sa, "*********"));
	result = infoblox_make_rewrite_label(mctx, &label_buf, "prefix", 6,
					     &client_sa);
	ATF_REQUIRE_EQ(result, ISC_R_SUCCESS);
	ATF_REQUIRE_STREQ(get_buffer_string(label_buf), "prefix-192-0-2-1");
	isc_buffer_free(&label_buf);

	/* Check with the longest possible (textual) IPv4 address */
	CHECK(create_sockadr(&client_sa, "***************"));
	result = infoblox_make_rewrite_label(mctx, &label_buf, "prefix", 6,
					     &client_sa);
	ATF_REQUIRE_EQ(result, ISC_R_SUCCESS);
	ATF_REQUIRE_STREQ(get_buffer_string(label_buf),
			  "prefix-255-255-255-255");
	isc_buffer_free(&label_buf);

	/*
	 * empty prefix case.  named's configuration parser rejects such case,
	 * so it shouldn't happen when it's called by named.  nevertheless
	 * it wouldn't cause disruption (just producing awkward label), and we
	 * confirm that here.
	 */
	result = infoblox_make_rewrite_label(mctx, &label_buf, NULL, 0,
					     &client_sa);
	ATF_REQUIRE_EQ(result, ISC_R_SUCCESS);
	ATF_REQUIRE_STREQ(get_buffer_string(label_buf), "-255-255-255-255");
	isc_buffer_free(&label_buf);

  cleanup:
	if (mctx != NULL)
		isc_mem_destroy(&mctx);
}

ATF_TC(query_rewrite_nomem);
ATF_TC_HEAD(query_rewrite_nomem, tc) {
  atf_tc_set_md_var(tc, "descr", "emulate short memory situation");
}
ATF_TC_BODY(query_rewrite_nomem, tc) {
	isc_result_t result = ISC_R_SUCCESS;
	isc_buffer_t *label_buf = NULL;

	UNUSED(tc);

	CHECK(isc_mem_create(0, 0, &mctx));

	isc_mem_setquota(mctx, 1); /* severely limit the quota */
	result = infoblox_make_rewrite_label(mctx, &label_buf, "prefix", 6,
					     &client_sa);
	ATF_REQUIRE_EQ(result, ISC_R_NOMEMORY);

	ATF_REQUIRE_EQ(isc_mem_inuse(mctx), 0);

  cleanup:
	if (mctx != NULL)
		isc_mem_destroy(&mctx);
}

/*
 * Main
 */
ATF_TP_ADD_TCS(tp) {
	ATF_TP_ADD_TC(tp, make_rewrite_label);
	ATF_TP_ADD_TC(tp, query_rewrite_nomem);

	return (atf_no_error());
}
