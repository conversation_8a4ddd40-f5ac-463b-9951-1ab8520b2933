.\" Copyright (C) 2004-2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: named.conf
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2017-08-15
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "NAMED\&.CONF" "5" "2017\-08\-15" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
named.conf \- configuration file for \fBnamed\fR
.SH "SYNOPSIS"
.HP \w'\fBnamed\&.conf\fR\ 'u
\fBnamed\&.conf\fR
.SH "DESCRIPTION"
.PP
named\&.conf
is the configuration file for
\fBnamed\fR\&. Statements are enclosed in braces and terminated with a semi\-colon\&. Clauses in the statements are also semi\-colon terminated\&. The usual comment styles are supported:
.PP
C style: /* */
.PP
C++ style: // to end of line
.PP
Unix style: # to end of line
.SH "ACL"
.sp
.if n \{\
.RS 4
.\}
.nf
acl \fIstring\fR { \fIaddress_match_element\fR; \&.\&.\&. };
.fi
.if n \{\
.RE
.\}
.SH "CONTROLS"
.sp
.if n \{\
.RS 4
.\}
.nf
controls {
	inet ( \fIipv4_address\fR | \fIipv6_address\fR |
	    * ) [ port ( \fIinteger\fR | * ) ] allow
	    { \fIaddress_match_element\fR; \&.\&.\&. } [
	    keys { \fIstring\fR; \&.\&.\&. } ] [ read\-only
	    \fIboolean\fR ];
	unix \fIquoted_string\fR perm \fIinteger\fR
	    owner \fIinteger\fR group \fIinteger\fR [
	    keys { \fIstring\fR; \&.\&.\&. } ] [ read\-only
	    \fIboolean\fR ];
};
.fi
.if n \{\
.RE
.\}
.SH "DLZ"
.sp
.if n \{\
.RS 4
.\}
.nf
dlz \fIstring\fR {
	database \fIstring\fR;
	search \fIboolean\fR;
};
.fi
.if n \{\
.RE
.\}
.SH "DYNDB"
.sp
.if n \{\
.RS 4
.\}
.nf
dyndb \fIstring\fR \fIquoted_string\fR {
    \fIunspecified\-text\fR };
.fi
.if n \{\
.RE
.\}
.SH "KEY"
.sp
.if n \{\
.RS 4
.\}
.nf
key \fIstring\fR {
	algorithm \fIstring\fR;
	secret \fIstring\fR;
};
.fi
.if n \{\
.RE
.\}
.SH "LOGGING"
.sp
.if n \{\
.RS 4
.\}
.nf
logging {
	category \fIstring\fR { \fIstring\fR; \&.\&.\&. };
	channel \fIstring\fR {
		buffered \fIboolean\fR;
		file \fIquoted_string\fR [ versions ( "unlimited" | \fIinteger\fR )
		    ] [ size \fIsize\fR ];
		null;
		print\-category \fIboolean\fR;
		print\-severity \fIboolean\fR;
		print\-time \fIboolean\fR;
		severity \fIlog_severity\fR;
		stderr;
		syslog [ \fIsyslog_facility\fR ];
	};
};
.fi
.if n \{\
.RE
.\}
.SH "LWRES"
.sp
.if n \{\
.RS 4
.\}
.nf
lwres {
	listen\-on [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fIipv4_address\fR
	    | \fIipv6_address\fR ) [ port \fIinteger\fR ] [ dscp \fIinteger\fR ]; \&.\&.\&. };
	lwres\-clients \fIinteger\fR;
	lwres\-tasks \fIinteger\fR;
	ndots \fIinteger\fR;
	search { \fIstring\fR; \&.\&.\&. };
	view \fIstring\fR [ \fIclass\fR ];
};
.fi
.if n \{\
.RE
.\}
.SH "MANAGED-KEYS"
.sp
.if n \{\
.RS 4
.\}
.nf
managed\-keys { \fIstring\fR \fIstring\fR \fIinteger\fR
    \fIinteger\fR \fIinteger\fR \fIquoted_string\fR; \&.\&.\&. };
.fi
.if n \{\
.RE
.\}
.SH "MASTERS"
.sp
.if n \{\
.RS 4
.\}
.nf
masters \fIstring\fR [ port \fIinteger\fR ] [ dscp
    \fIinteger\fR ] { ( \fImasters\fR | \fIipv4_address\fR [
    port \fIinteger\fR ] | \fIipv6_address\fR [ port
    \fIinteger\fR ] ) [ key \fIstring\fR ]; \&.\&.\&. };
.fi
.if n \{\
.RE
.\}
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
options {
	acache\-cleaning\-interval \fIinteger\fR;
	acache\-enable \fIboolean\fR;
	additional\-from\-auth \fIboolean\fR;
	additional\-from\-cache \fIboolean\fR;
	allow\-new\-zones \fIboolean\fR;
	allow\-notify { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-query { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-query\-cache { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-query\-cache\-on { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-query\-on { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-recursion { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-recursion\-on { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-transfer { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-update { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-update\-forwarding { \fIaddress_match_element\fR; \&.\&.\&. };
	also\-notify [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fImasters\fR |
	    \fIipv4_address\fR [ port \fIinteger\fR ] | \fIipv6_address\fR [ port
	    \fIinteger\fR ] ) [ key \fIstring\fR ]; \&.\&.\&. };
	alt\-transfer\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * )
	    ] [ dscp \fIinteger\fR ];
	alt\-transfer\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR |
	    * ) ] [ dscp \fIinteger\fR ];
	attach\-cache \fIstring\fR;
	auth\-nxdomain \fIboolean\fR; // default changed
	auto\-dnssec ( allow | maintain | off );
	automatic\-interface\-scan \fIboolean\fR;
	avoid\-v4\-udp\-ports { \fIportrange\fR; \&.\&.\&. };
	avoid\-v6\-udp\-ports { \fIportrange\fR; \&.\&.\&. };
	bindkeys\-file \fIquoted_string\fR;
	blackhole { \fIaddress_match_element\fR; \&.\&.\&. };
	cache\-file \fIquoted_string\fR;
	catalog\-zones { zone \fIquoted_string\fR [ default\-masters [ port
	    \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fImasters\fR | \fIipv4_address\fR [
	    port \fIinteger\fR ] | \fIipv6_address\fR [ port \fIinteger\fR ] ) [ key
	    \fIstring\fR ]; \&.\&.\&. } ] [ zone\-directory \fIquoted_string\fR ] [
	    in\-memory \fIboolean\fR ] [ min\-update\-interval \fIinteger\fR ]; \&.\&.\&. };
	check\-dup\-records ( fail | warn | ignore );
	check\-integrity \fIboolean\fR;
	check\-mx ( fail | warn | ignore );
	check\-mx\-cname ( fail | warn | ignore );
	check\-names ( master | slave | response
	    ) ( fail | warn | ignore );
	check\-sibling \fIboolean\fR;
	check\-spf ( warn | ignore );
	check\-srv\-cname ( fail | warn | ignore );
	check\-wildcard \fIboolean\fR;
	cleaning\-interval \fIinteger\fR;
	clients\-per\-query \fIinteger\fR;
	cookie\-algorithm ( aes | sha1 | sha256 );
	cookie\-secret \fIstring\fR;
	coresize ( default | unlimited | \fIsizeval\fR );
	datasize ( default | unlimited | \fIsizeval\fR );
	deny\-answer\-addresses { \fIaddress_match_element\fR; \&.\&.\&. } [
	    except\-from { \fIquoted_string\fR; \&.\&.\&. } ];
	deny\-answer\-aliases { \fIquoted_string\fR; \&.\&.\&. } [ except\-from {
	    \fIquoted_string\fR; \&.\&.\&. } ];
	dialup ( notify | notify\-passive | passive | refresh | \fIboolean\fR );
	directory \fIquoted_string\fR;
	disable\-algorithms \fIstring\fR { \fIstring\fR;
	    \&.\&.\&. };
	disable\-ds\-digests \fIstring\fR { \fIstring\fR;
	    \&.\&.\&. };
	disable\-empty\-zone \fIstring\fR;
	dns64 \fInetprefix\fR {
		break\-dnssec \fIboolean\fR;
		clients { \fIaddress_match_element\fR; \&.\&.\&. };
		exclude { \fIaddress_match_element\fR; \&.\&.\&. };
		mapped { \fIaddress_match_element\fR; \&.\&.\&. };
		recursive\-only \fIboolean\fR;
		suffix \fIipv6_address\fR;
	};
	dns64\-contact \fIstring\fR;
	dns64\-server \fIstring\fR;
	dnssec\-accept\-expired \fIboolean\fR;
	dnssec\-dnskey\-kskonly \fIboolean\fR;
	dnssec\-enable \fIboolean\fR;
	dnssec\-loadkeys\-interval \fIinteger\fR;
	dnssec\-lookaside ( \fIstring\fR trust\-anchor
	    \fIstring\fR | auto | no );
	dnssec\-must\-be\-secure \fIstring\fR \fIboolean\fR;
	dnssec\-secure\-to\-insecure \fIboolean\fR;
	dnssec\-update\-mode ( maintain | no\-resign );
	dnssec\-validation ( yes | no | auto );
	dnstap { ( all | auth | client | forwarder |
	    resolver ) [ ( query | response ) ]; \&.\&.\&. };
	dnstap\-identity ( \fIquoted_string\fR | none |
	    hostname );
	dnstap\-output ( file | unix ) \fIquoted_string\fR;
	dnstap\-version ( \fIquoted_string\fR | none );
	dscp \fIinteger\fR;
	dual\-stack\-servers [ port \fIinteger\fR ] { ( \fIquoted_string\fR [ port
	    \fIinteger\fR ] [ dscp \fIinteger\fR ] | \fIipv4_address\fR [ port
	    \fIinteger\fR ] [ dscp \fIinteger\fR ] | \fIipv6_address\fR [ port
	    \fIinteger\fR ] [ dscp \fIinteger\fR ] ); \&.\&.\&. };
	dump\-file \fIquoted_string\fR;
	edns\-udp\-size \fIinteger\fR;
	empty\-contact \fIstring\fR;
	empty\-server \fIstring\fR;
	empty\-zones\-enable \fIboolean\fR;
	fetch\-quota\-params \fIinteger\fR \fIfixedpoint\fR \fIfixedpoint\fR \fIfixedpoint\fR;
	fetches\-per\-server \fIinteger\fR [ ( drop | fail ) ];
	fetches\-per\-zone \fIinteger\fR [ ( drop | fail ) ];
	files ( default | unlimited | \fIsizeval\fR );
	filter\-aaaa { \fIaddress_match_element\fR; \&.\&.\&. };
	filter\-aaaa\-on\-v4 ( break\-dnssec | \fIboolean\fR );
	filter\-aaaa\-on\-v6 ( break\-dnssec | \fIboolean\fR );
	flush\-zones\-on\-shutdown \fIboolean\fR;
	forward ( first | only );
	forwarders [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fIipv4_address\fR
	    | \fIipv6_address\fR ) [ port \fIinteger\fR ] [ dscp \fIinteger\fR ]; \&.\&.\&. };
	fstrm\-set\-buffer\-hint \fIinteger\fR;
	fstrm\-set\-flush\-timeout \fIinteger\fR;
	fstrm\-set\-input\-queue\-size \fIinteger\fR;
	fstrm\-set\-output\-notify\-threshold \fIinteger\fR;
	fstrm\-set\-output\-queue\-model ( mpsc | spsc );
	fstrm\-set\-output\-queue\-size \fIinteger\fR;
	fstrm\-set\-reopen\-interval \fIinteger\fR;
	geoip\-directory ( \fIquoted_string\fR | none );
	geoip\-use\-ecs \fIboolean\fR;
	heartbeat\-interval \fIinteger\fR;
	hostname ( \fIquoted_string\fR | none );
	inline\-signing \fIboolean\fR;
	interface\-interval \fIinteger\fR;
	ixfr\-from\-differences ( master | slave | \fIboolean\fR );
	keep\-response\-order { \fIaddress_match_element\fR; \&.\&.\&. };
	key\-directory \fIquoted_string\fR;
	lame\-ttl \fIttlval\fR;
	listen\-on [ port \fIinteger\fR ] [ dscp
	    \fIinteger\fR ] {
	    \fIaddress_match_element\fR; \&.\&.\&. };
	listen\-on\-v6 [ port \fIinteger\fR ] [ dscp
	    \fIinteger\fR ] {
	    \fIaddress_match_element\fR; \&.\&.\&. };
	lmdb\-mapsize \fIsizeval\fR;
	lock\-file ( \fIquoted_string\fR | none );
	managed\-keys\-directory \fIquoted_string\fR;
	masterfile\-format ( map | raw | text );
	masterfile\-style ( full | relative );
	match\-mapped\-addresses \fIboolean\fR;
	max\-acache\-size ( unlimited | \fIsizeval\fR );
	max\-cache\-size ( default | unlimited | \fIsizeval\fR | \fIpercentage\fR );
	max\-cache\-ttl \fIinteger\fR;
	max\-clients\-per\-query \fIinteger\fR;
	max\-journal\-size ( unlimited | \fIsizeval\fR );
	max\-ncache\-ttl \fIinteger\fR;
	max\-records \fIinteger\fR;
	max\-recursion\-depth \fIinteger\fR;
	max\-recursion\-queries \fIinteger\fR;
	max\-refresh\-time \fIinteger\fR;
	max\-retry\-time \fIinteger\fR;
	max\-rsa\-exponent\-size \fIinteger\fR;
	max\-transfer\-idle\-in \fIinteger\fR;
	max\-transfer\-idle\-out \fIinteger\fR;
	max\-transfer\-time\-in \fIinteger\fR;
	max\-transfer\-time\-out \fIinteger\fR;
	max\-udp\-size \fIinteger\fR;
	max\-zone\-ttl ( unlimited | \fIttlval\fR );
	memstatistics \fIboolean\fR;
	memstatistics\-file \fIquoted_string\fR;
	message\-compression \fIboolean\fR;
	min\-refresh\-time \fIinteger\fR;
	min\-retry\-time \fIinteger\fR;
	minimal\-any \fIboolean\fR;
	minimal\-responses ( no\-auth | no\-auth\-recursive | \fIboolean\fR );
	multi\-master \fIboolean\fR;
	no\-case\-compress { \fIaddress_match_element\fR; \&.\&.\&. };
	nocookie\-udp\-size \fIinteger\fR;
	notify ( explicit | master\-only | \fIboolean\fR );
	notify\-delay \fIinteger\fR;
	notify\-rate \fIinteger\fR;
	notify\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * ) ] [
	    dscp \fIinteger\fR ];
	notify\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR | * ) ]
	    [ dscp \fIinteger\fR ];
	notify\-to\-soa \fIboolean\fR;
	nta\-lifetime \fIttlval\fR;
	nta\-recheck \fIttlval\fR;
	nxdomain\-redirect \fIstring\fR;
	pid\-file ( \fIquoted_string\fR | none );
	port \fIinteger\fR;
	preferred\-glue \fIstring\fR;
	prefetch \fIinteger\fR [ \fIinteger\fR ];
	provide\-ixfr \fIboolean\fR;
	query\-source ( ( [ address ] ( \fIipv4_address\fR | * ) [ port (
	    \fIinteger\fR | * ) ] ) | ( [ [ address ] ( \fIipv4_address\fR | * ) ]
	    port ( \fIinteger\fR | * ) ) ) [ dscp \fIinteger\fR ];
	query\-source\-v6 ( ( [ address ] ( \fIipv6_address\fR | * ) [ port (
	    \fIinteger\fR | * ) ] ) | ( [ [ address ] ( \fIipv6_address\fR | * ) ]
	    port ( \fIinteger\fR | * ) ) ) [ dscp \fIinteger\fR ];
	querylog \fIboolean\fR;
	random\-device \fIquoted_string\fR;
	rate\-limit {
		all\-per\-second \fIinteger\fR;
		errors\-per\-second \fIinteger\fR;
		exempt\-clients { \fIaddress_match_element\fR; \&.\&.\&. };
		ipv4\-prefix\-length \fIinteger\fR;
		ipv6\-prefix\-length \fIinteger\fR;
		log\-only \fIboolean\fR;
		max\-table\-size \fIinteger\fR;
		min\-table\-size \fIinteger\fR;
		nodata\-per\-second \fIinteger\fR;
		nxdomains\-per\-second \fIinteger\fR;
		qps\-scale \fIinteger\fR;
		referrals\-per\-second \fIinteger\fR;
		responses\-per\-second \fIinteger\fR;
		slip \fIinteger\fR;
		window \fIinteger\fR;
	};
	recursing\-file \fIquoted_string\fR;
	recursion \fIboolean\fR;
	recursive\-clients \fIinteger\fR;
	request\-expire \fIboolean\fR;
	request\-ixfr \fIboolean\fR;
	request\-nsid \fIboolean\fR;
	require\-server\-cookie \fIboolean\fR;
	reserved\-sockets \fIinteger\fR;
	resolver\-query\-timeout \fIinteger\fR;
	response\-policy { zone \fIquoted_string\fR [ log \fIboolean\fR ] [
	    max\-policy\-ttl \fIinteger\fR ] [ policy ( cname | disabled | drop |
	    given | no\-op | nodata | nxdomain | passthru | tcp\-only
	    \fIquoted_string\fR ) ] [ recursive\-only \fIboolean\fR ]; \&.\&.\&. } [
	    break\-dnssec \fIboolean\fR ] [ max\-policy\-ttl \fIinteger\fR ] [
	    min\-ns\-dots \fIinteger\fR ] [ nsip\-wait\-recurse \fIboolean\fR ] [
	    qname\-wait\-recurse \fIboolean\fR ] [ recursive\-only \fIboolean\fR ];
	root\-delegation\-only [ exclude { \fIquoted_string\fR; \&.\&.\&. } ];
	rrset\-order { [ class \fIstring\fR ] [ type \fIstring\fR ] [ name
	    \fIquoted_string\fR ] \fIstring\fR \fIstring\fR; \&.\&.\&. };
	secroots\-file \fIquoted_string\fR;
	send\-cookie \fIboolean\fR;
	serial\-query\-rate \fIinteger\fR;
	serial\-update\-method ( date | increment | unixtime );
	server\-id ( \fIquoted_string\fR | none | hostname );
	servfail\-ttl \fIttlval\fR;
	session\-keyalg \fIstring\fR;
	session\-keyfile ( \fIquoted_string\fR | none );
	session\-keyname \fIstring\fR;
	sig\-signing\-nodes \fIinteger\fR;
	sig\-signing\-signatures \fIinteger\fR;
	sig\-signing\-type \fIinteger\fR;
	sig\-validity\-interval \fIinteger\fR [ \fIinteger\fR ];
	sortlist { \fIaddress_match_element\fR; \&.\&.\&. };
	stacksize ( default | unlimited | \fIsizeval\fR );
	startup\-notify\-rate \fIinteger\fR;
	statistics\-file \fIquoted_string\fR;
	tcp\-clients \fIinteger\fR;
	tcp\-listen\-queue \fIinteger\fR;
	tkey\-dhkey \fIquoted_string\fR \fIinteger\fR;
	tkey\-domain \fIquoted_string\fR;
	tkey\-gssapi\-credential \fIquoted_string\fR;
	tkey\-gssapi\-keytab \fIquoted_string\fR;
	transfer\-format ( many\-answers | one\-answer );
	transfer\-message\-size \fIinteger\fR;
	transfer\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * ) ] [
	    dscp \fIinteger\fR ];
	transfer\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR | * )
	    ] [ dscp \fIinteger\fR ];
	transfers\-in \fIinteger\fR;
	transfers\-out \fIinteger\fR;
	transfers\-per\-ns \fIinteger\fR;
	trust\-anchor\-telemetry \fIboolean\fR; // experimental
	try\-tcp\-refresh \fIboolean\fR;
	update\-check\-ksk \fIboolean\fR;
	use\-alt\-transfer\-source \fIboolean\fR;
	use\-v4\-udp\-ports { \fIportrange\fR; \&.\&.\&. };
	use\-v6\-udp\-ports { \fIportrange\fR; \&.\&.\&. };
	v6\-bias \fIinteger\fR;
	version ( \fIquoted_string\fR | none );
	zero\-no\-soa\-ttl \fIboolean\fR;
	zero\-no\-soa\-ttl\-cache \fIboolean\fR;
	zone\-statistics ( full | terse | none | \fIboolean\fR );
};
.fi
.if n \{\
.RE
.\}
.SH "SERVER"
.sp
.if n \{\
.RS 4
.\}
.nf
server \fInetprefix\fR {
	bogus \fIboolean\fR;
	edns \fIboolean\fR;
	edns\-udp\-size \fIinteger\fR;
	edns\-version \fIinteger\fR;
	keys \fIserver_key\fR;
	max\-udp\-size \fIinteger\fR;
	notify\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * ) ] [
	    dscp \fIinteger\fR ];
	notify\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR | * ) ]
	    [ dscp \fIinteger\fR ];
	provide\-ixfr \fIboolean\fR;
	query\-source ( ( [ address ] ( \fIipv4_address\fR | * ) [ port (
	    \fIinteger\fR | * ) ] ) | ( [ [ address ] ( \fIipv4_address\fR | * ) ]
	    port ( \fIinteger\fR | * ) ) ) [ dscp \fIinteger\fR ];
	query\-source\-v6 ( ( [ address ] ( \fIipv6_address\fR | * ) [ port (
	    \fIinteger\fR | * ) ] ) | ( [ [ address ] ( \fIipv6_address\fR | * ) ]
	    port ( \fIinteger\fR | * ) ) ) [ dscp \fIinteger\fR ];
	request\-expire \fIboolean\fR;
	request\-ixfr \fIboolean\fR;
	request\-nsid \fIboolean\fR;
	send\-cookie \fIboolean\fR;
	tcp\-only \fIboolean\fR;
	transfer\-format ( many\-answers | one\-answer );
	transfer\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * ) ] [
	    dscp \fIinteger\fR ];
	transfer\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR | * )
	    ] [ dscp \fIinteger\fR ];
	transfers \fIinteger\fR;
};
.fi
.if n \{\
.RE
.\}
.SH "STATISTICS-CHANNELS"
.sp
.if n \{\
.RS 4
.\}
.nf
statistics\-channels {
	inet ( \fIipv4_address\fR | \fIipv6_address\fR |
	    * ) [ port ( \fIinteger\fR | * ) ] [
	    allow { \fIaddress_match_element\fR; \&.\&.\&.
	    } ];
};
.fi
.if n \{\
.RE
.\}
.SH "TRUSTED-KEYS"
.sp
.if n \{\
.RS 4
.\}
.nf
trusted\-keys { \fIstring\fR \fIinteger\fR \fIinteger\fR
    \fIinteger\fR \fIquoted_string\fR; \&.\&.\&. };
.fi
.if n \{\
.RE
.\}
.SH "VIEW"
.sp
.if n \{\
.RS 4
.\}
.nf
view \fIstring\fR [ \fIclass\fR ] {
	acache\-cleaning\-interval \fIinteger\fR;
	acache\-enable \fIboolean\fR;
	additional\-from\-auth \fIboolean\fR;
	additional\-from\-cache \fIboolean\fR;
	allow\-new\-zones \fIboolean\fR;
	allow\-notify { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-query { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-query\-cache { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-query\-cache\-on { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-query\-on { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-recursion { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-recursion\-on { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-transfer { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-update { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-update\-forwarding { \fIaddress_match_element\fR; \&.\&.\&. };
	also\-notify [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fImasters\fR |
	    \fIipv4_address\fR [ port \fIinteger\fR ] | \fIipv6_address\fR [ port
	    \fIinteger\fR ] ) [ key \fIstring\fR ]; \&.\&.\&. };
	alt\-transfer\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * )
	    ] [ dscp \fIinteger\fR ];
	alt\-transfer\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR |
	    * ) ] [ dscp \fIinteger\fR ];
	attach\-cache \fIstring\fR;
	auth\-nxdomain \fIboolean\fR; // default changed
	auto\-dnssec ( allow | maintain | off );
	cache\-file \fIquoted_string\fR;
	catalog\-zones { zone \fIquoted_string\fR [ default\-masters [ port
	    \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fImasters\fR | \fIipv4_address\fR [
	    port \fIinteger\fR ] | \fIipv6_address\fR [ port \fIinteger\fR ] ) [ key
	    \fIstring\fR ]; \&.\&.\&. } ] [ zone\-directory \fIquoted_string\fR ] [
	    in\-memory \fIboolean\fR ] [ min\-update\-interval \fIinteger\fR ]; \&.\&.\&. };
	check\-dup\-records ( fail | warn | ignore );
	check\-integrity \fIboolean\fR;
	check\-mx ( fail | warn | ignore );
	check\-mx\-cname ( fail | warn | ignore );
	check\-names ( master | slave | response
	    ) ( fail | warn | ignore );
	check\-sibling \fIboolean\fR;
	check\-spf ( warn | ignore );
	check\-srv\-cname ( fail | warn | ignore );
	check\-wildcard \fIboolean\fR;
	cleaning\-interval \fIinteger\fR;
	clients\-per\-query \fIinteger\fR;
	deny\-answer\-addresses { \fIaddress_match_element\fR; \&.\&.\&. } [
	    except\-from { \fIquoted_string\fR; \&.\&.\&. } ];
	deny\-answer\-aliases { \fIquoted_string\fR; \&.\&.\&. } [ except\-from {
	    \fIquoted_string\fR; \&.\&.\&. } ];
	dialup ( notify | notify\-passive | passive | refresh | \fIboolean\fR );
	disable\-algorithms \fIstring\fR { \fIstring\fR;
	    \&.\&.\&. };
	disable\-ds\-digests \fIstring\fR { \fIstring\fR;
	    \&.\&.\&. };
	disable\-empty\-zone \fIstring\fR;
	dlz \fIstring\fR {
		database \fIstring\fR;
		search \fIboolean\fR;
	};
	dns64 \fInetprefix\fR {
		break\-dnssec \fIboolean\fR;
		clients { \fIaddress_match_element\fR; \&.\&.\&. };
		exclude { \fIaddress_match_element\fR; \&.\&.\&. };
		mapped { \fIaddress_match_element\fR; \&.\&.\&. };
		recursive\-only \fIboolean\fR;
		suffix \fIipv6_address\fR;
	};
	dns64\-contact \fIstring\fR;
	dns64\-server \fIstring\fR;
	dnssec\-accept\-expired \fIboolean\fR;
	dnssec\-dnskey\-kskonly \fIboolean\fR;
	dnssec\-enable \fIboolean\fR;
	dnssec\-loadkeys\-interval \fIinteger\fR;
	dnssec\-lookaside ( \fIstring\fR trust\-anchor
	    \fIstring\fR | auto | no );
	dnssec\-must\-be\-secure \fIstring\fR \fIboolean\fR;
	dnssec\-secure\-to\-insecure \fIboolean\fR;
	dnssec\-update\-mode ( maintain | no\-resign );
	dnssec\-validation ( yes | no | auto );
	dnstap { ( all | auth | client | forwarder |
	    resolver ) [ ( query | response ) ]; \&.\&.\&. };
	dual\-stack\-servers [ port \fIinteger\fR ] { ( \fIquoted_string\fR [ port
	    \fIinteger\fR ] [ dscp \fIinteger\fR ] | \fIipv4_address\fR [ port
	    \fIinteger\fR ] [ dscp \fIinteger\fR ] | \fIipv6_address\fR [ port
	    \fIinteger\fR ] [ dscp \fIinteger\fR ] ); \&.\&.\&. };
	dyndb \fIstring\fR \fIquoted_string\fR {
	    \fIunspecified\-text\fR };
	edns\-udp\-size \fIinteger\fR;
	empty\-contact \fIstring\fR;
	empty\-server \fIstring\fR;
	empty\-zones\-enable \fIboolean\fR;
	fetch\-quota\-params \fIinteger\fR \fIfixedpoint\fR \fIfixedpoint\fR \fIfixedpoint\fR;
	fetches\-per\-server \fIinteger\fR [ ( drop | fail ) ];
	fetches\-per\-zone \fIinteger\fR [ ( drop | fail ) ];
	filter\-aaaa { \fIaddress_match_element\fR; \&.\&.\&. };
	filter\-aaaa\-on\-v4 ( break\-dnssec | \fIboolean\fR );
	filter\-aaaa\-on\-v6 ( break\-dnssec | \fIboolean\fR );
	forward ( first | only );
	forwarders [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fIipv4_address\fR
	    | \fIipv6_address\fR ) [ port \fIinteger\fR ] [ dscp \fIinteger\fR ]; \&.\&.\&. };
	inline\-signing \fIboolean\fR;
	ixfr\-from\-differences ( master | slave | \fIboolean\fR );
	key \fIstring\fR {
		algorithm \fIstring\fR;
		secret \fIstring\fR;
	};
	key\-directory \fIquoted_string\fR;
	lame\-ttl \fIttlval\fR;
	lmdb\-mapsize \fIsizeval\fR;
	managed\-keys { \fIstring\fR \fIstring\fR
	    \fIinteger\fR \fIinteger\fR \fIinteger\fR
	    \fIquoted_string\fR; \&.\&.\&. };
	masterfile\-format ( map | raw | text );
	masterfile\-style ( full | relative );
	match\-clients { \fIaddress_match_element\fR; \&.\&.\&. };
	match\-destinations { \fIaddress_match_element\fR; \&.\&.\&. };
	match\-recursive\-only \fIboolean\fR;
	max\-acache\-size ( unlimited | \fIsizeval\fR );
	max\-cache\-size ( default | unlimited | \fIsizeval\fR | \fIpercentage\fR );
	max\-cache\-ttl \fIinteger\fR;
	max\-clients\-per\-query \fIinteger\fR;
	max\-journal\-size ( unlimited | \fIsizeval\fR );
	max\-ncache\-ttl \fIinteger\fR;
	max\-records \fIinteger\fR;
	max\-recursion\-depth \fIinteger\fR;
	max\-recursion\-queries \fIinteger\fR;
	max\-refresh\-time \fIinteger\fR;
	max\-retry\-time \fIinteger\fR;
	max\-transfer\-idle\-in \fIinteger\fR;
	max\-transfer\-idle\-out \fIinteger\fR;
	max\-transfer\-time\-in \fIinteger\fR;
	max\-transfer\-time\-out \fIinteger\fR;
	max\-udp\-size \fIinteger\fR;
	max\-zone\-ttl ( unlimited | \fIttlval\fR );
	message\-compression \fIboolean\fR;
	min\-refresh\-time \fIinteger\fR;
	min\-retry\-time \fIinteger\fR;
	minimal\-any \fIboolean\fR;
	minimal\-responses ( no\-auth | no\-auth\-recursive | \fIboolean\fR );
	multi\-master \fIboolean\fR;
	no\-case\-compress { \fIaddress_match_element\fR; \&.\&.\&. };
	nocookie\-udp\-size \fIinteger\fR;
	notify ( explicit | master\-only | \fIboolean\fR );
	notify\-delay \fIinteger\fR;
	notify\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * ) ] [
	    dscp \fIinteger\fR ];
	notify\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR | * ) ]
	    [ dscp \fIinteger\fR ];
	notify\-to\-soa \fIboolean\fR;
	nta\-lifetime \fIttlval\fR;
	nta\-recheck \fIttlval\fR;
	nxdomain\-redirect \fIstring\fR;
	preferred\-glue \fIstring\fR;
	prefetch \fIinteger\fR [ \fIinteger\fR ];
	provide\-ixfr \fIboolean\fR;
	query\-source ( ( [ address ] ( \fIipv4_address\fR | * ) [ port (
	    \fIinteger\fR | * ) ] ) | ( [ [ address ] ( \fIipv4_address\fR | * ) ]
	    port ( \fIinteger\fR | * ) ) ) [ dscp \fIinteger\fR ];
	query\-source\-v6 ( ( [ address ] ( \fIipv6_address\fR | * ) [ port (
	    \fIinteger\fR | * ) ] ) | ( [ [ address ] ( \fIipv6_address\fR | * ) ]
	    port ( \fIinteger\fR | * ) ) ) [ dscp \fIinteger\fR ];
	rate\-limit {
		all\-per\-second \fIinteger\fR;
		errors\-per\-second \fIinteger\fR;
		exempt\-clients { \fIaddress_match_element\fR; \&.\&.\&. };
		ipv4\-prefix\-length \fIinteger\fR;
		ipv6\-prefix\-length \fIinteger\fR;
		log\-only \fIboolean\fR;
		max\-table\-size \fIinteger\fR;
		min\-table\-size \fIinteger\fR;
		nodata\-per\-second \fIinteger\fR;
		nxdomains\-per\-second \fIinteger\fR;
		qps\-scale \fIinteger\fR;
		referrals\-per\-second \fIinteger\fR;
		responses\-per\-second \fIinteger\fR;
		slip \fIinteger\fR;
		window \fIinteger\fR;
	};
	recursion \fIboolean\fR;
	request\-expire \fIboolean\fR;
	request\-ixfr \fIboolean\fR;
	request\-nsid \fIboolean\fR;
	require\-server\-cookie \fIboolean\fR;
	resolver\-query\-timeout \fIinteger\fR;
	response\-policy { zone \fIquoted_string\fR [ log \fIboolean\fR ] [
	    max\-policy\-ttl \fIinteger\fR ] [ policy ( cname | disabled | drop |
	    given | no\-op | nodata | nxdomain | passthru | tcp\-only
	    \fIquoted_string\fR ) ] [ recursive\-only \fIboolean\fR ]; \&.\&.\&. } [
	    break\-dnssec \fIboolean\fR ] [ max\-policy\-ttl \fIinteger\fR ] [
	    min\-ns\-dots \fIinteger\fR ] [ nsip\-wait\-recurse \fIboolean\fR ] [
	    qname\-wait\-recurse \fIboolean\fR ] [ recursive\-only \fIboolean\fR ];
	root\-delegation\-only [ exclude { \fIquoted_string\fR; \&.\&.\&. } ];
	rrset\-order { [ class \fIstring\fR ] [ type \fIstring\fR ] [ name
	    \fIquoted_string\fR ] \fIstring\fR \fIstring\fR; \&.\&.\&. };
	send\-cookie \fIboolean\fR;
	serial\-update\-method ( date | increment | unixtime );
	server \fInetprefix\fR {
		bogus \fIboolean\fR;
		edns \fIboolean\fR;
		edns\-udp\-size \fIinteger\fR;
		edns\-version \fIinteger\fR;
		keys \fIserver_key\fR;
		max\-udp\-size \fIinteger\fR;
		notify\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | *
		    ) ] [ dscp \fIinteger\fR ];
		notify\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR
		    | * ) ] [ dscp \fIinteger\fR ];
		provide\-ixfr \fIboolean\fR;
		query\-source ( ( [ address ] ( \fIipv4_address\fR | * ) [ port
		    ( \fIinteger\fR | * ) ] ) | ( [ [ address ] (
		    \fIipv4_address\fR | * ) ] port ( \fIinteger\fR | * ) ) ) [
		    dscp \fIinteger\fR ];
		query\-source\-v6 ( ( [ address ] ( \fIipv6_address\fR | * ) [
		    port ( \fIinteger\fR | * ) ] ) | ( [ [ address ] (
		    \fIipv6_address\fR | * ) ] port ( \fIinteger\fR | * ) ) ) [
		    dscp \fIinteger\fR ];
		request\-expire \fIboolean\fR;
		request\-ixfr \fIboolean\fR;
		request\-nsid \fIboolean\fR;
		send\-cookie \fIboolean\fR;
		tcp\-only \fIboolean\fR;
		transfer\-format ( many\-answers | one\-answer );
		transfer\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR |
		    * ) ] [ dscp \fIinteger\fR ];
		transfer\-source\-v6 ( \fIipv6_address\fR | * ) [ port (
		    \fIinteger\fR | * ) ] [ dscp \fIinteger\fR ];
		transfers \fIinteger\fR;
	};
	servfail\-ttl \fIttlval\fR;
	sig\-signing\-nodes \fIinteger\fR;
	sig\-signing\-signatures \fIinteger\fR;
	sig\-signing\-type \fIinteger\fR;
	sig\-validity\-interval \fIinteger\fR [ \fIinteger\fR ];
	sortlist { \fIaddress_match_element\fR; \&.\&.\&. };
	transfer\-format ( many\-answers | one\-answer );
	transfer\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * ) ] [
	    dscp \fIinteger\fR ];
	transfer\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR | * )
	    ] [ dscp \fIinteger\fR ];
	trust\-anchor\-telemetry \fIboolean\fR; // experimental
	trusted\-keys { \fIstring\fR \fIinteger\fR
	    \fIinteger\fR \fIinteger\fR \fIquoted_string\fR;
	    \&.\&.\&. };
	try\-tcp\-refresh \fIboolean\fR;
	update\-check\-ksk \fIboolean\fR;
	use\-alt\-transfer\-source \fIboolean\fR;
	v6\-bias \fIinteger\fR;
	zero\-no\-soa\-ttl \fIboolean\fR;
	zero\-no\-soa\-ttl\-cache \fIboolean\fR;
	zone \fIstring\fR [ \fIclass\fR ] {
		allow\-notify { \fIaddress_match_element\fR; \&.\&.\&. };
		allow\-query { \fIaddress_match_element\fR; \&.\&.\&. };
		allow\-query\-on { \fIaddress_match_element\fR; \&.\&.\&. };
		allow\-transfer { \fIaddress_match_element\fR; \&.\&.\&. };
		allow\-update { \fIaddress_match_element\fR; \&.\&.\&. };
		allow\-update\-forwarding { \fIaddress_match_element\fR; \&.\&.\&. };
		also\-notify [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { (
		    \fImasters\fR | \fIipv4_address\fR [ port \fIinteger\fR ] |
		    \fIipv6_address\fR [ port \fIinteger\fR ] ) [ key \fIstring\fR ];
		    \&.\&.\&. };
		alt\-transfer\-source ( \fIipv4_address\fR | * ) [ port (
		    \fIinteger\fR | * ) ] [ dscp \fIinteger\fR ];
		alt\-transfer\-source\-v6 ( \fIipv6_address\fR | * ) [ port (
		    \fIinteger\fR | * ) ] [ dscp \fIinteger\fR ];
		auto\-dnssec ( allow | maintain | off );
		check\-dup\-records ( fail | warn | ignore );
		check\-integrity \fIboolean\fR;
		check\-mx ( fail | warn | ignore );
		check\-mx\-cname ( fail | warn | ignore );
		check\-names ( fail | warn | ignore );
		check\-sibling \fIboolean\fR;
		check\-spf ( warn | ignore );
		check\-srv\-cname ( fail | warn | ignore );
		check\-wildcard \fIboolean\fR;
		database \fIstring\fR;
		delegation\-only \fIboolean\fR;
		dialup ( notify | notify\-passive | passive | refresh |
		    \fIboolean\fR );
		dlz \fIstring\fR;
		dnssec\-dnskey\-kskonly \fIboolean\fR;
		dnssec\-loadkeys\-interval \fIinteger\fR;
		dnssec\-secure\-to\-insecure \fIboolean\fR;
		dnssec\-update\-mode ( maintain | no\-resign );
		file \fIquoted_string\fR;
		forward ( first | only );
		forwarders [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { (
		    \fIipv4_address\fR | \fIipv6_address\fR ) [ port \fIinteger\fR ] [
		    dscp \fIinteger\fR ]; \&.\&.\&. };
		in\-view \fIstring\fR;
		inline\-signing \fIboolean\fR;
		ixfr\-from\-differences \fIboolean\fR;
		journal \fIquoted_string\fR;
		key\-directory \fIquoted_string\fR;
		masterfile\-format ( map | raw | text );
		masterfile\-style ( full | relative );
		masters [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fImasters\fR
		    | \fIipv4_address\fR [ port \fIinteger\fR ] | \fIipv6_address\fR [
		    port \fIinteger\fR ] ) [ key \fIstring\fR ]; \&.\&.\&. };
		max\-ixfr\-log\-size ( default | unlimited |
		max\-journal\-size ( unlimited | \fIsizeval\fR );
		max\-records \fIinteger\fR;
		max\-refresh\-time \fIinteger\fR;
		max\-retry\-time \fIinteger\fR;
		max\-transfer\-idle\-in \fIinteger\fR;
		max\-transfer\-idle\-out \fIinteger\fR;
		max\-transfer\-time\-in \fIinteger\fR;
		max\-transfer\-time\-out \fIinteger\fR;
		max\-zone\-ttl ( unlimited | \fIttlval\fR );
		min\-refresh\-time \fIinteger\fR;
		min\-retry\-time \fIinteger\fR;
		multi\-master \fIboolean\fR;
		notify ( explicit | master\-only | \fIboolean\fR );
		notify\-delay \fIinteger\fR;
		notify\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | *
		    ) ] [ dscp \fIinteger\fR ];
		notify\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR
		    | * ) ] [ dscp \fIinteger\fR ];
		notify\-to\-soa \fIboolean\fR;
		pubkey \fIinteger\fR
		    \fIinteger\fR
		    \fIinteger\fR
		request\-expire \fIboolean\fR;
		request\-ixfr \fIboolean\fR;
		serial\-update\-method ( date | increment | unixtime );
		server\-addresses { ( \fIipv4_address\fR | \fIipv6_address\fR ) [
		    port \fIinteger\fR ]; \&.\&.\&. };
		server\-names { \fIquoted_string\fR; \&.\&.\&. };
		sig\-signing\-nodes \fIinteger\fR;
		sig\-signing\-signatures \fIinteger\fR;
		sig\-signing\-type \fIinteger\fR;
		sig\-validity\-interval \fIinteger\fR [ \fIinteger\fR ];
		transfer\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR |
		    * ) ] [ dscp \fIinteger\fR ];
		transfer\-source\-v6 ( \fIipv6_address\fR | * ) [ port (
		    \fIinteger\fR | * ) ] [ dscp \fIinteger\fR ];
		try\-tcp\-refresh \fIboolean\fR;
		type ( delegation\-only | forward | hint | master | redirect
		    | slave | static\-stub | stub );
		update\-check\-ksk \fIboolean\fR;
		update\-policy ( local | { ( deny | grant ) \fIstring\fR (
		    6to4\-self | external | krb5\-self | krb5\-subdomain |
		    ms\-self | ms\-subdomain | name | self | selfsub |
		    selfwild | subdomain | tcp\-self | wildcard | zonesub )
		    [ \fIstring\fR ] \fIrrtypelist\fR; \&.\&.\&. };
		use\-alt\-transfer\-source \fIboolean\fR;
		zero\-no\-soa\-ttl \fIboolean\fR;
		zone\-statistics ( full | terse | none | \fIboolean\fR );
	};
	zone\-statistics ( full | terse | none | \fIboolean\fR );
};
.fi
.if n \{\
.RE
.\}
.SH "ZONE"
.sp
.if n \{\
.RS 4
.\}
.nf
zone \fIstring\fR [ \fIclass\fR ] {
	allow\-notify { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-query { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-query\-on { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-transfer { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-update { \fIaddress_match_element\fR; \&.\&.\&. };
	allow\-update\-forwarding { \fIaddress_match_element\fR; \&.\&.\&. };
	also\-notify [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fImasters\fR |
	    \fIipv4_address\fR [ port \fIinteger\fR ] | \fIipv6_address\fR [ port
	    \fIinteger\fR ] ) [ key \fIstring\fR ]; \&.\&.\&. };
	alt\-transfer\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * )
	    ] [ dscp \fIinteger\fR ];
	alt\-transfer\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR |
	    * ) ] [ dscp \fIinteger\fR ];
	auto\-dnssec ( allow | maintain | off );
	check\-dup\-records ( fail | warn | ignore );
	check\-integrity \fIboolean\fR;
	check\-mx ( fail | warn | ignore );
	check\-mx\-cname ( fail | warn | ignore );
	check\-names ( fail | warn | ignore );
	check\-sibling \fIboolean\fR;
	check\-spf ( warn | ignore );
	check\-srv\-cname ( fail | warn | ignore );
	check\-wildcard \fIboolean\fR;
	database \fIstring\fR;
	delegation\-only \fIboolean\fR;
	dialup ( notify | notify\-passive | passive | refresh | \fIboolean\fR );
	dlz \fIstring\fR;
	dnssec\-dnskey\-kskonly \fIboolean\fR;
	dnssec\-loadkeys\-interval \fIinteger\fR;
	dnssec\-secure\-to\-insecure \fIboolean\fR;
	dnssec\-update\-mode ( maintain | no\-resign );
	file \fIquoted_string\fR;
	forward ( first | only );
	forwarders [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fIipv4_address\fR
	    | \fIipv6_address\fR ) [ port \fIinteger\fR ] [ dscp \fIinteger\fR ]; \&.\&.\&. };
	in\-view \fIstring\fR;
	inline\-signing \fIboolean\fR;
	ixfr\-from\-differences \fIboolean\fR;
	journal \fIquoted_string\fR;
	key\-directory \fIquoted_string\fR;
	masterfile\-format ( map | raw | text );
	masterfile\-style ( full | relative );
	masters [ port \fIinteger\fR ] [ dscp \fIinteger\fR ] { ( \fImasters\fR |
	    \fIipv4_address\fR [ port \fIinteger\fR ] | \fIipv6_address\fR [ port
	    \fIinteger\fR ] ) [ key \fIstring\fR ]; \&.\&.\&. };
	max\-journal\-size ( unlimited | \fIsizeval\fR );
	max\-records \fIinteger\fR;
	max\-refresh\-time \fIinteger\fR;
	max\-retry\-time \fIinteger\fR;
	max\-transfer\-idle\-in \fIinteger\fR;
	max\-transfer\-idle\-out \fIinteger\fR;
	max\-transfer\-time\-in \fIinteger\fR;
	max\-transfer\-time\-out \fIinteger\fR;
	max\-zone\-ttl ( unlimited | \fIttlval\fR );
	min\-refresh\-time \fIinteger\fR;
	min\-retry\-time \fIinteger\fR;
	multi\-master \fIboolean\fR;
	notify ( explicit | master\-only | \fIboolean\fR );
	notify\-delay \fIinteger\fR;
	notify\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * ) ] [
	    dscp \fIinteger\fR ];
	notify\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR | * ) ]
	    [ dscp \fIinteger\fR ];
	notify\-to\-soa \fIboolean\fR;
	pubkey \fIinteger\fR \fIinteger\fR
	request\-expire \fIboolean\fR;
	request\-ixfr \fIboolean\fR;
	serial\-update\-method ( date | increment | unixtime );
	server\-addresses { ( \fIipv4_address\fR | \fIipv6_address\fR ) [ port
	    \fIinteger\fR ]; \&.\&.\&. };
	server\-names { \fIquoted_string\fR; \&.\&.\&. };
	sig\-signing\-nodes \fIinteger\fR;
	sig\-signing\-signatures \fIinteger\fR;
	sig\-signing\-type \fIinteger\fR;
	sig\-validity\-interval \fIinteger\fR [ \fIinteger\fR ];
	transfer\-source ( \fIipv4_address\fR | * ) [ port ( \fIinteger\fR | * ) ] [
	    dscp \fIinteger\fR ];
	transfer\-source\-v6 ( \fIipv6_address\fR | * ) [ port ( \fIinteger\fR | * )
	    ] [ dscp \fIinteger\fR ];
	try\-tcp\-refresh \fIboolean\fR;
	type ( delegation\-only | forward | hint | master | redirect | slave
	    | static\-stub | stub );
	update\-check\-ksk \fIboolean\fR;
	update\-policy ( local | { ( deny | grant ) \fIstring\fR ( 6to4\-self |
	    external | krb5\-self | krb5\-subdomain | ms\-self | ms\-subdomain
	    | name | self | selfsub | selfwild | subdomain | tcp\-self |
	    wildcard | zonesub ) [ \fIstring\fR ] \fIrrtypelist\fR; \&.\&.\&. };
	use\-alt\-transfer\-source \fIboolean\fR;
	zero\-no\-soa\-ttl \fIboolean\fR;
	zone\-statistics ( full | terse | none | \fIboolean\fR );
};
.fi
.if n \{\
.RE
.\}
.SH "FILES"
.PP
/etc/named\&.conf
.SH "SEE ALSO"
.PP
\fBddns-confgen\fR(8),
\fBnamed\fR(8),
\fBnamed-checkconf\fR(8),
\fBrndc\fR(8),
\fBrndc-confgen\fR(8),
BIND 9 Administrator Reference Manual\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2004-2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
.br
