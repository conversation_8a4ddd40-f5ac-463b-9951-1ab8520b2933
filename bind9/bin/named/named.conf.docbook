<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Generated by doc/misc/docbook-options.pl -->

<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.named.conf">
  <info>
    <date>2017-08-15</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><filename>named.conf</filename></refentrytitle>
    <manvolnum>5</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><filename>named.conf</filename></refname>
    <refpurpose>configuration file for <command>named</command></refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2004</year>
      <year>2005</year>
      <year>2006</year>
      <year>2007</year>
      <year>2008</year>
      <year>2009</year>
      <year>2010</year>
      <year>2011</year>
      <year>2013</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2017</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>named.conf</command>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><filename>named.conf</filename> is the configuration file
      for
      <command>named</command>.  Statements are enclosed
      in braces and terminated with a semi-colon.  Clauses in
      the statements are also semi-colon terminated.  The usual
      comment styles are supported:
    </para>
    <para>
      C style: /* */
    </para>
    <para>
      C++ style: // to end of line
    </para>
    <para>
      Unix style: # to end of line
    </para>
  </refsection>

  <refsection><info><title>ACL</title></info>

    <literallayout class="normal">
acl <replaceable>string</replaceable> { <replaceable>address_match_element</replaceable>; ... };
</literallayout>
  </refsection>

  <refsection><info><title>CONTROLS</title></info>

    <literallayout class="normal">
controls {
	inet ( <replaceable>ipv4_address</replaceable> | <replaceable>ipv6_address</replaceable> |
	    * ) [ port ( <replaceable>integer</replaceable> | * ) ] allow
	    { <replaceable>address_match_element</replaceable>; ... } [
	    keys { <replaceable>string</replaceable>; ... } ] [ read-only
	    <replaceable>boolean</replaceable> ];
	unix <replaceable>quoted_string</replaceable> perm <replaceable>integer</replaceable>
	    owner <replaceable>integer</replaceable> group <replaceable>integer</replaceable> [
	    keys { <replaceable>string</replaceable>; ... } ] [ read-only
	    <replaceable>boolean</replaceable> ];
};
</literallayout>
  </refsection>

  <refsection><info><title>DLZ</title></info>

    <literallayout class="normal">
dlz <replaceable>string</replaceable> {
	database <replaceable>string</replaceable>;
	search <replaceable>boolean</replaceable>;
};
</literallayout>
  </refsection>

  <refsection><info><title>DYNDB</title></info>

    <literallayout class="normal">
dyndb <replaceable>string</replaceable> <replaceable>quoted_string</replaceable> {
    <replaceable>unspecified-text</replaceable> };
</literallayout>
  </refsection>

  <refsection><info><title>KEY</title></info>

    <literallayout class="normal">
key <replaceable>string</replaceable> {
	algorithm <replaceable>string</replaceable>;
	secret <replaceable>string</replaceable>;
};
</literallayout>
  </refsection>

  <refsection><info><title>LOGGING</title></info>

    <literallayout class="normal">
logging {
	category <replaceable>string</replaceable> { <replaceable>string</replaceable>; ... };
	channel <replaceable>string</replaceable> {
		buffered <replaceable>boolean</replaceable>;
		file <replaceable>quoted_string</replaceable> [ versions ( "unlimited" | <replaceable>integer</replaceable> )
		    ] [ size <replaceable>size</replaceable> ];
		null;
		print-category <replaceable>boolean</replaceable>;
		print-severity <replaceable>boolean</replaceable>;
		print-time <replaceable>boolean</replaceable>;
		severity <replaceable>log_severity</replaceable>;
		stderr;
		syslog [ <replaceable>syslog_facility</replaceable> ];
	};
};
</literallayout>
  </refsection>

  <refsection><info><title>LWRES</title></info>

    <literallayout class="normal">
lwres {
	listen-on [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>ipv4_address</replaceable>
	    | <replaceable>ipv6_address</replaceable> ) [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ]; ... };
	lwres-clients <replaceable>integer</replaceable>;
	lwres-tasks <replaceable>integer</replaceable>;
	ndots <replaceable>integer</replaceable>;
	search { <replaceable>string</replaceable>; ... };
	view <replaceable>string</replaceable> [ <replaceable>class</replaceable> ];
};
</literallayout>
  </refsection>

  <refsection><info><title>MANAGED-KEYS</title></info>

    <literallayout class="normal">
managed-keys { <replaceable>string</replaceable> <replaceable>string</replaceable> <replaceable>integer</replaceable>
    <replaceable>integer</replaceable> <replaceable>integer</replaceable> <replaceable>quoted_string</replaceable>; ... };
</literallayout>
  </refsection>

  <refsection><info><title>MASTERS</title></info>

    <literallayout class="normal">
masters <replaceable>string</replaceable> [ port <replaceable>integer</replaceable> ] [ dscp
    <replaceable>integer</replaceable> ] { ( <replaceable>masters</replaceable> | <replaceable>ipv4_address</replaceable> [
    port <replaceable>integer</replaceable> ] | <replaceable>ipv6_address</replaceable> [ port
    <replaceable>integer</replaceable> ] ) [ key <replaceable>string</replaceable> ]; ... };
</literallayout>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>

    <literallayout class="normal">
options {
	acache-cleaning-interval <replaceable>integer</replaceable>;
	acache-enable <replaceable>boolean</replaceable>;
	additional-from-auth <replaceable>boolean</replaceable>;
	additional-from-cache <replaceable>boolean</replaceable>;
	allow-new-zones <replaceable>boolean</replaceable>;
	allow-notify { <replaceable>address_match_element</replaceable>; ... };
	allow-query { <replaceable>address_match_element</replaceable>; ... };
	allow-query-cache { <replaceable>address_match_element</replaceable>; ... };
	allow-query-cache-on { <replaceable>address_match_element</replaceable>; ... };
	allow-query-on { <replaceable>address_match_element</replaceable>; ... };
	allow-recursion { <replaceable>address_match_element</replaceable>; ... };
	allow-recursion-on { <replaceable>address_match_element</replaceable>; ... };
	allow-transfer { <replaceable>address_match_element</replaceable>; ... };
	allow-update { <replaceable>address_match_element</replaceable>; ... };
	allow-update-forwarding { <replaceable>address_match_element</replaceable>; ... };
	also-notify [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>masters</replaceable> |
	    <replaceable>ipv4_address</replaceable> [ port <replaceable>integer</replaceable> ] | <replaceable>ipv6_address</replaceable> [ port
	    <replaceable>integer</replaceable> ] ) [ key <replaceable>string</replaceable> ]; ... };
	alt-transfer-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * )
	    ] [ dscp <replaceable>integer</replaceable> ];
	alt-transfer-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> |
	    * ) ] [ dscp <replaceable>integer</replaceable> ];
	attach-cache <replaceable>string</replaceable>;
	auth-nxdomain <replaceable>boolean</replaceable>; // default changed
	auto-dnssec ( allow | maintain | off );
	automatic-interface-scan <replaceable>boolean</replaceable>;
	avoid-v4-udp-ports { <replaceable>portrange</replaceable>; ... };
	avoid-v6-udp-ports { <replaceable>portrange</replaceable>; ... };
	bindkeys-file <replaceable>quoted_string</replaceable>;
	blackhole { <replaceable>address_match_element</replaceable>; ... };
	cache-file <replaceable>quoted_string</replaceable>;
	catalog-zones { zone <replaceable>quoted_string</replaceable> [ default-masters [ port
	    <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>masters</replaceable> | <replaceable>ipv4_address</replaceable> [
	    port <replaceable>integer</replaceable> ] | <replaceable>ipv6_address</replaceable> [ port <replaceable>integer</replaceable> ] ) [ key
	    <replaceable>string</replaceable> ]; ... } ] [ zone-directory <replaceable>quoted_string</replaceable> ] [
	    in-memory <replaceable>boolean</replaceable> ] [ min-update-interval <replaceable>integer</replaceable> ]; ... };
	check-dup-records ( fail | warn | ignore );
	check-integrity <replaceable>boolean</replaceable>;
	check-mx ( fail | warn | ignore );
	check-mx-cname ( fail | warn | ignore );
	check-names ( master | slave | response
	    ) ( fail | warn | ignore );
	check-sibling <replaceable>boolean</replaceable>;
	check-spf ( warn | ignore );
	check-srv-cname ( fail | warn | ignore );
	check-wildcard <replaceable>boolean</replaceable>;
	cleaning-interval <replaceable>integer</replaceable>;
	clients-per-query <replaceable>integer</replaceable>;
	cookie-algorithm ( aes | sha1 | sha256 );
	cookie-secret <replaceable>string</replaceable>;
	coresize ( default | unlimited | <replaceable>sizeval</replaceable> );
	datasize ( default | unlimited | <replaceable>sizeval</replaceable> );
	deny-answer-addresses { <replaceable>address_match_element</replaceable>; ... } [
	    except-from { <replaceable>quoted_string</replaceable>; ... } ];
	deny-answer-aliases { <replaceable>quoted_string</replaceable>; ... } [ except-from {
	    <replaceable>quoted_string</replaceable>; ... } ];
	dialup ( notify | notify-passive | passive | refresh | <replaceable>boolean</replaceable> );
	directory <replaceable>quoted_string</replaceable>;
	disable-algorithms <replaceable>string</replaceable> { <replaceable>string</replaceable>;
	    ... };
	disable-ds-digests <replaceable>string</replaceable> { <replaceable>string</replaceable>;
	    ... };
	disable-empty-zone <replaceable>string</replaceable>;
	dns64 <replaceable>netprefix</replaceable> {
		break-dnssec <replaceable>boolean</replaceable>;
		clients { <replaceable>address_match_element</replaceable>; ... };
		exclude { <replaceable>address_match_element</replaceable>; ... };
		mapped { <replaceable>address_match_element</replaceable>; ... };
		recursive-only <replaceable>boolean</replaceable>;
		suffix <replaceable>ipv6_address</replaceable>;
	};
	dns64-contact <replaceable>string</replaceable>;
	dns64-server <replaceable>string</replaceable>;
	dnssec-accept-expired <replaceable>boolean</replaceable>;
	dnssec-dnskey-kskonly <replaceable>boolean</replaceable>;
	dnssec-enable <replaceable>boolean</replaceable>;
	dnssec-loadkeys-interval <replaceable>integer</replaceable>;
	dnssec-lookaside ( <replaceable>string</replaceable> trust-anchor
	    <replaceable>string</replaceable> | auto | no );
	dnssec-must-be-secure <replaceable>string</replaceable> <replaceable>boolean</replaceable>;
	dnssec-secure-to-insecure <replaceable>boolean</replaceable>;
	dnssec-update-mode ( maintain | no-resign );
	dnssec-validation ( yes | no | auto );
	dnstap { ( all | auth | client | forwarder |
	    resolver ) [ ( query | response ) ]; ... };
	dnstap-identity ( <replaceable>quoted_string</replaceable> | none |
	    hostname );
	dnstap-output ( file | unix ) <replaceable>quoted_string</replaceable>;
	dnstap-version ( <replaceable>quoted_string</replaceable> | none );
	dscp <replaceable>integer</replaceable>;
	dual-stack-servers [ port <replaceable>integer</replaceable> ] { ( <replaceable>quoted_string</replaceable> [ port
	    <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] | <replaceable>ipv4_address</replaceable> [ port
	    <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] | <replaceable>ipv6_address</replaceable> [ port
	    <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] ); ... };
	dump-file <replaceable>quoted_string</replaceable>;
	edns-udp-size <replaceable>integer</replaceable>;
	empty-contact <replaceable>string</replaceable>;
	empty-server <replaceable>string</replaceable>;
	empty-zones-enable <replaceable>boolean</replaceable>;
	fetch-quota-params <replaceable>integer</replaceable> <replaceable>fixedpoint</replaceable> <replaceable>fixedpoint</replaceable> <replaceable>fixedpoint</replaceable>;
	fetches-per-server <replaceable>integer</replaceable> [ ( drop | fail ) ];
	fetches-per-zone <replaceable>integer</replaceable> [ ( drop | fail ) ];
	files ( default | unlimited | <replaceable>sizeval</replaceable> );
	filter-aaaa { <replaceable>address_match_element</replaceable>; ... };
	filter-aaaa-on-v4 ( break-dnssec | <replaceable>boolean</replaceable> );
	filter-aaaa-on-v6 ( break-dnssec | <replaceable>boolean</replaceable> );
	flush-zones-on-shutdown <replaceable>boolean</replaceable>;
	forward ( first | only );
	forwarders [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>ipv4_address</replaceable>
	    | <replaceable>ipv6_address</replaceable> ) [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ]; ... };
	fstrm-set-buffer-hint <replaceable>integer</replaceable>;
	fstrm-set-flush-timeout <replaceable>integer</replaceable>;
	fstrm-set-input-queue-size <replaceable>integer</replaceable>;
	fstrm-set-output-notify-threshold <replaceable>integer</replaceable>;
	fstrm-set-output-queue-model ( mpsc | spsc );
	fstrm-set-output-queue-size <replaceable>integer</replaceable>;
	fstrm-set-reopen-interval <replaceable>integer</replaceable>;
	geoip-directory ( <replaceable>quoted_string</replaceable> | none );
	geoip-use-ecs <replaceable>boolean</replaceable>;
	heartbeat-interval <replaceable>integer</replaceable>;
	hostname ( <replaceable>quoted_string</replaceable> | none );
	inline-signing <replaceable>boolean</replaceable>;
	interface-interval <replaceable>integer</replaceable>;
	ixfr-from-differences ( master | slave | <replaceable>boolean</replaceable> );
	keep-response-order { <replaceable>address_match_element</replaceable>; ... };
	key-directory <replaceable>quoted_string</replaceable>;
	lame-ttl <replaceable>ttlval</replaceable>;
	listen-on [ port <replaceable>integer</replaceable> ] [ dscp
	    <replaceable>integer</replaceable> ] {
	    <replaceable>address_match_element</replaceable>; ... };
	listen-on-v6 [ port <replaceable>integer</replaceable> ] [ dscp
	    <replaceable>integer</replaceable> ] {
	    <replaceable>address_match_element</replaceable>; ... };
	lmdb-mapsize <replaceable>sizeval</replaceable>;
	lock-file ( <replaceable>quoted_string</replaceable> | none );
	managed-keys-directory <replaceable>quoted_string</replaceable>;
	masterfile-format ( map | raw | text );
	masterfile-style ( full | relative );
	match-mapped-addresses <replaceable>boolean</replaceable>;
	max-acache-size ( unlimited | <replaceable>sizeval</replaceable> );
	max-cache-size ( default | unlimited | <replaceable>sizeval</replaceable> | <replaceable>percentage</replaceable> );
	max-cache-ttl <replaceable>integer</replaceable>;
	max-clients-per-query <replaceable>integer</replaceable>;
	max-journal-size ( unlimited | <replaceable>sizeval</replaceable> );
	max-ncache-ttl <replaceable>integer</replaceable>;
	max-records <replaceable>integer</replaceable>;
	max-recursion-depth <replaceable>integer</replaceable>;
	max-recursion-queries <replaceable>integer</replaceable>;
	max-refresh-time <replaceable>integer</replaceable>;
	max-retry-time <replaceable>integer</replaceable>;
	max-rsa-exponent-size <replaceable>integer</replaceable>;
	max-transfer-idle-in <replaceable>integer</replaceable>;
	max-transfer-idle-out <replaceable>integer</replaceable>;
	max-transfer-time-in <replaceable>integer</replaceable>;
	max-transfer-time-out <replaceable>integer</replaceable>;
	max-udp-size <replaceable>integer</replaceable>;
	max-zone-ttl ( unlimited | <replaceable>ttlval</replaceable> );
	memstatistics <replaceable>boolean</replaceable>;
	memstatistics-file <replaceable>quoted_string</replaceable>;
	message-compression <replaceable>boolean</replaceable>;
	min-refresh-time <replaceable>integer</replaceable>;
	min-retry-time <replaceable>integer</replaceable>;
	minimal-any <replaceable>boolean</replaceable>;
	minimal-responses ( no-auth | no-auth-recursive | <replaceable>boolean</replaceable> );
	multi-master <replaceable>boolean</replaceable>;
	no-case-compress { <replaceable>address_match_element</replaceable>; ... };
	nocookie-udp-size <replaceable>integer</replaceable>;
	notify ( explicit | master-only | <replaceable>boolean</replaceable> );
	notify-delay <replaceable>integer</replaceable>;
	notify-rate <replaceable>integer</replaceable>;
	notify-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ] [
	    dscp <replaceable>integer</replaceable> ];
	notify-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ]
	    [ dscp <replaceable>integer</replaceable> ];
	notify-to-soa <replaceable>boolean</replaceable>;
	nta-lifetime <replaceable>ttlval</replaceable>;
	nta-recheck <replaceable>ttlval</replaceable>;
	nxdomain-redirect <replaceable>string</replaceable>;
	pid-file ( <replaceable>quoted_string</replaceable> | none );
	port <replaceable>integer</replaceable>;
	preferred-glue <replaceable>string</replaceable>;
	prefetch <replaceable>integer</replaceable> [ <replaceable>integer</replaceable> ];
	provide-ixfr <replaceable>boolean</replaceable>;
	query-source ( ( [ address ] ( <replaceable>ipv4_address</replaceable> | * ) [ port (
	    <replaceable>integer</replaceable> | * ) ] ) | ( [ [ address ] ( <replaceable>ipv4_address</replaceable> | * ) ]
	    port ( <replaceable>integer</replaceable> | * ) ) ) [ dscp <replaceable>integer</replaceable> ];
	query-source-v6 ( ( [ address ] ( <replaceable>ipv6_address</replaceable> | * ) [ port (
	    <replaceable>integer</replaceable> | * ) ] ) | ( [ [ address ] ( <replaceable>ipv6_address</replaceable> | * ) ]
	    port ( <replaceable>integer</replaceable> | * ) ) ) [ dscp <replaceable>integer</replaceable> ];
	querylog <replaceable>boolean</replaceable>;
	random-device <replaceable>quoted_string</replaceable>;
	rate-limit {
		all-per-second <replaceable>integer</replaceable>;
		errors-per-second <replaceable>integer</replaceable>;
		exempt-clients { <replaceable>address_match_element</replaceable>; ... };
		ipv4-prefix-length <replaceable>integer</replaceable>;
		ipv6-prefix-length <replaceable>integer</replaceable>;
		log-only <replaceable>boolean</replaceable>;
		max-table-size <replaceable>integer</replaceable>;
		min-table-size <replaceable>integer</replaceable>;
		nodata-per-second <replaceable>integer</replaceable>;
		nxdomains-per-second <replaceable>integer</replaceable>;
		qps-scale <replaceable>integer</replaceable>;
		referrals-per-second <replaceable>integer</replaceable>;
		responses-per-second <replaceable>integer</replaceable>;
		slip <replaceable>integer</replaceable>;
		window <replaceable>integer</replaceable>;
	};
	recursing-file <replaceable>quoted_string</replaceable>;
	recursion <replaceable>boolean</replaceable>;
	recursive-clients <replaceable>integer</replaceable>;
	request-expire <replaceable>boolean</replaceable>;
	request-ixfr <replaceable>boolean</replaceable>;
	request-nsid <replaceable>boolean</replaceable>;
	require-server-cookie <replaceable>boolean</replaceable>;
	reserved-sockets <replaceable>integer</replaceable>;
	resolver-query-timeout <replaceable>integer</replaceable>;
	response-policy { zone <replaceable>quoted_string</replaceable> [ log <replaceable>boolean</replaceable> ] [
	    max-policy-ttl <replaceable>integer</replaceable> ] [ policy ( cname | disabled | drop |
	    given | no-op | nodata | nxdomain | passthru | tcp-only
	    <replaceable>quoted_string</replaceable> ) ] [ recursive-only <replaceable>boolean</replaceable> ]; ... } [
	    break-dnssec <replaceable>boolean</replaceable> ] [ max-policy-ttl <replaceable>integer</replaceable> ] [
	    min-ns-dots <replaceable>integer</replaceable> ] [ nsip-wait-recurse <replaceable>boolean</replaceable> ] [
	    qname-wait-recurse <replaceable>boolean</replaceable> ] [ recursive-only <replaceable>boolean</replaceable> ];
	root-delegation-only [ exclude { <replaceable>quoted_string</replaceable>; ... } ];
	rrset-order { [ class <replaceable>string</replaceable> ] [ type <replaceable>string</replaceable> ] [ name
	    <replaceable>quoted_string</replaceable> ] <replaceable>string</replaceable> <replaceable>string</replaceable>; ... };
	secroots-file <replaceable>quoted_string</replaceable>;
	send-cookie <replaceable>boolean</replaceable>;
	serial-query-rate <replaceable>integer</replaceable>;
	serial-update-method ( date | increment | unixtime );
	server-id ( <replaceable>quoted_string</replaceable> | none | hostname );
	servfail-ttl <replaceable>ttlval</replaceable>;
	session-keyalg <replaceable>string</replaceable>;
	session-keyfile ( <replaceable>quoted_string</replaceable> | none );
	session-keyname <replaceable>string</replaceable>;
	sig-signing-nodes <replaceable>integer</replaceable>;
	sig-signing-signatures <replaceable>integer</replaceable>;
	sig-signing-type <replaceable>integer</replaceable>;
	sig-validity-interval <replaceable>integer</replaceable> [ <replaceable>integer</replaceable> ];
	sortlist { <replaceable>address_match_element</replaceable>; ... };
	stacksize ( default | unlimited | <replaceable>sizeval</replaceable> );
	startup-notify-rate <replaceable>integer</replaceable>;
	statistics-file <replaceable>quoted_string</replaceable>;
	tcp-clients <replaceable>integer</replaceable>;
	tcp-listen-queue <replaceable>integer</replaceable>;
	tkey-dhkey <replaceable>quoted_string</replaceable> <replaceable>integer</replaceable>;
	tkey-domain <replaceable>quoted_string</replaceable>;
	tkey-gssapi-credential <replaceable>quoted_string</replaceable>;
	tkey-gssapi-keytab <replaceable>quoted_string</replaceable>;
	transfer-format ( many-answers | one-answer );
	transfer-message-size <replaceable>integer</replaceable>;
	transfer-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ] [
	    dscp <replaceable>integer</replaceable> ];
	transfer-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * )
	    ] [ dscp <replaceable>integer</replaceable> ];
	transfers-in <replaceable>integer</replaceable>;
	transfers-out <replaceable>integer</replaceable>;
	transfers-per-ns <replaceable>integer</replaceable>;
	trust-anchor-telemetry <replaceable>boolean</replaceable>; // experimental
	try-tcp-refresh <replaceable>boolean</replaceable>;
	update-check-ksk <replaceable>boolean</replaceable>;
	use-alt-transfer-source <replaceable>boolean</replaceable>;
	use-v4-udp-ports { <replaceable>portrange</replaceable>; ... };
	use-v6-udp-ports { <replaceable>portrange</replaceable>; ... };
	v6-bias <replaceable>integer</replaceable>;
	version ( <replaceable>quoted_string</replaceable> | none );
	zero-no-soa-ttl <replaceable>boolean</replaceable>;
	zero-no-soa-ttl-cache <replaceable>boolean</replaceable>;
	zone-statistics ( full | terse | none | <replaceable>boolean</replaceable> );
};
</literallayout>
  </refsection>

  <refsection><info><title>SERVER</title></info>

    <literallayout class="normal">
server <replaceable>netprefix</replaceable> {
	bogus <replaceable>boolean</replaceable>;
	edns <replaceable>boolean</replaceable>;
	edns-udp-size <replaceable>integer</replaceable>;
	edns-version <replaceable>integer</replaceable>;
	keys <replaceable>server_key</replaceable>;
	max-udp-size <replaceable>integer</replaceable>;
	notify-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ] [
	    dscp <replaceable>integer</replaceable> ];
	notify-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ]
	    [ dscp <replaceable>integer</replaceable> ];
	provide-ixfr <replaceable>boolean</replaceable>;
	query-source ( ( [ address ] ( <replaceable>ipv4_address</replaceable> | * ) [ port (
	    <replaceable>integer</replaceable> | * ) ] ) | ( [ [ address ] ( <replaceable>ipv4_address</replaceable> | * ) ]
	    port ( <replaceable>integer</replaceable> | * ) ) ) [ dscp <replaceable>integer</replaceable> ];
	query-source-v6 ( ( [ address ] ( <replaceable>ipv6_address</replaceable> | * ) [ port (
	    <replaceable>integer</replaceable> | * ) ] ) | ( [ [ address ] ( <replaceable>ipv6_address</replaceable> | * ) ]
	    port ( <replaceable>integer</replaceable> | * ) ) ) [ dscp <replaceable>integer</replaceable> ];
	request-expire <replaceable>boolean</replaceable>;
	request-ixfr <replaceable>boolean</replaceable>;
	request-nsid <replaceable>boolean</replaceable>;
	send-cookie <replaceable>boolean</replaceable>;
	tcp-only <replaceable>boolean</replaceable>;
	transfer-format ( many-answers | one-answer );
	transfer-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ] [
	    dscp <replaceable>integer</replaceable> ];
	transfer-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * )
	    ] [ dscp <replaceable>integer</replaceable> ];
	transfers <replaceable>integer</replaceable>;
};
</literallayout>
  </refsection>

  <refsection><info><title>STATISTICS-CHANNELS</title></info>

    <literallayout class="normal">
statistics-channels {
	inet ( <replaceable>ipv4_address</replaceable> | <replaceable>ipv6_address</replaceable> |
	    * ) [ port ( <replaceable>integer</replaceable> | * ) ] [
	    allow { <replaceable>address_match_element</replaceable>; ...
	    } ];
};
</literallayout>
  </refsection>

  <refsection><info><title>TRUSTED-KEYS</title></info>

    <literallayout class="normal">
trusted-keys { <replaceable>string</replaceable> <replaceable>integer</replaceable> <replaceable>integer</replaceable>
    <replaceable>integer</replaceable> <replaceable>quoted_string</replaceable>; ... };
</literallayout>
  </refsection>

  <refsection><info><title>VIEW</title></info>

    <literallayout class="normal">
view <replaceable>string</replaceable> [ <replaceable>class</replaceable> ] {
	acache-cleaning-interval <replaceable>integer</replaceable>;
	acache-enable <replaceable>boolean</replaceable>;
	additional-from-auth <replaceable>boolean</replaceable>;
	additional-from-cache <replaceable>boolean</replaceable>;
	allow-new-zones <replaceable>boolean</replaceable>;
	allow-notify { <replaceable>address_match_element</replaceable>; ... };
	allow-query { <replaceable>address_match_element</replaceable>; ... };
	allow-query-cache { <replaceable>address_match_element</replaceable>; ... };
	allow-query-cache-on { <replaceable>address_match_element</replaceable>; ... };
	allow-query-on { <replaceable>address_match_element</replaceable>; ... };
	allow-recursion { <replaceable>address_match_element</replaceable>; ... };
	allow-recursion-on { <replaceable>address_match_element</replaceable>; ... };
	allow-transfer { <replaceable>address_match_element</replaceable>; ... };
	allow-update { <replaceable>address_match_element</replaceable>; ... };
	allow-update-forwarding { <replaceable>address_match_element</replaceable>; ... };
	also-notify [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>masters</replaceable> |
	    <replaceable>ipv4_address</replaceable> [ port <replaceable>integer</replaceable> ] | <replaceable>ipv6_address</replaceable> [ port
	    <replaceable>integer</replaceable> ] ) [ key <replaceable>string</replaceable> ]; ... };
	alt-transfer-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * )
	    ] [ dscp <replaceable>integer</replaceable> ];
	alt-transfer-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> |
	    * ) ] [ dscp <replaceable>integer</replaceable> ];
	attach-cache <replaceable>string</replaceable>;
	auth-nxdomain <replaceable>boolean</replaceable>; // default changed
	auto-dnssec ( allow | maintain | off );
	cache-file <replaceable>quoted_string</replaceable>;
	catalog-zones { zone <replaceable>quoted_string</replaceable> [ default-masters [ port
	    <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>masters</replaceable> | <replaceable>ipv4_address</replaceable> [
	    port <replaceable>integer</replaceable> ] | <replaceable>ipv6_address</replaceable> [ port <replaceable>integer</replaceable> ] ) [ key
	    <replaceable>string</replaceable> ]; ... } ] [ zone-directory <replaceable>quoted_string</replaceable> ] [
	    in-memory <replaceable>boolean</replaceable> ] [ min-update-interval <replaceable>integer</replaceable> ]; ... };
	check-dup-records ( fail | warn | ignore );
	check-integrity <replaceable>boolean</replaceable>;
	check-mx ( fail | warn | ignore );
	check-mx-cname ( fail | warn | ignore );
	check-names ( master | slave | response
	    ) ( fail | warn | ignore );
	check-sibling <replaceable>boolean</replaceable>;
	check-spf ( warn | ignore );
	check-srv-cname ( fail | warn | ignore );
	check-wildcard <replaceable>boolean</replaceable>;
	cleaning-interval <replaceable>integer</replaceable>;
	clients-per-query <replaceable>integer</replaceable>;
	deny-answer-addresses { <replaceable>address_match_element</replaceable>; ... } [
	    except-from { <replaceable>quoted_string</replaceable>; ... } ];
	deny-answer-aliases { <replaceable>quoted_string</replaceable>; ... } [ except-from {
	    <replaceable>quoted_string</replaceable>; ... } ];
	dialup ( notify | notify-passive | passive | refresh | <replaceable>boolean</replaceable> );
	disable-algorithms <replaceable>string</replaceable> { <replaceable>string</replaceable>;
	    ... };
	disable-ds-digests <replaceable>string</replaceable> { <replaceable>string</replaceable>;
	    ... };
	disable-empty-zone <replaceable>string</replaceable>;
	dlz <replaceable>string</replaceable> {
		database <replaceable>string</replaceable>;
		search <replaceable>boolean</replaceable>;
	};
	dns64 <replaceable>netprefix</replaceable> {
		break-dnssec <replaceable>boolean</replaceable>;
		clients { <replaceable>address_match_element</replaceable>; ... };
		exclude { <replaceable>address_match_element</replaceable>; ... };
		mapped { <replaceable>address_match_element</replaceable>; ... };
		recursive-only <replaceable>boolean</replaceable>;
		suffix <replaceable>ipv6_address</replaceable>;
	};
	dns64-contact <replaceable>string</replaceable>;
	dns64-server <replaceable>string</replaceable>;
	dnssec-accept-expired <replaceable>boolean</replaceable>;
	dnssec-dnskey-kskonly <replaceable>boolean</replaceable>;
	dnssec-enable <replaceable>boolean</replaceable>;
	dnssec-loadkeys-interval <replaceable>integer</replaceable>;
	dnssec-lookaside ( <replaceable>string</replaceable> trust-anchor
	    <replaceable>string</replaceable> | auto | no );
	dnssec-must-be-secure <replaceable>string</replaceable> <replaceable>boolean</replaceable>;
	dnssec-secure-to-insecure <replaceable>boolean</replaceable>;
	dnssec-update-mode ( maintain | no-resign );
	dnssec-validation ( yes | no | auto );
	dnstap { ( all | auth | client | forwarder |
	    resolver ) [ ( query | response ) ]; ... };
	dual-stack-servers [ port <replaceable>integer</replaceable> ] { ( <replaceable>quoted_string</replaceable> [ port
	    <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] | <replaceable>ipv4_address</replaceable> [ port
	    <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] | <replaceable>ipv6_address</replaceable> [ port
	    <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] ); ... };
	dyndb <replaceable>string</replaceable> <replaceable>quoted_string</replaceable> {
	    <replaceable>unspecified-text</replaceable> };
	edns-udp-size <replaceable>integer</replaceable>;
	empty-contact <replaceable>string</replaceable>;
	empty-server <replaceable>string</replaceable>;
	empty-zones-enable <replaceable>boolean</replaceable>;
	fetch-quota-params <replaceable>integer</replaceable> <replaceable>fixedpoint</replaceable> <replaceable>fixedpoint</replaceable> <replaceable>fixedpoint</replaceable>;
	fetches-per-server <replaceable>integer</replaceable> [ ( drop | fail ) ];
	fetches-per-zone <replaceable>integer</replaceable> [ ( drop | fail ) ];
	filter-aaaa { <replaceable>address_match_element</replaceable>; ... };
	filter-aaaa-on-v4 ( break-dnssec | <replaceable>boolean</replaceable> );
	filter-aaaa-on-v6 ( break-dnssec | <replaceable>boolean</replaceable> );
	forward ( first | only );
	forwarders [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>ipv4_address</replaceable>
	    | <replaceable>ipv6_address</replaceable> ) [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ]; ... };
	inline-signing <replaceable>boolean</replaceable>;
	ixfr-from-differences ( master | slave | <replaceable>boolean</replaceable> );
	key <replaceable>string</replaceable> {
		algorithm <replaceable>string</replaceable>;
		secret <replaceable>string</replaceable>;
	};
	key-directory <replaceable>quoted_string</replaceable>;
	lame-ttl <replaceable>ttlval</replaceable>;
	lmdb-mapsize <replaceable>sizeval</replaceable>;
	managed-keys { <replaceable>string</replaceable> <replaceable>string</replaceable>
	    <replaceable>integer</replaceable> <replaceable>integer</replaceable> <replaceable>integer</replaceable>
	    <replaceable>quoted_string</replaceable>; ... };
	masterfile-format ( map | raw | text );
	masterfile-style ( full | relative );
	match-clients { <replaceable>address_match_element</replaceable>; ... };
	match-destinations { <replaceable>address_match_element</replaceable>; ... };
	match-recursive-only <replaceable>boolean</replaceable>;
	max-acache-size ( unlimited | <replaceable>sizeval</replaceable> );
	max-cache-size ( default | unlimited | <replaceable>sizeval</replaceable> | <replaceable>percentage</replaceable> );
	max-cache-ttl <replaceable>integer</replaceable>;
	max-clients-per-query <replaceable>integer</replaceable>;
	max-journal-size ( unlimited | <replaceable>sizeval</replaceable> );
	max-ncache-ttl <replaceable>integer</replaceable>;
	max-records <replaceable>integer</replaceable>;
	max-recursion-depth <replaceable>integer</replaceable>;
	max-recursion-queries <replaceable>integer</replaceable>;
	max-refresh-time <replaceable>integer</replaceable>;
	max-retry-time <replaceable>integer</replaceable>;
	max-transfer-idle-in <replaceable>integer</replaceable>;
	max-transfer-idle-out <replaceable>integer</replaceable>;
	max-transfer-time-in <replaceable>integer</replaceable>;
	max-transfer-time-out <replaceable>integer</replaceable>;
	max-udp-size <replaceable>integer</replaceable>;
	max-zone-ttl ( unlimited | <replaceable>ttlval</replaceable> );
	message-compression <replaceable>boolean</replaceable>;
	min-refresh-time <replaceable>integer</replaceable>;
	min-retry-time <replaceable>integer</replaceable>;
	minimal-any <replaceable>boolean</replaceable>;
	minimal-responses ( no-auth | no-auth-recursive | <replaceable>boolean</replaceable> );
	multi-master <replaceable>boolean</replaceable>;
	no-case-compress { <replaceable>address_match_element</replaceable>; ... };
	nocookie-udp-size <replaceable>integer</replaceable>;
	notify ( explicit | master-only | <replaceable>boolean</replaceable> );
	notify-delay <replaceable>integer</replaceable>;
	notify-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ] [
	    dscp <replaceable>integer</replaceable> ];
	notify-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ]
	    [ dscp <replaceable>integer</replaceable> ];
	notify-to-soa <replaceable>boolean</replaceable>;
	nta-lifetime <replaceable>ttlval</replaceable>;
	nta-recheck <replaceable>ttlval</replaceable>;
	nxdomain-redirect <replaceable>string</replaceable>;
	preferred-glue <replaceable>string</replaceable>;
	prefetch <replaceable>integer</replaceable> [ <replaceable>integer</replaceable> ];
	provide-ixfr <replaceable>boolean</replaceable>;
	query-source ( ( [ address ] ( <replaceable>ipv4_address</replaceable> | * ) [ port (
	    <replaceable>integer</replaceable> | * ) ] ) | ( [ [ address ] ( <replaceable>ipv4_address</replaceable> | * ) ]
	    port ( <replaceable>integer</replaceable> | * ) ) ) [ dscp <replaceable>integer</replaceable> ];
	query-source-v6 ( ( [ address ] ( <replaceable>ipv6_address</replaceable> | * ) [ port (
	    <replaceable>integer</replaceable> | * ) ] ) | ( [ [ address ] ( <replaceable>ipv6_address</replaceable> | * ) ]
	    port ( <replaceable>integer</replaceable> | * ) ) ) [ dscp <replaceable>integer</replaceable> ];
	rate-limit {
		all-per-second <replaceable>integer</replaceable>;
		errors-per-second <replaceable>integer</replaceable>;
		exempt-clients { <replaceable>address_match_element</replaceable>; ... };
		ipv4-prefix-length <replaceable>integer</replaceable>;
		ipv6-prefix-length <replaceable>integer</replaceable>;
		log-only <replaceable>boolean</replaceable>;
		max-table-size <replaceable>integer</replaceable>;
		min-table-size <replaceable>integer</replaceable>;
		nodata-per-second <replaceable>integer</replaceable>;
		nxdomains-per-second <replaceable>integer</replaceable>;
		qps-scale <replaceable>integer</replaceable>;
		referrals-per-second <replaceable>integer</replaceable>;
		responses-per-second <replaceable>integer</replaceable>;
		slip <replaceable>integer</replaceable>;
		window <replaceable>integer</replaceable>;
	};
	recursion <replaceable>boolean</replaceable>;
	request-expire <replaceable>boolean</replaceable>;
	request-ixfr <replaceable>boolean</replaceable>;
	request-nsid <replaceable>boolean</replaceable>;
	require-server-cookie <replaceable>boolean</replaceable>;
	resolver-query-timeout <replaceable>integer</replaceable>;
	response-policy { zone <replaceable>quoted_string</replaceable> [ log <replaceable>boolean</replaceable> ] [
	    max-policy-ttl <replaceable>integer</replaceable> ] [ policy ( cname | disabled | drop |
	    given | no-op | nodata | nxdomain | passthru | tcp-only
	    <replaceable>quoted_string</replaceable> ) ] [ recursive-only <replaceable>boolean</replaceable> ]; ... } [
	    break-dnssec <replaceable>boolean</replaceable> ] [ max-policy-ttl <replaceable>integer</replaceable> ] [
	    min-ns-dots <replaceable>integer</replaceable> ] [ nsip-wait-recurse <replaceable>boolean</replaceable> ] [
	    qname-wait-recurse <replaceable>boolean</replaceable> ] [ recursive-only <replaceable>boolean</replaceable> ];
	root-delegation-only [ exclude { <replaceable>quoted_string</replaceable>; ... } ];
	rrset-order { [ class <replaceable>string</replaceable> ] [ type <replaceable>string</replaceable> ] [ name
	    <replaceable>quoted_string</replaceable> ] <replaceable>string</replaceable> <replaceable>string</replaceable>; ... };
	send-cookie <replaceable>boolean</replaceable>;
	serial-update-method ( date | increment | unixtime );
	server <replaceable>netprefix</replaceable> {
		bogus <replaceable>boolean</replaceable>;
		edns <replaceable>boolean</replaceable>;
		edns-udp-size <replaceable>integer</replaceable>;
		edns-version <replaceable>integer</replaceable>;
		keys <replaceable>server_key</replaceable>;
		max-udp-size <replaceable>integer</replaceable>;
		notify-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | *
		    ) ] [ dscp <replaceable>integer</replaceable> ];
		notify-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable>
		    | * ) ] [ dscp <replaceable>integer</replaceable> ];
		provide-ixfr <replaceable>boolean</replaceable>;
		query-source ( ( [ address ] ( <replaceable>ipv4_address</replaceable> | * ) [ port
		    ( <replaceable>integer</replaceable> | * ) ] ) | ( [ [ address ] (
		    <replaceable>ipv4_address</replaceable> | * ) ] port ( <replaceable>integer</replaceable> | * ) ) ) [
		    dscp <replaceable>integer</replaceable> ];
		query-source-v6 ( ( [ address ] ( <replaceable>ipv6_address</replaceable> | * ) [
		    port ( <replaceable>integer</replaceable> | * ) ] ) | ( [ [ address ] (
		    <replaceable>ipv6_address</replaceable> | * ) ] port ( <replaceable>integer</replaceable> | * ) ) ) [
		    dscp <replaceable>integer</replaceable> ];
		request-expire <replaceable>boolean</replaceable>;
		request-ixfr <replaceable>boolean</replaceable>;
		request-nsid <replaceable>boolean</replaceable>;
		send-cookie <replaceable>boolean</replaceable>;
		tcp-only <replaceable>boolean</replaceable>;
		transfer-format ( many-answers | one-answer );
		transfer-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> |
		    * ) ] [ dscp <replaceable>integer</replaceable> ];
		transfer-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port (
		    <replaceable>integer</replaceable> | * ) ] [ dscp <replaceable>integer</replaceable> ];
		transfers <replaceable>integer</replaceable>;
	};
	servfail-ttl <replaceable>ttlval</replaceable>;
	sig-signing-nodes <replaceable>integer</replaceable>;
	sig-signing-signatures <replaceable>integer</replaceable>;
	sig-signing-type <replaceable>integer</replaceable>;
	sig-validity-interval <replaceable>integer</replaceable> [ <replaceable>integer</replaceable> ];
	sortlist { <replaceable>address_match_element</replaceable>; ... };
	transfer-format ( many-answers | one-answer );
	transfer-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ] [
	    dscp <replaceable>integer</replaceable> ];
	transfer-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * )
	    ] [ dscp <replaceable>integer</replaceable> ];
	trust-anchor-telemetry <replaceable>boolean</replaceable>; // experimental
	trusted-keys { <replaceable>string</replaceable> <replaceable>integer</replaceable>
	    <replaceable>integer</replaceable> <replaceable>integer</replaceable> <replaceable>quoted_string</replaceable>;
	    ... };
	try-tcp-refresh <replaceable>boolean</replaceable>;
	update-check-ksk <replaceable>boolean</replaceable>;
	use-alt-transfer-source <replaceable>boolean</replaceable>;
	v6-bias <replaceable>integer</replaceable>;
	zero-no-soa-ttl <replaceable>boolean</replaceable>;
	zero-no-soa-ttl-cache <replaceable>boolean</replaceable>;
	zone <replaceable>string</replaceable> [ <replaceable>class</replaceable> ] {
		allow-notify { <replaceable>address_match_element</replaceable>; ... };
		allow-query { <replaceable>address_match_element</replaceable>; ... };
		allow-query-on { <replaceable>address_match_element</replaceable>; ... };
		allow-transfer { <replaceable>address_match_element</replaceable>; ... };
		allow-update { <replaceable>address_match_element</replaceable>; ... };
		allow-update-forwarding { <replaceable>address_match_element</replaceable>; ... };
		also-notify [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { (
		    <replaceable>masters</replaceable> | <replaceable>ipv4_address</replaceable> [ port <replaceable>integer</replaceable> ] |
		    <replaceable>ipv6_address</replaceable> [ port <replaceable>integer</replaceable> ] ) [ key <replaceable>string</replaceable> ];
		    ... };
		alt-transfer-source ( <replaceable>ipv4_address</replaceable> | * ) [ port (
		    <replaceable>integer</replaceable> | * ) ] [ dscp <replaceable>integer</replaceable> ];
		alt-transfer-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port (
		    <replaceable>integer</replaceable> | * ) ] [ dscp <replaceable>integer</replaceable> ];
		auto-dnssec ( allow | maintain | off );
		check-dup-records ( fail | warn | ignore );
		check-integrity <replaceable>boolean</replaceable>;
		check-mx ( fail | warn | ignore );
		check-mx-cname ( fail | warn | ignore );
		check-names ( fail | warn | ignore );
		check-sibling <replaceable>boolean</replaceable>;
		check-spf ( warn | ignore );
		check-srv-cname ( fail | warn | ignore );
		check-wildcard <replaceable>boolean</replaceable>;
		database <replaceable>string</replaceable>;
		delegation-only <replaceable>boolean</replaceable>;
		dialup ( notify | notify-passive | passive | refresh |
		    <replaceable>boolean</replaceable> );
		dlz <replaceable>string</replaceable>;
		dnssec-dnskey-kskonly <replaceable>boolean</replaceable>;
		dnssec-loadkeys-interval <replaceable>integer</replaceable>;
		dnssec-secure-to-insecure <replaceable>boolean</replaceable>;
		dnssec-update-mode ( maintain | no-resign );
		file <replaceable>quoted_string</replaceable>;
		forward ( first | only );
		forwarders [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { (
		    <replaceable>ipv4_address</replaceable> | <replaceable>ipv6_address</replaceable> ) [ port <replaceable>integer</replaceable> ] [
		    dscp <replaceable>integer</replaceable> ]; ... };
		in-view <replaceable>string</replaceable>;
		inline-signing <replaceable>boolean</replaceable>;
		ixfr-from-differences <replaceable>boolean</replaceable>;
		journal <replaceable>quoted_string</replaceable>;
		key-directory <replaceable>quoted_string</replaceable>;
		masterfile-format ( map | raw | text );
		masterfile-style ( full | relative );
		masters [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>masters</replaceable>
		    | <replaceable>ipv4_address</replaceable> [ port <replaceable>integer</replaceable> ] | <replaceable>ipv6_address</replaceable> [
		    port <replaceable>integer</replaceable> ] ) [ key <replaceable>string</replaceable> ]; ... };
		max-ixfr-log-size ( default | unlimited |
		max-journal-size ( unlimited | <replaceable>sizeval</replaceable> );
		max-records <replaceable>integer</replaceable>;
		max-refresh-time <replaceable>integer</replaceable>;
		max-retry-time <replaceable>integer</replaceable>;
		max-transfer-idle-in <replaceable>integer</replaceable>;
		max-transfer-idle-out <replaceable>integer</replaceable>;
		max-transfer-time-in <replaceable>integer</replaceable>;
		max-transfer-time-out <replaceable>integer</replaceable>;
		max-zone-ttl ( unlimited | <replaceable>ttlval</replaceable> );
		min-refresh-time <replaceable>integer</replaceable>;
		min-retry-time <replaceable>integer</replaceable>;
		multi-master <replaceable>boolean</replaceable>;
		notify ( explicit | master-only | <replaceable>boolean</replaceable> );
		notify-delay <replaceable>integer</replaceable>;
		notify-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | *
		    ) ] [ dscp <replaceable>integer</replaceable> ];
		notify-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable>
		    | * ) ] [ dscp <replaceable>integer</replaceable> ];
		notify-to-soa <replaceable>boolean</replaceable>;
		pubkey <replaceable>integer</replaceable>
		    <replaceable>integer</replaceable>
		    <replaceable>integer</replaceable>
		request-expire <replaceable>boolean</replaceable>;
		request-ixfr <replaceable>boolean</replaceable>;
		serial-update-method ( date | increment | unixtime );
		server-addresses { ( <replaceable>ipv4_address</replaceable> | <replaceable>ipv6_address</replaceable> ) [
		    port <replaceable>integer</replaceable> ]; ... };
		server-names { <replaceable>quoted_string</replaceable>; ... };
		sig-signing-nodes <replaceable>integer</replaceable>;
		sig-signing-signatures <replaceable>integer</replaceable>;
		sig-signing-type <replaceable>integer</replaceable>;
		sig-validity-interval <replaceable>integer</replaceable> [ <replaceable>integer</replaceable> ];
		transfer-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> |
		    * ) ] [ dscp <replaceable>integer</replaceable> ];
		transfer-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port (
		    <replaceable>integer</replaceable> | * ) ] [ dscp <replaceable>integer</replaceable> ];
		try-tcp-refresh <replaceable>boolean</replaceable>;
		type ( delegation-only | forward | hint | master | redirect
		    | slave | static-stub | stub );
		update-check-ksk <replaceable>boolean</replaceable>;
		update-policy ( local | { ( deny | grant ) <replaceable>string</replaceable> (
		    6to4-self | external | krb5-self | krb5-subdomain |
		    ms-self | ms-subdomain | name | self | selfsub |
		    selfwild | subdomain | tcp-self | wildcard | zonesub )
		    [ <replaceable>string</replaceable> ] <replaceable>rrtypelist</replaceable>; ... };
		use-alt-transfer-source <replaceable>boolean</replaceable>;
		zero-no-soa-ttl <replaceable>boolean</replaceable>;
		zone-statistics ( full | terse | none | <replaceable>boolean</replaceable> );
	};
	zone-statistics ( full | terse | none | <replaceable>boolean</replaceable> );
};
</literallayout>
  </refsection>

  <refsection><info><title>ZONE</title></info>

    <literallayout class="normal">
zone <replaceable>string</replaceable> [ <replaceable>class</replaceable> ] {
	allow-notify { <replaceable>address_match_element</replaceable>; ... };
	allow-query { <replaceable>address_match_element</replaceable>; ... };
	allow-query-on { <replaceable>address_match_element</replaceable>; ... };
	allow-transfer { <replaceable>address_match_element</replaceable>; ... };
	allow-update { <replaceable>address_match_element</replaceable>; ... };
	allow-update-forwarding { <replaceable>address_match_element</replaceable>; ... };
	also-notify [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>masters</replaceable> |
	    <replaceable>ipv4_address</replaceable> [ port <replaceable>integer</replaceable> ] | <replaceable>ipv6_address</replaceable> [ port
	    <replaceable>integer</replaceable> ] ) [ key <replaceable>string</replaceable> ]; ... };
	alt-transfer-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * )
	    ] [ dscp <replaceable>integer</replaceable> ];
	alt-transfer-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> |
	    * ) ] [ dscp <replaceable>integer</replaceable> ];
	auto-dnssec ( allow | maintain | off );
	check-dup-records ( fail | warn | ignore );
	check-integrity <replaceable>boolean</replaceable>;
	check-mx ( fail | warn | ignore );
	check-mx-cname ( fail | warn | ignore );
	check-names ( fail | warn | ignore );
	check-sibling <replaceable>boolean</replaceable>;
	check-spf ( warn | ignore );
	check-srv-cname ( fail | warn | ignore );
	check-wildcard <replaceable>boolean</replaceable>;
	database <replaceable>string</replaceable>;
	delegation-only <replaceable>boolean</replaceable>;
	dialup ( notify | notify-passive | passive | refresh | <replaceable>boolean</replaceable> );
	dlz <replaceable>string</replaceable>;
	dnssec-dnskey-kskonly <replaceable>boolean</replaceable>;
	dnssec-loadkeys-interval <replaceable>integer</replaceable>;
	dnssec-secure-to-insecure <replaceable>boolean</replaceable>;
	dnssec-update-mode ( maintain | no-resign );
	file <replaceable>quoted_string</replaceable>;
	forward ( first | only );
	forwarders [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>ipv4_address</replaceable>
	    | <replaceable>ipv6_address</replaceable> ) [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ]; ... };
	in-view <replaceable>string</replaceable>;
	inline-signing <replaceable>boolean</replaceable>;
	ixfr-from-differences <replaceable>boolean</replaceable>;
	journal <replaceable>quoted_string</replaceable>;
	key-directory <replaceable>quoted_string</replaceable>;
	masterfile-format ( map | raw | text );
	masterfile-style ( full | relative );
	masters [ port <replaceable>integer</replaceable> ] [ dscp <replaceable>integer</replaceable> ] { ( <replaceable>masters</replaceable> |
	    <replaceable>ipv4_address</replaceable> [ port <replaceable>integer</replaceable> ] | <replaceable>ipv6_address</replaceable> [ port
	    <replaceable>integer</replaceable> ] ) [ key <replaceable>string</replaceable> ]; ... };
	max-journal-size ( unlimited | <replaceable>sizeval</replaceable> );
	max-records <replaceable>integer</replaceable>;
	max-refresh-time <replaceable>integer</replaceable>;
	max-retry-time <replaceable>integer</replaceable>;
	max-transfer-idle-in <replaceable>integer</replaceable>;
	max-transfer-idle-out <replaceable>integer</replaceable>;
	max-transfer-time-in <replaceable>integer</replaceable>;
	max-transfer-time-out <replaceable>integer</replaceable>;
	max-zone-ttl ( unlimited | <replaceable>ttlval</replaceable> );
	min-refresh-time <replaceable>integer</replaceable>;
	min-retry-time <replaceable>integer</replaceable>;
	multi-master <replaceable>boolean</replaceable>;
	notify ( explicit | master-only | <replaceable>boolean</replaceable> );
	notify-delay <replaceable>integer</replaceable>;
	notify-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ] [
	    dscp <replaceable>integer</replaceable> ];
	notify-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ]
	    [ dscp <replaceable>integer</replaceable> ];
	notify-to-soa <replaceable>boolean</replaceable>;
	pubkey <replaceable>integer</replaceable> <replaceable>integer</replaceable>
	request-expire <replaceable>boolean</replaceable>;
	request-ixfr <replaceable>boolean</replaceable>;
	serial-update-method ( date | increment | unixtime );
	server-addresses { ( <replaceable>ipv4_address</replaceable> | <replaceable>ipv6_address</replaceable> ) [ port
	    <replaceable>integer</replaceable> ]; ... };
	server-names { <replaceable>quoted_string</replaceable>; ... };
	sig-signing-nodes <replaceable>integer</replaceable>;
	sig-signing-signatures <replaceable>integer</replaceable>;
	sig-signing-type <replaceable>integer</replaceable>;
	sig-validity-interval <replaceable>integer</replaceable> [ <replaceable>integer</replaceable> ];
	transfer-source ( <replaceable>ipv4_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * ) ] [
	    dscp <replaceable>integer</replaceable> ];
	transfer-source-v6 ( <replaceable>ipv6_address</replaceable> | * ) [ port ( <replaceable>integer</replaceable> | * )
	    ] [ dscp <replaceable>integer</replaceable> ];
	try-tcp-refresh <replaceable>boolean</replaceable>;
	type ( delegation-only | forward | hint | master | redirect | slave
	    | static-stub | stub );
	update-check-ksk <replaceable>boolean</replaceable>;
	update-policy ( local | { ( deny | grant ) <replaceable>string</replaceable> ( 6to4-self |
	    external | krb5-self | krb5-subdomain | ms-self | ms-subdomain
	    | name | self | selfsub | selfwild | subdomain | tcp-self |
	    wildcard | zonesub ) [ <replaceable>string</replaceable> ] <replaceable>rrtypelist</replaceable>; ... };
	use-alt-transfer-source <replaceable>boolean</replaceable>;
	zero-no-soa-ttl <replaceable>boolean</replaceable>;
	zone-statistics ( full | terse | none | <replaceable>boolean</replaceable> );
};
</literallayout>
  </refsection>

  <refsection><info><title>FILES</title></info>

    <para><filename>/etc/named.conf</filename>
    </para>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
	<refentrytitle>ddns-confgen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>named</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>named-checkconf</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>rndc</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>rndc-confgen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>.
    </para>
  </refsection>

</refentry>
