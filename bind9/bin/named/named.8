.\" Copyright (C) 2000, 2001, 2003-2009, 2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: named
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-02-19
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "NAMED" "8" "2014\-02\-19" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
named \- Internet domain name server
.SH "SYNOPSIS"
.HP \w'\fBnamed\fR\ 'u
\fBnamed\fR [[\fB\-4\fR] | [\fB\-6\fR]] [\fB\-c\ \fR\fB\fIconfig\-file\fR\fR] [\fB\-d\ \fR\fB\fIdebug\-level\fR\fR] [\fB\-D\ \fR\fB\fIstring\fR\fR] [\fB\-E\ \fR\fB\fIengine\-name\fR\fR] [\fB\-f\fR] [\fB\-g\fR] [\fB\-L\ \fR\fB\fIlogfile\fR\fR] [\fB\-M\ \fR\fB\fIoption\fR\fR] [\fB\-m\ \fR\fB\fIflag\fR\fR] [\fB\-n\ \fR\fB\fI#cpus\fR\fR] [\fB\-p\ \fR\fB\fIport\fR\fR] [\fB\-s\fR] [\fB\-S\ \fR\fB\fI#max\-socks\fR\fR] [\fB\-t\ \fR\fB\fIdirectory\fR\fR] [\fB\-U\ \fR\fB\fI#listeners\fR\fR] [\fB\-u\ \fR\fB\fIuser\fR\fR] [\fB\-v\fR] [\fB\-V\fR] [\fB\-X\ \fR\fB\fIlock\-file\fR\fR] [\fB\-x\ \fR\fB\fIcache\-file\fR\fR]
.SH "DESCRIPTION"
.PP
\fBnamed\fR
is a Domain Name System (DNS) server, part of the BIND 9 distribution from ISC\&. For more information on the DNS, see RFCs 1033, 1034, and 1035\&.
.PP
When invoked without arguments,
\fBnamed\fR
will read the default configuration file
/etc/named\&.conf, read any initial data, and listen for queries\&.
.SH "OPTIONS"
.PP
\-4
.RS 4
Use IPv4 only even if the host machine is capable of IPv6\&.
\fB\-4\fR
and
\fB\-6\fR
are mutually exclusive\&.
.RE
.PP
\-6
.RS 4
Use IPv6 only even if the host machine is capable of IPv4\&.
\fB\-4\fR
and
\fB\-6\fR
are mutually exclusive\&.
.RE
.PP
\-c \fIconfig\-file\fR
.RS 4
Use
\fIconfig\-file\fR
as the configuration file instead of the default,
/etc/named\&.conf\&. To ensure that reloading the configuration file continues to work after the server has changed its working directory due to to a possible
\fBdirectory\fR
option in the configuration file,
\fIconfig\-file\fR
should be an absolute pathname\&.
.RE
.PP
\-d \fIdebug\-level\fR
.RS 4
Set the daemon\*(Aqs debug level to
\fIdebug\-level\fR\&. Debugging traces from
\fBnamed\fR
become more verbose as the debug level increases\&.
.RE
.PP
\-D \fIstring\fR
.RS 4
Specifies a string that is used to identify a instance of
\fBnamed\fR
in a process listing\&. The contents of
\fIstring\fR
are not examined\&.
.RE
.PP
\-E \fIengine\-name\fR
.RS 4
When applicable, specifies the hardware to use for cryptographic operations, such as a secure key store used for signing\&.
.sp
When BIND is built with OpenSSL PKCS#11 support, this defaults to the string "pkcs11", which identifies an OpenSSL engine that can drive a cryptographic accelerator or hardware service module\&. When BIND is built with native PKCS#11 cryptography (\-\-enable\-native\-pkcs11), it defaults to the path of the PKCS#11 provider library specified via "\-\-with\-pkcs11"\&.
.RE
.PP
\-f
.RS 4
Run the server in the foreground (i\&.e\&. do not daemonize)\&.
.RE
.PP
\-g
.RS 4
Run the server in the foreground and force all logging to
stderr\&.
.RE
.PP
\-L \fIlogfile\fR
.RS 4
Log to the file
\fBlogfile\fR
by default instead of the system log\&.
.RE
.PP
\-M \fIoption\fR
.RS 4
Sets the default memory context options\&. Currently the only supported option is
\fIexternal\fR, which causes the internal memory manager to be bypassed in favor of system\-provided memory allocation functions\&.
.RE
.PP
\-m \fIflag\fR
.RS 4
Turn on memory usage debugging flags\&. Possible flags are
\fIusage\fR,
\fItrace\fR,
\fIrecord\fR,
\fIsize\fR, and
\fImctx\fR\&. These correspond to the ISC_MEM_DEBUGXXXX flags described in
<isc/mem\&.h>\&.
.RE
.PP
\-n \fI#cpus\fR
.RS 4
Create
\fI#cpus\fR
worker threads to take advantage of multiple CPUs\&. If not specified,
\fBnamed\fR
will try to determine the number of CPUs present and create one thread per CPU\&. If it is unable to determine the number of CPUs, a single worker thread will be created\&.
.RE
.PP
\-p \fIport\fR
.RS 4
Listen for queries on port
\fIport\fR\&. If not specified, the default is port 53\&.
.RE
.PP
\-s
.RS 4
Write memory usage statistics to
stdout
on exit\&.
.if n \{\
.sp
.\}
.RS 4
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBNote\fR
.ps -1
.br
This option is mainly of interest to BIND 9 developers and may be removed or changed in a future release\&.
.sp .5v
.RE
.RE
.PP
\-S \fI#max\-socks\fR
.RS 4
Allow
\fBnamed\fR
to use up to
\fI#max\-socks\fR
sockets\&. The default value is 4096 on systems built with default configuration options, and 21000 on systems built with "configure \-\-with\-tuning=large"\&.
.if n \{\
.sp
.\}
.RS 4
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBWarning\fR
.ps -1
.br
This option should be unnecessary for the vast majority of users\&. The use of this option could even be harmful because the specified value may exceed the limitation of the underlying system API\&. It is therefore set only when the default configuration causes exhaustion of file descriptors and the operational environment is known to support the specified number of sockets\&. Note also that the actual maximum number is normally a little fewer than the specified value because
\fBnamed\fR
reserves some file descriptors for its internal use\&.
.sp .5v
.RE
.RE
.PP
\-t \fIdirectory\fR
.RS 4
Chroot to
\fIdirectory\fR
after processing the command line arguments, but before reading the configuration file\&.
.if n \{\
.sp
.\}
.RS 4
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBWarning\fR
.ps -1
.br
This option should be used in conjunction with the
\fB\-u\fR
option, as chrooting a process running as root doesn\*(Aqt enhance security on most systems; the way
\fBchroot(2)\fR
is defined allows a process with root privileges to escape a chroot jail\&.
.sp .5v
.RE
.RE
.PP
\-U \fI#listeners\fR
.RS 4
Use
\fI#listeners\fR
worker threads to listen for incoming UDP packets on each address\&. If not specified,
\fBnamed\fR
will calculate a default value based on the number of detected CPUs: 1 for 1 CPU, and the number of detected CPUs minus one for machines with more than 1 CPU\&. This cannot be increased to a value higher than the number of CPUs\&. If
\fB\-n\fR
has been set to a higher value than the number of detected CPUs, then
\fB\-U\fR
may be increased as high as that value, but no higher\&. On Windows, the number of UDP listeners is hardwired to 1 and this option has no effect\&.
.RE
.PP
\-u \fIuser\fR
.RS 4
Setuid to
\fIuser\fR
after completing privileged operations, such as creating sockets that listen on privileged ports\&.
.if n \{\
.sp
.\}
.RS 4
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBNote\fR
.ps -1
.br
On Linux,
\fBnamed\fR
uses the kernel\*(Aqs capability mechanism to drop all root privileges except the ability to
\fBbind(2)\fR
to a privileged port and set process resource limits\&. Unfortunately, this means that the
\fB\-u\fR
option only works when
\fBnamed\fR
is run on kernel 2\&.2\&.18 or later, or kernel 2\&.3\&.99\-pre3 or later, since previous kernels did not allow privileges to be retained after
\fBsetuid(2)\fR\&.
.sp .5v
.RE
.RE
.PP
\-v
.RS 4
Report the version number and exit\&.
.RE
.PP
\-V
.RS 4
Report the version number and build options, and exit\&.
.RE
.PP
\-X \fIlock\-file\fR
.RS 4
Acquire a lock on the specified file at runtime; this helps to prevent duplicate
\fBnamed\fR
instances from running simultaneously\&. Use of this option overrides the
\fBlock\-file\fR
option in
named\&.conf\&. If set to
none, the lock file check is disabled\&.
.RE
.PP
\-x \fIcache\-file\fR
.RS 4
Load data from
\fIcache\-file\fR
into the cache of the default view\&.
.if n \{\
.sp
.\}
.RS 4
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBWarning\fR
.ps -1
.br
This option must not be used\&. It is only of interest to BIND 9 developers and may be removed or changed in a future release\&.
.sp .5v
.RE
.RE
.SH "SIGNALS"
.PP
In routine operation, signals should not be used to control the nameserver;
\fBrndc\fR
should be used instead\&.
.PP
SIGHUP
.RS 4
Force a reload of the server\&.
.RE
.PP
SIGINT, SIGTERM
.RS 4
Shut down the server\&.
.RE
.PP
The result of sending any other signals to the server is undefined\&.
.SH "CONFIGURATION"
.PP
The
\fBnamed\fR
configuration file is too complex to describe in detail here\&. A complete description is provided in the
BIND 9 Administrator Reference Manual\&.
.PP
\fBnamed\fR
inherits the
\fBumask\fR
(file creation mode mask) from the parent process\&. If files created by
\fBnamed\fR, such as journal files, need to have custom permissions, the
\fBumask\fR
should be set explicitly in the script used to start the
\fBnamed\fR
process\&.
.SH "FILES"
.PP
/etc/named\&.conf
.RS 4
The default configuration file\&.
.RE
.PP
/var/run/named/named\&.pid
.RS 4
The default process\-id file\&.
.RE
.SH "SEE ALSO"
.PP
RFC 1033,
RFC 1034,
RFC 1035,
\fBnamed-checkconf\fR(8),
\fBnamed-checkzone\fR(8),
\fBrndc\fR(8),
\fBlwresd\fR(8),
\fBnamed.conf\fR(5),
BIND 9 Administrator Reference Manual\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2000, 2001, 2003-2009, 2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
.br
