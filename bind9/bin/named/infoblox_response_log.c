/*
 * Copyright (c) 2014 Infoblox Inc. All Rights Reserved.
 */

#include <config.h>

#include <isc/buffer.h>
#include <isc/region.h>
#include <isc/util.h>

#include <named/infoblox_response_log.h>

/*
 * Finish the buffer for response logging.
 *
 * This function makes sure 'buffer' ends with a new line character ('\n'),
 * tweaking some of the trailing data in the buffer if necessary:
 * - If 'buffer' ends with a space, it will be replaced with a NL.
 * - Otherwise, if 'buffer' has available space for the NL, it will be appended
 *   to 'buffer'.
 * - Otherwise, if 'buffer' has at least 3 bytes of data, the trailing 3 bytes
 *   will be replaced with '..\n'.
 * - Otherwise, the trailing byte of the 'buffer' data (which always exists,
 *   see below) will be replace with a NL.  Note: this will make the resulting
 *   text look interrupted abruptly, but this case shouldn't happen in practice
 *   in the context this function is supposed to be called (see below).
 *
 * Note that this function does NOT nul-terminate the buffer.
 *
 * The caller must ensure 'buffer' has at least one byte of data.
 *
 * This function is supposed to be called only from ns_client_send() as part
 * of response logging processing, but is defined separately and made public
 * for testing purposes.  It shouldn't be called from other production code.
 */
void
infoblox_response_log_finish(isc_buffer_t *buffer) {
	isc_region_t r;
	unsigned int i;
	/* Remove trailing space, if any. */
	isc_buffer_usedregion(buffer, &r);
	/*
	 * prerequisite requirement: eliminate this case helps keep the code
	 * simpler and avoid testing impossible cases.
	 */
	REQUIRE(r.length > 0);
	if (r.base[r.length - 1] == ' ')
		isc_buffer_subtract(buffer, 1);

	/*
	 * Add a new line.  If we have removed a space above, this should
	 * always succeed; otherwise we know there is at least one byte of
	 * data in the buffer (because of the prerequisite), so we override it
	 * with a NL if necessary.
	 * Note also that infoblox_isc_buffer_putmem() does not always append
	 * the given string: it could replace the last 3 characters with '...'
	 * or even keep the buffer intact if it's very short.
	 */
	infoblox_isc_buffer_putmem(buffer, (const unsigned char*)"\n", 1);
	isc_buffer_usedregion(buffer, &r);
	INSIST(r.length > 0);
	if (r.base[r.length - 1] != '\n')
		r.base[r.length - 1] = '\n';
	if (r.length > 8100) {  // truncate log message
		r.base[8100] = ' ';
		r.base[8101] = '.'; r.base[8102] = '.'; r.base[8103] = '.';
		r.base[8104] = '\n';
		for (i = 8105 ; i < r.length ; i++) {         
			r.base[i] = '\0';
		}
	}
}
