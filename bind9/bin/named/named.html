<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000, 2001, 2003-2009, 2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>named</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.named"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">named</span>
     &#8212; Internet domain name server
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">named</code> 
       [
	[<code class="option">-4</code>]
	 |  [<code class="option">-6</code>]
      ]
       [<code class="option">-c <em class="replaceable"><code>config-file</code></em></code>]
       [<code class="option">-d <em class="replaceable"><code>debug-level</code></em></code>]
       [<code class="option">-D <em class="replaceable"><code>string</code></em></code>]
       [<code class="option">-E <em class="replaceable"><code>engine-name</code></em></code>]
       [<code class="option">-f</code>]
       [<code class="option">-g</code>]
       [<code class="option">-L <em class="replaceable"><code>logfile</code></em></code>]
       [<code class="option">-M <em class="replaceable"><code>option</code></em></code>]
       [<code class="option">-m <em class="replaceable"><code>flag</code></em></code>]
       [<code class="option">-n <em class="replaceable"><code>#cpus</code></em></code>]
       [<code class="option">-p <em class="replaceable"><code>port</code></em></code>]
       [<code class="option">-s</code>]
       [<code class="option">-S <em class="replaceable"><code>#max-socks</code></em></code>]
       [<code class="option">-t <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-U <em class="replaceable"><code>#listeners</code></em></code>]
       [<code class="option">-u <em class="replaceable"><code>user</code></em></code>]
       [<code class="option">-v</code>]
       [<code class="option">-V</code>]
       [<code class="option">-X <em class="replaceable"><code>lock-file</code></em></code>]
       [<code class="option">-x <em class="replaceable"><code>cache-file</code></em></code>]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>named</strong></span>
      is a Domain Name System (DNS) server,
      part of the BIND 9 distribution from ISC.  For more
      information on the DNS, see RFCs 1033, 1034, and 1035.
    </p>
    <p>
      When invoked without arguments, <span class="command"><strong>named</strong></span>
      will
      read the default configuration file
      <code class="filename">/etc/named.conf</code>, read any initial
      data, and listen for queries.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-4</span></dt>
<dd>
          <p>
            Use IPv4 only even if the host machine is capable of IPv6.
            <code class="option">-4</code> and <code class="option">-6</code> are mutually
            exclusive.
          </p>
        </dd>
<dt><span class="term">-6</span></dt>
<dd>
          <p>
            Use IPv6 only even if the host machine is capable of IPv4.
            <code class="option">-4</code> and <code class="option">-6</code> are mutually
            exclusive.
          </p>
        </dd>
<dt><span class="term">-c <em class="replaceable"><code>config-file</code></em></span></dt>
<dd>
          <p>
            Use <em class="replaceable"><code>config-file</code></em> as the
            configuration file instead of the default,
            <code class="filename">/etc/named.conf</code>.  To
            ensure that reloading the configuration file continues
            to work after the server has changed its working
            directory due to to a possible
            <code class="option">directory</code> option in the configuration
            file, <em class="replaceable"><code>config-file</code></em> should be
            an absolute pathname.
          </p>
        </dd>
<dt><span class="term">-d <em class="replaceable"><code>debug-level</code></em></span></dt>
<dd>
          <p>
            Set the daemon's debug level to <em class="replaceable"><code>debug-level</code></em>.
            Debugging traces from <span class="command"><strong>named</strong></span> become
            more verbose as the debug level increases.
          </p>
        </dd>
<dt><span class="term">-D <em class="replaceable"><code>string</code></em></span></dt>
<dd>
          <p>
            Specifies a string that is used to identify a instance of
            <span class="command"><strong>named</strong></span> in a process listing.  The contents
            of <em class="replaceable"><code>string</code></em> are
            not examined.
          </p>
        </dd>
<dt><span class="term">-E <em class="replaceable"><code>engine-name</code></em></span></dt>
<dd>
          <p>
            When applicable, specifies the hardware to use for
            cryptographic operations, such as a secure key store used
            for signing.
          </p>
          <p>
            When BIND is built with OpenSSL PKCS#11 support, this defaults
            to the string "pkcs11", which identifies an OpenSSL engine
            that can drive a cryptographic accelerator or hardware service
            module.  When BIND is built with native PKCS#11 cryptography
            (--enable-native-pkcs11), it defaults to the path of the PKCS#11
            provider library specified via "--with-pkcs11".
          </p>
        </dd>
<dt><span class="term">-f</span></dt>
<dd>
          <p>
            Run the server in the foreground (i.e. do not daemonize).
          </p>
        </dd>
<dt><span class="term">-g</span></dt>
<dd>
          <p>
            Run the server in the foreground and force all logging
            to <code class="filename">stderr</code>.
          </p>
        </dd>
<dt><span class="term">-L <em class="replaceable"><code>logfile</code></em></span></dt>
<dd>
          <p>
            Log to the file <code class="option">logfile</code> by default
            instead of the system log.
          </p>
        </dd>
<dt><span class="term">-M <em class="replaceable"><code>option</code></em></span></dt>
<dd>
          <p>
            Sets the default memory context options.  Currently
            the only supported option is
            <em class="replaceable"><code>external</code></em>,
            which causes the internal memory manager to be bypassed
            in favor of system-provided memory allocation functions.
          </p>
        </dd>
<dt><span class="term">-m <em class="replaceable"><code>flag</code></em></span></dt>
<dd>
          <p>
            Turn on memory usage debugging flags.  Possible flags are
            <em class="replaceable"><code>usage</code></em>,
            <em class="replaceable"><code>trace</code></em>,
            <em class="replaceable"><code>record</code></em>,
            <em class="replaceable"><code>size</code></em>, and
            <em class="replaceable"><code>mctx</code></em>.
            These correspond to the ISC_MEM_DEBUGXXXX flags described in
            <code class="filename">&lt;isc/mem.h&gt;</code>.
          </p>
        </dd>
<dt><span class="term">-n <em class="replaceable"><code>#cpus</code></em></span></dt>
<dd>
          <p>
            Create <em class="replaceable"><code>#cpus</code></em> worker threads
            to take advantage of multiple CPUs.  If not specified,
            <span class="command"><strong>named</strong></span> will try to determine the
            number of CPUs present and create one thread per CPU.
            If it is unable to determine the number of CPUs, a
            single worker thread will be created.
          </p>
        </dd>
<dt><span class="term">-p <em class="replaceable"><code>port</code></em></span></dt>
<dd>
          <p>
            Listen for queries on port <em class="replaceable"><code>port</code></em>.  If not
            specified, the default is port 53.
          </p>
        </dd>
<dt><span class="term">-s</span></dt>
<dd>
          <p>
            Write memory usage statistics to <code class="filename">stdout</code> on exit.
          </p>
          <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
<h3 class="title">Note</h3>
            <p>
              This option is mainly of interest to BIND 9 developers
              and may be removed or changed in a future release.
            </p>
          </div>
        </dd>
<dt><span class="term">-S <em class="replaceable"><code>#max-socks</code></em></span></dt>
<dd>
          <p>
            Allow <span class="command"><strong>named</strong></span> to use up to
            <em class="replaceable"><code>#max-socks</code></em> sockets.
            The default value is 4096 on systems built with default
            configuration options, and 21000 on systems built with
            "configure --with-tuning=large".
          </p>
          <div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;">
<h3 class="title">Warning</h3>
            <p>
              This option should be unnecessary for the vast majority
              of users.
              The use of this option could even be harmful because the
              specified value may exceed the limitation of the
              underlying system API.
              It is therefore set only when the default configuration
              causes exhaustion of file descriptors and the
              operational environment is known to support the
              specified number of sockets.
              Note also that the actual maximum number is normally a little
              fewer than the specified value because
              <span class="command"><strong>named</strong></span> reserves some file descriptors
              for its internal use.
            </p>
          </div>
        </dd>
<dt><span class="term">-t <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
          <p>Chroot
            to <em class="replaceable"><code>directory</code></em> after
            processing the command line arguments, but before
            reading the configuration file.
          </p>
          <div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;">
<h3 class="title">Warning</h3>
            <p>
              This option should be used in conjunction with the
              <code class="option">-u</code> option, as chrooting a process
              running as root doesn't enhance security on most
              systems; the way <code class="function">chroot(2)</code> is
              defined allows a process with root privileges to
              escape a chroot jail.
            </p>
          </div>
        </dd>
<dt><span class="term">-U <em class="replaceable"><code>#listeners</code></em></span></dt>
<dd>
          <p>
            Use <em class="replaceable"><code>#listeners</code></em>
            worker threads to listen for incoming UDP packets on each
            address.  If not specified, <span class="command"><strong>named</strong></span> will
            calculate a default value based on the number of detected
            CPUs: 1 for 1 CPU, and the number of detected CPUs
            minus one for machines with more than 1 CPU. This cannot
            be increased to a value higher than the number of CPUs.
            If <code class="option">-n</code> has been set to a higher value than
            the number of detected CPUs, then <code class="option">-U</code> may
            be increased as high as that value, but no higher.
            On Windows, the number of UDP listeners is hardwired to 1
            and this option has no effect.
          </p>
        </dd>
<dt><span class="term">-u <em class="replaceable"><code>user</code></em></span></dt>
<dd>
          <p>Setuid
            to <em class="replaceable"><code>user</code></em> after completing
            privileged operations, such as creating sockets that
            listen on privileged ports.
          </p>
          <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
<h3 class="title">Note</h3>
            <p>
              On Linux, <span class="command"><strong>named</strong></span> uses the kernel's
                        capability mechanism to drop all root privileges
              except the ability to <code class="function">bind(2)</code> to
              a
              privileged port and set process resource limits.
              Unfortunately, this means that the <code class="option">-u</code>
              option only works when <span class="command"><strong>named</strong></span> is
              run
              on kernel 2.2.18 or later, or kernel 2.3.99-pre3 or
              later, since previous kernels did not allow privileges
              to be retained after <code class="function">setuid(2)</code>.
            </p>
          </div>
        </dd>
<dt><span class="term">-v</span></dt>
<dd>
          <p>
            Report the version number and exit.
          </p>
        </dd>
<dt><span class="term">-V</span></dt>
<dd>
          <p>
            Report the version number and build options, and exit.
          </p>
        </dd>
<dt><span class="term">-X <em class="replaceable"><code>lock-file</code></em></span></dt>
<dd>
          <p>
            Acquire a lock on the specified file at runtime; this
            helps to prevent duplicate <span class="command"><strong>named</strong></span> instances
            from running simultaneously.
            Use of this option overrides the <span class="command"><strong>lock-file</strong></span>
            option in <code class="filename">named.conf</code>.
            If set to <code class="literal">none</code>, the lock file check
            is disabled.
          </p>
        </dd>
<dt><span class="term">-x <em class="replaceable"><code>cache-file</code></em></span></dt>
<dd>
          <p>
            Load data from <em class="replaceable"><code>cache-file</code></em> into the
            cache of the default view.
          </p>
          <div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;">
<h3 class="title">Warning</h3>
            <p>
              This option must not be used.  It is only of interest
              to BIND 9 developers and may be removed or changed in a
              future release.
            </p>
          </div>
        </dd>
</dl></div>

  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>SIGNALS</h2>

    <p>
      In routine operation, signals should not be used to control
      the nameserver; <span class="command"><strong>rndc</strong></span> should be used
      instead.
    </p>

    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">SIGHUP</span></dt>
<dd>
          <p>
            Force a reload of the server.
          </p>
        </dd>
<dt><span class="term">SIGINT, SIGTERM</span></dt>
<dd>
          <p>
            Shut down the server.
          </p>
        </dd>
</dl></div>

    <p>
      The result of sending any other signals to the server is undefined.
    </p>

  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>CONFIGURATION</h2>

    <p>
      The <span class="command"><strong>named</strong></span> configuration file is too complex
      to describe in detail here.  A complete description is provided
      in the
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>.
    </p>

    <p>
      <span class="command"><strong>named</strong></span> inherits the <code class="function">umask</code>
      (file creation mode mask) from the parent process. If files
      created by <span class="command"><strong>named</strong></span>, such as journal files,
      need to have custom permissions, the <code class="function">umask</code>
      should be set explicitly in the script used to start the
      <span class="command"><strong>named</strong></span> process.
    </p>

  </div>

  <div class="refsection">
<a name="id-1.11"></a><h2>FILES</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term"><code class="filename">/etc/named.conf</code></span></dt>
<dd>
          <p>
            The default configuration file.
          </p>
        </dd>
<dt><span class="term"><code class="filename">/var/run/named/named.pid</code></span></dt>
<dd>
          <p>
            The default process-id file.
          </p>
        </dd>
</dl></div>

  </div>

  <div class="refsection">
<a name="id-1.12"></a><h2>SEE ALSO</h2>

    <p><em class="citetitle">RFC 1033</em>,
      <em class="citetitle">RFC 1034</em>,
      <em class="citetitle">RFC 1035</em>,
      <span class="citerefentry">
        <span class="refentrytitle">named-checkconf</span>
        (8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">named-checkzone</span>
        (8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">rndc</span>
        (8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">lwresd</span>
        (8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">named.conf</span>
        (5)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>.
    </p>
  </div>

</div></body>
</html>
