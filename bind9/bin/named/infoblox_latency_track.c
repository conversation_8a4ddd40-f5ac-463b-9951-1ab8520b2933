/*
 * Copyright (c) 2013 Infoblox Inc. All Rights Reserved.
 */

#include <unistd.h>
#include <sys/time.h>

#include <isc/random.h>
#include <isc/types.h>
#include <isc/condition.h>
#include <isc/netaddr.h>
#include <isc/sockaddr.h>
#include <isc/radix.h>
#include <isc/thread.h>
#include <isc/task.h>
#include <isc/time.h>
#include <isc/timer.h>
#include <isc/util.h>

#include <dns/view.h>
#include <dns/zone.h>
#include <dns/request.h>
#include <dns/dispatch.h>
#include <named/globals.h>
#include <named/log.h>

#include <dns/infoblox_ha_state.h>
#include <dns/infoblox_onedb.h>
#include <dns/infoblox_latency_tree.h>
#include <named/infoblox_latency_track.h>

/* macros to check if a pointer represents a proper latency tracking manager */
#define LMMAGIC			ISC_MAGIC('L', 'A', 'T', 'M')
#define VALID_LM(lm)		ISC_MAGIC_VALID((lm), (LMMAGIC))

/* Callback type to iterate through tree payloads */
typedef isc_result_t (*ib_ltinfo_cb)(infoblox_lt_mgr_t *mgr, infoblox_lt_info_t *info);
static isc_result_t infoblox_for_each_latency(infoblox_lt_mgr_t *mgr, ib_ltinfo_cb cb, const char *action);
static void destroy(infoblox_lt_mgr_t *mgr);

/*
 * Context of an outstanding latency query.
 * This is used so that we can cancel outstanding queries on manager shutdown
 * and that the manager won't be destroyed with outstanding queries.
 * It also records the start time of each query for calculating the latency on
 * completion.
 */
struct query_state {
	ISC_LINK(struct query_state) link;
	dns_request_t *request;
	isc_time_t start;
	infoblox_lt_mgr_t *mgr;
};
typedef struct query_state query_state_t;

// latency update manager
struct infoblox_lt_mgr {
	/* Unlocked: initialized once, never changed */
	unsigned	magic;
	isc_mem_t	*mctx;
	isc_mutex_t	lock;
	infoblox_lt_tree_t *lt_tree;
	isc_task_t	*task;
	isc_thread_t	thread_id;
	isc_event_t	ctlevent;

	isc_refcount_t references;

	// Locked by 'lock'
	isc_condition_t	wakeup;
	int		done;

	// needed for SOA queries
	ISC_LIST(struct query_state) queries; /* outstanding SOA query info */
	dns_requestmgr_t *requestmgr;
};

infoblox_lt_mgr_t *ib_ltmgr = NULL;

/*
 * Called after the external destroy of the latency manager.  At this point
 * the updater thread has been terminated, and no more queries can be inserted
 * to the manager's list.  Also, since this callback shares the task with
 * update_rtt(), there can be no race with it.  So we don't have to lock the
 * manager.  This is important because dns_request_cancel() will acquire its
 * internal lock, while update_rtt() is called with that lock held and acquires
 * the manager lock.
 */
static void
mgr_shutdown(isc_task_t *task, isc_event_t *event) {
	infoblox_lt_mgr_t *mgr = event->ev_arg;
	query_state_t *qstate;

	REQUIRE(VALID_LM(mgr));
	INSIST(task == mgr->task);
	INSIST(mgr->done);

	for (qstate = ISC_LIST_HEAD(mgr->queries);
	     qstate != NULL;
	     qstate = ISC_LIST_NEXT(qstate, link))
	{
		dns_request_cancel(qstate->request);
	}

	isc_int32_t refs;
	isc_refcount_decrement(&mgr->references, &refs);
	if (refs == 0) {
		destroy(mgr);
	}
}

/*
 * A helper for update_rtt(), calculating the updated RTT (latency) of a
 * master based on the start and end times of the probe query, the previous
 * (old) RTT, and whether to penalize the master (in case the query fails).
 *
 * This function returns the updated RTT in microseconds.
 *
 * This function allows the case where 'start' is later than 'end'.  It's
 * basically unexpected in our usage, but might still happen if the system clock
 * is tweaked (NTP adjustment, in a VM environment, etc).  In this case this
 * function returns 'old'.
 *
 * This function is essentially a private subroutine in this file, but is
 * defined as public for testing purposes.  It shouldn't be used in the
 * production code outside of this file.
 */
isc_uint64_t
infoblox_latency_calc(const isc_time_t *start, const isc_time_t *end,
		      isc_uint64_t old, isc_boolean_t penalize)
{
	isc_uint64_t adjusted;
	if (penalize) {
		/* increase RTT up to a limit */
		adjusted = ISC_MIN(old + IBLT_RTT_INCREASE_ON_FAILURE,
				   IBLT_MAX_RTT);
	} else {
		/*
		 * update as following: adjusted rtt = 0.3*old_rtt + 0.7*new_rtt
		 * assuming maximum RTT is far lower than the upper limit for
		 * rtt data type; otherwise, we would need to pay care for
		 * possible overflows.
		 */
		if (isc_time_compare(end, start) < 0) {
			adjusted = old;
		} else {
			adjusted =
				(3*old + 7*isc_time_microdiff(end, start))/10;
		}
	}

	return (adjusted);
}

/*
 * Callback function called on each SOA query complete.
 * There are two possible cases:
 *	1) response was retrieved before timeout;
 *	2) timeout occurred before getting a response.
 *
 * Note:
 * qstate->mgr is assigned before request is created in infoblox_update_latency(),
 * so it is a valid pointer when update_rtt() is called. However,
 * qstate->request, which is assigned after infoblox_update_latency()->dns_request_create()
 * returned, may not be set when this function is invoked. So it's essential to
 * acquire mgr->lock before accessing qstate->request.
 */
static void
update_rtt(isc_task_t *task, isc_event_t *event) {
	UNUSED(task);

	dns_requestevent_t *reqev = (dns_requestevent_t *)event;
	query_state_t *qstate = (query_state_t *)event->ev_arg;
	INSIST(qstate != NULL);
	infoblox_lt_mgr_t *mgr = qstate->mgr;
	REQUIRE(VALID_LM(mgr));

	if (reqev->result == ISC_R_CANCELED)
		goto cleanup;

	isc_result_t result = ISC_R_SUCCESS;
	char server_ip[ISC_SOCKADDR_FORMATSIZE];

	/* record time right away */
	isc_time_t now;
	TIME_NOW(&now);

	isc_sockaddr_t *destaddr = NULL;
	destaddr = infoblox_get_dns_request_destaddr(reqev->request);
	isc_sockaddr_format(destaddr, server_ip, sizeof(server_ip));

	isc_prefix_t prefix;
	infoblox_prefix_from_sockaddr(destaddr, &prefix);

	isc_uint64_t old_rtt, new_rtt;
	isc_radix_node_t *node = NULL;
	infoblox_lt_info_t *ndata = NULL;
	result = infoblox_isc_radix_search(mgr->lt_tree->radix, &node, &prefix,
					   (void **)&ndata);
	if (node == NULL)
	{
		infoblox_log(1, "failed to acquire an expected node in latency tree for %s", server_ip);
		goto cleanup;
	}

	if (ndata == NULL)
	{
		infoblox_log(1, "failed to acquire an expected data in latency tree for %s", server_ip);
		goto cleanup;
	}

	// we don't check that response is correct here; just simple status of the response will suffice
	// we will do more detailed check later when actually doing master switch attempt

	RWLOCK(&mgr->lt_tree->rwlock, isc_rwlocktype_write);

	old_rtt = ndata->rtt;
	new_rtt = infoblox_latency_calc(&qstate->start, &now, old_rtt,
					reqev->result != ISC_R_SUCCESS);
	ndata->rtt = new_rtt;

	/*
	 * Mark the master as "unreachable" on any request event failure.
	 * Note that this could be a false positive since it's a UDP query
	 * (which can be lost even for a "reachable" destination) and can also
	 * fail for a local reason, not specific to the remote master.  In the
	 * initial implementation we are not really sure if this is sufficient
	 * in practice or we need more precise and reliable determination
	 * logic; we can extend it as we have more experiences.
	 */
	ndata->unreachable = ISC_TF(reqev->result != ISC_R_SUCCESS);
	RWUNLOCK(&mgr->lt_tree->rwlock, isc_rwlocktype_write);

	infoblox_log(4, "Latency test to primary %s %s."
		     " RTT updated from %lld usec to %lld usec",
		     server_ip,
		     (reqev->result == ISC_R_SUCCESS) ? "succeeded" : "failed",
		     old_rtt, new_rtt);

  cleanup:
  /* Acquire mgr->lock before accessing qstate->request.
   * See comments at the beginning of this function.*/
	LOCK(&mgr->lock);
	INSIST(reqev->request == qstate->request);
	dns_request_destroy(&reqev->request);
	isc_event_free(&event);

	ISC_LIST_UNLINK(mgr->queries, qstate, link);
	UNLOCK(&mgr->lock);
	isc_mem_put(mgr->mctx, qstate, sizeof(*qstate));

	isc_int32_t refs;
	isc_refcount_decrement(&mgr->references, &refs);
	if (refs == 0 && mgr->done) {
		destroy(mgr);
	}
}

/*
 * Function to prepare SOA query for a server, send it with timeout
 * and set up a callback for RTT processing.
 * The caller must hold the mgr lock (or ensure serialization in other way,
 * in case it's called from a test).
 */
static isc_result_t
infoblox_update_latency(infoblox_lt_mgr_t *mgr, infoblox_lt_info_t *info)
{
	isc_result_t result = ISC_R_SUCCESS;
	query_state_t *qstate = NULL;

	REQUIRE(VALID_LM(mgr));
	REQUIRE(info != NULL);

	dns_message_t *query = NULL;
	result = dns_message_create(mgr->mctx, DNS_MESSAGE_INTENTRENDER, &query);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	query->opcode = dns_opcode_query;
	query->rdclass = dns_rdataclass_in;
	/*
	 * Random value should be good enough, no Kaminsky-style attack
	 * expected to be applicable. This is because id uniqueness is
	 * local to each server. Generally, we should not expect that
	 * there are multiple SOA queries running through the network
	 * for each server from latency tracking code. We do one query
	 * per server, wait till timeout or response, sleep, then repeat
	 * from start. There is no place for multiple SOA queries.
	 */
	isc_uint32_t random;
	isc_random_get(&random);
	query->id = (random & 0xffff);

	dns_name_t *qname = NULL;
	result = dns_message_gettempname(query, &qname);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	dns_name_init(qname, NULL);
	dns_name_clone(&info->zone_name, qname);
	dns_message_addname(query, qname, DNS_SECTION_QUESTION);

	dns_rdataset_t *qrdataset = NULL;
	result = dns_message_gettemprdataset(query, &qrdataset);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	dns_rdataset_init(qrdataset);
	dns_rdataset_makequestion(qrdataset, dns_rdataclass_in,
				  dns_rdatatype_soa);
	ISC_LIST_APPEND(qname->list, qrdataset, link);

	qstate = isc_mem_get(mgr->mctx, sizeof(*qstate));
	if (qstate == NULL)
		goto cleanup;

	/* Assign mgr to qstate->mgr before calling dns_request_create().
	 * Because update_rtt() may be invoked before dns_request_create() returns
	 * if there is thread switching inside. In such case qstate->request may not
	 * have been assigned and we need acquire qstate->mgr->lock before
	 * accessing qstate->request in update_rtt().
	 * Be aware that mgr->lock is released by the caller of infoblox_update_latency().
	 * */
	ISC_LINK_INIT(qstate, link);
	qstate->mgr = mgr;
	TIME_NOW(&qstate->start);

	dns_request_t *request = NULL;
	result = dns_request_create(mgr->requestmgr, query, &info->addr, 0,
				    NULL /* tsigkey */,
				    infoblox_latency_response_timeout(),
				    mgr->task, update_rtt, qstate /* arg */,
				    &request);
	if (result == ISC_R_SUCCESS) {
		qstate->request = request;
		isc_refcount_increment(&(mgr->references), NULL);
		ISC_LIST_APPEND(mgr->queries, qstate, link);
		qstate = NULL;	/* reset to avoid duplicate free */
	}

cleanup:
	if (query != NULL)
		dns_message_destroy(&query);
	if (qstate != NULL)
		isc_mem_put(mgr->mctx, qstate, sizeof(*qstate));
	return result;
}

/*
 * Trivial wrapper of an internal function infoblox_update_latency() to test it.
 *
 * Must not be called from production code.
 */
isc_result_t
ibtest_update_latency_wrapper(infoblox_lt_mgr_t *mgr, infoblox_lt_info_t *info)
{
	return (infoblox_update_latency(mgr, info));
}

static isc_threadresult_t
infoblox_lt_mgr_run(void *arg)
{
	infoblox_lt_mgr_t *mgr = (infoblox_lt_mgr_t *) arg;
	isc_interval_t interval;
	isc_time_t wakeup_time;
	isc_result_t result = ISC_R_SUCCESS;

	isc_interval_set(&interval, /* sec= */ infoblox_latency_poll_interval(), /* nsec= */ 0);

	LOCK(&mgr->lock);

	while (!mgr->done) {
		result = isc_time_nowplusinterval(&wakeup_time, &interval);
		if (result != ISC_R_SUCCESS) {
			UNLOCK(&mgr->lock);
			return ((isc_threadresult_t)(uintptr_t)result);
		}

		result = isc_condition_waituntil(&mgr->wakeup, &mgr->lock, &wakeup_time);
		if (result == ISC_R_TIMEDOUT) {
			// Normal case
		} else if (result != ISC_R_SUCCESS) {
			UNLOCK (&mgr->lock);
			return ((isc_threadresult_t)(uintptr_t)result);
		}

		if (mgr->done)
			break;

		//(void) infoblox_dump_server_latencies(mgr);

		/*
		 * TODO-MMDNS: currently we send all SOA queries in one
		 * go (only once per server assigned to any multi master
		 * secondary zone with ib-multi-master = automatic).
		 * This can become a problem if the number of servers to
		 * track latency is huge (like thousand or so). If this
		 * is the expected case, we may try to spread requests
		 * through the time till the next queries cycle occurs.
		 */

		// skip latency data collection in passive HA mode
		if (infoblox_get_is_ha_active())
			(void) infoblox_for_each_latency(mgr, infoblox_update_latency, "update");
		//infoblox_log(1, "LM: still running...");

	}
	UNLOCK(&mgr->lock);

	return ((isc_threadresult_t)ISC_R_SUCCESS);
}

isc_result_t
infoblox_lt_mgr_create(isc_mem_t *mctx, infoblox_lt_tree_t *tree,
		       isc_taskmgr_t *taskmgr, dns_requestmgr_t *requestmgr,
		       infoblox_lt_mgr_t **mgrp)
{
	isc_result_t result = ISC_R_SUCCESS;
	infoblox_lt_mgr_t *mgr = NULL;
	isc_boolean_t lock_initialized = ISC_FALSE;
	isc_boolean_t wakeup_initialized = ISC_FALSE;

	REQUIRE(ISCAPI_MCTX_VALID(mctx));
	REQUIRE(VALID_LT(tree));
	REQUIRE(mgrp != NULL && *mgrp == NULL);
	REQUIRE(taskmgr != NULL);
	REQUIRE(requestmgr != NULL);

	mgr = isc_mem_get(mctx, sizeof(*mgr));
	if (mgr == NULL)
		return ISC_R_NOMEMORY;
	memset(mgr, 0, sizeof(*mgr));

	mgr->magic = LMMAGIC;
	isc_refcount_init(&mgr->references, 1);

	result = isc_mutex_init(&mgr->lock);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	lock_initialized = ISC_TRUE;

	result = isc_condition_init(&mgr->wakeup);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	wakeup_initialized = ISC_TRUE;

	isc_mem_attach(mctx, &mgr->mctx);
	result = isc_task_create(taskmgr, 0, &mgr->task);
	if (result != ISC_R_SUCCESS)
		goto cleanup;
	dns_requestmgr_attach(requestmgr, &mgr->requestmgr);

	ISC_EVENT_INIT(&mgr->ctlevent, sizeof(mgr->ctlevent), 0, NULL,
		       ISC_TASKEVENT_SHUTDOWN, mgr_shutdown, mgr, mgr,
		       NULL, NULL);

	ISC_LIST_INIT(mgr->queries);
	infoblox_lt_tree_attach(tree, &mgr->lt_tree);

	result = isc_thread_create(infoblox_lt_mgr_run, mgr, &mgr->thread_id);
	if (result != ISC_R_SUCCESS)
		goto cleanup;

	*mgrp = mgr;

	return result;

cleanup:
	if (mgr)
	{
		if (mgr->requestmgr)
			dns_requestmgr_detach(&mgr->requestmgr);
		if (mgr->mctx)
			isc_mem_detach(&mgr->mctx);
		if (mgr->task != NULL)
			isc_task_detach(&mgr->task);
		if (wakeup_initialized)
			(void) isc_condition_destroy(&mgr->wakeup);
		if (lock_initialized)
			(void) isc_mutex_destroy(&mgr->lock);
		isc_mem_put(mctx, mgr, sizeof(*mgr));
	}
	return (result);
}

void
infoblox_lt_mgr_destroy(infoblox_lt_mgr_t **mgrp)
{
	isc_event_t *ev;

	REQUIRE(mgrp != NULL);
	if (*mgrp == NULL)
		return;
	REQUIRE(VALID_LM(*mgrp));

	LOCK(&(*mgrp)->lock);
	(*mgrp)->done = 1;
	SIGNAL(&(*mgrp)->wakeup);
	UNLOCK(&(*mgrp)->lock);

	infoblox_signal_thread((*mgrp)->thread_id);

	/*
	 * Synchronize with the update thread.  This ensures there'll be no
	 * more outstanding queries.
	 */
	isc_result_t result;
	result = isc_thread_join((*mgrp)->thread_id, NULL);
	if (result != ISC_R_SUCCESS) {
		UNEXPECTED_ERROR(__FILE__, __LINE__,
				 "infoblox_lt_mgr_destroy: "
				 "isc_thread_join() => %s",
				 isc_result_totext(result));
	}

	/*
	 * Leave the rest of the cleanup to a separate event.  See
	 * mgr_shutdown() regarding why we need it.
	 */
	ev = &(*mgrp)->ctlevent;
	isc_task_send((*mgrp)->task, &ev);

	*mgrp = NULL;
}

static void
destroy(infoblox_lt_mgr_t *mgr) {
	isc_mem_t *mctx;

	INSIST(isc_refcount_current(&mgr->references) == 0);

	dns_requestmgr_detach(&mgr->requestmgr);
	isc_task_detach(&mgr->task);

	INSIST(ISC_LIST_EMPTY(mgr->queries));
	infoblox_lt_tree_detach(&mgr->lt_tree);

	(void) isc_condition_destroy(&mgr->wakeup);
	(void) isc_mutex_destroy(&mgr->lock);

	mctx = mgr->mctx;
	isc_mem_put(mctx, mgr, sizeof(*mgr));
	isc_mem_detach(&mctx);
}

/*
 * Function to iterate through all tree payloads
 *
 * cb:     callback function called for each payload.
 * action: used to log in case of failure.
 */
static isc_result_t
infoblox_for_each_latency(infoblox_lt_mgr_t *mgr, ib_ltinfo_cb cb, const char *action)
{
	isc_result_t result = ISC_R_SUCCESS;

	REQUIRE(cb != NULL && action != NULL);

	REQUIRE(VALID_LM(mgr));

	RWLOCK(&mgr->lt_tree->rwlock, isc_rwlocktype_read);
	isc_radix_node_t *node = NULL;
	RADIX_WALK(mgr->lt_tree->radix->head, node) {
		unsigned int i = 0;
		for (i = 0;
		     i < sizeof(node->data) / sizeof(node->data[0]);
		     i++)
		{
			infoblox_lt_info_t *lt_info = (infoblox_lt_info_t *) node->data[i];
			if (lt_info == NULL)
				continue;

			result = cb(mgr, lt_info);
			if (result != ISC_R_SUCCESS)
			{
				char server_ip_with_port[ISC_SOCKADDR_FORMATSIZE];
				isc_sockaddr_format(&lt_info->addr, server_ip_with_port, sizeof(server_ip_with_port));
				if (result == ISC_R_FAMILYNOSUPPORT)
				{
					if (lt_info->skip_addr_family_log == ISC_FALSE)
					{
						infoblox_log(1, "failed to %s latency for %s: %s "
							"(message will only be logged once per primary)",
							action, server_ip_with_port, isc_result_totext(result));
						lt_info->skip_addr_family_log = ISC_TRUE;
					}
				}
				else
				{
					infoblox_log(1, "failed to %s latency for %s: %s",
						action, server_ip_with_port, isc_result_totext(result));
				}
				goto cleanup; // exit right away
			}
		}
	} RADIX_WALK_END;

cleanup:
	RWUNLOCK(&mgr->lt_tree->rwlock, isc_rwlocktype_read);
	return result;
}

static isc_result_t
infoblox_print_latency(infoblox_lt_mgr_t *mgr, infoblox_lt_info_t *info)
{
	UNUSED(mgr);

	REQUIRE(info != NULL);

	char server_ip_with_port[ISC_SOCKADDR_FORMATSIZE];
	isc_sockaddr_format(&info->addr, server_ip_with_port, sizeof(server_ip_with_port));
	infoblox_log(1, "Server %s: estimated RTT = %lld usec (%.2f sec)", server_ip_with_port, info->rtt, ((float)info->rtt)/1000000);

	return ISC_R_SUCCESS;
}

isc_result_t
infoblox_dump_server_latencies(infoblox_lt_mgr_t *mgr)
{
	REQUIRE(VALID_LM(mgr));
	return infoblox_for_each_latency(mgr, infoblox_print_latency, "dump");
}

// Function to set new request manager to send SOA queries; needed when old
// request manager is shut down f.e. due to 'rndc reconfig'
//
// Access to manager's request manager is controlled thru grabbing mgr->lock.
// The only code that accesses the manager, other than create/destroy
// functions, is the main thread loop which is executed with the lock grabbed.
// This should be enough for synchronization.
void
infoblox_lt_mgr_set_requestmgr(infoblox_lt_mgr_t *mgr, dns_requestmgr_t *requestmgr)
{
	REQUIRE(VALID_LM(mgr));
	LOCK(&mgr->lock);
	if (mgr->requestmgr)
		dns_requestmgr_detach(&mgr->requestmgr);
	dns_requestmgr_attach(requestmgr, &mgr->requestmgr);
	UNLOCK(&mgr->lock);
}

/*
 * Return the task object used in the latency manager.
 *
 * Intended to be only used for testing; for such caller's convenience
 * it doesn't acquire a new reference to the returned task object (unlike other
 * public 'gettask' functions).  It's caller's responsibility to manage the
 * lifetime of the returned object.
 */
isc_task_t *
ibtest_lt_mgr_gettask(infoblox_lt_mgr_t *mgr) {
	REQUIRE(VALID_LM(mgr));
	return (mgr->task);
}
