﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|@PLATFORM@">
      <Configuration>Debug</Configuration>
      <Platform>@PLATFORM@</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|@PLATFORM@">
      <Configuration>Release</Configuration>
      <Platform>@PLATFORM@</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{723C65DA-A96C-4BA3-A34E-44F11CA346F9}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>named</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>..\..\..\Build\$(Configuration)\</OutDir>
    <IntDir>.\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\Build\$(Configuration)\</OutDir>
    <IntDir>.\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;@CRYPTO@@USE_GSSAPI@BUILDER="Visual Studio";_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile>.\$(Configuration)\$(TargetName).pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\$(Configuration)\</AssemblerListingLocation>
      <ObjectFileName>.\$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(OutDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <BrowseInformation>true</BrowseInformation>
      <AdditionalIncludeDirectories>@OPENSSL_INC@@GSSAPI_INC@@GEOIP_INC@.\;..\..\..\;@LIBXML2_INC@..\win32\include;..\include;..\..\..\lib\isc\win32;..\..\..\lib\isc\win32\include;..\..\..\lib\isc\include;..\..\..\lib\dns\include;..\..\..\lib\isccc\include;..\..\..\lib\lwres\win32\include;..\..\..\lib\lwres\include;..\..\..\lib\isccfg\include;..\..\..\lib\bind9\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>..\..\..\Build\$(Configuration)\$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>..\..\..\lib\isc\win32\$(Configuration);..\..\..\lib\dns\win32\$(Configuration);..\..\..\lib\isccc\win32\$(Configuration);..\..\..\lib\lwres\win32\$(Configuration);..\..\..\lib\isccfg\win32\$(Configuration);..\..\..\lib\bind9\win32\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>@LIBXML2_LIB@@OPENSSL_LIB@@GSSAPI_LIB@@<EMAIL>;libdns.lib;libisccc.lib;liblwres.lib;libisccfg.lib;libbind9.lib;version.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>@INTRINSIC@</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;@CRYPTO@@USE_GSSAPI@BUILDER="Visual Studio";NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <StringPooling>true</StringPooling>
      <PrecompiledHeaderOutputFile>.\$(Configuration)\$(TargetName).pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\$(Configuration)\</AssemblerListingLocation>
      <ObjectFileName>.\$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(OutDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <AdditionalIncludeDirectories>@OPENSSL_INC@@GSSAPI_INC@@GEOIP_INC@.\;..\..\..\;@LIBXML2_INC@..\win32\include;..\include;..\..\..\lib\isc\win32;..\..\..\lib\isc\win32\include;..\..\..\lib\isc\include;..\..\..\lib\dns\include;..\..\..\lib\isccc\include;..\..\..\lib\lwres\win32\include;..\..\..\lib\lwres\include;..\..\..\lib\isccfg\include;..\..\..\lib\bind9\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>..\..\..\Build\$(Configuration)\$(TargetName)$(TargetExt)</OutputFile>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <AdditionalLibraryDirectories>..\..\..\lib\isc\win32\$(Configuration);..\..\..\lib\dns\win32\$(Configuration);..\..\..\lib\isccc\win32\$(Configuration);..\..\..\lib\lwres\win32\$(Configuration);..\..\..\lib\isccfg\win32\$(Configuration);..\..\..\lib\bind9\win32\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>@LIBXML2_LIB@@OPENSSL_LIB@@GSSAPI_LIB@@<EMAIL>;libdns.lib;libisccc.lib;liblwres.lib;libisccfg.lib;libbind9.lib;version.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\builtin.c" />
    <ClCompile Include="..\client.c" />
    <ClCompile Include="..\config.c" />
    <ClCompile Include="..\control.c" />
    <ClCompile Include="..\controlconf.c" />
@IF GEOIP
    <ClCompile Include="..\geoip.c" />
@END GEOIP
    <ClCompile Include="..\interfacemgr.c" />
    <ClCompile Include="..\listenlist.c" />
    <ClCompile Include="..\log.c" />
    <ClCompile Include="..\logconf.c" />
    <ClCompile Include="..\lwaddr.c" />
    <ClCompile Include="..\lwdclient.c" />
    <ClCompile Include="..\lwderror.c" />
    <ClCompile Include="..\lwdgabn.c" />
    <ClCompile Include="..\lwdgnba.c" />
    <ClCompile Include="..\lwdgrbn.c" />
    <ClCompile Include="..\lwdnoop.c" />
    <ClCompile Include="..\lwresd.c" />
    <ClCompile Include="..\lwsearch.c" />
    <ClCompile Include="..\main.c" />
    <ClCompile Include="..\notify.c" />
    <ClCompile Include="..\query.c" />
    <ClCompile Include="..\server.c" />
    <ClCompile Include="..\sortlist.c" />
    <ClCompile Include="..\statschannel.c" />
    <ClCompile Include="..\tkeyconf.c" />
    <ClCompile Include="..\tsigconf.c" />
    <ClCompile Include="..\update.c" />
    <ClCompile Include="..\xfrout.c" />
    <ClCompile Include="..\zoneconf.c" />
    <ClCompile Include="dlz_dlopen_driver.c" />
    <ClCompile Include="ntservice.c" />
    <ClCompile Include="os.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\include\named\builtin.h" />
    <ClInclude Include="..\include\named\client.h" />
    <ClInclude Include="..\include\named\config.h" />
    <ClInclude Include="..\include\named\control.h" />
@IF GEOIP
    <ClInclude Include="..\include\named\geoip.h" />
@END GEOIP
    <ClInclude Include="..\include\named\globals.h" />
    <ClInclude Include="..\include\named\interfacemgr.h" />
    <ClInclude Include="..\include\named\listenlist.h" />
    <ClInclude Include="..\include\named\log.h" />
    <ClInclude Include="..\include\named\logconf.h" />
    <ClInclude Include="..\include\named\lwaddr.h" />
    <ClInclude Include="..\include\named\lwdclient.h" />
    <ClInclude Include="..\include\named\lwresd.h" />
    <ClInclude Include="..\include\named\lwsearch.h" />
    <ClInclude Include="..\include\named\main.h" />
    <ClInclude Include="..\include\named\notify.h" />
    <ClInclude Include="..\include\named\query.h" />
    <ClInclude Include="..\include\named\seccomp.h" />
    <ClInclude Include="..\include\named\server.h" />
    <ClInclude Include="..\include\named\sortlist.h" />
    <ClInclude Include="..\include\named\statschannel.h" />
    <ClInclude Include="..\include\named\tkeyconf.h" />
    <ClInclude Include="..\include\named\tsigconf.h" />
    <ClInclude Include="..\include\named\types.h" />
    <ClInclude Include="..\include\named\update.h" />
    <ClInclude Include="..\include\named\xfrout.h" />
    <ClInclude Include="..\include\named\zoneconf.h" />
    <ClInclude Include="include\named\ntservice.h" />
    <ClInclude Include="include\named\os.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
