﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dlz_dlopen_driver.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ntservice.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="os.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\builtin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\client.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\config.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\control.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\controlconf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
@IF GEOIP
    <ClCompile Include="..\geoip.c">
      <Filter>Source Files</Filter>
    </ClCompile>
@END GEOIP
    <ClCompile Include="..\interfacemgr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\listenlist.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\log.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\logconf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\lwaddr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\lwdclient.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\lwderror.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\lwdgabn.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\lwdgnba.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\lwdgrbn.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\lwdnoop.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\lwresd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\lwsearch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\main.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\notify.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\query.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\server.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\sortlist.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\statschannel.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\tkeyconf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\tsigconf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\update.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\xfrout.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\zoneconf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\named\ntservice.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\named\os.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\builtin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\client.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\control.h">
      <Filter>Header Files</Filter>
    </ClInclude>
@IF GEOIP
    <ClInclude Include="..\include\named\geoip.h">
      <Filter>Header Files</Filter>
    </ClInclude>
@END GEOIP
    <ClInclude Include="..\include\named\globals.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\interfacemgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\listenlist.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\log.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\logconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\lwaddr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\lwdclient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\lwresd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\lwsearch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\main.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\notify.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\query.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\seccomp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\server.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\sortlist.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\statschannel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\tkeyconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\tsigconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\update.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\xfrout.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\named\zoneconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>