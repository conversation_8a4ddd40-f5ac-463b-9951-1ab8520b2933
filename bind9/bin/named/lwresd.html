<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000, 2001, 2004, 2005, 2007-2009, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>lwresd</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.lwresd"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">lwresd</span>
     &#8212; lightweight resolver daemon
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">lwresd</code> 
       [<code class="option">-c <em class="replaceable"><code>config-file</code></em></code>]
       [<code class="option">-C <em class="replaceable"><code>config-file</code></em></code>]
       [<code class="option">-d <em class="replaceable"><code>debug-level</code></em></code>]
       [<code class="option">-f</code>]
       [<code class="option">-g</code>]
       [<code class="option">-i <em class="replaceable"><code>pid-file</code></em></code>]
       [<code class="option">-m <em class="replaceable"><code>flag</code></em></code>]
       [<code class="option">-n <em class="replaceable"><code>#cpus</code></em></code>]
       [<code class="option">-P <em class="replaceable"><code>port</code></em></code>]
       [<code class="option">-p <em class="replaceable"><code>port</code></em></code>]
       [<code class="option">-s</code>]
       [<code class="option">-t <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-u <em class="replaceable"><code>user</code></em></code>]
       [<code class="option">-v</code>]
       [
	[<code class="option">-4</code>]
	 |  [<code class="option">-6</code>]
      ]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>


    <p><span class="command"><strong>lwresd</strong></span>
      is the daemon providing name lookup
      services to clients that use the BIND 9 lightweight resolver
      library.  It is essentially a stripped-down, caching-only name
      server that answers queries using the BIND 9 lightweight
      resolver protocol rather than the DNS protocol.
    </p>

    <p><span class="command"><strong>lwresd</strong></span>
      listens for resolver queries on a
      UDP port on the IPv4 loopback interface, 127.0.0.1.  This
      means that <span class="command"><strong>lwresd</strong></span> can only be used by
      processes running on the local machine.  By default, UDP port
      number 921 is used for lightweight resolver requests and
      responses.
    </p>
    <p>
      Incoming lightweight resolver requests are decoded by the
      server which then resolves them using the DNS protocol.  When
      the DNS lookup completes, <span class="command"><strong>lwresd</strong></span> encodes
      the answers in the lightweight resolver format and returns
      them to the client that made the request.
    </p>
    <p>
      If <code class="filename">/etc/resolv.conf</code> contains any
      <code class="option">nameserver</code> entries, <span class="command"><strong>lwresd</strong></span>
      sends recursive DNS queries to those servers.  This is similar
      to the use of forwarders in a caching name server.  If no
      <code class="option">nameserver</code> entries are present, or if
      forwarding fails, <span class="command"><strong>lwresd</strong></span> resolves the
      queries autonomously starting at the root name servers, using
      a built-in list of root server hints.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-4</span></dt>
<dd>
          <p>
            Use IPv4 only even if the host machine is capable of IPv6.
            <code class="option">-4</code> and <code class="option">-6</code> are mutually
            exclusive.
          </p>
        </dd>
<dt><span class="term">-6</span></dt>
<dd>
          <p>
            Use IPv6 only even if the host machine is capable of IPv4.
            <code class="option">-4</code> and <code class="option">-6</code> are mutually
            exclusive.
          </p>
        </dd>
<dt><span class="term">-c <em class="replaceable"><code>config-file</code></em></span></dt>
<dd>
          <p>
            Use <em class="replaceable"><code>config-file</code></em> as the
            configuration file instead of the default,
            <code class="filename">/etc/lwresd.conf</code>.
	    
	    <code class="option">-c</code> can not be used with <code class="option">-C</code>.
          </p>
        </dd>
<dt><span class="term">-C <em class="replaceable"><code>config-file</code></em></span></dt>
<dd>
          <p>
            Use <em class="replaceable"><code>config-file</code></em> as the
            configuration file instead of the default,
            <code class="filename">/etc/resolv.conf</code>.
	    <code class="option">-C</code> can not be used with <code class="option">-c</code>.
          </p>
        </dd>
<dt><span class="term">-d <em class="replaceable"><code>debug-level</code></em></span></dt>
<dd>
          <p>
            Set the daemon's debug level to <em class="replaceable"><code>debug-level</code></em>.
            Debugging traces from <span class="command"><strong>lwresd</strong></span> become
            		more verbose as the debug level increases.
          </p>
        </dd>
<dt><span class="term">-f</span></dt>
<dd>
          <p>
            Run the server in the foreground (i.e. do not daemonize).
          </p>
        </dd>
<dt><span class="term">-g</span></dt>
<dd>
          <p>
            Run the server in the foreground and force all logging
            to <code class="filename">stderr</code>.
          </p>
        </dd>
<dt><span class="term">-i <em class="replaceable"><code>pid-file</code></em></span></dt>
<dd>
          <p>
            Use <em class="replaceable"><code>pid-file</code></em> as the
            PID file instead of the default,
            <code class="filename">/var/run/lwresd/lwresd.pid</code>.
          </p>
        </dd>
<dt><span class="term">-m <em class="replaceable"><code>flag</code></em></span></dt>
<dd>
          <p>
            Turn on memory usage debugging flags.  Possible flags are
            <em class="replaceable"><code>usage</code></em>,
            <em class="replaceable"><code>trace</code></em>,
            <em class="replaceable"><code>record</code></em>,
            <em class="replaceable"><code>size</code></em>, and
            <em class="replaceable"><code>mctx</code></em>.
            These correspond to the ISC_MEM_DEBUGXXXX flags described in
            <code class="filename">&lt;isc/mem.h&gt;</code>.
          </p>
        </dd>
<dt><span class="term">-n <em class="replaceable"><code>#cpus</code></em></span></dt>
<dd>
          <p>
            Create <em class="replaceable"><code>#cpus</code></em> worker threads
            to take advantage of multiple CPUs.  If not specified,
            <span class="command"><strong>lwresd</strong></span> will try to determine the
            number of CPUs present and create one thread per CPU.
            If it is unable to determine the number of CPUs, a
            single worker thread will be created.
          </p>
        </dd>
<dt><span class="term">-P <em class="replaceable"><code>port</code></em></span></dt>
<dd>
          <p>
            Listen for lightweight resolver queries on port
            <em class="replaceable"><code>port</code></em>.  If
            		not specified, the default is port 921.
          </p>
        </dd>
<dt><span class="term">-p <em class="replaceable"><code>port</code></em></span></dt>
<dd>
          <p>
            Send DNS lookups to port <em class="replaceable"><code>port</code></em>.  If not
            specified, the default is port 53.  This provides a
            way of testing the lightweight resolver daemon with a
            name server that listens for queries on a non-standard
            port number.
          </p>
        </dd>
<dt><span class="term">-s</span></dt>
<dd>
          <p>
            Write memory usage statistics to <code class="filename">stdout</code>
            on exit.
          </p>
          <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
<h3 class="title">Note</h3>
            <p>
              This option is mainly of interest to BIND 9 developers
              and may be removed or changed in a future release.
            </p>
          </div>
        </dd>
<dt><span class="term">-t <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
          <p>Chroot
	    to <em class="replaceable"><code>directory</code></em> after
            processing the command line arguments, but before
            reading the configuration file.
          </p>
          <div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;">
<h3 class="title">Warning</h3>
            <p>
              This option should be used in conjunction with the
              <code class="option">-u</code> option, as chrooting a process
              running as root doesn't enhance security on most
              systems; the way <code class="function">chroot(2)</code> is
              defined allows a process with root privileges to
              escape a chroot jail.
            </p>
          </div>
        </dd>
<dt><span class="term">-u <em class="replaceable"><code>user</code></em></span></dt>
<dd>
          <p>Setuid
	    to <em class="replaceable"><code>user</code></em> after completing
            privileged operations, such as creating sockets that
            listen on privileged ports.
          </p>
        </dd>
<dt><span class="term">-v</span></dt>
<dd>
          <p>
            Report the version number and exit.
          </p>
        </dd>
</dl></div>

  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>FILES</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term"><code class="filename">/etc/resolv.conf</code></span></dt>
<dd>
          <p>
            The default configuration file.
          </p>
        </dd>
<dt><span class="term"><code class="filename">/var/run/lwresd.pid</code></span></dt>
<dd>
          <p>
            The default process-id file.
          </p>
        </dd>
</dl></div>

  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
        <span class="refentrytitle">named</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">lwres</span>(3)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">resolver</span>(5)
      </span>.
    </p>
  </div>

</div></body>
</html>
