# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

# Attempt to disable parallel processing.
.NOTPARALLEL:
.NO_PARALLEL:

VERSION=@BIND9_VERSION@

@BIND9_PRODUCT@

@BIND9_DESCRIPTION@

@BIND9_SRCID@

@BIND9_CONFIGARGS@

@BIND9_MAKE_INCLUDES@

include ../../Makefile-infoblox.inc

#
# Add database drivers here.
#
DBDRIVER_OBJS =
DBDRIVER_SRCS =
DBDRIVER_INCLUDES =
DBDRIVER_LIBS =

DLZ_DRIVER_DIR =	${top_srcdir}/contrib/dlz/drivers

DLZDRIVER_OBJS =	@DLZ_DRIVER_OBJS@
DLZDRIVER_SRCS =	@DLZ_DRIVER_SRCS@
DLZDRIVER_INCLUDES =	@DLZ_DRIVER_INCLUDES@
DLZDRIVER_LIBS =	@DLZ_DRIVER_LIBS@

CINCLUDES =	-I${srcdir}/include -I${srcdir}/unix/include -I. \
		${LWRES_INCLUDES} ${DNS_INCLUDES} ${BIND9_INCLUDES} \
		${ISCCFG_INCLUDES} ${ISCCC_INCLUDES} ${ISC_INCLUDES} \
		${DLZDRIVER_INCLUDES} ${DBDRIVER_INCLUDES} @DST_OPENSSL_INC@ ${INFOBLOX_INCLUDES}

CDEFINES =      @CONTRIB_DLZ@ @USE_PKCS11@ @PKCS11_ENGINE@ @CRYPTO@

CWARNINGS =

INFOBLOX_SOURCE_ROOT	=	${top_srcdir}/..
INFOBLOX_PRODUCT_ROOT	=	$(INFOBLOX_SOURCE_ROOT)/products/dns
INFOBLOX_INCLUDES	=	-I$(INFOBLOX_SOURCE_ROOT)/common/server/include \
				-I$(INFOBLOX_SOURCE_ROOT)/products/one/server/include \
				-I$(INFOBLOX_PRODUCT_ROOT)/server/include \
				-I$(INFOBLOX_SOURCE_ROOT)/products/bind/server/include \
				-I/opt/bloxdb/include \
				-I/usr/lib/x86_64-linux-gnu/heimdal \
				-I$(INFOBLOX_SOURCE_ROOT)/products/analytics/server/include

DNSLIBS =	../../lib/dns/libdns.@A@ @DNS_CRYPTO_LIBS@
ISCCFGLIBS =	../../lib/isccfg/libisccfg.@A@
ISCCCLIBS =	../../lib/isccc/libisccc.@A@
ISCLIBS =	../../lib/isc/libisc.@A@
ISCNOSYMLIBS =	../../lib/isc/libisc-nosymtbl.@A@
LWRESLIBS =	../../lib/lwres/liblwres.@A@
BIND9LIBS =	../../lib/bind9/libbind9.@A@

DNSDEPLIBS =	../../lib/dns/libdns.@A@
ISCCFGDEPLIBS =	../../lib/isccfg/libisccfg.@A@
ISCCCDEPLIBS =	../../lib/isccc/libisccc.@A@
ISCDEPLIBS =	../../lib/isc/libisc.@A@
LWRESDEPLIBS =	../../lib/lwres/liblwres.@A@
BIND9DEPLIBS =	../../lib/bind9/libbind9.@A@

DEPLIBS =	${LWRESDEPLIBS} ${DNSDEPLIBS} ${BIND9DEPLIBS} \
		${ISCCFGDEPLIBS} ${ISCCCDEPLIBS} ${ISCDEPLIBS}

INFOBLOX_LIBS += -libthales_preload -libone_safenet -libidns_ipc

LIBS =		${LWRESLIBS} ${DNSLIBS} ${BIND9LIBS} \
		${ISCCFGLIBS} ${ISCCCLIBS} ${ISCLIBS} \
		${DLZDRIVER_LIBS} ${DBDRIVER_LIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}

NOSYMLIBS =	${LWRESLIBS} ${DNSLIBS} ${BIND9LIBS} \
		${ISCCFGLIBS} ${ISCCCLIBS} ${ISCNOSYMLIBS} \
		${DLZDRIVER_LIBS} ${DBDRIVER_LIBS} @LIBS@

LIBSPIC :=	$(LIBS:%.a=%.a-pic) ${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}

SUBDIRS =	unix
TESTDIRS =	@UNITTESTS@

IB_SO_TARGETS = libibbind.so

TARGETS =	named@EXEEXT@ lwresd@EXEEXT@ ${IB_SO_TARGETS}

GEOIPLINKOBJS = geoip.@O@

OBJS =		builtin.@O@ client.@O@ config.@O@ control.@O@ \
		controlconf.@O@ fuzz.@O@ @GEOIPLINKOBJS@ interfacemgr.@O@ \
		listenlist.@O@ log.@O@ logconf.@O@ main.@O@ notify.@O@ \
		query.@O@ server.@O@ sortlist.@O@ statschannel.@O@ \
		tkeyconf.@O@ tsigconf.@O@ update.@O@ xfrout.@O@ \
		zoneconf.@O@ \
		lwaddr.@O@ lwresd.@O@ lwdclient.@O@ lwderror.@O@ lwdgabn.@O@ \
		lwdgnba.@O@ lwdgrbn.@O@ lwdnoop.@O@ lwsearch.@O@ \
		infoblox_latency_track.@O@ \
		${DLZDRIVER_OBJS} ${DBDRIVER_OBJS}
OBJS +=		infoblox_query_util_private.@O@ \
		infoblox_response_log.@O@ \
		infoblox_config.@O@

UOBJS =		unix/os.@O@ unix/dlz_dlopen_driver.@O@

SYMOBJS =	symtbl.@O@

GEOIPLINKSRCS = geoip.c

SRCS =		builtin.c client.c config.c control.c \
		controlconf.c fuzz.c @GEOIPLINKSRCS@ interfacemgr.c \
		listenlist.c log.c logconf.c main.c notify.c \
		query.c server.c sortlist.c statschannel.c \
		tkeyconf.c tsigconf.c update.c xfrout.c \
		zoneconf.c \
		lwaddr.c lwresd.c lwdclient.c lwderror.c lwdgabn.c \
		lwdgnba.c lwdgrbn.c lwdnoop.c lwsearch.c \
		infoblox_latency_track.c \
		${DLZDRIVER_SRCS} ${DBDRIVER_SRCS}

SRCS +=		infoblox_query_util_private.c \
		infoblox_response_log.c \
		infoblox_config.c

MANPAGES =	named.8 lwresd.8 named.conf.5

HTMLPAGES =	named.html lwresd.html named.conf.html

MANOBJS =	${MANPAGES} ${HTMLPAGES}

@BIND9_MAKE_RULES@

main.@O@: main.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} \
		-DVERSION=\"${VERSION}\" \
		-DPRODUCT=\"${PRODUCT}\" \
		-DDESCRIPTION=\"${DESCRIPTION}\" \
		-DSRCID=\"${SRCID}\" \
		-DCONFIGARGS="\"${CONFIGARGS}\"" \
		-DBUILDER="\"make\"" \
		-DNS_LOCALSTATEDIR=\"${localstatedir}\" \
		-DNS_SYSCONFDIR=\"${sysconfdir}\" -c ${srcdir}/main.c

config.@O@: config.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} \
		-DVERSION=\"${VERSION}\" \
		-DSRCID=\"${SRCID}\" \
		-DDYNDB_LIBDIR=\"@libdir@/bind\" \
		-DNS_LOCALSTATEDIR=\"${localstatedir}\" \
		-DNS_SYSCONFDIR=\"${sysconfdir}\" \
		-c ${srcdir}/config.c

server.@O@: server.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} \
		-DPRODUCT=\"${PRODUCT}\" \
		-DVERSION=\"${VERSION}\" -c ${srcdir}/server.c

named@EXEEXT@: ${OBJS} ${DEPLIBS}
	export MAKE_SYMTABLE="yes"; \
	export BASEOBJS="${OBJS} ${UOBJS}"; \
	${FINALBUILDCMD}

lwresd@EXEEXT@: named@EXEEXT@
	rm -f lwresd@EXEEXT@
	@LN@ named@EXEEXT@ lwresd@EXEEXT@

doc man:: ${MANOBJS}

docclean manclean maintainer-clean::
	rm -f ${MANOBJS}

clean distclean maintainer-clean::
	rm -f ${TARGETS} ${OBJS}

maintainer-clean::

bind9.xsl.h: bind9.xsl ${srcdir}/convertxsl.pl
	${PERL} ${srcdir}/convertxsl.pl < ${srcdir}/bind9.xsl > bind9.xsl.h

depend: bind9.xsl.h
statschannel.@O@: bind9.xsl.h

installdirs:
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${sbindir}
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${mandir}/man5
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${mandir}/man8

install:: named@EXEEXT@ lwresd@EXEEXT@ installdirs
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} named@EXEEXT@ ${DESTDIR}${sbindir}
	(cd ${DESTDIR}${sbindir}; rm -f lwresd@EXEEXT@; @LN@ named@EXEEXT@ lwresd@EXEEXT@)
	${INSTALL_DATA} ${srcdir}/named.8 ${DESTDIR}${mandir}/man8
	${INSTALL_DATA} ${srcdir}/lwresd.8 ${DESTDIR}${mandir}/man8
	${INSTALL_DATA} ${srcdir}/named.conf.5 ${DESTDIR}${mandir}/man5
	cp ${IB_SO_TARGETS} /infoblox/dns/lib64

uninstall::
	rm -f ${DESTDIR}${mandir}/man5/named.conf.5
	rm -f ${DESTDIR}${mandir}/man8/lwresd.8
	rm -f ${DESTDIR}${mandir}/man8/named.8
	rm -f ${DESTDIR}${sbindir}/lwresd@EXEEXT@
	${LIBTOOL_MODE_UNINSTALL} rm -f ${DESTDIR}${sbindir}/named@EXEEXT@

@DLZ_DRIVER_RULES@

named-symtbl.@O@: named-symtbl.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} -c named-symtbl.c

libibbind.so: ${DEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ -shared -Wl,--whole-archive ${LIBSPIC} -Wl,--no-whole-archive
