# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

PYTHON	=	@PYTHON@

PYTESTS =	dnskey_test.py policy_test.py

@BIND9_MAKE_RULES@

check test:
	for test in $(PYTESTS); do \
		PYTHONPATH=${srcdir}/../.. $(PYTHON) ${srcdir}/$$test; \
	done

clean distclean::
	rm -rf *.pyc __pycache__

distclean::
	rm -f ${PYTESTS}
