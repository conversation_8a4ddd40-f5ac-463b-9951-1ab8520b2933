/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

policy postpublish_rsa {
	algorithm rsasha1;
	coverage 1y;
	roll-period zsk 3mo;
	pre-publish zsk 2w;
	post-publish zsk 2w;
	roll-period ksk 1y;
	pre-publish ksk 1mo;
	post-publish ksk 2mo;
	keyttl 1h;
	key-size ksk 2048;
	key-size zsk 1024;
};

// Policy that defines a post-publish period lower than the rollover period
zone good_postpublish.test {
	policy postpublish_rsa;
	coverage 6mo;
	roll-period ksk 4mo;
	pre-publish ksk 1mo;
};

// Policy that defines a post-publish period equal to the rollover period
zone bad_postpublish.test {
	policy postpublish_rsa;
	coverage 6mo;
	roll-period ksk 4mo;
	pre-publish ksk 4mo;
};


