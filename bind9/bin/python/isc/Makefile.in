# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

SUBDIRS	=	tests

PYTHON	=	@PYTHON@

PYSRCS =	__init__.py checkds.py coverage.py dnskey.py eventlist.py \
		keydict.py keyevent.py keymgr.py keyseries.py keyzone.py \
		policy.py rndc.py utils.py

TARGETS =	parsetab.py

@BIND9_MAKE_RULES@

.SUFFIXES: .py .pyc
.py.pyc:
	$(PYTHON) -m compileall .

parsetab.py: policy.py
	$(PYTHON) policy.py parse /dev/null > /dev/null
	PYTHONPATH=${srcdir} $(PYTHON) -m parsetab

check test: subdirs

clean distclean::
	rm -f *.pyc parser.out parsetab.py
	rm -rf __pycache__ build

distclean::
	rm -rf ${PYSRCS}
