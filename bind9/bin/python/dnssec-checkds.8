.\" Copyright (C) 2012-2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: dnssec-checkds
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2013-01-01
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DNSSEC\-CHECKDS" "8" "2013\-01\-01" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dnssec-checkds \- DNSSEC delegation consistency checking tool
.SH "SYNOPSIS"
.HP \w'\fBdnssec\-checkds\fR\ 'u
\fBdnssec\-checkds\fR [\fB\-l\ \fR\fB\fIdomain\fR\fR] [\fB\-f\ \fR\fB\fIfile\fR\fR] [\fB\-d\ \fR\fB\fIdig\ path\fR\fR] [\fB\-D\ \fR\fB\fIdsfromkey\ path\fR\fR] {zone}
.HP \w'\fBdnssec\-dsfromkey\fR\ 'u
\fBdnssec\-dsfromkey\fR [\fB\-l\ \fR\fB\fIdomain\fR\fR] [\fB\-f\ \fR\fB\fIfile\fR\fR] [\fB\-d\ \fR\fB\fIdig\ path\fR\fR] [\fB\-D\ \fR\fB\fIdsfromkey\ path\fR\fR] {zone}
.SH "DESCRIPTION"
.PP
\fBdnssec\-checkds\fR
verifies the correctness of Delegation Signer (DS) or DNSSEC Lookaside Validation (DLV) resource records for keys in a specified zone\&.
.SH "OPTIONS"
.PP
\-f \fIfile\fR
.RS 4
If a
\fBfile\fR
is specified, then the zone is read from that file to find the DNSKEY records\&. If not, then the DNSKEY records for the zone are looked up in the DNS\&.
.RE
.PP
\-l \fIdomain\fR
.RS 4
Check for a DLV record in the specified lookaside domain, instead of checking for a DS record in the zone\*(Aqs parent\&.
.RE
.PP
\-d \fIdig path\fR
.RS 4
Specifies a path to a
\fBdig\fR
binary\&. Used for testing\&.
.RE
.PP
\-D \fIdsfromkey path\fR
.RS 4
Specifies a path to a
\fBdnssec\-dsfromkey\fR
binary\&. Used for testing\&.
.RE
.SH "SEE ALSO"
.PP
\fBdnssec-dsfromkey\fR(8),
\fBdnssec-keygen\fR(8),
\fBdnssec-signzone\fR(8),
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2012-2018 Internet Systems Consortium, Inc. ("ISC")
.br
