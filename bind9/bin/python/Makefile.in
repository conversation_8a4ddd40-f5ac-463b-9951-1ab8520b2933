# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

PYTHON	=	@PYTHON@

SUBDIRS	=	isc

TARGETS =	dnssec-checkds dnssec-coverage dnssec-keymgr
PYSRCS =	dnssec-checkds.py dnssec-coverage.py dnssec-keymgr.py

MANPAGES =	dnssec-checkds.8 dnssec-coverage.8 dnssec-keymgr.8
HTMLPAGES =	dnssec-checkds.html dnssec-coverage.html dnssec-keymgr.html
MANOBJS =	${MANPAGES} ${HTMLPAGES}

@BIND9_MAKE_RULES@

dnssec-checkds: dnssec-checkds.py
	cp -f dnssec-checkds.py dnssec-checkds
	chmod +x dnssec-checkds

dnssec-coverage: dnssec-coverage.py
	cp -f dnssec-coverage.py dnssec-coverage
	chmod +x dnssec-coverage

dnssec-keymgr: dnssec-keymgr.py
	cp -f dnssec-keymgr.py dnssec-keymgr
	chmod +x dnssec-keymgr

doc man:: ${MANOBJS}

docclean manclean maintainer-clean::
	rm -f ${MANOBJS}

installdirs:
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${sbindir}
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${mandir}/man8

install:: ${TARGETS} installdirs
	${INSTALL_SCRIPT} dnssec-checkds ${DESTDIR}${sbindir}
	${INSTALL_SCRIPT} dnssec-coverage ${DESTDIR}${sbindir}
	${INSTALL_SCRIPT} dnssec-keymgr ${DESTDIR}${sbindir}
	${INSTALL_DATA} ${srcdir}/dnssec-checkds.8 ${DESTDIR}${mandir}/man8
	${INSTALL_DATA} ${srcdir}/dnssec-coverage.8 ${DESTDIR}${mandir}/man8
	${INSTALL_DATA} ${srcdir}/dnssec-keymgr.8 ${DESTDIR}${mandir}/man8
	if test -n "${PYTHON}" ; then \
		if test -n "${DESTDIR}" ; then \
			${PYTHON} ${srcdir}/setup.py install --root=${DESTDIR} --prefix=${prefix} @PYTHON_INSTALL_LIB@ ; \
		else \
			${PYTHON} ${srcdir}/setup.py install --prefix=${prefix} @PYTHON_INSTALL_LIB@ ; \
		fi ; \
		rm -rf build ; \
	fi

uninstall::
	rm -f ${DESTDIR}${mandir}/man8/dnssec-keymgr.8
	rm -f ${DESTDIR}${mandir}/man8/dnssec-coverage.8
	rm -f ${DESTDIR}${mandir}/man8/dnssec-checkds.8
	rm -f ${DESTDIR}${sbindir}/dnssec-keymgr
	rm -f ${DESTDIR}${sbindir}/dnssec-coverage
	rm -f ${DESTDIR}${sbindir}/dnssec-checkds
	# only manually uninstall for the python package itself

clean distclean::
	rm -f ${TARGETS}
	rm -rf build

distclean::
	rm -f dnssec-checkds.py dnssec-coverage.py dnssec-keymgr.py
