.\" Copyright (C) 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: dnssec-coverage
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-01-11
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DNSSEC\-COVERAGE" "8" "2014\-01\-11" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dnssec-coverage \- checks future DNSKEY coverage for a zone
.SH "SYNOPSIS"
.HP \w'\fBdnssec\-coverage\fR\ 'u
\fBdnssec\-coverage\fR [\fB\-K\ \fR\fB\fIdirectory\fR\fR] [\fB\-l\ \fR\fB\fIlength\fR\fR] [\fB\-f\ \fR\fB\fIfile\fR\fR] [\fB\-d\ \fR\fB\fIDNSKEY\ TTL\fR\fR] [\fB\-m\ \fR\fB\fImax\ TTL\fR\fR] [\fB\-r\ \fR\fB\fIinterval\fR\fR] [\fB\-c\ \fR\fB\fIcompilezone\ path\fR\fR] [\fB\-k\fR] [\fB\-z\fR] [zone...]
.SH "DESCRIPTION"
.PP
\fBdnssec\-coverage\fR
verifies that the DNSSEC keys for a given zone or a set of zones have timing metadata set properly to ensure no future lapses in DNSSEC coverage\&.
.PP
If
\fBzone\fR
is specified, then keys found in the key repository matching that zone are scanned, and an ordered list is generated of the events scheduled for that key (i\&.e\&., publication, activation, inactivation, deletion)\&. The list of events is walked in order of occurrence\&. Warnings are generated if any event is scheduled which could cause the zone to enter a state in which validation failures might occur: for example, if the number of published or active keys for a given algorithm drops to zero, or if a key is deleted from the zone too soon after a new key is rolled, and cached data signed by the prior key has not had time to expire from resolver caches\&.
.PP
If
\fBzone\fR
is not specified, then all keys in the key repository will be scanned, and all zones for which there are keys will be analyzed\&. (Note: This method of reporting is only accurate if all the zones that have keys in a given repository share the same TTL parameters\&.)
.SH "OPTIONS"
.PP
\-K \fIdirectory\fR
.RS 4
Sets the directory in which keys can be found\&. Defaults to the current working directory\&.
.RE
.PP
\-f \fIfile\fR
.RS 4
If a
\fBfile\fR
is specified, then the zone is read from that file; the largest TTL and the DNSKEY TTL are determined directly from the zone data, and the
\fB\-m\fR
and
\fB\-d\fR
options do not need to be specified on the command line\&.
.RE
.PP
\-l \fIduration\fR
.RS 4
The length of time to check for DNSSEC coverage\&. Key events scheduled further into the future than
\fBduration\fR
will be ignored, and assumed to be correct\&.
.sp
The value of
\fBduration\fR
can be set in seconds, or in larger units of time by adding a suffix: \*(Aqmi\*(Aq for minutes, \*(Aqh\*(Aq for hours, \*(Aqd\*(Aq for days, \*(Aqw\*(Aq for weeks, \*(Aqmo\*(Aq for months, \*(Aqy\*(Aq for years\&.
.RE
.PP
\-m \fImaximum TTL\fR
.RS 4
Sets the value to be used as the maximum TTL for the zone or zones being analyzed when determining whether there is a possibility of validation failure\&. When a zone\-signing key is deactivated, there must be enough time for the record in the zone with the longest TTL to have expired from resolver caches before that key can be purged from the DNSKEY RRset\&. If that condition does not apply, a warning will be generated\&.
.sp
The length of the TTL can be set in seconds, or in larger units of time by adding a suffix: \*(Aqmi\*(Aq for minutes, \*(Aqh\*(Aq for hours, \*(Aqd\*(Aq for days, \*(Aqw\*(Aq for weeks, \*(Aqmo\*(Aq for months, \*(Aqy\*(Aq for years\&.
.sp
This option is not necessary if the
\fB\-f\fR
has been used to specify a zone file\&. If
\fB\-f\fR
has been specified, this option may still be used; it will override the value found in the file\&.
.sp
If this option is not used and the maximum TTL cannot be retrieved from a zone file, a warning is generated and a default value of 1 week is used\&.
.RE
.PP
\-d \fIDNSKEY TTL\fR
.RS 4
Sets the value to be used as the DNSKEY TTL for the zone or zones being analyzed when determining whether there is a possibility of validation failure\&. When a key is rolled (that is, replaced with a new key), there must be enough time for the old DNSKEY RRset to have expired from resolver caches before the new key is activated and begins generating signatures\&. If that condition does not apply, a warning will be generated\&.
.sp
The length of the TTL can be set in seconds, or in larger units of time by adding a suffix: \*(Aqmi\*(Aq for minutes, \*(Aqh\*(Aq for hours, \*(Aqd\*(Aq for days, \*(Aqw\*(Aq for weeks, \*(Aqmo\*(Aq for months, \*(Aqy\*(Aq for years\&.
.sp
This option is not necessary if
\fB\-f\fR
has been used to specify a zone file from which the TTL of the DNSKEY RRset can be read, or if a default key TTL was set using ith the
\fB\-L\fR
to
\fBdnssec\-keygen\fR\&. If either of those is true, this option may still be used; it will override the values found in the zone file or the key file\&.
.sp
If this option is not used and the key TTL cannot be retrieved from the zone file or the key file, then a warning is generated and a default value of 1 day is used\&.
.RE
.PP
\-r \fIresign interval\fR
.RS 4
Sets the value to be used as the resign interval for the zone or zones being analyzed when determining whether there is a possibility of validation failure\&. This value defaults to 22\&.5 days, which is also the default in
\fBnamed\fR\&. However, if it has been changed by the
\fBsig\-validity\-interval\fR
option in
named\&.conf, then it should also be changed here\&.
.sp
The length of the interval can be set in seconds, or in larger units of time by adding a suffix: \*(Aqmi\*(Aq for minutes, \*(Aqh\*(Aq for hours, \*(Aqd\*(Aq for days, \*(Aqw\*(Aq for weeks, \*(Aqmo\*(Aq for months, \*(Aqy\*(Aq for years\&.
.RE
.PP
\-k
.RS 4
Only check KSK coverage; ignore ZSK events\&. Cannot be used with
\fB\-z\fR\&.
.RE
.PP
\-z
.RS 4
Only check ZSK coverage; ignore KSK events\&. Cannot be used with
\fB\-k\fR\&.
.RE
.PP
\-c \fIcompilezone path\fR
.RS 4
Specifies a path to a
\fBnamed\-compilezone\fR
binary\&. Used for testing\&.
.RE
.SH "SEE ALSO"
.PP
\fBdnssec-checkds\fR(8),
\fBdnssec-dsfromkey\fR(8),
\fBdnssec-keygen\fR(8),
\fBdnssec-signzone\fR(8)
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
