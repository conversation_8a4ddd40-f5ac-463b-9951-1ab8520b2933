<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.dnssec-keymgr">
  <info>
    <date>2016-06-03</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>dnssec-keymgr</application></refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>dnssec-keymgr</application></refname>
    <refpurpose>Ensures correct DNSKEY coverage for a zone based on a defined policy</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>dnssec-keymgr</command>
      <arg choice="opt" rep="norepeat"><option>-K <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">file</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-f</option></arg>
      <arg choice="opt" rep="norepeat"><option>-k</option></arg>
      <arg choice="opt" rep="norepeat"><option>-q</option></arg>
      <arg choice="opt" rep="norepeat"><option>-v</option></arg>
      <arg choice="opt" rep="norepeat"><option>-z</option></arg>
      <arg choice="opt" rep="norepeat"><option>-g <replaceable class="parameter">path</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-r <replaceable class="parameter">path</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-s <replaceable class="parameter">path</replaceable></option></arg>
      <arg choice="opt" rep="repeat">zone</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>
    <para>
      <command>dnssec-keymgr</command> is a high level Python wrapper
      to facilitate the key rollover process for zones handled by
      BIND. It uses the BIND commands for manipulating DNSSEC key
      metadata: <command>dnssec-keygen</command> and
      <command>dnssec-settime</command>.
    </para>
    <para>
      DNSSEC policy can be read from a configuration file (default
      <filename>/etc/dnssec-policy.conf</filename>), from which the key
      parameters, publication and rollover schedule, and desired
      coverage duration for any given zone can be determined.  This
      file may be used to define individual DNSSEC policies on a
      per-zone basis, or to set a default policy used for all zones.
    </para>
    <para>
      When <command>dnssec-keymgr</command> runs, it examines the DNSSEC
      keys for one or more zones, comparing their timing metadata against
      the policies for those zones.  If key settings do not conform to the
      DNSSEC policy (for example, because the policy has been changed),
      they are automatically corrected.
    </para>
    <para>
      A zone policy can specify a duration for which we want to
      ensure the key correctness (<option>coverage</option>).  It can
      also specify a rollover period (<option>roll-period</option>).
      If policy indicates that a key should roll over before the
      coverage period ends, then a successor key will automatically be
      created and added to the end of the key series.
    </para>
    <para>
      If zones are specified on the command line,
      <command>dnssec-keymgr</command> will examine only those zones.
      If a specified zone does not already have keys in place, then
      keys will be generated for it according to policy.
    </para>
    <para>
      If zones are <emphasis>not</emphasis> specified on the command
      line, then <command>dnssec-keymgr</command> will search the
      key directory (either the current working directory or the directory
      set by the <option>-K</option> option), and check the keys for
      all the zones represented in the directory.
    </para>
    <para>
      It is expected that this tool will be run automatically and
      unattended (for example, by <command>cron</command>).
    </para>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>
    <variablelist>
      <varlistentry>
	<term>-c <replaceable class="parameter">file</replaceable></term>
	<listitem>
	  <para>
	    If <option>-c</option> is specified, then the DNSSEC
	    policy is read from <option>file</option>.  (If not
	    specified, then the policy is read from
	    <filename>/etc/dnssec-policy.conf</filename>; if that file
	    doesn't exist, a built-in global default policy is used.)
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-f</term>
	<listitem>
	  <para>
	    Force: allow updating of key events even if they are
	    already in the past. This is not recommended for use with
	    zones in which keys have already been published. However,
	    if a set of keys has been generated all of which have
	    publication and activation dates in the past, but the
	    keys have not been published in a zone as yet, then this
	    option can be used to clean them up and turn them into a
	    proper series of keys with appropriate rollover intervals.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-g <replaceable class="parameter">keygen-path</replaceable></term>
	<listitem>
	  <para>
	    Specifies a path to a <command>dnssec-keygen</command> binary.
	    Used for testing.
	    See also the <option>-s</option> option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-h</term>
	<listitem>
	  <para>
	    Print the <command>dnssec-keymgr</command> help summary
	    and exit.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-K <replaceable class="parameter">directory</replaceable></term>
	<listitem>
	  <para>
	    Sets the directory in which keys can be found.  Defaults to the
	    current working directory.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-k</term>
	<listitem>
	  <para>
	    Only apply policies to KSK keys.
	    See also the <option>-z</option> option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-q</term>
	<listitem>
	  <para>
	    Quiet: suppress printing of <command>dnssec-keygen</command>
	    and <command>dnssec-settime</command>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-r <replaceable class="parameter">randomdev</replaceable></term>
	<listitem>
	  <para>
	    Specifies a path to a file containing random data.
	    This is passed to the <command>dnssec-keygen</command> binary
	    using its <option>-r</option> option.
<!-- TODO: what to do about "-r keyboard"? -->
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-s <replaceable class="parameter">settime-path</replaceable></term>
	<listitem>
	  <para>
	    Specifies a path to a <command>dnssec-settime</command> binary.
	    Used for testing.
	    See also the <option>-g</option> option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-v</term>
	<listitem>
	  <para>
	    Print the <command>dnssec-keymgr</command> version and exit.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-z</term>
	<listitem>
	  <para>
	    Only apply policies to ZSK keys.
	    See also the <option>-k</option> option.
	  </para>
	</listitem>
      </varlistentry>
    </variablelist>
  </refsection>

  <refsection><info><title>POLICY CONFIGURATION</title></info>
    <para>
      The <filename>dnssec-policy.conf</filename> file can specify three kinds
      of policies:
    </para>
    <itemizedlist>
      <listitem>
	<para>
	  <emphasis>Policy classes</emphasis>
	  (<option>policy <replaceable>name</replaceable> { ... };</option>)
	  can be inherited by zone policies or other policy classes; these
	  can be used to create sets of different security profiles. For
	  example, a policy class <userinput>normal</userinput> might specify
	  1024-bit key sizes, but a class <userinput>extra</userinput> might
	  specify 2048 bits instead; <userinput>extra</userinput> would be
	  used for zones that had unusually high security needs.
	</para>
      </listitem>
      <listitem>
	<para>
	  Algorithm policies:
	  (<option>algorithm-policy <replaceable>algorithm</replaceable> { ... };</option> )
	  override default per-algorithm settings.  For example, by default,
	  RSASHA256 keys use 2048-bit key sizes for both KSK and ZSK. This
	  can be modified using <command>algorithm-policy</command>, and the
	  new key sizes would then be used for any key of type RSASHA256.
	</para>
      </listitem>
      <listitem>
	<para>
	  Zone policies:
	  (<option>zone <replaceable>name</replaceable> { ... };</option> )
	  set policy for a single zone by name. A zone policy can inherit
	  a policy class by including a <option>policy</option> option.
	  Zone names beginning with digits (i.e., 0-9) must be quoted.
	</para>
      </listitem>
    </itemizedlist>
    <para>
      Options that can be specified in policies:
    </para>
    <variablelist>
      <varlistentry>
	<term><command>algorithm</command></term>
	<listitem>
	  <para>
	    The key algorithm. If no policy is defined, the default is
	    RSASHA256.
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term><command>coverage</command></term>
	<listitem>
	  <para>
	    The length of time to ensure that keys will be correct; no action
	    will be taken to create new keys to be activated after this time.
	    This can be represented as a number of seconds, or as a duration using
	    human-readable units (examples: "1y" or "6 months").
	    A default value for this option can be set in algorithm policies
	    as well as in policy classes or zone policies.
	    If no policy is configured, the default is six months.
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term><command>directory</command></term>
	<listitem>
	  <para>
	    Specifies the directory in which keys should be stored.
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term><command>key-size</command></term>
	<listitem>
	  <para>
	    Specifies the number of bits to use in creating keys.
	    Takes two arguments: keytype (eihter "zsk" or "ksk") and size.
	    A default value for this option can be set in algorithm policies
	    as well as in policy classes or zone policies. If no policy is
	    configured, the default is 1024 bits for DSA keys and 2048 for
	    RSA.
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term><command>keyttl</command></term>
	<listitem>
	  <para>
	    The key TTL. If no policy is defined, the default is one hour.
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term><command>post-publish</command></term>
	<listitem>
	  <para>
	    How long after inactivation a key should be deleted from the zone.
	    Note: If <option>roll-period</option> is not set, this value is
	    ignored. Takes two arguments: keytype (eihter "zsk" or "ksk") and a
	    duration. A default value for this option can be set in algorithm
	    policies as well as in policy classes or zone policies. The default
	    is one month.
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term><command>pre-publish</command></term>
	<listitem>
	  <para>
	    How long before activation a key should be published.  Note: If
	    <option>roll-period</option> is not set, this value is ignored.
	    Takes two arguments: keytype (either "zsk" or "ksk") and a duration.
	    A default value for this option can be set in algorithm policies
	    as well as in policy classes or zone policies.  The default is
	    one month.
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term><command>roll-period</command></term>
	<listitem>
	  <para>
	    How frequently keys should be rolled over.
	    Takes two arguments: keytype (eihter "zsk" or "ksk") and a duration.
	    A default value for this option can be set in algorithm policies
	    as well as in policy classes or zone policies.  If no policy is
	    configured, the default is one year for ZSK's. KSK's do not
	    roll over by default.
	  </para>
	</listitem>
      </varlistentry>
      <varlistentry>
	<term><command>standby</command></term>
	<listitem>
	  <para>
	    Not yet implemented.
	  </para>
	</listitem>
      </varlistentry>
    </variablelist>
  </refsection>

  <refsection><info><title>REMAINING WORK</title></info>
  <itemizedlist>
    <listitem>
      <para>
	Enable scheduling of KSK rollovers using the <option>-P sync</option>
	and <option>-D sync</option> options to
	<command>dnssec-keygen</command> and
	<command>dnssec-settime</command>.  Check the parent zone
	(as in <command>dnssec-checkds</command>) to determine when it's
	safe for the key to roll.
      </para>
    </listitem>
    <listitem>
      <para>
	Allow configuration of standby keys and use of the REVOKE bit,
	for keys that use RFC 5011 semantics.
      </para>
    </listitem>
  </itemizedlist>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>
    <para>
      <citerefentry>
	<refentrytitle>dnssec-coverage</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>dnssec-settime</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>dnssec-checkds</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>
    </para>
  </refsection>

</refentry>
