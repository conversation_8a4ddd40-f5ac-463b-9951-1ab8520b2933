<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>dnssec-coverage</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.dnssec-coverage"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">dnssec-coverage</span>
     &#8212; checks future DNSKEY coverage for a zone
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">dnssec-coverage</code> 
       [<code class="option">-K <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-l <em class="replaceable"><code>length</code></em></code>]
       [<code class="option">-f <em class="replaceable"><code>file</code></em></code>]
       [<code class="option">-d <em class="replaceable"><code>DNSKEY TTL</code></em></code>]
       [<code class="option">-m <em class="replaceable"><code>max TTL</code></em></code>]
       [<code class="option">-r <em class="replaceable"><code>interval</code></em></code>]
       [<code class="option">-c <em class="replaceable"><code>compilezone path</code></em></code>]
       [<code class="option">-k</code>]
       [<code class="option">-z</code>]
       [zone...]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>dnssec-coverage</strong></span>
      verifies that the DNSSEC keys for a given zone or a set of zones
      have timing metadata set properly to ensure no future lapses in DNSSEC
      coverage.
    </p>
    <p>
      If <code class="option">zone</code> is specified, then keys found in
      the key repository matching that zone are scanned, and an ordered
      list is generated of the events scheduled for that key (i.e.,
      publication, activation, inactivation, deletion).  The list of
      events is walked in order of occurrence.  Warnings are generated
      if any event is scheduled which could cause the zone to enter a
      state in which validation failures might occur: for example, if
      the number of published or active keys for a given algorithm drops
      to zero, or if a key is deleted from the zone too soon after a new
      key is rolled, and cached data signed by the prior key has not had
      time to expire from resolver caches.
    </p>
    <p>
      If <code class="option">zone</code> is not specified, then all keys in the
      key repository will be scanned, and all zones for which there are
      keys will be analyzed.  (Note: This method of reporting is only
      accurate if all the zones that have keys in a given repository
      share the same TTL parameters.)
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-K <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
          <p>
            Sets the directory in which keys can be found.  Defaults to the
            current working directory.
          </p>
        </dd>
<dt><span class="term">-f <em class="replaceable"><code>file</code></em></span></dt>
<dd>
          <p>
            If a <code class="option">file</code> is specified, then the zone is
            read from that file; the largest TTL and the DNSKEY TTL are
            determined directly from the zone data, and the
            <code class="option">-m</code> and <code class="option">-d</code> options do
            not need to be specified on the command line.
          </p>
        </dd>
<dt><span class="term">-l <em class="replaceable"><code>duration</code></em></span></dt>
<dd>
          <p>
            The length of time to check for DNSSEC coverage.  Key events
            scheduled further into the future than <code class="option">duration</code>
            will be ignored, and assumed to be correct.
          </p>
          <p>
            The value of <code class="option">duration</code> can be set in seconds,
            or in larger units of time by adding a suffix: 'mi' for minutes,
            'h' for hours, 'd' for days, 'w' for weeks, 'mo' for months,
            'y' for years.
          </p>
        </dd>
<dt><span class="term">-m <em class="replaceable"><code>maximum TTL</code></em></span></dt>
<dd>
          <p>
            Sets the value to be used as the maximum TTL for the zone or
            zones being analyzed when determining whether there is a
            possibility of validation failure.  When a zone-signing key is
            deactivated, there must be enough time for the record in the
            zone with the longest TTL to have expired from resolver caches
            before that key can be purged from the DNSKEY RRset.  If that
            condition does not apply, a warning will be generated.
          </p>
          <p>
            The length of the TTL can be set in seconds, or in larger units
            of time by adding a suffix: 'mi' for minutes, 'h' for hours,
            'd' for days, 'w' for weeks, 'mo' for months, 'y' for years.
          </p>
          <p>
            This option is not necessary if the <code class="option">-f</code> has
            been used to specify a zone file.  If <code class="option">-f</code> has
            been specified, this option may still be used; it will override
            the value found in the file.
          </p>
          <p>
            If this option is not used and the maximum TTL cannot be retrieved
            from a zone file, a warning is generated and a default value of
            1 week is used.
          </p>
        </dd>
<dt><span class="term">-d <em class="replaceable"><code>DNSKEY TTL</code></em></span></dt>
<dd>
          <p>
            Sets the value to be used as the DNSKEY TTL for the zone or
            zones being analyzed when determining whether there is a
            possibility of validation failure.  When a key is rolled (that
            is, replaced with a new key), there must be enough time for the
            old DNSKEY RRset to have expired from resolver caches before
            the new key is activated and begins generating signatures.  If
            that condition does not apply, a warning will be generated.
          </p>
          <p>
            The length of the TTL can be set in seconds, or in larger units
            of time by adding a suffix: 'mi' for minutes, 'h' for hours,
            'd' for days, 'w' for weeks, 'mo' for months, 'y' for years.
          </p>
          <p>
            This option is not necessary if <code class="option">-f</code> has
            been used to specify a zone file from which the TTL
            of the DNSKEY RRset can be read, or if a default key TTL was
            set using ith the <code class="option">-L</code> to
            <span class="command"><strong>dnssec-keygen</strong></span>.  If either of those is true,
            this option may still be used; it will override the values
            found in the zone file or the key file.
          </p>
          <p>
            If this option is not used and the key TTL cannot be retrieved
            from the zone file or the key file, then a warning is generated
            and a default value of 1 day is used.
          </p>
        </dd>
<dt><span class="term">-r <em class="replaceable"><code>resign interval</code></em></span></dt>
<dd>
          <p>
            Sets the value to be used as the resign interval for the zone
            or zones being analyzed when determining whether there is a
            possibility of validation failure.  This value defaults to
            22.5 days, which is also the default in
            <span class="command"><strong>named</strong></span>.  However, if it has been changed
            by the <code class="option">sig-validity-interval</code> option in
            <code class="filename">named.conf</code>, then it should also be
            changed here.
          </p>
          <p>
            The length of the interval can be set in seconds, or in larger
            units of time by adding a suffix: 'mi' for minutes, 'h' for hours,
            'd' for days, 'w' for weeks, 'mo' for months, 'y' for years.
          </p>
        </dd>
<dt><span class="term">-k</span></dt>
<dd>
          <p>
	    Only check KSK coverage; ignore ZSK events. Cannot be
            used with <code class="option">-z</code>.
          </p>
        </dd>
<dt><span class="term">-z</span></dt>
<dd>
          <p>
	    Only check ZSK coverage; ignore KSK events. Cannot be
            used with <code class="option">-k</code>.
          </p>
        </dd>
<dt><span class="term">-c <em class="replaceable"><code>compilezone path</code></em></span></dt>
<dd>
          <p>
            Specifies a path to a <span class="command"><strong>named-compilezone</strong></span> binary.
            Used for testing.
          </p>
        </dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>SEE ALSO</h2>

    <p>
      <span class="citerefentry">
        <span class="refentrytitle">dnssec-checkds</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">dnssec-dsfromkey</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">dnssec-keygen</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">dnssec-signzone</span>(8)
      </span>
    </p>
  </div>

</div></body>
</html>
