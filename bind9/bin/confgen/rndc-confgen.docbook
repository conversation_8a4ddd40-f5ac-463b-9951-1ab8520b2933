<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.rndc-confgen">
  <info>
    <date>2013-03-14</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>rndc-confgen</application></refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>rndc-confgen</application></refname>
    <refpurpose>rndc key generation tool</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2001</year>
      <year>2003</year>
      <year>2004</year>
      <year>2005</year>
      <year>2007</year>
      <year>2009</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>rndc-confgen</command>
      <arg choice="opt" rep="norepeat"><option>-a</option></arg>
      <arg choice="opt" rep="norepeat"><option>-A <replaceable class="parameter">algorithm</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-b <replaceable class="parameter">keysize</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">keyfile</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-h</option></arg>
      <arg choice="opt" rep="norepeat"><option>-k <replaceable class="parameter">keyname</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-p <replaceable class="parameter">port</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-r <replaceable class="parameter">randomfile</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-s <replaceable class="parameter">address</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-t <replaceable class="parameter">chrootdir</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-u <replaceable class="parameter">user</replaceable></option></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>rndc-confgen</command>
      generates configuration files
      for <command>rndc</command>.  It can be used as a
      convenient alternative to writing the
      <filename>rndc.conf</filename> file
      and the corresponding <command>controls</command>
      and <command>key</command>
      statements in <filename>named.conf</filename> by hand.
      Alternatively, it can be run with the <command>-a</command>
      option to set up a <filename>rndc.key</filename> file and
      avoid the need for a <filename>rndc.conf</filename> file
      and a <command>controls</command> statement altogether.
    </para>

  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
        <term>-a</term>
        <listitem>
          <para>
            Do automatic <command>rndc</command> configuration.
            This creates a file <filename>rndc.key</filename>
            in <filename>/etc</filename> (or whatever
            <varname>sysconfdir</varname>
            was specified as when <acronym>BIND</acronym> was
            built)
            that is read by both <command>rndc</command>
            and <command>named</command> on startup.  The
            <filename>rndc.key</filename> file defines a default
            command channel and authentication key allowing
            <command>rndc</command> to communicate with
            <command>named</command> on the local host
            with no further configuration.
          </para>
          <para>
            Running <command>rndc-confgen -a</command> allows
            BIND 9 and <command>rndc</command> to be used as
            drop-in
            replacements for BIND 8 and <command>ndc</command>,
            with no changes to the existing BIND 8
            <filename>named.conf</filename> file.
          </para>
          <para>
            If a more elaborate configuration than that
            generated by <command>rndc-confgen -a</command>
            is required, for example if rndc is to be used remotely,
            you should run <command>rndc-confgen</command> without
            the
            <command>-a</command> option and set up a
            <filename>rndc.conf</filename> and
            <filename>named.conf</filename>
            as directed.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-A <replaceable class="parameter">algorithm</replaceable></term>
        <listitem>
          <para>
            Specifies the algorithm to use for the TSIG key.  Available
            choices are: hmac-md5, hmac-sha1, hmac-sha224, hmac-sha256,
            hmac-sha384 and hmac-sha512.  The default is hmac-md5 or
            if MD5 was disabled hmac-sha256.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-b <replaceable class="parameter">keysize</replaceable></term>
        <listitem>
          <para>
            Specifies the size of the authentication key in bits.
            Must be between 1 and 512 bits; the default is the
            hash size.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-c <replaceable class="parameter">keyfile</replaceable></term>
        <listitem>
          <para>
            Used with the <command>-a</command> option to specify
            an alternate location for <filename>rndc.key</filename>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-h</term>
        <listitem>
          <para>
            Prints a short summary of the options and arguments to
            <command>rndc-confgen</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-k <replaceable class="parameter">keyname</replaceable></term>
        <listitem>
          <para>
            Specifies the key name of the rndc authentication key.
            This must be a valid domain name.
            The default is <constant>rndc-key</constant>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-p <replaceable class="parameter">port</replaceable></term>
        <listitem>
          <para>
            Specifies the command channel port where <command>named</command>
            listens for connections from <command>rndc</command>.
            The default is 953.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-r <replaceable class="parameter">randomfile</replaceable></term>
        <listitem>
          <para>
            Specifies a source of random data for generating the
            authorization.  If the operating
            system does not provide a <filename>/dev/random</filename>
            or equivalent device, the default source of randomness
            is keyboard input.  <filename>randomdev</filename>
            specifies
            the name of a character device or file containing random
            data to be used instead of the default.  The special value
            <filename>keyboard</filename> indicates that keyboard
            input should be used.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-s <replaceable class="parameter">address</replaceable></term>
        <listitem>
          <para>
            Specifies the IP address where <command>named</command>
            listens for command channel connections from
            <command>rndc</command>.  The default is the loopback
            address 127.0.0.1.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-t <replaceable class="parameter">chrootdir</replaceable></term>
        <listitem>
          <para>
            Used with the <command>-a</command> option to specify
            a directory where <command>named</command> will run
            chrooted.  An additional copy of the <filename>rndc.key</filename>
            will be written relative to this directory so that
            it will be found by the chrooted <command>named</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-u <replaceable class="parameter">user</replaceable></term>
        <listitem>
          <para>
            Used with the <command>-a</command> option to set the
            owner
            of the <filename>rndc.key</filename> file generated.
            If
            <command>-t</command> is also specified only the file
            in
            the chroot area has its owner changed.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>EXAMPLES</title></info>

    <para>
      To allow <command>rndc</command> to be used with
      no manual configuration, run
    </para>
    <para><userinput>rndc-confgen -a</userinput>
    </para>
    <para>
      To print a sample <filename>rndc.conf</filename> file and
      corresponding <command>controls</command> and <command>key</command>
      statements to be manually inserted into <filename>named.conf</filename>,
      run
    </para>
    <para><userinput>rndc-confgen</userinput>
    </para>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
        <refentrytitle>rndc</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>rndc.conf</refentrytitle><manvolnum>5</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>named</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>.
    </para>
  </refsection>

</refentry>
