.\" Copyright (C) 2001, 2003-2005, 2007, 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: rndc-confgen
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2013-03-14
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "RNDC\-CONFGEN" "8" "2013\-03\-14" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
rndc-confgen \- rndc key generation tool
.SH "SYNOPSIS"
.HP \w'\fBrndc\-confgen\fR\ 'u
\fBrndc\-confgen\fR [\fB\-a\fR] [\fB\-A\ \fR\fB\fIalgorithm\fR\fR] [\fB\-b\ \fR\fB\fIkeysize\fR\fR] [\fB\-c\ \fR\fB\fIkeyfile\fR\fR] [\fB\-h\fR] [\fB\-k\ \fR\fB\fIkeyname\fR\fR] [\fB\-p\ \fR\fB\fIport\fR\fR] [\fB\-r\ \fR\fB\fIrandomfile\fR\fR] [\fB\-s\ \fR\fB\fIaddress\fR\fR] [\fB\-t\ \fR\fB\fIchrootdir\fR\fR] [\fB\-u\ \fR\fB\fIuser\fR\fR]
.SH "DESCRIPTION"
.PP
\fBrndc\-confgen\fR
generates configuration files for
\fBrndc\fR\&. It can be used as a convenient alternative to writing the
rndc\&.conf
file and the corresponding
\fBcontrols\fR
and
\fBkey\fR
statements in
named\&.conf
by hand\&. Alternatively, it can be run with the
\fB\-a\fR
option to set up a
rndc\&.key
file and avoid the need for a
rndc\&.conf
file and a
\fBcontrols\fR
statement altogether\&.
.SH "OPTIONS"
.PP
\-a
.RS 4
Do automatic
\fBrndc\fR
configuration\&. This creates a file
rndc\&.key
in
/etc
(or whatever
\fIsysconfdir\fR
was specified as when
BIND
was built) that is read by both
\fBrndc\fR
and
\fBnamed\fR
on startup\&. The
rndc\&.key
file defines a default command channel and authentication key allowing
\fBrndc\fR
to communicate with
\fBnamed\fR
on the local host with no further configuration\&.
.sp
Running
\fBrndc\-confgen \-a\fR
allows BIND 9 and
\fBrndc\fR
to be used as drop\-in replacements for BIND 8 and
\fBndc\fR, with no changes to the existing BIND 8
named\&.conf
file\&.
.sp
If a more elaborate configuration than that generated by
\fBrndc\-confgen \-a\fR
is required, for example if rndc is to be used remotely, you should run
\fBrndc\-confgen\fR
without the
\fB\-a\fR
option and set up a
rndc\&.conf
and
named\&.conf
as directed\&.
.RE
.PP
\-A \fIalgorithm\fR
.RS 4
Specifies the algorithm to use for the TSIG key\&. Available choices are: hmac\-md5, hmac\-sha1, hmac\-sha224, hmac\-sha256, hmac\-sha384 and hmac\-sha512\&. The default is hmac\-md5 or if MD5 was disabled hmac\-sha256\&.
.RE
.PP
\-b \fIkeysize\fR
.RS 4
Specifies the size of the authentication key in bits\&. Must be between 1 and 512 bits; the default is the hash size\&.
.RE
.PP
\-c \fIkeyfile\fR
.RS 4
Used with the
\fB\-a\fR
option to specify an alternate location for
rndc\&.key\&.
.RE
.PP
\-h
.RS 4
Prints a short summary of the options and arguments to
\fBrndc\-confgen\fR\&.
.RE
.PP
\-k \fIkeyname\fR
.RS 4
Specifies the key name of the rndc authentication key\&. This must be a valid domain name\&. The default is
\fBrndc\-key\fR\&.
.RE
.PP
\-p \fIport\fR
.RS 4
Specifies the command channel port where
\fBnamed\fR
listens for connections from
\fBrndc\fR\&. The default is 953\&.
.RE
.PP
\-r \fIrandomfile\fR
.RS 4
Specifies a source of random data for generating the authorization\&. If the operating system does not provide a
/dev/random
or equivalent device, the default source of randomness is keyboard input\&.
randomdev
specifies the name of a character device or file containing random data to be used instead of the default\&. The special value
keyboard
indicates that keyboard input should be used\&.
.RE
.PP
\-s \fIaddress\fR
.RS 4
Specifies the IP address where
\fBnamed\fR
listens for command channel connections from
\fBrndc\fR\&. The default is the loopback address 127\&.0\&.0\&.1\&.
.RE
.PP
\-t \fIchrootdir\fR
.RS 4
Used with the
\fB\-a\fR
option to specify a directory where
\fBnamed\fR
will run chrooted\&. An additional copy of the
rndc\&.key
will be written relative to this directory so that it will be found by the chrooted
\fBnamed\fR\&.
.RE
.PP
\-u \fIuser\fR
.RS 4
Used with the
\fB\-a\fR
option to set the owner of the
rndc\&.key
file generated\&. If
\fB\-t\fR
is also specified only the file in the chroot area has its owner changed\&.
.RE
.SH "EXAMPLES"
.PP
To allow
\fBrndc\fR
to be used with no manual configuration, run
.PP
\fBrndc\-confgen \-a\fR
.PP
To print a sample
rndc\&.conf
file and corresponding
\fBcontrols\fR
and
\fBkey\fR
statements to be manually inserted into
named\&.conf, run
.PP
\fBrndc\-confgen\fR
.SH "SEE ALSO"
.PP
\fBrndc\fR(8),
\fBrndc.conf\fR(5),
\fBnamed\fR(8),
BIND 9 Administrator Reference Manual\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2001, 2003-2005, 2007, 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
