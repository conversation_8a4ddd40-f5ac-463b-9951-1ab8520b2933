<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>ddns-confgen</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.ddns-confgen"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">ddns-confgen</span>
     &#8212; ddns key generation tool
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">tsig-keygen</code> 
       [<code class="option">-a <em class="replaceable"><code>algorithm</code></em></code>]
       [<code class="option">-h</code>]
       [<code class="option">-r <em class="replaceable"><code>randomfile</code></em></code>]
       [name]
    </p></div>
    <div class="cmdsynopsis"><p>
      <code class="command">ddns-confgen</code> 
       [<code class="option">-a <em class="replaceable"><code>algorithm</code></em></code>]
       [<code class="option">-h</code>]
       [<code class="option">-k <em class="replaceable"><code>keyname</code></em></code>]
       [<code class="option">-q</code>]
       [<code class="option">-r <em class="replaceable"><code>randomfile</code></em></code>]
       [
         -s <em class="replaceable"><code>name</code></em> 
         |   -z <em class="replaceable"><code>zone</code></em> 
      ]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p>
      <span class="command"><strong>tsig-keygen</strong></span> and <span class="command"><strong>ddns-confgen</strong></span>
      are invocation methods for a utility that generates keys for use
      in TSIG signing.  The resulting keys can be used, for example,
      to secure dynamic DNS updates to a zone or for the
      <span class="command"><strong>rndc</strong></span> command channel.
    </p>

    <p>
      When run as <span class="command"><strong>tsig-keygen</strong></span>, a domain name
      can be specified on the command line which will be used as
      the name of the generated key.  If no name is specified,
      the default is <code class="constant">tsig-key</code>.
    </p>

    <p>
      When run as <span class="command"><strong>ddns-confgen</strong></span>, the generated
      key is accompanied by configuration text and instructions
      that can be used with <span class="command"><strong>nsupdate</strong></span> and
      <span class="command"><strong>named</strong></span> when setting up dynamic DNS,
      including an example <span class="command"><strong>update-policy</strong></span>
      statement.  (This usage similar to the
      <span class="command"><strong>rndc-confgen</strong></span> command for setting
      up command channel security.)
    </p>

    <p>
      Note that <span class="command"><strong>named</strong></span> itself can configure a
      local DDNS key for use with <span class="command"><strong>nsupdate -l</strong></span>:
      it does this when a zone is configured with
      <span class="command"><strong>update-policy local;</strong></span>.
      <span class="command"><strong>ddns-confgen</strong></span> is only needed when a
      more elaborate configuration is required: for instance,
      if <span class="command"><strong>nsupdate</strong></span> is to be used from a remote
      system.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-a <em class="replaceable"><code>algorithm</code></em></span></dt>
<dd>
	  <p>
            Specifies the algorithm to use for the TSIG key.  Available
            choices are: hmac-md5, hmac-sha1, hmac-sha224, hmac-sha256,
            hmac-sha384 and hmac-sha512.  The default is hmac-sha256.
            Options are case-insensitive, and the "hmac-" prefix
            may be omitted.
	  </p>
	</dd>
<dt><span class="term">-h</span></dt>
<dd>
	  <p>
	    Prints a short summary of options and arguments.
	  </p>
	</dd>
<dt><span class="term">-k <em class="replaceable"><code>keyname</code></em></span></dt>
<dd>
	  <p>
	    Specifies the key name of the DDNS authentication key.
	    The default is <code class="constant">ddns-key</code> when neither
	    the <code class="option">-s</code> nor <code class="option">-z</code> option is
	    specified; otherwise, the default
	    is <code class="constant">ddns-key</code> as a separate label
	    followed by the argument of the option, e.g.,
	    <code class="constant">ddns-key.example.com.</code>
	    The key name must have the format of a valid domain name,
	    consisting of letters, digits, hyphens and periods.
	  </p>
	</dd>
<dt><span class="term">-q</span></dt>
<dd>
	  <p>
	    (<span class="command"><strong>ddns-confgen</strong></span> only.) Quiet mode:  Print
            only the key, with no explanatory text or usage examples;
            This is essentially identical to <span class="command"><strong>tsig-keygen</strong></span>.
	  </p>
	</dd>
<dt><span class="term">-r <em class="replaceable"><code>randomfile</code></em></span></dt>
<dd>
	  <p>
            Specifies a source of random data for generating the
            authorization.  If the operating system does not provide a
            <code class="filename">/dev/random</code> or equivalent device, the
            default source of randomness is keyboard input.
            <code class="filename">randomdev</code> specifies the name of a
            character device or file containing random data to be used
            instead of the default.  The special value
            <code class="filename">keyboard</code> indicates that keyboard input
            should be used.
	  </p>
	</dd>
<dt><span class="term">-s <em class="replaceable"><code>name</code></em></span></dt>
<dd>
	  <p>
            (<span class="command"><strong>ddns-confgen</strong></span> only.)
	    Generate configuration example to allow dynamic updates
            of a single hostname.  The example <span class="command"><strong>named.conf</strong></span>
            text shows how to set an update policy for the specified
            <em class="replaceable"><code>name</code></em>
	    using the "name" nametype.  The default key name is
	    ddns-key.<em class="replaceable"><code>name</code></em>.
	    Note that the "self" nametype cannot be used, since
	    the name to be updated may differ from the key name.
	    This option cannot be used with the <code class="option">-z</code> option.
	  </p>
	</dd>
<dt><span class="term">-z <em class="replaceable"><code>zone</code></em></span></dt>
<dd>
	  <p>
            (<span class="command"><strong>ddns-confgen</strong></span> only.)
	    Generate configuration example to allow dynamic updates
            of a zone:  The example <span class="command"><strong>named.conf</strong></span> text
            shows how to set an update policy for the specified
	    <em class="replaceable"><code>zone</code></em>
	    using the "zonesub" nametype, allowing updates to
            all subdomain names within that
            <em class="replaceable"><code>zone</code></em>.
	    This option cannot be used with the <code class="option">-s</code> option.
	  </p>
	</dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
	<span class="refentrytitle">nsupdate</span>(1)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">named.conf</span>(5)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">named</span>(8)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>.
    </p>
  </div>

</div></body>
</html>
