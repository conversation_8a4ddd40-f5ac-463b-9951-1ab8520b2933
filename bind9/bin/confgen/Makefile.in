# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

# $Id: Makefile.in,v 1.8 2009/12/05 23:31:40 each Exp $

.NOTPARALLEL:

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

# Attempt to disable parallel processing.
.NOTPARALLEL:
.NO_PARALLEL:

VERSION=@BIND9_VERSION@

@BIND9_MAKE_INCLUDES@

CINCLUDES = -I${srcdir}/include ${ISC_INCLUDES} ${ISCCC_INCLUDES} \
	${ISCCFG_INCLUDES} ${DNS_INCLUDES} ${BIND9_INCLUDES}

CDEFINES =
CWARNINGS =

ISCCFGLIBS =	../../lib/isccfg/libisccfg.@A@
ISCCCLIBS =	../../lib/isccc/libisccc.@A@
ISCLIBS =	../../lib/isc/libisc.@A@
ISCNOSYMLIBS =	../../lib/isc/libisc-nosymtbl.@A@
DNSLIBS =	../../lib/dns/libdns.@A@ @DNS_CRYPTO_LIBS@
BIND9LIBS =	../../lib/bind9/libbind9.@A@

ISCCFGDEPLIBS =	../../lib/isccfg/libisccfg.@A@
ISCCCDEPLIBS =	../../lib/isccc/libisccc.@A@
ISCDEPLIBS =	../../lib/isc/libisc.@A@
DNSDEPLIBS =	../../lib/dns/libdns.@A@
BIND9DEPLIBS =	../../lib/bind9/libbind9.@A@

RNDCLIBS =	${ISCCFGLIBS} ${ISCCCLIBS} ${BIND9LIBS} ${DNSLIBS} ${ISCLIBS} @LIBS@
RNDCDEPLIBS =	${ISCCFGDEPLIBS} ${ISCCCDEPLIBS} ${BIND9DEPLIBS} ${DNSDEPLIBS} ${ISCDEPLIBS}

LIBS =		${DNSLIBS} ${ISCLIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}

NOSYMLIBS =	${DNSLIBS} ${ISCNOSYMLIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}

CONFDEPLIBS =	${DNSDEPLIBS} ${ISCDEPLIBS}

SRCS=		rndc-confgen.c ddns-confgen.c

SUBDIRS =	unix

TARGETS =	rndc-confgen@EXEEXT@ ddns-confgen@EXEEXT@ tsig-keygen@EXEEXT@

include ../../Makefile-infoblox.inc

MANPAGES =	rndc-confgen.8 ddns-confgen.8

HTMLPAGES =	rndc-confgen.html ddns-confgen.html

MANOBJS =	${MANPAGES} ${HTMLPAGES}

UOBJS =		unix/os.@O@

@BIND9_MAKE_RULES@

rndc-confgen.@O@: rndc-confgen.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} \
		-DRNDC_KEYFILE=\"${sysconfdir}/rndc.key\" \
		-c ${srcdir}/rndc-confgen.c

ddns-confgen.@O@: ddns-confgen.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} -c ${srcdir}/ddns-confgen.c

rndc-confgen@EXEEXT@: rndc-confgen.@O@ util.@O@ keygen.@O@ ${CONFDEPLIBS}
	export BASEOBJS="rndc-confgen.@O@ util.@O@ keygen.@O@ ${UOBJS}"; \
	${FINALBUILDCMD}

ddns-confgen@EXEEXT@: ddns-confgen.@O@ util.@O@ keygen.@O@ ${CONFDEPLIBS}
	export BASEOBJS="ddns-confgen.@O@ util.@O@ keygen.@O@ ${UOBJS}"; \
	${FINALBUILDCMD}

# make a link in the build directory to assist with testing
tsig-keygen@EXEEXT@: ddns-confgen@EXEEXT@
	rm -f tsig-keygen@EXEEXT@
	${LINK_PROGRAM} ddns-confgen@EXEEXT@ tsig-keygen@EXEEXT@

doc man:: ${MANOBJS}

docclean manclean maintainer-clean::
	rm -f ${MANOBJS}

installdirs:
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${sbindir}
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${mandir}/man8

install:: rndc-confgen@EXEEXT@ ddns-confgen@EXEEXT@ installdirs
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} rndc-confgen@EXEEXT@ ${DESTDIR}${sbindir}
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} ddns-confgen@EXEEXT@ ${DESTDIR}${sbindir}
	${INSTALL_DATA} ${srcdir}/rndc-confgen.8 ${DESTDIR}${mandir}/man8
	${INSTALL_DATA} ${srcdir}/ddns-confgen.8 ${DESTDIR}${mandir}/man8
	(cd ${DESTDIR}${sbindir}; rm -f tsig-keygen@EXEEXT@; ${LINK_PROGRAM} ddns-confgen@EXEEXT@ tsig-keygen@EXEEXT@)
	(cd ${DESTDIR}${mandir}/man8; rm -f tsig-keygen.8; ${LINK_PROGRAM} ddns-confgen.8 tsig-keygen.8)

uninstall::
	rm -f ${DESTDIR}${mandir}/man8/tsig-keygen.8
	rm -f ${DESTDIR}${sbindir}/tsig-keygen@EXEEXT@
	rm -f ${DESTDIR}${mandir}/man8/ddns-confgen.8
	rm -f ${DESTDIR}${mandir}/man8/rndc-confgen.8
	${LIBTOOL_MODE_UNINSTALL} rm -f ${DESTDIR}${sbindir}/ddns-confgen@EXEEXT@
	${LIBTOOL_MODE_UNINSTALL} rm -f ${DESTDIR}${sbindir}/rndc-confgen@EXEEXT@

clean distclean maintainer-clean::
	rm -f ${TARGETS}
