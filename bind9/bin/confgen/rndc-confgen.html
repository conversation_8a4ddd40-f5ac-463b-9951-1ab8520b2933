<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2001, 2003-2005, 2007, 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>rndc-confgen</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.rndc-confgen"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">rndc-confgen</span>
     &#8212; rndc key generation tool
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">rndc-confgen</code> 
       [<code class="option">-a</code>]
       [<code class="option">-A <em class="replaceable"><code>algorithm</code></em></code>]
       [<code class="option">-b <em class="replaceable"><code>keysize</code></em></code>]
       [<code class="option">-c <em class="replaceable"><code>keyfile</code></em></code>]
       [<code class="option">-h</code>]
       [<code class="option">-k <em class="replaceable"><code>keyname</code></em></code>]
       [<code class="option">-p <em class="replaceable"><code>port</code></em></code>]
       [<code class="option">-r <em class="replaceable"><code>randomfile</code></em></code>]
       [<code class="option">-s <em class="replaceable"><code>address</code></em></code>]
       [<code class="option">-t <em class="replaceable"><code>chrootdir</code></em></code>]
       [<code class="option">-u <em class="replaceable"><code>user</code></em></code>]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>rndc-confgen</strong></span>
      generates configuration files
      for <span class="command"><strong>rndc</strong></span>.  It can be used as a
      convenient alternative to writing the
      <code class="filename">rndc.conf</code> file
      and the corresponding <span class="command"><strong>controls</strong></span>
      and <span class="command"><strong>key</strong></span>
      statements in <code class="filename">named.conf</code> by hand.
      Alternatively, it can be run with the <span class="command"><strong>-a</strong></span>
      option to set up a <code class="filename">rndc.key</code> file and
      avoid the need for a <code class="filename">rndc.conf</code> file
      and a <span class="command"><strong>controls</strong></span> statement altogether.
    </p>

  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-a</span></dt>
<dd>
          <p>
            Do automatic <span class="command"><strong>rndc</strong></span> configuration.
            This creates a file <code class="filename">rndc.key</code>
            in <code class="filename">/etc</code> (or whatever
            <code class="varname">sysconfdir</code>
            was specified as when <acronym class="acronym">BIND</acronym> was
            built)
            that is read by both <span class="command"><strong>rndc</strong></span>
            and <span class="command"><strong>named</strong></span> on startup.  The
            <code class="filename">rndc.key</code> file defines a default
            command channel and authentication key allowing
            <span class="command"><strong>rndc</strong></span> to communicate with
            <span class="command"><strong>named</strong></span> on the local host
            with no further configuration.
          </p>
          <p>
            Running <span class="command"><strong>rndc-confgen -a</strong></span> allows
            BIND 9 and <span class="command"><strong>rndc</strong></span> to be used as
            drop-in
            replacements for BIND 8 and <span class="command"><strong>ndc</strong></span>,
            with no changes to the existing BIND 8
            <code class="filename">named.conf</code> file.
          </p>
          <p>
            If a more elaborate configuration than that
            generated by <span class="command"><strong>rndc-confgen -a</strong></span>
            is required, for example if rndc is to be used remotely,
            you should run <span class="command"><strong>rndc-confgen</strong></span> without
            the
            <span class="command"><strong>-a</strong></span> option and set up a
            <code class="filename">rndc.conf</code> and
            <code class="filename">named.conf</code>
            as directed.
          </p>
        </dd>
<dt><span class="term">-A <em class="replaceable"><code>algorithm</code></em></span></dt>
<dd>
          <p>
            Specifies the algorithm to use for the TSIG key.  Available
            choices are: hmac-md5, hmac-sha1, hmac-sha224, hmac-sha256,
            hmac-sha384 and hmac-sha512.  The default is hmac-md5 or
            if MD5 was disabled hmac-sha256.
          </p>
        </dd>
<dt><span class="term">-b <em class="replaceable"><code>keysize</code></em></span></dt>
<dd>
          <p>
            Specifies the size of the authentication key in bits.
            Must be between 1 and 512 bits; the default is the
            hash size.
          </p>
        </dd>
<dt><span class="term">-c <em class="replaceable"><code>keyfile</code></em></span></dt>
<dd>
          <p>
            Used with the <span class="command"><strong>-a</strong></span> option to specify
            an alternate location for <code class="filename">rndc.key</code>.
          </p>
        </dd>
<dt><span class="term">-h</span></dt>
<dd>
          <p>
            Prints a short summary of the options and arguments to
            <span class="command"><strong>rndc-confgen</strong></span>.
          </p>
        </dd>
<dt><span class="term">-k <em class="replaceable"><code>keyname</code></em></span></dt>
<dd>
          <p>
            Specifies the key name of the rndc authentication key.
            This must be a valid domain name.
            The default is <code class="constant">rndc-key</code>.
          </p>
        </dd>
<dt><span class="term">-p <em class="replaceable"><code>port</code></em></span></dt>
<dd>
          <p>
            Specifies the command channel port where <span class="command"><strong>named</strong></span>
            listens for connections from <span class="command"><strong>rndc</strong></span>.
            The default is 953.
          </p>
        </dd>
<dt><span class="term">-r <em class="replaceable"><code>randomfile</code></em></span></dt>
<dd>
          <p>
            Specifies a source of random data for generating the
            authorization.  If the operating
            system does not provide a <code class="filename">/dev/random</code>
            or equivalent device, the default source of randomness
            is keyboard input.  <code class="filename">randomdev</code>
            specifies
            the name of a character device or file containing random
            data to be used instead of the default.  The special value
            <code class="filename">keyboard</code> indicates that keyboard
            input should be used.
          </p>
        </dd>
<dt><span class="term">-s <em class="replaceable"><code>address</code></em></span></dt>
<dd>
          <p>
            Specifies the IP address where <span class="command"><strong>named</strong></span>
            listens for command channel connections from
            <span class="command"><strong>rndc</strong></span>.  The default is the loopback
            address 127.0.0.1.
          </p>
        </dd>
<dt><span class="term">-t <em class="replaceable"><code>chrootdir</code></em></span></dt>
<dd>
          <p>
            Used with the <span class="command"><strong>-a</strong></span> option to specify
            a directory where <span class="command"><strong>named</strong></span> will run
            chrooted.  An additional copy of the <code class="filename">rndc.key</code>
            will be written relative to this directory so that
            it will be found by the chrooted <span class="command"><strong>named</strong></span>.
          </p>
        </dd>
<dt><span class="term">-u <em class="replaceable"><code>user</code></em></span></dt>
<dd>
          <p>
            Used with the <span class="command"><strong>-a</strong></span> option to set the
            owner
            of the <code class="filename">rndc.key</code> file generated.
            If
            <span class="command"><strong>-t</strong></span> is also specified only the file
            in
            the chroot area has its owner changed.
          </p>
        </dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>EXAMPLES</h2>

    <p>
      To allow <span class="command"><strong>rndc</strong></span> to be used with
      no manual configuration, run
    </p>
    <p><strong class="userinput"><code>rndc-confgen -a</code></strong>
    </p>
    <p>
      To print a sample <code class="filename">rndc.conf</code> file and
      corresponding <span class="command"><strong>controls</strong></span> and <span class="command"><strong>key</strong></span>
      statements to be manually inserted into <code class="filename">named.conf</code>,
      run
    </p>
    <p><strong class="userinput"><code>rndc-confgen</code></strong>
    </p>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
        <span class="refentrytitle">rndc</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">rndc.conf</span>(5)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">named</span>(8)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>.
    </p>
  </div>

</div></body>
</html>
