.\" Copyright (C) 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: ddns-confgen
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-03-06
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DDNS\-CONFGEN" "8" "2014\-03\-06" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
ddns-confgen \- ddns key generation tool
.SH "SYNOPSIS"
.HP \w'\fBtsig\-keygen\fR\ 'u
\fBtsig\-keygen\fR [\fB\-a\ \fR\fB\fIalgorithm\fR\fR] [\fB\-h\fR] [\fB\-r\ \fR\fB\fIrandomfile\fR\fR] [name]
.HP \w'\fBddns\-confgen\fR\ 'u
\fBddns\-confgen\fR [\fB\-a\ \fR\fB\fIalgorithm\fR\fR] [\fB\-h\fR] [\fB\-k\ \fR\fB\fIkeyname\fR\fR] [\fB\-q\fR] [\fB\-r\ \fR\fB\fIrandomfile\fR\fR] [\-s\ \fIname\fR | \-z\ \fIzone\fR]
.SH "DESCRIPTION"
.PP
\fBtsig\-keygen\fR
and
\fBddns\-confgen\fR
are invocation methods for a utility that generates keys for use in TSIG signing\&. The resulting keys can be used, for example, to secure dynamic DNS updates to a zone or for the
\fBrndc\fR
command channel\&.
.PP
When run as
\fBtsig\-keygen\fR, a domain name can be specified on the command line which will be used as the name of the generated key\&. If no name is specified, the default is
\fBtsig\-key\fR\&.
.PP
When run as
\fBddns\-confgen\fR, the generated key is accompanied by configuration text and instructions that can be used with
\fBnsupdate\fR
and
\fBnamed\fR
when setting up dynamic DNS, including an example
\fBupdate\-policy\fR
statement\&. (This usage similar to the
\fBrndc\-confgen\fR
command for setting up command channel security\&.)
.PP
Note that
\fBnamed\fR
itself can configure a local DDNS key for use with
\fBnsupdate \-l\fR: it does this when a zone is configured with
\fBupdate\-policy local;\fR\&.
\fBddns\-confgen\fR
is only needed when a more elaborate configuration is required: for instance, if
\fBnsupdate\fR
is to be used from a remote system\&.
.SH "OPTIONS"
.PP
\-a \fIalgorithm\fR
.RS 4
Specifies the algorithm to use for the TSIG key\&. Available choices are: hmac\-md5, hmac\-sha1, hmac\-sha224, hmac\-sha256, hmac\-sha384 and hmac\-sha512\&. The default is hmac\-sha256\&. Options are case\-insensitive, and the "hmac\-" prefix may be omitted\&.
.RE
.PP
\-h
.RS 4
Prints a short summary of options and arguments\&.
.RE
.PP
\-k \fIkeyname\fR
.RS 4
Specifies the key name of the DDNS authentication key\&. The default is
\fBddns\-key\fR
when neither the
\fB\-s\fR
nor
\fB\-z\fR
option is specified; otherwise, the default is
\fBddns\-key\fR
as a separate label followed by the argument of the option, e\&.g\&.,
\fBddns\-key\&.example\&.com\&.\fR
The key name must have the format of a valid domain name, consisting of letters, digits, hyphens and periods\&.
.RE
.PP
\-q
.RS 4
(\fBddns\-confgen\fR
only\&.) Quiet mode: Print only the key, with no explanatory text or usage examples; This is essentially identical to
\fBtsig\-keygen\fR\&.
.RE
.PP
\-r \fIrandomfile\fR
.RS 4
Specifies a source of random data for generating the authorization\&. If the operating system does not provide a
/dev/random
or equivalent device, the default source of randomness is keyboard input\&.
randomdev
specifies the name of a character device or file containing random data to be used instead of the default\&. The special value
keyboard
indicates that keyboard input should be used\&.
.RE
.PP
\-s \fIname\fR
.RS 4
(\fBddns\-confgen\fR
only\&.) Generate configuration example to allow dynamic updates of a single hostname\&. The example
\fBnamed\&.conf\fR
text shows how to set an update policy for the specified
\fIname\fR
using the "name" nametype\&. The default key name is ddns\-key\&.\fIname\fR\&. Note that the "self" nametype cannot be used, since the name to be updated may differ from the key name\&. This option cannot be used with the
\fB\-z\fR
option\&.
.RE
.PP
\-z \fIzone\fR
.RS 4
(\fBddns\-confgen\fR
only\&.) Generate configuration example to allow dynamic updates of a zone: The example
\fBnamed\&.conf\fR
text shows how to set an update policy for the specified
\fIzone\fR
using the "zonesub" nametype, allowing updates to all subdomain names within that
\fIzone\fR\&. This option cannot be used with the
\fB\-s\fR
option\&.
.RE
.SH "SEE ALSO"
.PP
\fBnsupdate\fR(1),
\fBnamed.conf\fR(5),
\fBnamed\fR(8),
BIND 9 Administrator Reference Manual\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
