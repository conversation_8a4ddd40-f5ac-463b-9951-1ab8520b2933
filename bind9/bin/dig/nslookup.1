.\" Copyright (C) 2004-2007, 2010, 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: nslookup
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-01-24
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "NSLOOKUP" "1" "2014\-01\-24" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
nslookup \- query Internet name servers interactively
.SH "SYNOPSIS"
.HP \w'\fBnslookup\fR\ 'u
\fBnslookup\fR [\fB\-option\fR] [name\ |\ \-] [server]
.SH "DESCRIPTION"
.PP
\fBNslookup\fR
is a program to query Internet domain name servers\&.
\fBNslookup\fR
has two modes: interactive and non\-interactive\&. Interactive mode allows the user to query name servers for information about various hosts and domains or to print a list of hosts in a domain\&. Non\-interactive mode is used to print just the name and requested information for a host or domain\&.
.SH "ARGUMENTS"
.PP
Interactive mode is entered in the following cases:
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
when no arguments are given (the default name server will be used)
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
when the first argument is a hyphen (\-) and the second argument is the host name or Internet address of a name server\&.
.RE
.PP
Non\-interactive mode is used when the name or Internet address of the host to be looked up is given as the first argument\&. The optional second argument specifies the host name or address of a name server\&.
.PP
Options can also be specified on the command line if they precede the arguments and are prefixed with a hyphen\&. For example, to change the default query type to host information, and the initial timeout to 10 seconds, type:
.sp
.if n \{\
.RS 4
.\}
.nf
nslookup \-query=hinfo  \-timeout=10
.fi
.if n \{\
.RE
.\}
.PP
The
\fB\-version\fR
option causes
\fBnslookup\fR
to print the version number and immediately exits\&.
.SH "INTERACTIVE COMMANDS"
.PP
\fBhost\fR [server]
.RS 4
Look up information for host using the current default server or using server, if specified\&. If host is an Internet address and the query type is A or PTR, the name of the host is returned\&. If host is a name and does not have a trailing period, the search list is used to qualify the name\&.
.sp
To look up a host not in the current domain, append a period to the name\&.
.RE
.PP
\fBserver\fR \fIdomain\fR
.RS 4
.RE
.PP
\fBlserver\fR \fIdomain\fR
.RS 4
Change the default server to
\fIdomain\fR;
\fBlserver\fR
uses the initial server to look up information about
\fIdomain\fR, while
\fBserver\fR
uses the current default server\&. If an authoritative answer can\*(Aqt be found, the names of servers that might have the answer are returned\&.
.RE
.PP
\fBroot\fR
.RS 4
not implemented
.RE
.PP
\fBfinger\fR
.RS 4
not implemented
.RE
.PP
\fBls\fR
.RS 4
not implemented
.RE
.PP
\fBview\fR
.RS 4
not implemented
.RE
.PP
\fBhelp\fR
.RS 4
not implemented
.RE
.PP
\fB?\fR
.RS 4
not implemented
.RE
.PP
\fBexit\fR
.RS 4
Exits the program\&.
.RE
.PP
\fBset\fR \fIkeyword\fR\fI[=value]\fR
.RS 4
This command is used to change state information that affects the lookups\&. Valid keywords are:
.PP
\fBall\fR
.RS 4
Prints the current values of the frequently used options to
\fBset\fR\&. Information about the current default server and host is also printed\&.
.RE
.PP
\fBclass=\fR\fIvalue\fR
.RS 4
Change the query class to one of:
.PP
\fBIN\fR
.RS 4
the Internet class
.RE
.PP
\fBCH\fR
.RS 4
the Chaos class
.RE
.PP
\fBHS\fR
.RS 4
the Hesiod class
.RE
.PP
\fBANY\fR
.RS 4
wildcard
.RE
.sp
The class specifies the protocol group of the information\&.
.sp
(Default = IN; abbreviation = cl)
.RE
.PP
\fB\fI[no]\fR\fR\fBdebug\fR
.RS 4
Turn on or off the display of the full response packet and any intermediate response packets when searching\&.
.sp
(Default = nodebug; abbreviation =
[no]deb)
.RE
.PP
\fB\fI[no]\fR\fR\fBd2\fR
.RS 4
Turn debugging mode on or off\&. This displays more about what nslookup is doing\&.
.sp
(Default = nod2)
.RE
.PP
\fBdomain=\fR\fIname\fR
.RS 4
Sets the search list to
\fIname\fR\&.
.RE
.PP
\fB\fI[no]\fR\fR\fBsearch\fR
.RS 4
If the lookup request contains at least one period but doesn\*(Aqt end with a trailing period, append the domain names in the domain search list to the request until an answer is received\&.
.sp
(Default = search)
.RE
.PP
\fBport=\fR\fIvalue\fR
.RS 4
Change the default TCP/UDP name server port to
\fIvalue\fR\&.
.sp
(Default = 53; abbreviation = po)
.RE
.PP
\fBquerytype=\fR\fIvalue\fR
.RS 4
.RE
.PP
\fBtype=\fR\fIvalue\fR
.RS 4
Change the type of the information query\&.
.sp
(Default = A; abbreviations = q, ty)
.RE
.PP
\fB\fI[no]\fR\fR\fBrecurse\fR
.RS 4
Tell the name server to query other servers if it does not have the information\&.
.sp
(Default = recurse; abbreviation = [no]rec)
.RE
.PP
\fBndots=\fR\fInumber\fR
.RS 4
Set the number of dots (label separators) in a domain that will disable searching\&. Absolute names always stop searching\&.
.RE
.PP
\fBretry=\fR\fInumber\fR
.RS 4
Set the number of retries to number\&.
.RE
.PP
\fBtimeout=\fR\fInumber\fR
.RS 4
Change the initial timeout interval for waiting for a reply to number seconds\&.
.RE
.PP
\fB\fI[no]\fR\fR\fBvc\fR
.RS 4
Always use a virtual circuit when sending requests to the server\&.
.sp
(Default = novc)
.RE
.PP
\fB\fI[no]\fR\fR\fBfail\fR
.RS 4
Try the next nameserver if a nameserver responds with SERVFAIL or a referral (nofail) or terminate query (fail) on such a response\&.
.sp
(Default = nofail)
.RE
.sp
.RE
.SH "RETURN VALUES"
.PP
\fBnslookup\fR
returns with an exit status of 1 if any query failed, and 0 otherwise\&.
.SH "FILES"
.PP
/etc/resolv\&.conf
.SH "SEE ALSO"
.PP
\fBdig\fR(1),
\fBhost\fR(1),
\fBnamed\fR(8)\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2004-2007, 2010, 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
