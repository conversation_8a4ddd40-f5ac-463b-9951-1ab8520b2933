<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2004-2007, 2010, 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>nslookup</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.nslookup"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    nslookup
     &#8212; query Internet name servers interactively
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">nslookup</code> 
       [<code class="option">-option</code>]
       [name | -]
       [server]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>Nslookup</strong></span>
      is a program to query Internet domain name servers.  <span class="command"><strong>Nslookup</strong></span>
      has two modes: interactive and non-interactive.  Interactive mode allows
      the user to query name servers for information about various hosts and
      domains or to print a list of hosts in a domain.  Non-interactive mode
      is
      used to print just the name and requested information for a host or
      domain.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>ARGUMENTS</h2>

    <p>
      Interactive mode is entered in the following cases:
      </p>
<div class="orderedlist"><ol class="orderedlist" type="a">
<li class="listitem">
          <p>
            when no arguments are given (the default name server will be used)
          </p>
        </li>
<li class="listitem">
          <p>
            when the first argument is a hyphen (-) and the second argument is
            the host name or Internet address of a name server.
          </p>
        </li>
</ol></div>
<p>
    </p>

    <p>
      Non-interactive mode is used when the name or Internet address of the
      host to be looked up is given as the first argument. The optional second
      argument specifies the host name or address of a name server.
    </p>

    <p>
      Options can also be specified on the command line if they precede the
      arguments and are prefixed with a hyphen.  For example, to
      change the default query type to host information, and the initial
      timeout to 10 seconds, type:
      
        </p>
<pre class="programlisting">
nslookup -query=hinfo  -timeout=10
</pre>
<p>
      
    </p>
    <p>
      The <code class="option">-version</code> option causes
      <span class="command"><strong>nslookup</strong></span> to print the version
      number and immediately exits.
    </p>

  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>INTERACTIVE COMMANDS</h2>

    <div class="variablelist"><dl class="variablelist">
<dt><span class="term"><code class="constant">host</code> [<span class="optional">server</span>]</span></dt>
<dd>
          <p>
            Look up information for host using the current default server or
            using server, if specified.  If host is an Internet address and
            the query type is A or PTR, the name of the host is returned.
            If host is a name and does not have a trailing period, the
            search list is used to qualify the name.
          </p>

          <p>
            To look up a host not in the current domain, append a period to
            the name.
          </p>
        </dd>
<dt><span class="term"><code class="constant">server</code> <em class="replaceable"><code>domain</code></em></span></dt>
<dd>
          <p></p>
        </dd>
<dt><span class="term"><code class="constant">lserver</code> <em class="replaceable"><code>domain</code></em></span></dt>
<dd>
          <p>
            Change the default server to <em class="replaceable"><code>domain</code></em>; <code class="constant">lserver</code> uses the initial
            server to look up information about <em class="replaceable"><code>domain</code></em>, while <code class="constant">server</code> uses
            the current default server.  If an authoritative answer can't be
            found, the names of servers that might have the answer are
            returned.
          </p>
        </dd>
<dt><span class="term"><code class="constant">root</code></span></dt>
<dd>
          <p>
            not implemented
          </p>
        </dd>
<dt><span class="term"><code class="constant">finger</code></span></dt>
<dd>
          <p>
            not implemented
          </p>
        </dd>
<dt><span class="term"><code class="constant">ls</code></span></dt>
<dd>
          <p>
            not implemented
          </p>
        </dd>
<dt><span class="term"><code class="constant">view</code></span></dt>
<dd>
          <p>
            not implemented
          </p>
        </dd>
<dt><span class="term"><code class="constant">help</code></span></dt>
<dd>
          <p>
            not implemented
          </p>
        </dd>
<dt><span class="term"><code class="constant">?</code></span></dt>
<dd>
          <p>
            not implemented
          </p>
        </dd>
<dt><span class="term"><code class="constant">exit</code></span></dt>
<dd>
          <p>
            Exits the program.
          </p>
        </dd>
<dt><span class="term"><code class="constant">set</code>
          <em class="replaceable"><code>keyword[<span class="optional">=value</span>]</code></em></span></dt>
<dd>
          <p>
            This command is used to change state information that affects
            the lookups.  Valid keywords are:
            </p>
<div class="variablelist"><dl class="variablelist">
<dt><span class="term"><code class="constant">all</code></span></dt>
<dd>
                  <p>
                    Prints the current values of the frequently used
                    options to <span class="command"><strong>set</strong></span>.
                    Information about the  current default
                    server and host is also printed.
                  </p>
                </dd>
<dt><span class="term"><code class="constant">class=</code><em class="replaceable"><code>value</code></em></span></dt>
<dd>
                  <p>
                    Change the query class to one of:
                    </p>
<div class="variablelist"><dl class="variablelist">
<dt><span class="term"><code class="constant">IN</code></span></dt>
<dd>
                          <p>
                            the Internet class
                          </p>
                        </dd>
<dt><span class="term"><code class="constant">CH</code></span></dt>
<dd>
                          <p>
                            the Chaos class
                          </p>
                        </dd>
<dt><span class="term"><code class="constant">HS</code></span></dt>
<dd>
                          <p>
                            the Hesiod class
                          </p>
                        </dd>
<dt><span class="term"><code class="constant">ANY</code></span></dt>
<dd>
                          <p>
                            wildcard
                          </p>
                        </dd>
</dl></div>
<p>
                    The class specifies the protocol group of the information.

                  </p>
		  <p>
                    (Default = IN; abbreviation = cl)
                  </p>
                </dd>
<dt><span class="term"><code class="constant"><em class="replaceable"><code>[<span class="optional">no</span>]</code></em>debug</code></span></dt>
<dd>
                  <p>
		    Turn on or off the display of the full response packet and
		    any intermediate response packets when searching.
                  </p>
		  <p>
                    (Default = nodebug; abbreviation = [<span class="optional">no</span>]deb)
                  </p>
                </dd>
<dt><span class="term"><code class="constant"><em class="replaceable"><code>[<span class="optional">no</span>]</code></em>d2</code></span></dt>
<dd>
                  <p>
                    Turn debugging mode on or off.  This displays more about
	            what nslookup is doing.
                  </p>
		  <p>
                    (Default = nod2)
                  </p>
                </dd>
<dt><span class="term"><code class="constant">domain=</code><em class="replaceable"><code>name</code></em></span></dt>
<dd>
                  <p>
                    Sets the search list to <em class="replaceable"><code>name</code></em>.
                  </p>
                </dd>
<dt><span class="term"><code class="constant"><em class="replaceable"><code>[<span class="optional">no</span>]</code></em>search</code></span></dt>
<dd>
                  <p>
                    If the lookup request contains at least one period but
                    doesn't end with a trailing period, append the domain
                    names in the domain search list to the request until an
                    answer is received.
                  </p>
		  <p>
                    (Default = search)
                  </p>
                </dd>
<dt><span class="term"><code class="constant">port=</code><em class="replaceable"><code>value</code></em></span></dt>
<dd>
                  <p>
                    Change the default TCP/UDP name server port to <em class="replaceable"><code>value</code></em>.
                  </p>
		  <p>
                    (Default = 53; abbreviation = po)
                  </p>
                </dd>
<dt><span class="term"><code class="constant">querytype=</code><em class="replaceable"><code>value</code></em></span></dt>
<dd>
                  <p></p>
                </dd>
<dt><span class="term"><code class="constant">type=</code><em class="replaceable"><code>value</code></em></span></dt>
<dd>
                  <p>
                    Change the type of the information query.
                  </p>
		  <p>
                    (Default = A; abbreviations = q, ty)
                  </p>
                </dd>
<dt><span class="term"><code class="constant"><em class="replaceable"><code>[<span class="optional">no</span>]</code></em>recurse</code></span></dt>
<dd>
                  <p>
                    Tell the name server to query other servers if it does not
                    have the
                    information.
                  </p>
		  <p>
                    (Default = recurse; abbreviation = [no]rec)
                  </p>
                </dd>
<dt><span class="term"><code class="constant">ndots=</code><em class="replaceable"><code>number</code></em></span></dt>
<dd>
                  <p>
		    Set the number of dots (label separators) in a domain
		    that will disable searching.  Absolute names always
		    stop searching.
                  </p>
                </dd>
<dt><span class="term"><code class="constant">retry=</code><em class="replaceable"><code>number</code></em></span></dt>
<dd>
                  <p>
                    Set the number of retries to number.
                  </p>
                </dd>
<dt><span class="term"><code class="constant">timeout=</code><em class="replaceable"><code>number</code></em></span></dt>
<dd>
                  <p>
                    Change the initial timeout interval for waiting for a
                    reply to number seconds.
                  </p>
                </dd>
<dt><span class="term"><code class="constant"><em class="replaceable"><code>[<span class="optional">no</span>]</code></em>vc</code></span></dt>
<dd>
                  <p>
                    Always use a virtual circuit when sending requests to the
                    server.
                  </p>
		  <p>
                    (Default = novc)
                  </p>
                </dd>
<dt><span class="term"><code class="constant"><em class="replaceable"><code>[<span class="optional">no</span>]</code></em>fail</code></span></dt>
<dd>
                  <p>
		    Try the next nameserver if a nameserver responds with
		    SERVFAIL or a referral (nofail) or terminate query
		    (fail) on such a response.
		  </p>
		  <p>
                    (Default = nofail)
                  </p>
	        </dd>
</dl></div>
<p>
          </p>
        </dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>RETURN VALUES</h2>
    <p>
      <span class="command"><strong>nslookup</strong></span> returns with an exit status of 1
      if any query failed, and 0 otherwise.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.11"></a><h2>FILES</h2>

    <p><code class="filename">/etc/resolv.conf</code>
    </p>
  </div>

  <div class="refsection">
<a name="id-1.12"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
        <span class="refentrytitle">dig</span>(1)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">host</span>(1)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">named</span>(8)
      </span>.
    </p>
  </div>
</div></body>
</html>
