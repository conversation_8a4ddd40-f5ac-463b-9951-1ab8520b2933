<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000-2002, 2004, 2005, 2007-2009, 2014-2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>host</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.host"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    host
     &#8212; DNS lookup utility
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">host</code> 
       [<code class="option">-aCdlnrsTUwv</code>]
       [<code class="option">-c <em class="replaceable"><code>class</code></em></code>]
       [<code class="option">-N <em class="replaceable"><code>ndots</code></em></code>]
       [<code class="option">-R <em class="replaceable"><code>number</code></em></code>]
       [<code class="option">-t <em class="replaceable"><code>type</code></em></code>]
       [<code class="option">-W <em class="replaceable"><code>wait</code></em></code>]
       [<code class="option">-m <em class="replaceable"><code>flag</code></em></code>]
       [
	[<code class="option">-4</code>]
	 |  [<code class="option">-6</code>]
      ]
       [<code class="option">-v</code>]
       [<code class="option">-V</code>]
       {name}
       [server]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>


    <p><span class="command"><strong>host</strong></span>
      is a simple utility for performing DNS lookups.
      It is normally used to convert names to IP addresses and vice versa.
      When no arguments or options are given,
      <span class="command"><strong>host</strong></span>
      prints a short summary of its command line arguments and options.
    </p>

    <p><em class="parameter"><code>name</code></em> is the domain name that is to be
      looked
      up.  It can also be a dotted-decimal IPv4 address or a colon-delimited
      IPv6 address, in which case <span class="command"><strong>host</strong></span> will by
      default
      perform a reverse lookup for that address.
      <em class="parameter"><code>server</code></em> is an optional argument which
      is either
      the name or IP address of the name server that <span class="command"><strong>host</strong></span>
      should query instead of the server or servers listed in
      <code class="filename">/etc/resolv.conf</code>.
    </p>

  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>

    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-4</span></dt>
<dd>
	  <p>
	    Use IPv4 only for query transport.
	    See also the <code class="option">-6</code> option.
	  </p>
	</dd>
<dt><span class="term">-6</span></dt>
<dd>
	  <p>
	    Use IPv6 only for query transport.
	    See also the <code class="option">-4</code> option.
	  </p>
	</dd>
<dt><span class="term">-a</span></dt>
<dd>
	  <p>
	    "All". The <code class="option">-a</code> option is normally equivalent
	    to <code class="option">-v -t <code class="literal">ANY</code></code>.
	    It also affects the behaviour of the <code class="option">-l</code>
	    list zone option.
	  </p>
	</dd>
<dt><span class="term">-c <em class="replaceable"><code>class</code></em></span></dt>
<dd>
	  <p>
	    Query class: This can be used to lookup HS (Hesiod) or CH
	    (Chaosnet) class resource records. The default class is IN
	    (Internet).
	  </p>
	</dd>
<dt><span class="term">-C</span></dt>
<dd>
	  <p>
	    Check consistency: <span class="command"><strong>host</strong></span> will query the
	    SOA records for zone <em class="parameter"><code>name</code></em> from all
	    the listed authoritative name servers for that zone. The
	    list of name servers is defined by the NS records that are
	    found for the zone.
	  </p>
	</dd>
<dt><span class="term">-d</span></dt>
<dd>
	  <p>
	    Print debugging traces.
	    Equivalent to the <code class="option">-v</code> verbose option.
	  </p>
	</dd>
<dt><span class="term">-i</span></dt>
<dd>
	  <p>
	    Obsolete.
	    Use the IP6.INT domain for reverse lookups of IPv6
	    addresses as defined in RFC1886 and deprecated in RFC4159.
	    The default is to use IP6.ARPA as specified in RFC3596.
	  </p>
	</dd>
<dt><span class="term">-l</span></dt>
<dd>
	  <p>
	    List zone:
	    The <span class="command"><strong>host</strong></span> command performs a zone transfer of
	    zone <em class="parameter"><code>name</code></em> and prints out the NS,
	    PTR and address records (A/AAAA).
	  </p>
	  <p>
	    Together, the <code class="option">-l -a</code>
	    options print all records in the zone.
	  </p>
	</dd>
<dt><span class="term">-N <em class="replaceable"><code>ndots</code></em></span></dt>
<dd>
	  <p>
	    The number of dots that have to be
	    in <em class="parameter"><code>name</code></em> for it to be considered
	    absolute. The default value is that defined using the
	    ndots statement in <code class="filename">/etc/resolv.conf</code>,
	    or 1 if no ndots statement is present. Names with fewer
	    dots are interpreted as relative names and will be
	    searched for in the domains listed in
	    the <span class="type">search</span> or <span class="type">domain</span> directive
	    in <code class="filename">/etc/resolv.conf</code>.
	  </p>
	</dd>
<dt><span class="term">-r</span></dt>
<dd>
	  <p>
	    Non-recursive query:
	    Setting this option clears the RD (recursion desired) bit
	    in the query. This should mean that the name server
	    receiving the query will not attempt to
	    resolve <em class="parameter"><code>name</code></em>.
	    The <code class="option">-r</code> option
	    enables <span class="command"><strong>host</strong></span> to mimic the behavior of a
	    name server by making non-recursive queries and expecting
	    to receive answers to those queries that can be
	    referrals to other name servers.
	  </p>
	</dd>
<dt><span class="term">-R <em class="replaceable"><code>number</code></em></span></dt>
<dd>
	  <p>
	    Number of retries for UDP queries:
	    If <em class="parameter"><code>number</code></em> is negative or zero, the
	    number of retries will default to 1. The default value is
	    1, or the value of the <em class="parameter"><code>attempts</code></em>
	    option in <code class="filename">/etc/resolv.conf</code>, if set.
	  </p>
	</dd>
<dt><span class="term">-s</span></dt>
<dd>
	  <p>
	    Do <span class="emphasis"><em>not</em></span> send the query to the next
	    nameserver if any server responds with a SERVFAIL
	    response, which is the reverse of normal stub resolver
	    behavior.
	  </p>
	</dd>
<dt><span class="term">-t <em class="replaceable"><code>type</code></em></span></dt>
<dd>
	  <p>
	    Query type:
	    The <em class="parameter"><code>type</code></em> argument can be any
	    recognized query type: CNAME, NS, SOA, TXT, DNSKEY, AXFR, etc.
	  </p>
	  <p>
	    When no query type is specified, <span class="command"><strong>host</strong></span>
	    automatically selects an appropriate query type. By default, it
	    looks for A, AAAA, and MX records.
	    If the <code class="option">-C</code> option is given, queries will
	    be made for SOA records.
	    If <em class="parameter"><code>name</code></em> is a dotted-decimal IPv4
	    address or colon-delimited IPv6
	    address, <span class="command"><strong>host</strong></span> will query for PTR
	    records.
	  </p>
	  <p>
	    If a query type of IXFR is chosen the starting serial
	    number can be specified by appending an equal followed by
	    the starting serial number
	    (like <code class="option">-t <code class="literal">IXFR=12345678</code></code>).
	  </p>
	</dd>
<dt>
<span class="term">-T, </span><span class="term">-U</span>
</dt>
<dd>
	  <p>
	    TCP/UDP:
	    By default, <span class="command"><strong>host</strong></span> uses UDP when making
	    queries. The <code class="option">-T</code> option makes it use a TCP
	    connection when querying the name server. TCP will be
	    automatically selected for queries that require it, such
	    as zone transfer (AXFR) requests.  Type ANY queries default
	    to TCP but can be forced to UDP initially using <code class="option">-U</code>.
	  </p>
	</dd>
<dt><span class="term">-m <em class="replaceable"><code>flag</code></em></span></dt>
<dd>
	  <p>
	    Memory usage debugging: the flag can
	    be <em class="parameter"><code>record</code></em>, <em class="parameter"><code>usage</code></em>,
	    or <em class="parameter"><code>trace</code></em>. You can specify
	    the <code class="option">-m</code> option more than once to set
	    multiple flags.
	  </p>
	</dd>
<dt><span class="term">-v</span></dt>
<dd>
	  <p>
	    Verbose output.
	    Equivalent to the <code class="option">-d</code> debug option.
	    Verbose output can also be enabled by setting
	    the <em class="parameter"><code>debug</code></em> option
	    in <code class="filename">/etc/resolv.conf</code>.
	  </p>
	</dd>
<dt><span class="term">-V</span></dt>
<dd>
	  <p>
	    Print the version number and exit.
	  </p>
	</dd>
<dt><span class="term">-w</span></dt>
<dd>
	  <p>
	    Wait forever: The query timeout is set to the maximum possible.
	    See also the <code class="option">-W</code> option.
	  </p>
	</dd>
<dt><span class="term">-W <em class="replaceable"><code>wait</code></em></span></dt>
<dd>
	  <p>
	    Timeout: Wait for up to <em class="parameter"><code>wait</code></em>
	    seconds for a reply. If <em class="parameter"><code>wait</code></em> is
	    less than one, the wait interval is set to one second.
	  </p>
	  <p>
	    By default, <span class="command"><strong>host</strong></span> will wait for 5
	    seconds for UDP responses and 10 seconds for TCP
	    connections. These defaults can be overridden by
	    the <em class="parameter"><code>timeout</code></em> option
	    in <code class="filename">/etc/resolv.conf</code>.
	  </p>
	  <p>
	    See also the <code class="option">-w</code> option.
	  </p>
	</dd>
</dl></div>

  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>IDN SUPPORT</h2>

    <p>
      If <span class="command"><strong>host</strong></span> has been built with IDN (internationalized
      domain name) support, it can accept and display non-ASCII domain names.
      <span class="command"><strong>host</strong></span> appropriately converts character encoding of
      domain name before sending a request to DNS server or displaying a
      reply from the server.
      If you'd like to turn off the IDN support for some reason, defines
      the <code class="envar">IDN_DISABLE</code> environment variable.
      The IDN support is disabled if the variable is set when
      <span class="command"><strong>host</strong></span> runs.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>FILES</h2>

    <p><code class="filename">/etc/resolv.conf</code>
    </p>
  </div>

  <div class="refsection">
<a name="id-1.11"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
        <span class="refentrytitle">dig</span>(1)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">named</span>(8)
      </span>.
    </p>
  </div>

</div></body>
</html>
