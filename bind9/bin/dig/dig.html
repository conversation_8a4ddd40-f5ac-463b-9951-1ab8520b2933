<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000-2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>dig</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.dig"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    dig
     &#8212; DNS lookup utility
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">dig</code> 
       [@server]
       [<code class="option">-b <em class="replaceable"><code>address</code></em></code>]
       [<code class="option">-c <em class="replaceable"><code>class</code></em></code>]
       [<code class="option">-f <em class="replaceable"><code>filename</code></em></code>]
       [<code class="option">-k <em class="replaceable"><code>filename</code></em></code>]
       [<code class="option">-m</code>]
       [<code class="option">-p <em class="replaceable"><code>port#</code></em></code>]
       [<code class="option">-q <em class="replaceable"><code>name</code></em></code>]
       [<code class="option">-t <em class="replaceable"><code>type</code></em></code>]
       [<code class="option">-v</code>]
       [<code class="option">-x <em class="replaceable"><code>addr</code></em></code>]
       [<code class="option">-y <em class="replaceable"><code>[<span class="optional">hmac:</span>]name:key</code></em></code>]
       [
	[<code class="option">-4</code>]
	 |  [<code class="option">-6</code>]
      ]
       [name]
       [type]
       [class]
       [queryopt...]
    </p></div>

    <div class="cmdsynopsis"><p>
      <code class="command">dig</code> 
       [<code class="option">-h</code>]
    </p></div>

    <div class="cmdsynopsis"><p>
      <code class="command">dig</code> 
       [global-queryopt...]
       [query...]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>dig</strong></span> is a flexible tool
      for interrogating DNS name servers.  It performs DNS lookups and
      displays the answers that are returned from the name server(s) that
      were queried.  Most DNS administrators use <span class="command"><strong>dig</strong></span> to
      troubleshoot DNS problems because of its flexibility, ease of use and
      clarity of output.  Other lookup tools tend to have less functionality
      than <span class="command"><strong>dig</strong></span>.
    </p>

    <p>
      Although <span class="command"><strong>dig</strong></span> is normally used with
      command-line
      arguments, it also has a batch mode of operation for reading lookup
      requests from a file.  A brief summary of its command-line arguments
      and options is printed when the <code class="option">-h</code> option is given.
      Unlike earlier versions, the BIND 9 implementation of
      <span class="command"><strong>dig</strong></span> allows multiple lookups to be issued
      from the
      command line.
    </p>

    <p>
      Unless it is told to query a specific name server,
      <span class="command"><strong>dig</strong></span> will try each of the servers listed in
      <code class="filename">/etc/resolv.conf</code>. If no usable server addresses
      are found, <span class="command"><strong>dig</strong></span> will send the query to the local
      host.
    </p>

    <p>
      When no command line arguments or options are given,
      <span class="command"><strong>dig</strong></span> will perform an NS query for "." (the root).
    </p>

    <p>
      It is possible to set per-user defaults for <span class="command"><strong>dig</strong></span> via
      <code class="filename">${HOME}/.digrc</code>.  This file is read and
      any options in it
      are applied before the command line arguments.
    </p>

    <p>
      The IN and CH class names overlap with the IN and CH top level
      domain names.  Either use the <code class="option">-t</code> and
      <code class="option">-c</code> options to specify the type and class,
      use the <code class="option">-q</code> the specify the domain name, or
      use "IN." and "CH." when looking up these top level domains.
    </p>

  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>SIMPLE USAGE</h2>


    <p>
      A typical invocation of <span class="command"><strong>dig</strong></span> looks like:
      </p>
<pre class="programlisting"> dig @server name type </pre>
<p>
      where:

      </p>
<div class="variablelist"><dl class="variablelist">
<dt><span class="term"><code class="constant">server</code></span></dt>
<dd>
	    <p>
	      is the name or IP address of the name server to query.  This
	      can be an IPv4 address in dotted-decimal notation or an IPv6
	      address in colon-delimited notation.  When the supplied
	      <em class="parameter"><code>server</code></em> argument is a hostname,
	      <span class="command"><strong>dig</strong></span> resolves that name before querying
	      that name server.
	    </p>
	    <p>
	      If no <em class="parameter"><code>server</code></em> argument is
	      provided, <span class="command"><strong>dig</strong></span> consults
	      <code class="filename">/etc/resolv.conf</code>; if an
	      address is found there, it queries the name server at
	      that address. If either of the <code class="option">-4</code> or
	      <code class="option">-6</code> options are in use, then
	      only addresses for the corresponding transport
	      will be tried.  If no usable addresses are found,
	      <span class="command"><strong>dig</strong></span> will send the query to the
	      local host.  The reply from the name server that
	      responds is displayed.
	    </p>
	  </dd>
<dt><span class="term"><code class="constant">name</code></span></dt>
<dd>
	    <p>
	      is the name of the resource record that is to be looked up.
	    </p>
	  </dd>
<dt><span class="term"><code class="constant">type</code></span></dt>
<dd>
	    <p>
	      indicates what type of query is required &#8212;
	      ANY, A, MX, SIG, etc.
	      <em class="parameter"><code>type</code></em> can be any valid query
	      type.  If no
	      <em class="parameter"><code>type</code></em> argument is supplied,
	      <span class="command"><strong>dig</strong></span> will perform a lookup for an
	      A record.
	    </p>
	  </dd>
</dl></div>
<p>
    </p>

  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-4</span></dt>
<dd>
	  <p>
	    Use IPv4 only.
	  </p>
	</dd>
<dt><span class="term">-6</span></dt>
<dd>
	  <p>
	    Use IPv6 only.
	  </p>
	</dd>
<dt><span class="term">-b <em class="replaceable"><code>address[<span class="optional">#port</span>]</code></em></span></dt>
<dd>
	  <p>
	    Set the source IP address of the query.
	    The <em class="parameter"><code>address</code></em> must be a valid address on
	    one of the host's network interfaces, or "0.0.0.0" or "::". An
	    optional port may be specified by appending "#&lt;port&gt;"
	  </p>
	</dd>
<dt><span class="term">-c <em class="replaceable"><code>class</code></em></span></dt>
<dd>
	  <p>
	    Set the query class. The
	    default <em class="parameter"><code>class</code></em> is IN; other classes
	    are HS for Hesiod records or CH for Chaosnet records.
	  </p>
	</dd>
<dt><span class="term">-f <em class="replaceable"><code>file</code></em></span></dt>
<dd>
	  <p>
	    Batch mode: <span class="command"><strong>dig</strong></span> reads a list of lookup
	    requests to process from the
	    given <em class="parameter"><code>file</code></em>. Each line in the file
	    should be organized in the same way they would be
	    presented as queries to
	    <span class="command"><strong>dig</strong></span> using the command-line interface.
	  </p>
	</dd>
<dt><span class="term">-i</span></dt>
<dd>
	  <p>
	    Do reverse IPv6 lookups using the obsolete RFC1886 IP6.INT
	    domain, which is no longer in use. Obsolete bit string
	    label queries (RFC2874) are not attempted.
	  </p>
	</dd>
<dt><span class="term">-k <em class="replaceable"><code>keyfile</code></em></span></dt>
<dd>
	  <p>
	    Sign queries using TSIG using a key read from the given file.
	    Key files can be generated using
	    <span class="citerefentry">
	      <span class="refentrytitle">tsig-keygen</span>(8)
	    </span>.
	    When using TSIG authentication with <span class="command"><strong>dig</strong></span>,
	    the name server that is queried needs to know the key and
	    algorithm that is being used. In BIND, this is done by
	    providing appropriate <span class="command"><strong>key</strong></span>
	    and <span class="command"><strong>server</strong></span> statements in
	    <code class="filename">named.conf</code>.
	  </p>
	</dd>
<dt><span class="term">-m</span></dt>
<dd>
	  <p>
	    Enable memory usage debugging.
	    
	  </p>
	</dd>
<dt><span class="term">-p <em class="replaceable"><code>port</code></em></span></dt>
<dd>
	  <p>
	    Send the query to a non-standard port on the server,
	    instead of the default port 53. This option would be used
	    to test a name server that has been configured to listen
	    for queries on a non-standard port number.
	  </p>
	</dd>
<dt><span class="term">-q <em class="replaceable"><code>name</code></em></span></dt>
<dd>
	  <p>
	    The domain name to query. This is useful to distinguish
	    the <em class="parameter"><code>name</code></em> from other arguments.
	  </p>
	</dd>
<dt><span class="term">-t <em class="replaceable"><code>type</code></em></span></dt>
<dd>
	  <p>
	    The resource record type to query. It can be any valid query type
	    which is
	    supported in BIND 9.  The default query type is "A", unless the
	    <code class="option">-x</code> option is supplied to indicate a reverse lookup.
	    A zone transfer can be requested by specifying a type of AXFR.  When
	    an incremental zone transfer (IXFR) is required, set the
	    <em class="parameter"><code>type</code></em> to <code class="literal">ixfr=N</code>.
	    The incremental zone transfer will contain the changes
	    made to the zone since the serial number in the zone's SOA
	    record was
	    <em class="parameter"><code>N</code></em>.
	  </p>
	</dd>
<dt><span class="term">-u</span></dt>
<dd>
	  <p>
	    Print query times in microseconds instead of milliseconds.
	  </p>
	</dd>
<dt><span class="term">-v</span></dt>
<dd>
	  <p>
	    Print the version number and exit.
	  </p>
	</dd>
<dt><span class="term">-x <em class="replaceable"><code>addr</code></em></span></dt>
<dd>
	  <p>
	    Simplified reverse lookups, for mapping addresses to
	    names. The <em class="parameter"><code>addr</code></em> is an IPv4 address
	    in dotted-decimal notation, or a colon-delimited IPv6
	    address. When the <code class="option">-x</code> is used, there is no
	    need to provide
	    the <em class="parameter"><code>name</code></em>, <em class="parameter"><code>class</code></em>
	    and <em class="parameter"><code>type</code></em>
	    arguments. <span class="command"><strong>dig</strong></span> automatically performs a
	    lookup for a name like
	    <code class="literal">**********.in-addr.arpa</code> and sets the
	    query type and class to PTR and IN respectively. IPv6
	    addresses are looked up using nibble format under the
	    IP6.ARPA domain (but see also the <code class="option">-i</code>
	    option).
	  </p>
	</dd>
<dt><span class="term">-y <em class="replaceable"><code>[<span class="optional">hmac:</span>]keyname:secret</code></em></span></dt>
<dd>
	  <p>
	    Sign queries using TSIG with the given authentication key.
	    <em class="parameter"><code>keyname</code></em> is the name of the key, and
	    <em class="parameter"><code>secret</code></em> is the base64 encoded shared secret.
	    <em class="parameter"><code>hmac</code></em> is the name of the key algorithm;
	    valid choices are <code class="literal">hmac-md5</code>,
	    <code class="literal">hmac-sha1</code>, <code class="literal">hmac-sha224</code>,
	    <code class="literal">hmac-sha256</code>, <code class="literal">hmac-sha384</code>, or
	    <code class="literal">hmac-sha512</code>.  If <em class="parameter"><code>hmac</code></em>
	    is not specified, the default is <code class="literal">hmac-md5</code>
	    or if MD5 was disabled <code class="literal">hmac-sha256</code>.
	  </p>
	  <p>
	    NOTE: You should use the <code class="option">-k</code> option and
	    avoid the <code class="option">-y</code> option, because
	    with <code class="option">-y</code> the shared secret is supplied as
	    a command line argument in clear text. This may be visible
	    in the output from
	    <span class="citerefentry">
	      <span class="refentrytitle">ps</span>(1)
	    </span>
	    or in a history file maintained by the user's shell.
	  </p>
	</dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>QUERY OPTIONS</h2>


    <p><span class="command"><strong>dig</strong></span>
      provides a number of query options which affect
      the way in which lookups are made and the results displayed.  Some of
      these set or reset flag bits in the query header, some determine which
      sections of the answer get printed, and others determine the timeout
      and retry strategies.
    </p>

    <p>
      Each query option is identified by a keyword preceded by a plus sign
      (<code class="literal">+</code>).  Some keywords set or reset an
      option.  These may be preceded
      by the string <code class="literal">no</code> to negate the meaning of
      that keyword.  Other
      keywords assign values to options like the timeout interval.  They
      have the form <code class="option">+keyword=value</code>.
      Keywords may be abbreviated, provided the abbreviation is
      unambiguous; for example, <code class="literal">+cd</code> is equivalent
      to <code class="literal">+cdflag</code>.
      The query options are:

      </p>
<div class="variablelist"><dl class="variablelist">
<dt><span class="term"><code class="option">+[no]aaflag</code></span></dt>
<dd>
	    <p>
	      A synonym for <em class="parameter"><code>+[no]aaonly</code></em>.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]aaonly</code></span></dt>
<dd>
	    <p>
	      Sets the "aa" flag in the query.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]additional</code></span></dt>
<dd>
	    <p>
	      Display [do not display] the additional section of a
	      reply.  The default is to display it.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]adflag</code></span></dt>
<dd>
	    <p>
	      Set [do not set] the AD (authentic data) bit in the
	      query.  This requests the server to return whether
	      all of the answer and authority sections have all
	      been validated as secure according to the security
	      policy of the server.  AD=1 indicates that all records
	      have been validated as secure and the answer is not
	      from a OPT-OUT range.  AD=0 indicate that some part
	      of the answer was insecure or not validated.  This
	      bit is set by default.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]all</code></span></dt>
<dd>
	    <p>
	      Set or clear all display flags.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]answer</code></span></dt>
<dd>
	    <p>
	      Display [do not display] the answer section of a
	      reply.  The default is to display it.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]authority</code></span></dt>
<dd>
	    <p>
	      Display [do not display] the authority section of a
	      reply.  The default is to display it.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]badcookie</code></span></dt>
<dd>
	    <p>
	      Retry lookup with the new server cookie if a
	      BADCOOKIE response is received.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]besteffort</code></span></dt>
<dd>
	    <p>
	      Attempt to display the contents of messages which are
	      malformed.  The default is to not display malformed
	      answers.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+bufsize=B</code></span></dt>
<dd>
	    <p>
	      Set the UDP message buffer size advertised using EDNS0
	      to <em class="parameter"><code>B</code></em> bytes.  The maximum and
	      minimum sizes of this buffer are 65535 and 0 respectively.
	      Values outside this range are rounded up or down
	      appropriately.  Values other than zero will cause a
	      EDNS query to be sent.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]cdflag</code></span></dt>
<dd>
	    <p>
	      Set [do not set] the CD (checking disabled) bit in
	      the query.  This requests the server to not perform
	      DNSSEC validation of responses.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]class</code></span></dt>
<dd>
	    <p>
	      Display [do not display] the CLASS when printing the
	      record.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]cmd</code></span></dt>
<dd>
	    <p>
	      Toggles the printing of the initial comment in the
	      output identifying the version of <span class="command"><strong>dig</strong></span>
	      and the query options that have been applied.  This
	      comment is printed by default.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]comments</code></span></dt>
<dd>
	    <p>
	      Toggle the display of comment lines in the output.
	      The default is to print comments.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]cookie[<span class="optional">=####</span>]</code></span></dt>
<dd>
	    <p>
	      Send a COOKIE EDNS option, with optional
	      value.  Replaying a COOKIE from a previous response will
	      allow the server to identify a previous client.  The
	      default is <code class="option">+cookie</code>.
	    </p>
	    <p>
	      <span class="command"><strong>+cookie</strong></span> is also set when +trace
	      is set to better emulate the default queries from a
	      nameserver.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]crypto</code></span></dt>
<dd>
	    <p>
	      Toggle the display of cryptographic fields in DNSSEC
	      records.  The contents of these field are unnecessary
	      to debug most DNSSEC validation failures and removing
	      them makes it easier to see the common failures.  The
	      default is to display the fields.  When omitted they
	      are replaced by the string "[omitted]" or in the
	      DNSKEY case the key id is displayed as the replacement,
	      e.g. "[ key id = value ]".
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]defname</code></span></dt>
<dd>
	    <p>
	      Deprecated, treated as a synonym for
	      <em class="parameter"><code>+[no]search</code></em>
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]dnssec</code></span></dt>
<dd>
	    <p>
	      Requests DNSSEC records be sent by setting the DNSSEC
	      OK bit (DO) in the OPT record in the additional section
	      of the query.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+domain=somename</code></span></dt>
<dd>
	    <p>
	      Set the search list to contain the single domain
	      <em class="parameter"><code>somename</code></em>, as if specified in
	      a <span class="command"><strong>domain</strong></span> directive in
	      <code class="filename">/etc/resolv.conf</code>, and enable
	      search list processing as if the
	      <em class="parameter"><code>+search</code></em> option were given.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+dscp=value</code></span></dt>
<dd>
	    <p>
	      Set the DSCP code point to be used when sending the
	      query.  Valid DSCP code points are in the range
	      [0..63].  By default no code point is explicitly set.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]edns[=#]</code></span></dt>
<dd>
	    <p>
	       Specify the EDNS version to query with.  Valid values
	       are 0 to 255.  Setting the EDNS version will cause
	       a EDNS query to be sent.  <code class="option">+noedns</code>
	       clears the remembered EDNS version.  EDNS is set to
	       0 by default.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]ednsflags[=#]</code></span></dt>
<dd>
	    <p>
	      Set the must-be-zero EDNS flags bits (Z bits) to the
	      specified value. Decimal, hex and octal encodings are
	      accepted. Setting a named flag (e.g. DO) will silently be
	      ignored. By default, no Z bits are set.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]ednsnegotiation</code></span></dt>
<dd>
	    <p>
	      Enable / disable EDNS version negotiation. By default
	      EDNS version negotiation is enabled.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]ednsopt[=code[:value]]</code></span></dt>
<dd>
	    <p>
	      Specify EDNS option with code point <code class="option">code</code>
	      and optionally payload of <code class="option">value</code> as a
	      hexadecimal string.  <code class="option">code</code> can be
	      either an EDNS option name (for example,
	      <code class="literal">NSID</code> or <code class="literal">ECS</code>),
	      or an arbitrary numeric value.  <code class="option">+noednsopt</code>
	      clears the EDNS options to be sent.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]expire</code></span></dt>
<dd>
	    <p>
	      Send an EDNS Expire option.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]fail</code></span></dt>
<dd>
	    <p>
	      Do not try the next server if you receive a SERVFAIL.
	      The default is to not try the next server which is
	      the reverse of normal stub resolver behavior.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]header-only</code></span></dt>
<dd>
	    <p>
	      Send a query with a DNS header without a question section.
	      The default is to add a question section.  The query type
	      and query name are ignored when this is set.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]identify</code></span></dt>
<dd>
	    <p>
	      Show [or do not show] the IP address and port number
	      that supplied the answer when the
	      <em class="parameter"><code>+short</code></em> option is enabled.  If
	      short form answers are requested, the default is not
	      to show the source address and port number of the
	      server that provided the answer.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]idnout</code></span></dt>
<dd>
	    <p>
	      Convert [do not convert] puny code on output.
	      This requires IDN SUPPORT to have been enabled at
	      compile time.  The default is to convert output.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]ignore</code></span></dt>
<dd>
	    <p>
	      Ignore truncation in UDP responses instead of retrying
	      with TCP.  By default, TCP retries are performed.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]keepopen</code></span></dt>
<dd>
	    <p>
	      Keep the TCP socket open between queries and reuse
	      it rather than creating a new TCP socket for each
	      lookup.  The default is <code class="option">+nokeepopen</code>.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]mapped</code></span></dt>
<dd>
	    <p>
	      Allow mapped IPv4 over IPv6 addresses to be used.  The
	      default is <code class="option">+mapped</code>.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]multiline</code></span></dt>
<dd>
	    <p>
	      Print records like the SOA records in a verbose
	      multi-line format with human-readable comments.  The
	      default is to print each record on a single line, to
	      facilitate machine parsing of the <span class="command"><strong>dig</strong></span>
	      output.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+ndots=D</code></span></dt>
<dd>
	    <p>
	      Set the number of dots that have to appear in
	      <em class="parameter"><code>name</code></em> to <em class="parameter"><code>D</code></em>
	      for it to be considered absolute.  The default value
	      is that defined using the ndots statement in
	      <code class="filename">/etc/resolv.conf</code>, or 1 if no
	      ndots statement is present.  Names with fewer dots
	      are interpreted as relative names and will be searched
	      for in the domains listed in the <code class="option">search</code>
	      or <code class="option">domain</code> directive in
	      <code class="filename">/etc/resolv.conf</code> if
	      <code class="option">+search</code> is set.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]nsid</code></span></dt>
<dd>
	    <p>
	      Include an EDNS name server ID request when sending
	      a query.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]nssearch</code></span></dt>
<dd>
	    <p>
	      When this option is set, <span class="command"><strong>dig</strong></span>
	      attempts to find the authoritative name servers for
	      the zone containing the name being looked up and
	      display the SOA record that each name server has for
	      the zone.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]onesoa</code></span></dt>
<dd>
	    <p>
	      Print only one (starting) SOA record when performing
	      an AXFR. The default is to print both the starting
	      and ending SOA records.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]opcode=value</code></span></dt>
<dd>
	    <p>
	      Set [restore] the DNS message opcode to the specified
	      value.  The default value is QUERY (0).
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+padding=value</code></span></dt>
<dd>
	    <p>
	      Pad the size of the query packet using the EDNS Padding option
	      to blocks of <em class="parameter"><code>value</code></em> bytes. For example,
	      <code class="option">+padding=32</code> would cause a 48-byte query to
	      be padded to 64 bytes.  The default block size is 0, which
	      disables padding. The maximum is 512. Values are
	      ordinarily expected to be powers of two, such as 128;
	      however, this is not mandatory.  Responses to
	      padded queries may also be padded, but only if the query
	      uses TCP or DNS COOKIE.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]qr</code></span></dt>
<dd>
	    <p>
	      Print [do not print] the query as it is sent.  By
	      default, the query is not printed.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]question</code></span></dt>
<dd>
	    <p>
	      Print [do not print] the question section of a query
	      when an answer is returned.  The default is to print
	      the question section as a comment.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]rdflag</code></span></dt>
<dd>
	    <p>
	      A synonym for <em class="parameter"><code>+[no]recurse</code></em>.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]recurse</code></span></dt>
<dd>
	    <p>
	      Toggle the setting of the RD (recursion desired) bit
	      in the query.  This bit is set by default, which means
	      <span class="command"><strong>dig</strong></span> normally sends recursive
	      queries.  Recursion is automatically disabled when
	      the <em class="parameter"><code>+nssearch</code></em> or
	      <em class="parameter"><code>+trace</code></em> query options are used.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+retry=T</code></span></dt>
<dd>
	    <p>
	      Sets the number of times to retry UDP queries to
	      server to <em class="parameter"><code>T</code></em> instead of the
	      default, 2.  Unlike <em class="parameter"><code>+tries</code></em>,
	      this does not include the initial query.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]rrcomments</code></span></dt>
<dd>
	    <p>
	      Toggle the display of per-record comments in the
	      output (for example, human-readable key information
	      about DNSKEY records).  The default is not to print
	      record comments unless multiline mode is active.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]search</code></span></dt>
<dd>
	    <p>
	      Use [do not use] the search list defined by the
	      searchlist or domain directive in
	      <code class="filename">resolv.conf</code> (if any).  The search
	      list is not used by default.
	    </p>
	    <p>
	      'ndots' from <code class="filename">resolv.conf</code> (default 1)
	       which may be overridden by <em class="parameter"><code>+ndots</code></em>
	      determines if the name will be treated as relative
	      or not and hence whether a search is eventually
	      performed or not.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]short</code></span></dt>
<dd>
	    <p>
	      Provide a terse answer.  The default is to print the
	      answer in a verbose form.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]showsearch</code></span></dt>
<dd>
	    <p>
	      Perform [do not perform] a search showing intermediate
	      results.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]sigchase</code></span></dt>
<dd>
	    <p>
	      Chase DNSSEC signature chains. Requires dig be compiled
	      with -DDIG_SIGCHASE. This feature is deprecated.
	      Use <span class="command"><strong>delv</strong></span> instead.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+split=W</code></span></dt>
<dd>
	    <p>
	      Split long hex- or base64-formatted fields in resource
	      records into chunks of <em class="parameter"><code>W</code></em>
	      characters (where <em class="parameter"><code>W</code></em> is rounded
	      up to the nearest multiple of 4).
	      <em class="parameter"><code>+nosplit</code></em> or
	      <em class="parameter"><code>+split=0</code></em> causes fields not to
	      be split at all.  The default is 56 characters, or
	      44 characters when multiline mode is active.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]stats</code></span></dt>
<dd>
	    <p>
	      This query option toggles the printing of statistics:
	      when the query was made, the size of the reply and
	      so on.  The default behavior is to print the query
	      statistics.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]subnet=addr[/prefix-length]</code></span></dt>
<dd>
	    <p>
	      Send (don't send) an EDNS CLIENT-SUBNET option with the
	      specified IP address or network prefix.
	    </p>
	    <p>
              <span class="command"><strong>dig +subnet=0.0.0.0/0</strong></span>, or simply
              <span class="command"><strong>dig +subnet=0</strong></span> for short, sends an EDNS
              CLIENT-SUBNET option with an empty address and a source
              prefix-length of zero, which signals to a resolver that
              the client's address information must
              <span class="emphasis"><em>not</em></span> be used when resolving
              this query.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]tcp</code></span></dt>
<dd>
	    <p>
	      Use [do not use] TCP when querying name servers. The
	      default behavior is to use UDP unless a type
	      <code class="literal">any</code> or <code class="literal">ixfr=N</code>
	      query is requested, in which case the default is TCP.
	      AXFR queries always use TCP.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+timeout=T</code></span></dt>
<dd>
	    <p>

	      Sets the timeout for a query to
	      <em class="parameter"><code>T</code></em> seconds.  The default
	      timeout is 5 seconds.
	      An attempt to set <em class="parameter"><code>T</code></em> to less
	      than 1 will result
	      in a query timeout of 1 second being applied.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]topdown</code></span></dt>
<dd>
	    <p>
	      When chasing DNSSEC signature chains perform a top-down
	      validation.  Requires dig be compiled with -DDIG_SIGCHASE.
	      This feature is deprecated. Use <span class="command"><strong>delv</strong></span> instead.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]trace</code></span></dt>
<dd>
	    <p>
	      Toggle tracing of the delegation path from the root
	      name servers for the name being looked up.  Tracing
	      is disabled by default.  When tracing is enabled,
	      <span class="command"><strong>dig</strong></span> makes iterative queries to
	      resolve the name being looked up.  It will follow
	      referrals from the root servers, showing the answer
	      from each server that was used to resolve the lookup.
	    </p> <p>
	      If @server is also specified, it affects only the
	      initial query for the root zone name servers.
	    </p> <p>
	      <span class="command"><strong>+dnssec</strong></span> is also set when +trace
	      is set to better emulate the default queries from a
	      nameserver.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+tries=T</code></span></dt>
<dd>
	    <p>
	      Sets the number of times to try UDP queries to server
	      to <em class="parameter"><code>T</code></em> instead of the default,
	      3.  If <em class="parameter"><code>T</code></em> is less than or equal
	      to zero, the number of tries is silently rounded up
	      to 1.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+trusted-key=####</code></span></dt>
<dd>
	    <p>
	      Specifies a file containing trusted keys to be used
	      with <code class="option">+sigchase</code>.  Each DNSKEY record
	      must be on its own line.
	    </p> <p>
	      If not specified, <span class="command"><strong>dig</strong></span> will look
	      for <code class="filename">/etc/trusted-key.key</code> then
	      <code class="filename">trusted-key.key</code> in the current
	      directory.
	    </p> <p>
	      Requires dig be compiled with -DDIG_SIGCHASE.
	      This feature is deprecated. Use <span class="command"><strong>delv</strong></span> instead.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]ttlid</code></span></dt>
<dd>
	    <p>
	      Display [do not display] the TTL when printing the
	      record.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]ttlunits</code></span></dt>
<dd>
	    <p>
	      Display [do not display] the TTL in friendly human-readable
	      time units of "s", "m", "h", "d", and "w", representing
	      seconds, minutes, hours, days and weeks.  Implies +ttlid.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]unknownformat</code></span></dt>
<dd>
	    <p>
	      Print all RDATA in unknown RR type presentation format
	      (RFC 3597). The default is to print RDATA for known types
	      in the type's presentation format.
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]vc</code></span></dt>
<dd>
	    <p>
	      Use [do not use] TCP when querying name servers.  This
	      alternate syntax to <em class="parameter"><code>+[no]tcp</code></em>
	      is provided for backwards compatibility.  The "vc"
	      stands for "virtual circuit".
	    </p>
	  </dd>
<dt><span class="term"><code class="option">+[no]zflag</code></span></dt>
<dd>
	    <p>
	      Set [do not set] the last unassigned DNS header flag in a
	      DNS query.  This flag is off by default.
	    </p>
	  </dd>
</dl></div>
<p>

    </p>
  </div>

  <div class="refsection">
<a name="id-1.11"></a><h2>MULTIPLE QUERIES</h2>


    <p>
      The BIND 9 implementation of <span class="command"><strong>dig </strong></span>
      supports
      specifying multiple queries on the command line (in addition to
      supporting the <code class="option">-f</code> batch file option).  Each of those
      queries can be supplied with its own set of flags, options and query
      options.
    </p>

    <p>
      In this case, each <em class="parameter"><code>query</code></em> argument
      represent an
      individual query in the command-line syntax described above.  Each
      consists of any of the standard options and flags, the name to be
      looked up, an optional query type and class and any query options that
      should be applied to that query.
    </p>

    <p>
      A global set of query options, which should be applied to all queries,
      can also be supplied.  These global query options must precede the
      first tuple of name, class, type, options, flags, and query options
      supplied on the command line.  Any global query options (except
      the <code class="option">+[no]cmd</code> option) can be
      overridden by a query-specific set of query options.  For example:
      </p>
<pre class="programlisting">
dig +qr www.isc.org any -x 127.0.0.1 isc.org ns +noqr
</pre>
<p>
      shows how <span class="command"><strong>dig</strong></span> could be used from the
      command line
      to make three lookups: an ANY query for <code class="literal">www.isc.org</code>, a
      reverse lookup of 127.0.0.1 and a query for the NS records of
      <code class="literal">isc.org</code>.

      A global query option of <em class="parameter"><code>+qr</code></em> is
      applied, so
      that <span class="command"><strong>dig</strong></span> shows the initial query it made
      for each
      lookup.  The final query has a local query option of
      <em class="parameter"><code>+noqr</code></em> which means that <span class="command"><strong>dig</strong></span>
      will not print the initial query when it looks up the NS records for
      <code class="literal">isc.org</code>.
    </p>

  </div>

  <div class="refsection">
<a name="id-1.12"></a><h2>IDN SUPPORT</h2>

    <p>
      If <span class="command"><strong>dig</strong></span> has been built with IDN (internationalized
      domain name) support, it can accept and display non-ASCII domain names.
      <span class="command"><strong>dig</strong></span> appropriately converts character encoding of
      domain name before sending a request to DNS server or displaying a
      reply from the server.
      If you'd like to turn off the IDN support for some reason, defines
      the <code class="envar">IDN_DISABLE</code> environment variable.
      The IDN support is disabled if the variable is set when
      <span class="command"><strong>dig</strong></span> runs.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.13"></a><h2>FILES</h2>

    <p><code class="filename">/etc/resolv.conf</code>
    </p>
    <p><code class="filename">${HOME}/.digrc</code>
    </p>
  </div>

  <div class="refsection">
<a name="id-1.14"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
	<span class="refentrytitle">delv</span>(1)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">host</span>(1)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">named</span>(8)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">dnssec-keygen</span>(8)
      </span>,
      <em class="citetitle">RFC1035</em>.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.15"></a><h2>BUGS</h2>

    <p>
      There are probably too many query options.
    </p>
  </div>

</div></body>
</html>
