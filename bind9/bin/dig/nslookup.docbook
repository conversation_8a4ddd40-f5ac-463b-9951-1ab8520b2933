<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!--
 - Copyright (c) 1985, 1989
 -    The Regents of the University of California.  All rights reserved.
 -
 - Redistribution and use in source and binary forms, with or without
 - modification, are permitted provided that the following conditions
 - are met:
 - 1. Redistributions of source code must retain the above copyright
 -    notice, this list of conditions and the following disclaimer.
 - 2. Redistributions in binary form must reproduce the above copyright
 -    notice, this list of conditions and the following disclaimer in the
 -    documentation and/or other materials provided with the distribution.
 - 3. Neither the name of the University nor the names of its contributors
 -    may be used to endorse or promote products derived from this software
 -    without specific prior written permission.
 -
 - THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
 - ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 - IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 - ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
 - FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 - DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 - OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 - HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 - LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 - OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 - SUCH DAMAGE.
-->
<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.nslookup">
  <info>
    <date>2014-01-24</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle>nslookup</refentrytitle>
    <manvolnum>1</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname>nslookup</refname>
    <refpurpose>query Internet name servers interactively</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2004</year>
      <year>2005</year>
      <year>2006</year>
      <year>2007</year>
      <year>2010</year>
      <year>2013</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>nslookup</command>
      <arg choice="opt" rep="norepeat"><option>-option</option></arg>
      <arg choice="opt" rep="norepeat">name | -</arg>
      <arg choice="opt" rep="norepeat">server</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>Nslookup</command>
      is a program to query Internet domain name servers.  <command>Nslookup</command>
      has two modes: interactive and non-interactive.  Interactive mode allows
      the user to query name servers for information about various hosts and
      domains or to print a list of hosts in a domain.  Non-interactive mode
      is
      used to print just the name and requested information for a host or
      domain.
    </para>
  </refsection>

  <refsection><info><title>ARGUMENTS</title></info>

    <para>
      Interactive mode is entered in the following cases:
      <orderedlist numeration="loweralpha" inheritnum="ignore" continuation="restarts">
        <listitem>
          <para>
            when no arguments are given (the default name server will be used)
          </para>
        </listitem>
        <listitem>
          <para>
            when the first argument is a hyphen (-) and the second argument is
            the host name or Internet address of a name server.
          </para>
        </listitem>
      </orderedlist>
    </para>

    <para>
      Non-interactive mode is used when the name or Internet address of the
      host to be looked up is given as the first argument. The optional second
      argument specifies the host name or address of a name server.
    </para>

    <para>
      Options can also be specified on the command line if they precede the
      arguments and are prefixed with a hyphen.  For example, to
      change the default query type to host information, and the initial
      timeout to 10 seconds, type:
      <!-- <informalexample> produces bad nroff. -->
        <programlisting>
nslookup -query=hinfo  -timeout=10
</programlisting>
      <!-- </informalexample> -->
    </para>
    <para>
      The <option>-version</option> option causes
      <command>nslookup</command> to print the version
      number and immediately exits.
    </para>

  </refsection>

  <refsection><info><title>INTERACTIVE COMMANDS</title></info>

    <variablelist>
      <varlistentry>
        <term><constant>host</constant> <optional>server</optional></term>
        <listitem>
          <para>
            Look up information for host using the current default server or
            using server, if specified.  If host is an Internet address and
            the query type is A or PTR, the name of the host is returned.
            If host is a name and does not have a trailing period, the
            search list is used to qualify the name.
          </para>

          <para>
            To look up a host not in the current domain, append a period to
            the name.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term><constant>server</constant> <replaceable class="parameter">domain</replaceable></term>
        <listitem>
          <para/>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><constant>lserver</constant> <replaceable class="parameter">domain</replaceable></term>
        <listitem>
          <para>
            Change the default server to <replaceable>domain</replaceable>; <constant>lserver</constant> uses the initial
            server to look up information about <replaceable>domain</replaceable>, while <constant>server</constant> uses
            the current default server.  If an authoritative answer can't be
            found, the names of servers that might have the answer are
            returned.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term><constant>root</constant></term>
        <listitem>
          <para>
            not implemented
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term><constant>finger</constant></term>
        <listitem>
          <para>
            not implemented
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term><constant>ls</constant></term>
        <listitem>
          <para>
            not implemented
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term><constant>view</constant></term>
        <listitem>
          <para>
            not implemented
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term><constant>help</constant></term>
        <listitem>
          <para>
            not implemented
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term><constant>?</constant></term>
        <listitem>
          <para>
            not implemented
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term><constant>exit</constant></term>
        <listitem>
          <para>
            Exits the program.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term><constant>set</constant>
          <replaceable>keyword<optional>=value</optional></replaceable></term>
        <listitem>
          <para>
            This command is used to change state information that affects
            the lookups.  Valid keywords are:
            <variablelist>
              <varlistentry>
                <term><constant>all</constant></term>
                <listitem>
                  <para>
                    Prints the current values of the frequently used
                    options to <command>set</command>.
                    Information about the  current default
                    server and host is also printed.
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant>class=</constant><replaceable>value</replaceable></term>
                <listitem>
                  <para>
                    Change the query class to one of:
                    <variablelist>
                      <varlistentry>
                        <term><constant>IN</constant></term>
                        <listitem>
                          <para>
                            the Internet class
                          </para>
                        </listitem>
                      </varlistentry>
                      <varlistentry>
                        <term><constant>CH</constant></term>
                        <listitem>
                          <para>
                            the Chaos class
                          </para>
                        </listitem>
                      </varlistentry>
                      <varlistentry>
                        <term><constant>HS</constant></term>
                        <listitem>
                          <para>
                            the Hesiod class
                          </para>
                        </listitem>
                      </varlistentry>
                      <varlistentry>
                        <term><constant>ANY</constant></term>
                        <listitem>
                          <para>
                            wildcard
                          </para>
                        </listitem>
                      </varlistentry>
                    </variablelist>
                    The class specifies the protocol group of the information.

                  </para>
		  <para>
                    (Default = IN; abbreviation = cl)
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant><replaceable><optional>no</optional></replaceable>debug</constant></term>
                <listitem>
                  <para>
		    Turn on or off the display of the full response packet and
		    any intermediate response packets when searching.
                  </para>
		  <para>
                    (Default = nodebug; abbreviation = <optional>no</optional>deb)
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant><replaceable><optional>no</optional></replaceable>d2</constant></term>
                <listitem>
                  <para>
                    Turn debugging mode on or off.  This displays more about
	            what nslookup is doing.
                  </para>
		  <para>
                    (Default = nod2)
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant>domain=</constant><replaceable>name</replaceable></term>
                <listitem>
                  <para>
                    Sets the search list to <replaceable>name</replaceable>.
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant><replaceable><optional>no</optional></replaceable>search</constant></term>
                <listitem>
                  <para>
                    If the lookup request contains at least one period but
                    doesn't end with a trailing period, append the domain
                    names in the domain search list to the request until an
                    answer is received.
                  </para>
		  <para>
                    (Default = search)
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant>port=</constant><replaceable>value</replaceable></term>
                <listitem>
                  <para>
                    Change the default TCP/UDP name server port to <replaceable>value</replaceable>.
                  </para>
		  <para>
                    (Default = 53; abbreviation = po)
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant>querytype=</constant><replaceable>value</replaceable></term>
                <listitem>
                  <para/>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant>type=</constant><replaceable>value</replaceable></term>
                <listitem>
                  <para>
                    Change the type of the information query.
                  </para>
		  <para>
                    (Default = A; abbreviations = q, ty)
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant><replaceable><optional>no</optional></replaceable>recurse</constant></term>
                <listitem>
                  <para>
                    Tell the name server to query other servers if it does not
                    have the
                    information.
                  </para>
		  <para>
                    (Default = recurse; abbreviation = [no]rec)
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant>ndots=</constant><replaceable>number</replaceable></term>
                <listitem>
                  <para>
		    Set the number of dots (label separators) in a domain
		    that will disable searching.  Absolute names always
		    stop searching.
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant>retry=</constant><replaceable>number</replaceable></term>
                <listitem>
                  <para>
                    Set the number of retries to number.
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant>timeout=</constant><replaceable>number</replaceable></term>
                <listitem>
                  <para>
                    Change the initial timeout interval for waiting for a
                    reply to number seconds.
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant><replaceable><optional>no</optional></replaceable>vc</constant></term>
                <listitem>
                  <para>
                    Always use a virtual circuit when sending requests to the
                    server.
                  </para>
		  <para>
                    (Default = novc)
                  </para>
                </listitem>
              </varlistentry>

              <varlistentry>
                <term><constant><replaceable><optional>no</optional></replaceable>fail</constant></term>
                <listitem>
                  <para>
		    Try the next nameserver if a nameserver responds with
		    SERVFAIL or a referral (nofail) or terminate query
		    (fail) on such a response.
		  </para>
		  <para>
                    (Default = nofail)
                  </para>
	        </listitem>
	      </varlistentry>

            </variablelist>
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsection>

  <refsection><info><title>RETURN VALUES</title></info>
    <para>
      <command>nslookup</command> returns with an exit status of 1
      if any query failed, and 0 otherwise.
    </para>
  </refsection>

  <refsection><info><title>FILES</title></info>

    <para><filename>/etc/resolv.conf</filename>
    </para>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
        <refentrytitle>dig</refentrytitle><manvolnum>1</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>host</refentrytitle><manvolnum>1</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>named</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>.
    </para>
  </refsection>
</refentry>
