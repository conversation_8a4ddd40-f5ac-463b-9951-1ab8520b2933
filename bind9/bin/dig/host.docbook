<!DOCTYPE book [
<!ENTITY mdash "&#8212;">]>
<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.host">
  <info>
    <date>2009-01-20</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle>host</refentrytitle>
    <manvolnum>1</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname>host</refname>
    <refpurpose>DNS lookup utility</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2000</year>
      <year>2001</year>
      <year>2002</year>
      <year>2004</year>
      <year>2005</year>
      <year>2007</year>
      <year>2008</year>
      <year>2009</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2017</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>host</command>
      <arg choice="opt" rep="norepeat"><option>-aCdlnrsTUwv</option></arg>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">class</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-N <replaceable class="parameter">ndots</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-R <replaceable class="parameter">number</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-t <replaceable class="parameter">type</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-W <replaceable class="parameter">wait</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-m <replaceable class="parameter">flag</replaceable></option></arg>
      <group choice="opt" rep="norepeat">
	<arg choice="opt" rep="norepeat"><option>-4</option></arg>
	<arg choice="opt" rep="norepeat"><option>-6</option></arg>
      </group>
      <arg choice="opt" rep="norepeat"><option>-v</option></arg>
      <arg choice="opt" rep="norepeat"><option>-V</option></arg>
      <arg choice="req" rep="norepeat">name</arg>
      <arg choice="opt" rep="norepeat">server</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>


    <para><command>host</command>
      is a simple utility for performing DNS lookups.
      It is normally used to convert names to IP addresses and vice versa.
      When no arguments or options are given,
      <command>host</command>
      prints a short summary of its command line arguments and options.
    </para>

    <para><parameter>name</parameter> is the domain name that is to be
      looked
      up.  It can also be a dotted-decimal IPv4 address or a colon-delimited
      IPv6 address, in which case <command>host</command> will by
      default
      perform a reverse lookup for that address.
      <parameter>server</parameter> is an optional argument which
      is either
      the name or IP address of the name server that <command>host</command>
      should query instead of the server or servers listed in
      <filename>/etc/resolv.conf</filename>.
    </para>

  </refsection>

  <refsection><info><title>OPTIONS</title></info>

    <variablelist>

      <varlistentry>
	<term>-4</term>
	<listitem>
	  <para>
	    Use IPv4 only for query transport.
	    See also the <option>-6</option> option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-6</term>
	<listitem>
	  <para>
	    Use IPv6 only for query transport.
	    See also the <option>-4</option> option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-a</term>
	<listitem>
	  <para>
	    "All". The <option>-a</option> option is normally equivalent
	    to <option>-v -t <literal>ANY</literal></option>.
	    It also affects the behaviour of the <option>-l</option>
	    list zone option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-c <replaceable class="parameter">class</replaceable></term>
	<listitem>
	  <para>
	    Query class: This can be used to lookup HS (Hesiod) or CH
	    (Chaosnet) class resource records. The default class is IN
	    (Internet).
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-C</term>
	<listitem>
	  <para>
	    Check consistency: <command>host</command> will query the
	    SOA records for zone <parameter>name</parameter> from all
	    the listed authoritative name servers for that zone. The
	    list of name servers is defined by the NS records that are
	    found for the zone.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-d</term>
	<listitem>
	  <para>
	    Print debugging traces.
	    Equivalent to the <option>-v</option> verbose option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-i</term>
	<listitem>
	  <para>
	    Obsolete.
	    Use the IP6.INT domain for reverse lookups of IPv6
	    addresses as defined in RFC1886 and deprecated in RFC4159.
	    The default is to use IP6.ARPA as specified in RFC3596.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-l</term>
	<listitem>
	  <para>
	    List zone:
	    The <command>host</command> command performs a zone transfer of
	    zone <parameter>name</parameter> and prints out the NS,
	    PTR and address records (A/AAAA).
	  </para>
	  <para>
	    Together, the <option>-l -a</option>
	    options print all records in the zone.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-N <replaceable class="parameter">ndots</replaceable></term>
	<listitem>
	  <para>
	    The number of dots that have to be
	    in <parameter>name</parameter> for it to be considered
	    absolute. The default value is that defined using the
	    ndots statement in <filename>/etc/resolv.conf</filename>,
	    or 1 if no ndots statement is present. Names with fewer
	    dots are interpreted as relative names and will be
	    searched for in the domains listed in
	    the <type>search</type> or <type>domain</type> directive
	    in <filename>/etc/resolv.conf</filename>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-r</term>
	<listitem>
	  <para>
	    Non-recursive query:
	    Setting this option clears the RD (recursion desired) bit
	    in the query. This should mean that the name server
	    receiving the query will not attempt to
	    resolve <parameter>name</parameter>.
	    The <option>-r</option> option
	    enables <command>host</command> to mimic the behavior of a
	    name server by making non-recursive queries and expecting
	    to receive answers to those queries that can be
	    referrals to other name servers.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-R <replaceable class="parameter">number</replaceable></term>
	<listitem>
	  <para>
	    Number of retries for UDP queries:
	    If <parameter>number</parameter> is negative or zero, the
	    number of retries will default to 1. The default value is
	    1, or the value of the <parameter>attempts</parameter>
	    option in <filename>/etc/resolv.conf</filename>, if set.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-s</term>
	<listitem>
	  <para>
	    Do <emphasis>not</emphasis> send the query to the next
	    nameserver if any server responds with a SERVFAIL
	    response, which is the reverse of normal stub resolver
	    behavior.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-t <replaceable class="parameter">type</replaceable></term>
	<listitem>
	  <para>
	    Query type:
	    The <parameter>type</parameter> argument can be any
	    recognized query type: CNAME, NS, SOA, TXT, DNSKEY, AXFR, etc.
	  </para>
	  <para>
	    When no query type is specified, <command>host</command>
	    automatically selects an appropriate query type. By default, it
	    looks for A, AAAA, and MX records.
	    If the <option>-C</option> option is given, queries will
	    be made for SOA records.
	    If <parameter>name</parameter> is a dotted-decimal IPv4
	    address or colon-delimited IPv6
	    address, <command>host</command> will query for PTR
	    records.
	  </para>
	  <para>
	    If a query type of IXFR is chosen the starting serial
	    number can be specified by appending an equal followed by
	    the starting serial number
	    (like <option>-t <literal>IXFR=12345678</literal></option>).
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-T</term>
	<term>-U</term>
	<listitem>
	  <para>
	    TCP/UDP:
	    By default, <command>host</command> uses UDP when making
	    queries. The <option>-T</option> option makes it use a TCP
	    connection when querying the name server. TCP will be
	    automatically selected for queries that require it, such
	    as zone transfer (AXFR) requests.  Type ANY queries default
	    to TCP but can be forced to UDP initially using <option>-U</option>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-m <replaceable class="parameter">flag</replaceable></term>
	<listitem>
	  <para>
	    Memory usage debugging: the flag can
	    be <parameter>record</parameter>, <parameter>usage</parameter>,
	    or <parameter>trace</parameter>. You can specify
	    the <option>-m</option> option more than once to set
	    multiple flags.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-v</term>
	<listitem>
	  <para>
	    Verbose output.
	    Equivalent to the <option>-d</option> debug option.
	    Verbose output can also be enabled by setting
	    the <parameter>debug</parameter> option
	    in <filename>/etc/resolv.conf</filename>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-V</term>
	<listitem>
	  <para>
	    Print the version number and exit.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-w</term>
	<listitem>
	  <para>
	    Wait forever: The query timeout is set to the maximum possible.
	    See also the <option>-W</option> option.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-W <replaceable class="parameter">wait</replaceable></term>
	<listitem>
	  <para>
	    Timeout: Wait for up to <parameter>wait</parameter>
	    seconds for a reply. If <parameter>wait</parameter> is
	    less than one, the wait interval is set to one second.
	  </para>
	  <para>
	    By default, <command>host</command> will wait for 5
	    seconds for UDP responses and 10 seconds for TCP
	    connections. These defaults can be overridden by
	    the <parameter>timeout</parameter> option
	    in <filename>/etc/resolv.conf</filename>.
	  </para>
	  <para>
	    See also the <option>-w</option> option.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>

  </refsection>

  <refsection><info><title>IDN SUPPORT</title></info>

    <para>
      If <command>host</command> has been built with IDN (internationalized
      domain name) support, it can accept and display non-ASCII domain names.
      <command>host</command> appropriately converts character encoding of
      domain name before sending a request to DNS server or displaying a
      reply from the server.
      If you'd like to turn off the IDN support for some reason, defines
      the <envar>IDN_DISABLE</envar> environment variable.
      The IDN support is disabled if the variable is set when
      <command>host</command> runs.
    </para>
  </refsection>

  <refsection><info><title>FILES</title></info>

    <para><filename>/etc/resolv.conf</filename>
    </para>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
        <refentrytitle>dig</refentrytitle><manvolnum>1</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>named</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>.
    </para>
  </refsection>

</refentry>
