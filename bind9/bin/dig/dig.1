.\" Copyright (C) 2000-2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: dig
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-02-19
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "DIG" "1" "2014\-02\-19" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
dig \- DNS lookup utility
.SH "SYNOPSIS"
.HP \w'\fBdig\fR\ 'u
\fBdig\fR [@server] [\fB\-b\ \fR\fB\fIaddress\fR\fR] [\fB\-c\ \fR\fB\fIclass\fR\fR] [\fB\-f\ \fR\fB\fIfilename\fR\fR] [\fB\-k\ \fR\fB\fIfilename\fR\fR] [\fB\-m\fR] [\fB\-p\ \fR\fB\fIport#\fR\fR] [\fB\-q\ \fR\fB\fIname\fR\fR] [\fB\-t\ \fR\fB\fItype\fR\fR] [\fB\-v\fR] [\fB\-x\ \fR\fB\fIaddr\fR\fR] [\fB\-y\ \fR\fB\fI[hmac:]\fR\fIname:key\fR\fR] [[\fB\-4\fR] | [\fB\-6\fR]] [name] [type] [class] [queryopt...]
.HP \w'\fBdig\fR\ 'u
\fBdig\fR [\fB\-h\fR]
.HP \w'\fBdig\fR\ 'u
\fBdig\fR [global\-queryopt...] [query...]
.SH "DESCRIPTION"
.PP
\fBdig\fR
is a flexible tool for interrogating DNS name servers\&. It performs DNS lookups and displays the answers that are returned from the name server(s) that were queried\&. Most DNS administrators use
\fBdig\fR
to troubleshoot DNS problems because of its flexibility, ease of use and clarity of output\&. Other lookup tools tend to have less functionality than
\fBdig\fR\&.
.PP
Although
\fBdig\fR
is normally used with command\-line arguments, it also has a batch mode of operation for reading lookup requests from a file\&. A brief summary of its command\-line arguments and options is printed when the
\fB\-h\fR
option is given\&. Unlike earlier versions, the BIND 9 implementation of
\fBdig\fR
allows multiple lookups to be issued from the command line\&.
.PP
Unless it is told to query a specific name server,
\fBdig\fR
will try each of the servers listed in
/etc/resolv\&.conf\&. If no usable server addresses are found,
\fBdig\fR
will send the query to the local host\&.
.PP
When no command line arguments or options are given,
\fBdig\fR
will perform an NS query for "\&." (the root)\&.
.PP
It is possible to set per\-user defaults for
\fBdig\fR
via
${HOME}/\&.digrc\&. This file is read and any options in it are applied before the command line arguments\&.
.PP
The IN and CH class names overlap with the IN and CH top level domain names\&. Either use the
\fB\-t\fR
and
\fB\-c\fR
options to specify the type and class, use the
\fB\-q\fR
the specify the domain name, or use "IN\&." and "CH\&." when looking up these top level domains\&.
.SH "SIMPLE USAGE"
.PP
A typical invocation of
\fBdig\fR
looks like:
.sp
.if n \{\
.RS 4
.\}
.nf
 dig @server name type 
.fi
.if n \{\
.RE
.\}
.sp
where:
.PP
\fBserver\fR
.RS 4
is the name or IP address of the name server to query\&. This can be an IPv4 address in dotted\-decimal notation or an IPv6 address in colon\-delimited notation\&. When the supplied
\fIserver\fR
argument is a hostname,
\fBdig\fR
resolves that name before querying that name server\&.
.sp
If no
\fIserver\fR
argument is provided,
\fBdig\fR
consults
/etc/resolv\&.conf; if an address is found there, it queries the name server at that address\&. If either of the
\fB\-4\fR
or
\fB\-6\fR
options are in use, then only addresses for the corresponding transport will be tried\&. If no usable addresses are found,
\fBdig\fR
will send the query to the local host\&. The reply from the name server that responds is displayed\&.
.RE
.PP
\fBname\fR
.RS 4
is the name of the resource record that is to be looked up\&.
.RE
.PP
\fBtype\fR
.RS 4
indicates what type of query is required \(em ANY, A, MX, SIG, etc\&.
\fItype\fR
can be any valid query type\&. If no
\fItype\fR
argument is supplied,
\fBdig\fR
will perform a lookup for an A record\&.
.RE
.SH "OPTIONS"
.PP
\-4
.RS 4
Use IPv4 only\&.
.RE
.PP
\-6
.RS 4
Use IPv6 only\&.
.RE
.PP
\-b \fIaddress\fR\fI[#port]\fR
.RS 4
Set the source IP address of the query\&. The
\fIaddress\fR
must be a valid address on one of the host\*(Aqs network interfaces, or "0\&.0\&.0\&.0" or "::"\&. An optional port may be specified by appending "#<port>"
.RE
.PP
\-c \fIclass\fR
.RS 4
Set the query class\&. The default
\fIclass\fR
is IN; other classes are HS for Hesiod records or CH for Chaosnet records\&.
.RE
.PP
\-f \fIfile\fR
.RS 4
Batch mode:
\fBdig\fR
reads a list of lookup requests to process from the given
\fIfile\fR\&. Each line in the file should be organized in the same way they would be presented as queries to
\fBdig\fR
using the command\-line interface\&.
.RE
.PP
\-i
.RS 4
Do reverse IPv6 lookups using the obsolete RFC1886 IP6\&.INT domain, which is no longer in use\&. Obsolete bit string label queries (RFC2874) are not attempted\&.
.RE
.PP
\-k \fIkeyfile\fR
.RS 4
Sign queries using TSIG using a key read from the given file\&. Key files can be generated using
\fBtsig-keygen\fR(8)\&. When using TSIG authentication with
\fBdig\fR, the name server that is queried needs to know the key and algorithm that is being used\&. In BIND, this is done by providing appropriate
\fBkey\fR
and
\fBserver\fR
statements in
named\&.conf\&.
.RE
.PP
\-m
.RS 4
Enable memory usage debugging\&.
.RE
.PP
\-p \fIport\fR
.RS 4
Send the query to a non\-standard port on the server, instead of the default port 53\&. This option would be used to test a name server that has been configured to listen for queries on a non\-standard port number\&.
.RE
.PP
\-q \fIname\fR
.RS 4
The domain name to query\&. This is useful to distinguish the
\fIname\fR
from other arguments\&.
.RE
.PP
\-t \fItype\fR
.RS 4
The resource record type to query\&. It can be any valid query type which is supported in BIND 9\&. The default query type is "A", unless the
\fB\-x\fR
option is supplied to indicate a reverse lookup\&. A zone transfer can be requested by specifying a type of AXFR\&. When an incremental zone transfer (IXFR) is required, set the
\fItype\fR
to
ixfr=N\&. The incremental zone transfer will contain the changes made to the zone since the serial number in the zone\*(Aqs SOA record was
\fIN\fR\&.
.RE
.PP
\-u
.RS 4
Print query times in microseconds instead of milliseconds\&.
.RE
.PP
\-v
.RS 4
Print the version number and exit\&.
.RE
.PP
\-x \fIaddr\fR
.RS 4
Simplified reverse lookups, for mapping addresses to names\&. The
\fIaddr\fR
is an IPv4 address in dotted\-decimal notation, or a colon\-delimited IPv6 address\&. When the
\fB\-x\fR
is used, there is no need to provide the
\fIname\fR,
\fIclass\fR
and
\fItype\fR
arguments\&.
\fBdig\fR
automatically performs a lookup for a name like
94\&.2\&.0\&.192\&.in\-addr\&.arpa
and sets the query type and class to PTR and IN respectively\&. IPv6 addresses are looked up using nibble format under the IP6\&.ARPA domain (but see also the
\fB\-i\fR
option)\&.
.RE
.PP
\-y \fI[hmac:]\fR\fIkeyname:secret\fR
.RS 4
Sign queries using TSIG with the given authentication key\&.
\fIkeyname\fR
is the name of the key, and
\fIsecret\fR
is the base64 encoded shared secret\&.
\fIhmac\fR
is the name of the key algorithm; valid choices are
hmac\-md5,
hmac\-sha1,
hmac\-sha224,
hmac\-sha256,
hmac\-sha384, or
hmac\-sha512\&. If
\fIhmac\fR
is not specified, the default is
hmac\-md5
or if MD5 was disabled
hmac\-sha256\&.
.sp
NOTE: You should use the
\fB\-k\fR
option and avoid the
\fB\-y\fR
option, because with
\fB\-y\fR
the shared secret is supplied as a command line argument in clear text\&. This may be visible in the output from
\fBps\fR(1)
or in a history file maintained by the user\*(Aqs shell\&.
.RE
.SH "QUERY OPTIONS"
.PP
\fBdig\fR
provides a number of query options which affect the way in which lookups are made and the results displayed\&. Some of these set or reset flag bits in the query header, some determine which sections of the answer get printed, and others determine the timeout and retry strategies\&.
.PP
Each query option is identified by a keyword preceded by a plus sign (+)\&. Some keywords set or reset an option\&. These may be preceded by the string
no
to negate the meaning of that keyword\&. Other keywords assign values to options like the timeout interval\&. They have the form
\fB+keyword=value\fR\&. Keywords may be abbreviated, provided the abbreviation is unambiguous; for example,
+cd
is equivalent to
+cdflag\&. The query options are:
.PP
\fB+[no]aaflag\fR
.RS 4
A synonym for
\fI+[no]aaonly\fR\&.
.RE
.PP
\fB+[no]aaonly\fR
.RS 4
Sets the "aa" flag in the query\&.
.RE
.PP
\fB+[no]additional\fR
.RS 4
Display [do not display] the additional section of a reply\&. The default is to display it\&.
.RE
.PP
\fB+[no]adflag\fR
.RS 4
Set [do not set] the AD (authentic data) bit in the query\&. This requests the server to return whether all of the answer and authority sections have all been validated as secure according to the security policy of the server\&. AD=1 indicates that all records have been validated as secure and the answer is not from a OPT\-OUT range\&. AD=0 indicate that some part of the answer was insecure or not validated\&. This bit is set by default\&.
.RE
.PP
\fB+[no]all\fR
.RS 4
Set or clear all display flags\&.
.RE
.PP
\fB+[no]answer\fR
.RS 4
Display [do not display] the answer section of a reply\&. The default is to display it\&.
.RE
.PP
\fB+[no]authority\fR
.RS 4
Display [do not display] the authority section of a reply\&. The default is to display it\&.
.RE
.PP
\fB+[no]badcookie\fR
.RS 4
Retry lookup with the new server cookie if a BADCOOKIE response is received\&.
.RE
.PP
\fB+[no]besteffort\fR
.RS 4
Attempt to display the contents of messages which are malformed\&. The default is to not display malformed answers\&.
.RE
.PP
\fB+bufsize=B\fR
.RS 4
Set the UDP message buffer size advertised using EDNS0 to
\fIB\fR
bytes\&. The maximum and minimum sizes of this buffer are 65535 and 0 respectively\&. Values outside this range are rounded up or down appropriately\&. Values other than zero will cause a EDNS query to be sent\&.
.RE
.PP
\fB+[no]cdflag\fR
.RS 4
Set [do not set] the CD (checking disabled) bit in the query\&. This requests the server to not perform DNSSEC validation of responses\&.
.RE
.PP
\fB+[no]class\fR
.RS 4
Display [do not display] the CLASS when printing the record\&.
.RE
.PP
\fB+[no]cmd\fR
.RS 4
Toggles the printing of the initial comment in the output identifying the version of
\fBdig\fR
and the query options that have been applied\&. This comment is printed by default\&.
.RE
.PP
\fB+[no]comments\fR
.RS 4
Toggle the display of comment lines in the output\&. The default is to print comments\&.
.RE
.PP
\fB+[no]cookie\fR\fB[=####]\fR
.RS 4
Send a COOKIE EDNS option, with optional value\&. Replaying a COOKIE from a previous response will allow the server to identify a previous client\&. The default is
\fB+cookie\fR\&.
.sp
\fB+cookie\fR
is also set when +trace is set to better emulate the default queries from a nameserver\&.
.RE
.PP
\fB+[no]crypto\fR
.RS 4
Toggle the display of cryptographic fields in DNSSEC records\&. The contents of these field are unnecessary to debug most DNSSEC validation failures and removing them makes it easier to see the common failures\&. The default is to display the fields\&. When omitted they are replaced by the string "[omitted]" or in the DNSKEY case the key id is displayed as the replacement, e\&.g\&. "[ key id = value ]"\&.
.RE
.PP
\fB+[no]defname\fR
.RS 4
Deprecated, treated as a synonym for
\fI+[no]search\fR
.RE
.PP
\fB+[no]dnssec\fR
.RS 4
Requests DNSSEC records be sent by setting the DNSSEC OK bit (DO) in the OPT record in the additional section of the query\&.
.RE
.PP
\fB+domain=somename\fR
.RS 4
Set the search list to contain the single domain
\fIsomename\fR, as if specified in a
\fBdomain\fR
directive in
/etc/resolv\&.conf, and enable search list processing as if the
\fI+search\fR
option were given\&.
.RE
.PP
\fB+dscp=value\fR
.RS 4
Set the DSCP code point to be used when sending the query\&. Valid DSCP code points are in the range [0\&.\&.63]\&. By default no code point is explicitly set\&.
.RE
.PP
\fB+[no]edns[=#]\fR
.RS 4
Specify the EDNS version to query with\&. Valid values are 0 to 255\&. Setting the EDNS version will cause a EDNS query to be sent\&.
\fB+noedns\fR
clears the remembered EDNS version\&. EDNS is set to 0 by default\&.
.RE
.PP
\fB+[no]ednsflags[=#]\fR
.RS 4
Set the must\-be\-zero EDNS flags bits (Z bits) to the specified value\&. Decimal, hex and octal encodings are accepted\&. Setting a named flag (e\&.g\&. DO) will silently be ignored\&. By default, no Z bits are set\&.
.RE
.PP
\fB+[no]ednsnegotiation\fR
.RS 4
Enable / disable EDNS version negotiation\&. By default EDNS version negotiation is enabled\&.
.RE
.PP
\fB+[no]ednsopt[=code[:value]]\fR
.RS 4
Specify EDNS option with code point
\fBcode\fR
and optionally payload of
\fBvalue\fR
as a hexadecimal string\&.
\fBcode\fR
can be either an EDNS option name (for example,
NSID
or
ECS), or an arbitrary numeric value\&.
\fB+noednsopt\fR
clears the EDNS options to be sent\&.
.RE
.PP
\fB+[no]expire\fR
.RS 4
Send an EDNS Expire option\&.
.RE
.PP
\fB+[no]fail\fR
.RS 4
Do not try the next server if you receive a SERVFAIL\&. The default is to not try the next server which is the reverse of normal stub resolver behavior\&.
.RE
.PP
\fB+[no]header\-only\fR
.RS 4
Send a query with a DNS header without a question section\&. The default is to add a question section\&. The query type and query name are ignored when this is set\&.
.RE
.PP
\fB+[no]identify\fR
.RS 4
Show [or do not show] the IP address and port number that supplied the answer when the
\fI+short\fR
option is enabled\&. If short form answers are requested, the default is not to show the source address and port number of the server that provided the answer\&.
.RE
.PP
\fB+[no]idnout\fR
.RS 4
Convert [do not convert] puny code on output\&. This requires IDN SUPPORT to have been enabled at compile time\&. The default is to convert output\&.
.RE
.PP
\fB+[no]ignore\fR
.RS 4
Ignore truncation in UDP responses instead of retrying with TCP\&. By default, TCP retries are performed\&.
.RE
.PP
\fB+[no]keepopen\fR
.RS 4
Keep the TCP socket open between queries and reuse it rather than creating a new TCP socket for each lookup\&. The default is
\fB+nokeepopen\fR\&.
.RE
.PP
\fB+[no]mapped\fR
.RS 4
Allow mapped IPv4 over IPv6 addresses to be used\&. The default is
\fB+mapped\fR\&.
.RE
.PP
\fB+[no]multiline\fR
.RS 4
Print records like the SOA records in a verbose multi\-line format with human\-readable comments\&. The default is to print each record on a single line, to facilitate machine parsing of the
\fBdig\fR
output\&.
.RE
.PP
\fB+ndots=D\fR
.RS 4
Set the number of dots that have to appear in
\fIname\fR
to
\fID\fR
for it to be considered absolute\&. The default value is that defined using the ndots statement in
/etc/resolv\&.conf, or 1 if no ndots statement is present\&. Names with fewer dots are interpreted as relative names and will be searched for in the domains listed in the
\fBsearch\fR
or
\fBdomain\fR
directive in
/etc/resolv\&.conf
if
\fB+search\fR
is set\&.
.RE
.PP
\fB+[no]nsid\fR
.RS 4
Include an EDNS name server ID request when sending a query\&.
.RE
.PP
\fB+[no]nssearch\fR
.RS 4
When this option is set,
\fBdig\fR
attempts to find the authoritative name servers for the zone containing the name being looked up and display the SOA record that each name server has for the zone\&.
.RE
.PP
\fB+[no]onesoa\fR
.RS 4
Print only one (starting) SOA record when performing an AXFR\&. The default is to print both the starting and ending SOA records\&.
.RE
.PP
\fB+[no]opcode=value\fR
.RS 4
Set [restore] the DNS message opcode to the specified value\&. The default value is QUERY (0)\&.
.RE
.PP
\fB+padding=value\fR
.RS 4
Pad the size of the query packet using the EDNS Padding option to blocks of
\fIvalue\fR
bytes\&. For example,
\fB+padding=32\fR
would cause a 48\-byte query to be padded to 64 bytes\&. The default block size is 0, which disables padding\&. The maximum is 512\&. Values are ordinarily expected to be powers of two, such as 128; however, this is not mandatory\&. Responses to padded queries may also be padded, but only if the query uses TCP or DNS COOKIE\&.
.RE
.PP
\fB+[no]qr\fR
.RS 4
Print [do not print] the query as it is sent\&. By default, the query is not printed\&.
.RE
.PP
\fB+[no]question\fR
.RS 4
Print [do not print] the question section of a query when an answer is returned\&. The default is to print the question section as a comment\&.
.RE
.PP
\fB+[no]rdflag\fR
.RS 4
A synonym for
\fI+[no]recurse\fR\&.
.RE
.PP
\fB+[no]recurse\fR
.RS 4
Toggle the setting of the RD (recursion desired) bit in the query\&. This bit is set by default, which means
\fBdig\fR
normally sends recursive queries\&. Recursion is automatically disabled when the
\fI+nssearch\fR
or
\fI+trace\fR
query options are used\&.
.RE
.PP
\fB+retry=T\fR
.RS 4
Sets the number of times to retry UDP queries to server to
\fIT\fR
instead of the default, 2\&. Unlike
\fI+tries\fR, this does not include the initial query\&.
.RE
.PP
\fB+[no]rrcomments\fR
.RS 4
Toggle the display of per\-record comments in the output (for example, human\-readable key information about DNSKEY records)\&. The default is not to print record comments unless multiline mode is active\&.
.RE
.PP
\fB+[no]search\fR
.RS 4
Use [do not use] the search list defined by the searchlist or domain directive in
resolv\&.conf
(if any)\&. The search list is not used by default\&.
.sp
\*(Aqndots\*(Aq from
resolv\&.conf
(default 1) which may be overridden by
\fI+ndots\fR
determines if the name will be treated as relative or not and hence whether a search is eventually performed or not\&.
.RE
.PP
\fB+[no]short\fR
.RS 4
Provide a terse answer\&. The default is to print the answer in a verbose form\&.
.RE
.PP
\fB+[no]showsearch\fR
.RS 4
Perform [do not perform] a search showing intermediate results\&.
.RE
.PP
\fB+[no]sigchase\fR
.RS 4
Chase DNSSEC signature chains\&. Requires dig be compiled with \-DDIG_SIGCHASE\&. This feature is deprecated\&. Use
\fBdelv\fR
instead\&.
.RE
.PP
\fB+split=W\fR
.RS 4
Split long hex\- or base64\-formatted fields in resource records into chunks of
\fIW\fR
characters (where
\fIW\fR
is rounded up to the nearest multiple of 4)\&.
\fI+nosplit\fR
or
\fI+split=0\fR
causes fields not to be split at all\&. The default is 56 characters, or 44 characters when multiline mode is active\&.
.RE
.PP
\fB+[no]stats\fR
.RS 4
This query option toggles the printing of statistics: when the query was made, the size of the reply and so on\&. The default behavior is to print the query statistics\&.
.RE
.PP
\fB+[no]subnet=addr[/prefix\-length]\fR
.RS 4
Send (don\*(Aqt send) an EDNS CLIENT\-SUBNET option with the specified IP address or network prefix\&.
.sp
\fBdig +subnet=0\&.0\&.0\&.0/0\fR, or simply
\fBdig +subnet=0\fR
for short, sends an EDNS CLIENT\-SUBNET option with an empty address and a source prefix\-length of zero, which signals to a resolver that the client\*(Aqs address information must
\fInot\fR
be used when resolving this query\&.
.RE
.PP
\fB+[no]tcp\fR
.RS 4
Use [do not use] TCP when querying name servers\&. The default behavior is to use UDP unless a type
any
or
ixfr=N
query is requested, in which case the default is TCP\&. AXFR queries always use TCP\&.
.RE
.PP
\fB+timeout=T\fR
.RS 4
Sets the timeout for a query to
\fIT\fR
seconds\&. The default timeout is 5 seconds\&. An attempt to set
\fIT\fR
to less than 1 will result in a query timeout of 1 second being applied\&.
.RE
.PP
\fB+[no]topdown\fR
.RS 4
When chasing DNSSEC signature chains perform a top\-down validation\&. Requires dig be compiled with \-DDIG_SIGCHASE\&. This feature is deprecated\&. Use
\fBdelv\fR
instead\&.
.RE
.PP
\fB+[no]trace\fR
.RS 4
Toggle tracing of the delegation path from the root name servers for the name being looked up\&. Tracing is disabled by default\&. When tracing is enabled,
\fBdig\fR
makes iterative queries to resolve the name being looked up\&. It will follow referrals from the root servers, showing the answer from each server that was used to resolve the lookup\&.
.sp
If @server is also specified, it affects only the initial query for the root zone name servers\&.
.sp
\fB+dnssec\fR
is also set when +trace is set to better emulate the default queries from a nameserver\&.
.RE
.PP
\fB+tries=T\fR
.RS 4
Sets the number of times to try UDP queries to server to
\fIT\fR
instead of the default, 3\&. If
\fIT\fR
is less than or equal to zero, the number of tries is silently rounded up to 1\&.
.RE
.PP
\fB+trusted\-key=####\fR
.RS 4
Specifies a file containing trusted keys to be used with
\fB+sigchase\fR\&. Each DNSKEY record must be on its own line\&.
.sp
If not specified,
\fBdig\fR
will look for
/etc/trusted\-key\&.key
then
trusted\-key\&.key
in the current directory\&.
.sp
Requires dig be compiled with \-DDIG_SIGCHASE\&. This feature is deprecated\&. Use
\fBdelv\fR
instead\&.
.RE
.PP
\fB+[no]ttlid\fR
.RS 4
Display [do not display] the TTL when printing the record\&.
.RE
.PP
\fB+[no]ttlunits\fR
.RS 4
Display [do not display] the TTL in friendly human\-readable time units of "s", "m", "h", "d", and "w", representing seconds, minutes, hours, days and weeks\&. Implies +ttlid\&.
.RE
.PP
\fB+[no]unknownformat\fR
.RS 4
Print all RDATA in unknown RR type presentation format (RFC 3597)\&. The default is to print RDATA for known types in the type\*(Aqs presentation format\&.
.RE
.PP
\fB+[no]vc\fR
.RS 4
Use [do not use] TCP when querying name servers\&. This alternate syntax to
\fI+[no]tcp\fR
is provided for backwards compatibility\&. The "vc" stands for "virtual circuit"\&.
.RE
.PP
\fB+[no]zflag\fR
.RS 4
Set [do not set] the last unassigned DNS header flag in a DNS query\&. This flag is off by default\&.
.RE
.SH "MULTIPLE QUERIES"
.PP
The BIND 9 implementation of
\fBdig \fR
supports specifying multiple queries on the command line (in addition to supporting the
\fB\-f\fR
batch file option)\&. Each of those queries can be supplied with its own set of flags, options and query options\&.
.PP
In this case, each
\fIquery\fR
argument represent an individual query in the command\-line syntax described above\&. Each consists of any of the standard options and flags, the name to be looked up, an optional query type and class and any query options that should be applied to that query\&.
.PP
A global set of query options, which should be applied to all queries, can also be supplied\&. These global query options must precede the first tuple of name, class, type, options, flags, and query options supplied on the command line\&. Any global query options (except the
\fB+[no]cmd\fR
option) can be overridden by a query\-specific set of query options\&. For example:
.sp
.if n \{\
.RS 4
.\}
.nf
dig +qr www\&.isc\&.org any \-x 127\&.0\&.0\&.1 isc\&.org ns +noqr
.fi
.if n \{\
.RE
.\}
.sp
shows how
\fBdig\fR
could be used from the command line to make three lookups: an ANY query for
www\&.isc\&.org, a reverse lookup of 127\&.0\&.0\&.1 and a query for the NS records of
isc\&.org\&. A global query option of
\fI+qr\fR
is applied, so that
\fBdig\fR
shows the initial query it made for each lookup\&. The final query has a local query option of
\fI+noqr\fR
which means that
\fBdig\fR
will not print the initial query when it looks up the NS records for
isc\&.org\&.
.SH "IDN SUPPORT"
.PP
If
\fBdig\fR
has been built with IDN (internationalized domain name) support, it can accept and display non\-ASCII domain names\&.
\fBdig\fR
appropriately converts character encoding of domain name before sending a request to DNS server or displaying a reply from the server\&. If you\*(Aqd like to turn off the IDN support for some reason, defines the
\fBIDN_DISABLE\fR
environment variable\&. The IDN support is disabled if the variable is set when
\fBdig\fR
runs\&.
.SH "FILES"
.PP
/etc/resolv\&.conf
.PP
${HOME}/\&.digrc
.SH "SEE ALSO"
.PP
\fBdelv\fR(1),
\fBhost\fR(1),
\fBnamed\fR(8),
\fBdnssec-keygen\fR(8),
RFC1035\&.
.SH "BUGS"
.PP
There are probably too many query options\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2000-2011, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
.br
