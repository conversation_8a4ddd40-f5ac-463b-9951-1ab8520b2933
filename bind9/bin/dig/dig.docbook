<!DOCTYPE book [
<!ENTITY mdash "&#8212;">]>
<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.dig">
  <info>
    <date>2014-02-19</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle>dig</refentrytitle>
    <manvolnum>1</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname>dig</refname>
    <refpurpose>DNS lookup utility</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2000</year>
      <year>2001</year>
      <year>2002</year>
      <year>2003</year>
      <year>2004</year>
      <year>2005</year>
      <year>2006</year>
      <year>2007</year>
      <year>2008</year>
      <year>2009</year>
      <year>2010</year>
      <year>2011</year>
      <year>2013</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2017</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>dig</command>
      <arg choice="opt" rep="norepeat">@server</arg>
      <arg choice="opt" rep="norepeat"><option>-b <replaceable class="parameter">address</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">class</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-f <replaceable class="parameter">filename</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-k <replaceable class="parameter">filename</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-m</option></arg>
      <arg choice="opt" rep="norepeat"><option>-p <replaceable class="parameter">port#</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-q <replaceable class="parameter">name</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-t <replaceable class="parameter">type</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-v</option></arg>
      <arg choice="opt" rep="norepeat"><option>-x <replaceable class="parameter">addr</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-y <replaceable class="parameter"><optional>hmac:</optional>name:key</replaceable></option></arg>
      <group choice="opt" rep="norepeat">
	<arg choice="opt" rep="norepeat"><option>-4</option></arg>
	<arg choice="opt" rep="norepeat"><option>-6</option></arg>
      </group>
      <arg choice="opt" rep="norepeat">name</arg>
      <arg choice="opt" rep="norepeat">type</arg>
      <arg choice="opt" rep="norepeat">class</arg>
      <arg choice="opt" rep="repeat">queryopt</arg>
    </cmdsynopsis>

    <cmdsynopsis sepchar=" ">
      <command>dig</command>
      <arg choice="opt" rep="norepeat"><option>-h</option></arg>
    </cmdsynopsis>

    <cmdsynopsis sepchar=" ">
      <command>dig</command>
      <arg choice="opt" rep="repeat">global-queryopt</arg>
      <arg choice="opt" rep="repeat">query</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>dig</command> is a flexible tool
      for interrogating DNS name servers.  It performs DNS lookups and
      displays the answers that are returned from the name server(s) that
      were queried.  Most DNS administrators use <command>dig</command> to
      troubleshoot DNS problems because of its flexibility, ease of use and
      clarity of output.  Other lookup tools tend to have less functionality
      than <command>dig</command>.
    </para>

    <para>
      Although <command>dig</command> is normally used with
      command-line
      arguments, it also has a batch mode of operation for reading lookup
      requests from a file.  A brief summary of its command-line arguments
      and options is printed when the <option>-h</option> option is given.
      Unlike earlier versions, the BIND 9 implementation of
      <command>dig</command> allows multiple lookups to be issued
      from the
      command line.
    </para>

    <para>
      Unless it is told to query a specific name server,
      <command>dig</command> will try each of the servers listed in
      <filename>/etc/resolv.conf</filename>. If no usable server addresses
      are found, <command>dig</command> will send the query to the local
      host.
    </para>

    <para>
      When no command line arguments or options are given,
      <command>dig</command> will perform an NS query for "." (the root).
    </para>

    <para>
      It is possible to set per-user defaults for <command>dig</command> via
      <filename>${HOME}/.digrc</filename>.  This file is read and
      any options in it
      are applied before the command line arguments.
    </para>

    <para>
      The IN and CH class names overlap with the IN and CH top level
      domain names.  Either use the <option>-t</option> and
      <option>-c</option> options to specify the type and class,
      use the <option>-q</option> the specify the domain name, or
      use "IN." and "CH." when looking up these top level domains.
    </para>

  </refsection>

  <refsection><info><title>SIMPLE USAGE</title></info>


    <para>
      A typical invocation of <command>dig</command> looks like:
      <programlisting> dig @server name type </programlisting>
      where:

      <variablelist>

	<varlistentry>
	  <term><constant>server</constant></term>
	  <listitem>
	    <para>
	      is the name or IP address of the name server to query.  This
	      can be an IPv4 address in dotted-decimal notation or an IPv6
	      address in colon-delimited notation.  When the supplied
	      <parameter>server</parameter> argument is a hostname,
	      <command>dig</command> resolves that name before querying
	      that name server.
	    </para>
	    <para>
	      If no <parameter>server</parameter> argument is
	      provided, <command>dig</command> consults
	      <filename>/etc/resolv.conf</filename>; if an
	      address is found there, it queries the name server at
	      that address. If either of the <option>-4</option> or
	      <option>-6</option> options are in use, then
	      only addresses for the corresponding transport
	      will be tried.  If no usable addresses are found,
	      <command>dig</command> will send the query to the
	      local host.  The reply from the name server that
	      responds is displayed.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><constant>name</constant></term>
	  <listitem>
	    <para>
	      is the name of the resource record that is to be looked up.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><constant>type</constant></term>
	  <listitem>
	    <para>
	      indicates what type of query is required &mdash;
	      ANY, A, MX, SIG, etc.
	      <parameter>type</parameter> can be any valid query
	      type.  If no
	      <parameter>type</parameter> argument is supplied,
	      <command>dig</command> will perform a lookup for an
	      A record.
	    </para>
	  </listitem>
	</varlistentry>

      </variablelist>
    </para>

  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
	<term>-4</term>
	<listitem>
	  <para>
	    Use IPv4 only.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-6</term>
	<listitem>
	  <para>
	    Use IPv6 only.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-b <replaceable class="parameter">address<optional>#port</optional></replaceable></term>
	<listitem>
	  <para>
	    Set the source IP address of the query.
	    The <parameter>address</parameter> must be a valid address on
	    one of the host's network interfaces, or "0.0.0.0" or "::". An
	    optional port may be specified by appending "#&lt;port&gt;"
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-c <replaceable class="parameter">class</replaceable></term>
	<listitem>
	  <para>
	    Set the query class. The
	    default <parameter>class</parameter> is IN; other classes
	    are HS for Hesiod records or CH for Chaosnet records.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-f <replaceable class="parameter">file</replaceable></term>
	<listitem>
	  <para>
	    Batch mode: <command>dig</command> reads a list of lookup
	    requests to process from the
	    given <parameter>file</parameter>. Each line in the file
	    should be organized in the same way they would be
	    presented as queries to
	    <command>dig</command> using the command-line interface.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-i</term>
	<listitem>
	  <para>
	    Do reverse IPv6 lookups using the obsolete RFC1886 IP6.INT
	    domain, which is no longer in use. Obsolete bit string
	    label queries (RFC2874) are not attempted.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-k <replaceable class="parameter">keyfile</replaceable></term>
	<listitem>
	  <para>
	    Sign queries using TSIG using a key read from the given file.
	    Key files can be generated using
	    <citerefentry>
	      <refentrytitle>tsig-keygen</refentrytitle><manvolnum>8</manvolnum>
	    </citerefentry>.
	    When using TSIG authentication with <command>dig</command>,
	    the name server that is queried needs to know the key and
	    algorithm that is being used. In BIND, this is done by
	    providing appropriate <command>key</command>
	    and <command>server</command> statements in
	    <filename>named.conf</filename>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-m</term>
	<listitem>
	  <para>
	    Enable memory usage debugging.
	    <!-- It enables ISC_MEM_DEBUGTRACE and ISC_MEM_DEBUGRECORD
		 documented in include/isc/mem.h -->
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-p <replaceable class="parameter">port</replaceable></term>
	<listitem>
	  <para>
	    Send the query to a non-standard port on the server,
	    instead of the default port 53. This option would be used
	    to test a name server that has been configured to listen
	    for queries on a non-standard port number.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-q <replaceable class="parameter">name</replaceable></term>
	<listitem>
	  <para>
	    The domain name to query. This is useful to distinguish
	    the <parameter>name</parameter> from other arguments.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-t <replaceable class="parameter">type</replaceable></term>
	<listitem>
	  <para>
	    The resource record type to query. It can be any valid query type
	    which is
	    supported in BIND 9.  The default query type is "A", unless the
	    <option>-x</option> option is supplied to indicate a reverse lookup.
	    A zone transfer can be requested by specifying a type of AXFR.  When
	    an incremental zone transfer (IXFR) is required, set the
	    <parameter>type</parameter> to <literal>ixfr=N</literal>.
	    The incremental zone transfer will contain the changes
	    made to the zone since the serial number in the zone's SOA
	    record was
	    <parameter>N</parameter>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-u</term>
	<listitem>
	  <para>
	    Print query times in microseconds instead of milliseconds.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-v</term>
	<listitem>
	  <para>
	    Print the version number and exit.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-x <replaceable class="parameter">addr</replaceable></term>
	<listitem>
	  <para>
	    Simplified reverse lookups, for mapping addresses to
	    names. The <parameter>addr</parameter> is an IPv4 address
	    in dotted-decimal notation, or a colon-delimited IPv6
	    address. When the <option>-x</option> is used, there is no
	    need to provide
	    the <parameter>name</parameter>, <parameter>class</parameter>
	    and <parameter>type</parameter>
	    arguments. <command>dig</command> automatically performs a
	    lookup for a name like
	    <literal>**********.in-addr.arpa</literal> and sets the
	    query type and class to PTR and IN respectively. IPv6
	    addresses are looked up using nibble format under the
	    IP6.ARPA domain (but see also the <option>-i</option>
	    option).
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-y <replaceable class="parameter"><optional>hmac:</optional>keyname:secret</replaceable></term>
	<listitem>
	  <para>
	    Sign queries using TSIG with the given authentication key.
	    <parameter>keyname</parameter> is the name of the key, and
	    <parameter>secret</parameter> is the base64 encoded shared secret.
	    <parameter>hmac</parameter> is the name of the key algorithm;
	    valid choices are <literal>hmac-md5</literal>,
	    <literal>hmac-sha1</literal>, <literal>hmac-sha224</literal>,
	    <literal>hmac-sha256</literal>, <literal>hmac-sha384</literal>, or
	    <literal>hmac-sha512</literal>.  If <parameter>hmac</parameter>
	    is not specified, the default is <literal>hmac-md5</literal>
	    or if MD5 was disabled <literal>hmac-sha256</literal>.
	  </para>
	  <para>
	    NOTE: You should use the <option>-k</option> option and
	    avoid the <option>-y</option> option, because
	    with <option>-y</option> the shared secret is supplied as
	    a command line argument in clear text. This may be visible
	    in the output from
	    <citerefentry>
	      <refentrytitle>ps</refentrytitle><manvolnum>1</manvolnum>
	    </citerefentry>
	    or in a history file maintained by the user's shell.
	  </para>
	</listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>QUERY OPTIONS</title></info>


    <para><command>dig</command>
      provides a number of query options which affect
      the way in which lookups are made and the results displayed.  Some of
      these set or reset flag bits in the query header, some determine which
      sections of the answer get printed, and others determine the timeout
      and retry strategies.
    </para>

    <para>
      Each query option is identified by a keyword preceded by a plus sign
      (<literal>+</literal>).  Some keywords set or reset an
      option.  These may be preceded
      by the string <literal>no</literal> to negate the meaning of
      that keyword.  Other
      keywords assign values to options like the timeout interval.  They
      have the form <option>+keyword=value</option>.
      Keywords may be abbreviated, provided the abbreviation is
      unambiguous; for example, <literal>+cd</literal> is equivalent
      to <literal>+cdflag</literal>.
      The query options are:

      <variablelist>

	<varlistentry>
	  <term><option>+[no]aaflag</option></term>
	  <listitem>
	    <para>
	      A synonym for <parameter>+[no]aaonly</parameter>.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]aaonly</option></term>
	  <listitem>
	    <para>
	      Sets the "aa" flag in the query.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]additional</option></term>
	  <listitem>
	    <para>
	      Display [do not display] the additional section of a
	      reply.  The default is to display it.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]adflag</option></term>
	  <listitem>
	    <para>
	      Set [do not set] the AD (authentic data) bit in the
	      query.  This requests the server to return whether
	      all of the answer and authority sections have all
	      been validated as secure according to the security
	      policy of the server.  AD=1 indicates that all records
	      have been validated as secure and the answer is not
	      from a OPT-OUT range.  AD=0 indicate that some part
	      of the answer was insecure or not validated.  This
	      bit is set by default.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]all</option></term>
	  <listitem>
	    <para>
	      Set or clear all display flags.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]answer</option></term>
	  <listitem>
	    <para>
	      Display [do not display] the answer section of a
	      reply.  The default is to display it.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]authority</option></term>
	  <listitem>
	    <para>
	      Display [do not display] the authority section of a
	      reply.  The default is to display it.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]badcookie</option></term>
	  <listitem>
	    <para>
	      Retry lookup with the new server cookie if a
	      BADCOOKIE response is received.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]besteffort</option></term>
	  <listitem>
	    <para>
	      Attempt to display the contents of messages which are
	      malformed.  The default is to not display malformed
	      answers.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+bufsize=B</option></term>
	  <listitem>
	    <para>
	      Set the UDP message buffer size advertised using EDNS0
	      to <parameter>B</parameter> bytes.  The maximum and
	      minimum sizes of this buffer are 65535 and 0 respectively.
	      Values outside this range are rounded up or down
	      appropriately.  Values other than zero will cause a
	      EDNS query to be sent.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]cdflag</option></term>
	  <listitem>
	    <para>
	      Set [do not set] the CD (checking disabled) bit in
	      the query.  This requests the server to not perform
	      DNSSEC validation of responses.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]class</option></term>
	  <listitem>
	    <para>
	      Display [do not display] the CLASS when printing the
	      record.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]cmd</option></term>
	  <listitem>
	    <para>
	      Toggles the printing of the initial comment in the
	      output identifying the version of <command>dig</command>
	      and the query options that have been applied.  This
	      comment is printed by default.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]comments</option></term>
	  <listitem>
	    <para>
	      Toggle the display of comment lines in the output.
	      The default is to print comments.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]cookie<optional>=####</optional></option></term>
	  <listitem>
	    <para>
	      Send a COOKIE EDNS option, with optional
	      value.  Replaying a COOKIE from a previous response will
	      allow the server to identify a previous client.  The
	      default is <option>+cookie</option>.
	    </para>
	    <para>
	      <command>+cookie</command> is also set when +trace
	      is set to better emulate the default queries from a
	      nameserver.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]crypto</option></term>
	  <listitem>
	    <para>
	      Toggle the display of cryptographic fields in DNSSEC
	      records.  The contents of these field are unnecessary
	      to debug most DNSSEC validation failures and removing
	      them makes it easier to see the common failures.  The
	      default is to display the fields.  When omitted they
	      are replaced by the string "[omitted]" or in the
	      DNSKEY case the key id is displayed as the replacement,
	      e.g. "[ key id = value ]".
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]defname</option></term>
	  <listitem>
	    <para>
	      Deprecated, treated as a synonym for
	      <parameter>+[no]search</parameter>
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]dnssec</option></term>
	  <listitem>
	    <para>
	      Requests DNSSEC records be sent by setting the DNSSEC
	      OK bit (DO) in the OPT record in the additional section
	      of the query.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+domain=somename</option></term>
	  <listitem>
	    <para>
	      Set the search list to contain the single domain
	      <parameter>somename</parameter>, as if specified in
	      a <command>domain</command> directive in
	      <filename>/etc/resolv.conf</filename>, and enable
	      search list processing as if the
	      <parameter>+search</parameter> option were given.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+dscp=value</option></term> <listitem>
	    <para>
	      Set the DSCP code point to be used when sending the
	      query.  Valid DSCP code points are in the range
	      [0..63].  By default no code point is explicitly set.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]edns[=#]</option></term>
	  <listitem>
	    <para>
	       Specify the EDNS version to query with.  Valid values
	       are 0 to 255.  Setting the EDNS version will cause
	       a EDNS query to be sent.  <option>+noedns</option>
	       clears the remembered EDNS version.  EDNS is set to
	       0 by default.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]ednsflags[=#]</option></term>
	  <listitem>
	    <para>
	      Set the must-be-zero EDNS flags bits (Z bits) to the
	      specified value. Decimal, hex and octal encodings are
	      accepted. Setting a named flag (e.g. DO) will silently be
	      ignored. By default, no Z bits are set.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]ednsnegotiation</option></term>
	  <listitem>
	    <para>
	      Enable / disable EDNS version negotiation. By default
	      EDNS version negotiation is enabled.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]ednsopt[=code[:value]]</option></term>
	  <listitem>
	    <para>
	      Specify EDNS option with code point <option>code</option>
	      and optionally payload of <option>value</option> as a
	      hexadecimal string.  <option>code</option> can be
	      either an EDNS option name (for example,
	      <literal>NSID</literal> or <literal>ECS</literal>),
	      or an arbitrary numeric value.  <option>+noednsopt</option>
	      clears the EDNS options to be sent.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]expire</option></term>
	  <listitem>
	    <para>
	      Send an EDNS Expire option.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]fail</option></term>
	  <listitem>
	    <para>
	      Do not try the next server if you receive a SERVFAIL.
	      The default is to not try the next server which is
	      the reverse of normal stub resolver behavior.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]header-only</option></term>
	  <listitem>
	    <para>
	      Send a query with a DNS header without a question section.
	      The default is to add a question section.  The query type
	      and query name are ignored when this is set.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]identify</option></term>
	  <listitem>
	    <para>
	      Show [or do not show] the IP address and port number
	      that supplied the answer when the
	      <parameter>+short</parameter> option is enabled.  If
	      short form answers are requested, the default is not
	      to show the source address and port number of the
	      server that provided the answer.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]idnout</option></term>
	  <listitem>
	    <para>
	      Convert [do not convert] puny code on output.
	      This requires IDN SUPPORT to have been enabled at
	      compile time.  The default is to convert output.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]ignore</option></term>
	  <listitem>
	    <para>
	      Ignore truncation in UDP responses instead of retrying
	      with TCP.  By default, TCP retries are performed.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]keepopen</option></term>
	  <listitem>
	    <para>
	      Keep the TCP socket open between queries and reuse
	      it rather than creating a new TCP socket for each
	      lookup.  The default is <option>+nokeepopen</option>.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]mapped</option></term>
	  <listitem>
	    <para>
	      Allow mapped IPv4 over IPv6 addresses to be used.  The
	      default is <option>+mapped</option>.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]multiline</option></term>
	  <listitem>
	    <para>
	      Print records like the SOA records in a verbose
	      multi-line format with human-readable comments.  The
	      default is to print each record on a single line, to
	      facilitate machine parsing of the <command>dig</command>
	      output.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+ndots=D</option></term>
	  <listitem>
	    <para>
	      Set the number of dots that have to appear in
	      <parameter>name</parameter> to <parameter>D</parameter>
	      for it to be considered absolute.  The default value
	      is that defined using the ndots statement in
	      <filename>/etc/resolv.conf</filename>, or 1 if no
	      ndots statement is present.  Names with fewer dots
	      are interpreted as relative names and will be searched
	      for in the domains listed in the <option>search</option>
	      or <option>domain</option> directive in
	      <filename>/etc/resolv.conf</filename> if
	      <option>+search</option> is set.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]nsid</option></term>
	  <listitem>
	    <para>
	      Include an EDNS name server ID request when sending
	      a query.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]nssearch</option></term>
	  <listitem>
	    <para>
	      When this option is set, <command>dig</command>
	      attempts to find the authoritative name servers for
	      the zone containing the name being looked up and
	      display the SOA record that each name server has for
	      the zone.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]onesoa</option></term>
	  <listitem>
	    <para>
	      Print only one (starting) SOA record when performing
	      an AXFR. The default is to print both the starting
	      and ending SOA records.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]opcode=value</option></term>
	  <listitem>
	    <para>
	      Set [restore] the DNS message opcode to the specified
	      value.  The default value is QUERY (0).
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+padding=value</option></term>
	  <listitem>
	    <para>
	      Pad the size of the query packet using the EDNS Padding option
	      to blocks of <parameter>value</parameter> bytes. For example,
	      <option>+padding=32</option> would cause a 48-byte query to
	      be padded to 64 bytes.  The default block size is 0, which
	      disables padding. The maximum is 512. Values are
	      ordinarily expected to be powers of two, such as 128;
	      however, this is not mandatory.  Responses to
	      padded queries may also be padded, but only if the query
	      uses TCP or DNS COOKIE.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]qr</option></term>
	  <listitem>
	    <para>
	      Print [do not print] the query as it is sent.  By
	      default, the query is not printed.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]question</option></term>
	  <listitem>
	    <para>
	      Print [do not print] the question section of a query
	      when an answer is returned.  The default is to print
	      the question section as a comment.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]rdflag</option></term>
	  <listitem>
	    <para>
	      A synonym for <parameter>+[no]recurse</parameter>.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]recurse</option></term>
	  <listitem>
	    <para>
	      Toggle the setting of the RD (recursion desired) bit
	      in the query.  This bit is set by default, which means
	      <command>dig</command> normally sends recursive
	      queries.  Recursion is automatically disabled when
	      the <parameter>+nssearch</parameter> or
	      <parameter>+trace</parameter> query options are used.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+retry=T</option></term>
	  <listitem>
	    <para>
	      Sets the number of times to retry UDP queries to
	      server to <parameter>T</parameter> instead of the
	      default, 2.  Unlike <parameter>+tries</parameter>,
	      this does not include the initial query.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]rrcomments</option></term>
	  <listitem>
	    <para>
	      Toggle the display of per-record comments in the
	      output (for example, human-readable key information
	      about DNSKEY records).  The default is not to print
	      record comments unless multiline mode is active.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]search</option></term>
	  <listitem>
	    <para>
	      Use [do not use] the search list defined by the
	      searchlist or domain directive in
	      <filename>resolv.conf</filename> (if any).  The search
	      list is not used by default.
	    </para>
	    <para>
	      'ndots' from <filename>resolv.conf</filename> (default 1)
	       which may be overridden by <parameter>+ndots</parameter>
	      determines if the name will be treated as relative
	      or not and hence whether a search is eventually
	      performed or not.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]short</option></term>
	  <listitem>
	    <para>
	      Provide a terse answer.  The default is to print the
	      answer in a verbose form.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]showsearch</option></term>
	  <listitem>
	    <para>
	      Perform [do not perform] a search showing intermediate
	      results.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]sigchase</option></term>
	  <listitem>
	    <para>
	      Chase DNSSEC signature chains. Requires dig be compiled
	      with -DDIG_SIGCHASE. This feature is deprecated.
	      Use <command>delv</command> instead.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+split=W</option></term>
	  <listitem>
	    <para>
	      Split long hex- or base64-formatted fields in resource
	      records into chunks of <parameter>W</parameter>
	      characters (where <parameter>W</parameter> is rounded
	      up to the nearest multiple of 4).
	      <parameter>+nosplit</parameter> or
	      <parameter>+split=0</parameter> causes fields not to
	      be split at all.  The default is 56 characters, or
	      44 characters when multiline mode is active.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]stats</option></term>
	  <listitem>
	    <para>
	      This query option toggles the printing of statistics:
	      when the query was made, the size of the reply and
	      so on.  The default behavior is to print the query
	      statistics.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]subnet=addr[/prefix-length]</option></term>
	  <listitem>
	    <para>
	      Send (don't send) an EDNS CLIENT-SUBNET option with the
	      specified IP address or network prefix.
	    </para>
	    <para>
              <command>dig +subnet=0.0.0.0/0</command>, or simply
              <command>dig +subnet=0</command> for short, sends an EDNS
              CLIENT-SUBNET option with an empty address and a source
              prefix-length of zero, which signals to a resolver that
              the client's address information must
              <emphasis>not</emphasis> be used when resolving
              this query.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]tcp</option></term>
	  <listitem>
	    <para>
	      Use [do not use] TCP when querying name servers. The
	      default behavior is to use UDP unless a type
	      <literal>any</literal> or <literal>ixfr=N</literal>
	      query is requested, in which case the default is TCP.
	      AXFR queries always use TCP.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+timeout=T</option></term>
	  <listitem>
	    <para>

	      Sets the timeout for a query to
	      <parameter>T</parameter> seconds.  The default
	      timeout is 5 seconds.
	      An attempt to set <parameter>T</parameter> to less
	      than 1 will result
	      in a query timeout of 1 second being applied.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]topdown</option></term>
	  <listitem>
	    <para>
	      When chasing DNSSEC signature chains perform a top-down
	      validation.  Requires dig be compiled with -DDIG_SIGCHASE.
	      This feature is deprecated. Use <command>delv</command> instead.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]trace</option></term>
	  <listitem>
	    <para>
	      Toggle tracing of the delegation path from the root
	      name servers for the name being looked up.  Tracing
	      is disabled by default.  When tracing is enabled,
	      <command>dig</command> makes iterative queries to
	      resolve the name being looked up.  It will follow
	      referrals from the root servers, showing the answer
	      from each server that was used to resolve the lookup.
	    </para> <para>
	      If @server is also specified, it affects only the
	      initial query for the root zone name servers.
	    </para> <para>
	      <command>+dnssec</command> is also set when +trace
	      is set to better emulate the default queries from a
	      nameserver.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+tries=T</option></term>
	  <listitem>
	    <para>
	      Sets the number of times to try UDP queries to server
	      to <parameter>T</parameter> instead of the default,
	      3.  If <parameter>T</parameter> is less than or equal
	      to zero, the number of tries is silently rounded up
	      to 1.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+trusted-key=####</option></term>
	  <listitem>
	    <para>
	      Specifies a file containing trusted keys to be used
	      with <option>+sigchase</option>.  Each DNSKEY record
	      must be on its own line.
	    </para> <para>
	      If not specified, <command>dig</command> will look
	      for <filename>/etc/trusted-key.key</filename> then
	      <filename>trusted-key.key</filename> in the current
	      directory.
	    </para> <para>
	      Requires dig be compiled with -DDIG_SIGCHASE.
	      This feature is deprecated. Use <command>delv</command> instead.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]ttlid</option></term>
	  <listitem>
	    <para>
	      Display [do not display] the TTL when printing the
	      record.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]ttlunits</option></term>
	  <listitem>
	    <para>
	      Display [do not display] the TTL in friendly human-readable
	      time units of "s", "m", "h", "d", and "w", representing
	      seconds, minutes, hours, days and weeks.  Implies +ttlid.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]unknownformat</option></term>
	  <listitem>
	    <para>
	      Print all RDATA in unknown RR type presentation format
	      (RFC 3597). The default is to print RDATA for known types
	      in the type's presentation format.
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]vc</option></term>
	  <listitem>
	    <para>
	      Use [do not use] TCP when querying name servers.  This
	      alternate syntax to <parameter>+[no]tcp</parameter>
	      is provided for backwards compatibility.  The "vc"
	      stands for "virtual circuit".
	    </para>
	  </listitem>
	</varlistentry>

	<varlistentry>
	  <term><option>+[no]zflag</option></term>
	  <listitem>
	    <para>
	      Set [do not set] the last unassigned DNS header flag in a
	      DNS query.  This flag is off by default.
	    </para>
	  </listitem>
	</varlistentry>

      </variablelist>

    </para>
  </refsection>

  <refsection><info><title>MULTIPLE QUERIES</title></info>


    <para>
      The BIND 9 implementation of <command>dig </command>
      supports
      specifying multiple queries on the command line (in addition to
      supporting the <option>-f</option> batch file option).  Each of those
      queries can be supplied with its own set of flags, options and query
      options.
    </para>

    <para>
      In this case, each <parameter>query</parameter> argument
      represent an
      individual query in the command-line syntax described above.  Each
      consists of any of the standard options and flags, the name to be
      looked up, an optional query type and class and any query options that
      should be applied to that query.
    </para>

    <para>
      A global set of query options, which should be applied to all queries,
      can also be supplied.  These global query options must precede the
      first tuple of name, class, type, options, flags, and query options
      supplied on the command line.  Any global query options (except
      the <option>+[no]cmd</option> option) can be
      overridden by a query-specific set of query options.  For example:
      <programlisting>
dig +qr www.isc.org any -x 127.0.0.1 isc.org ns +noqr
</programlisting>
      shows how <command>dig</command> could be used from the
      command line
      to make three lookups: an ANY query for <literal>www.isc.org</literal>, a
      reverse lookup of 127.0.0.1 and a query for the NS records of
      <literal>isc.org</literal>.

      A global query option of <parameter>+qr</parameter> is
      applied, so
      that <command>dig</command> shows the initial query it made
      for each
      lookup.  The final query has a local query option of
      <parameter>+noqr</parameter> which means that <command>dig</command>
      will not print the initial query when it looks up the NS records for
      <literal>isc.org</literal>.
    </para>

  </refsection>

  <refsection><info><title>IDN SUPPORT</title></info>

    <para>
      If <command>dig</command> has been built with IDN (internationalized
      domain name) support, it can accept and display non-ASCII domain names.
      <command>dig</command> appropriately converts character encoding of
      domain name before sending a request to DNS server or displaying a
      reply from the server.
      If you'd like to turn off the IDN support for some reason, defines
      the <envar>IDN_DISABLE</envar> environment variable.
      The IDN support is disabled if the variable is set when
      <command>dig</command> runs.
    </para>
  </refsection>

  <refsection><info><title>FILES</title></info>

    <para><filename>/etc/resolv.conf</filename>
    </para>
    <para><filename>${HOME}/.digrc</filename>
    </para>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
	<refentrytitle>delv</refentrytitle><manvolnum>1</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>host</refentrytitle><manvolnum>1</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>named</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
	<refentrytitle>dnssec-keygen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>RFC1035</citetitle>.
    </para>
  </refsection>

  <refsection><info><title>BUGS</title></info>

    <para>
      There are probably too many query options.
    </para>
  </refsection>

</refentry>
