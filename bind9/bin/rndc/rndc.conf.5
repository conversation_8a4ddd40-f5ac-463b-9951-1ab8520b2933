.\" Copyright (C) 2000, 2001, 2004, 2005, 2007, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: rndc.conf
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2013-03-14
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "RNDC\&.CONF" "5" "2013\-03\-14" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
rndc.conf \- rndc configuration file
.SH "SYNOPSIS"
.HP \w'\fBrndc\&.conf\fR\ 'u
\fBrndc\&.conf\fR
.SH "DESCRIPTION"
.PP
rndc\&.conf
is the configuration file for
\fBrndc\fR, the BIND 9 name server control utility\&. This file has a similar structure and syntax to
named\&.conf\&. Statements are enclosed in braces and terminated with a semi\-colon\&. Clauses in the statements are also semi\-colon terminated\&. The usual comment styles are supported:
.PP
C style: /* */
.PP
C++ style: // to end of line
.PP
Unix style: # to end of line
.PP
rndc\&.conf
is much simpler than
named\&.conf\&. The file uses three statements: an options statement, a server statement and a key statement\&.
.PP
The
\fBoptions\fR
statement contains five clauses\&. The
\fBdefault\-server\fR
clause is followed by the name or address of a name server\&. This host will be used when no name server is given as an argument to
\fBrndc\fR\&. The
\fBdefault\-key\fR
clause is followed by the name of a key which is identified by a
\fBkey\fR
statement\&. If no
\fBkeyid\fR
is provided on the rndc command line, and no
\fBkey\fR
clause is found in a matching
\fBserver\fR
statement, this default key will be used to authenticate the server\*(Aqs commands and responses\&. The
\fBdefault\-port\fR
clause is followed by the port to connect to on the remote name server\&. If no
\fBport\fR
option is provided on the rndc command line, and no
\fBport\fR
clause is found in a matching
\fBserver\fR
statement, this default port will be used to connect\&. The
\fBdefault\-source\-address\fR
and
\fBdefault\-source\-address\-v6\fR
clauses which can be used to set the IPv4 and IPv6 source addresses respectively\&.
.PP
After the
\fBserver\fR
keyword, the server statement includes a string which is the hostname or address for a name server\&. The statement has three possible clauses:
\fBkey\fR,
\fBport\fR
and
\fBaddresses\fR\&. The key name must match the name of a key statement in the file\&. The port number specifies the port to connect to\&. If an
\fBaddresses\fR
clause is supplied these addresses will be used instead of the server name\&. Each address can take an optional port\&. If an
\fBsource\-address\fR
or
\fBsource\-address\-v6\fR
of supplied then these will be used to specify the IPv4 and IPv6 source addresses respectively\&.
.PP
The
\fBkey\fR
statement begins with an identifying string, the name of the key\&. The statement has two clauses\&.
\fBalgorithm\fR
identifies the authentication algorithm for
\fBrndc\fR
to use; currently only HMAC\-MD5 (for compatibility), HMAC\-SHA1, HMAC\-SHA224, HMAC\-SHA256 (default), HMAC\-SHA384 and HMAC\-SHA512 are supported\&. This is followed by a secret clause which contains the base\-64 encoding of the algorithm\*(Aqs authentication key\&. The base\-64 string is enclosed in double quotes\&.
.PP
There are two common ways to generate the base\-64 string for the secret\&. The BIND 9 program
\fBrndc\-confgen\fR
can be used to generate a random key, or the
\fBmmencode\fR
program, also known as
\fBmimencode\fR, can be used to generate a base\-64 string from known input\&.
\fBmmencode\fR
does not ship with BIND 9 but is available on many systems\&. See the EXAMPLE section for sample command lines for each\&.
.SH "EXAMPLE"
.PP
.if n \{\
.RS 4
.\}
.nf
      options {
        default\-server  localhost;
        default\-key     samplekey;
      };
.fi
.if n \{\
.RE
.\}
.PP
.if n \{\
.RS 4
.\}
.nf
      server localhost {
        key             samplekey;
      };
.fi
.if n \{\
.RE
.\}
.PP
.if n \{\
.RS 4
.\}
.nf
      server testserver {
        key		testkey;
        addresses	{ localhost port 5353; };
      };
.fi
.if n \{\
.RE
.\}
.PP
.if n \{\
.RS 4
.\}
.nf
      key samplekey {
        algorithm       hmac\-sha256;
        secret          "6FMfj43Osz4lyb24OIe2iGEz9lf1llJO+lz";
      };
.fi
.if n \{\
.RE
.\}
.PP
.if n \{\
.RS 4
.\}
.nf
      key testkey {
        algorithm	hmac\-sha256;
        secret		"R3HI8P6BKw9ZwXwN3VZKuQ==";
      };
.fi
.if n \{\
.RE
.\}
.PP
In the above example,
\fBrndc\fR
will by default use the server at localhost (127\&.0\&.0\&.1) and the key called samplekey\&. Commands to the localhost server will use the samplekey key, which must also be defined in the server\*(Aqs configuration file with the same name and secret\&. The key statement indicates that samplekey uses the HMAC\-SHA256 algorithm and its secret clause contains the base\-64 encoding of the HMAC\-SHA256 secret enclosed in double quotes\&.
.PP
If
\fBrndc \-s testserver\fR
is used then
\fBrndc\fR
will connect to server on localhost port 5353 using the key testkey\&.
.PP
To generate a random secret with
\fBrndc\-confgen\fR:
.PP
\fBrndc\-confgen\fR
.PP
A complete
rndc\&.conf
file, including the randomly generated key, will be written to the standard output\&. Commented\-out
\fBkey\fR
and
\fBcontrols\fR
statements for
named\&.conf
are also printed\&.
.PP
To generate a base\-64 secret with
\fBmmencode\fR:
.PP
\fBecho "known plaintext for a secret" | mmencode\fR
.SH "NAME SERVER CONFIGURATION"
.PP
The name server must be configured to accept rndc connections and to recognize the key specified in the
rndc\&.conf
file, using the controls statement in
named\&.conf\&. See the sections on the
\fBcontrols\fR
statement in the BIND 9 Administrator Reference Manual for details\&.
.SH "SEE ALSO"
.PP
\fBrndc\fR(8),
\fBrndc-confgen\fR(8),
\fBmmencode\fR(1),
BIND 9 Administrator Reference Manual\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2000, 2001, 2004, 2005, 2007, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
