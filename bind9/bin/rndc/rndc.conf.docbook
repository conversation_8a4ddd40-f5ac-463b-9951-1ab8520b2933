<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.rndc.conf">
  <info>
    <date>2013-03-14</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><filename>rndc.conf</filename></refentrytitle>
    <manvolnum>5</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><filename>rndc.conf</filename></refname>
    <refpurpose>rndc configuration file</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2000</year>
      <year>2001</year>
      <year>2004</year>
      <year>2005</year>
      <year>2007</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>rndc.conf</command>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><filename>rndc.conf</filename> is the configuration file
      for <command>rndc</command>, the BIND 9 name server control
      utility.  This file has a similar structure and syntax to
      <filename>named.conf</filename>.  Statements are enclosed
      in braces and terminated with a semi-colon.  Clauses in
      the statements are also semi-colon terminated.  The usual
      comment styles are supported:
    </para>
    <para>
      C style: /* */
    </para>
    <para>
      C++ style: // to end of line
    </para>
    <para>
      Unix style: # to end of line
    </para>
    <para><filename>rndc.conf</filename> is much simpler than
      <filename>named.conf</filename>.  The file uses three
      statements: an options statement, a server statement
      and a key statement.
    </para>
    <para>
      The <option>options</option> statement contains five clauses.
      The <option>default-server</option> clause is followed by the
      name or address of a name server.  This host will be used when
      no name server is given as an argument to
      <command>rndc</command>.  The <option>default-key</option>
      clause is followed by the name of a key which is identified by
      a <option>key</option> statement.  If no
      <option>keyid</option> is provided on the rndc command line,
      and no <option>key</option> clause is found in a matching
      <option>server</option> statement, this default key will be
      used to authenticate the server's commands and responses.  The
      <option>default-port</option> clause is followed by the port
      to connect to on the remote name server.  If no
      <option>port</option> option is provided on the rndc command
      line, and no <option>port</option> clause is found in a
      matching <option>server</option> statement, this default port
      will be used to connect.
      The <option>default-source-address</option> and
      <option>default-source-address-v6</option> clauses which
      can be used to set the IPv4 and IPv6 source addresses
      respectively.
    </para>
    <para>
      After the <option>server</option> keyword, the server
      statement includes a string which is the hostname or address
      for a name server.  The statement has three possible clauses:
      <option>key</option>, <option>port</option> and
      <option>addresses</option>. The key name must match the
      name of a key statement in the file.  The port number
      specifies the port to connect to.  If an <option>addresses</option>
      clause is supplied these addresses will be used instead of
      the server name.  Each address can take an optional port.
      If an <option>source-address</option> or <option>source-address-v6</option>
      of supplied then these will be used to specify the IPv4 and IPv6
      source addresses respectively.
    </para>
    <para>
      The <option>key</option> statement begins with an identifying
      string, the name of the key.  The statement has two clauses.
      <option>algorithm</option> identifies the authentication algorithm
      for <command>rndc</command> to use; currently only HMAC-MD5
      (for compatibility), HMAC-SHA1, HMAC-SHA224, HMAC-SHA256
      (default), HMAC-SHA384 and HMAC-SHA512 are
      supported.  This is followed by a secret clause which contains
      the base-64 encoding of the algorithm's authentication key.  The
      base-64 string is enclosed in double quotes.
    </para>
    <para>
      There are two common ways to generate the base-64 string for the
      secret.  The BIND 9 program <command>rndc-confgen</command>
      can
      be used to generate a random key, or the
      <command>mmencode</command> program, also known as
      <command>mimencode</command>, can be used to generate a
      base-64
      string from known input.  <command>mmencode</command> does
      not
      ship with BIND 9 but is available on many systems.  See the
      EXAMPLE section for sample command lines for each.
    </para>
  </refsection>

  <refsection><info><title>EXAMPLE</title></info>


    <para><programlisting>
      options {
        default-server  localhost;
        default-key     samplekey;
      };
</programlisting>
    </para>
    <para><programlisting>
      server localhost {
        key             samplekey;
      };
</programlisting>
    </para>
    <para><programlisting>
      server testserver {
        key		testkey;
        addresses	{ localhost port 5353; };
      };
</programlisting>
    </para>
    <para><programlisting>
      key samplekey {
        algorithm       hmac-sha256;
        secret          "6FMfj43Osz4lyb24OIe2iGEz9lf1llJO+lz";
      };
</programlisting>
    </para>
    <para><programlisting>
      key testkey {
        algorithm	hmac-sha256;
        secret		"R3HI8P6BKw9ZwXwN3VZKuQ==";
      };
    </programlisting>
    </para>

    <para>
      In the above example, <command>rndc</command> will by
      default use
      the server at localhost (127.0.0.1) and the key called samplekey.
      Commands to the localhost server will use the samplekey key, which
      must also be defined in the server's configuration file with the
      same name and secret.  The key statement indicates that samplekey
      uses the HMAC-SHA256 algorithm and its secret clause contains the
      base-64 encoding of the HMAC-SHA256 secret enclosed in double quotes.
    </para>
    <para>
      If <command>rndc -s testserver</command> is used then <command>rndc</command> will
      connect to server on localhost port 5353 using the key testkey.
    </para>
    <para>
      To generate a random secret with <command>rndc-confgen</command>:
    </para>
    <para><userinput>rndc-confgen</userinput>
    </para>
    <para>
      A complete <filename>rndc.conf</filename> file, including
      the
      randomly generated key, will be written to the standard
      output.  Commented-out <option>key</option> and
      <option>controls</option> statements for
      <filename>named.conf</filename> are also printed.
    </para>
    <para>
      To generate a base-64 secret with <command>mmencode</command>:
    </para>
    <para><userinput>echo "known plaintext for a secret" | mmencode</userinput>
    </para>
  </refsection>

  <refsection><info><title>NAME SERVER CONFIGURATION</title></info>

    <para>
      The name server must be configured to accept rndc connections and
      to recognize the key specified in the <filename>rndc.conf</filename>
      file, using the controls statement in <filename>named.conf</filename>.
      See the sections on the <option>controls</option> statement in the
      BIND 9 Administrator Reference Manual for details.
    </para>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
        <refentrytitle>rndc</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>rndc-confgen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>mmencode</refentrytitle><manvolnum>1</manvolnum>
      </citerefentry>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>.
    </para>
  </refsection>

</refentry>
