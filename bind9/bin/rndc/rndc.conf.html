<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000, 2001, 2004, 2005, 2007, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>rndc.conf</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.rndc.conf"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <code class="filename">rndc.conf</code>
     &#8212; rndc configuration file
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">rndc.conf</code> 
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><code class="filename">rndc.conf</code> is the configuration file
      for <span class="command"><strong>rndc</strong></span>, the BIND 9 name server control
      utility.  This file has a similar structure and syntax to
      <code class="filename">named.conf</code>.  Statements are enclosed
      in braces and terminated with a semi-colon.  Clauses in
      the statements are also semi-colon terminated.  The usual
      comment styles are supported:
    </p>
    <p>
      C style: /* */
    </p>
    <p>
      C++ style: // to end of line
    </p>
    <p>
      Unix style: # to end of line
    </p>
    <p><code class="filename">rndc.conf</code> is much simpler than
      <code class="filename">named.conf</code>.  The file uses three
      statements: an options statement, a server statement
      and a key statement.
    </p>
    <p>
      The <code class="option">options</code> statement contains five clauses.
      The <code class="option">default-server</code> clause is followed by the
      name or address of a name server.  This host will be used when
      no name server is given as an argument to
      <span class="command"><strong>rndc</strong></span>.  The <code class="option">default-key</code>
      clause is followed by the name of a key which is identified by
      a <code class="option">key</code> statement.  If no
      <code class="option">keyid</code> is provided on the rndc command line,
      and no <code class="option">key</code> clause is found in a matching
      <code class="option">server</code> statement, this default key will be
      used to authenticate the server's commands and responses.  The
      <code class="option">default-port</code> clause is followed by the port
      to connect to on the remote name server.  If no
      <code class="option">port</code> option is provided on the rndc command
      line, and no <code class="option">port</code> clause is found in a
      matching <code class="option">server</code> statement, this default port
      will be used to connect.
      The <code class="option">default-source-address</code> and
      <code class="option">default-source-address-v6</code> clauses which
      can be used to set the IPv4 and IPv6 source addresses
      respectively.
    </p>
    <p>
      After the <code class="option">server</code> keyword, the server
      statement includes a string which is the hostname or address
      for a name server.  The statement has three possible clauses:
      <code class="option">key</code>, <code class="option">port</code> and
      <code class="option">addresses</code>. The key name must match the
      name of a key statement in the file.  The port number
      specifies the port to connect to.  If an <code class="option">addresses</code>
      clause is supplied these addresses will be used instead of
      the server name.  Each address can take an optional port.
      If an <code class="option">source-address</code> or <code class="option">source-address-v6</code>
      of supplied then these will be used to specify the IPv4 and IPv6
      source addresses respectively.
    </p>
    <p>
      The <code class="option">key</code> statement begins with an identifying
      string, the name of the key.  The statement has two clauses.
      <code class="option">algorithm</code> identifies the authentication algorithm
      for <span class="command"><strong>rndc</strong></span> to use; currently only HMAC-MD5
      (for compatibility), HMAC-SHA1, HMAC-SHA224, HMAC-SHA256
      (default), HMAC-SHA384 and HMAC-SHA512 are
      supported.  This is followed by a secret clause which contains
      the base-64 encoding of the algorithm's authentication key.  The
      base-64 string is enclosed in double quotes.
    </p>
    <p>
      There are two common ways to generate the base-64 string for the
      secret.  The BIND 9 program <span class="command"><strong>rndc-confgen</strong></span>
      can
      be used to generate a random key, or the
      <span class="command"><strong>mmencode</strong></span> program, also known as
      <span class="command"><strong>mimencode</strong></span>, can be used to generate a
      base-64
      string from known input.  <span class="command"><strong>mmencode</strong></span> does
      not
      ship with BIND 9 but is available on many systems.  See the
      EXAMPLE section for sample command lines for each.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>EXAMPLE</h2>


    <pre class="programlisting">
      options {
        default-server  localhost;
        default-key     samplekey;
      };
</pre>
<p>
    </p>
    <pre class="programlisting">
      server localhost {
        key             samplekey;
      };
</pre>
<p>
    </p>
    <pre class="programlisting">
      server testserver {
        key		testkey;
        addresses	{ localhost port 5353; };
      };
</pre>
<p>
    </p>
    <pre class="programlisting">
      key samplekey {
        algorithm       hmac-sha256;
        secret          "6FMfj43Osz4lyb24OIe2iGEz9lf1llJO+lz";
      };
</pre>
<p>
    </p>
    <pre class="programlisting">
      key testkey {
        algorithm	hmac-sha256;
        secret		"R3HI8P6BKw9ZwXwN3VZKuQ==";
      };
    </pre>
<p>
    </p>

    <p>
      In the above example, <span class="command"><strong>rndc</strong></span> will by
      default use
      the server at localhost (127.0.0.1) and the key called samplekey.
      Commands to the localhost server will use the samplekey key, which
      must also be defined in the server's configuration file with the
      same name and secret.  The key statement indicates that samplekey
      uses the HMAC-SHA256 algorithm and its secret clause contains the
      base-64 encoding of the HMAC-SHA256 secret enclosed in double quotes.
    </p>
    <p>
      If <span class="command"><strong>rndc -s testserver</strong></span> is used then <span class="command"><strong>rndc</strong></span> will
      connect to server on localhost port 5353 using the key testkey.
    </p>
    <p>
      To generate a random secret with <span class="command"><strong>rndc-confgen</strong></span>:
    </p>
    <p><strong class="userinput"><code>rndc-confgen</code></strong>
    </p>
    <p>
      A complete <code class="filename">rndc.conf</code> file, including
      the
      randomly generated key, will be written to the standard
      output.  Commented-out <code class="option">key</code> and
      <code class="option">controls</code> statements for
      <code class="filename">named.conf</code> are also printed.
    </p>
    <p>
      To generate a base-64 secret with <span class="command"><strong>mmencode</strong></span>:
    </p>
    <p><strong class="userinput"><code>echo "known plaintext for a secret" | mmencode</code></strong>
    </p>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>NAME SERVER CONFIGURATION</h2>

    <p>
      The name server must be configured to accept rndc connections and
      to recognize the key specified in the <code class="filename">rndc.conf</code>
      file, using the controls statement in <code class="filename">named.conf</code>.
      See the sections on the <code class="option">controls</code> statement in the
      BIND 9 Administrator Reference Manual for details.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
        <span class="refentrytitle">rndc</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">rndc-confgen</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">mmencode</span>(1)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>.
    </p>
  </div>

</div></body>
</html>
