.\" Copyright (C) 2000, 2001, 2004, 2005, 2007, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: rndc
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-08-15
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "RNDC" "8" "2014\-08\-15" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
rndc \- name server control utility
.SH "SYNOPSIS"
.HP \w'\fBrndc\fR\ 'u
\fBrndc\fR [\fB\-b\ \fR\fB\fIsource\-address\fR\fR] [\fB\-c\ \fR\fB\fIconfig\-file\fR\fR] [\fB\-k\ \fR\fB\fIkey\-file\fR\fR] [\fB\-s\ \fR\fB\fIserver\fR\fR] [\fB\-p\ \fR\fB\fIport\fR\fR] [\fB\-q\fR] [\fB\-r\fR] [\fB\-V\fR] [\fB\-y\ \fR\fB\fIkey_id\fR\fR] {command}
.SH "DESCRIPTION"
.PP
\fBrndc\fR
controls the operation of a name server\&. It supersedes the
\fBndc\fR
utility that was provided in old BIND releases\&. If
\fBrndc\fR
is invoked with no command line options or arguments, it prints a short summary of the supported commands and the available options and their arguments\&.
.PP
\fBrndc\fR
communicates with the name server over a TCP connection, sending commands authenticated with digital signatures\&. In the current versions of
\fBrndc\fR
and
\fBnamed\fR, the only supported authentication algorithms are HMAC\-MD5 (for compatibility), HMAC\-SHA1, HMAC\-SHA224, HMAC\-SHA256 (default), HMAC\-SHA384 and HMAC\-SHA512\&. They use a shared secret on each end of the connection\&. This provides TSIG\-style authentication for the command request and the name server\*(Aqs response\&. All commands sent over the channel must be signed by a key_id known to the server\&.
.PP
\fBrndc\fR
reads a configuration file to determine how to contact the name server and decide what algorithm and key it should use\&.
.SH "OPTIONS"
.PP
\-b \fIsource\-address\fR
.RS 4
Use
\fIsource\-address\fR
as the source address for the connection to the server\&. Multiple instances are permitted to allow setting of both the IPv4 and IPv6 source addresses\&.
.RE
.PP
\-c \fIconfig\-file\fR
.RS 4
Use
\fIconfig\-file\fR
as the configuration file instead of the default,
/etc/rndc\&.conf\&.
.RE
.PP
\-k \fIkey\-file\fR
.RS 4
Use
\fIkey\-file\fR
as the key file instead of the default,
/etc/rndc\&.key\&. The key in
/etc/rndc\&.key
will be used to authenticate commands sent to the server if the
\fIconfig\-file\fR
does not exist\&.
.RE
.PP
\-s \fIserver\fR
.RS 4
\fIserver\fR
is the name or address of the server which matches a server statement in the configuration file for
\fBrndc\fR\&. If no server is supplied on the command line, the host named by the default\-server clause in the options statement of the
\fBrndc\fR
configuration file will be used\&.
.RE
.PP
\-p \fIport\fR
.RS 4
Send commands to TCP port
\fIport\fR
instead of BIND 9\*(Aqs default control channel port, 953\&.
.RE
.PP
\-q
.RS 4
Quiet mode: Message text returned by the server will not be printed except when there is an error\&.
.RE
.PP
\-r
.RS 4
Instructs
\fBrndc\fR
to print the result code returned by
\fBnamed\fR
after executing the requested command (e\&.g\&., ISC_R_SUCCESS, ISC_R_FAILURE, etc)\&.
.RE
.PP
\-V
.RS 4
Enable verbose logging\&.
.RE
.PP
\-y \fIkey_id\fR
.RS 4
Use the key
\fIkey_id\fR
from the configuration file\&.
\fIkey_id\fR
must be known by
\fBnamed\fR
with the same algorithm and secret string in order for control message validation to succeed\&. If no
\fIkey_id\fR
is specified,
\fBrndc\fR
will first look for a key clause in the server statement of the server being used, or if no server statement is present for that host, then the default\-key clause of the options statement\&. Note that the configuration file contains shared secrets which are used to send authenticated control commands to name servers\&. It should therefore not have general read or write access\&.
.RE
.SH "COMMANDS"
.PP
A list of commands supported by
\fBrndc\fR
can be seen by running
\fBrndc\fR
without arguments\&.
.PP
Currently supported commands are:
.PP
\fBaddzone \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR\fB \fR\fB\fIconfiguration\fR\fR\fB \fR
.RS 4
Add a zone while the server is running\&. This command requires the
\fBallow\-new\-zones\fR
option to be set to
\fByes\fR\&. The
\fIconfiguration\fR
string specified on the command line is the zone configuration text that would ordinarily be placed in
named\&.conf\&.
.sp
The configuration is saved in a file called
\fIname\fR\&.nzf, where
\fIname\fR
is the name of the view, or if it contains characters that are incompatible with use as a file name, a cryptographic hash generated from the name of the view\&. When
\fBnamed\fR
is restarted, the file will be loaded into the view configuration, so that zones that were added can persist after a restart\&.
.sp
This sample
\fBaddzone\fR
command would add the zone
example\&.com
to the default view:
.sp
$\fBrndc addzone example\&.com \*(Aq{ type master; file "example\&.com\&.db"; };\*(Aq\fR
.sp
(Note the brackets and semi\-colon around the zone configuration text\&.)
.sp
See also
\fBrndc delzone\fR
and
\fBrndc modzone\fR\&.
.RE
.PP
\fBdelzone \fR\fB[\-clean]\fR\fB \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR\fB \fR
.RS 4
Delete a zone while the server is running\&.
.sp
If the
\fB\-clean\fR
argument is specified, the zone\*(Aqs master file (and journal file, if any) will be deleted along with the zone\&. Without the
\fB\-clean\fR
option, zone files must be cleaned up by hand\&. (If the zone is of type "slave" or "stub", the files needing to be cleaned up will be reported in the output of the
\fBrndc delzone\fR
command\&.)
.sp
If the zone was originally added via
\fBrndc addzone\fR, then it will be removed permanently\&. However, if it was originally configured in
named\&.conf, then that original configuration is still in place; when the server is restarted or reconfigured, the zone will come back\&. To remove it permanently, it must also be removed from
named\&.conf
.sp
See also
\fBrndc addzone\fR
and
\fBrndc modzone\fR\&.
.RE
.PP
\fBdnstap ( \-reopen | \-roll \fR\fB[\fInumber\fR]\fR\fB )\fR
.RS 4
Close and re\-open DNSTAP output files\&.
\fBrndc dnstap \-reopen\fR
allows the output file to be renamed externally, so that
\fBnamed\fR
can truncate and re\-open it\&.
\fBrndc dnstap \-roll\fR
causes the output file to be rolled automatically, similar to log files; the most recent output file has "\&.0" appended to its name; the previous most recent output file is moved to "\&.1", and so on\&. If
\fInumber\fR
is specified, then the number of backup log files is limited to that number\&.
.RE
.PP
\fBdumpdb \fR\fB[\-all|\-cache|\-ecscache|\-zone|\-adb|\-bad|\-fail]\fR\fB \fR\fB[\fIview \&.\&.\&.\fR]\fR
.RS 4
Dump the server\*(Aqs caches (default) and/or zones to the dump file for the specified views\&. If no view is specified, all views are dumped\&. (See the
\fBdump\-file\fR
option in the BIND 9 Administrator Reference Manual\&.)
.RE
.PP
\fBflush\fR
.RS 4
Flushes the server\*(Aqs cache\&.
.RE
.PP
\fBflushname\fR \fIname\fR [\fIview\fR]
.RS 4
Flushes the given name from the view\*(Aqs DNS cache and, if applicable, from the view\*(Aqs nameserver address database, bad server cache and SERVFAIL cache\&.
.RE
.PP
\fBflushtree\fR \fIname\fR [\fIview\fR]
.RS 4
Flushes the given name, and all of its subdomains, from the view\*(Aqs DNS cache, address database, bad server cache, and SERVFAIL cache\&.
.RE
.PP
\fBfreeze \fR\fB[\fIzone\fR [\fIclass\fR [\fIview\fR]]]\fR
.RS 4
Suspend updates to a dynamic zone\&. If no zone is specified, then all zones are suspended\&. This allows manual edits to be made to a zone normally updated by dynamic update\&. It also causes changes in the journal file to be synced into the master file\&. All dynamic update attempts will be refused while the zone is frozen\&.
.sp
See also
\fBrndc thaw\fR\&.
.RE
.PP
\fBhalt \fR\fB[\-p]\fR
.RS 4
Stop the server immediately\&. Recent changes made through dynamic update or IXFR are not saved to the master files, but will be rolled forward from the journal files when the server is restarted\&. If
\fB\-p\fR
is specified
\fBnamed\fR\*(Aqs process id is returned\&. This allows an external process to determine when
\fBnamed\fR
had completed halting\&.
.sp
See also
\fBrndc stop\fR\&.
.RE
.PP
\fBloadkeys \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR
.RS 4
Fetch all DNSSEC keys for the given zone from the key directory\&. If they are within their publication period, merge them into the zone\*(Aqs DNSKEY RRset\&. Unlike
\fBrndc sign\fR, however, the zone is not immediately re\-signed by the new keys, but is allowed to incrementally re\-sign over time\&.
.sp
This command requires that the
\fBauto\-dnssec\fR
zone option be set to
maintain, and also requires the zone to be configured to allow dynamic DNS\&. (See "Dynamic Update Policies" in the Administrator Reference Manual for more details\&.)
.RE
.PP
\fBmanaged\-keys \fR\fB\fI(status | refresh | sync)\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR
.RS 4
When run with the "status" keyword, print the current status of the managed\-keys database for the specified view, or for all views if none is specified\&. When run with the "refresh" keyword, force an immediate refresh of all the managed\-keys in the specified view, or all views\&. When run with the "sync" keyword, force an immediate dump of the managed\-keys database to disk (in the file
managed\-keys\&.bind
or (\fIviewname\fR\&.mkeys)\&.
.RE
.PP
\fBmodzone \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR\fB \fR\fB\fIconfiguration\fR\fR\fB \fR
.RS 4
Modify the configuration of a zone while the server is running\&. This command requires the
\fBallow\-new\-zones\fR
option to be set to
\fByes\fR\&. As with
\fBaddzone\fR, the
\fIconfiguration\fR
string specified on the command line is the zone configuration text that would ordinarily be placed in
named\&.conf\&.
.sp
If the zone was originally added via
\fBrndc addzone\fR, the configuration changes will be recorded permanently and will still be in effect after the server is restarted or reconfigured\&. However, if it was originally configured in
named\&.conf, then that original configuration is still in place; when the server is restarted or reconfigured, the zone will revert to its original configuration\&. To make the changes permanent, it must also be modified in
named\&.conf
.sp
See also
\fBrndc addzone\fR
and
\fBrndc delzone\fR\&.
.RE
.PP
\fBnotify \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR
.RS 4
Resend NOTIFY messages for the zone\&.
.RE
.PP
\fBnotrace\fR
.RS 4
Sets the server\*(Aqs debugging level to 0\&.
.sp
See also
\fBrndc trace\fR\&.
.RE
.PP
\fBnta \fR\fB[( \-d | \-f | \-r | \-l \fIduration\fR)]\fR\fB \fR\fB\fIdomain\fR\fR\fB \fR\fB[\fIview\fR]\fR\fB \fR
.RS 4
Sets a DNSSEC negative trust anchor (NTA) for
\fBdomain\fR, with a lifetime of
\fBduration\fR\&. The default lifetime is configured in
named\&.conf
via the
\fBnta\-lifetime\fR
option, and defaults to one hour\&. The lifetime cannot exceed one week\&.
.sp
A negative trust anchor selectively disables DNSSEC validation for zones that are known to be failing because of misconfiguration rather than an attack\&. When data to be validated is at or below an active NTA (and above any other configured trust anchors),
\fBnamed\fR
will abort the DNSSEC validation process and treat the data as insecure rather than bogus\&. This continues until the NTA\*(Aqs lifetime is elapsed\&.
.sp
NTAs persist across restarts of the
\fBnamed\fR
server\&. The NTAs for a view are saved in a file called
\fIname\fR\&.nta, where
\fIname\fR
is the name of the view, or if it contains characters that are incompatible with use as a file name, a cryptographic hash generated from the name of the view\&.
.sp
An existing NTA can be removed by using the
\fB\-remove\fR
option\&.
.sp
An NTA\*(Aqs lifetime can be specified with the
\fB\-lifetime\fR
option\&. TTL\-style suffixes can be used to specify the lifetime in seconds, minutes, or hours\&. If the specified NTA already exists, its lifetime will be updated to the new value\&. Setting
\fBlifetime\fR
to zero is equivalent to
\fB\-remove\fR\&.
.sp
If
\fB\-dump\fR
is used, any other arguments are ignored, and a list of existing NTAs is printed (note that this may include NTAs that are expired but have not yet been cleaned up)\&.
.sp
Normally,
\fBnamed\fR
will periodically test to see whether data below an NTA can now be validated (see the
\fBnta\-recheck\fR
option in the Administrator Reference Manual for details)\&. If data can be validated, then the NTA is regarded as no longer necessary, and will be allowed to expire early\&. The
\fB\-force\fR
overrides this behavior and forces an NTA to persist for its entire lifetime, regardless of whether data could be validated if the NTA were not present\&.
.sp
All of these options can be shortened, i\&.e\&., to
\fB\-l\fR,
\fB\-r\fR,
\fB\-d\fR, and
\fB\-f\fR\&.
.RE
.PP
\fBquerylog\fR [ on | off ]
.RS 4
Enable or disable query logging\&. (For backward compatibility, this command can also be used without an argument to toggle query logging on and off\&.)
.sp
Query logging can also be enabled by explicitly directing the
\fBqueries\fR\fBcategory\fR
to a
\fBchannel\fR
in the
\fBlogging\fR
section of
named\&.conf
or by specifying
\fBquerylog yes;\fR
in the
\fBoptions\fR
section of
named\&.conf\&.
.RE
.PP
\fBreconfig\fR
.RS 4
Reload the configuration file and load new zones, but do not reload existing zone files even if they have changed\&. This is faster than a full
\fBreload\fR
when there is a large number of zones because it avoids the need to examine the modification times of the zones files\&.
.RE
.PP
\fBrecursing\fR
.RS 4
Dump the list of queries
\fBnamed\fR
is currently recursing on, and the list of domains to which iterative queries are currently being sent\&. (The second list includes the number of fetches currently active for the given domain, and how many have been passed or dropped because of the
\fBfetches\-per\-zone\fR
option\&.)
.RE
.PP
\fBrefresh \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR
.RS 4
Schedule zone maintenance for the given zone\&.
.RE
.PP
\fBreload\fR
.RS 4
Reload configuration file and zones\&.
.RE
.PP
\fBreload \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR
.RS 4
Reload the given zone\&.
.RE
.PP
\fBretransfer \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR
.RS 4
Retransfer the given slave zone from the master server\&.
.sp
If the zone is configured to use
\fBinline\-signing\fR, the signed version of the zone is discarded; after the retransfer of the unsigned version is complete, the signed version will be regenerated with all new signatures\&.
.RE
.PP
\fBscan\fR
.RS 4
Scan the list of available network interfaces for changes, without performing a full
\fBreconfig\fR
or waiting for the
\fBinterface\-interval\fR
timer\&.
.RE
.PP
\fBsecroots \fR\fB[\-]\fR\fB \fR\fB[\fIview \&.\&.\&.\fR]\fR
.RS 4
Dump the server\*(Aqs security roots and negative trust anchors for the specified views\&. If no view is specified, all views are dumped\&.
.sp
If the first argument is "\-", then the output is returned via the
\fBrndc\fR
response channel and printed to the standard output\&. Otherwise, it is written to the secroots dump file, which defaults to
named\&.secroots, but can be overridden via the
\fBsecroots\-file\fR
option in
named\&.conf\&.
.sp
See also
\fBrndc managed\-keys\fR\&.
.RE
.PP
\fBshowzone \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR\fB \fR
.RS 4
Print the configuration of a running zone\&.
.sp
See also
\fBrndc zonestatus\fR\&.
.RE
.PP
\fBsign \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR
.RS 4
Fetch all DNSSEC keys for the given zone from the key directory (see the
\fBkey\-directory\fR
option in the BIND 9 Administrator Reference Manual)\&. If they are within their publication period, merge them into the zone\*(Aqs DNSKEY RRset\&. If the DNSKEY RRset is changed, then the zone is automatically re\-signed with the new key set\&.
.sp
This command requires that the
\fBauto\-dnssec\fR
zone option be set to
allow
or
maintain, and also requires the zone to be configured to allow dynamic DNS\&. (See "Dynamic Update Policies" in the Administrator Reference Manual for more details\&.)
.sp
See also
\fBrndc loadkeys\fR\&.
.RE
.PP
\fBsigning \fR\fB[( \-list | \-clear \fIkeyid/algorithm\fR | \-clear all | \-nsec3param ( \fIparameters\fR | none ) | \-serial \fIvalue\fR ) ]\fR\fB \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR\fB \fR
.RS 4
List, edit, or remove the DNSSEC signing state records for the specified zone\&. The status of ongoing DNSSEC operations (such as signing or generating NSEC3 chains) is stored in the zone in the form of DNS resource records of type
\fBsig\-signing\-type\fR\&.
\fBrndc signing \-list\fR
converts these records into a human\-readable form, indicating which keys are currently signing or have finished signing the zone, and which NSEC3 chains are being created or removed\&.
.sp
\fBrndc signing \-clear\fR
can remove a single key (specified in the same format that
\fBrndc signing \-list\fR
uses to display it), or all keys\&. In either case, only completed keys are removed; any record indicating that a key has not yet finished signing the zone will be retained\&.
.sp
\fBrndc signing \-nsec3param\fR
sets the NSEC3 parameters for a zone\&. This is the only supported mechanism for using NSEC3 with
\fBinline\-signing\fR
zones\&. Parameters are specified in the same format as an NSEC3PARAM resource record: hash algorithm, flags, iterations, and salt, in that order\&.
.sp
Currently, the only defined value for hash algorithm is
1, representing SHA\-1\&. The
\fBflags\fR
may be set to
0
or
1, depending on whether you wish to set the opt\-out bit in the NSEC3 chain\&.
\fBiterations\fR
defines the number of additional times to apply the algorithm when generating an NSEC3 hash\&. The
\fBsalt\fR
is a string of data expressed in hexadecimal, a hyphen (`\-\*(Aq) if no salt is to be used, or the keyword
auto, which causes
\fBnamed\fR
to generate a random 64\-bit salt\&.
.sp
So, for example, to create an NSEC3 chain using the SHA\-1 hash algorithm, no opt\-out flag, 10 iterations, and a salt value of "FFFF", use:
\fBrndc signing \-nsec3param 1 0 10 FFFF \fR\fB\fIzone\fR\fR\&. To set the opt\-out flag, 15 iterations, and no salt, use:
\fBrndc signing \-nsec3param 1 1 15 \- \fR\fB\fIzone\fR\fR\&.
.sp
\fBrndc signing \-nsec3param none\fR
removes an existing NSEC3 chain and replaces it with NSEC\&.
.sp
\fBrndc signing \-serial value\fR
sets the serial number of the zone to value\&. If the value would cause the serial number to go backwards it will be rejected\&. The primary use is to set the serial on inline signed zones\&.
.RE
.PP
\fBstats\fR
.RS 4
Write server statistics to the statistics file\&. (See the
\fBstatistics\-file\fR
option in the BIND 9 Administrator Reference Manual\&.)
.RE
.PP
\fBstatus\fR
.RS 4
Display status of the server\&. Note that the number of zones includes the internal
\fBbind/CH\fR
zone and the default
\fB\&./IN\fR
hint zone if there is not an explicit root zone configured\&.
.RE
.PP
\fBstop \fR\fB[\-p]\fR
.RS 4
Stop the server, making sure any recent changes made through dynamic update or IXFR are first saved to the master files of the updated zones\&. If
\fB\-p\fR
is specified
\fBnamed\fR\*(Aqs process id is returned\&. This allows an external process to determine when
\fBnamed\fR
had completed stopping\&.
.sp
See also
\fBrndc halt\fR\&.
.RE
.PP
\fBsync \fR\fB[\-clean]\fR\fB \fR\fB[\fIzone\fR [\fIclass\fR [\fIview\fR]]]\fR
.RS 4
Sync changes in the journal file for a dynamic zone to the master file\&. If the "\-clean" option is specified, the journal file is also removed\&. If no zone is specified, then all zones are synced\&.
.RE
.PP
\fBtcp\-timeouts \fR\fB[\fIinitial\fR \fIidle\fR \fIkeepalive\fR \fIadvertised\fR]\fR
.RS 4
When called without arguments, display the current values of the
\fBtcp\-initial\-timeout\fR,
\fBtcp\-idle\-timeout\fR,
\fBtcp\-keepalive\-timeout\fR
and
\fBtcp\-advertised\-timeout\fR
options\&. When called with arguments, update these values\&. This allows an administrator to make rapid adjustments when under a denial of service attack\&. See the descriptions of these options in the BIND 9 Administrator Reference Manual for details of their use\&.
.RE
.PP
\fBthaw \fR\fB[\fIzone\fR [\fIclass\fR [\fIview\fR]]]\fR
.RS 4
Enable updates to a frozen dynamic zone\&. If no zone is specified, then all frozen zones are enabled\&. This causes the server to reload the zone from disk, and re\-enables dynamic updates after the load has completed\&. After a zone is thawed, dynamic updates will no longer be refused\&. If the zone has changed and the
\fBixfr\-from\-differences\fR
option is in use, then the journal file will be updated to reflect changes in the zone\&. Otherwise, if the zone has changed, any existing journal file will be removed\&.
.sp
See also
\fBrndc freeze\fR\&.
.RE
.PP
\fBtrace\fR
.RS 4
Increment the servers debugging level by one\&.
.RE
.PP
\fBtrace \fR\fB\fIlevel\fR\fR
.RS 4
Sets the server\*(Aqs debugging level to an explicit value\&.
.sp
See also
\fBrndc notrace\fR\&.
.RE
.PP
\fBtsig\-delete\fR \fIkeyname\fR [\fIview\fR]
.RS 4
Delete a given TKEY\-negotiated key from the server\&. (This does not apply to statically configured TSIG keys\&.)
.RE
.PP
\fBtsig\-list\fR
.RS 4
List the names of all TSIG keys currently configured for use by
\fBnamed\fR
in each view\&. The list both statically configured keys and dynamic TKEY\-negotiated keys\&.
.RE
.PP
\fBvalidation ( on | off | status ) \fR\fB[\fIview \&.\&.\&.\fR]\fR\fB \fR
.RS 4
Enable, disable, or check the current status of DNSSEC validation\&. Note
\fBdnssec\-enable\fR
also needs to be set to
\fByes\fR
or
\fBauto\fR
to be effective\&. It defaults to enabled\&.
.RE
.PP
\fBzonestatus \fR\fB\fIzone\fR\fR\fB \fR\fB[\fIclass\fR [\fIview\fR]]\fR
.RS 4
Displays the current status of the given zone, including the master file name and any include files from which it was loaded, when it was most recently loaded, the current serial number, the number of nodes, whether the zone supports dynamic updates, whether the zone is DNSSEC signed, whether it uses automatic DNSSEC key management or inline signing, and the scheduled refresh or expiry times for the zone\&.
.sp
See also
\fBrndc showzone\fR\&.
.RE
.SH "LIMITATIONS"
.PP
There is currently no way to provide the shared secret for a
\fBkey_id\fR
without using the configuration file\&.
.PP
Several error messages could be clearer\&.
.SH "SEE ALSO"
.PP
\fBrndc.conf\fR(5),
\fBrndc-confgen\fR(8),
\fBnamed\fR(8),
\fBnamed.conf\fR(5),
\fBndc\fR(8),
BIND 9 Administrator Reference Manual\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2000, 2001, 2004, 2005, 2007, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
.br
