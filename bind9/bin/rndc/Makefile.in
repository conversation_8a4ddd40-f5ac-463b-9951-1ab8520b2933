# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

# $Id$

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

VERSION=@BIND9_VERSION@

@BIND9_MAKE_INCLUDES@

CINCLUDES = -I${srcdir}/include ${ISC_INCLUDES} ${ISCCC_INCLUDES} \
	${ISCCFG_INCLUDES} ${DNS_INCLUDES} ${BIND9_INCLUDES}

CDEFINES =
CWARNINGS =

ISCCFGLIBS =	../../lib/isccfg/libisccfg.@A@
ISCCCLIBS =	../../lib/isccc/libisccc.@A@
ISCLIBS =	../../lib/isc/libisc.@A@
ISCNOSYMLIBS =	../../lib/isc/libisc-nosymtbl.@A@
DNSLIBS =	../../lib/dns/libdns.@A@ @DNS_CRYPTO_LIBS@
BIND9LIBS =	../../lib/bind9/libbind9.@A@

ISCCFGDEPLIBS =	../../lib/isccfg/libisccfg.@A@
ISCCCDEPLIBS =	../../lib/isccc/libisccc.@A@
ISCDEPLIBS =	../../lib/isc/libisc.@A@
DNSDEPLIBS =	../../lib/dns/libdns.@A@
BIND9DEPLIBS =	../../lib/bind9/libbind9.@A@

LIBS =		${ISCLIBS} @LIBS@
NOSYMLIBS =	${ISCNOSYMLIBS} @LIBS@

RNDCDEPLIBS =	${ISCCFGDEPLIBS} ${ISCCCDEPLIBS} ${BIND9DEPLIBS} ${DNSDEPLIBS} ${ISCDEPLIBS}

CONFDEPLIBS =	${DNSDEPLIBS} ${ISCDEPLIBS}

SRCS=		rndc.c

TARGETS =	rndc@EXEEXT@

MANPAGES =	rndc.8 rndc.conf.5

HTMLPAGES =	rndc.html rndc.conf.html

MANOBJS =	${MANPAGES} ${HTMLPAGES}

@BIND9_MAKE_RULES@

rndc.@O@: rndc.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} \
		-DVERSION=\"${VERSION}\" \
		-DRNDC_CONFFILE=\"${sysconfdir}/rndc.conf\" \
		-DRNDC_KEYFILE=\"${sysconfdir}/rndc.key\" \
		-c ${srcdir}/rndc.c

rndc@EXEEXT@: rndc.@O@ util.@O@ ${RNDCDEPLIBS}
	export BASEOBJS="rndc.@O@ util.@O@"; \
	export LIBS0="${ISCCFGLIBS} ${ISCCCLIBS} ${BIND9LIBS} ${DNSLIBS}"; \
	${FINALBUILDCMD}

doc man:: ${MANOBJS}

docclean manclean maintainer-clean::
	rm -f ${MANOBJS}

installdirs:
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${sbindir}
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${mandir}/man8
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${mandir}/man5

install:: rndc@EXEEXT@ installdirs
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} rndc@EXEEXT@ ${DESTDIR}${sbindir}
	${INSTALL_DATA} ${srcdir}/rndc.8 ${DESTDIR}${mandir}/man8
	${INSTALL_DATA} ${srcdir}/rndc.conf.5 ${DESTDIR}${mandir}/man5

uninstall::
	rm -f ${DESTDIR}${mandir}/man5/rndc.conf.5
	rm -f ${DESTDIR}${mandir}/man8/rndc.8
	${LIBTOOL_MODE_UNINSTALL} rm -f ${DESTDIR}${sbindir}/rndc@EXEEXT@

clean distclean maintainer-clean::
	rm -f ${TARGETS}
