<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000, 2001, 2004, 2005, 2007, 2013-2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>rndc</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.rndc"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">rndc</span>
     &#8212; name server control utility
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">rndc</code> 
       [<code class="option">-b <em class="replaceable"><code>source-address</code></em></code>]
       [<code class="option">-c <em class="replaceable"><code>config-file</code></em></code>]
       [<code class="option">-k <em class="replaceable"><code>key-file</code></em></code>]
       [<code class="option">-s <em class="replaceable"><code>server</code></em></code>]
       [<code class="option">-p <em class="replaceable"><code>port</code></em></code>]
       [<code class="option">-q</code>]
       [<code class="option">-r</code>]
       [<code class="option">-V</code>]
       [<code class="option">-y <em class="replaceable"><code>key_id</code></em></code>]
       {command}
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>rndc</strong></span>
      controls the operation of a name
      server.  It supersedes the <span class="command"><strong>ndc</strong></span> utility
      that was provided in old BIND releases.  If
      <span class="command"><strong>rndc</strong></span> is invoked with no command line
      options or arguments, it prints a short summary of the
      supported commands and the available options and their
      arguments.
    </p>
    <p><span class="command"><strong>rndc</strong></span>
      communicates with the name server over a TCP connection, sending
      commands authenticated with digital signatures.  In the current
      versions of
      <span class="command"><strong>rndc</strong></span> and <span class="command"><strong>named</strong></span>,
      the only supported authentication algorithms are HMAC-MD5
      (for compatibility), HMAC-SHA1, HMAC-SHA224, HMAC-SHA256
      (default), HMAC-SHA384 and HMAC-SHA512.
      They use a shared secret on each end of the connection.
      This provides TSIG-style authentication for the command
      request and the name server's response.  All commands sent
      over the channel must be signed by a key_id known to the
      server.
    </p>
    <p><span class="command"><strong>rndc</strong></span>
      reads a configuration file to
      determine how to contact the name server and decide what
      algorithm and key it should use.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-b <em class="replaceable"><code>source-address</code></em></span></dt>
<dd>
	  <p>
	    Use <em class="replaceable"><code>source-address</code></em>
	    as the source address for the connection to the server.
	    Multiple instances are permitted to allow setting of both
	    the IPv4 and IPv6 source addresses.
	  </p>
	</dd>
<dt><span class="term">-c <em class="replaceable"><code>config-file</code></em></span></dt>
<dd>
	  <p>
	    Use <em class="replaceable"><code>config-file</code></em>
	    as the configuration file instead of the default,
	    <code class="filename">/etc/rndc.conf</code>.
	  </p>
	</dd>
<dt><span class="term">-k <em class="replaceable"><code>key-file</code></em></span></dt>
<dd>
	  <p>
	    Use <em class="replaceable"><code>key-file</code></em>
	    as the key file instead of the default,
	    <code class="filename">/etc/rndc.key</code>.  The key in
	    <code class="filename">/etc/rndc.key</code> will be used to
	    authenticate
	    commands sent to the server if the <em class="replaceable"><code>config-file</code></em>
	    does not exist.
	  </p>
	</dd>
<dt><span class="term">-s <em class="replaceable"><code>server</code></em></span></dt>
<dd>
	  <p><em class="replaceable"><code>server</code></em> is
	    the name or address of the server which matches a
	    server statement in the configuration file for
	    <span class="command"><strong>rndc</strong></span>.  If no server is supplied on the
	    command line, the host named by the default-server clause
	    in the options statement of the <span class="command"><strong>rndc</strong></span>
	    configuration file will be used.
	  </p>
	</dd>
<dt><span class="term">-p <em class="replaceable"><code>port</code></em></span></dt>
<dd>
	  <p>
	    Send commands to TCP port
	    <em class="replaceable"><code>port</code></em>
	    instead
	    of BIND 9's default control channel port, 953.
	  </p>
	</dd>
<dt><span class="term">-q</span></dt>
<dd>
	  <p>
	    Quiet mode: Message text returned by the server
	    will not be printed except when there is an error.
	  </p>
	</dd>
<dt><span class="term">-r</span></dt>
<dd>
	  <p>
	    Instructs <span class="command"><strong>rndc</strong></span> to print the result code
	    returned by <span class="command"><strong>named</strong></span> after executing the
	    requested command (e.g., ISC_R_SUCCESS, ISC_R_FAILURE, etc).
	  </p>
	</dd>
<dt><span class="term">-V</span></dt>
<dd>
	  <p>
	    Enable verbose logging.
	  </p>
	</dd>
<dt><span class="term">-y <em class="replaceable"><code>key_id</code></em></span></dt>
<dd>
	  <p>
	    Use the key <em class="replaceable"><code>key_id</code></em>
	    from the configuration file.
	    <em class="replaceable"><code>key_id</code></em>
	    must be
	    known by <span class="command"><strong>named</strong></span> with the same algorithm and secret string
	    in order for control message validation to succeed.
	    If no <em class="replaceable"><code>key_id</code></em>
	    is specified, <span class="command"><strong>rndc</strong></span> will first look
	    for a key clause in the server statement of the server
	    being used, or if no server statement is present for that
	    host, then the default-key clause of the options statement.
	    Note that the configuration file contains shared secrets
	    which are used to send authenticated control commands
	    to name servers.  It should therefore not have general read
	    or write access.
	  </p>
	</dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>COMMANDS</h2>

    <p>
      A list of commands supported by <span class="command"><strong>rndc</strong></span> can
      be seen by running <span class="command"><strong>rndc</strong></span> without arguments.
    </p>
    <p>
      Currently supported commands are:
    </p>

    <div class="variablelist"><dl class="variablelist">
<dt><span class="term"><strong class="userinput"><code>addzone <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>] <em class="replaceable"><code>configuration</code></em> </code></strong></span></dt>
<dd>
	  <p>
	    Add a zone while the server is running.  This
	    command requires the
	    <span class="command"><strong>allow-new-zones</strong></span> option to be set
	    to <strong class="userinput"><code>yes</code></strong>.  The
	    <em class="replaceable"><code>configuration</code></em> string
	    specified on the command line is the zone
	    configuration text that would ordinarily be
	    placed in <code class="filename">named.conf</code>.
	  </p>
	  <p>
	    The configuration is saved in a file called
	    <code class="filename"><em class="replaceable"><code>name</code></em>.nzf</code>,
	    where <em class="replaceable"><code>name</code></em> is the
	    name of the view, or if it contains characters
	    that are incompatible with use as a file name, a
	    cryptographic hash generated from the name
	    of the view.
	    When <span class="command"><strong>named</strong></span> is
	    restarted, the file will be loaded into the view
	    configuration, so that zones that were added
	    can persist after a restart.
	  </p>
	  <p>
	    This sample <span class="command"><strong>addzone</strong></span> command
	    would add the zone <code class="literal">example.com</code>
	    to the default view:
	  </p>
	  <p>
<code class="prompt">$ </code><strong class="userinput"><code>rndc addzone example.com '{ type master; file "example.com.db"; };'</code></strong>
	  </p>
	  <p>
	    (Note the brackets and semi-colon around the zone
	    configuration text.)
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc delzone</strong></span> and <span class="command"><strong>rndc modzone</strong></span>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>delzone [<span class="optional">-clean</span>] <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>] </code></strong></span></dt>
<dd>
	  <p>
	    Delete a zone while the server is running.
	  </p>
	  <p>
	    If the <code class="option">-clean</code> argument is specified,
	    the zone's master file (and journal file, if any)
	    will be deleted along with the zone.  Without the
	    <code class="option">-clean</code> option, zone files must
	    be cleaned up by hand.  (If the zone is of
	    type "slave" or "stub", the files needing to
	    be cleaned up will be reported in the output
	    of the <span class="command"><strong>rndc delzone</strong></span> command.)
	  </p>
	  <p>
	    If the zone was originally added via
	    <span class="command"><strong>rndc addzone</strong></span>, then it will be
	    removed permanently. However, if it was originally
	    configured in <code class="filename">named.conf</code>, then
	    that original configuration is still in place; when
	    the server is restarted or reconfigured, the zone will
	    come back. To remove it permanently, it must also be
	    removed from <code class="filename">named.conf</code>
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc addzone</strong></span> and <span class="command"><strong>rndc modzone</strong></span>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>dnstap ( -reopen | -roll [<span class="optional"><em class="replaceable"><code>number</code></em></span>] )</code></strong></span></dt>
<dd>
	  <p>
	    Close and re-open DNSTAP output files.
	    <span class="command"><strong>rndc dnstap -reopen</strong></span> allows the output
	    file to be renamed externally, so
	    that <span class="command"><strong>named</strong></span> can truncate and re-open it.
	    <span class="command"><strong>rndc dnstap -roll</strong></span> causes the output file
	    to be rolled automatically, similar to log files; the most
	    recent output file has ".0" appended to its name; the
	    previous most recent output file is moved to ".1", and so on.
	    If <em class="replaceable"><code>number</code></em> is specified, then the
	    number of backup log files is limited to that number.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>dumpdb [<span class="optional">-all|-cache|-ecscache|-zone|-adb|-bad|-fail</span>] [<span class="optional"><em class="replaceable"><code>view ...</code></em></span>]</code></strong></span></dt>
<dd>
	  <p>
	    Dump the server's caches (default) and/or zones to
	    the dump file for the specified views.  If no view
            is specified, all views are dumped.
	    (See the <span class="command"><strong>dump-file</strong></span> option in
	    the BIND 9 Administrator Reference Manual.)
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>flush</code></strong></span></dt>
<dd>
	  <p>
	    Flushes the server's cache.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>flushname</code></strong> <em class="replaceable"><code>name</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>] </span></dt>
<dd>
	  <p>
	    Flushes the given name from the view's DNS cache
	    and, if applicable, from the view's nameserver address
	    database, bad server cache and SERVFAIL cache.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>flushtree</code></strong> <em class="replaceable"><code>name</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>] </span></dt>
<dd>
	  <p>
	    Flushes the given name, and all of its subdomains,
	    from the view's DNS cache, address database,
	    bad server cache, and SERVFAIL cache.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>freeze [<span class="optional"><em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Suspend updates to a dynamic zone.  If no zone is
	    specified, then all zones are suspended.  This allows
	    manual edits to be made to a zone normally updated by
	    dynamic update.  It also causes changes in the
	    journal file to be synced into the master file.
	    All dynamic update attempts will be refused while
	    the zone is frozen.
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc thaw</strong></span>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>halt [<span class="optional">-p</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Stop the server immediately.  Recent changes
	    made through dynamic update or IXFR are not saved to
	    the master files, but will be rolled forward from the
	    journal files when the server is restarted.
	    If <code class="option">-p</code> is specified <span class="command"><strong>named</strong></span>'s process id is returned.
	    This allows an external process to determine when <span class="command"><strong>named</strong></span>
	    had completed halting.
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc stop</strong></span>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>loadkeys <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Fetch all DNSSEC keys for the given zone
	    from the key directory.  If they are within
	    their publication period, merge them into the
	    zone's DNSKEY RRset.  Unlike <span class="command"><strong>rndc
	    sign</strong></span>, however, the zone is not
	    immediately re-signed by the new keys, but is
	    allowed to incrementally re-sign over time.
	  </p>
	  <p>
	    This command requires that the
	    <span class="command"><strong>auto-dnssec</strong></span> zone option
	    be set to <code class="literal">maintain</code>,
	    and also requires the zone to be configured to
	    allow dynamic DNS.
	    (See "Dynamic Update Policies" in the Administrator
	    Reference Manual for more details.)
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>managed-keys <em class="replaceable"><code>(status | refresh | sync)</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    When run with the "status" keyword, print the current
	    status of the managed-keys database for the specified
	    view, or for all views if none is specified.  When run
	    with the "refresh" keyword, force an immediate refresh
	    of all the managed-keys in the specified view, or all
	    views.  When run with the "sync" keyword, force an
	    immediate dump of the managed-keys database to disk (in
	    the file <code class="filename">managed-keys.bind</code> or
	    (<code class="filename"><em class="replaceable"><code>viewname</code></em>.mkeys</code>).
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>modzone <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>] <em class="replaceable"><code>configuration</code></em> </code></strong></span></dt>
<dd>
	  <p>
	    Modify the configuration of a zone while the server
	    is running.  This command requires the
	    <span class="command"><strong>allow-new-zones</strong></span> option to be
	    set to <strong class="userinput"><code>yes</code></strong>.  As with
	    <span class="command"><strong>addzone</strong></span>, the
	    <em class="replaceable"><code>configuration</code></em> string
	    specified on the command line is the zone
	    configuration text that would ordinarily be
	    placed in <code class="filename">named.conf</code>.
	  </p>
	  <p>
	    If the zone was originally added via
	    <span class="command"><strong>rndc addzone</strong></span>, the configuration
	    changes will be recorded permanently and will still be
	    in effect after the server is restarted or reconfigured.
	    However, if it was originally configured in
	    <code class="filename">named.conf</code>, then that original
	    configuration is still in place; when the server is
	    restarted or reconfigured, the zone will revert to
	    its original configuration.  To make the changes
	    permanent, it must also be modified in
	    <code class="filename">named.conf</code>
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc addzone</strong></span> and <span class="command"><strong>rndc delzone</strong></span>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>notify <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Resend NOTIFY messages for the zone.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>notrace</code></strong></span></dt>
<dd>
	  <p>
	    Sets the server's debugging level to 0.
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc trace</strong></span>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>nta
	[<span class="optional">( -d | -f | -r | -l <em class="replaceable"><code>duration</code></em>)</span>]
	<em class="replaceable"><code>domain</code></em>
	[<span class="optional"><em class="replaceable"><code>view</code></em></span>]
	</code></strong></span></dt>
<dd>
	  <p>
	    Sets a DNSSEC negative trust anchor (NTA)
	    for <code class="option">domain</code>, with a lifetime of
	    <code class="option">duration</code>.  The default lifetime is
	    configured in <code class="filename">named.conf</code> via the
	    <code class="option">nta-lifetime</code> option, and defaults to
	    one hour.  The lifetime cannot exceed one week.
	  </p>
	  <p>
	    A negative trust anchor selectively disables
	    DNSSEC validation for zones that are known to be
	    failing because of misconfiguration rather than
	    an attack.  When data to be validated is
	    at or below an active NTA (and above any other
	    configured trust anchors), <span class="command"><strong>named</strong></span> will
	    abort the DNSSEC validation process and treat the data as
	    insecure rather than bogus.  This continues until the
	    NTA's lifetime is elapsed.
	  </p>
	  <p>
	    NTAs persist across restarts of the <span class="command"><strong>named</strong></span> server.
	    The NTAs for a view are saved in a file called
	    <code class="filename"><em class="replaceable"><code>name</code></em>.nta</code>,
	    where <em class="replaceable"><code>name</code></em> is the
	    name of the view, or if it contains characters
	    that are incompatible with use as a file name, a
	    cryptographic hash generated from the name
	    of the view.
	  </p>
	  <p>
	    An existing NTA can be removed by using the
	    <code class="option">-remove</code> option.
	  </p>
	  <p>
	    An NTA's lifetime can be specified with the
	    <code class="option">-lifetime</code> option.  TTL-style
	    suffixes can be used to specify the lifetime in
	    seconds, minutes, or hours.  If the specified NTA
	    already exists, its lifetime will be updated to the
	    new value.  Setting <code class="option">lifetime</code> to zero
	    is equivalent to <code class="option">-remove</code>.
	  </p>
	  <p>
	    If <code class="option">-dump</code> is used, any other arguments
	    are ignored, and a list of existing NTAs is printed
	    (note that this may include NTAs that are expired but
	    have not yet been cleaned up).
	  </p>
	  <p>
	    Normally, <span class="command"><strong>named</strong></span> will periodically
	    test to see whether data below an NTA can now be
	    validated (see the <code class="option">nta-recheck</code> option
	    in the Administrator Reference Manual for details).
	    If data can be validated, then the NTA is regarded as
	    no longer necessary, and will be allowed to expire
	    early.  The <code class="option">-force</code> overrides this
	    behavior and forces an NTA to persist for its entire
	    lifetime, regardless of whether data could be
	    validated if the NTA were not present.
	  </p>
	  <p>
	    All of these options can be shortened, i.e., to
	    <code class="option">-l</code>, <code class="option">-r</code>, <code class="option">-d</code>,
	    and <code class="option">-f</code>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>querylog</code></strong> [<span class="optional"> on | off </span>] </span></dt>
<dd>
	  <p>
	    Enable or disable query logging.  (For backward
	    compatibility, this command can also be used without
	    an argument to toggle query logging on and off.)
	  </p>
	  <p>
	    Query logging can also be enabled
	    by explicitly directing the <span class="command"><strong>queries</strong></span>
	    <span class="command"><strong>category</strong></span> to a
	    <span class="command"><strong>channel</strong></span> in the
	    <span class="command"><strong>logging</strong></span> section of
	    <code class="filename">named.conf</code> or by specifying
	    <span class="command"><strong>querylog yes;</strong></span> in the
	    <span class="command"><strong>options</strong></span> section of
	    <code class="filename">named.conf</code>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>reconfig</code></strong></span></dt>
<dd>
	  <p>
	    Reload the configuration file and load new zones,
	    but do not reload existing zone files even if they
	    have changed.
	    This is faster than a full <span class="command"><strong>reload</strong></span> when there
	    is a large number of zones because it avoids the need
	    to examine the
	    modification times of the zones files.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>recursing</code></strong></span></dt>
<dd>
	  <p>
	    Dump the list of queries <span class="command"><strong>named</strong></span> is currently
	    recursing on, and the list of domains to which iterative
	    queries are currently being sent.  (The second list includes
	    the number of fetches currently active for the given domain,
	    and how many have been passed or dropped because of the
	    <code class="option">fetches-per-zone</code> option.)
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>refresh <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Schedule zone maintenance for the given zone.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>reload</code></strong></span></dt>
<dd>
	  <p>
	    Reload configuration file and zones.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>reload <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Reload the given zone.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>retransfer <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Retransfer the given slave zone from the master server.
	  </p>
	  <p>
	    If the zone is configured to use
	    <span class="command"><strong>inline-signing</strong></span>, the signed
	    version of the zone is discarded; after the
	    retransfer of the unsigned version is complete, the
	    signed version will be regenerated with all new
	    signatures.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>scan</code></strong></span></dt>
<dd>
	  <p>
	     Scan the list of available network interfaces
	     for changes, without performing a full
	     <span class="command"><strong>reconfig</strong></span> or waiting for the
	     <span class="command"><strong>interface-interval</strong></span> timer.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>secroots [<span class="optional">-</span>] [<span class="optional"><em class="replaceable"><code>view ...</code></em></span>]</code></strong></span></dt>
<dd>
	  <p>
	    Dump the server's security roots and negative trust anchors
	    for the specified views.  If no view is specified, all views
	    are dumped.
	  </p>
	  <p>
	    If the first argument is "-", then the output is
	    returned via the <span class="command"><strong>rndc</strong></span> response channel
	    and printed to the standard output.
	    Otherwise, it is written to the secroots dump file, which
	    defaults to <code class="filename">named.secroots</code>, but can be
	    overridden via the <code class="option">secroots-file</code> option in
	    <code class="filename">named.conf</code>.
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc managed-keys</strong></span>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>showzone <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>] </code></strong></span></dt>
<dd>
	  <p>
	    Print the configuration of a running zone.
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc zonestatus</strong></span>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>sign <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Fetch all DNSSEC keys for the given zone
	    from the key directory (see the
	    <span class="command"><strong>key-directory</strong></span> option in
	    the BIND 9 Administrator Reference Manual).  If they are within
	    their publication period, merge them into the
	    zone's DNSKEY RRset.  If the DNSKEY RRset
	    is changed, then the zone is automatically
	    re-signed with the new key set.
	  </p>
	  <p>
	    This command requires that the
	    <span class="command"><strong>auto-dnssec</strong></span> zone option be set
	    to <code class="literal">allow</code> or
	    <code class="literal">maintain</code>,
	    and also requires the zone to be configured to
	    allow dynamic DNS.
	    (See "Dynamic Update Policies" in the Administrator
	    Reference Manual for more details.)
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc loadkeys</strong></span>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>signing [<span class="optional">( -list | -clear <em class="replaceable"><code>keyid/algorithm</code></em> | -clear <code class="literal">all</code> | -nsec3param ( <em class="replaceable"><code>parameters</code></em> | <code class="literal">none</code> ) | -serial <em class="replaceable"><code>value</code></em> ) </span>] <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>] </code></strong></span></dt>
<dd>
	  <p>
	    List, edit, or remove the DNSSEC signing state records
	    for the specified zone.  The status of ongoing DNSSEC
	    operations (such as signing or generating
	    NSEC3 chains) is stored in the zone in the form
	    of DNS resource records of type
	    <span class="command"><strong>sig-signing-type</strong></span>.
	    <span class="command"><strong>rndc signing -list</strong></span> converts
	    these records into a human-readable form,
	    indicating which keys are currently signing
	    or have finished signing the zone, and which NSEC3
	    chains are being created or removed.
	  </p>
	  <p>
	    <span class="command"><strong>rndc signing -clear</strong></span> can remove
	    a single key (specified in the same format that
	    <span class="command"><strong>rndc signing -list</strong></span> uses to
	    display it), or all keys.  In either case, only
	    completed keys are removed; any record indicating
	    that a key has not yet finished signing the zone
	    will be retained.
	  </p>
	  <p>
	    <span class="command"><strong>rndc signing -nsec3param</strong></span> sets
	    the NSEC3 parameters for a zone.  This is the
	    only supported mechanism for using NSEC3 with
	    <span class="command"><strong>inline-signing</strong></span> zones.
	    Parameters are specified in the same format as
	    an NSEC3PARAM resource record: hash algorithm,
	    flags, iterations, and salt, in that order.
	  </p>
	  <p>
	    Currently, the only defined value for hash algorithm
	    is <code class="literal">1</code>, representing SHA-1.
	    The <code class="option">flags</code> may be set to
	    <code class="literal">0</code> or <code class="literal">1</code>,
	    depending on whether you wish to set the opt-out
	    bit in the NSEC3 chain.  <code class="option">iterations</code>
	    defines the number of additional times to apply
	    the algorithm when generating an NSEC3 hash.  The
	    <code class="option">salt</code> is a string of data expressed
	    in hexadecimal, a hyphen (`-') if no salt is
	    to be used, or the keyword <code class="literal">auto</code>,
	    which causes <span class="command"><strong>named</strong></span> to generate a
	    random 64-bit salt.
	  </p>
	  <p>
	    So, for example, to create an NSEC3 chain using
	    the SHA-1 hash algorithm, no opt-out flag,
	    10 iterations, and a salt value of "FFFF", use:
	    <span class="command"><strong>rndc signing -nsec3param 1 0 10 FFFF <em class="replaceable"><code>zone</code></em></strong></span>.
	    To set the opt-out flag, 15 iterations, and no
	    salt, use:
	    <span class="command"><strong>rndc signing -nsec3param 1 1 15 - <em class="replaceable"><code>zone</code></em></strong></span>.
	  </p>
	  <p>
	    <span class="command"><strong>rndc signing -nsec3param none</strong></span>
	    removes an existing NSEC3 chain and replaces it
	    with NSEC.
	  </p>
	  <p>
	    <span class="command"><strong>rndc signing -serial value</strong></span> sets
	    the serial number of the zone to value.  If the value
	    would cause the serial number to go backwards it will
	    be rejected.  The primary use is to set the serial on
	    inline signed zones.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>stats</code></strong></span></dt>
<dd>
	  <p>
	    Write server statistics to the statistics file.
	    (See the <span class="command"><strong>statistics-file</strong></span> option in
	    the BIND 9 Administrator Reference Manual.)
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>status</code></strong></span></dt>
<dd>
	  <p>
	    Display status of the server.
	    Note that the number of zones includes the internal <span class="command"><strong>bind/CH</strong></span> zone
	    and the default <span class="command"><strong>./IN</strong></span>
	    hint zone if there is not an
	    explicit root zone configured.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>stop [<span class="optional">-p</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Stop the server, making sure any recent changes
	    made through dynamic update or IXFR are first saved to
	    the master files of the updated zones.
	    If <code class="option">-p</code> is specified <span class="command"><strong>named</strong></span>'s process id is returned.
	    This allows an external process to determine when <span class="command"><strong>named</strong></span>
	    had completed stopping.
	  </p>
	  <p>See also <span class="command"><strong>rndc halt</strong></span>.</p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>sync [<span class="optional">-clean</span>] [<span class="optional"><em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Sync changes in the journal file for a dynamic zone
	    to the master file.  If the "-clean" option is
	    specified, the journal file is also removed.  If
	    no zone is specified, then all zones are synced.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>tcp-timeouts [<span class="optional"><em class="replaceable"><code>initial</code></em> <em class="replaceable"><code>idle</code></em> <em class="replaceable"><code>keepalive</code></em> <em class="replaceable"><code>advertised</code></em></span>]</code></strong></span></dt>
<dd>
	  <p>
	    When called without arguments, display the current
            values of the <span class="command"><strong>tcp-initial-timeout</strong></span>,
	    <span class="command"><strong>tcp-idle-timeout</strong></span>,
	    <span class="command"><strong>tcp-keepalive-timeout</strong></span> and
	    <span class="command"><strong>tcp-advertised-timeout</strong></span> options.
            When called with arguments, update these values. This
            allows an administrator to make rapid adjustments when
            under a denial of service attack.  See the descriptions of
            these options in the BIND 9 Administrator Reference Manual
            for details of their use.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>thaw [<span class="optional"><em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Enable updates to a frozen dynamic zone.  If no
	    zone is specified, then all frozen zones are
	    enabled.  This causes the server to reload the zone
	    from disk, and re-enables dynamic updates after the
	    load has completed.  After a zone is thawed,
	    dynamic updates will no longer be refused.  If
	    the zone has changed and the
	    <span class="command"><strong>ixfr-from-differences</strong></span> option is
	    in use, then the journal file will be updated to
	    reflect changes in the zone.  Otherwise, if the
	    zone has changed, any existing journal file will be
	    removed.
	  </p>
	  <p>See also <span class="command"><strong>rndc freeze</strong></span>.</p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>trace</code></strong></span></dt>
<dd>
	  <p>
	    Increment the servers debugging level by one.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>trace <em class="replaceable"><code>level</code></em></code></strong></span></dt>
<dd>
	  <p>
	    Sets the server's debugging level to an explicit
	    value.
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc notrace</strong></span>.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>tsig-delete</code></strong> <em class="replaceable"><code>keyname</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span></dt>
<dd>
	  <p>
	    Delete a given TKEY-negotiated key from the server.
	    (This does not apply to statically configured TSIG
	    keys.)
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>tsig-list</code></strong></span></dt>
<dd>
	  <p>
	    List the names of all TSIG keys currently configured
	    for use by <span class="command"><strong>named</strong></span> in each view.  The
	    list both statically configured keys and dynamic
	    TKEY-negotiated keys.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>validation ( on | off | status ) [<span class="optional"><em class="replaceable"><code>view ...</code></em></span>] </code></strong></span></dt>
<dd>
	  <p>
	    Enable, disable, or check the current status of
	    DNSSEC validation.
	    Note <span class="command"><strong>dnssec-enable</strong></span> also needs to be
	    set to <strong class="userinput"><code>yes</code></strong> or
	    <strong class="userinput"><code>auto</code></strong> to be effective.
	    It defaults to enabled.
	  </p>
	</dd>
<dt><span class="term"><strong class="userinput"><code>zonestatus <em class="replaceable"><code>zone</code></em> [<span class="optional"><em class="replaceable"><code>class</code></em> [<span class="optional"><em class="replaceable"><code>view</code></em></span>]</span>]</code></strong></span></dt>
<dd>
	  <p>
	    Displays the current status of the given zone,
	    including the master file name and any include
	    files from which it was loaded, when it was most
	    recently loaded, the current serial number, the
	    number of nodes, whether the zone supports
	    dynamic updates, whether the zone is DNSSEC
	    signed, whether it uses automatic DNSSEC key
	    management or inline signing, and the scheduled
	    refresh or expiry times for the zone.
	  </p>
	  <p>
	    See also <span class="command"><strong>rndc showzone</strong></span>.
	  </p>
	</dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>LIMITATIONS</h2>

    <p>
      There is currently no way to provide the shared secret for a
      <code class="option">key_id</code> without using the configuration file.
    </p>
    <p>
      Several error messages could be clearer.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.11"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
	<span class="refentrytitle">rndc.conf</span>(5)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">rndc-confgen</span>(8)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">named</span>(8)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">named.conf</span>(5)
      </span>,
      <span class="citerefentry">
	<span class="refentrytitle">ndc</span>(8)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>.
    </p>
  </div>

</div></body>
</html>
