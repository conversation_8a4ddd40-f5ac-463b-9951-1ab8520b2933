<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000-2002, 2004-2007, 2009-2011, 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>named-checkzone</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.named-checkzone"></a><div class="titlepage"></div>
  
  

  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">named-checkzone</span>, 
    <span class="application">named-compilezone</span>
     &#8212; zone file validity checking or converting tool
  </p>
</div>

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">named-checkzone</code> 
       [<code class="option">-d</code>]
       [<code class="option">-h</code>]
       [<code class="option">-j</code>]
       [<code class="option">-q</code>]
       [<code class="option">-v</code>]
       [<code class="option">-c <em class="replaceable"><code>class</code></em></code>]
       [<code class="option">-f <em class="replaceable"><code>format</code></em></code>]
       [<code class="option">-F <em class="replaceable"><code>format</code></em></code>]
       [<code class="option">-J <em class="replaceable"><code>filename</code></em></code>]
       [<code class="option">-i <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-k <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-m <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-M <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-n <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-l <em class="replaceable"><code>ttl</code></em></code>]
       [<code class="option">-L <em class="replaceable"><code>serial</code></em></code>]
       [<code class="option">-o <em class="replaceable"><code>filename</code></em></code>]
       [<code class="option">-r <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-s <em class="replaceable"><code>style</code></em></code>]
       [<code class="option">-S <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-t <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-T <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-w <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-D</code>]
       [<code class="option">-W <em class="replaceable"><code>mode</code></em></code>]
       {zonename}
       {filename}
    </p></div>
    <div class="cmdsynopsis"><p>
      <code class="command">named-compilezone</code> 
       [<code class="option">-d</code>]
       [<code class="option">-j</code>]
       [<code class="option">-q</code>]
       [<code class="option">-v</code>]
       [<code class="option">-c <em class="replaceable"><code>class</code></em></code>]
       [<code class="option">-C <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-f <em class="replaceable"><code>format</code></em></code>]
       [<code class="option">-F <em class="replaceable"><code>format</code></em></code>]
       [<code class="option">-J <em class="replaceable"><code>filename</code></em></code>]
       [<code class="option">-i <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-k <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-m <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-n <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-l <em class="replaceable"><code>ttl</code></em></code>]
       [<code class="option">-L <em class="replaceable"><code>serial</code></em></code>]
       [<code class="option">-r <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-s <em class="replaceable"><code>style</code></em></code>]
       [<code class="option">-t <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-T <em class="replaceable"><code>mode</code></em></code>]
       [<code class="option">-w <em class="replaceable"><code>directory</code></em></code>]
       [<code class="option">-D</code>]
       [<code class="option">-W <em class="replaceable"><code>mode</code></em></code>]
       {<code class="option">-o <em class="replaceable"><code>filename</code></em></code>}
       {zonename}
       {filename}
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>named-checkzone</strong></span>
      checks the syntax and integrity of a zone file.  It performs the
      same checks as <span class="command"><strong>named</strong></span> does when loading a
      zone.  This makes <span class="command"><strong>named-checkzone</strong></span> useful for
      checking zone files before configuring them into a name server.
    </p>
    <p>
        <span class="command"><strong>named-compilezone</strong></span> is similar to
	<span class="command"><strong>named-checkzone</strong></span>, but it always dumps the
        zone contents to a specified file in a specified format.
	Additionally, it applies stricter check levels by default,
        since the dump output will be used as an actual zone file
	loaded by <span class="command"><strong>named</strong></span>.
	When manually specified otherwise, the check levels must at
        least be as strict as those specified in the
	<span class="command"><strong>named</strong></span> configuration file.
     </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>


    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-d</span></dt>
<dd>
          <p>
            Enable debugging.
          </p>
        </dd>
<dt><span class="term">-h</span></dt>
<dd>
          <p>
            Print the usage summary and exit.
          </p>
        </dd>
<dt><span class="term">-q</span></dt>
<dd>
          <p>
            Quiet mode - exit code only.
          </p>
        </dd>
<dt><span class="term">-v</span></dt>
<dd>
          <p>
            Print the version of the <span class="command"><strong>named-checkzone</strong></span>
            program and exit.
          </p>
        </dd>
<dt><span class="term">-j</span></dt>
<dd>
          <p>
            When loading a zone file, read the journal if it exists.
            The journal file name is assumed to be the zone file name
	    appended with the string <code class="filename">.jnl</code>.
          </p>
        </dd>
<dt><span class="term">-J <em class="replaceable"><code>filename</code></em></span></dt>
<dd>
          <p>
            When loading the zone file read the journal from the given
            file, if it exists. (Implies -j.)
          </p>
        </dd>
<dt><span class="term">-c <em class="replaceable"><code>class</code></em></span></dt>
<dd>
          <p>
            Specify the class of the zone.  If not specified, "IN" is assumed.
          </p>
        </dd>
<dt><span class="term">-i <em class="replaceable"><code>mode</code></em></span></dt>
<dd>
	  <p>
	      Perform post-load zone integrity checks.  Possible modes are
	      <span class="command"><strong>"full"</strong></span> (default),
	      <span class="command"><strong>"full-sibling"</strong></span>,
	      <span class="command"><strong>"local"</strong></span>,
	      <span class="command"><strong>"local-sibling"</strong></span> and
	      <span class="command"><strong>"none"</strong></span>.
	  </p>
	  <p>
	      Mode <span class="command"><strong>"full"</strong></span> checks that MX records
	      refer to A or AAAA record (both in-zone and out-of-zone
	      hostnames).  Mode <span class="command"><strong>"local"</strong></span> only
	      checks MX records which refer to in-zone hostnames.
	  </p>
	  <p>
	      Mode <span class="command"><strong>"full"</strong></span> checks that SRV records
	      refer to A or AAAA record (both in-zone and out-of-zone
	      hostnames).  Mode <span class="command"><strong>"local"</strong></span> only
	      checks SRV records which refer to in-zone hostnames.
	  </p>
	  <p>
	      Mode <span class="command"><strong>"full"</strong></span> checks that delegation NS
	      records refer to A or AAAA record (both in-zone and out-of-zone
	      hostnames).  It also checks that glue address records
	      in the zone match those advertised by the child.
	      Mode <span class="command"><strong>"local"</strong></span> only checks NS records which
	      refer to in-zone hostnames or that some required glue exists,
	      that is when the nameserver is in a child zone.
	  </p>
	  <p>
	      Mode <span class="command"><strong>"full-sibling"</strong></span> and
	      <span class="command"><strong>"local-sibling"</strong></span> disable sibling glue
	      checks but are otherwise the same as <span class="command"><strong>"full"</strong></span>
	      and <span class="command"><strong>"local"</strong></span> respectively.
	  </p>
	  <p>
	      Mode <span class="command"><strong>"none"</strong></span> disables the checks.
	  </p>
	</dd>
<dt><span class="term">-f <em class="replaceable"><code>format</code></em></span></dt>
<dd>
	  <p>
	    Specify the format of the zone file.
	    Possible formats are <span class="command"><strong>"text"</strong></span> (default),
	    <span class="command"><strong>"raw"</strong></span>, and <span class="command"><strong>"map"</strong></span>.
	  </p>
	</dd>
<dt><span class="term">-F <em class="replaceable"><code>format</code></em></span></dt>
<dd>
	  <p>
	    Specify the format of the output file specified.
	    For <span class="command"><strong>named-checkzone</strong></span>,
	    this does not cause any effects unless it dumps the zone
	    contents.
	  </p>
	  <p>
	    Possible formats are <span class="command"><strong>"text"</strong></span> (default),
	    which is the standard textual representation of the zone,
	    and <span class="command"><strong>"map"</strong></span>, <span class="command"><strong>"raw"</strong></span>,
            and <span class="command"><strong>"raw=N"</strong></span>, which store the zone in a
            binary format for rapid loading by <span class="command"><strong>named</strong></span>.
            <span class="command"><strong>"raw=N"</strong></span> specifies the format version of
            the raw zone file: if N is 0, the raw file can be read by
            any version of <span class="command"><strong>named</strong></span>; if N is 1, the file
            can be read by release 9.9.0 or higher; the default is 1.
	  </p>
	</dd>
<dt><span class="term">-k <em class="replaceable"><code>mode</code></em></span></dt>
<dd>
          <p>
            Perform <span class="command"><strong>"check-names"</strong></span> checks with the
	    specified failure mode.
            Possible modes are <span class="command"><strong>"fail"</strong></span>
	    (default for <span class="command"><strong>named-compilezone</strong></span>),
            <span class="command"><strong>"warn"</strong></span>
	    (default for <span class="command"><strong>named-checkzone</strong></span>) and
            <span class="command"><strong>"ignore"</strong></span>.
          </p>
        </dd>
<dt><span class="term">-l <em class="replaceable"><code>ttl</code></em></span></dt>
<dd>
          <p>
            Sets a maximum permissible TTL for the input file.
            Any record with a TTL higher than this value will cause
            the zone to be rejected.  This is similar to using the
            <span class="command"><strong>max-zone-ttl</strong></span> option in
            <code class="filename">named.conf</code>.
          </p>
        </dd>
<dt><span class="term">-L <em class="replaceable"><code>serial</code></em></span></dt>
<dd>
          <p>
            When compiling a zone to "raw" or "map" format, set the
            "source serial" value in the header to the specified serial
            number.  (This is expected to be used primarily for testing
            purposes.)
          </p>
        </dd>
<dt><span class="term">-m <em class="replaceable"><code>mode</code></em></span></dt>
<dd>
          <p>
            Specify whether MX records should be checked to see if they
            are addresses.  Possible modes are <span class="command"><strong>"fail"</strong></span>,
            <span class="command"><strong>"warn"</strong></span> (default) and
            <span class="command"><strong>"ignore"</strong></span>.
          </p>
        </dd>
<dt><span class="term">-M <em class="replaceable"><code>mode</code></em></span></dt>
<dd>
	  <p>
	    Check if a MX record refers to a CNAME.
            Possible modes are <span class="command"><strong>"fail"</strong></span>,
            <span class="command"><strong>"warn"</strong></span> (default) and
            <span class="command"><strong>"ignore"</strong></span>.
	  </p>
        </dd>
<dt><span class="term">-n <em class="replaceable"><code>mode</code></em></span></dt>
<dd>
          <p>
            Specify whether NS records should be checked to see if they
            are addresses.
	    Possible modes are <span class="command"><strong>"fail"</strong></span>
	    (default for <span class="command"><strong>named-compilezone</strong></span>),
            <span class="command"><strong>"warn"</strong></span>
	    (default for <span class="command"><strong>named-checkzone</strong></span>) and
            <span class="command"><strong>"ignore"</strong></span>.
          </p>
        </dd>
<dt><span class="term">-o <em class="replaceable"><code>filename</code></em></span></dt>
<dd>
          <p>
            Write zone output to <code class="filename">filename</code>.
	    If <code class="filename">filename</code> is <code class="filename">-</code> then
	    write to standard out.
	    This is mandatory for <span class="command"><strong>named-compilezone</strong></span>.
          </p>
        </dd>
<dt><span class="term">-r <em class="replaceable"><code>mode</code></em></span></dt>
<dd>
	  <p>
            Check for records that are treated as different by DNSSEC but
	    are semantically equal in plain DNS.
            Possible modes are <span class="command"><strong>"fail"</strong></span>,
            <span class="command"><strong>"warn"</strong></span> (default) and
            <span class="command"><strong>"ignore"</strong></span>.
	  </p>
        </dd>
<dt><span class="term">-s <em class="replaceable"><code>style</code></em></span></dt>
<dd>
	  <p>
	    Specify the style of the dumped zone file.
	    Possible styles are <span class="command"><strong>"full"</strong></span> (default)
	    and <span class="command"><strong>"relative"</strong></span>.
	    The full format is most suitable for processing
	    automatically by a separate script.
	    On the other hand, the relative format is more
	    human-readable and is thus suitable for editing by hand.
	    For <span class="command"><strong>named-checkzone</strong></span>
	    this does not cause any effects unless it dumps the zone
	    contents.
	    It also does not have any meaning if the output format
	    is not text.
	  </p>
	</dd>
<dt><span class="term">-S <em class="replaceable"><code>mode</code></em></span></dt>
<dd>
	  <p>
	    Check if a SRV record refers to a CNAME.
            Possible modes are <span class="command"><strong>"fail"</strong></span>,
            <span class="command"><strong>"warn"</strong></span> (default) and
            <span class="command"><strong>"ignore"</strong></span>.
	  </p>
        </dd>
<dt><span class="term">-t <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
          <p>
            Chroot to <code class="filename">directory</code> so that
            include
            directives in the configuration file are processed as if
            run by a similarly chrooted <span class="command"><strong>named</strong></span>.
          </p>
        </dd>
<dt><span class="term">-T <em class="replaceable"><code>mode</code></em></span></dt>
<dd>
	  <p>
	    Check if Sender Policy Framework (SPF) records exist
	    and issues a warning if an SPF-formatted TXT record is
	    not also present.  Possible modes are <span class="command"><strong>"warn"</strong></span>
	    (default), <span class="command"><strong>"ignore"</strong></span>.
	  </p>
	</dd>
<dt><span class="term">-w <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
          <p>
            chdir to <code class="filename">directory</code> so that
            relative
            filenames in master file $INCLUDE directives work.  This
            is similar to the directory clause in
            <code class="filename">named.conf</code>.
          </p>
        </dd>
<dt><span class="term">-D</span></dt>
<dd>
          <p>
            Dump zone file in canonical format.
	    This is always enabled for <span class="command"><strong>named-compilezone</strong></span>.
          </p>
        </dd>
<dt><span class="term">-W <em class="replaceable"><code>mode</code></em></span></dt>
<dd>
          <p>
            Specify whether to check for non-terminal wildcards.
            Non-terminal wildcards are almost always the result of a
            failure to understand the wildcard matching algorithm (RFC 1034).
            Possible modes are <span class="command"><strong>"warn"</strong></span> (default)
            and
            <span class="command"><strong>"ignore"</strong></span>.
          </p>
        </dd>
<dt><span class="term">zonename</span></dt>
<dd>
          <p>
            The domain name of the zone being checked.
          </p>
        </dd>
<dt><span class="term">filename</span></dt>
<dd>
          <p>
            The name of the zone file.
          </p>
        </dd>
</dl></div>

  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>RETURN VALUES</h2>

    <p><span class="command"><strong>named-checkzone</strong></span>
      returns an exit status of 1 if
      errors were detected and 0 otherwise.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
        <span class="refentrytitle">named</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">named-checkconf</span>(8)
      </span>,
      <em class="citetitle">RFC 1035</em>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>.
    </p>
  </div>

</div></body>
</html>
