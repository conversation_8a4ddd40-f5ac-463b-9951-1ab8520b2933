# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

# $Id$

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

VERSION=@BIND9_VERSION@

@BIND9_MAKE_INCLUDES@

CINCLUDES =	${BIND9_INCLUDES} ${DNS_INCLUDES} ${ISCCFG_INCLUDES} \
		${ISC_INCLUDES} @DST_OPENSSL_INC@ ${INFOBLOX_INCLUDES}

INFOBLOX_SOURCE_ROOT    =       ${top_srcdir}/..
INFOBLOX_INCLUDES       =       -I$(INFOBLOX_SOURCE_ROOT)/common/server/include
INFOBLOX_INCLUDES	=	-I$(INFOBLOX_SOURCE_ROOT)/common/server/include \
				-I/opt/bloxdb/include \
				-I$(INFOBLOX_SOURCE_ROOT)/products/dns/server/include

CDEFINES = 	@CRYPTO@ -DNAMED_CONFFILE=\"${sysconfdir}/named.conf\"
CWARNINGS =

DNSLIBS =	../../lib/dns/libdns.@A@ @DNS_CRYPTO_LIBS@
ISCCFGLIBS =	../../lib/isccfg/libisccfg.@A@
ISCLIBS =	../../lib/isc/libisc.@A@
ISCNOSYMLIBS =	../../lib/isc/libisc-nosymtbl.@A@
BIND9LIBS =	../../lib/bind9/libbind9.@A@

DNSDEPLIBS =	../../lib/dns/libdns.@A@
ISCCFGDEPLIBS =	../../lib/isccfg/libisccfg.@A@
ISCDEPLIBS =	../../lib/isc/libisc.@A@
BIND9DEPLIBS =	../../lib/bind9/libbind9.@A@

LIBS =		${ISCLIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}
NOSYMLIBS =	${ISCNOSYMLIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}

SUBDIRS =

# Alphabetically
TARGETS =	named-checkconf@EXEEXT@ named-checkzone@EXEEXT@

# Alphabetically
SRCS =		named-checkconf.c named-checkzone.c check-tool.c

MANPAGES =	named-checkconf.8 named-checkzone.8

HTMLPAGES =	named-checkconf.html named-checkzone.html

MANOBJS =	${MANPAGES} ${HTMLPAGES}

include ../../Makefile-infoblox.inc

@BIND9_MAKE_RULES@

named-checkconf.@O@: named-checkconf.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} \
		-DVERSION=\"${VERSION}\" \
		-c ${srcdir}/named-checkconf.c

named-checkzone.@O@: named-checkzone.c
	${LIBTOOL_MODE_COMPILE} ${CC} ${ALL_CFLAGS} \
		-DVERSION=\"${VERSION}\" \
		-c ${srcdir}/named-checkzone.c

named-checkconf@EXEEXT@: named-checkconf.@O@ check-tool.@O@ ${ISCDEPLIBS} \
		${DNSDEPLIBS} ${ISCCFGDEPLIBS} ${BIND9DEPLIBS}
	export BASEOBJS="named-checkconf.@O@ check-tool.@O@"; \
	export LIBS0="${BIND9LIBS} ${ISCCFGLIBS} ${DNSLIBS}"; \
	${FINALBUILDCMD}

named-checkzone@EXEEXT@: named-checkzone.@O@ check-tool.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	export BASEOBJS="named-checkzone.@O@ check-tool.@O@"; \
	export LIBS0="${ISCCFGLIBS} ${DNSLIBS}"; \
	${FINALBUILDCMD}

doc man:: ${MANOBJS}

docclean manclean maintainer-clean::
	rm -f ${MANOBJS}

installdirs:
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${sbindir}
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${mandir}/man8

install:: named-checkconf@EXEEXT@ named-checkzone@EXEEXT@ installdirs
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} named-checkconf@EXEEXT@ ${DESTDIR}${sbindir}
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} named-checkzone@EXEEXT@ ${DESTDIR}${sbindir}
	(cd ${DESTDIR}${sbindir}; rm -f named-compilezone@EXEEXT@; ${LINK_PROGRAM} named-checkzone@EXEEXT@ named-compilezone@EXEEXT@)
	for m in ${MANPAGES}; do ${INSTALL_DATA} ${srcdir}/$$m ${DESTDIR}${mandir}/man8; done
	(cd ${DESTDIR}${mandir}/man8; rm -f named-compilezone.8; ${LINK_PROGRAM} named-checkzone.8 named-compilezone.8)

uninstall::
	rm -f ${DESTDIR}${mandir}/man8/named-compilezone.8
	for m in ${MANPAGES}; do rm -f ${DESTDIR}${mandir}/man8/$$m ; done
	rm -f ${DESTDIR}${sbindir}/named-compilezone@EXEEXT@
	${LIBTOOL_MODE_UNINSTALL} rm -f ${DESTDIR}${sbindir}/named-checkconf@EXEEXT@
	${LIBTOOL_MODE_UNINSTALL} rm -f ${DESTDIR}${sbindir}/named-checkzone@EXEEXT@

clean distclean::
	rm -f ${TARGETS} r1.htm
