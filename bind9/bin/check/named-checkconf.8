.\" Copyright (C) 2000-2002, 2004, 2005, 2007, 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: named-checkconf
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-01-10
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "NAMED\-CHECKCONF" "8" "2014\-01\-10" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
named-checkconf \- named configuration file syntax checking tool
.SH "SYNOPSIS"
.HP \w'\fBnamed\-checkconf\fR\ 'u
\fBnamed\-checkconf\fR [\fB\-hjvz\fR] [\fB\-p\fR\ [\fB\-x\fR\ ]] [\fB\-t\ \fR\fB\fIdirectory\fR\fR] {filename}
.SH "DESCRIPTION"
.PP
\fBnamed\-checkconf\fR
checks the syntax, but not the semantics, of a
\fBnamed\fR
configuration file\&. The file is parsed and checked for syntax errors, along with all files included by it\&. If no file is specified,
/etc/named\&.conf
is read by default\&.
.PP
Note: files that
\fBnamed\fR
reads in separate parser contexts, such as
rndc\&.key
and
bind\&.keys, are not automatically read by
\fBnamed\-checkconf\fR\&. Configuration errors in these files may cause
\fBnamed\fR
to fail to run, even if
\fBnamed\-checkconf\fR
was successful\&.
\fBnamed\-checkconf\fR
can be run on these files explicitly, however\&.
.SH "OPTIONS"
.PP
\-h
.RS 4
Print the usage summary and exit\&.
.RE
.PP
\-j
.RS 4
When loading a zonefile read the journal if it exists\&.
.RE
.PP
\-p
.RS 4
Print out the
named\&.conf
and included files in canonical form if no errors were detected\&. See also the
\fB\-x\fR
option\&.
.RE
.PP
\-t \fIdirectory\fR
.RS 4
Chroot to
directory
so that include directives in the configuration file are processed as if run by a similarly chrooted
\fBnamed\fR\&.
.RE
.PP
\-v
.RS 4
Print the version of the
\fBnamed\-checkconf\fR
program and exit\&.
.RE
.PP
\-x
.RS 4
When printing the configuration files in canonical form, obscure shared secrets by replacing them with strings of question marks (\*(Aq?\*(Aq)\&. This allows the contents of
named\&.conf
and related files to be shared \(em for example, when submitting bug reports \(em without compromising private data\&. This option cannot be used without
\fB\-p\fR\&.
.RE
.PP
\-z
.RS 4
Perform a test load of all master zones found in
named\&.conf\&.
.RE
.PP
filename
.RS 4
The name of the configuration file to be checked\&. If not specified, it defaults to
/etc/named\&.conf\&.
.RE
.SH "RETURN VALUES"
.PP
\fBnamed\-checkconf\fR
returns an exit status of 1 if errors were detected and 0 otherwise\&.
.SH "SEE ALSO"
.PP
\fBnamed\fR(8),
\fBnamed-checkzone\fR(8),
BIND 9 Administrator Reference Manual\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2000-2002, 2004, 2005, 2007, 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
