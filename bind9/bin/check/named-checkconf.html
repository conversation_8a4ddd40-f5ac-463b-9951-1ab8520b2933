<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2000-2002, 2004, 2005, 2007, 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>named-checkconf</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.named-checkconf"></a><div class="titlepage"></div>
  
  

  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">named-checkconf</span>
     &#8212; named configuration file syntax checking tool
  </p>
</div>

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">named-checkconf</code> 
       [<code class="option">-hjvz</code>]
       [<code class="option">-p</code>
	 [<code class="option">-x</code>
      ]]
       [<code class="option">-t <em class="replaceable"><code>directory</code></em></code>]
       {filename}
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p><span class="command"><strong>named-checkconf</strong></span>
      checks the syntax, but not the semantics, of a
      <span class="command"><strong>named</strong></span> configuration file.  The file is parsed
      and checked for syntax errors, along with all files included by it.
      If no file is specified, <code class="filename">/etc/named.conf</code> is read
      by default.
    </p>
    <p>
      Note: files that <span class="command"><strong>named</strong></span> reads in separate
      parser contexts, such as <code class="filename">rndc.key</code> and
      <code class="filename">bind.keys</code>, are not automatically read
      by <span class="command"><strong>named-checkconf</strong></span>.  Configuration
      errors in these files may cause <span class="command"><strong>named</strong></span> to
      fail to run, even if <span class="command"><strong>named-checkconf</strong></span> was
      successful.  <span class="command"><strong>named-checkconf</strong></span> can be run
      on these files explicitly, however.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>OPTIONS</h2>

    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-h</span></dt>
<dd>
          <p>
            Print the usage summary and exit.
          </p>
        </dd>
<dt><span class="term">-j</span></dt>
<dd>
          <p>
            When loading a zonefile read the journal if it exists.
          </p>
        </dd>
<dt><span class="term">-p</span></dt>
<dd>
          <p>
	    Print out the <code class="filename">named.conf</code> and included files
	    in canonical form if no errors were detected.
            See also the <code class="option">-x</code> option.
          </p>
        </dd>
<dt><span class="term">-t <em class="replaceable"><code>directory</code></em></span></dt>
<dd>
          <p>
            Chroot to <code class="filename">directory</code> so that include
            directives in the configuration file are processed as if
            run by a similarly chrooted <span class="command"><strong>named</strong></span>.
          </p>
        </dd>
<dt><span class="term">-v</span></dt>
<dd>
          <p>
            Print the version of the <span class="command"><strong>named-checkconf</strong></span>
            program and exit.
          </p>
        </dd>
<dt><span class="term">-x</span></dt>
<dd>
          <p>
	    When printing the configuration files in canonical
            form, obscure shared secrets by replacing them with
            strings of question marks ('?'). This allows the
            contents of <code class="filename">named.conf</code> and related
            files to be shared &#8212; for example, when submitting
            bug reports &#8212; without compromising private data.
            This option cannot be used without <code class="option">-p</code>.
          </p>
        </dd>
<dt><span class="term">-z</span></dt>
<dd>
          <p>
	    Perform a test load of all master zones found in
	    <code class="filename">named.conf</code>.
          </p>
        </dd>
<dt><span class="term">filename</span></dt>
<dd>
          <p>
            The name of the configuration file to be checked.  If not
            specified, it defaults to <code class="filename">/etc/named.conf</code>.
          </p>
        </dd>
</dl></div>

  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>RETURN VALUES</h2>

    <p><span class="command"><strong>named-checkconf</strong></span>
      returns an exit status of 1 if
      errors were detected and 0 otherwise.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.10"></a><h2>SEE ALSO</h2>

    <p><span class="citerefentry">
        <span class="refentrytitle">named</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">named-checkzone</span>(8)
      </span>,
      <em class="citetitle">BIND 9 Administrator Reference Manual</em>.
    </p>
  </div>
</div></body>
</html>
