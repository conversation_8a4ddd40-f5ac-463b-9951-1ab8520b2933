.\" Copyright (C) 2000-2002, 2004-2007, 2009-2011, 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: named-checkzone
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-02-19
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "NAMED\-CHECKZONE" "8" "2014\-02\-19" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
named-checkzone, named-compilezone \- zone file validity checking or converting tool
.SH "SYNOPSIS"
.HP \w'\fBnamed\-checkzone\fR\ 'u
\fBnamed\-checkzone\fR [\fB\-d\fR] [\fB\-h\fR] [\fB\-j\fR] [\fB\-q\fR] [\fB\-v\fR] [\fB\-c\ \fR\fB\fIclass\fR\fR] [\fB\-f\ \fR\fB\fIformat\fR\fR] [\fB\-F\ \fR\fB\fIformat\fR\fR] [\fB\-J\ \fR\fB\fIfilename\fR\fR] [\fB\-i\ \fR\fB\fImode\fR\fR] [\fB\-k\ \fR\fB\fImode\fR\fR] [\fB\-m\ \fR\fB\fImode\fR\fR] [\fB\-M\ \fR\fB\fImode\fR\fR] [\fB\-n\ \fR\fB\fImode\fR\fR] [\fB\-l\ \fR\fB\fIttl\fR\fR] [\fB\-L\ \fR\fB\fIserial\fR\fR] [\fB\-o\ \fR\fB\fIfilename\fR\fR] [\fB\-r\ \fR\fB\fImode\fR\fR] [\fB\-s\ \fR\fB\fIstyle\fR\fR] [\fB\-S\ \fR\fB\fImode\fR\fR] [\fB\-t\ \fR\fB\fIdirectory\fR\fR] [\fB\-T\ \fR\fB\fImode\fR\fR] [\fB\-w\ \fR\fB\fIdirectory\fR\fR] [\fB\-D\fR] [\fB\-W\ \fR\fB\fImode\fR\fR] {zonename} {filename}
.HP \w'\fBnamed\-compilezone\fR\ 'u
\fBnamed\-compilezone\fR [\fB\-d\fR] [\fB\-j\fR] [\fB\-q\fR] [\fB\-v\fR] [\fB\-c\ \fR\fB\fIclass\fR\fR] [\fB\-C\ \fR\fB\fImode\fR\fR] [\fB\-f\ \fR\fB\fIformat\fR\fR] [\fB\-F\ \fR\fB\fIformat\fR\fR] [\fB\-J\ \fR\fB\fIfilename\fR\fR] [\fB\-i\ \fR\fB\fImode\fR\fR] [\fB\-k\ \fR\fB\fImode\fR\fR] [\fB\-m\ \fR\fB\fImode\fR\fR] [\fB\-n\ \fR\fB\fImode\fR\fR] [\fB\-l\ \fR\fB\fIttl\fR\fR] [\fB\-L\ \fR\fB\fIserial\fR\fR] [\fB\-r\ \fR\fB\fImode\fR\fR] [\fB\-s\ \fR\fB\fIstyle\fR\fR] [\fB\-t\ \fR\fB\fIdirectory\fR\fR] [\fB\-T\ \fR\fB\fImode\fR\fR] [\fB\-w\ \fR\fB\fIdirectory\fR\fR] [\fB\-D\fR] [\fB\-W\ \fR\fB\fImode\fR\fR] {\fB\-o\ \fR\fB\fIfilename\fR\fR} {zonename} {filename}
.SH "DESCRIPTION"
.PP
\fBnamed\-checkzone\fR
checks the syntax and integrity of a zone file\&. It performs the same checks as
\fBnamed\fR
does when loading a zone\&. This makes
\fBnamed\-checkzone\fR
useful for checking zone files before configuring them into a name server\&.
.PP
\fBnamed\-compilezone\fR
is similar to
\fBnamed\-checkzone\fR, but it always dumps the zone contents to a specified file in a specified format\&. Additionally, it applies stricter check levels by default, since the dump output will be used as an actual zone file loaded by
\fBnamed\fR\&. When manually specified otherwise, the check levels must at least be as strict as those specified in the
\fBnamed\fR
configuration file\&.
.SH "OPTIONS"
.PP
\-d
.RS 4
Enable debugging\&.
.RE
.PP
\-h
.RS 4
Print the usage summary and exit\&.
.RE
.PP
\-q
.RS 4
Quiet mode \- exit code only\&.
.RE
.PP
\-v
.RS 4
Print the version of the
\fBnamed\-checkzone\fR
program and exit\&.
.RE
.PP
\-j
.RS 4
When loading a zone file, read the journal if it exists\&. The journal file name is assumed to be the zone file name appended with the string
\&.jnl\&.
.RE
.PP
\-J \fIfilename\fR
.RS 4
When loading the zone file read the journal from the given file, if it exists\&. (Implies \-j\&.)
.RE
.PP
\-c \fIclass\fR
.RS 4
Specify the class of the zone\&. If not specified, "IN" is assumed\&.
.RE
.PP
\-i \fImode\fR
.RS 4
Perform post\-load zone integrity checks\&. Possible modes are
\fB"full"\fR
(default),
\fB"full\-sibling"\fR,
\fB"local"\fR,
\fB"local\-sibling"\fR
and
\fB"none"\fR\&.
.sp
Mode
\fB"full"\fR
checks that MX records refer to A or AAAA record (both in\-zone and out\-of\-zone hostnames)\&. Mode
\fB"local"\fR
only checks MX records which refer to in\-zone hostnames\&.
.sp
Mode
\fB"full"\fR
checks that SRV records refer to A or AAAA record (both in\-zone and out\-of\-zone hostnames)\&. Mode
\fB"local"\fR
only checks SRV records which refer to in\-zone hostnames\&.
.sp
Mode
\fB"full"\fR
checks that delegation NS records refer to A or AAAA record (both in\-zone and out\-of\-zone hostnames)\&. It also checks that glue address records in the zone match those advertised by the child\&. Mode
\fB"local"\fR
only checks NS records which refer to in\-zone hostnames or that some required glue exists, that is when the nameserver is in a child zone\&.
.sp
Mode
\fB"full\-sibling"\fR
and
\fB"local\-sibling"\fR
disable sibling glue checks but are otherwise the same as
\fB"full"\fR
and
\fB"local"\fR
respectively\&.
.sp
Mode
\fB"none"\fR
disables the checks\&.
.RE
.PP
\-f \fIformat\fR
.RS 4
Specify the format of the zone file\&. Possible formats are
\fB"text"\fR
(default),
\fB"raw"\fR, and
\fB"map"\fR\&.
.RE
.PP
\-F \fIformat\fR
.RS 4
Specify the format of the output file specified\&. For
\fBnamed\-checkzone\fR, this does not cause any effects unless it dumps the zone contents\&.
.sp
Possible formats are
\fB"text"\fR
(default), which is the standard textual representation of the zone, and
\fB"map"\fR,
\fB"raw"\fR, and
\fB"raw=N"\fR, which store the zone in a binary format for rapid loading by
\fBnamed\fR\&.
\fB"raw=N"\fR
specifies the format version of the raw zone file: if N is 0, the raw file can be read by any version of
\fBnamed\fR; if N is 1, the file can be read by release 9\&.9\&.0 or higher; the default is 1\&.
.RE
.PP
\-k \fImode\fR
.RS 4
Perform
\fB"check\-names"\fR
checks with the specified failure mode\&. Possible modes are
\fB"fail"\fR
(default for
\fBnamed\-compilezone\fR),
\fB"warn"\fR
(default for
\fBnamed\-checkzone\fR) and
\fB"ignore"\fR\&.
.RE
.PP
\-l \fIttl\fR
.RS 4
Sets a maximum permissible TTL for the input file\&. Any record with a TTL higher than this value will cause the zone to be rejected\&. This is similar to using the
\fBmax\-zone\-ttl\fR
option in
named\&.conf\&.
.RE
.PP
\-L \fIserial\fR
.RS 4
When compiling a zone to "raw" or "map" format, set the "source serial" value in the header to the specified serial number\&. (This is expected to be used primarily for testing purposes\&.)
.RE
.PP
\-m \fImode\fR
.RS 4
Specify whether MX records should be checked to see if they are addresses\&. Possible modes are
\fB"fail"\fR,
\fB"warn"\fR
(default) and
\fB"ignore"\fR\&.
.RE
.PP
\-M \fImode\fR
.RS 4
Check if a MX record refers to a CNAME\&. Possible modes are
\fB"fail"\fR,
\fB"warn"\fR
(default) and
\fB"ignore"\fR\&.
.RE
.PP
\-n \fImode\fR
.RS 4
Specify whether NS records should be checked to see if they are addresses\&. Possible modes are
\fB"fail"\fR
(default for
\fBnamed\-compilezone\fR),
\fB"warn"\fR
(default for
\fBnamed\-checkzone\fR) and
\fB"ignore"\fR\&.
.RE
.PP
\-o \fIfilename\fR
.RS 4
Write zone output to
filename\&. If
filename
is
\-
then write to standard out\&. This is mandatory for
\fBnamed\-compilezone\fR\&.
.RE
.PP
\-r \fImode\fR
.RS 4
Check for records that are treated as different by DNSSEC but are semantically equal in plain DNS\&. Possible modes are
\fB"fail"\fR,
\fB"warn"\fR
(default) and
\fB"ignore"\fR\&.
.RE
.PP
\-s \fIstyle\fR
.RS 4
Specify the style of the dumped zone file\&. Possible styles are
\fB"full"\fR
(default) and
\fB"relative"\fR\&. The full format is most suitable for processing automatically by a separate script\&. On the other hand, the relative format is more human\-readable and is thus suitable for editing by hand\&. For
\fBnamed\-checkzone\fR
this does not cause any effects unless it dumps the zone contents\&. It also does not have any meaning if the output format is not text\&.
.RE
.PP
\-S \fImode\fR
.RS 4
Check if a SRV record refers to a CNAME\&. Possible modes are
\fB"fail"\fR,
\fB"warn"\fR
(default) and
\fB"ignore"\fR\&.
.RE
.PP
\-t \fIdirectory\fR
.RS 4
Chroot to
directory
so that include directives in the configuration file are processed as if run by a similarly chrooted
\fBnamed\fR\&.
.RE
.PP
\-T \fImode\fR
.RS 4
Check if Sender Policy Framework (SPF) records exist and issues a warning if an SPF\-formatted TXT record is not also present\&. Possible modes are
\fB"warn"\fR
(default),
\fB"ignore"\fR\&.
.RE
.PP
\-w \fIdirectory\fR
.RS 4
chdir to
directory
so that relative filenames in master file $INCLUDE directives work\&. This is similar to the directory clause in
named\&.conf\&.
.RE
.PP
\-D
.RS 4
Dump zone file in canonical format\&. This is always enabled for
\fBnamed\-compilezone\fR\&.
.RE
.PP
\-W \fImode\fR
.RS 4
Specify whether to check for non\-terminal wildcards\&. Non\-terminal wildcards are almost always the result of a failure to understand the wildcard matching algorithm (RFC 1034)\&. Possible modes are
\fB"warn"\fR
(default) and
\fB"ignore"\fR\&.
.RE
.PP
zonename
.RS 4
The domain name of the zone being checked\&.
.RE
.PP
filename
.RS 4
The name of the zone file\&.
.RE
.SH "RETURN VALUES"
.PP
\fBnamed\-checkzone\fR
returns an exit status of 1 if errors were detected and 0 otherwise\&.
.SH "SEE ALSO"
.PP
\fBnamed\fR(8),
\fBnamed-checkconf\fR(8),
RFC 1035,
BIND 9 Administrator Reference Manual\&.
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2000-2002, 2004-2007, 2009-2011, 2013-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
