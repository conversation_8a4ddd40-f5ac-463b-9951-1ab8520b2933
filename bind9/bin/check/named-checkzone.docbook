<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.named-checkzone">
  <info>
    <date>2014-02-19</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>named-checkzone</application></refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <docinfo>
    <copyright>
      <year>2000</year>
      <year>2001</year>
      <year>2002</year>
      <year>2004</year>
      <year>2005</year>
      <year>2006</year>
      <year>2007</year>
      <year>2009</year>
      <year>2010</year>
      <year>2011</year>
      <year>2013</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refnamediv>
    <refname><application>named-checkzone</application></refname>
    <refname><application>named-compilezone</application></refname>
    <refpurpose>zone file validity checking or converting tool</refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>named-checkzone</command>
      <arg choice="opt" rep="norepeat"><option>-d</option></arg>
      <arg choice="opt" rep="norepeat"><option>-h</option></arg>
      <arg choice="opt" rep="norepeat"><option>-j</option></arg>
      <arg choice="opt" rep="norepeat"><option>-q</option></arg>
      <arg choice="opt" rep="norepeat"><option>-v</option></arg>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">class</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-f <replaceable class="parameter">format</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-F <replaceable class="parameter">format</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-J <replaceable class="parameter">filename</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-i <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-k <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-m <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-M <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-n <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-l <replaceable class="parameter">ttl</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-L <replaceable class="parameter">serial</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-o <replaceable class="parameter">filename</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-r <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-s <replaceable class="parameter">style</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-S <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-t <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-T <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-w <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-D</option></arg>
      <arg choice="opt" rep="norepeat"><option>-W <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="req" rep="norepeat">zonename</arg>
      <arg choice="req" rep="norepeat">filename</arg>
    </cmdsynopsis>
    <cmdsynopsis sepchar=" ">
      <command>named-compilezone</command>
      <arg choice="opt" rep="norepeat"><option>-d</option></arg>
      <arg choice="opt" rep="norepeat"><option>-j</option></arg>
      <arg choice="opt" rep="norepeat"><option>-q</option></arg>
      <arg choice="opt" rep="norepeat"><option>-v</option></arg>
      <arg choice="opt" rep="norepeat"><option>-c <replaceable class="parameter">class</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-C <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-f <replaceable class="parameter">format</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-F <replaceable class="parameter">format</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-J <replaceable class="parameter">filename</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-i <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-k <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-m <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-n <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-l <replaceable class="parameter">ttl</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-L <replaceable class="parameter">serial</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-r <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-s <replaceable class="parameter">style</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-t <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-T <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-w <replaceable class="parameter">directory</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-D</option></arg>
      <arg choice="opt" rep="norepeat"><option>-W <replaceable class="parameter">mode</replaceable></option></arg>
      <arg choice="req" rep="norepeat"><option>-o <replaceable class="parameter">filename</replaceable></option></arg>
      <arg choice="req" rep="norepeat">zonename</arg>
      <arg choice="req" rep="norepeat">filename</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para><command>named-checkzone</command>
      checks the syntax and integrity of a zone file.  It performs the
      same checks as <command>named</command> does when loading a
      zone.  This makes <command>named-checkzone</command> useful for
      checking zone files before configuring them into a name server.
    </para>
    <para>
        <command>named-compilezone</command> is similar to
	<command>named-checkzone</command>, but it always dumps the
        zone contents to a specified file in a specified format.
	Additionally, it applies stricter check levels by default,
        since the dump output will be used as an actual zone file
	loaded by <command>named</command>.
	When manually specified otherwise, the check levels must at
        least be as strict as those specified in the
	<command>named</command> configuration file.
     </para>
  </refsection>

  <refsection><info><title>OPTIONS</title></info>


    <variablelist>
      <varlistentry>
        <term>-d</term>
        <listitem>
          <para>
            Enable debugging.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-h</term>
        <listitem>
          <para>
            Print the usage summary and exit.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-q</term>
        <listitem>
          <para>
            Quiet mode - exit code only.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-v</term>
        <listitem>
          <para>
            Print the version of the <command>named-checkzone</command>
            program and exit.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-j</term>
        <listitem>
          <para>
            When loading a zone file, read the journal if it exists.
            The journal file name is assumed to be the zone file name
	    appended with the string <filename>.jnl</filename>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-J <replaceable class="parameter">filename</replaceable></term>
        <listitem>
          <para>
            When loading the zone file read the journal from the given
            file, if it exists. (Implies -j.)
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-c <replaceable class="parameter">class</replaceable></term>
        <listitem>
          <para>
            Specify the class of the zone.  If not specified, "IN" is assumed.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-i <replaceable class="parameter">mode</replaceable></term>
	<listitem>
	  <para>
	      Perform post-load zone integrity checks.  Possible modes are
	      <command>"full"</command> (default),
	      <command>"full-sibling"</command>,
	      <command>"local"</command>,
	      <command>"local-sibling"</command> and
	      <command>"none"</command>.
	  </para>
	  <para>
	      Mode <command>"full"</command> checks that MX records
	      refer to A or AAAA record (both in-zone and out-of-zone
	      hostnames).  Mode <command>"local"</command> only
	      checks MX records which refer to in-zone hostnames.
	  </para>
	  <para>
	      Mode <command>"full"</command> checks that SRV records
	      refer to A or AAAA record (both in-zone and out-of-zone
	      hostnames).  Mode <command>"local"</command> only
	      checks SRV records which refer to in-zone hostnames.
	  </para>
	  <para>
	      Mode <command>"full"</command> checks that delegation NS
	      records refer to A or AAAA record (both in-zone and out-of-zone
	      hostnames).  It also checks that glue address records
	      in the zone match those advertised by the child.
	      Mode <command>"local"</command> only checks NS records which
	      refer to in-zone hostnames or that some required glue exists,
	      that is when the nameserver is in a child zone.
	  </para>
	  <para>
	      Mode <command>"full-sibling"</command> and
	      <command>"local-sibling"</command> disable sibling glue
	      checks but are otherwise the same as <command>"full"</command>
	      and <command>"local"</command> respectively.
	  </para>
	  <para>
	      Mode <command>"none"</command> disables the checks.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-f <replaceable class="parameter">format</replaceable></term>
	<listitem>
	  <para>
	    Specify the format of the zone file.
	    Possible formats are <command>"text"</command> (default),
	    <command>"raw"</command>, and <command>"map"</command>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-F <replaceable class="parameter">format</replaceable></term>
	<listitem>
	  <para>
	    Specify the format of the output file specified.
	    For <command>named-checkzone</command>,
	    this does not cause any effects unless it dumps the zone
	    contents.
	  </para>
	  <para>
	    Possible formats are <command>"text"</command> (default),
	    which is the standard textual representation of the zone,
	    and <command>"map"</command>, <command>"raw"</command>,
            and <command>"raw=N"</command>, which store the zone in a
            binary format for rapid loading by <command>named</command>.
            <command>"raw=N"</command> specifies the format version of
            the raw zone file: if N is 0, the raw file can be read by
            any version of <command>named</command>; if N is 1, the file
            can be read by release 9.9.0 or higher; the default is 1.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
        <term>-k <replaceable class="parameter">mode</replaceable></term>
        <listitem>
          <para>
            Perform <command>"check-names"</command> checks with the
	    specified failure mode.
            Possible modes are <command>"fail"</command>
	    (default for <command>named-compilezone</command>),
            <command>"warn"</command>
	    (default for <command>named-checkzone</command>) and
            <command>"ignore"</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-l <replaceable class="parameter">ttl</replaceable></term>
        <listitem>
          <para>
            Sets a maximum permissible TTL for the input file.
            Any record with a TTL higher than this value will cause
            the zone to be rejected.  This is similar to using the
            <command>max-zone-ttl</command> option in
            <filename>named.conf</filename>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-L <replaceable class="parameter">serial</replaceable></term>
        <listitem>
          <para>
            When compiling a zone to "raw" or "map" format, set the
            "source serial" value in the header to the specified serial
            number.  (This is expected to be used primarily for testing
            purposes.)
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-m <replaceable class="parameter">mode</replaceable></term>
        <listitem>
          <para>
            Specify whether MX records should be checked to see if they
            are addresses.  Possible modes are <command>"fail"</command>,
            <command>"warn"</command> (default) and
            <command>"ignore"</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>-M <replaceable class="parameter">mode</replaceable></term>
        <listitem>
	  <para>
	    Check if a MX record refers to a CNAME.
            Possible modes are <command>"fail"</command>,
            <command>"warn"</command> (default) and
            <command>"ignore"</command>.
	  </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-n <replaceable class="parameter">mode</replaceable></term>
        <listitem>
          <para>
            Specify whether NS records should be checked to see if they
            are addresses.
	    Possible modes are <command>"fail"</command>
	    (default for <command>named-compilezone</command>),
            <command>"warn"</command>
	    (default for <command>named-checkzone</command>) and
            <command>"ignore"</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-o <replaceable class="parameter">filename</replaceable></term>
        <listitem>
          <para>
            Write zone output to <filename>filename</filename>.
	    If <filename>filename</filename> is <filename>-</filename> then
	    write to standard out.
	    This is mandatory for <command>named-compilezone</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>-r <replaceable class="parameter">mode</replaceable></term>
        <listitem>
	  <para>
            Check for records that are treated as different by DNSSEC but
	    are semantically equal in plain DNS.
            Possible modes are <command>"fail"</command>,
            <command>"warn"</command> (default) and
            <command>"ignore"</command>.
	  </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>-s <replaceable class="parameter">style</replaceable></term>
	<listitem>
	  <para>
	    Specify the style of the dumped zone file.
	    Possible styles are <command>"full"</command> (default)
	    and <command>"relative"</command>.
	    The full format is most suitable for processing
	    automatically by a separate script.
	    On the other hand, the relative format is more
	    human-readable and is thus suitable for editing by hand.
	    For <command>named-checkzone</command>
	    this does not cause any effects unless it dumps the zone
	    contents.
	    It also does not have any meaning if the output format
	    is not text.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
	<term>-S <replaceable class="parameter">mode</replaceable></term>
        <listitem>
	  <para>
	    Check if a SRV record refers to a CNAME.
            Possible modes are <command>"fail"</command>,
            <command>"warn"</command> (default) and
            <command>"ignore"</command>.
	  </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-t <replaceable class="parameter">directory</replaceable></term>
        <listitem>
          <para>
            Chroot to <filename>directory</filename> so that
            include
            directives in the configuration file are processed as if
            run by a similarly chrooted <command>named</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
	<term>-T <replaceable class="parameter">mode</replaceable></term>
	<listitem>
	  <para>
	    Check if Sender Policy Framework (SPF) records exist
	    and issues a warning if an SPF-formatted TXT record is
	    not also present.  Possible modes are <command>"warn"</command>
	    (default), <command>"ignore"</command>.
	  </para>
	</listitem>
      </varlistentry>

      <varlistentry>
        <term>-w <replaceable class="parameter">directory</replaceable></term>
        <listitem>
          <para>
            chdir to <filename>directory</filename> so that
            relative
            filenames in master file $INCLUDE directives work.  This
            is similar to the directory clause in
            <filename>named.conf</filename>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-D</term>
        <listitem>
          <para>
            Dump zone file in canonical format.
	    This is always enabled for <command>named-compilezone</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-W <replaceable class="parameter">mode</replaceable></term>
        <listitem>
          <para>
            Specify whether to check for non-terminal wildcards.
            Non-terminal wildcards are almost always the result of a
            failure to understand the wildcard matching algorithm (RFC 1034).
            Possible modes are <command>"warn"</command> (default)
            and
            <command>"ignore"</command>.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>zonename</term>
        <listitem>
          <para>
            The domain name of the zone being checked.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>filename</term>
        <listitem>
          <para>
            The name of the zone file.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>

  </refsection>

  <refsection><info><title>RETURN VALUES</title></info>

    <para><command>named-checkzone</command>
      returns an exit status of 1 if
      errors were detected and 0 otherwise.
    </para>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para><citerefentry>
        <refentrytitle>named</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>named-checkconf</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citetitle>RFC 1035</citetitle>,
      <citetitle>BIND 9 Administrator Reference Manual</citetitle>.
    </para>
  </refsection>

</refentry>
