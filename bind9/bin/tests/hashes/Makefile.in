# Copyright (C) 2010, 2012, 2014  Internet Systems Consortium, Inc. ("ISC")
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
# REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
# AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
# INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
# LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
# OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.

# $Id: Makefile.in,v 1.3 2010/09/29 23:47:05 tbox Exp $

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

CINCLUDES =	${TEST_INCLUDES} ${ISC_INCLUDES}

CDEFINES =
CWARNINGS =

ISCLIBS =	../../../lib/isc/libisc.@A@ @ISC_OPENSSL_LIBS@

ISCDEPLIBS =	../../../lib/isc/libisc.@A@

DEPLIBS =	${ISCDEPLIBS}

LIBS =		${ISCLIBS} ${ISCLIBS} @LIBS@

TLIB =		../../../lib/tests/libt_api.@A@

TARGETS =	t_hashes@EXEEXT@

SRCS =		t_hashes.c

@BIND9_MAKE_RULES@

t_hashes@EXEEXT@: t_hashes.@O@ ${DEPLIBS} ${TLIB}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ t_hashes.@O@ ${TLIB} ${LIBS}

test: t_hashes@EXEEXT@
	-@./t_hashes@EXEEXT@ -c @top_srcdir@/t_config -b @srcdir@ -q 60 -a

testhelp:
	@./t_hashes@EXEEXT@ -h

clean distclean::
	rm -f ${TARGETS}
