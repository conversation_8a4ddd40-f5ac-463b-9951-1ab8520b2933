﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|@PLATFORM@">
      <Configuration>Debug</Configuration>
      <Platform>@PLATFORM@</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|@PLATFORM@">
      <Configuration>Release</Configuration>
      <Platform>@PLATFORM@</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{EE9B94CF-7C33-4F3B-A674-FB756D422C54}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>t_dst</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>..\..\..\..\Build\$(Configuration)\</OutDir>
    <IntDir>.\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\..\Build\$(Configuration)\</OutDir>
    <IntDir>.\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@PLATFORM@'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile>.\$(Configuration)\$(TargetName).pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\$(Configuration)\</AssemblerListingLocation>
      <ObjectFileName>.\$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(OutDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <BrowseInformation>true</BrowseInformation>
      <AdditionalIncludeDirectories>.\;..\..\..\..\;@LIBXML2_INC@..\..\..\..\lib\isc\win32;..\..\..\..\lib\isc\win32\include;..\..\..\..\lib\isc\include;..\..\..\..\lib\dns\include;..\..\..\..\lib\tests\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>..\..\..\..\Build\$(Configuration)\$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalLibraryDirectories>..\..\..\..\lib\isc\win32\$(Configuration);..\..\..\..\lib\dns\win32\$(Configuration);..\..\..\..\lib\tests\win32\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>@<EMAIL>;libdns.lib;libtests.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PreBuildEvent>
      <Command>cd ..
copy "Kdh.+002+18602.key.in" "Kdh.+002+18602.key"
copy "Kdh.+002+18602.private.in" "Kdh.+002+18602.private"
copy "Kdh.+002+48957.key.in" "Kdh.+002+48957.key"
copy "Kdh.+002+48957.private.in" "Kdh.+002+48957.private"
copy "Ktest.+001+00002.key.in" "Ktest.+001+00002.key"
copy "Ktest.+001+54622.key.in" "Ktest.+001+54622.key"
copy "Ktest.+001+54622.private.in" "Ktest.+001+54622.private"
copy "Ktest.+003+23616.key.in" "Ktest.+003+23616.key"
copy "Ktest.+003+23616.private.in" "Ktest.+003+23616.private"
copy "Ktest.+003+49667.key.in" "Ktest.+003+49667.key"
copy dst_2_data.in dst_2_data
copy t2_data_1.in t2_data_1
copy t2_data_2.in t2_data_2
copy t2_dsasig.in t2_dsasig
copy t2_rsasig.in t2_rsasig
</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|@PLATFORM@'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>@INTRINSIC@</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <StringPooling>true</StringPooling>
      <PrecompiledHeaderOutputFile>.\$(Configuration)\$(TargetName).pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\$(Configuration)\</AssemblerListingLocation>
      <ObjectFileName>.\$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(OutDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <AdditionalIncludeDirectories>.\;..\..\..\..\;@LIBXML2_INC@..\..\..\..\lib\isc\win32;..\..\..\..\lib\isc\win32\include;..\..\..\..\lib\isc\include;..\..\..\..\lib\dns\include;..\..\..\..\lib\tests\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>..\..\..\..\Build\$(Configuration)\$(TargetName)$(TargetExt)</OutputFile>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <AdditionalLibraryDirectories>..\..\..\..\lib\isc\win32\$(Configuration);..\..\..\..\lib\dns\win32\$(Configuration);..\..\..\..\lib\tests\win32\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>@<EMAIL>;libdns.lib;libtests.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PreBuildEvent>
      <Command>cd ..
copy "Kdh.+002+18602.key.in" "Kdh.+002+18602.key"
copy "Kdh.+002+18602.private.in" "Kdh.+002+18602.private"
copy "Kdh.+002+48957.key.in" "Kdh.+002+48957.key"
copy "Kdh.+002+48957.private.in" "Kdh.+002+48957.private"
copy "Ktest.+001+00002.key.in" "Ktest.+001+00002.key"
copy "Ktest.+001+54622.key.in" "Ktest.+001+54622.key"
copy "Ktest.+001+54622.private.in" "Ktest.+001+54622.private"
copy "Ktest.+003+23616.key.in" "Ktest.+003+23616.key"
copy "Ktest.+003+23616.private.in" "Ktest.+003+23616.private"
copy "Ktest.+003+49667.key.in" "Ktest.+003+49667.key"
copy dst_2_data.in dst_2_data
copy t2_data_1.in t2_data_1
copy t2_data_2.in t2_data_2
copy t2_dsasig.in t2_dsasig
copy t2_rsasig.in t2_rsasig
</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\t_dst.c" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>