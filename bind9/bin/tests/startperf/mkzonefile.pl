#!/usr/bin/perl
#
# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

# $Id: mkzonefile.pl,v 1.2 2011/09/02 21:15:35 each Exp $
use strict;

die "Usage: makenames.pl zonename num_records" if (@ARGV != 2);
my $zname = @ARGV[0];
my $nrecords = @ARGV[1];

my @chars = split("", "abcdefghijklmnopqrstuvwxyz");

print"\$TTL 300	; 5 minutes
\$ORIGIN $zname.
@			IN SOA	mname1. . (
				********** ; serial
				20         ; refresh (20 seconds)
				20         ; retry (20 seconds)
				1814400    ; expire (3 weeks)
				600        ; minimum (1 hour)
				)
			NS	ns
ns			A	*********\n";

srand; 
for (my $i = 0; $i < $nrecords; $i++) {
        my $name = "";
        for (my $j = 0; $j < 8; $j++) {
                my $r = rand 25;
                $name .= $chars[$r];
        }
        print "$name" . "\tIN\tA\t";
        my $x = int rand 254;
        my $y = int rand 254;
        my $z = int rand 254;
        print "10.$x.$y.$z\n";
}

