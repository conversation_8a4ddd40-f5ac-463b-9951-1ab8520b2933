# Copyright (C) 2004, 2007, 2009, 2012, 2014  Internet Systems Consortium, Inc. ("ISC")
# Copyright (C) 2000-2002  Internet Software Consortium.
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
# REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
# AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
# INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
# LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
# OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.

# $Id$

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

CINCLUDES =	${TEST_INCLUDES} ${DNS_INCLUDES} ${ISC_INCLUDES}

CDEFINES =
CWARNINGS =

ISCLIBS =	../../../lib/isc/libisc.@A@ @ISC_OPENSSL_LIBS@

ISCDEPLIBS =	../../../lib/isc/libisc.@A@

DEPLIBS =	${ISCDEPLIBS}

LIBS =		${ISCLIBS} @LIBS@

TARGETS =	t_net@EXEEXT@

SRCS =		driver.c netaddr_multicast.c sockaddr_multicast.c

OBJS =		driver.@O@ netaddr_multicast.@O@ sockaddr_multicast.@O@

@BIND9_MAKE_RULES@

t_net@EXEEXT@: ${OBJS} ${DEPLIBS} ${TLIB}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ ${OBJS} ${TLIB} ${LIBS}

test: t_net@EXEEXT@
	-@./t_net@EXEEXT@

clean distclean::
	rm -f ${TARGETS}
	rm -f ${OBJS}
