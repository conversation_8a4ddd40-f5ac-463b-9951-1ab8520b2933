# Copyright (C) 2004, 2007, 2009, 2012, 2014  Internet Systems Consortium, Inc. ("ISC")
# Copyright (C) 1999-2002  Internet Software Consortium.
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
# REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
# AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
# INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
# LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
# OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.

# $Id$

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

CINCLUDES =	${TEST_INCLUDES} ${DNS_INCLUDES} ${ISC_INCLUDES}

CDEFINES =
CWARNINGS =

# Note that we do not want to use libtool for libt_api
DNSLIBS =	../../../lib/dns/libdns.@A@ @DNS_CRYPTO_LIBS@
ISCLIBS =	../../../lib/isc/libisc.@A@

DNSDEPLIBS =	../../../lib/dns/libdns.@A@
ISCDEPLIBS =	../../../lib/isc/libisc.@A@

DEPLIBS =	${DNSDEPLIBS} ${ISCDEPLIBS}

LIBS =		${DNSLIBS} ${ISCLIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}

TLIB =		../../../lib/tests/libt_api.@A@

TARGETS =	t_names@EXEEXT@

include ../../../Makefile-infoblox.inc

SRCS =		t_names.c

@BIND9_MAKE_RULES@

t_names@EXEEXT@: t_names.@O@ ${DEPLIBS} ${TLIB}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ t_names.@O@ ${TLIB} ${LIBS}

test: t_names@EXEEXT@
	-@./t_names@EXEEXT@ -c @top_srcdir@/t_config -b @srcdir@ -a

testhelp:
	@./t_names@EXEEXT@ -h

clean distclean::
	rm -f ${TARGETS}
