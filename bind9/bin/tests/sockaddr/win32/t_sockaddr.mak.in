# Microsoft Developer Studio Generated NMAKE File, Based on t_sockaddr.dsp
!IF "$(CFG)" == ""
CFG=t_sockaddr - @PLATFORM@ Debug
!MESSAGE No configuration specified. Defaulting to t_sockaddr - @PLATFORM@ Debug.
!ENDIF 

!IF "$(CFG)" != "t_sockaddr - @PLATFORM@ Release" && "$(CFG)" != "t_sockaddr - @PLATFORM@ Debug"
!MESSAGE Invalid configuration "$(CFG)" specified.
!MESSAGE You can specify a configuration when running NMAKE
!MESSAGE by defining the macro CFG on the command line. For example:
!MESSAGE 
!MESSAGE NMAKE /f "t_sockaddr.mak" CFG="t_sockaddr - @PLATFORM@ Debug"
!MESSAGE 
!MESSAGE Possible choices for configuration are:
!MESSAGE 
!MESSAGE "t_sockaddr - @PLATFORM@ Release" (based on "@PLATFORM@ (x86) Console Application")
!MESSAGE "t_sockaddr - @PLATFORM@ Debug" (based on "@PLATFORM@ (x86) Console Application")
!MESSAGE 
!ERROR An invalid configuration is specified.
!ENDIF 

!IF "$(OS)" == "Windows_NT"
NULL=
!ELSE 
NULL=nul
!ENDIF 

CPP=cl.exe
RSC=rc.exe
LIBXML=@LIBXML2_LIB@

!IF  "$(CFG)" == "t_sockaddr - @PLATFORM@ Release"
_VC_MANIFEST_INC=0
_VC_MANIFEST_BASENAME=__VC80
!ELSE
_VC_MANIFEST_INC=1
_VC_MANIFEST_BASENAME=__VC80.Debug
!ENDIF

####################################################
# Specifying name of temporary resource file used only in incremental builds:

!if "$(_VC_MANIFEST_INC)" == "1"
_VC_MANIFEST_AUTO_RES=$(_VC_MANIFEST_BASENAME).auto.res
!else
_VC_MANIFEST_AUTO_RES=
!endif

####################################################
# _VC_MANIFEST_EMBED_EXE - command to embed manifest in EXE:

!if "$(_VC_MANIFEST_INC)" == "1"

#MT_SPECIAL_RETURN=1090650113
#MT_SPECIAL_SWITCH=-notify_resource_update
MT_SPECIAL_RETURN=0
MT_SPECIAL_SWITCH=
_VC_MANIFEST_EMBED_EXE= \
if exist $@.manifest mt.exe -manifest $@.manifest -out:$(_VC_MANIFEST_BASENAME).auto.manifest $(MT_SPECIAL_SWITCH) & \
if "%ERRORLEVEL%" == "$(MT_SPECIAL_RETURN)" \
rc /r $(_VC_MANIFEST_BASENAME).auto.rc & \
link $** /out:$@ $(LFLAGS)

!else

_VC_MANIFEST_EMBED_EXE= \
if exist $@.manifest mt.exe -manifest $@.manifest -outputresource:$@;1

!endif

####################################################
# _VC_MANIFEST_EMBED_DLL - command to embed manifest in DLL:

!if "$(_VC_MANIFEST_INC)" == "1"

#MT_SPECIAL_RETURN=1090650113
#MT_SPECIAL_SWITCH=-notify_resource_update
MT_SPECIAL_RETURN=0
MT_SPECIAL_SWITCH=
_VC_MANIFEST_EMBED_EXE= \
if exist $@.manifest mt.exe -manifest $@.manifest -out:$(_VC_MANIFEST_BASENAME).auto.manifest $(MT_SPECIAL_SWITCH) & \
if "%ERRORLEVEL%" == "$(MT_SPECIAL_RETURN)" \
rc /r $(_VC_MANIFEST_BASENAME).auto.rc & \
link $** /out:$@ $(LFLAGS)

!else

_VC_MANIFEST_EMBED_EXE= \
if exist $@.manifest mt.exe -manifest $@.manifest -outputresource:$@;2

!endif
####################################################
# _VC_MANIFEST_CLEAN - command to clean resources files generated temporarily:

!if "$(_VC_MANIFEST_INC)" == "1"

_VC_MANIFEST_CLEAN=-del $(_VC_MANIFEST_BASENAME).auto.res \
    $(_VC_MANIFEST_BASENAME).auto.rc \
    $(_VC_MANIFEST_BASENAME).auto.manifest

!else

_VC_MANIFEST_CLEAN=

!endif

!IF  "$(CFG)" == "t_sockaddr - @PLATFORM@ Release"

OUTDIR=.\Release
INTDIR=.\Release

!IF "$(RECURSE)" == "0" 

ALL : "..\..\..\..\Build\Release\t_sockaddr.exe"

!ELSE 

ALL : "libtests - @PLATFORM@ Release" "libisc - @PLATFORM@ Release" "..\..\..\..\Build\Release\t_sockaddr.exe"

!ENDIF 

!IF "$(RECURSE)" == "1" 
CLEAN :"libisc - @PLATFORM@ ReleaseCLEAN" "libtests - @PLATFORM@ ReleaseCLEAN"
!ELSE 
CLEAN :
!ENDIF 
	-@erase "$(INTDIR)\t_sockaddr.obj"
	-@erase "$(INTDIR)\vc60.idb"
	-@erase "..\..\..\..\Build\Release\t_sockaddr.exe"
	-@$(_VC_MANIFEST_CLEAN)

"$(OUTDIR)" :
    if not exist "$(OUTDIR)/$(NULL)" mkdir "$(OUTDIR)"

CPP_PROJ=/nologo /MD /W3 @COPTX@ @COPTI@ /O2 /I "./" /I "../../../../" @LIBXML2_INC@ /I "../../../../lib/isc/win32" /I "../../../../lib/isc/win32/include" /I "../../../../lib/isc/include" /I "../../../../lib/tests/include" /D "WIN32" /D "NDEBUG" /D "__STDC__" /D "_CONSOLE" /D "_MBCS" /Fp"$(INTDIR)\t_sockaddr.pch" @COPTY@ /Fo"$(INTDIR)\\" /Fd"$(INTDIR)\\" /FD /c 
BSC32=bscmake.exe
BSC32_FLAGS=/nologo /o"$(OUTDIR)\t_sockaddr.bsc" 
BSC32_SBRS= \
	
LINK32=link.exe
LINK32_FLAGS=user32.lib advapi32.lib kernel32.lib ws2_32.lib ../../../../lib/isc/win32/Release/libisc.lib ../../../../lib/tests/win32/Release/libtests.lib $(LIBXML) /nologo /subsystem:console /incremental:no /pdb:"$(OUTDIR)\t_sockaddr.pdb" @MACHINE@ /out:"../../../../Build/Release/t_sockaddr.exe" 
LINK32_OBJS= \
	"$(INTDIR)\t_sockaddr.obj" \
	"..\..\..\..\lib\isc\win32\Release\libisc.lib" \
	"..\..\..\..\lib\tests\win32\Release\libtests.lib"

"..\..\..\..\Build\Release\t_sockaddr.exe" : "$(OUTDIR)" $(DEF_FILE) $(LINK32_OBJS)
    $(LINK32) @<<
  $(LINK32_FLAGS) $(LINK32_OBJS)
<<
    $(_VC_MANIFEST_EMBED_EXE)

!ELSEIF  "$(CFG)" == "t_sockaddr - @PLATFORM@ Debug"

OUTDIR=.\Debug
INTDIR=.\Debug
# Begin Custom Macros
OutDir=.\Debug
# End Custom Macros

!IF "$(RECURSE)" == "0" 

ALL : "..\..\..\..\Build\Debug\t_sockaddr.exe" "$(OUTDIR)\t_sockaddr.bsc"

!ELSE 

ALL : "libtests - @PLATFORM@ Debug" "libisc - @PLATFORM@ Debug" "..\..\..\..\Build\Debug\t_sockaddr.exe" "$(OUTDIR)\t_sockaddr.bsc"

!ENDIF 

!IF "$(RECURSE)" == "1" 
CLEAN :"libisc - @PLATFORM@ DebugCLEAN" "libtests - @PLATFORM@ DebugCLEAN"
!ELSE 
CLEAN :
!ENDIF 
	-@erase "$(INTDIR)\t_sockaddr.obj"
	-@erase "$(INTDIR)\t_sockaddr.sbr"
	-@erase "$(INTDIR)\vc60.idb"
	-@erase "$(INTDIR)\vc60.pdb"
	-@erase "$(OUTDIR)\t_sockaddr.bsc"
	-@erase "$(OUTDIR)\t_sockaddr.map"
	-@erase "$(OUTDIR)\t_sockaddr.pdb"
	-@erase "..\..\..\..\Build\Debug\t_sockaddr.exe"
	-@erase "..\..\..\..\Build\Debug\t_sockaddr.ilk"
	-@$(_VC_MANIFEST_CLEAN)

"$(OUTDIR)" :
    if not exist "$(OUTDIR)/$(NULL)" mkdir "$(OUTDIR)"

CPP_PROJ=/nologo /MDd /W3 /Gm @COPTX@ @COPTI@ /ZI /Od /I "./" /I "../../../../" @LIBXML2_INC@ /I "../../../../lib/isc/win32" /I "../../../../lib/isc/win32/include" /I "../../../../lib/isc/include" /I "../../../../lib/tests/include" /D "WIN32" /D "_DEBUG" /D "_CONSOLE" /D "_MBCS" /D "i386" /FR"$(INTDIR)\\" /Fo"$(INTDIR)\\" /Fd"$(INTDIR)\\" /FD /GZ /c 
BSC32=bscmake.exe
BSC32_FLAGS=/nologo /o"$(OUTDIR)\t_sockaddr.bsc" 
BSC32_SBRS= \
	"$(INTDIR)\t_sockaddr.sbr"

"$(OUTDIR)\t_sockaddr.bsc" : "$(OUTDIR)" $(BSC32_SBRS)
    $(BSC32) @<<
  $(BSC32_FLAGS) $(BSC32_SBRS)
<<

LINK32=link.exe
LINK32_FLAGS=user32.lib advapi32.lib kernel32.lib ws2_32.lib ../../../../lib/isc/win32/Debug/libisc.lib ../../../../lib/tests/win32/Debug/libtests.lib $(LIBXML) /nologo /subsystem:console /incremental:yes /pdb:"$(OUTDIR)\t_sockaddr.pdb" /map:"$(INTDIR)\t_sockaddr.map" /debug @MACHINE@ /out:"../../../../Build/Debug/t_sockaddr.exe" /pdbtype:sept 
LINK32_OBJS= \
	"$(INTDIR)\t_sockaddr.obj" \
	"..\..\..\..\lib\isc\win32\Debug\libisc.lib" \
	"..\..\..\..\lib\tests\win32\Debug\libtests.lib"

"..\..\..\..\Build\Debug\t_sockaddr.exe" : "$(OUTDIR)" $(DEF_FILE) $(LINK32_OBJS)
    $(LINK32) @<<
  $(LINK32_FLAGS) $(LINK32_OBJS)
<<
    $(_VC_MANIFEST_EMBED_EXE)

!ENDIF 

.c{$(INTDIR)}.obj::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.cpp{$(INTDIR)}.obj::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.cxx{$(INTDIR)}.obj::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.c{$(INTDIR)}.sbr::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.cpp{$(INTDIR)}.sbr::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.cxx{$(INTDIR)}.sbr::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<


!IF "$(NO_EXTERNAL_DEPS)" != "1"
!IF EXISTS("t_sockaddr.dep")
!INCLUDE "t_sockaddr.dep"
!ELSE 
!MESSAGE Warning: cannot find "t_sockaddr.dep"
!ENDIF 
!ENDIF 


!IF "$(CFG)" == "t_sockaddr - @PLATFORM@ Release" || "$(CFG)" == "t_sockaddr - @PLATFORM@ Debug"
SOURCE=..\t_sockaddr.c

!IF  "$(CFG)" == "t_sockaddr - @PLATFORM@ Release"


"$(INTDIR)\t_sockaddr.obj" : $(SOURCE) "$(INTDIR)"
	$(CPP) $(CPP_PROJ) $(SOURCE)


!ELSEIF  "$(CFG)" == "t_sockaddr - @PLATFORM@ Debug"


"$(INTDIR)\t_sockaddr.obj"	"$(INTDIR)\t_sockaddr.sbr" : $(SOURCE) "$(INTDIR)"
	$(CPP) $(CPP_PROJ) $(SOURCE)


!ENDIF 

!IF  "$(CFG)" == "t_sockaddr - @PLATFORM@ Release"

"libisc - @PLATFORM@ Release" : 
   cd "..\..\..\..\lib\isc\win32"
   $(MAKE) /$(MAKEFLAGS) /F ".\libisc.mak" CFG="libisc - @PLATFORM@ Release" 
   cd "..\..\..\bin\tests\sockaddr\win32"

"libisc - @PLATFORM@ ReleaseCLEAN" : 
   cd "..\..\..\..\lib\isc\win32"
   $(MAKE) /$(MAKEFLAGS) /F ".\libisc.mak" CFG="libisc - @PLATFORM@ Release" RECURSE=1 CLEAN 
   cd "..\..\..\bin\tests\sockaddr\win32"

!ELSEIF  "$(CFG)" == "t_sockaddr - @PLATFORM@ Debug"

"libisc - @PLATFORM@ Debug" : 
   cd "..\..\..\..\lib\isc\win32"
   $(MAKE) /$(MAKEFLAGS) /F ".\libisc.mak" CFG="libisc - @PLATFORM@ Debug" 
   cd "..\..\..\bin\tests\sockaddr\win32"

"libisc - @PLATFORM@ DebugCLEAN" : 
   cd "..\..\..\..\lib\isc\win32"
   $(MAKE) /$(MAKEFLAGS) /F ".\libisc.mak" CFG="libisc - @PLATFORM@ Debug" RECURSE=1 CLEAN 
   cd "..\..\..\bin\tests\sockaddr\win32"

!ENDIF 

!IF  "$(CFG)" == "t_sockaddr - @PLATFORM@ Release"

"libtests - @PLATFORM@ Release" : 
   cd "..\..\..\..\lib\tests\win32"
   $(MAKE) /$(MAKEFLAGS) /F ".\libtests.mak" CFG="libtests - @PLATFORM@ Release" 
   cd "..\..\..\bin\tests\sockaddr\win32"

"libtests - @PLATFORM@ ReleaseCLEAN" : 
   cd "..\..\..\..\lib\tests\win32"
   $(MAKE) /$(MAKEFLAGS) /F ".\libtests.mak" CFG="libtests - @PLATFORM@ Release" RECURSE=1 CLEAN 
   cd "..\..\..\bin\tests\sockaddr\win32"

!ELSEIF  "$(CFG)" == "t_sockaddr - @PLATFORM@ Debug"

"libtests - @PLATFORM@ Debug" : 
   cd "..\..\..\..\lib\tests\win32"
   $(MAKE) /$(MAKEFLAGS) /F ".\libtests.mak" CFG="libtests - @PLATFORM@ Debug" 
   cd "..\..\..\bin\tests\sockaddr\win32"

"libtests - @PLATFORM@ DebugCLEAN" : 
   cd "..\..\..\..\lib\tests\win32"
   $(MAKE) /$(MAKEFLAGS) /F ".\libtests.mak" CFG="libtests - @PLATFORM@ Debug" RECURSE=1 CLEAN 
   cd "..\..\..\bin\tests\sockaddr\win32"

!ENDIF 


!ENDIF 

####################################################
# Commands to generate initial empty manifest file and the RC file
# that references it, and for generating the .res file:

$(_VC_MANIFEST_BASENAME).auto.res : $(_VC_MANIFEST_BASENAME).auto.rc

$(_VC_MANIFEST_BASENAME).auto.rc : $(_VC_MANIFEST_BASENAME).auto.manifest
    type <<$@
#include <winuser.h>
1RT_MANIFEST"$(_VC_MANIFEST_BASENAME).auto.manifest"
<< KEEP

$(_VC_MANIFEST_BASENAME).auto.manifest :
    type <<$@
<?xml version='1.0' encoding='UTF-8' standalone='yes'?>
<assembly xmlns='urn:schemas-microsoft-com:asm.v1' manifestVersion='1.0'>
</assembly>
<< KEEP
