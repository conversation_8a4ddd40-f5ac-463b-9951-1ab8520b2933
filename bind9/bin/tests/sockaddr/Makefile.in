# Copyright (C) 2004, 2007, 2009, 2012, 2014  Internet Systems Consortium, Inc. ("ISC")
# Copyright (C) 1999-2002  Internet Software Consortium.
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
# REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
# AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
# INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
# LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
# OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.

# $Id$

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

CINCLUDES =	${TEST_INCLUDES} ${ISC_INCLUDES}

CDEFINES =
CWARNINGS =

ISCLIBS =	../../../lib/isc/libisc.@A@ @ISC_OPENSSL_LIBS@
TAPIDEPLIBS =	../../../lib/tests/libt_api.@A@

ISCDEPLIBS =	../../../lib/isc/libisc.@A@
TAPILIBS =	../../../lib/tests/libt_api.@A@

DEPLIBS =	${TAPIDEPLIBS} ${ISCDEPLIBS}

LIBS =		${TAPILIBS} ${ISCLIBS} ${ISCLIBS} @LIBS@

TARGETS =	t_sockaddr@EXEEXT@

SRCS =		t_sockaddr.c

@BIND9_MAKE_RULES@

t_sockaddr@EXEEXT@: t_sockaddr.@O@ ${DEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ t_sockaddr.@O@ ${LIBS}

test: t_sockaddr@EXEEXT@
	-@./t_sockaddr@EXEEXT@ -b @srcdir@ -a

testhelp:
	@./t_sockaddr@EXEEXT@ -h

clean distclean::
	rm -f ${TARGETS}
