adding name a.vix.com
adding name b.vix.com
adding name c.vix.com
vix.com. (black)
	++ BEG down from vix.com.
	b (black)
		a (RED from b)
			NULL
			NULL
		c (RED from b)
			NULL
			NULL
	-- END down from vix.com.
	NULL
	NULL
adding name a.b.c.d.e.f.vix.com
adding name b.b.c.d.e.f.vix.com
adding name c.b.c.d.e.f.vix.com
vix.com. (black)
	++ BEG down from vix.com.
	b (black)
		a (black from b)
			NULL
			NULL
		c (black from b)
			NULL
			b.c.d.e.f (RED from c)
				++ BEG down from b.c.d.e.f
				b (black)
					a (RED from b)
						NULL
						NULL
					c (RED from b)
						NULL
						NULL
				-- END down from b.c.d.e.f
				NULL
				NULL
	-- END down from vix.com.
	NULL
	NULL
adding name a.d.e.f.vix.com
adding name q.d.e.f.vix.com
adding name d.e.f.vix.com
vix.com. (black)
	++ BEG down from vix.com.
	b (black)
		a (black from b)
			NULL
			NULL
		c (black from b)
			NULL
			d.e.f (RED from c)
				++ BEG down from d.e.f
				b.c (black)
					++ BEG down from b.c
					b (black)
						a (RED from b)
							NULL
							NULL
						c (RED from b)
							NULL
							NULL
					-- END down from b.c
					a (RED from b.c)
						NULL
						NULL
					q (RED from b.c)
						NULL
						NULL
				-- END down from d.e.f
				NULL
				NULL
	-- END down from vix.com.
	NULL
	NULL
adding name g.h.vix.com
vix.com. (black)
	++ BEG down from vix.com.
	b (black)
		a (black from b)
			NULL
			NULL
		d.e.f (black from b)
			++ BEG down from d.e.f
			b.c (black)
				++ BEG down from b.c
				b (black)
					a (RED from b)
						NULL
						NULL
					c (RED from b)
						NULL
						NULL
				-- END down from b.c
				a (RED from b.c)
					NULL
					NULL
				q (RED from b.c)
					NULL
					NULL
			-- END down from d.e.f
			c (RED from d.e.f)
				NULL
				NULL
			g.h (RED from d.e.f)
				NULL
				NULL
	-- END down from vix.com.
	NULL
	NULL
searching for name q.d.e.f.vix.com ... found exact: q.d.e.f.vix.com.
searching for name just-parent.a.vix.com ... found parent: a.vix.com.
	(foundname: a.vix.com.)
searching for name no-real-parent.vix.com ... NOT FOUND!
searching for name does.not.exist.at.all ... NOT FOUND!
iterating forward
  new origin: .
vix.com
  new origin: vix.com.
a
b
c
d.e.f
  new origin: d.e.f.vix.com.
a
b.c
  new origin: b.c.d.e.f.vix.com.
a
b
c
  new origin: d.e.f.vix.com.
q
  new origin: vix.com.
g.h
iterating backward
  new origin: vix.com.
g.h
  new origin: d.e.f.vix.com.
q
  new origin: b.c.d.e.f.vix.com.
c
b
a
  new origin: d.e.f.vix.com.
b.c
a
  new origin: vix.com.
d.e.f
c
b
a
  new origin: .
vix.com
checking chain information for vix.com.
  found exact.  no data at node.
  name from dns_rbt_findnode: vix.com.
  name from dns_rbtnodechain_current: vix.com.
      (foundname = vix.com, origin = .)
  level_matches = 0, level_count = 0
checking chain information for zzz.com.
  name not found.  no data at node.
  name from dns_rbtnodechain_current: g.h.vix.com.
      (foundname = g.h, origin = vix.com.)
  level_matches = 0, level_count = 1
checking chain information for 0.vix.com.
  found parent.  no data at node.
  name from dns_rbt_findnode: vix.com.
  name from dns_rbtnodechain_current: vix.com.
      (foundname = vix.com, origin = .)
  level_matches = 0, level_count = 0
checking chain information for d.vix.com.
  found parent.  no data at node.
  name from dns_rbt_findnode: vix.com.
  name from dns_rbtnodechain_current: c.vix.com.
      (foundname = c, origin = vix.com.)
  level_matches = 0, level_count = 1
checking chain information for f.vix.com.
  found parent.  no data at node.
  name from dns_rbt_findnode: vix.com.
  name from dns_rbtnodechain_current: c.vix.com.
      (foundname = c, origin = vix.com.)
  level_matches = 0, level_count = 1
checking chain information for a.e.f.vix.com.
  found parent.  no data at node.
  name from dns_rbt_findnode: vix.com.
  name from dns_rbtnodechain_current: c.vix.com.
      (foundname = c, origin = vix.com.)
  level_matches = 0, level_count = 1
checking chain information for z.e.f.vix.com.
  found parent.  no data at node.
  name from dns_rbt_findnode: vix.com.
  name from dns_rbtnodechain_current: q.d.e.f.vix.com.
      (foundname = q, origin = d.e.f.vix.com.)
  level_matches = 0, level_count = 2
checking chain information for g.vix.com.
  found parent.  no data at node.
  name from dns_rbt_findnode: vix.com.
  name from dns_rbtnodechain_current: q.d.e.f.vix.com.
      (foundname = q, origin = d.e.f.vix.com.)
  level_matches = 0, level_count = 2
checking chain information for i.vix.com.
  found parent.  no data at node.
  name from dns_rbt_findnode: vix.com.
  name from dns_rbtnodechain_current: g.h.vix.com.
      (foundname = g.h, origin = vix.com.)
  level_matches = 0, level_count = 1
checking chain information for b.c.vix.com.
  found parent.  data at node: c.vix.com.
  name from dns_rbt_findnode: c.vix.com.
  name from dns_rbtnodechain_current: c.vix.com.
      (foundname = c, origin = vix.com.)
  level_matches = 1, level_count = 1
nuking name d.e.f.vix.com and its descendants
vix.com. (black)
	++ BEG down from vix.com.
	b (black)
		a (black from b)
			NULL
			NULL
		g.h (black from b)
			c (RED from g.h)
				NULL
				NULL
			NULL
	-- END down from vix.com.
	NULL
	NULL
adding name x.a.vix.com
adding name y.x.a.vix.com
vix.com. (black)
	++ BEG down from vix.com.
	b (black)
		a (black from b)
			++ BEG down from a
			x (black)
				++ BEG down from x
				y (black)
					NULL
					NULL
				-- END down from x
				NULL
				NULL
			-- END down from a
			NULL
			NULL
		g.h (black from b)
			c (RED from g.h)
				NULL
				NULL
			NULL
	-- END down from vix.com.
	NULL
	NULL
deleting name a.vix.com
deleting name x.a.vix.com
vix.com. (black)
	++ BEG down from vix.com.
	b (black)
		a (black from b)
			++ BEG down from a
			x (black)
				++ BEG down from x
				y (black)
					NULL
					NULL
				-- END down from x
				NULL
				NULL
			-- END down from a
			NULL
			NULL
		g.h (black from b)
			c (RED from g.h)
				NULL
				NULL
			NULL
	-- END down from vix.com.
	NULL
	NULL
deleting name b.vix.com
deleting name c.vix.com
vix.com. (black)
	++ BEG down from vix.com.
	g.h (black)
		a (RED from g.h)
			++ BEG down from a
			x (black)
				++ BEG down from x
				y (black)
					NULL
					NULL
				-- END down from x
				NULL
				NULL
			-- END down from a
			NULL
			NULL
		NULL
	-- END down from vix.com.
	NULL
	NULL
deleting name y.x.a.vix.com
vix.com. (black)
	++ BEG down from vix.com.
	g.h (black)
		a (RED from g.h)
			++ BEG down from a
			x (black)
				NULL
				NULL
			-- END down from a
			NULL
			NULL
		NULL
	-- END down from vix.com.
	NULL
	NULL
deleting name g.h.vix.com.
adding name \[b100000].vix.com.
adding name \[b010000].vix.com.
adding name \[b001000].vix.com.
adding name \[b000100].vix.com.
adding name \[b000010].vix.com.
adding name \[b000001].vix.com.
vix.com. (black)
	++ BEG down from vix.com.
	\[x80/6] (black)
		\[x0/1] (RED from \[x80/6])
			++ BEG down from \[x0/1]
			\[x80/5] (black)
				\[x0/1] (RED from \[x80/5])
					++ BEG down from \[x0/1]
					\[x8/4] (black)
						\[x0/1] (RED from \[x8/4])
							++ BEG down from \[x0/1]
							\[x8/3] (black)
								\[x0/1] (RED from \[x8/3])
									++ BEG down from \[x0/1]
									\[x8/2] (black)
										\[x4/2] (RED from \[x8/2])
											NULL
											NULL
										NULL
									-- END down from \[x0/1]
									NULL
									NULL
								NULL
							-- END down from \[x0/1]
							NULL
							NULL
						NULL
					-- END down from \[x0/1]
					NULL
					NULL
				NULL
			-- END down from \[x0/1]
			NULL
			NULL
		a (RED from \[x80/6])
			++ BEG down from a
			x (black)
				NULL
				NULL
			-- END down from a
			NULL
			NULL
	-- END down from vix.com.
	NULL
	NULL
searching for name \[b000100].vix.com. ... found exact: \[x10/6].vix.com.
adding name vix.com.
nuking name vix.com. and its descendants
adding name a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.
adding name b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.
b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w. (black)
	++ BEG down from b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.
	a (black)
		NULL
		NULL
	-- END down from b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.
	NULL
	NULL
adding name .
nuking name . and its descendants
adding name \[xFFFF/16].\[xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF/256].com
adding name \[xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF/128].com
\[xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF/128].com. (black)
	++ BEG down from \[xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF/128].com.
	\[xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF/144] (black)
		NULL
		NULL
	-- END down from \[xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF/128].com.
	NULL
	NULL
