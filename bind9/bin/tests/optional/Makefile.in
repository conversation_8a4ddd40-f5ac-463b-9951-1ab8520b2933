# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

CINCLUDES =	${DNS_INCLUDES} ${ISC_INCLUDES} ${ISCCFG_INCLUDES} \
		${LWRES_INCLUDES} @DST_OPENSSL_INC@ @DST_GSSAPI_INC@

CDEFINES =	@CRYPTO@ @USE_GSSAPI@

CWARNINGS =
BACKTRACECFLAGS = @BACKTRACECFLAGS@

DNSLIBS =	../../../lib/dns/libdns.@A@ @DNS_CRYPTO_LIBS@
ISCLIBS =	../../../lib/isc/libisc.@A@ @ISC_OPENSSL_LIBS@
ISCNOSYMLIBS =	../../../lib/isc/libisc-nosymtbl.@A@ @ISC_OPENSSL_LIBS@
ISCCFGLIBS = 	../../../lib/isccfg/libisccfg.@A@
LWRESDEPLIBS =	../../../lib/lwres/liblwres.@A@

DNSDEPLIBS =	../../../lib/dns/libdns.@A@
ISCDEPLIBS =	../../../lib/isc/libisc.@A@
ISCDEPNOSYMLIBS = ../../../lib/isc/libisc-nosymtbl.@A@
ISCCFGDEPLIBS = ../../../lib/isccfg/libisccfg.@A@
LWRESLIBS =	../../../lib/lwres/liblwres.@A@

LIBS =		@LIBS@

SUBDIRS =

# These programs are not built by default, but only when
# configured with --enable-developer or built explicitly with
# "make all_tests"

TARGETS =	@XTARGETS@
XTARGETS =	adb_test@EXEEXT@ \
		byaddr_test@EXEEXT@ \
		backtrace_test@EXEEXT@ \
		backtrace_test_nosymtbl@EXEEXT@ \
		byname_test@EXEEXT@ \
		db_test@EXEEXT@ \
		dst_test@EXEEXT@ \
		entropy_test@EXEEXT@ \
		entropy2_test@EXEEXT@ \
		gsstest@EXEEXT@ \
		gxba_test@EXEEXT@ \
		gxbn_test@EXEEXT@ \
		hash_test@EXEEXT@ \
		fsaccess_test@EXEEXT@ \
		inter_test@EXEEXT@ \
		keyboard_test@EXEEXT@ \
		lex_test@EXEEXT@ \
		lfsr_test@EXEEXT@ \
		log_test@EXEEXT@ \
		lwres_test@EXEEXT@ \
		lwresconf_test@EXEEXT@ \
		master_test@EXEEXT@ \
		mempool_test@EXEEXT@ \
		name_test@EXEEXT@ \
		nsecify@EXEEXT@ \
		ratelimiter_test@EXEEXT@ \
		rbt_test@EXEEXT@ \
		rwlock_test@EXEEXT@ \
		serial_test@EXEEXT@ \
		shutdown_test@EXEEXT@ \
		sig0_test@EXEEXT@ \
		sock_test@EXEEXT@ \
		sym_test@EXEEXT@ \
		task_test@EXEEXT@ \
		timer_test@EXEEXT@ \
		zone_test@EXEEXT@

SRCS =		${XSRCS}
XSRCS =		adb_test.c \
		byaddr_test.c \
		backtrace_test.c \
		byname_test.c \
		db_test.c \
		dst_test.c \
		entropy_test.c \
		entropy2_test.c \
		hash_test.c \
		fsaccess_test.c \
		gsstest.c \
		gxba_test.c \
		gxbn_test.c \
		inter_test.c \
		keyboard_test.c \
		lex_test.c \
		lfsr_test.c \
		log_test.c \
		lwres_test.c \
		lwresconf_test.c \
		master_test.c \
		mempool_test.c \
		name_test.c \
		nsecify.c \
		ratelimiter_test.c \
		rbt_test.c \
		rwlock_test.c \
		serial_test.c \
		shutdown_test.c \
		sig0_test.c \
		sock_test.c \
		sym_test.c \
		task_test.c \
		timer_test.c \
		zone_test.c

@BIND9_MAKE_RULES@

# disable optimization for backtrace test to get the expected result
BTTEST_CFLAGS =	${BACKTRACECFLAGS} ${EXT_CFLAGS} ${ALL_CPPFLAGS} -g \
		${ALWAYS_WARNINGS} ${STD_CWARNINGS} ${CWARNINGS}

all_tests: ${XTARGETS}

adb_test@EXEEXT@: adb_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ adb_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

backtrace_test_nosymtbl@EXEEXT@: ${srcdir}/backtrace_test.c ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${BTTEST_CFLAGS} ${LDFLAGS} -o $@ \
	${srcdir}/backtrace_test.c ${ISCLIBS} ${LIBS}

backtrace_test@EXEEXT@: ${srcdir}/backtrace_test.c backtrace_test_nosymtbl@EXEEXT@
	#first step: create a first symbol table
	rm -f symtbl.c
	if test X${MKSYMTBL_PROGRAM} != X; then \
	${MKSYMTBL_PROGRAM} ${top_srcdir}/util/mksymtbl.pl \
	backtrace_test_nosymtbl@EXEEXT@; else \
	cp ${top_srcdir}/lib/isc/backtrace-emptytbl.c symtbl.c; fi
	#second step: build a binary with the first symbol table
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${BTTEST_CFLAGS} ${LDFLAGS} \
	-o $@0 ${srcdir}/backtrace_test.c symtbl.c \
	${ISCNOSYMLIBS} ${LIBS}
	rm -f symtbl.c
	#third step: create a second symbol table
	if test X${MKSYMTBL_PROGRAM} != X; then \
	${MKSYMTBL_PROGRAM} ${top_srcdir}/util/mksymtbl.pl $@0; else \
	cp ${top_srcdir}/lib/isc/backtrace-emptytbl.c symtbl.c; fi
	#fourth step: build the final binary
	rm -f $@0
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${BTTEST_CFLAGS} ${LDFLAGS} \
	-o $@ ${srcdir}/backtrace_test.c symtbl.c ${ISCNOSYMLIBS} ${LIBS}
	rm -f symtbl.c

nsecify@EXEEXT@: nsecify.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ nsecify.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

byaddr_test@EXEEXT@: byaddr_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ byaddr_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

byname_test@EXEEXT@: byname_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ byname_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

lex_test@EXEEXT@: lex_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ lex_test.@O@ \
		${ISCLIBS} ${LIBS}

lfsr_test@EXEEXT@: lfsr_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ lfsr_test.@O@ \
		${ISCLIBS} ${LIBS}

log_test@EXEEXT@: log_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ log_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

name_test@EXEEXT@: name_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ name_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

hash_test@EXEEXT@: hash_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ hash_test.@O@ \
		${ISCLIBS} ${LIBS}

entropy_test@EXEEXT@: entropy_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ entropy_test.@O@ \
		${ISCLIBS} ${LIBS}

entropy2_test@EXEEXT@: entropy2_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ entropy2_test.@O@ \
		${ISCLIBS} ${LIBS}

sock_test@EXEEXT@: sock_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ sock_test.@O@ \
		${ISCLIBS} ${LIBS}

sym_test@EXEEXT@: sym_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ sym_test.@O@ \
		${ISCLIBS} ${LIBS}

task_test@EXEEXT@: task_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ task_test.@O@ \
		${ISCLIBS} ${LIBS}

shutdown_test@EXEEXT@: shutdown_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ shutdown_test.@O@ \
		${ISCLIBS} ${LIBS}

timer_test@EXEEXT@: timer_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ timer_test.@O@ \
		${ISCLIBS} ${LIBS}

ratelimiter_test@EXEEXT@: ratelimiter_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ ratelimiter_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

rbt_test@EXEEXT@: rbt_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ rbt_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

rwlock_test@EXEEXT@: rwlock_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ rwlock_test.@O@ \
		${ISCLIBS} ${LIBS}

master_test@EXEEXT@: master_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ master_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

db_test@EXEEXT@: db_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ db_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

mempool_test@EXEEXT@: mempool_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ mempool_test.@O@ \
		${ISCLIBS} ${LIBS}

serial_test@EXEEXT@: serial_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ serial_test.@O@ \
		${ISCLIBS} ${LIBS}

zone_test@EXEEXT@: zone_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ zone_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

fsaccess_test@EXEEXT@: fsaccess_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ fsaccess_test.@O@ \
		${ISCLIBS} ${LIBS}

inter_test@EXEEXT@: inter_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ inter_test.@O@ \
		${ISCLIBS} ${LIBS}

keyboard_test@EXEEXT@: keyboard_test.@O@ ${ISCDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ keyboard_test.@O@ \
		${ISCLIBS} ${LIBS}

lwresconf_test@EXEEXT@: lwresconf_test.@O@ ${ISCDEPLIBS} ${LWRESDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ lwresconf_test.@O@ \
		${LWRESLIBS} ${ISCLIBS} ${LIBS}

lwres_test@EXEEXT@: lwres_test.@O@ ${ISCDEPLIBS} ${LWRESDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ lwres_test.@O@ \
		${LWRESLIBS} ${ISCLIBS} ${LIBS}

gxbn_test@EXEEXT@: gxbn_test.@O@ ${LWRESDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ gxbn_test.@O@ \
		${LWRESLIBS} ${ISCLIBS} ${LIBS}

gxba_test@EXEEXT@: gxba_test.@O@ ${LWRESDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ gxba_test.@O@ \
		${LWRESLIBS} ${ISCLIBS} ${LIBS}

sig0_test@EXEEXT@: sig0_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} -o $@ sig0_test.@O@ \
		${DNSLIBS} ${ISCLIBS} ${LIBS}

dst_test@EXEEXT@: dst_test.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} \
		-o $@ dst_test.@O@ ${DNSLIBS} ${ISCLIBS} ${LIBS}

gsstest@EXEEXT@: gsstest.@O@ ${ISCDEPLIBS} ${DNSDEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${CFLAGS} ${LDFLAGS} \
		-o $@ gsstest.@O@ ${DNSLIBS} ${ISCLIBS} ${LIBS}

clean distclean::
	rm -f ${TARGETS} ${XTARGETS}
	rm -f backtrace_test_symtbl.c

check: test

test:
	@for dir in $(TESTDIRS) ;\
	do \
		( cd $$dir; $(MAKE) test ) ;\
	done
