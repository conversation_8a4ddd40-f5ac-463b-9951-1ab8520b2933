# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

add a.vix.com
add b.vix.com
add c.vix.com
print
add a.b.c.d.e.f.vix.com
add b.b.c.d.e.f.vix.com
add c.b.c.d.e.f.vix.com
print
add a.d.e.f.vix.com
add q.d.e.f.vix.com
add d.e.f.vix.com
print
add g.h.vix.com
print
search q.d.e.f.vix.com
search just-parent.a.vix.com
search no-real-parent.vix.com
search does.not.exist.at.all
forward
backward
# existing name
check vix.com.
# greater than stop node, which has down pointer
check zzz.com.
# less than lowest in level (would be left link from stop node)
check 0.vix.com
# greater than stop node, no down pointer
check d.vix.com
# superdomain stored in existing node
check f.vix.com
# common ancestor stored in existing node; existing is successor
check a.e.f.vix.com
# common ancestor stored in existing node; existing is less but not predecessor
check z.e.f.vix.com
#
check g.vix.com
#
check i.vix.com
#
check b.c.vix.com
nuke d.e.f.vix.com
print
add x.a.vix.com
add y.x.a.vix.com
print
delete a.vix.com
delete x.a.vix.com
print
delete b.vix.com
delete c.vix.com
print
delete y.x.a.vix.com
print
delete g.h.vix.com.
add \[b100000].vix.com.
add \[b010000].vix.com.
add \[b001000].vix.com.
add \[b000100].vix.com.
add \[b000010].vix.com.
add \[b000001].vix.com.
p
search \[b000100].vix.com.
# zap the entire tree
add vix.com.
nuke vix.com.
add a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.
add b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.
print
add .
# zap it again
nuke .
# test splitting of maximal bitstring
add \[xFFFF/16].\[xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF/256].com
add \[xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF/128].com
print
quit
