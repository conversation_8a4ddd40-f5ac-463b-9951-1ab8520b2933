# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

PROVIDER =	@PKCS11_PROVIDER@

CINCLUDES =	${ISC_INCLUDES}

CDEFINES =	-DPK11_LIB_LOCATION=\"${PROVIDER}\"

ISCLIBS =	../../../../lib/isc/libisc.@A@ @ISC_OPENSSL_LIBS@

LIBS =		${ISCLIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}
include ../../../../Makefile-infoblox.inc

SUBDIRS =

TARGETS =	session@EXEEXT@ login@EXEEXT@ random@EXEEXT@ \
		sha1@EXEEXT@ create@EXEEXT@ find@EXEEXT@ \
		pubrsa@EXEEXT@ privrsa@EXEEXT@ genrsa@EXEEXT@ \
		sign@EXEEXT@ verify@EXEEXT@

SRCS =		session.c login.c random.c sha1.c create.c find.c \
		pubrsa.c privrsa.c genrsa.c sign.c verify.c

@BIND9_MAKE_RULES@

session@EXEEXT@: @srcdir@/session.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/session.c ${LIBS}

login@EXEEXT@: @srcdir@/login.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/login.c ${LIBS}

random@EXEEXT@: @srcdir@/random.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/random.c ${LIBS}

sha1@EXEEXT@: @srcdir@/sha1.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/sha1.c ${LIBS}

create@EXEEXT@: @srcdir@/create.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/create.c ${LIBS}

find@EXEEXT@: @srcdir@/find.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/find.c ${LIBS}

pubrsa@EXEEXT@: @srcdir@/pubrsa.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/pubrsa.c ${LIBS}

privrsa@EXEEXT@: @srcdir@/privrsa.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/privrsa.c ${LIBS}

genrsa@EXEEXT@: @srcdir@/genrsa.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/genrsa.c ${LIBS}

sign@EXEEXT@: @srcdir@/sign.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/sign.c ${LIBS}

verify@EXEEXT@: @srcdir@/verify.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/verify.c ${LIBS}

clean distclean::
	rm -f ${TARGETS}
