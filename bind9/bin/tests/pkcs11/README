Copyright (C) Internet Systems Consortium, Inc. ("ISC")

See COPYRIGHT in the source root or http://isc.org/copyright.html for terms.

"pkcs11-hmacmd5" is here to check for the presence of a known bug in
the Thales nCipher PKCS#11 provider library.  To test for the bug, use
pkcs11-hmacmd5 to hash a test vector from RFC 2104, and determine
whether the resulting digest is is correct.  For instance:

    echo -n "Hi There" | \
        ./pkcs11-hmacmd5 -p <PIN> -k '0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b'

...must return "9294727a3638bb1c13f48ef8158bfc9d".

If any other value is returned, then the provider library is buggy,
and theflag PK11_MD5_HMAC_REPLACE must be defined in
lib/isc/include/pk11/site.h
However, if the correct value is returned, then it is safe to turn
off PK11_MD5_HMAC_REPLACE. (It is on by default.)
