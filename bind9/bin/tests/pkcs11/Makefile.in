# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

PROVIDER =	@PKCS11_PROVIDER@

CINCLUDES =	${ISC_INCLUDES}

CDEFINES =	-DPK11_LIB_LOCATION=\"${PROVIDER}\"

ISCLIBS =	../../../lib/isc/libisc.@A@ @ISC_OPENSSL_LIBS@

LIBS =		${ISCLIBS} @LIBS@ \
		${INFOBLOX_LIB_PATHS} ${INFOBLOX_LIBS}
include ../../../Makefile-infoblox.inc

SUBDIRS =	benchmarks

TARGETS =	pkcs11-md5sum@EXEEXT@ pkcs11-hmacmd5@EXEEXT@
SRCS =		pkcs11-md5sum.c pkcs11-hmacmd5.c

@BIND9_MAKE_RULES@

pkcs11-md5sum@EXEEXT@: @srcdir@/pkcs11-md5sum.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/pkcs11-md5sum.c ${LIBS}

pkcs11-hmacmd5@EXEEXT@: @srcdir@/pkcs11-hmacmd5.c
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ @srcdir@/pkcs11-hmacmd5.c ${LIBS}

test:

clean distclean::
	rm -f ${TARGETS}
