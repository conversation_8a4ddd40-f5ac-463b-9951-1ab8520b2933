/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

controls { /* empty */ };

acl accept { *********; };

acl badaccept { *********; };

key one {
	algorithm hmac-md5;
	secret "1234abcd8765";
};

key two {
	algorithm hmac-md5;
	secret "1234efgh8765";
};

options {
	port @PORT@;
	pid-file "named.pid";
	listen-on { *********; };
	listen-on-v6 { none; };
	recursion no;
};

include "controls.conf";

zone "." {
	type hint;
	file "../../common/root.hint";
};

zone "normal.example" {
	type master;
	file "generic.db";
};

zone "any.example" {
	type master;
	file "generic.db";
	allow-query { any; };
};

zone "none.example" {
	type master;
	file "generic.db";
	allow-query { none; };
};

zone "addrallow.example" {
	type master;
	file "generic.db";
	allow-query { *********; };
};

zone "addrnotallow.example" {
	type master;
	file "generic.db";
	allow-query { *********; };
};

zone "addrdisallow.example" {
	type master;
	file "generic.db";
	allow-query { ! *********; };
};

zone "aclallow.example" {
	type master;
	file "generic.db";
	allow-query { accept; };
};

zone "aclnotallow.example" {
	type master;
	file "generic.db";
	allow-query { badaccept; };
};

zone "acldisallow.example" {
	type master;
	file "generic.db";
	allow-query { ! accept; };
};

/* Also usable for testing key not allowed */
zone "keyallow.example" {
	type master;
	file "generic.db";
	allow-query { key one; };
};

zone "keydisallow.example" {
	type master;
	file "generic.db";
	allow-query { ! key one; };
};
