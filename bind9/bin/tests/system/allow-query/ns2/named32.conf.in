/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

controls { /* empty */ };

key one {
	algorithm hmac-md5;
	secret "1234abcd8765";
};

options {
	port @PORT@;
	pid-file "named.pid";
	listen-on { *********; };
	listen-on-v6 { none; };
	recursion no;
};

include "controls.conf";

view "internal" {

	allow-query {! key one; };

	zone "." {
		type hint;
		file "../../common/root.hint";
	};

	zone "normal.example" {
		type master;
		file "generic.db";
	};
};
