/*
 * Copyright (C) 2013  Internet Systems Consortium, Inc. ("ISC")
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
 * OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */

/* $Id: named.conf,v 1.5 2007/06/19 23:47:06 tbox Exp $ */

options {
	query-source address *********;
	notify-source *********;
	transfer-source *********;
	recursion no;
	additional-from-auth no;
	port 5300;
	pid-file "named.pid";
	listen-on { *********; };
	listen-on-v6 { none; };
	notify no;
	minimal-responses no;
};

include "../../common/rndc.key";

controls {
	inet ********* port 9953 allow { any; } keys { rndc_key; };
};

zone "rt.example" {
	type master;
	file "rt.db";
};

zone "naptr.example" {
	type master;
	file "naptr.db";
};

zone "rt2.example" {
	type master;
	file "rt2.db";
};

zone "naptr2.example" {
	type master;
	file "naptr2.db";
};

zone "nid.example" {
	type master;
	file "nid.db";
};
