/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

options {
	query-source address *********;
	notify-source *********;
	transfer-source *********;
	recursion no;
	additional-from-auth no;
	port @PORT@;
	pid-file "named.pid";
	listen-on { *********; };
	listen-on-v6 { none; };
	notify no;
	minimal-any yes;
	minimal-responses no-auth;
};

include "../../common/rndc.key";

controls {
	inet ********* port @CONTROLPORT@ allow { any; } keys { rndc_key; };
};

zone "rt.example" {
	type master;
	file "rt.db";
};

zone "naptr.example" {
	type master;
	file "naptr.db";
};

zone "rt2.example" {
	type master;
	file "rt2.db";
};

zone "naptr2.example" {
	type master;
	file "naptr2.db";
};

zone "nid.example" {
	type master;
	file "nid.db";
};
