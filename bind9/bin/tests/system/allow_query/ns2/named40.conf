/*
 * Copyright (C) 2010  Internet Systems Consortium, Inc. ("ISC")
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
 * OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */

/* $Id: named40.conf,v 1.2 2010/11/16 01:37:39 sar Exp $ */

controls { /* empty */ };

acl accept { 10.53.0.2; }; 

acl badaccept { 10.53.0.1; }; 

key one {
        algorithm hmac-md5;
        secret "1234abcd8765";
};

key two {
        algorithm hmac-md5;
        secret "1234efgh8765";
};

options {
	port 5300;
	pid-file "named.pid";
	listen-on { 10.53.0.2; };
	listen-on-v6 { none; };
        recursion no;
};

include "../../common/controls.conf";

zone "." {
	type hint;
	file "../../common/root.hint";
};

zone "normal.example" {
	type master;
	file "normal.db";
};

zone "any.example" {
	type master;
	file "any.db";
	allow-query { any; };
};

zone "none.example" {
	type master;
	file "none.db";
	allow-query { none; };
};

zone "addrallow.example" {
	type master;
	file "addrallow.db";
	allow-query { 10.53.0.2; };
};

zone "addrnotallow.example" {
	type master;
	file "addrnotallow.db";
	allow-query { 10.53.0.1; };
};

zone "addrdisallow.example" {
	type master;
	file "addrdisallow.db";
	allow-query { ! 10.53.0.2; };
};

zone "aclallow.example" {
	type master;
	file "aclallow.db";
	allow-query { accept; };
};

zone "aclnotallow.example" {
	type master;
	file "aclnotallow.db";
	allow-query { badaccept; };
};

zone "acldisallow.example" {
	type master;
	file "acldisallow.db";
	allow-query { ! accept; };
};

/* Also usable for testing key not allowed */
zone "keyallow.example" {
	type master;
	file "keyallow.db";
	allow-query { key one; };
};

zone "keydisallow.example" {
	type master;
	file "keydisallow.db";
	allow-query { ! key one; };
};


