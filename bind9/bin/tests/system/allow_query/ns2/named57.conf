/*
 * Copyright (C) 2013  Internet Systems Consortium, Inc. ("ISC")
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
 * OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */

controls { /* empty */ };

options {
	port 5300;
	pid-file "named.pid";
	listen-on { 10.53.0.2; };
	listen-on-v6 { none; };
	recursion no;
};

include "../../common/controls.conf";

view "internal" {
	allow-query-on { any; };

	zone "." {
		type hint;
		file "../../common/root.hint";
	};

	zone "normal.example" {
		type master;
		file "normal.db";
	};

	zone "aclnotallow.example" {
		type master;
		file "aclnotallow.db";
		allow-query-on { none; };
	};
};
