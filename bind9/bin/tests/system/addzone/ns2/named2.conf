/*
 * Copyright (C) 2010, 2011  Internet Systems Consortium, Inc. ("ISC")
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
 * OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */

/* $Id: named2.conf,v 1.5 2011/06/17 23:47:49 tbox Exp $ */

controls { /* empty */ };

include "../../common/controls.conf";

options {
	port 5300;
	pid-file "named.pid";
	listen-on { *********; *********; };
	listen-on-v6 { none; };
	recursion no;
};

view internal {
	match-clients { *********; };
	allow-new-zones no;
        recursion yes;

	zone "." {
		type hint;
		file "../../common/root.hint";
	};
};

view external {
	match-clients { any; };
	allow-new-zones yes;

	zone "." {
		type hint;
		file "../../common/root.hint";
	};
};

# This view is only here to test that configuration context is cleaned
# up correctly when using multiple named ACLs (regression test for RT #22739)
acl match { none; };
acl nobody { none; };
view extra {
        match-clients { match; };
        allow-new-zones yes;
        allow-transfer { nobody; };
        allow-query { nobody; };
        allow-recursion { nobody; };
};
