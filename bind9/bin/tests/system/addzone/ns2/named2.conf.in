/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

include "../../common/rndc.key";

controls {
	inet ********* port @CONTROLPORT@ allow { any; } keys { rndc_key; };
};


options {
	port @PORT@;
	pid-file "named.pid";
	listen-on { *********; *********; };
	listen-on-v6 { none; };
	recursion no;
};

view internal {
	match-clients { *********; };
	allow-new-zones no;
	recursion yes;

	response-policy { zone "policy"; };

	zone "." {
		type hint;
		file "../../common/root.hint";
	};

	zone "policy" {
		type master;
		file "normal.db";
	};
};

view external {
	match-clients { any; };
	allow-new-zones yes;

	zone "." {
		type hint;
		file "../../common/root.hint";
	};
};

# This view is only here to test that configuration context is cleaned
# up correctly when using multiple named ACLs (regression test for RT #22739)
acl match { none; };
acl nobody { none; };
view extra {
	match-clients { match; };
	allow-new-zones yes;
	allow-transfer { nobody; };
	allow-query { nobody; };
	allow-recursion { nobody; };
};
