/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

options {
	query-source address *********;
	notify-source *********;
	transfer-source *********;
	port @PORT@;
	pid-file "named.pid";
	listen-on { *********; };
	listen-on-v6 { none; };
	recursion no;
	notify no;
	allow-new-zones yes;
	allow-transfer { none; };
};

controls {
	inet ********* port @CONTROLPORT@ allow { any; } keys { rndc_key; };
};

key rndc_key {
	secret "1234abcd8765";
	algorithm hmac-sha256;
};

zone "existing" {
	type master;
	file "existing.db";
};
