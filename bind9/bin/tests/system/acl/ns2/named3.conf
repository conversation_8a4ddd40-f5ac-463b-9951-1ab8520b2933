/*
 * Copyright (C) 2008  Internet Systems Consortium, Inc. ("ISC")
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
 * OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */

/* $Id: named3.conf,v 1.2 2008/01/10 01:10:01 marka Exp $ */

controls { /* empty */ };

options {
	query-source address *********;
	notify-source *********;
	transfer-source *********;
	port 5300;
	pid-file "named.pid";
	listen-on { *********; };
	listen-on-v6 { none; };
	recursion no;
	notify yes;
	ixfr-from-differences yes;
	check-integrity no;
};

include "../../common/controls.conf";

key one {
        algorithm hmac-md5;
        secret "1234abcd8765";
};

key two {
        algorithm hmac-md5;
        secret "1234abcd8765";
};

key three {
        algorithm hmac-md5;
        secret "1234abcd8765";
};

acl reject {
        !key one; !key two; any;
};

acl accept {
        *********; *********;
};

zone "." {
	type hint;
	file "../../common/root.hint";
};

zone "example" {
	type master;
	file "example.db";
};

zone "tsigzone" {
	type master;
	file "tsigzone.db";
        allow-transfer { !reject; accept; };
};
