/*
 * Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * See the COPYRIGHT file distributed with this work for additional
 * information regarding copyright ownership.
 */

key rndc_key {
	secret "1234abcd8765";
	algorithm hmac-sha256;
};

controls {
	inet ********* port @CONTROLPORT@ allow { any; } keys { rndc_key; };
};

options {
	query-source address *********;
	notify-source *********;
	transfer-source *********;
	port @PORT@;
	pid-file "named.pid";
	listen-on { *********; *********; };
	listen-on-v6 { none; };
	recursion yes;
	notify yes;
	ixfr-from-differences yes;
	check-integrity no;
	allow-query-on { *********; };
	ecs-forward { *********; none; };
};

zone "." {
	type hint;
	file "../../common/root.hint";
};

zone "example" {
	type master;
	file "example.db";
};

zone "tsigzone" {
	type master;
	file "tsigzone.db";
	allow-transfer { ecs 10.53/16; !10/8; };
};
