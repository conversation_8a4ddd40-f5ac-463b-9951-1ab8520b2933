/*
 * Copyright (C) 2009, 2013  Internet Systems Consortium, Inc. ("ISC")
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND ISC DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS.  IN NO EVENT SHALL ISC BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE
 * OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */

/* $Id: named.conf,v 1.3 2009/11/30 23:48:02 tbox Exp $ */

// NS1

controls { /* empty */ };

options {
	query-source address *********;
	notify-source *********;
	transfer-source *********;
	port 5300;
	pid-file "named.pid";
	listen-on { *********; };
	listen-on-v6 { none; };
	recursion no;
	notify yes;
	dnssec-enable yes;
	dnssec-validation yes;
};

key rndc_key {
	secret "1234abcd8765";
	algorithm hmac-sha256;
};

controls {
	inet ********* port 9953 allow { any; } keys { rndc_key; };
};

zone "." {
	type master;
	file "root.db";
	allow-transfer { any; };
	allow-query { any; };
	allow-update { any; };
	auto-dnssec maintain;
};

include "trusted.conf";
