.\" Copyright (C) 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: pkcs11-destroy
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-01-15
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "PKCS11\-DESTROY" "8" "2014\-01\-15" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
pkcs11-destroy \- destroy PKCS#11 objects
.SH "SYNOPSIS"
.HP \w'\fBpkcs11\-destroy\fR\ 'u
\fBpkcs11\-destroy\fR [\fB\-m\ \fR\fB\fImodule\fR\fR] [\fB\-s\ \fR\fB\fIslot\fR\fR] {\-i\ \fIID\fR | \-l\ \fIlabel\fR} [\fB\-p\ \fR\fB\fIPIN\fR\fR] [\fB\-w\ \fR\fB\fIseconds\fR\fR]
.SH "DESCRIPTION"
.PP
\fBpkcs11\-destroy\fR
destroys keys stored in a PKCS#11 device, identified by their
\fBID\fR
or
\fBlabel\fR\&.
.PP
Matching keys are displayed before being destroyed\&. By default, there is a five second delay to allow the user to interrupt the process before the destruction takes place\&.
.SH "ARGUMENTS"
.PP
\-m \fImodule\fR
.RS 4
Specify the PKCS#11 provider module\&. This must be the full path to a shared library object implementing the PKCS#11 API for the device\&.
.RE
.PP
\-s \fIslot\fR
.RS 4
Open the session with the given PKCS#11 slot\&. The default is slot 0\&.
.RE
.PP
\-i \fIID\fR
.RS 4
Destroy keys with the given object ID\&.
.RE
.PP
\-l \fIlabel\fR
.RS 4
Destroy keys with the given label\&.
.RE
.PP
\-p \fIPIN\fR
.RS 4
Specify the PIN for the device\&. If no PIN is provided on the command line,
\fBpkcs11\-destroy\fR
will prompt for it\&.
.RE
.PP
\-w \fIseconds\fR
.RS 4
Specify how long to pause before carrying out key destruction\&. The default is five seconds\&. If set to
0, destruction will be immediate\&.
.RE
.SH "SEE ALSO"
.PP
\fBpkcs11-keygen\fR(8),
\fBpkcs11-list\fR(8),
\fBpkcs11-tokens\fR(8)
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
