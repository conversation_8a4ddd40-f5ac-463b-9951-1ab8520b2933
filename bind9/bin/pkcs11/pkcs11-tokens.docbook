<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.pkcs11-tokens">
  <info>
    <date>2014-01-15</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>pkcs11-tokens</application></refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>pkcs11-tokens</application></refname>
    <refpurpose>list PKCS#11 available tokens</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>pkcs11-tokens</command>
      <arg choice="opt" rep="norepeat"><option>-m <replaceable class="parameter">module</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-v</option></arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para>
      <command>pkcs11-tokens</command>
      lists the PKCS#11 available tokens with defaults from the slot/token
      scan performed at application initialization.
    </para>
  </refsection>

  <refsection><info><title>ARGUMENTS</title></info>

    <variablelist>
      <varlistentry>
        <term>-m <replaceable class="parameter">module</replaceable></term>
        <listitem>
          <para>
            Specify the PKCS#11 provider module.  This must be the full
            path to a shared library object implementing the PKCS#11 API
            for the device.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-v</term>
        <listitem>
          <para>
            Make the PKCS#11 libisc initialization verbose.
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para>
      <citerefentry>
        <refentrytitle>pkcs11-destroy</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>pkcs11-keygen</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>pkcs11-list</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>
    </para>
  </refsection>

</refentry>
