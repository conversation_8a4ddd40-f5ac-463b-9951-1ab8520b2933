<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--
 - Copyright (C) 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
 - 
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
-->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>pkcs11-destroy</title>
<meta name="generator" content="DocBook XSL Stylesheets V1.78.1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="refentry">
<a name="man.pkcs11-destroy"></a><div class="titlepage"></div>
  
  

  

  <div class="refnamediv">
<h2>Name</h2>
<p>
    <span class="application">pkcs11-destroy</span>
     &#8212; destroy PKCS#11 objects
  </p>
</div>

  

  <div class="refsynopsisdiv">
<h2>Synopsis</h2>
    <div class="cmdsynopsis"><p>
      <code class="command">pkcs11-destroy</code> 
       [<code class="option">-m <em class="replaceable"><code>module</code></em></code>]
       [<code class="option">-s <em class="replaceable"><code>slot</code></em></code>]
       {
         -i <em class="replaceable"><code>ID</code></em> 
         |   -l <em class="replaceable"><code>label</code></em> 
      }
       [<code class="option">-p <em class="replaceable"><code>PIN</code></em></code>]
       [<code class="option">-w <em class="replaceable"><code>seconds</code></em></code>]
    </p></div>
  </div>

  <div class="refsection">
<a name="id-1.7"></a><h2>DESCRIPTION</h2>

    <p>
      <span class="command"><strong>pkcs11-destroy</strong></span> destroys keys stored in a
      PKCS#11 device, identified by their <code class="option">ID</code> or
      <code class="option">label</code>.
    </p>
    <p>
      Matching keys are displayed before being destroyed.  By default,
      there is a five second delay to allow the user to interrupt the
      process before the destruction takes place.
    </p>
  </div>

  <div class="refsection">
<a name="id-1.8"></a><h2>ARGUMENTS</h2>

    <div class="variablelist"><dl class="variablelist">
<dt><span class="term">-m <em class="replaceable"><code>module</code></em></span></dt>
<dd>
          <p>
            Specify the PKCS#11 provider module.  This must be the full
            path to a shared library object implementing the PKCS#11 API
            for the device.
          </p>
        </dd>
<dt><span class="term">-s <em class="replaceable"><code>slot</code></em></span></dt>
<dd>
          <p>
            Open the session with the given PKCS#11 slot.  The default is
            slot 0.
          </p>
        </dd>
<dt><span class="term">-i <em class="replaceable"><code>ID</code></em></span></dt>
<dd>
          <p>
            Destroy keys with the given object ID.
          </p>
        </dd>
<dt><span class="term">-l <em class="replaceable"><code>label</code></em></span></dt>
<dd>
          <p>
            Destroy keys with the given label.
          </p>
        </dd>
<dt><span class="term">-p <em class="replaceable"><code>PIN</code></em></span></dt>
<dd>
          <p>
            Specify the PIN for the device.  If no PIN is provided on the
            command line, <span class="command"><strong>pkcs11-destroy</strong></span> will prompt for it.
          </p>
        </dd>
<dt><span class="term">-w <em class="replaceable"><code>seconds</code></em></span></dt>
<dd>
          <p>
            Specify how long to pause before carrying out key destruction.
            The default is five seconds.  If set to <code class="literal">0</code>,
            destruction will be immediate.
          </p>
        </dd>
</dl></div>
  </div>

  <div class="refsection">
<a name="id-1.9"></a><h2>SEE ALSO</h2>

    <p>
      <span class="citerefentry">
        <span class="refentrytitle">pkcs11-keygen</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">pkcs11-list</span>(8)
      </span>,
      <span class="citerefentry">
        <span class="refentrytitle">pkcs11-tokens</span>(8)
      </span>
    </p>
  </div>

</div></body>
</html>
