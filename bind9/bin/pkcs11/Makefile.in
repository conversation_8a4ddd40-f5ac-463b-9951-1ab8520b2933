# Copyright (C) Internet Systems Consortium, Inc. ("ISC")
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# See the COPYRIGHT file distributed with this work for additional
# information regarding copyright ownership.

# $Id: Makefile.in,v 1.2 2009/10/05 12:07:08 fdupont Exp $

srcdir =	@srcdir@
VPATH =		@srcdir@
top_srcdir =	@top_srcdir@

@BIND9_MAKE_INCLUDES@

CINCLUDES =	${ISC_INCLUDES}

CDEFINES =

ISCLIBS =	../../lib/isc/libisc.@A@ @ISC_OPENSSL_LIBS@

ISCDEPLIBS =	../../lib/isc/libisc.@A@

DEPLIBS =	${ISCDEPLIBS}

# if FORCE_STATIC_PROVIDER: LIBS += ${PROVIDER}
LIBS =		${ISCLIBS} @LIBS@

SUBDIRS =	benchmarks

TARGETS =	pkcs11-list@EXEEXT@ pkcs11-destroy@EXEEXT@ \
		pkcs11-keygen@EXEEXT@ pkcs11-tokens@EXEEXT@
SRCS =		pkcs11-list.c pkcs11-destroy.c \
		pkcs11-keygen.c pkcs11-tokens.c
OBJS =		pkcs11-list.@O@ pkcs11-destroy.@O@ \
		pkcs11-keygen.@O@ pkcs11-tokens.@O@


MANPAGES =	pkcs11-list.8 pkcs11-destroy.8 \
		pkcs11-keygen.8 pkcs11-tokens.8
HTMLPAGES =	pkcs11-list.html pkcs11-destroy.html \
		pkcs11-keygen.html pkcs11-tokens.html
MANOBJS =	${MANPAGES} ${HTMLPAGES}

@BIND9_MAKE_RULES@

pkcs11-list@EXEEXT@: pkcs11-list.@O@ ${DEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ pkcs11-list.@O@ ${LIBS}

pkcs11-destroy@EXEEXT@: pkcs11-destroy.@O@ ${DEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ pkcs11-destroy.@O@ ${LIBS}

pkcs11-keygen@EXEEXT@: pkcs11-keygen.@O@ ${DEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ pkcs11-keygen.@O@ ${LIBS}

pkcs11-tokens@EXEEXT@: pkcs11-tokens.@O@ ${DEPLIBS}
	${LIBTOOL_MODE_LINK} ${PURIFY} ${CC} ${ALL_CFLAGS} ${LDFLAGS} \
		-o $@ pkcs11-tokens.@O@ ${LIBS}

doc man:: ${MANOBJS}

docclean manclean maintainer-clean::
	rm -f ${MANOBJS}

installdirs:
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${sbindir}
	$(SHELL) ${top_srcdir}/mkinstalldirs ${DESTDIR}${mandir}/man8

install:: ${TARGETS} installdirs
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} pkcs11-list@EXEEXT@ \
		${DESTDIR}${sbindir}
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} pkcs11-destroy@EXEEXT@ \
		${DESTDIR}${sbindir}
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} pkcs11-keygen@EXEEXT@ \
		${DESTDIR}${sbindir}
	${LIBTOOL_MODE_INSTALL} ${INSTALL_PROGRAM} pkcs11-tokens@EXEEXT@ \
		${DESTDIR}${sbindir}
	${INSTALL_DATA} ${srcdir}/pkcs11-list.8 ${DESTDIR}${mandir}/man8
	${INSTALL_DATA} ${srcdir}/pkcs11-destroy.8 ${DESTDIR}${mandir}/man8
	${INSTALL_DATA} ${srcdir}/pkcs11-keygen.8 ${DESTDIR}${mandir}/man8
	${INSTALL_DATA} ${srcdir}/pkcs11-tokens.8 ${DESTDIR}${mandir}/man8

uninstall::
	rm -f ${DESTDIR}${mandir}/man8/pkcs11-tokens.8
	rm -f ${DESTDIR}${mandir}/man8/pkcs11-keygen.8
	rm -f ${DESTDIR}${mandir}/man8/pkcs11-destroy.8
	rm -f ${DESTDIR}${mandir}/man8/pkcs11-list.8
	${LIBTOOL_MODE_UNINSTALL} rm -f /
		${DESTDIR}${sbindir}/pkcs11-tokens@EXEEXT@
	${LIBTOOL_MODE_UNINSTALL} rm -f /
		${DESTDIR}${sbindir}/pkcs11-keygen@EXEEXT@
	${LIBTOOL_MODE_UNINSTALL} rm -f /
		${DESTDIR}${sbindir}/pkcs11-destroy@EXEEXT@
	${LIBTOOL_MODE_UNINSTALL} rm -f /
		${DESTDIR}${sbindir}/pkcs11-list@EXEEXT@

clean distclean::
	rm -f ${OBJS} ${TARGETS}
