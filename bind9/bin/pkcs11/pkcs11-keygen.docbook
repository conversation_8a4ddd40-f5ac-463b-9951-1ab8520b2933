<!--
 - Copyright (C) Internet Systems Consortium, Inc. ("ISC")
 -
 - This Source Code Form is subject to the terms of the Mozilla Public
 - License, v. 2.0. If a copy of the MPL was not distributed with this
 - file, You can obtain one at http://mozilla.org/MPL/2.0/.
 -
 - See the COPYRIGHT file distributed with this work for additional
 - information regarding copyright ownership.
-->

<!-- Converted by db4-upgrade version 1.0 -->
<refentry xmlns:db="http://docbook.org/ns/docbook" version="5.0" xml:id="man.pkcs11-keygen">
  <info>
    <date>2014-01-15</date>
  </info>
  <refentryinfo>
    <corpname>ISC</corpname>
    <corpauthor>Internet Systems Consortium, Inc.</corpauthor>
  </refentryinfo>

  <refmeta>
    <refentrytitle><application>pkcs11-keygen</application></refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo>BIND9</refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname><application>pkcs11-keygen</application></refname>
    <refpurpose>generate keys on a PKCS#11 device</refpurpose>
  </refnamediv>

  <docinfo>
    <copyright>
      <year>2009</year>
      <year>2014</year>
      <year>2015</year>
      <year>2016</year>
      <year>2018</year>
      <holder>Internet Systems Consortium, Inc. ("ISC")</holder>
    </copyright>
  </docinfo>

  <refsynopsisdiv>
    <cmdsynopsis sepchar=" ">
      <command>pkcs11-keygen</command>
      <arg choice="req" rep="norepeat">-a <replaceable class="parameter">algorithm</replaceable></arg>
      <arg choice="opt" rep="norepeat"><option>-b <replaceable class="parameter">keysize</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-e</option></arg>
      <arg choice="opt" rep="norepeat"><option>-i <replaceable class="parameter">id</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-m <replaceable class="parameter">module</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-P</option></arg>
      <arg choice="opt" rep="norepeat"><option>-p <replaceable class="parameter">PIN</replaceable></option></arg>
      <arg choice="opt" rep="norepeat"><option>-q</option></arg>
      <arg choice="opt" rep="norepeat"><option>-S</option></arg>
      <arg choice="opt" rep="norepeat"><option>-s <replaceable class="parameter">slot</replaceable></option></arg>
      <arg choice="req" rep="norepeat">label</arg>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsection><info><title>DESCRIPTION</title></info>

    <para>
      <command>pkcs11-keygen</command> causes a PKCS#11 device to generate
      a new key pair with the given <option>label</option> (which must be
      unique) and with <option>keysize</option> bits of prime.
    </para>
  </refsection>

  <refsection><info><title>ARGUMENTS</title></info>

    <variablelist>
      <varlistentry>
        <term>-a <replaceable class="parameter">algorithm</replaceable></term>
        <listitem>
          <para>
            Specify the key algorithm class: Supported classes are RSA,
            DSA, DH, ECC and ECX. In addition to these strings, the
            <option>algorithm</option> can be specified as a DNSSEC
            signing algorithm that will be used with this key; for
            example, NSEC3RSASHA1 maps to RSA, ECDSAP256SHA256 maps
            to ECC, and ED25519 to ECX.  The default class is "RSA".
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-b <replaceable class="parameter">keysize</replaceable></term>
        <listitem>
          <para>
            Create the key pair with <option>keysize</option> bits of
            prime. For ECC keys, the only valid values are 256 and 384,
            and the default is 256. For ECX kyes, the only valid values
            are 256 and 456, and the default is 256.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-e</term>
        <listitem>
          <para>
            For RSA keys only, use a large exponent.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-i <replaceable class="parameter">id</replaceable></term>
        <listitem>
          <para>
            Create key objects with id. The id is either
            an unsigned short 2 byte or an unsigned long 4 byte number.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-m <replaceable class="parameter">module</replaceable></term>
        <listitem>
          <para>
            Specify the PKCS#11 provider module.  This must be the full
            path to a shared library object implementing the PKCS#11 API
            for the device.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-P</term>
        <listitem>
          <para>
            Set the new private key to be non-sensitive and extractable.
            The allows the private key data to be read from the PKCS#11
            device.  The default is for private keys to be sensitive and
            non-extractable.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-p <replaceable class="parameter">PIN</replaceable></term>
        <listitem>
          <para>
            Specify the PIN for the device.  If no PIN is provided on
            the command line, <command>pkcs11-keygen</command> will
            prompt for it.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-q</term>
        <listitem>
          <para>
            Quiet mode: suppress unnecessary output.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-S</term>
        <listitem>
          <para>
            For Diffie-Hellman (DH) keys only, use a special prime of
            768, 1024 or 1536 bit size and base (aka generator) 2.
	    If not specified, bit size will default to 1024.
          </para>
        </listitem>
      </varlistentry>

      <varlistentry>
        <term>-s <replaceable class="parameter">slot</replaceable></term>
        <listitem>
          <para>
            Open the session with the given PKCS#11 slot.  The default is
            slot 0.
          </para>
        </listitem>
      </varlistentry>

    </variablelist>
  </refsection>

  <refsection><info><title>SEE ALSO</title></info>

    <para>
      <citerefentry>
        <refentrytitle>pkcs11-destroy</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>pkcs11-list</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>pkcs11-tokens</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>,
      <citerefentry>
        <refentrytitle>dnssec-keyfromlabel</refentrytitle><manvolnum>8</manvolnum>
      </citerefentry>
    </para>
  </refsection>

</refentry>
