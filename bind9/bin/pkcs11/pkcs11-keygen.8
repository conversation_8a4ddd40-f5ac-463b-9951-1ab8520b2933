.\" Copyright (C) 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: pkcs11-keygen
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-01-15
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "PKCS11\-KEYGEN" "8" "2014\-01\-15" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
pkcs11-keygen \- generate keys on a PKCS#11 device
.SH "SYNOPSIS"
.HP \w'\fBpkcs11\-keygen\fR\ 'u
\fBpkcs11\-keygen\fR {\-a\ \fIalgorithm\fR} [\fB\-b\ \fR\fB\fIkeysize\fR\fR] [\fB\-e\fR] [\fB\-i\ \fR\fB\fIid\fR\fR] [\fB\-m\ \fR\fB\fImodule\fR\fR] [\fB\-P\fR] [\fB\-p\ \fR\fB\fIPIN\fR\fR] [\fB\-q\fR] [\fB\-S\fR] [\fB\-s\ \fR\fB\fIslot\fR\fR] {label}
.SH "DESCRIPTION"
.PP
\fBpkcs11\-keygen\fR
causes a PKCS#11 device to generate a new key pair with the given
\fBlabel\fR
(which must be unique) and with
\fBkeysize\fR
bits of prime\&.
.SH "ARGUMENTS"
.PP
\-a \fIalgorithm\fR
.RS 4
Specify the key algorithm class: Supported classes are RSA, DSA, DH, ECC and ECX\&. In addition to these strings, the
\fBalgorithm\fR
can be specified as a DNSSEC signing algorithm that will be used with this key; for example, NSEC3RSASHA1 maps to RSA, ECDSAP256SHA256 maps to ECC, and ED25519 to ECX\&. The default class is "RSA"\&.
.RE
.PP
\-b \fIkeysize\fR
.RS 4
Create the key pair with
\fBkeysize\fR
bits of prime\&. For ECC keys, the only valid values are 256 and 384, and the default is 256\&. For ECX kyes, the only valid values are 256 and 456, and the default is 256\&.
.RE
.PP
\-e
.RS 4
For RSA keys only, use a large exponent\&.
.RE
.PP
\-i \fIid\fR
.RS 4
Create key objects with id\&. The id is either an unsigned short 2 byte or an unsigned long 4 byte number\&.
.RE
.PP
\-m \fImodule\fR
.RS 4
Specify the PKCS#11 provider module\&. This must be the full path to a shared library object implementing the PKCS#11 API for the device\&.
.RE
.PP
\-P
.RS 4
Set the new private key to be non\-sensitive and extractable\&. The allows the private key data to be read from the PKCS#11 device\&. The default is for private keys to be sensitive and non\-extractable\&.
.RE
.PP
\-p \fIPIN\fR
.RS 4
Specify the PIN for the device\&. If no PIN is provided on the command line,
\fBpkcs11\-keygen\fR
will prompt for it\&.
.RE
.PP
\-q
.RS 4
Quiet mode: suppress unnecessary output\&.
.RE
.PP
\-S
.RS 4
For Diffie\-Hellman (DH) keys only, use a special prime of 768, 1024 or 1536 bit size and base (aka generator) 2\&. If not specified, bit size will default to 1024\&.
.RE
.PP
\-s \fIslot\fR
.RS 4
Open the session with the given PKCS#11 slot\&. The default is slot 0\&.
.RE
.SH "SEE ALSO"
.PP
\fBpkcs11-destroy\fR(8),
\fBpkcs11-list\fR(8),
\fBpkcs11-tokens\fR(8),
\fBdnssec-keyfromlabel\fR(8)
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2009, 2014-2016, 2018 Internet Systems Consortium, Inc. ("ISC")
.br
