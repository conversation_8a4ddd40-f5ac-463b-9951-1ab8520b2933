.\" Copyright (C) 2018 Internet Systems Consortium, Inc. ("ISC")
.\" 
.\" This Source Code Form is subject to the terms of the Mozilla Public
.\" License, v. 2.0. If a copy of the MPL was not distributed with this
.\" file, You can obtain one at http://mozilla.org/MPL/2.0/.
.\"
.hy 0
.ad l
'\" t
.\"     Title: pkcs11-tokens
.\"    Author: 
.\" Generator: DocBook XSL Stylesheets v1.78.1 <http://docbook.sf.net/>
.\"      Date: 2014-01-15
.\"    Manual: BIND9
.\"    Source: ISC
.\"  Language: English
.\"
.TH "PKCS11\-TOKENS" "8" "2014\-01\-15" "ISC" "BIND9"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
pkcs11-tokens \- list PKCS#11 available tokens
.SH "SYNOPSIS"
.HP \w'\fBpkcs11\-tokens\fR\ 'u
\fBpkcs11\-tokens\fR [\fB\-m\ \fR\fB\fImodule\fR\fR] [\fB\-v\fR]
.SH "DESCRIPTION"
.PP
\fBpkcs11\-tokens\fR
lists the PKCS#11 available tokens with defaults from the slot/token scan performed at application initialization\&.
.SH "ARGUMENTS"
.PP
\-m \fImodule\fR
.RS 4
Specify the PKCS#11 provider module\&. This must be the full path to a shared library object implementing the PKCS#11 API for the device\&.
.RE
.PP
\-v
.RS 4
Make the PKCS#11 libisc initialization verbose\&.
.RE
.SH "SEE ALSO"
.PP
\fBpkcs11-destroy\fR(8),
\fBpkcs11-keygen\fR(8),
\fBpkcs11-list\fR(8)
.SH "AUTHOR"
.PP
\fBInternet Systems Consortium, Inc\&.\fR
.SH "COPYRIGHT"
.br
Copyright \(co 2018 Internet Systems Consortium, Inc. ("ISC")
.br
