/*
 * Copyright (c) 2021 Infoblox Inc. All Rights Reserved.
 */

#ifndef __DNS_CONTROLD_FUNCTIONS_H
#define __DNS_CONTROLD_FUNCTIONS_H

#include "infoblox/one/distributedManager.h"
#include "infoblox/ib_annotate.h"
#include <dns_controld_constants.h>

/*
 * Keep in mind that these distributed manager calls are
 * sent to/from different releases of our product. Do NOT remove or
 * renumber or change the semantic of requests.
 */

// Start of operation code definitions

#define DM_DNS_LOW                      	0x10000000
ANNOTATE_SEUT_GROUP("OPCODES_CONTROLD_DNS")
#define DM_VIEW_DNS_STATS_REQUEST       	0x10000001
#define DM_CLEAR_DNS_CACHE_REQUEST      	0x10000002
#define DM_DNS_ZONE_DIG_REQUEST    		0x10000003
#define DM_DNS_CLEAR_SEC_DB_REQUEST     	0x10000004
#define DM_DNS_ZONE_SERIALNO_REQUEST    	0x10000005
#define DM_DISCOVERY_CONTROL_REQUEST            0x10000006
#define DM_DNS_PA_PING_REQUEST          	0x10000007
#define DM_VDISCOVERY_CONNECTIVITY   	    0x10000008
#define DM_DNS_PA_PING6_REQUEST          0x10000009
#define DM_DNS_CACHE_HIT_RATIO_REQUEST          0x1000000a
#define DM_DNS_CHECK_NETWORK_MONITORING         0x1000000b
#define DM_TAE_UPDATE_PROXY_CONF_REQUEST        0x1000000c
ANNOTATE_SEUT_NO_VERSION
#define DM_RPZ_RECENT_HITS_REQUEST              0x1000000d      // secvis: no longer in use
#define DM_VIEW_DDOS_STATS_REQUEST              0x1000000e
#define DM_DNS_PARENT_CHECK_REQUEST             0x1000000f
#define DM_IDNS_QUERY                           0x10000010
#define DM_IDNS_HEALTH_CHECK_MANUAL             0x10000011
#define DM_DNS_FQDN_DIG_REQUEST                 0x10000012
#define DM_IDNS_SET_DISABLED                    0x10000013

ANNOTATE_SEUT_END
#define DM_DNS_HIGH                             0x10000014

// Start of DNS operation version
ANNOTATE_SEUT_GROUP("VERSIONS_CONTROLD")
#define DM_VIEW_DNS_STATS_REQUEST_VERSION      10
#define DM_CLEAR_DNS_CACHE_REQUEST_VERSION     11
#define DM_DNS_ZONE_DIG_REQUEST_VERSION        10
#define DM_DNS_CLEAR_SEC_DB_REQUEST_VERSION    11
#define DM_DNS_ZONE_SERIALNO_REQUEST_VERSION   10
#define DM_DISCOVERY_CONTROL_REQUEST_VERSION   10
#define DM_DNS_PA_PING_REQUEST_VERSION         10
#define DM_VDISCOVERY_CONNECTIVITY_VERSION     10
#define DM_DNS_PA_PING6_REQUEST_VERSION        10
#define DM_VIEW_DDOS_STATS_REQUEST_VERSION     1
#define DM_DNS_CACHE_HIT_RATIO_REQUEST_VERSION 1
#define DM_DNS_CHECK_NETWORK_MONITORING_VERSION   1
#define DM_TAE_UPDATE_PROXY_CONF_REQUEST_VERSION  1
#define DM_RPZ_RECENT_HITS_REQUEST_VERSION        1
#define DM_DNS_PARENT_CHECK_REQUEST_VERSION       1
#define DM_IDNS_QUERY_VERSION                     1
#define DM_IDNS_HEALTH_CHECK_MANUAL_VERSION       1
#define DM_DNS_FQDN_DIG_REQUEST_VERSION           2
#define DM_IDNS_SET_DISABLED_VERSION              1

ANNOTATE_SEUT_END

#define DM_DHCP_LOW                     	0x20000000
ANNOTATE_SEUT_GROUP("OPCODES_CONTROLD_DHCP")
#define DM_DHCP_LEASE_HISTORY_TRANSFER  	0x20000001
#define DM_DHCP_LEASE_HISTORY_DOWNLOAD  	0x20000002
#define DM_DHCP_LEASE_HISTORY_UNPACK    	0x20000003
#define DM_DHCP_LEASE_HISTORY_IMPORT    	0x20000004
#define DM_DHCP_EXPERT_MODE_CONFIG_UPLOAD	0x20000007
#define DM_DHCP_EXPERT_MODE_CONFIG_DOWNLOAD	0x20000008
#define DM_DHCP_EXPERT_MODE_CONFIG_REMOVE	0x20000009
#define DM_DHCP_FO_STATE_CHANGE_REQUEST         0x2000000a
#define DM_DHCP_LEASE_STATE_CHANGE_REQUEST   	0x2000000b
#define DM_DHCP_CLEAR_MS_LEASE_REQUEST         0x2000000c
#define DM_DHCP_STATS_REQUEST               0x2000000d
#define DM_DHCP_CLEAR_NAC_AUTH_CACHE_REQUEST	0x2000000e
#define DM_DHCP_V6_STATS_REQUEST                0x2000000f
#define DM_DHCP_SET_NAC_FILTERS_SWITCH          0x20000010
ANNOTATE_SEUT_END
#define DM_DHCP_HIGH                            0x20000011

// Start of DHCP operation version
ANNOTATE_SEUT_GROUP("VERSIONS_CONTROLD")
#define DM_DHCP_LEASE_HISTORY_TRANSFER_VERSION          13
#define DM_DHCP_LEASE_HISTORY_DOWNLOAD_VERSION          13
#define DM_DHCP_LEASE_HISTORY_UNPACK_VERSION            13
#define DM_DHCP_LEASE_HISTORY_IMPORT_VERSION            10
#define DM_DHCP_EXPERT_MODE_CONFIG_UPLOAD_VERSION       10
#define DM_DHCP_EXPERT_MODE_CONFIG_DOWNLOAD_VERSION     10
#define DM_DHCP_EXPERT_MODE_CONFIG_REMOVE_VERSION       10
#define DM_DHCP_FO_STATE_CHANGE_REQUEST_VERSION         11
#define DM_DHCP_LEASE_STATE_CHANGE_REQUEST_VERSION      12
#define DM_DHCP_CLEAR_MS_LEASE_REQUEST_VERSION          10
#define DM_DHCP_STATS_REQUEST_VERSION                   10
#define DM_DHCP_CLEAR_NAC_AUTH_CACHE_REQUEST_VERSION    10
#define DM_DHCP_V6_STATS_REQUEST_VERSION                10
#define DM_DHCP_SET_NAC_FILTERS_SWITCH_VERSION          1
ANNOTATE_SEUT_END

#define  MAX_REGEXP_SZ                  256
//String length to held a serial number which can fit in a 32 bit integer
#define SOA_SERIAL_NUM_LENGTH 11
#define PING_RESULT_SIZE 1024*2
#define PING6_RESULT_SIZE 16
#define PING_MAX_NUM_PIDS 64
#define PING_MAX_NUM_RTT 256
#define IDNS_ID_SIZE 33
#define IDNS_QTYPE_STRING_LEN 6 //sizeof("NAPTR")
#define RESOURCE_RECORD_TYPE_LEN 33
#define DIG_RETURN_CODE_LEN 21
#define DIG_RESULT_STRING_LEN 8000 //< 8K because of MAX_MESSAGE_SIZE in iipc.c
#define IDNS_DTC_OBJECT_STRING_LEN 7 // Biggest len in "lbdn"/"pool"/"server"

// for lease history query
#define MAX_NUM_SEARCH_FIELDS 10

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_CLEAR_MS_LEASE_REQUEST")
typedef struct
{
    char m_ms_dhcp_server_oid[IOBJECT_MAX_STRING_LENGTH];
    char m_lease_ip[MAX_IPV4_ADDR_SZ];
} dhcp_clear_ms_lease_req_v10_t;

typedef dhcp_clear_ms_lease_req_v10_t dhcp_clear_ms_lease_req_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_IN("DM_DHCP_CLEAR_MS_LEASE_REQUEST")
typedef struct
{
    ib_bool_t m_ret_flag;
    char m_err_str[IOBJECT_MAX_STRING_LENGTH];
} dhcp_clear_ms_lease_resp_v10_t;

typedef dhcp_clear_ms_lease_resp_v10_t dhcp_clear_ms_lease_resp_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_LEASE_STATE_CHANGE_REQUEST")
typedef struct
{
  char  m_lease_state[IOBJECT_MAX_STRING_LENGTH];
  char  m_dhcp_srvr_ip_addr[MAX_IPV4_ADDR_SZ];
  char  m_lease_ip[MAX_IPV4_ADDR_SZ];
  char  m_dhcp_updater_key[IOBJECT_MAX_STRING_LENGTH];
} dhcp_lease_state_change_req_v10_t;

typedef struct
{
  char  m_lease_state[IOBJECT_MAX_STRING_LENGTH];
  char  m_dhcp_srvr_ip_addr[MAX_IPV4_ADDR_SZ];
  char  m_lease_ip[MAX_IPV6_ADDR_SZ];
  char  m_dhcp_updater_key[IOBJECT_MAX_STRING_LENGTH];
  char m_duid[IOBJECT_MAX_STRING_LENGTH];
} dhcp_lease_state_change_req_v11_t;

typedef struct
{
  char  m_lease_state[IOBJECT_MAX_STRING_LENGTH];
  char  m_dhcp_srvr_ip_addr[MAX_IPV4_ADDR_SZ];
  char  m_lease_ip[MAX_IPV6_ADDR_SZ];
  char  m_dhcp_updater_key[IOBJECT_MAX_STRING_LENGTH];
  char  m_duid[IOBJECT_MAX_STRING_LENGTH];
  char  m_dhcp_updater_key_alg[IOBJECT_MAX_STRING_LENGTH];
} dhcp_lease_state_change_req_v12_t;

typedef dhcp_lease_state_change_req_v12_t dhcp_lease_state_change_req_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_STATS_REQUEST", "DM_DHCP_V6_STATS_REQUEST")
typedef struct
{
  char                m_destination_file_name[MAX_FILE_NAME_SZ];
} dhcp_stats_v10_t;

typedef dhcp_stats_v10_t dhcp_stats_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_VIEW_DNS_STATS_REQUEST")
typedef struct
{
  char                m_destination_file_name[MAX_FILE_NAME_SZ];
  char                m_zone_fqdn[IOBJECT_MAX_STRING_LENGTH];
  char                m_view_key[IOBJECT_MAX_STRING_LENGTH];
  ib_bool_t           m_fetch_latest_data; // If true then only fetch the latest data
} dns_zone_stats_v10_t;

typedef dns_zone_stats_v10_t dns_zone_stats_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_VIEW_DDOS_STATS_REQUEST")
typedef struct
{
  char                m_destination_file_name[MAX_FILE_NAME_SZ];
} ddos_stats_v1_t;

typedef ddos_stats_v1_t ddos_stats_t;
ANNOTATE_SEUT_END

typedef dns_clear_sec_db_subzone_v11_t dns_clear_sec_db_subzone_t; 

ANNOTATE_SEUT_MSG_OUT("DM_DNS_CLEAR_SEC_DB_REQUEST")
typedef struct
{
  char                m_zone[IOBJECT_MAX_STRING_LENGTH];
  ib_bool_t           m_remove_subzones;  //Flag to indicate if subzones were removed
} dns_clear_sec_db_v10_t;

typedef struct
{
  char                            m_zone[IOBJECT_MAX_STRING_LENGTH];
  dns_clear_sec_db_subzone_v11_t  m_remove_subzones;
} dns_clear_sec_db_v11_t;

typedef dns_clear_sec_db_v11_t dns_clear_sec_db_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_CLEAR_DNS_CACHE_REQUEST")
typedef struct
{
    char m_domain[IOBJECT_MAX_STRING_LENGTH];
    char m_view_name[IOBJECT_MAX_STRING_LENGTH];
} dns_clear_cache_v10_t;

typedef struct
{
    char m_domain[IOBJECT_MAX_STRING_LENGTH];
    char m_view_name[IOBJECT_MAX_STRING_LENGTH];
    ib_bool_t m_clear_full_tree;
} dns_clear_cache_v11_t;

typedef dns_clear_cache_v11_t dns_clear_cache_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_TAE_UPDATE_PROXY_CONF_REQUEST")
typedef struct
{
    char m_nmURL[IOBJECT_MAX_STRING_LENGTH];
} tae_proxy_config_req_v1_t;

typedef tae_proxy_config_req_v1_t tae_proxy_config_req_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_RPZ_RECENT_HITS_REQUEST")
typedef struct
{
  char m_rpz_recent_hits_file_name[MAX_FILE_NAME_SZ];
} rpz_recent_hits_v1_t;

typedef rpz_recent_hits_v1_t rpz_recent_hits_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_CLEAR_NAC_AUTH_CACHE_REQUEST")
typedef struct
{
    char m_member[IOBJECT_MAX_STRING_LENGTH];
    char m_mac_addr[MAC_ADDRESS_STRING_LENGTH];
} nac_clear_cache_v10_t;

typedef nac_clear_cache_v10_t nac_clear_cache_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_SET_NAC_FILTERS_SWITCH")
typedef struct
{
    ib_bool_t m_disable;
} nac_filters_switch_v1_t;

typedef nac_filters_switch_v1_t nac_filters_switch_t;
ANNOTATE_SEUT_END

enum e_discovery_control_type
{
    DISCOVERY_CONTROL_PAUSE=0,
    DISCOVERY_CONTROL_STOP
};

ANNOTATE_SEUT_MSG_OUT("DM_DISCOVERY_CONTROL_REQUEST")
typedef struct
{
  enum e_discovery_control_type m_control_type; // control type (see below)
  char m_discovery_task_oid[IOBJECT_MAX_STRING_LENGTH]; // task oid
  ib_uint32_t m_search_options;
} dns_discovery_control_v10_t;

typedef dns_discovery_control_v10_t dns_discovery_control_t;
ANNOTATE_SEUT_END

#define MAX_USERNAME_LENGTH   256
#define MAX_PASSWORD_LENGTH   1024
#define MAX_PROTOCOL_LENGTH   16
ANNOTATE_SEUT_MSG_OUT("DM_VDISCOVERY_CONNECTIVITY")
typedef struct
{
  char  m_fqdn_or_ip[IOBJECT_MAX_STRING_LENGTH];
  char  m_username[MAX_USERNAME_LENGTH];
  char  m_password[MAX_PASSWORD_LENGTH];
  char  m_conn_proto[MAX_PROTOCOL_LENGTH];
  ib_uint16_t	m_port;
  ib_uint16_t m_timeout;
  char  m_dst_filename[IOBJECT_MAX_STRING_LENGTH];
  // 'disabled' is not needed for now.
  //ib_bool_t m_disabled;
} vdiscovery_connectivity_req_v10_t;

typedef vdiscovery_connectivity_req_v10_t vdiscovery_connectivity_req_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_LEASE_HISTORY_DOWNLOAD", "DM_DHCP_LEASE_HISTORY_TRANSFER", "DM_DHCP_LEASE_HISTORY_UNPACK")
typedef struct {
  char m_entity[128];
  #ifdef INFOBLOX_SGU_ENFORCE
    ib_search_request_t m_search_request;
  #else
    ib_search_request_v10_t m_search_request;
  #endif
  char m_start_time[MAX_TIMESTAMP_SZ];
  char m_end_time[MAX_TIMESTAMP_SZ];
} dhcp_lease_history_search_request_v10_t;

typedef dhcp_lease_history_search_request_v10_t dhcp_lease_history_search_request_v11_t;

typedef struct {
  char m_entity[128];
  #ifdef INFOBLOX_SGU_ENFORCE
    ib_search_request_t m_search_request;
  #else
    ib_search_request_v10_t m_search_request;
  #endif
  char m_start_time[MAX_TIMESTAMP_SZ];
  char m_end_time[MAX_TIMESTAMP_SZ];
  char m_prev_obj_id[IOBJECT_MAX_STRING_LENGTH];
} dhcp_lease_history_search_request_v12_t;

typedef struct {
  char m_entity[128];
  ib_search_request_v13_t m_search_request;
  char m_start_time[MAX_TIMESTAMP_SZ];
  char m_end_time[MAX_TIMESTAMP_SZ];
  char m_prev_obj_id[IOBJECT_MAX_STRING_LENGTH];
} dhcp_lease_history_search_request_v13_t;
typedef dhcp_lease_history_search_request_v13_t dhcp_lease_history_search_request_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_IN("DM_DHCP_LEASE_HISTORY_TRANSFER")
typedef struct {
  ib_search_response_t m_search_response;
  ib_bool_t m_has_more_data_forward;
  // Need to indicate the fact that we have more entries
  // in the reverse direction for GOTO_{PREV,NEXT} searches.
  ib_bool_t m_has_more_data_backward;
} dhcp_lease_history_search_response_v10_t;

typedef dhcp_lease_history_search_response_v10_t dhcp_lease_history_search_response_v11_t;

typedef struct {
  ib_search_response_t m_search_response;
  ib_bool_t m_has_more_data_forward;
  // Need to indicate the fact that we have more entries
  // in the reverse direction for GOTO_{PREV,NEXT} searches.
  ib_bool_t m_has_more_data_backward;
  char  m_goto_obj_id[IOBJECT_MAX_STRING_LENGTH];
} dhcp_lease_history_search_response_v12_t;

typedef dhcp_lease_history_search_response_v12_t dhcp_lease_history_search_response_v13_t;
typedef dhcp_lease_history_search_response_v13_t dhcp_lease_history_search_response_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_LEASE_HISTORY_IMPORT")
typedef struct
{
  char m_src_dir_name[MAX_FILE_NAME_SZ];
} dhcp_lease_history_import_v10_t;

typedef dhcp_lease_history_import_v10_t dhcp_lease_history_import_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_EXPERT_MODE_CONFIG_DOWNLOAD", "DM_DHCP_EXPERT_MODE_CONFIG_UPLOAD", "DM_DHCP_EXPERT_MODE_CONFIG_REMOVE")
typedef struct
{
  char  m_directory[MAX_FILE_NAME_SZ];
  char  m_filename[MAX_FILE_NAME_SZ];
  char  m_vnode_oid[IOBJECT_MAX_STRING_LENGTH];
} dhcp_file_v10_t;

typedef dhcp_file_v10_t dhcp_file_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_FO_STATE_CHANGE_REQUEST")
typedef struct
{
  char  m_partner_state[IOBJECT_MAX_STRING_LENGTH];
  char  m_peer_ip_addr[MAX_IPV4_ADDR_SZ];
  char  m_failover_assoc_name[IOBJECT_MAX_STRING_LENGTH];
  char  m_dhcp_updater_key[IOBJECT_MAX_STRING_LENGTH];
} dhcp_fo_state_change_req_v10_t;

typedef struct
{
  char  m_partner_state[IOBJECT_MAX_STRING_LENGTH];
  char  m_peer_ip_addr[MAX_IPV4_ADDR_SZ];
  char  m_failover_assoc_name[IOBJECT_MAX_STRING_LENGTH];
  char  m_dhcp_updater_key[IOBJECT_MAX_STRING_LENGTH];
  char  m_dhcp_updater_key_alg[IOBJECT_MAX_STRING_LENGTH];
} dhcp_fo_state_change_req_v11_t;

typedef dhcp_fo_state_change_req_v11_t dhcp_fo_state_change_req_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DNS_ZONE_SERIALNO_REQUEST", "DM_DNS_ZONE_DIG_REQUEST")
typedef struct
{
  char                zone_name[IOBJECT_MAX_STRING_LENGTH];
  char                dig_zone_name[IOBJECT_MAX_STRING_LENGTH];
  char                ip_address[MAX_FILE_NAME_SZ];
  char                zone_type[MAX_FILE_NAME_SZ];
  char                view_key[MAX_FILE_NAME_SZ];
} dns_sview_v10_t;

typedef dns_sview_v10_t dns_sview_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DNS_FQDN_DIG_REQUEST")
typedef struct
{
  char m_name_server[IOBJECT_MAX_STRING_LENGTH];
  char m_fqdn[IOBJECT_MAX_STRING_LENGTH];
  char m_record_type[RESOURCE_RECORD_TYPE_LEN];
  char m_source_address[MAX_IPV6_ADDR_SZ];
  ib_bool_t m_recursive_query;
} dns_fqdn_dig_request_v1_t;

typedef struct
{
  char m_name_server[IOBJECT_MAX_STRING_LENGTH];
  char m_fqdn[IOBJECT_MAX_STRING_LENGTH];
  char m_record_type[RESOURCE_RECORD_TYPE_LEN];
  char m_source_address[MAX_IPV6_ADDR_SZ];
  ib_uint32_t 	m_timeout;
  ib_uint32_t 	m_tries;
  ib_bool_t m_recursive_query;
} dns_fqdn_dig_request_v2_t;

typedef dns_fqdn_dig_request_v2_t dns_fqdn_dig_request_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_IN("DM_DNS_FQDN_DIG_REQUEST")
typedef struct
{
    char m_result_text[DIG_RESULT_STRING_LEN];
    char m_result[DIG_RETURN_CODE_LEN];
} dns_fqdn_dig_response_v1_t;

typedef dns_fqdn_dig_response_v1_t dns_fqdn_dig_response_v2_t;
typedef dns_fqdn_dig_response_v2_t dns_fqdn_dig_response_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DNS_PA_PING_REQUEST")
typedef struct
{
  char                m_start_address[MAX_IPV4_ADDR_SZ];
  char                m_end_address[MAX_IPV4_ADDR_SZ];
  ib_uint32_t         m_count;
  ib_uint32_t         m_timeout;
} dns_pa_ping_v10_t;

typedef dns_pa_ping_v10_t dns_pa_ping_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DNS_PA_PING6_REQUEST")
typedef struct
{
  char                m_address[MAX_IPV6_ADDR_SZ];
  ib_uint32_t         m_count;
  ib_uint32_t         m_timeout;
} dns_pa_ping6_v10_t;

typedef dns_pa_ping6_v10_t dns_pa_ping6_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DNS_CACHE_HIT_RATIO_REQUEST")
typedef struct
{
  char m_destination_file_name[MAX_FILE_NAME_SZ];
} dns_chr_req_v1_t;

typedef dns_chr_req_v1_t dns_chr_req_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DNS_PARENT_CHECK_REQUEST")
typedef struct
{
  char                zone_key[IOBJECT_MAX_STRING_LENGTH];
} dns_parent_check_request_v1_t;

typedef dns_parent_check_request_v1_t dns_parent_check_request_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_IN("DM_DNS_CHECK_NETWORK_MONITORING")
typedef struct
{
    ib_bool_t m_monitor_enabled;
} dns_check_network_monitoring_resp_v1_t;

typedef dns_check_network_monitoring_resp_v1_t dns_check_network_monitoring_resp_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_IDNS_QUERY")
typedef struct
{
  char address[MAX_IPV6_ADDR_SZ];
  char qname[IOBJECT_MAX_STRING_LENGTH];
  /* Type of desired results, can be A/AAAA/NAPTR */
  char qtype[IDNS_QTYPE_STRING_LEN];
  /*NULL terminated id size*/
  char lbdn_id [IDNS_ID_SIZE];
  /* Response file_name is generated as /tmp/idns_ibap_query_<ibap_id>.txt */
  char response_file_name [MAX_FILE_NAME_SZ];
} idns_query_v1_t;

typedef idns_query_v1_t idns_query_t;
ANNOTATE_SEUT_END

#define  MAX_HOSTNAME_ADDR_SZ           256
#define  MONITOR_ID_HEX_STRING_SZ        33
// The output of DTC health check is a string formatted as a python dict output.
// It will have two statuses (IPv4 and IPv6) and may be accompanied by two error
// messages of MONITOR_MAX_ERROR_STRING_LEN size (512 bytes) each, a status
// ("UNKNOWN"/"OFFLINE"/"ONLINE") and some dict formatting.
// Real maximum size is rather difficult to estimate, as maker will show error
// if the size of struct members for controld reply is defined as an expression.
// So, size of 1280 works fine, but size of 1024+256 generates an error...
#define  MAX_DTC_HEALTH_OUTPUT_MSG_SZ   1280

ANNOTATE_SEUT_MSG_IN("DM_IDNS_HEALTH_CHECK_MANUAL")
typedef struct
{
    char m_monitor_id[MONITOR_ID_HEX_STRING_SZ];
    char m_remote_srv[MAX_HOSTNAME_ADDR_SZ];
} dtc_health_check_request_v1_t;

typedef dtc_health_check_request_v1_t dtc_health_check_request_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_IDNS_HEALTH_CHECK_MANUAL")
typedef struct
{
    char m_output[MAX_DTC_HEALTH_OUTPUT_MSG_SZ];
} dtc_health_check_response_v1_t;

typedef dtc_health_check_response_v1_t dtc_health_check_response_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_IN("DM_IDNS_SET_DISABLED")
typedef struct
{
    char m_object_id [IDNS_ID_SIZE];
    char m_object_type [IDNS_DTC_OBJECT_STRING_LEN];
    ib_bool_t m_disabled;
    ib_uint32_t m_delay;
    ib_uint32_t m_duration;
    ib_bool_t m_stop_health_monitoring;
    ib_uint32_t m_disable_timeframe;
} dtc_set_disabled_request_v1_t;

typedef dtc_set_disabled_request_v1_t dtc_set_disabled_request_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_IDNS_SET_DISABLED")
typedef struct
{
	ib_bool_t m_ok;
} dtc_set_disabled_response_v1_t;

typedef dtc_set_disabled_response_v1_t dtc_set_disabled_response_t;
ANNOTATE_SEUT_END

ib_return_t dns_run_task (rtxml_instance * a_instance,
               iipc_session * a_session,
               iipc_address * a_remote_address,
               remote_request_t *a_req);

ib_return_t dhcp_run_task (rtxml_instance * a_instance,
               iipc_session * a_session,
               iipc_address * a_remote_address,
               remote_request_t *a_req);


ib_return_t dns_get_opcodes_definition(const dm_opcode_definition_t** a_list,
               ib_uint32_t* a_count);

ib_return_t dhcp_get_opcodes_definition(const dm_opcode_definition_t** a_list,
               ib_uint32_t* a_count);
#endif //__DNS_CONTROLD_FUNCTIONS_H
