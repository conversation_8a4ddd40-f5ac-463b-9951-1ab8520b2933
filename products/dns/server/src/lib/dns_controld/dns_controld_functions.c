/*
 * Copyright (c) 2025 Infoblox Inc. All Rights Reserved.
 */

#include "infoblox/util.h"
#include "infoblox/process.h"
#include "infoblox/dns/dns_controld_functions.h"
#include "infoblox/dns/dns_administration.h"
#include "infoblox/dns/dhcp_lease_history.h"
#include "infoblox/one/distributedManager.h"
#include "infoblox/dns/dns_view.h"
#include "infoblox/dns/nw_discover_common.h"
#include "infoblox/one/one_license.h"
#include "dns/infoblox_stats.h"
#include <infoblox/dns/dhcp_lease_file.h>
#include <dns/infoblox_stats.h>
#include "infoblox/ib_paths.h"

#include "com_infoblox_dns.h"
#include "infoblox/one/fastpath_util.h"

#define LEASE_HISTORY_EXPORT_FILE "/storage/tmp/dhcpLeaseHistory.csv"
#define LEASE_HISTORY_EXPORT_FILE_GZ "/storage/tmp/dhcpLeaseHistory.csv.gz"

#define RPZ_RECENT_HITS_FILE_ON_MEMBER "/storage/tmp/rpz_recent_hits.txt"

#define IDNS_QUERY_RESULT_UTIL_FILE "%s.original"

static ib_return_t
filter_dns_stats_file (const char *a_src_file, char *a_dest_file,
                       const dns_zone_stats_t *a_pZone )
{

   IENTER;
   FILE *src_filep = NULL, *dest_filep = NULL;
   char line[1024];
   char view_key[IOBJECT_MAX_STRING_LENGTH];
   char fqdn[IOBJECT_MAX_STRING_LENGTH];
   char timestamp[IOBJECT_MAX_STRING_LENGTH];
   struct timeval now = {0L, 0L}, before = {0L, 0L}, interval = {120L, 0L};
   time_t ts_val = 0L;

   ITEST(a_src_file, IERR_ARG);
   ITEST(a_dest_file, IERR_ARG);
   ITEST(a_pZone, IERR_ARG);

   src_filep = fopen (a_src_file, "r");
   if (src_filep == NULL)
    {
      ILOG ("Failed to open %s for reading", a_src_file);
      IRETURN (IERR_FAILURE);
    }

    dest_filep = fopen (a_dest_file, "w");
    if (dest_filep == NULL)
    {
      ILOG ("Failed to open %s for writing", a_dest_file);
      IRETURN (IERR_FAILURE);
    }

    // Calculate timestamp at 60 seconds ago
    gettimeofday(&now, NULL);
    timersub(&now, &interval, &before);

    while (fgets (line, sizeof(line), src_filep) != NULL)
    {
      sscanf (line, "%s %s %s", timestamp, view_key, fqdn);
      if (a_pZone->m_fetch_latest_data)
      {
        // If specified, return only the data updated within the last 60 seconds
        ts_val = atol(timestamp);
        if (ts_val >= before.tv_sec)
          fputs (line, dest_filep);
      }
      else
      {
        // Otherwise, return all data matching zone/view search criteria
        if (((strcasecmp(view_key, a_pZone->m_view_key) == 0) &&
              strcasecmp(fqdn, a_pZone->m_zone_fqdn) == 0))
          fputs (line, dest_filep);
      }
    }

cleanup:
    if (src_filep)
       fclose(src_filep);
    if (dest_filep)
       fclose(dest_filep);

	ILEAVE;
}

static ib_return_t
ddos_stats_request(const char *ptr,
                   ib_bool_t *xfer_file,
                   char *src_file_name,
                   int a_src_file_name_size,
                   char *dst_file_name,
                   int a_dst_file_name_size,
                   ib_uint32_t *retCode)
{
    IENTER;

    const ddos_stats_t *ddos_stats = (const ddos_stats_t*) ptr;

    IDEBUG("Running DDOS Stats report...");
    ITEST((ptr), IERR_ARG);
    ITEST((src_file_name), IERR_ARG);
    ITEST((dst_file_name), IERR_ARG);
    ITEST((retCode), IERR_ARG);
    ITEST(ddos_stats, IERR_ARG);
    ITEST(ddos_stats->m_destination_file_name, IERR_ARG);
    ITEST((strlen(ddos_stats->m_destination_file_name)>0), IERR_ARG);

    STRLCPY(src_file_name, IB_DDOS_STATS_FILE, a_src_file_name_size);
    STRLCPY(dst_file_name, ddos_stats->m_destination_file_name, a_dst_file_name_size);

    *retCode = IERR_SUCCESS;
    *xfer_file = TRUE;

cleanup:
    ILEAVE;
}

static ib_return_t
dns_stats_request (const char *ptr,
		   ib_bool_t *xfer_file,
		   char *src_file_name,
		   int a_src_file_name_size,
		   char *dst_file_name,
		   int a_dst_file_name_size,
		   ib_uint32_t *retCode)
{
   IENTER;
   int                   rc = 0;
   ib_bool_t pid_file_exists = FALSE;
   int pid = 0;
   char proc_dir[IOBJECT_MAX_STRING_LENGTH] = {0};
   FILE * pid_file = NULL, * stat_file = NULL;
   struct stat st = {0};
   const dns_zone_stats_t *pZone = (const dns_zone_stats_t*) ptr;
   ib_bool_t named_running = FALSE;


   IDEBUG ("Running DNS Stats report...");
   ITEST ((ptr), IERR_ARG);
   ITEST ((src_file_name), IERR_ARG);
   ITEST ((dst_file_name), IERR_ARG);
   ITEST ((retCode), IERR_ARG);
   ITEST(pZone, IERR_ARG);
   ITEST((strlen(pZone->m_destination_file_name)>0), IERR_ARG);
   IDEBUG ("destFile length=%d, %s", (int)strlen(pZone->m_destination_file_name), pZone->m_destination_file_name);
   IDEBUG ("FQDN = %s",  pZone->m_zone_fqdn);
   IDEBUG ("View Key =  %s", pZone->m_view_key);

   *retCode = IERR_SUCCESS;

   STRLCPY (src_file_name, "/storage/tmp/dns_stat_multi_tmp.txt", a_src_file_name_size);
   STRLCPY (dst_file_name, pZone->m_destination_file_name, a_dst_file_name_size);

   iutil_rm_f (src_file_name); // clean up the prev file
   iutil_rm_f (dst_file_name); // clean up the prev file

   ICALL(iutil_is_process_running_strict(NAMED_PID_FILE, "named", TRUE, &named_running));

   if(named_running)
       ICALL (filter_dns_stats_file(IB_NAMED_STAT_MULTI_FILE, src_file_name, pZone));

   // get named start time
   // first get pid
   if(named_running)
       ICALL (iutil_file_exists (NAMED_PID_FILE, &pid_file_exists));

   if (!pid_file_exists)
           *retCode = IERR_NOTFOUND;
   else
   {
           if(named_running)
               pid_file = fopen (NAMED_PID_FILE, "r");

           if (!pid_file)
                   *retCode = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
           else
           {
                   rc = fscanf (pid_file, "%d", &pid);
                   FCLOSE (pid_file);

                   if (rc == 0 || rc == EOF)
                           *retCode = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
                   else
                   {
                           // now get start date/time by calling stat on /proc/pid
                           SNPRINTF(proc_dir, sizeof(proc_dir), "/proc/%d", pid);
                           if (stat(proc_dir, &st) == -1)
                           {
                                   *retCode = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
                           }
                           else
                           {
                                   // append this to stats
                                   stat_file = fopen(src_file_name, "a");
                                   FPRINTF(stat_file, "### Server start %lu ###\n", st.st_mtime);
                                   FCLOSE(stat_file);
                                   *xfer_file = TRUE;
                           }
                   }
           }
   }
cleanup:
	ILEAVE;
}

static ib_return_t
dns_cache_hit_ratio_request(const char *ptr,
			    ib_bool_t *xfer_file,
			    char *src_file_name,
			    int a_src_file_name_size,
			    char *dst_file_name,
			    int a_dst_file_name_size,
			    ib_uint32_t *retCode)
{
  IENTER;
  dns_chr_req_t *chr_p = (dns_chr_req_t *)ptr;

  ITEST(retCode, IERR_ARG);
  *retCode = IERR_ARG;

  ITEST(src_file_name, IERR_ARG);
  ITEST(a_src_file_name_size >= sizeof(IB_CACHE_HITMISS_FILE), IERR_ARG);
  ITEST(dst_file_name, IERR_ARG);
  ITEST(a_dst_file_name_size > 0, IERR_ARG);
  ITEST(chr_p, IERR_ARG);
  ITEST(strlen(chr_p->m_destination_file_name) > 0, IERR_ARG);

  *retCode = IERR_INVOCATION_SYSTEM_CALL_FAILURE;

  STRLCPY(src_file_name, IB_CACHE_HITMISS_FILE, a_src_file_name_size);
  STRLCPY(dst_file_name, chr_p->m_destination_file_name, a_dst_file_name_size);
  *xfer_file = TRUE;

  *retCode = IERR_SUCCESS;

 cleanup:
  ILEAVE;
}

/*
 * One time check zone for discrepancy
 */
static ib_return_t
parent_check_request(dns_parent_check_request_t *a_request, ib_uint32_t *a_status)
{
    IENTER;
    int rc = 0;
    ITEST(a_request, IERR_ARG);
    ITEST(a_status, IERR_ARG);

    IDEBUG("One time parent check request recieved for zone:%s", a_request->zone_key);
    /* start parent check daemon in onetime check mode */
    rc = iexec_cmd(
        "/infoblox/dns/bin/dnsintegritycheckd", "-z", a_request->zone_key, "start");
    if (rc == 0)
    {
        *a_status = IERR_SUCCESS;
    }
    else
    {
        *a_status = IERR_FAILURE;
    }
cleanup:
    ILEAVE;
}

static ib_return_t
idns_query(const char *ptr,
                   ib_bool_t *xfer_file,
                   char *src_file_name,
                   int a_src_file_name_size,
                   char *dst_file_name,
                   int a_dst_file_name_size,
                   ib_uint32_t *retCode)
{
    IENTER;
    IDEBUG("Got idns_query...");
    const idns_query_t *idns_query_params = (const idns_query_t*) ptr;

    ITEST((ptr), IERR_ARG);
    ITEST((src_file_name), IERR_ARG);
    ITEST((dst_file_name), IERR_ARG);
    ITEST((retCode), IERR_ARG);
    ITEST(idns_query_params, IERR_ARG);
    ITEST(idns_query_params->address, IERR_ARG);
    ITEST((strlen(idns_query_params->address)>0), IERR_ARG);
    ITEST(idns_query_params->qname, IERR_ARG);
    ITEST((strlen(idns_query_params->qname)>0), IERR_ARG);
    ITEST(idns_query_params->qtype, IERR_ARG);
    ITEST((strlen(idns_query_params->qtype)>0), IERR_ARG);
    ITEST(idns_query_params->lbdn_id, IERR_ARG);
    ITEST((strlen(idns_query_params->lbdn_id)>0), IERR_ARG);
    ITEST(idns_query_params->response_file_name, IERR_ARG);
    ITEST((strlen(idns_query_params->response_file_name)>0), IERR_ARG);

    SNPRINTF(src_file_name, a_src_file_name_size,
            IDNS_QUERY_RESULT_UTIL_FILE, idns_query_params->response_file_name);
    IDEBUG("src_file_name=%s", src_file_name);

    char qname[IOBJECT_MAX_STRING_LENGTH];
    ICALL(iutil_escape_dns_name(idns_query_params->qname, qname, sizeof(qname)));

    ICALL(iexec_cmd_status(retCode, "/usr/sbin/rndc",
                           "-c", "/infoblox/var/named_conf/rndc.conf",
                           "ib-dtc", "query",
                           idns_query_params->address,
                           qname,
                           idns_query_params->qtype,
                           idns_query_params->lbdn_id,
                           src_file_name));
    if (*retCode != IERR_SUCCESS) {
        IDEBUG("retCode=%d", *retCode);
        IRETURN (*retCode);
    };

    STRLCPY(dst_file_name, idns_query_params->response_file_name, a_dst_file_name_size);
    *retCode = IERR_SUCCESS;
    *xfer_file = TRUE;

cleanup:
    ILEAVE;
}

static ib_return_t
idns_health_check_manual(dtc_health_check_request_t *a_dtc_request,
                         dtc_health_check_response_t *a_dtc_response,
                         ib_uint32_t *a_status)
{
  IENTER;

  char tmp_dir[PATH_MAX] = {0};
  char cfg_filename[PATH_MAX] = {0};

  ITEST(a_dtc_request, IERR_ARG);
  ITEST(a_dtc_response, IERR_ARG);
  ITEST(a_status, IERR_ARG);

  IDEBUG("Manual DTC health check for remote server %s "
         "using DTC health monitor with ID of %s.",
         a_dtc_request->m_remote_srv, a_dtc_request->m_monitor_id);

  ICALL(iutil_create_tmp_dir("/storage/tmp", tmp_dir, sizeof (tmp_dir)));
  ICALL(iutil_create_tmp_file(tmp_dir, cfg_filename, sizeof(cfg_filename)));

  // Run the DTC healthd config generation tool
  ICALL(iexec_cmd_output(a_status,
                         a_dtc_response->m_output,
                         sizeof(a_dtc_response->m_output),
                         "/infoblox/dns/bin/make_dtc_conf",
                         "-t", "healthd",
                         "-e", cfg_filename,
                         "-s", a_dtc_request->m_remote_srv,
                         "-m", a_dtc_request->m_monitor_id));
  if (*a_status != 0)
  {
    ILOG("Failed to create DTC health check configuration for remote server %s "
         "using DTC health monitor with ID of %s.",
         a_dtc_request->m_remote_srv, a_dtc_request->m_monitor_id);
    IDEBUG("Return code of make_healthd_conf utility: %d, Output: %s",
           *a_status, a_dtc_response->m_output);
    IRETURN(IERR_SUCCESS);
  }

  // Run the manual health check
  ICALL(iexec_cmd_output(a_status,
                         a_dtc_response->m_output,
                         sizeof(a_dtc_response->m_output),
                         "/infoblox/dns/bin/idns_health_check",
                         "-o", "-f", "-c", cfg_filename));

  if (*a_status != 0)
  {
    ILOG("Failed to perform DTC health check for remote server %s "
         "using DTC health monitor with ID of %s.",
         a_dtc_request->m_remote_srv, a_dtc_request->m_monitor_id);
    IDEBUG("Return code of idns_healthd_check utility: %d, Output: %s",
           *a_status, a_dtc_response->m_output);
    IRETURN(IERR_SUCCESS);
  }

cleanup:
  // Cleanup temporary configuration file if it was created
  if (tmp_dir[0] != '\0')
  {
    ICLEANUPCALL(iutil_recursive_remove_directory(tmp_dir));
  }
  ILEAVE;
}

static ib_return_t
idns_set_object_disabled(dtc_set_disabled_request_t *a_dtc_request,
                         dtc_set_disabled_response_t *a_dtc_response,
                         ib_uint32_t *a_status)
{
  IENTER;

  ITEST(a_dtc_request, IERR_ARG);
  ITEST(a_dtc_response, IERR_ARG);
  ITEST(a_status, IERR_ARG);

  char object_disabled_str[2] = {0};
  char delay_str[64] = {0};
  char duration_str[64] = {0};
  char stop_health_monitoring_str[2] = {0};
  char disable_timeframe_str[64] = {0};
  sprintf(object_disabled_str, "%d", a_dtc_request->m_disabled);
  sprintf(delay_str, "%u", a_dtc_request->m_delay);
  sprintf(duration_str, "%u", a_dtc_request->m_duration);
  sprintf(disable_timeframe_str, "%u", a_dtc_request->m_disable_timeframe);
  sprintf(stop_health_monitoring_str, "%d", a_dtc_request->m_stop_health_monitoring);

  IDEBUG("disable object type %s with id %s disabled (%s) stop_health_monitoring_str (%s).",
          a_dtc_request->m_object_type, a_dtc_request->m_object_id, object_disabled_str,
          stop_health_monitoring_str);

  ICALL(iexec_cmd_status(a_status, "/usr/sbin/rndc",
                         "-c", "/infoblox/var/named_conf/rndc.conf",
                         "ib-dtc", "set_disabled",
                         a_dtc_request->m_object_type,
                         a_dtc_request->m_object_id,
                         object_disabled_str,
                         delay_str,
                         duration_str,
                         stop_health_monitoring_str,
			 disable_timeframe_str));

  a_dtc_response->m_ok = TRUE;

cleanup:
  ILEAVE;
}


#ifndef NM_CONFIG_FILE
#define NM_CONFIG_FILE "/infoblox/var/netmon.config"
#endif

// Check whether DNS network monitoring is enabled
static ib_return_t
dns_check_network_monitoring(dns_check_network_monitoring_resp_t *a_resp)
{
  IENTER;

  ib_bool_t  found = FALSE;

  ITEST(a_resp, IERR_ARG);
  a_resp->m_monitor_enabled = FALSE;

  // Netmon config file will exists only if netmon is enabled, so check
  // if config file exists.
  ICALL (iutil_file_exists (NM_CONFIG_FILE, &found));
  if (found)
    a_resp->m_monitor_enabled = TRUE;

 cleanup:
  ILEAVE;
}

static ib_return_t
dhcp_clear_nac_auth_cache(const nac_clear_cache_t* a_request,
			  ib_uint32_t *a_status)
{
  IENTER;

  int rc = 0;
  char normalized[MAC_ADDRESS_STRING_LENGTH] = "";
  ib_bool_t success = FALSE;

  IDEBUG ("Running clear NAC Auth cache...");
  ITEST (a_request, IERR_ARG);
  ITEST (a_status, IERR_ARG);

  if (strlen(a_request->m_member) > 0)
  {
    if (a_request->m_mac_addr && strlen(a_request->m_mac_addr) > 0)
    {
      // Clear cache with corresponding MAC address, first try to normalize
      // the given MAC address.
      ICALL(iutil_normalize_mac_address(a_request->m_mac_addr, normalized,
                                        sizeof(normalized), &success));
      if (success)
      {
        ILOG("Clearing auth cache for MAC address %s on member %s",
             a_request->m_mac_addr, a_request->m_member);
        rc = iexec_cmd("/infoblox/dns/bin/clear_auth_cache", normalized);
        if (rc != 0)
        {
          *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
          ITEST(0, IERR_SUCCESS);
        }
      }
      else
      {
        ILOG("Failed to clearing auth cache as Invalid MAC address %s is given",
             a_request->m_mac_addr);
      }
    }
    else
    {
      //clear all cache
      ILOG("Clearing entire auth cache on member %s", a_request->m_member);
      rc = iexec_cmd("/infoblox/dns/bin/clear_auth_cache");
      if (rc != 0)
      {
        *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
        ITEST(0, IERR_SUCCESS);
      }
    }
  }
  else
  {
    *a_status=IERR_INVALID_MEMBER_REFERENCE;
  }

  *a_status = IERR_SUCCESS;

cleanup:
  ILEAVE;
}

static ib_return_t
dhcp_set_nac_filters_switch(const nac_filters_switch_t* a_request,
                            ib_uint32_t *a_status)
{
    IENTER;

    ITEST (a_request, IERR_ARG);
    ITEST (a_status, IERR_ARG);

    int rc = iexec_cmd("/infoblox/dns/bin/set_nac_filters_switch",
                       a_request->m_disable ? "1" : "0");
    if (rc != 0)
    {
        *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
        ITEST(0, IERR_SUCCESS);
    }
    *a_status = IERR_SUCCESS;

cleanup:
    ILEAVE;
}

/**
* Clear DNS cache.
* When DNS Cache accleration is enabled, clear SmartNIC cache too.
* SmartNIC cache is cleared only when entire cache is going to be cleared -
* i.e. "view" and "domain" parameters are NULL.
*/
static ib_return_t
dns_clear_request(rtxml_instance* a_instance, const remote_request_t * a_req, ib_bool_t *xfer_file, ib_uint32_t *retCode)
{
        IENTER;
        int rc = 0, exit_code = 0;
        const dns_clear_cache_t * clear_cache_data = NULL;
        const char *domain = NULL, *view_name = NULL;
        ib_bool_t clear_full_tree = FALSE;
        const char *cmd, *arg0 = NULL, *arg1 = NULL;
        ib_bool_t named_running = FALSE;

        IDEBUG ("Running Clear DNS Cache...");

        ITEST ((retCode), IERR_ARG);
        ITEST((a_req), IERR_ARG);
        *retCode = IERR_SUCCESS;

        // checks if request contains domain or view information
        if (a_req->m_data_length == sizeof(dns_clear_cache_t))
        {
                clear_cache_data = (dns_clear_cache_t*)&a_req->m_additional[0];
                if (clear_cache_data && strlen(clear_cache_data->m_domain) > 0)
                        domain = clear_cache_data->m_domain;
                if (clear_cache_data && strlen(clear_cache_data->m_view_name) > 0)
                        view_name = clear_cache_data->m_view_name;
                clear_full_tree = clear_cache_data->m_clear_full_tree;
        }

        if (domain != NULL)
        {
                // clear only specified domain from cache
                cmd = clear_full_tree ? "flushtree" : "flushname";
                arg0 = domain;
                arg1 = view_name;
        }
        else
        {
                // clear the whole dns cache
                cmd = "flush";
                arg0 = view_name;
        }

        ICALL(iutil_is_process_running_strict(NAMED_PID_FILE, "named", TRUE, &named_running));

        if(named_running)
        {
            rc = iexec_cmd("/usr/sbin/rndc", "-c",
                           "/infoblox/var/named_conf/rndc.conf",
                           cmd, arg0, arg1);
        }
        
        if (rc != 0)
                *retCode = IERR_INVOCATION_SYSTEM_CALL_FAILURE;

        *xfer_file = FALSE;

        ib_bool_t is_dca_enabled = FALSE;
        ICALL(dns_is_cache_acceleration_enabled(a_instance,&is_dca_enabled));
        if( !is_dca_enabled )
            IRETURN (IERR_SUCCESS);

        char dca_view_name[IOBJECT_MAX_STRING_LENGTH];
        ib_bool_t is_incorrect_view = FALSE;
        memset(dca_view_name, 0, sizeof(dca_view_name));
        map_view_name_dca_view_name(a_instance, view_name, dca_view_name, &is_incorrect_view);
        ITEST(strlen(dca_view_name), IERR_ARG);
        if(is_incorrect_view) {
          ILOG("CLEAR_CACHE: the given NIOS view %s is not correct", view_name);
          IRETURN(IERR_SUCCESS);
        }

        if (domain == NULL && view_name == NULL){

          ILOG("CLEAR_CACHE:FP - delete all cache entries");
          ICALL(clear_fastpath_view_cache_entries(dca_view_name, NULL,
                                                          FALSE, &exit_code));
          if(exit_code != 0) {
            ILOG("CLEAR_CACHE:FP - unable to delete cache entries");
            IRETURN(IERR_SUCCESS);
          }
          
        }
        else
        {
          if (domain != NULL){
            if (clear_full_tree)
            {
         
              ILOG("CLEAR_CACHE: clear cache tree - view_name(%s) , domain(%s)", dca_view_name, domain );
              ICALL(clear_fastpath_view_cache_entries(dca_view_name, domain,
                                                                 TRUE, &exit_code));
              if(exit_code != 0) {
                ILOG("CLEAR_CACHE:FP - unable to delete cache entries");
                IRETURN(IERR_SUCCESS);
              }
              
            }
            else
            {
           
              ILOG("CLEAR_CACHE: clear cache entry FP view name(%s) , domain(%s)", dca_view_name, domain );
              ICALL(clear_fastpath_view_cache_entries(dca_view_name, domain,
                                                                FALSE, &exit_code));
              if(exit_code != 0) {
                ILOG("CLEAR_CACHE:FP - unable to delete cache entries");
                IRETURN(IERR_SUCCESS);
              }
              
            }
          } else {
              ILOG("CLEAR_CACHE: clear cache from view - FP view_name(%s) ", dca_view_name);
            ICALL(clear_fastpath_view_cache_entries(dca_view_name, NULL,
                                                            FALSE, &exit_code));
            if(exit_code != 0)
                IRETURN(IERR_SUCCESS);
            
          }
        }
cleanup:
        if( __RET )
            *retCode = __RET;
        ILEAVE;
}

static ib_return_t
dns_discovery_control_request(const dns_discovery_control_t * a_req_data, ib_bool_t *xfer_file, ib_uint32_t *retCode)
{
   IENTER;

   const char * str_control_type = NULL;
   const char * str_task_oid = NULL;

   ITEST ((retCode && a_req_data && xfer_file), IERR_ARG);

   switch (a_req_data->m_control_type)
   {
   case DISCOVERY_CONTROL_PAUSE:
       str_control_type = "PAUSE";
       break;
   case DISCOVERY_CONTROL_STOP:
       str_control_type = "STOP";
       break;
   default:
       ITEST(0, IERR_ARG);
   }

   str_task_oid = a_req_data->m_discovery_task_oid;

   IDEBUG ("Received discovery control request: %s...", str_control_type);

   ICALL(iexec_cmd("/infoblox/dns/bin/check_discovery_state", "-c",
                   str_control_type, "-t", str_task_oid));

   *xfer_file = FALSE;
   *retCode = IERR_SUCCESS;

cleanup:
   ILEAVE;
}

static ib_return_t
vdiscovery_connectivity_request(vdiscovery_connectivity_req_t *a_req,
                                int *a_err_code,
                                ib_uint32_t *a_status)
{
  IENTER;
  int rc = 0;
  char err_filename[] = "/tmp/_vdiscovery_connect_test_XXXXXX";
  int fd = -1;
  FILE *fp = NULL;
  char err_msg[IOBJECT_MAX_STRING_LENGTH] = {0};
  size_t bytes = 0;
  char msg[IOBJECT_MAX_STRING_LENGTH] = {0};
  iexec_args args = { .args = NULL };

  ITEST (a_req, IERR_ARG);
  ITEST (a_err_code, IERR_ARG);
  ITEST (a_status, IERR_ARG);

  // Required args
  ITEST (a_req->m_fqdn_or_ip, IERR_ARG);
  ITEST (a_req->m_username, IERR_ARG);
  ITEST (a_req->m_password, IERR_ARG);

  //IDEBUG("fqdn_or_ip: %s, username: %s, password: %s, conn_proto: %s, port: %d, timeout: %d", a_req->m_fqdn_or_ip, a_req->m_username, a_req->m_password, a_req->m_conn_proto, a_req->m_port, a_req->m_timeout);

  fd = mkstemp(err_filename);

  ICALL(iexec_init_args(NULL, &args));
  ICALL(iexec_add_args(&args, IEXEC_STATUS(&rc), IEXEC_OUTFILE(err_filename),
        "-err2out", "/infoblox/dns/bin/vdiscovery_client",
        "-type", "test",
        "-server", a_req->m_fqdn_or_ip,
        "-username", a_req->m_username,
        "-password", a_req->m_password));
  ICALL(iexec_add_arg(&args, "-timeout"));
  // Timeout value is in seconds, but vdiscovery_client expects
  // milliseconds, so multiply by 1000.
  ICALL(iexec_fmt_arg(&args, "%d", a_req->m_timeout * 1000));

  // Optional args
  if (a_req->m_conn_proto[0] != '\0') {
    ICALL(iutil_string_lower_inplace(a_req->m_conn_proto));
    ICALL(iexec_add_args(&args, "-protocol", a_req->m_conn_proto));
  }
  if (a_req->m_port > 0) {
    ICALL(iexec_add_arg(&args, "-port"));
    ICALL(iexec_fmt_arg(&args, "%d", a_req->m_port));
  }

  ICALL(iexec_cmdv(args.args));
  if (rc != 0)
  {
    *a_err_code = rc;
    fp = fopen(err_filename, "r");
    if (fp) {
      // Assume that we read the entire message unless there was
      // an error since the messages will be short.
      bytes = fread(err_msg, sizeof(char), sizeof(err_msg), fp);
    }
    if (bytes <= 0) {
      SNPRINTF(err_msg, sizeof(err_msg), "Reason unknown\n");
    }
    SNPRINTF(msg, sizeof(msg),
        "Unable to test connectivity for server '%s': %s",
        a_req->m_fqdn_or_ip, err_msg);
    IDEBUG("%s", msg);
  }

  // We need to send the status of success even in case of
  // a failure with the vdiscovery_client run in order
  // to propagate the error code properly.
  // a_err_code will indicate the error appropriately.
  *a_status = IERR_SUCCESS;

cleanup:
  iexec_free_args(&args);
  unlink(err_filename);
  CLOSE(fd);
  FCLOSE(fp);
  ILEAVE;
}

enum _named_compilezone_mode
{
  NAMED_COMPILEZONE_CONVERT = 0,
  NAMED_COMPILEZONE_EXTRACT_SERIAL
};

static ib_return_t
_named_compilezone(const char* a_input_file, const char* a_zone_fqdn, const char* a_output_file, enum _named_compilezone_mode a_mode)
{
  IENTER;

  int rc = 0;
  imempool *pool = NULL;
  char* output_string = NULL;
  iexec_args args = { .args = NULL };

  ITEST(a_input_file, IERR_ARG);
  ITEST(a_zone_fqdn, IERR_ARG);
  ITEST(a_output_file, IERR_ARG);

  ICALL(imempool_new(&pool, MEMPOOL_DEFAULT_PAGE_SIZE));
  ICALL(iexec_init_args(pool, &args));

  /* A couple of notes:
   * - We are turning all the checks off,
   * - We are capturing the output of the utility in a
   *   buffer to include in the infoblox.log in case
   *   of failure.
   */
  ICALL(iexec_add_args(&args,
    "-status", &rc,
    "-pool", pool,
    "-outptr", &output_string,
    "/usr/sbin/named-compilezone",
    "-k", "ignore",
    "-n", "ignore",
    "-m", "ignore",
    "-r", "ignore",
    "-i", "none",
    "-M", "ignore",
    "-S", "ignore",
    "-W", "ignore",
    "-f", "raw",
    "-o", a_output_file));

  if (a_mode == NAMED_COMPILEZONE_CONVERT)
    ICALL(iexec_add_args(&args, "-F", "text"));
  else if (a_mode == NAMED_COMPILEZONE_EXTRACT_SERIAL)
    ICALL(iexec_add_args(&args, "-z"));

  ICALL(iexec_add_args(&args,
    a_zone_fqdn,
    a_input_file));

  ICALL(iexec_cmdv(args.args));
  if (rc != 0)
  {
    ILOG("Cannot convert database for zone '%s': %s", a_zone_fqdn, output_string);
    ITEST(FALSE, IERR_INVOCATION_SYSTEM_CALL_FAILURE);
  }

cleanup:
  iexec_free_args(&args);
  if (pool)
    ICLEANUPCALL(imempool_delete(&pool));

  ILEAVE;
}

static ib_return_t
dns_dig_request (const char *ptr,
		 ib_bool_t *xfer_file,
		 char *src_file_name,
		 int a_src_file_name_size,
		 char *dst_file_name,
		 int a_dst_file_name_size,
		 ib_uint32_t *retCode)
{
   IENTER;
   const dns_sview_t *pZone;
   char db_file_name[IOBJECT_MAX_STRING_LENGTH]="";
   const char * is_root="";

   ITEST ((ptr), IERR_ARG);
   pZone =  (const dns_sview_t *) ptr;
   ITEST ((retCode), IERR_ARG);

   *retCode = IERR_SUCCESS;

   ICALL (iutil_create_directory ("/infoblox/var/sview", 0777, TRUE));

   if (!strcmp(pZone->dig_zone_name, ".")) is_root=".";

   SNPRINTF (src_file_name, a_src_file_name_size, "/infoblox/var/sview/dig.%s",
        pZone->zone_name);
   SNPRINTF (db_file_name, sizeof (db_file_name),
	     "/infoblox/var/named_conf/db.%s%s", is_root, pZone->zone_name);
   if(db_file_name[strlen(db_file_name) - 1] == '.')
        db_file_name[strlen(db_file_name) - 1] = '\0';

   IDEBUG("Origin=%s masterfile=%s srcfile=%s",
        pZone->dig_zone_name, db_file_name, src_file_name);

   ICALL(_named_compilezone(db_file_name, pZone->dig_zone_name, src_file_name, NAMED_COMPILEZONE_CONVERT));
   ISYSCALL(chmod(src_file_name, 0666));

   *xfer_file = TRUE;
   SNPRINTF (dst_file_name, a_dst_file_name_size,
        "/infoblox/var/sview/db.%s",
	     pZone->zone_name);

  cleanup:
   ILEAVE;
}

static ib_return_t
dns_serialno_request (const char *ptr,
		 ib_bool_t *xfer_file,
		 char *serialno,
		 int serialno_size,
		 ib_uint32_t *retCode)
{
   IENTER;
   const dns_sview_t *pZone;
   char db_file_name[PATH_MAX]="";
   char rawline[IOBJECT_MAX_STRING_LENGTH]="";
   FILE *serial_fp = NULL;
   char tmp_dir[PATH_MAX] = "";
   char tmp_serial_file[PATH_MAX] = "";
   ib_bool_t found = FALSE;
   const char * is_root="";

   ITEST (ptr, IERR_ARG);
   pZone =  (const dns_sview_t *) ptr;
   ITEST ((retCode), IERR_ARG);

   IDEBUG("Get serial number from zone db file for zone '%s'",
        pZone->zone_name);

   if (!strcmp(pZone->dig_zone_name, ".")) is_root=".";

   SNPRINTF (db_file_name, sizeof (db_file_name),
	     "/infoblox/var/named_conf/db.%s%s", is_root, pZone->zone_name);
   if(db_file_name[strlen(db_file_name) - 1] == '.')
        db_file_name[strlen(db_file_name) - 1] = '\0';


   ICALL (iutil_create_tmp_dir ("/storage/tmp", tmp_dir, sizeof (tmp_dir)));
   ICALL (iutil_create_tmp_file (tmp_dir, tmp_serial_file,
                sizeof (tmp_serial_file)));
   ICALL (iutil_file_exists (tmp_serial_file, &found));

   IDEBUG("origin=%s, dbfile=%s serialfile=%s file_exists=%d",
        pZone->dig_zone_name, db_file_name, tmp_serial_file, found);

   // Extract serial number from db file and put it inside a
   // temporary file
   ICALL(_named_compilezone(db_file_name, pZone->dig_zone_name, tmp_serial_file, NAMED_COMPILEZONE_EXTRACT_SERIAL));

   // Read tmp_file to get the serial number.
   // If tmp_serial_file or serial number is not found, return
   // not found error.
   serial_fp = fopen(tmp_serial_file, "r");
   if (!serial_fp)
   {
        ILOG("Serial number file '%s' not found", tmp_serial_file);
        *retCode = IERR_NOTFOUND;
        ITEST (0, IERR_NOTFOUND);
   }

   if (!fgets(rawline, sizeof(rawline), serial_fp) ||
        *rawline == '\0')
   {
        ILOG("Serial number not found in file '%s'", tmp_serial_file);
        *retCode = IERR_NOTFOUND;
        ITEST (0, IERR_NOTFOUND);
   }

   // Extract serial number from line read
   SNPRINTF(serialno, serialno_size, "%s", rawline);

   *retCode = IERR_SUCCESS;
   *xfer_file = FALSE;

  cleanup:
    FCLOSE(serial_fp);
    if (tmp_dir[0])
        iutil_recursive_remove_directory(tmp_dir);
    ILEAVE;
}

/**
 * Execute ping for ping_address.py
 **/
static ib_return_t
dns_pa_ping_request (dns_pa_ping_t *p_pa_ping,
                     char *a_rtt_str_array,
                     ib_uint32_t a_rtt_str_array_len)
{
    IENTER;
    FILE *fp = NULL;
    const struct timespec ts = {.tv_sec = 0, .tv_nsec = 50 * 1000 * 1000};
    char tmp_rtt[IOBJECT_MAX_STRING_LENGTH] = "";
    char tmp_addr[IOBJECT_MAX_STRING_LENGTH] = "";
    char tmp_file[IOBJECT_MAX_STRING_LENGTH] = "";
    char tmp_dir[PATH_MAX]= "";
    char line[IOBJECT_MAX_STRING_LENGTH] = "";
    char rtt_str[IOBJECT_MAX_STRING_LENGTH] = "";
    char count_str[IOBJECT_MAX_STRING_LENGTH] = "";
    char timeout_str[IOBJECT_MAX_STRING_LENGTH] = "";
    char *pd = NULL;
    char *pe = NULL;
    const char *args[8];
    ib_uint32_t cur_addr = 0;
    ib_uint32_t start_addr32 = 0;
    ib_uint32_t end_addr32 = 0;
    ib_uint32_t i = 0;
    ib_uint32_t cpy_len = 0;
    ib_uint32_t test_val = 0;
    ib_int32_t pids[PING_MAX_NUM_PIDS];
    ib_bool_t any_pid_alive = FALSE;
    pid_t pid = 0;
    int rc = 0;
    int status = 0;
    ITEST ((p_pa_ping), IERR_ARG);

    /* Create tmp folder */
    ICALL (iutil_create_tmp_dir ("/storage/tmp", tmp_dir, sizeof (tmp_dir)));

    /* Prepare ping arguments */
    SNPRINTF (count_str, sizeof (count_str), "%u"
            , (unsigned int)p_pa_ping->m_count);
    SNPRINTF (timeout_str, sizeof (timeout_str), "%u"
            , (unsigned int)p_pa_ping->m_timeout);
    args[0] = "/bin/ping";
    args[1] = "-b";
    args[2] = "-c";
    args[3] = count_str;
    args[4] = "-w";
    args[5] = timeout_str;
    args[6] = "";
    args[7] = NULL;

    /* Init pids */
    for (i = 0; i < PING_MAX_NUM_PIDS; i++)
        pids[i] = -1;

    /* Execute ping concurrently */
    ICALL (address_to_uint32 (p_pa_ping->m_start_address, &start_addr32));
    ICALL (address_to_uint32 (p_pa_ping->m_end_address, &end_addr32));
    cur_addr = start_addr32;
    while (1)
    {
        any_pid_alive = FALSE;
        /* Execute PING_MAX_NUM_PIDS number of ping processes at a time */
        for (i = 0; i < PING_MAX_NUM_PIDS; i++)
        {
            /* Child process */
            if (pids[i] >= 0)
            {
                rc = waitpid(pids[i], &status, WNOHANG);
                if (rc == 0)
                {
                    any_pid_alive = TRUE;
                    continue;
                }
                else if (rc < 0)
                {
                    if (errno == EINTR)
                        any_pid_alive = TRUE;
                    else
                        IDEBUG("Received error in waitpid, errno=%d", errno);
                    continue;
                }
            }
            /* Parent process */
            if (cur_addr <= end_addr32)
            {
                /* Fork ping process */
                ICALL (uint32_to_address (cur_addr, tmp_addr, sizeof (tmp_addr)));
                args[6] = tmp_addr;
                SNPRINTF (tmp_file, sizeof (tmp_file)
                        , "%s/%s.txt", tmp_dir, tmp_addr);
                ICALL(iprocess_forkexec_with_std_inout("/bin/ping", args
                                , &pid, FALSE, NULL, tmp_file, tmp_file));
                pids[i] = pid;
                any_pid_alive = TRUE;
                cur_addr++;
            }
        }
        /* Exit condition */
        if (!any_pid_alive)
            break;
        nanosleep (&ts, NULL); // wait .05 secs
    }

    /* Generate return values */
    for (i = start_addr32; i <= end_addr32; i++)
    {
        ICALL (uint32_to_address (i, tmp_addr, sizeof (tmp_addr)));
        SNPRINTF (tmp_file, sizeof (tmp_file)
                    , "%s/%s.txt", tmp_dir, tmp_addr);
        STRLCPY(rtt_str, "0", IOBJECT_MAX_STRING_LENGTH); /* For TIME_OUT case */
        ITEST (fp = fopen (tmp_file, "r"), IERR_IO);
        while (fgets (line, sizeof(line), fp))
        {
            /* Try to find avg rtt
            * eg. "rtt min/avg/max/mdev = 0.013/0.015/0.019/0.002 ms"
            *     avg rtt is 0.015.
            */
            if (strstr(line, "rtt") == line)
            {
                pd = strchr(line, '=');
                if (pd)
                {
                    /* eg. " 0.013/0.015/0.019/0.002 ms" */
                    STRLCPY(tmp_rtt, pd+1, sizeof(tmp_rtt));
                    pd = strchr(tmp_rtt, '/');
                    if (pd)
                    {
                        /* eg. "0.015/0.019/0.002 ms" */
                        STRLCPY(tmp_rtt, pd+1, sizeof(tmp_rtt));
                        pe = strchr(tmp_rtt, '/');
                        if (pd)
                        {
                            /* eg. "0.015" */  /* For ACTIVE case */
                            cpy_len = (ib_uint32_t)(pe-tmp_rtt+1);
                            test_val = strlcpy(rtt_str, tmp_rtt, cpy_len);
                            ITEST (test_val < IOBJECT_MAX_STRING_LENGTH, IERR_BUFF_OVF);
                        }
                    }
                }
            }
        }
	FCLOSE (fp);
        STRLCAT(a_rtt_str_array, rtt_str, a_rtt_str_array_len);
        STRLCAT(a_rtt_str_array, "/", a_rtt_str_array_len);
    }
  cleanup:
    /* Remove tmp folder */
    if (fp)
       fclose(fp);

    if (tmp_dir[0])
        iutil_recursive_remove_directory (tmp_dir);
    ILEAVE;
}

/**
 * Execute ping6 for ping_address.py
 **/
static ib_return_t
dns_pa_ping6_request (dns_pa_ping6_t *p_pa_ping,
                     char *a_rtt_str_array,
                     ib_uint32_t a_rtt_str_array_len)
{
    IENTER;
    FILE *fp = NULL;
    const struct timespec ts = {.tv_sec = 0, .tv_nsec = 50 * 1000 * 1000};
    char tmp_rtt[IOBJECT_MAX_STRING_LENGTH] = "";
    char tmp_file[IOBJECT_MAX_STRING_LENGTH] = "";
    char tmp_dir[PATH_MAX]= "";
    char line[IOBJECT_MAX_STRING_LENGTH] = "";
    char rtt_str[IOBJECT_MAX_STRING_LENGTH] = "";
    char count_str[IOBJECT_MAX_STRING_LENGTH] = "";
    char timeout_str[IOBJECT_MAX_STRING_LENGTH] = "";
    char *pd = NULL;
    char *pe = NULL;
    const char *args[7];
    ib_uint32_t cpy_len = 0;
    ib_uint32_t test_val = 0;
    ib_int32_t chpid = -1;
    ib_bool_t pid_alive = FALSE;
    ib_bool_t forked = FALSE;
    pid_t pid = 0;
    int rc = 0;
    int status = 0;
    ITEST ((p_pa_ping), IERR_ARG);

    /* Create tmp folder */
    ICALL (iutil_create_tmp_dir ("/storage/tmp", tmp_dir, sizeof (tmp_dir)));
    /* Build tmp file path */
    SNPRINTF (tmp_file, sizeof (tmp_file),
              "%s/%s.txt", tmp_dir, p_pa_ping->m_address);

    /* Prepare ping arguments */
    SNPRINTF (count_str, sizeof (count_str), "%u"
            , (unsigned int)p_pa_ping->m_count);
    SNPRINTF (timeout_str, sizeof (timeout_str), "%u"
            , (unsigned int)p_pa_ping->m_timeout);
    args[0] = "/bin/ping6";
    args[1] = "-c";
    args[2] = count_str;
    args[3] = "-w";
    args[4] = timeout_str;
    args[5] = "";
    args[6] = NULL;

    /* Execute ping */
    while (1)
    {
        pid_alive = FALSE;
        /* Child process */
        if (chpid >= 0)
        {
            rc = waitpid(chpid, &status, WNOHANG);
            if (rc == 0)
            {
                pid_alive = TRUE;
                continue;
            }
            else if (rc < 0)
            {
                if (errno == EINTR)
                    pid_alive = TRUE;
                else
                    IDEBUG("Received error in waitpid, errno=%d", errno);
            }
        }
        /* Parent process */
        if (!forked)
        {
            /* Fork ping process */
            args[5] = p_pa_ping->m_address;
            ICALL(iprocess_forkexec_with_std_inout("/bin/ping6", args
                            , &pid, FALSE, NULL, tmp_file, tmp_file));
            chpid = pid;
            pid_alive = TRUE;
            forked=TRUE;
        }

        /* Exit condition */
        if (!pid_alive)
            break;
        nanosleep (&ts, NULL); // wait .05 secs
    }

    /* Generate return values */
    STRLCPY(rtt_str, "0", IOBJECT_MAX_STRING_LENGTH); /* For TIME_OUT case */
    ITEST (fp = fopen (tmp_file, "r"), IERR_IO);
    while (fgets (line, sizeof(line), fp))
    {
        /* Try to find avg rtt
        * eg. "rtt min/avg/max/mdev = 0.013/0.015/0.019/0.002 ms"
        *     avg rtt is 0.015.
        */
        if (strstr(line, "rtt") == line)
        {
            pd = strchr(line, '=');
            if (pd)
            {
                /* eg. " 0.013/0.015/0.019/0.002 ms" */
                STRLCPY(tmp_rtt, pd+1, sizeof(tmp_rtt));
                pd = strchr(tmp_rtt, '/');
                if (pd)
                {
                    /* eg. "0.015/0.019/0.002 ms" */
                    STRLCPY(tmp_rtt, pd+1, sizeof(tmp_rtt));
                    pe = strchr(tmp_rtt, '/');
                    if (pe)
                    {
                        /* eg. "0.015" */  /* For ACTIVE case */
                        cpy_len = (ib_uint32_t)(pe-tmp_rtt+1);
                        test_val = strlcpy(rtt_str, tmp_rtt, cpy_len);
                        ITEST (test_val < IOBJECT_MAX_STRING_LENGTH, IERR_BUFF_OVF);
                        break;
                    }
                }
            }
        }
    }
    fclose (fp);
    STRLCAT(a_rtt_str_array, rtt_str, a_rtt_str_array_len);
  cleanup:
    /* Remove tmp folder */
    if (tmp_dir[0])
        iutil_recursive_remove_directory (tmp_dir);
    ILEAVE;
}

static ib_return_t
copy_file_to_buffer(const char *filename, dns_fqdn_dig_response_t *a_resp)
{
    IENTER;
    FILE *fp = NULL;
    size_t len;
    ITEST (filename, IERR_ARG);
    ITEST (a_resp, IERR_ARG);
    fp = fopen(filename,"r");
    if (fp != NULL) {
        len = fread(a_resp->m_result_text, sizeof(char), DIG_RESULT_STRING_LEN, fp);
        if ( ferror( fp ) != 0 ) {
            IRETURN(IERR_FAILURE);
        } 
        else {
             a_resp->m_result_text[len++] = '\0'; /* Just to be safe. */
        }
    }
    cleanup:
        fclose(fp);
        ILEAVE;
}

static ib_return_t
execute_dig_request(int *rc, dns_fqdn_dig_request_t *a_request, const char *filename,
                    ib_bool_t is_short, ib_bool_t is_ip_address, char name_server_arg[],
                    ib_bool_t is_source_address, ib_bool_t is_timeout, ib_bool_t is_tries)
{
    IENTER;
    ITEST (a_request, IERR_ARG);
    ITEST (filename, IERR_ARG);
    ITEST (name_server_arg, IERR_ARG);

    // Initialize iexec_args with a fixed size
    iexec_args params;  //dig params

    // Allocate and copy command components
    ICALL(iexec_init_args(NULL, &params));
    ICALL(iexec_add_args(&params, "/usr/bin/dig"));
    ICALL(iexec_add_args(&params, name_server_arg));

    if (is_ip_address) {
        ICALL(iexec_add_args(&params, "-x"));
        ICALL(iexec_add_args(&params, a_request->m_fqdn));
    } else {
        ICALL(iexec_add_args(&params, a_request->m_fqdn));
    	ICALL(iexec_add_args(&params, a_request->m_record_type));
    }

    if (is_source_address) {
        ICALL(iexec_add_args(&params, "-b"));
    	ICALL(iexec_add_args(&params, a_request->m_source_address));
    }

    if (is_timeout) {
        char timeout_arg[32];
        SNPRINTF(timeout_arg, sizeof(timeout_arg), "+timeout=%d", a_request->m_timeout);
        ICALL(iexec_add_args(&params, timeout_arg));
    }

    if (is_tries) {
        char tries_arg[32];
        SNPRINTF(tries_arg, sizeof(tries_arg), "+tries=%d", a_request->m_tries);
        ICALL(iexec_add_args(&params, tries_arg));
    }

    if (!a_request->m_recursive_query) {
        ICALL(iexec_add_args(&params, "+norecurse"));
    }

    if (is_short) {
        ICALL(iexec_add_args(&params, "+short"));
    }

    /**
     * Execute the constructed dig command and redirect output to file
     *
     * Examples:
     * 1. Query an A record from a specific DNS server:
     *    /usr/bin/dig @*********** a.in A
     *
     * 2. Query any record type from the localhost DNS server with additional options:
     *    /usr/bin/dig @localhost a.in Any -b 127.0.0.1 +norecurse +short
     */
    ICALL(iexec_cmd_status(rc, IEXEC_OUTFILE(filename), params.args[0], params.args[1],
          params.args[2], params.args[3], params.args[4], params.args[5], params.args[6], 
          params.args[7], params.args[8], params.args[9], params.args[10], params.args[11]));

    IDEBUG("Return code from dig command = %d", *rc);

cleanup:
    iexec_free_args(&params);
    ILEAVE;
}

#define DIG_HEADER_PREFIX_LENGTH 38
static ib_return_t
read_rcode_from_dig_output(const char *filename, int *rc, char *res_code)
{
    IENTER;
    ITEST (filename, IERR_ARG);
    ITEST (res_code, IERR_ARG);
    ITEST (rc, IERR_ARG);
    if (rc != 0) {
        IDEBUG("Non-zero return code from dig output rc = %d", *rc);
    }

    FILE *output_file;
    // Read the return code from the dig output file
    switch (*rc) {
      case 0:
        output_file = fopen(filename, "r");
        if (output_file != NULL) {
          char line[256];
          while (fgets(line, sizeof(line), output_file)) {
              if (strncmp(line, ";; ->>HEADER<<- opcode: QUERY, status:", DIG_HEADER_PREFIX_LENGTH) == 0) {
                  char *status_field = strtok(line, ",");
                  status_field = strtok(NULL, ",");
                  if (status_field != NULL) {
                      char *status_str = strtok(status_field, " ");
                      status_str = strtok(NULL, " ");
                      if (status_str != NULL) {
                          strncpy(res_code, status_str, IOBJECT_MAX_STRING_LENGTH - 1);
                          res_code[IOBJECT_MAX_STRING_LENGTH - 1] = '\0';
                          break;
                      }
                  }
              }
          }
          fclose(output_file);
      }
        break;
      case 8:
        strncpy(res_code, "COULD_NOT_OPEN_BATCH_FILE", IOBJECT_MAX_STRING_LENGTH - 1);
        break;
      case 9:
        strncpy(res_code, "NO_REPLY_FROM_SERVER", IOBJECT_MAX_STRING_LENGTH - 1);
        break;
      case 10:
        strncpy(res_code, "UNKNOWN_ERROR", IOBJECT_MAX_STRING_LENGTH - 1);
        break;
      default:
        // Handle other cases if needed
        break;
    }
    // Null terminate the res_code string
    res_code[IOBJECT_MAX_STRING_LENGTH - 1] = '\0';
cleanup:
    // Clean up code here
    ILEAVE;
}

/**
 * Execute dig request on specific fqdn.
 * Typical invocation: dig @server fqdn record_type,
 * if fqdn is ip address then dig @server -x fqdn,
 * if server is local host add option 'source ip address': -b *********,
 * if query is not recursive add option +norecurse
 * NIOS-75578 - Modification to funtion
 * Redirect the dig output to file and then copy to buffer
 * If response for the fqdn is more than 8K, use +short option
 * If dig +short response is more than 8k, return error
 **/

static ib_return_t
dns_fqdn_dig_request(dns_fqdn_dig_request_t *a_request,
                     dns_fqdn_dig_response_t *a_resp)
{
    IENTER;

    char name_server_arg[IOBJECT_MAX_STRING_LENGTH] = "";
    ib_bool_t is_ip_address = FALSE;
    ib_bool_t is_source_address = FALSE;
    ib_bool_t is_timeout = FALSE;
    ib_bool_t is_tries = FALSE;
    int rc = 0;
    char res_code[IOBJECT_MAX_STRING_LENGTH] = "";
    ib_bool_t fexists = FALSE;
    ib_int64_t fsize = 0;
    ib_bool_t is_short = FALSE;
    const char *cmd_output_filename = "/tmp/dig_output";
    ITEST (a_request, IERR_ARG);
    ITEST (a_resp, IERR_ARG);
    memset(a_resp, 0, sizeof(a_resp));
    IDEBUG("Dig request arguments: server = %s fqdn = %s record type = %s recursive = %d source address = %s",
            a_request->m_name_server, a_request->m_fqdn, a_request->m_record_type,
            a_request->m_recursive_query, a_request->m_source_address);
    if (strlen(a_request->m_source_address) > 0)
        is_source_address = TRUE;

    if (a_request->m_timeout > 0)
        is_timeout = TRUE;

    if (a_request->m_tries > 0)
        is_tries = TRUE;

    SNPRINTF(name_server_arg, sizeof(name_server_arg), "@%s",
             strlen(a_request->m_name_server) > 0 ? a_request->m_name_server : "localhost");
    /* First Execute dig command without +short option */
    ICALL(execute_dig_request(&rc, a_request, cmd_output_filename, is_short, is_ip_address,
                              name_server_arg, is_source_address, is_timeout, is_tries));

    ICALL(iutil_file_size64( cmd_output_filename, &fexists, &fsize));
    /* If size of output if more than 8k,use short */
    if (fsize >= DIG_RESULT_STRING_LEN && !(is_short)){
        is_short = TRUE;
        ICALL(execute_dig_request(&rc, a_request, cmd_output_filename, is_short, is_ip_address,
                                  name_server_arg, is_source_address, is_timeout, is_tries));
        ICALL(iutil_file_size64( cmd_output_filename, &fexists, &fsize));
    }
    /* If dig +short output is more than 8k, throw error */
    if (fsize >= DIG_RESULT_STRING_LEN){
         STRLCPY(a_resp->m_result, "BUFFER_FLOW", sizeof(a_resp->m_result));
         goto cleanup;
    }
    /* copy file contents to buffer */
    ICALL(copy_file_to_buffer(cmd_output_filename,a_resp));

    /* Read return code from dig output */
    ICALL(read_rcode_from_dig_output(cmd_output_filename, &rc, res_code));

    IDEBUG("Dig request result: %d output = %s", rc, a_resp->m_result_text);
    if (sizeof(res_code)/sizeof(char*) > 0)
    {
        STRLCPY(a_resp->m_result, res_code, sizeof(a_resp->m_result));
    }
    if (is_short && strncmp(a_resp->m_result, "NOERROR", 7) == 0) {
      /* We have use +short option ,copy "NOERROR_SHORT" to result*/
      STRLCPY(a_resp->m_result, "NOERROR_SHORT", sizeof(a_resp->m_result));
    }

cleanup:
    ICALL(iutil_file_exists(cmd_output_filename, &fexists));
    if (fexists){
        ITEST(unlink(cmd_output_filename) == 0, IERR_SYSCALL);
    }
    ILEAVE;
}

/**
 * Update httpd template file used for proxing pages to NetMRI
 **/
static ib_return_t
tae_update_proxy_conf(tae_proxy_config_req_t *ptr,
                      ib_uint32_t *a_status)
{
  IENTER;
  FILE *filep = NULL;
  char storepass[] = "taestore";
  char cFile[PATH_MAX] = "/storage/etc/tae-proxy.conf";
  char ksFile[PATH_MAX] = "/infoblox/security/certs/tae_ca_cert.ks";
  char pemFile[PATH_MAX] = "/tmpfs/tae_reg_cert.pem";
  char tgts[39][20] = { "netmri", "skipjack", "webui", "rails", "styles",
    "gridsearch-mods", "gridsearch", "skipjackForm.js", "skipjackTx.js",
    "skipjackTable.js", "netmri.js", "help.js", "statusBlock.js", "buttons",
    "terminal", "WebViewer", "yfiles-flex", "yfiles-flex-netmri", "visual",
    "ExportChart", "amcharts", "miframe-mods", "api/base_uri",
    "api/server_info", "api/docs", "api/2.2", "api/2.3", "api/2.4",
    "api/2.5", "api/2.6", "api/2.7", "api/2.8", "api/2.9", "api/2.10",
    "api/3", "api/3.0", "api/3.1", "api/3.2" , "api/3.3" };
  int size_of_tgts = sizeof( tgts ) / 20;
  int x = 0;
  int rc = 0;

  ITEST (ptr->m_nmURL, IERR_ARG);

  /* Erase files if deregister or not, skip errors */
  unlink(ksFile);
  unlink(cFile);

  /* if URL is not empty. create the mappings */
  if (ptr->m_nmURL[0] != '\0') {
    /* If we have a pem file, create java keystore */
    if ((filep = fopen(pemFile, "r"))) {
      FCLOSE(filep);

      /* Create new keystore */
      rc = iexec_cmd("/usr/local/jre/bin/keytool", "-importcert",
                     "-alias", "TAE", "-file", pemFile,
                     "-keystore", ksFile, "-storepass", storepass,
                     "-noprompt");
      unlink(pemFile);

      if (rc != 0) {
        *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
        ITEST_QUIET(0, IERR_SUCCESS);
        goto cleanup;
      }
    }

    /* Create/overwrite tae-proxy.conf file */
    ITEST (filep = fopen (cFile, "w"), IERR_IO);

    for (x = 0; x < size_of_tgts; x++) {
      fprintf(filep,
        "    <Location /%s>\n"
        "        ProxyPass %s/%s\n"
        "        ProxyPassReverse %s/%s\n"
        "    </Location>\n",
        tgts[x],
        ptr->m_nmURL, tgts[x],
        ptr->m_nmURL, tgts[x]);
    }

    /* Map api differently */
    fprintf(filep,
      "    <Location /napi>\n"
      "        ProxyPass %s/api\n"
      "        ProxyPassReverse %s/api\n"
      "    </Location>\n",
      ptr->m_nmURL,
      ptr->m_nmURL);

    FCLOSE(filep);
  }

  /* This will update apache config */
  rc = iexec_cmd("/infoblox/one/bin/make_httpd_conf",
                 "ONE_CONFIG=/tmpfs/one-httpd.conf");
  if (rc != 0) {
    *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
    ITEST_QUIET(0, IERR_SUCCESS);
  } else {
    /* Send apache graceful restart if OK */
    rc = iexec_cmd("/usr/bin/apachectl", "-k", "graceful",
                   "-f", "/tmpfs/one-httpd.conf", "-DSSL");
    if (rc != 0) {
      *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
      ITEST_QUIET(0, IERR_SUCCESS);
    }

    *a_status = IERR_SUCCESS;
  }

 cleanup:
  ILEAVE;
}

ib_return_t
dns_run_task (rtxml_instance* a_instance,
	      iipc_session* a_session,
	      iipc_address* a_remote_address,
	      remote_request_t *a_req_msg)
{
   IENTER;
   remote_response_t  *resp_msg = NULL;
   ib_bool_t          xfile_flag = FALSE;
   char               src_file[MAX_FILE_NAME_SZ], dst_file[MAX_FILE_NAME_SZ];
   int                rc = 0, resp_size = 0;
   char               *resp_data = NULL;
   ib_uint32_t        status = IERR_SUCCESS;
   dns_check_network_monitoring_resp_t mon_resp_msg;
   dtc_health_check_request_t *dtc_request = NULL;
   dtc_set_disabled_request_t *dtc_disable_request = NULL;
   dtc_set_disabled_response_t dtc_disable_response;
   dtc_health_check_response_t dtc_monitor_resp;
   dns_fqdn_dig_response_t fqdn_dig_response;

   IDEBUG ("Received a run dns request");
   ITEST ((a_instance), IERR_ARG);
   ITEST ((a_session), IERR_ARG);
   ITEST ((a_remote_address), IERR_ARG);

              switch (a_req_msg->m_opCode) {
                case DM_VIEW_DNS_STATS_REQUEST:
                    ICALL (dns_stats_request (a_req_msg->m_additional,
					      &xfile_flag, src_file,
					      sizeof (src_file), dst_file,
					      sizeof (dst_file), &status));
                    break;
                case DM_CLEAR_DNS_CACHE_REQUEST:
                    ICALL (dns_clear_request (a_instance, a_req_msg,
                          &xfile_flag, &status));
                    break;

	      case DM_DNS_CACHE_HIT_RATIO_REQUEST:
	      	ICALL (dns_cache_hit_ratio_request(a_req_msg->m_additional,
						   &xfile_flag, src_file,
						   sizeof (src_file), dst_file,
						   sizeof (dst_file), &status));
		break;

              case DM_DISCOVERY_CONTROL_REQUEST:
                ICALL(dns_discovery_control_request((dns_discovery_control_t *) &a_req_msg->m_additional[0], &xfile_flag, &status));
                break;

              case DM_VDISCOVERY_CONNECTIVITY:
                ICALL(inm_free((void **)&resp_msg));
                ICALL(inm_calloc(1, sizeof(remote_response_t) +
                      sizeof(int), (void **)&resp_msg));
                int err_code = 0;
                ICALL(vdiscovery_connectivity_request((vdiscovery_connectivity_req_t*)&a_req_msg->m_additional[0], &err_code, &status));
                resp_size = sizeof(int);
                resp_data = (char *)&err_code;
                break;
		case DM_DNS_ZONE_DIG_REQUEST:
                    ICALL (dns_dig_request (a_req_msg->m_additional,
					    &xfile_flag, src_file,
					    sizeof (src_file), dst_file,
					    sizeof (dst_file), &status));
					break;
				case DM_DNS_ZONE_SERIALNO_REQUEST:
					resp_size = SOA_SERIAL_NUM_LENGTH;
					char serialno[SOA_SERIAL_NUM_LENGTH] = "";
                    ICALL (dns_serialno_request (a_req_msg->m_additional,
					    &xfile_flag, serialno, resp_size, &status));
					resp_data = serialno;
                    break;
                case DM_DNS_CLEAR_SEC_DB_REQUEST:
                    IDEBUG("Ignoring obsolete DM_DNS_CLEAR_SEC_DB_REQUEST.");
                    break;
                case DM_DNS_PA_PING_REQUEST:
                    resp_size = PING_RESULT_SIZE;
                    char rtt_str_array[PING_RESULT_SIZE] = "";
                    ICALL(dns_pa_ping_request(
                            (dns_pa_ping_t *) &a_req_msg->m_additional[0],
                            rtt_str_array, sizeof(rtt_str_array)));
                    resp_data = rtt_str_array;
                    break;
                case DM_DNS_PA_PING6_REQUEST:
                    resp_size = PING6_RESULT_SIZE;
                    char rtt_str[PING6_RESULT_SIZE] = "";
                    ICALL(dns_pa_ping6_request(
                            (dns_pa_ping6_t *) &a_req_msg->m_additional[0],
                            rtt_str, sizeof(rtt_str)));
                    resp_data = rtt_str;
                    break;
                case DM_DNS_CHECK_NETWORK_MONITORING:
                    resp_size = sizeof(mon_resp_msg);
                    resp_data = (char *)&mon_resp_msg;
                    ICALL(dns_check_network_monitoring(&mon_resp_msg));
                    break;
                case DM_TAE_UPDATE_PROXY_CONF_REQUEST:
                    ICALL(tae_update_proxy_conf(
                            (tae_proxy_config_req_t *) &a_req_msg->m_additional[0],
                            &status));
                    break;
              case DM_VIEW_DDOS_STATS_REQUEST:
                    ICALL(ddos_stats_request(a_req_msg->m_additional,
                                             &xfile_flag, src_file,
                                             sizeof (src_file), dst_file,
                                             sizeof (dst_file), &status));
                    break;
              case DM_DNS_PARENT_CHECK_REQUEST:
                  ICALL(parent_check_request(
                      (dns_parent_check_request_t *)a_req_msg->m_additional, &status));
                  break;
              case DM_IDNS_QUERY:
                    ICALL(idns_query(a_req_msg->m_additional,
                                             &xfile_flag, src_file,
                                             sizeof (src_file), dst_file,
                                             sizeof (dst_file), &status));
                    break;
              case DM_IDNS_HEALTH_CHECK_MANUAL:
                  ITEST(a_req_msg->m_data_length == sizeof(dtc_health_check_request_t), IERR_FAILURE);
                  dtc_request = (dtc_health_check_request_t *) &a_req_msg->m_additional[0];

                  resp_data = (char *) &dtc_monitor_resp;
                  resp_size = sizeof(dtc_health_check_response_t);
                  ICALL(idns_health_check_manual(dtc_request, &dtc_monitor_resp, &status));
                  break;
              case DM_IDNS_SET_DISABLED:
                  ITEST(a_req_msg->m_data_length == sizeof(dtc_set_disabled_request_t), IERR_FAILURE);
                  dtc_disable_request = (dtc_set_disabled_request_t *) &a_req_msg->m_additional[0];

                  resp_data = (char *) &dtc_disable_response;
                  resp_size = sizeof(dtc_disable_response);
                  ICALL(idns_set_object_disabled(dtc_disable_request, &dtc_disable_response, &status));
                  break;
              case DM_DNS_FQDN_DIG_REQUEST:
                    ICALL(dns_fqdn_dig_request((dns_fqdn_dig_request_t *) &a_req_msg->m_additional[0],
                                               &fqdn_dig_response));
                    resp_size = sizeof(fqdn_dig_response);
                    resp_data = (char *)&fqdn_dig_response;
                    break;
              default:
                    status   = IERR_ARG;
                }


       IDEBUG("File Generation Status = %x", status);

       if ((xfile_flag == TRUE) && (status == IERR_SUCCESS))
         {
           ICALL(one_xfer_text_file(a_req_msg->m_local_request, dst_file, src_file, FALSE, &rc));

           if (rc != 0)
              status = IERR_FILE_XFER_FAILED;

           IDEBUG("File Xfer status = %x", status);
         }


       if (a_req_msg->m_reply)
         {
           ICALL (inm_calloc (1, sizeof (remote_response_t)+ resp_size, (void**)&resp_msg));
           resp_msg->m_magic    = DM_PROTOCOL_MAGIC;
           resp_msg->m_status   = status;
           resp_msg->m_data_length = resp_size;
           if (resp_size > 0)
             memcpy(resp_msg->m_additional, resp_data, resp_size);
           ICALL (dm_send_timed_return_message (a_session,
						a_remote_address, resp_msg,
						sizeof (remote_response_t) +
						    resp_msg->m_data_length,
						a_req_msg->m_timeout));

           if (resp_msg)
	     inm_free ((void **)&resp_msg);
         }

cleanup:
  if (resp_msg)
   {
	inm_free ((void **)&resp_msg);
   }

	ILEAVE;
}

static ib_return_t
dhcp_lease_history_transfer (rtxml_instance *a_instance,
    dhcp_lease_history_search_request_t *a_query,
    ib_bool_t *a_xfer_file,
    char *a_src_file_name,
    int a_src_file_name_size,
    char *a_dest_file_name,
    int a_dest_file_name_size,
    dhcp_lease_history_search_response_t *a_response)
{
  IENTER;
  int                      rc=0;
  ib_bool_t db_open = FALSE;
  istrhash *nv_hash = NULL;
  istrhash *mb_hash = NULL;
  istrhash *ms_hash = NULL;
  imempool *pool = NULL;

  ITEST (a_instance, IERR_ARG);
  ITEST ((a_query), IERR_ARG);
  ITEST ((a_src_file_name), IERR_ARG);
  ITEST ((a_dest_file_name), IERR_ARG);
  ITEST ((a_response), IERR_ARG);

  a_response->m_search_response.m_status = IERR_FAILURE;
  a_response->m_search_response.m_goto_position = -1;

  /* Open the database and start a logdb txn */
  ICALL (onedb_database_open_defer_transaction ());
  db_open = TRUE;

  // Open regular db txn to get network view names
  // (needed to filter by network_view)
  ICALL (onedb_database_start_transaction(ONEDB_OPEN_READ_ONLY,
        a_query->m_entity));
  ICALL (imempool_new(&pool, MEMPOOL_DEFAULT_PAGE_SIZE));
  // These caches are needed for filtering purposes.
  // Opening a regular DB transaction while the logdb transaction
  // is open causes some issues, so we build caches of objects
  // to use for filtering for object members here.
  ICALL (build_network_view_cache(pool, a_instance, &nv_hash));
  ICALL (build_grid_member_cache(pool, a_instance, &mb_hash));
  ICALL (build_ms_server_cache(pool, a_instance, &ms_hash));
  // No need to commit a read txn
  ICALL (onedb_database_abort_transaction());

  // Open logdb txn
  ICALL (onedb_database_logdb_start_transaction (ONEDB_OPEN_READ_ONLY,
						 a_query->m_entity));
  ICALL(dns_process_lease_history_export(a_instance,
                                         LEASE_HISTORY_EXPORT_FILE,
                                         nv_hash,
                                         mb_hash,
                                         ms_hash,
                                         a_query,
                                         a_response));

  if (a_response->m_search_response.m_status != IERR_SUCCESS)
  {
    ILOG("dns_process_lease_history_export returned: %d",
        a_response->m_search_response.m_status);
    IRETURN(IERR_SUCCESS);
  }

  /* commit the logdb txn */
  ICALL (onedb_database_logdb_commit_transaction ());

  ICALL(iutil_rm_f(LEASE_HISTORY_EXPORT_FILE_GZ));

  rc = iexec_cmd("/bin/gzip", LEASE_HISTORY_EXPORT_FILE);
  if (rc != 0)
    {
      a_response->m_search_response.m_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
      ITEST(0,IERR_SUCCESS);
    }

  STRLCPY (a_src_file_name, LEASE_HISTORY_EXPORT_FILE_GZ,
	   a_src_file_name_size);
  STRLCPY (a_dest_file_name, a_query->m_search_request.m_dest_file_name,
	   a_dest_file_name_size);

  *a_xfer_file = TRUE;
  a_response->m_search_response.m_status = IERR_SUCCESS;

 cleanup:
  if (db_open)
    {
      onedb_database_logdb_abort_transaction ();
      onedb_database_close_no_transactions_active ();
    }
  if (pool)
    {
    ICLEANUPCALL(imempool_delete(&pool));
    }
  ILEAVE;
}

static ib_return_t
dhcp_lease_history_download(dhcp_lease_history_search_request_t *a_query, ib_uint32_t *a_status)
{
   IENTER;
   int rc = -1;

   ITEST ((a_query), IERR_ARG);
   ITEST ((a_status), IERR_ARG);

   *a_status = IERR_FAILURE;

   // Kluge (just as with support_files) to pass directory in as "file name".
   ICALL(iexec_cmd_status(&rc, "/infoblox/dns/bin/download_lease_history",
                         a_query->m_search_request.m_dest_file_name));
   if (rc != 0)
     {
       *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
       ITEST(0,IERR_SUCCESS);
     }

   *a_status = IERR_SUCCESS;

 cleanup:
   ILEAVE;
}

static ib_return_t
dhcp_lease_history_unpack(dhcp_lease_history_search_request_t *a_query, ib_uint32_t *a_status)
{
   IENTER;
   int                      rc=0;

   ITEST ((a_query), IERR_ARG);
   ITEST ((a_status), IERR_ARG);

   *a_status = IERR_FAILURE;

   // Kluge (just as with support_files) to pass directory in as "file name".
   rc = iexec_cmd("/infoblox/dns/bin/unpack_lease_history",
                  a_query->m_search_request.m_dest_file_name);
   if (rc != 0)
     {
       *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
       ITEST(0,IERR_SUCCESS);
     }

   *a_status = IERR_SUCCESS;

 cleanup:
   ILEAVE;
}

static ib_return_t
dhcp_lease_history_import(rtxml_instance *a_instance,
                          ib_bool_t a_local_request,
                          dhcp_lease_history_import_t *a_import,
                          ib_uint32_t *a_status)
{
  IENTER;
  char dir_name[MAX_FILE_NAME_SZ];
  int rc = 0;
  char file_name[MAX_FILE_NAME_SZ];
  char zipped_file_name[MAX_FILE_NAME_SZ];
  char src_file_name[MAX_FILE_NAME_SZ];
  ib_bool_t db_open = FALSE;

  ITEST ((a_import), IERR_ARG);
  ITEST ((a_status), IERR_ARG);

  ICALL (iutil_create_tmp_dir ("/storage/tmp", dir_name, sizeof (dir_name)));
  SNPRINTF (file_name, sizeof(file_name), "%s/dhcpLeaseHistory.csv",
	    dir_name);
  SNPRINTF (zipped_file_name, sizeof(zipped_file_name),
	    "%s/dhcpLeaseHistory.csv.gz", dir_name);

  *a_status = IERR_FAILURE;

  SNPRINTF (src_file_name, sizeof(src_file_name), "%s/import_file",
	    a_import->m_src_dir_name);
  ICALL(one_xfer_text_file(a_local_request, zipped_file_name, src_file_name, TRUE, &rc));
  if (rc != 0)
    {
      *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
      ITEST(0,IERR_SUCCESS);
    }

  rc = iexec_cmd("/infoblox/dns/bin/unpack_lease_history", dir_name);
  if (rc != 0)
    {
      *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
      ITEST(0,IERR_SUCCESS);
    }

  /* Open the database and start a logdb txn */
  ICALL (onedb_database_open_defer_transaction ());
  db_open = TRUE;
  ICALL (onedb_database_logdb_start_transaction (ONEDB_OPEN_READ_WRITE,
						 ONEDB_DEFAULT_SUPERUSER));

  ICALL(dns_process_lease_history_import(a_instance, file_name, a_status));

  if (*a_status != IERR_SUCCESS)
    IRETURN(IERR_SUCCESS);

  /* commit the logdb txn */
  ICALL (onedb_database_logdb_commit_transaction ());

 cleanup:
  if (db_open)
    {
      onedb_database_logdb_abort_transaction ();
      onedb_database_close_no_transactions_active ();
    }
  iutil_recursive_remove_directory (dir_name);
  ILEAVE;
}

/**
 * Copy uploaded DHCP expert mode config file to given filename.
 **/
static ib_return_t
dhcp_expert_mode_config_upload(dhcp_file_t *a_request,
                    ib_uint32_t *a_status)
{
  IENTER;
  int rc = 0;

  ITEST (a_request, IERR_ARG);
  ITEST (a_status, IERR_ARG);

  ITEST (a_request->m_directory, IERR_ARG);
  ITEST (a_request->m_vnode_oid, IERR_ARG);

  rc = iexec_cmd("/infoblox/dns/bin/upload_dhcp_expert_mode_config",
                 a_request->m_directory, a_request->m_vnode_oid);
  if (rc != 0)
    {
      *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
      ITEST_QUIET(0, IERR_SUCCESS);
    }

  *a_status = IERR_SUCCESS;

 cleanup:
  ILEAVE;
}

/**
 * Copy DHCP expert mode config file to given directory.
 **/
static ib_return_t
dhcp_expert_mode_config_download(dhcp_file_t *a_request,
                    ib_uint32_t *a_status)
{
  IENTER;
  int rc = 0;

  ITEST (a_request, IERR_ARG);
  ITEST (a_status, IERR_ARG);

  ITEST (a_request->m_directory, IERR_ARG);

  rc = iexec_cmd("/infoblox/dns/bin/download_dhcp_expert_mode_config",
                 a_request->m_directory, a_request->m_vnode_oid);
  if (rc != 0)
    {
      *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
      ITEST_QUIET(0, IERR_SUCCESS);
    }

  *a_status = IERR_SUCCESS;

 cleanup:
  ILEAVE;
}

static ib_return_t
dhcp_expert_mode_config_remove(dhcp_file_t *a_request_file,
                                ib_uint32_t *a_ret_code )
{
  IENTER;
  char full_path [IOBJECT_MAX_STRING_LENGTH];

  ITEST (a_request_file , IERR_ARG);
  ITEST (a_ret_code, IERR_ARG);

  snprintf( full_path ,  sizeof( full_path ) , "%s/%s" , a_request_file->m_directory ,
                  a_request_file->m_vnode_oid );
  *a_ret_code = unlink( full_path );

cleanup:

  ILEAVE;
}

static ib_return_t
dhcp_fo_state_change_request(dhcp_fo_state_change_req_t *a_request,
                                ib_uint32_t *a_status )
{
  IENTER;
  int rc = 0;

  ITEST (a_request, IERR_ARG);
  ITEST (a_status, IERR_ARG);

  iexec_cmd("-status", &rc, "-errfile", "/tmp/_partner_change_state.err",
            "/infoblox/dns/bin/partner_change_state",
            a_request->m_partner_state, a_request->m_peer_ip_addr,
            a_request->m_failover_assoc_name, a_request->m_dhcp_updater_key, a_request->m_dhcp_updater_key_alg);

  if (rc != 0)
    {
      ILOG("Unable to put the failover peer into the %s state", a_request->m_partner_state);
      *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
      ITEST_QUIET(0, IERR_SUCCESS);
    }

  *a_status = IERR_SUCCESS;

cleanup:

  ILEAVE;
}

static ib_return_t
dhcp_lease_state_change_request(dhcp_lease_state_change_req_t *a_request,
                                ib_uint32_t *a_status )
{
  IENTER;
  int rc = 0;

  ITEST (a_request, IERR_ARG);
  ITEST (a_status, IERR_ARG);

  iexec_cmd("-status", &rc, "-outfile", "/tmp/_lease_change_state.err",
            "-err2out", "/infoblox/dns/bin/lease_change_state",
            a_request->m_lease_state, a_request->m_dhcp_srvr_ip_addr,
            a_request->m_dhcp_updater_key, a_request->m_lease_ip,
            a_request->m_duid, a_request->m_dhcp_updater_key_alg);

  if (rc != 0)
    {
      ILOG("Unable to free the lease %s", a_request->m_lease_ip);
      *a_status = IERR_INVOCATION_SYSTEM_CALL_FAILURE;
      ITEST_QUIET(0, IERR_SUCCESS);
    }

  *a_status = IERR_SUCCESS;

cleanup:
  ILEAVE;
}

static ib_return_t
dhcp_clear_ms_lease_request(dhcp_clear_ms_lease_req_t *a_request,
        dhcp_clear_ms_lease_resp_t *a_resp)
{
    IENTER;

    int rc = 0;

    ITEST (a_request, IERR_ARG);
    ITEST (a_resp, IERR_ARG);

    iexec_cmd("-status", &rc, "-outsize", sizeof(a_resp->m_err_str), "-outbuf", a_resp->m_err_str, "/infoblox/dns/bin/clear_ms_lease", a_request->m_ms_dhcp_server_oid, a_request->m_lease_ip);

    a_resp->m_ret_flag = (rc != IERR_SUCCESS);
    if (rc != 0)
    {
        ILOG("Unable to free the lease %s for Microsoft server %s: %s", a_request->m_lease_ip, a_request->m_ms_dhcp_server_oid, a_resp->m_err_str);
        ITEST_QUIET(0, IERR_SUCCESS);
    }

cleanup:
    ILEAVE;
}

static ib_return_t
dhcp_stats_request (const char *ptr,
           ib_bool_t *xfer_file,
           char *src_file_name,
           int a_src_file_name_size,
           char *dst_file_name,
           int a_dst_file_name_size,
           ib_bool_t is_ipv4,
           ib_uint32_t *retCode)
{
    IENTER;
    const dhcp_stats_t *pDhcp = (const dhcp_stats_t*) ptr;

    IDEBUG ("Running %s Stats report...", (is_ipv4 ? "DHCP": "DHCPv6"));
    ITEST((ptr), IERR_ARG);
    ITEST((src_file_name), IERR_ARG);
    ITEST((dst_file_name), IERR_ARG);
    ITEST((retCode), IERR_ARG);
    ITEST((strlen(pDhcp->m_destination_file_name)>0), IERR_ARG);
    IDEBUG ("destFile length=%zu, %s", strlen(pDhcp->m_destination_file_name), pDhcp->m_destination_file_name);

    if (is_ipv4) {
        STRLCPY (src_file_name, DHCP_PACKETS_STAT_MULTI_FILE, a_src_file_name_size);
    } else {
        STRLCPY (src_file_name, DHCP_V6_PACKETS_STAT_MULTI_FILE, a_src_file_name_size);
    }
    STRLCPY (dst_file_name, pDhcp->m_destination_file_name, a_dst_file_name_size);

    *retCode = IERR_SUCCESS;
    *xfer_file = TRUE;

cleanup:
    ILEAVE;
}

ib_return_t
dhcp_run_task (rtxml_instance* a_instance,
               iipc_session* a_session,
               iipc_address* a_remote_address,
               remote_request_t *a_req_msg)
{
  IENTER;
  remote_response_t  *resp_msg = NULL;
  ib_bool_t          xfile_flag = FALSE;
  char               src_file[MAX_FILE_NAME_SZ], dest_file[MAX_FILE_NAME_SZ];
  int                rc = 0, resp_size = 0;
  char               *resp_data = NULL;
  ib_uint32_t        status = IERR_SUCCESS;
  dhcp_lease_history_search_response_t lease_history_response = {0};
  dhcp_clear_ms_lease_resp_t clear_ms_lease_response = {0};

  ITEST ((a_instance), IERR_ARG);
  ITEST ((a_session), IERR_ARG);
  ITEST ((a_remote_address), IERR_ARG);

        switch (a_req_msg->m_opCode) {
        case DM_DHCP_LEASE_HISTORY_TRANSFER:
          ICALL(dhcp_lease_history_transfer(a_instance,
                (dhcp_lease_history_search_request_t *) a_req_msg->m_additional,
                &xfile_flag,
                src_file,
                sizeof (src_file),
                dest_file,
                sizeof (dest_file),
                &lease_history_response));
          resp_size = sizeof(dhcp_lease_history_search_response_t);
          resp_data = (char *)&lease_history_response;
          status = lease_history_response.m_search_response.m_status;
          break;
        case DM_DHCP_LEASE_HISTORY_DOWNLOAD:
          ICALL(dhcp_lease_history_download((dhcp_lease_history_search_request_t *) &a_req_msg->m_additional[0],  &status));
          break;
        case DM_DHCP_LEASE_HISTORY_UNPACK:
          ICALL(dhcp_lease_history_unpack((dhcp_lease_history_search_request_t *) &a_req_msg->m_additional[0],  &status));
          break;
        case DM_DHCP_LEASE_HISTORY_IMPORT:
          ICALL(dhcp_lease_history_import(a_instance,
                                          a_req_msg->m_local_request,
                                          (dhcp_lease_history_import_t *) &a_req_msg->m_additional[0],
                                          &status));
          break;
        case DM_DHCP_EXPERT_MODE_CONFIG_UPLOAD:
          ICALL(dhcp_expert_mode_config_upload((dhcp_file_t *) &a_req_msg->m_additional[0],  &status));
          break;
        case DM_DHCP_EXPERT_MODE_CONFIG_DOWNLOAD:
          ICALL(dhcp_expert_mode_config_download((dhcp_file_t *) &a_req_msg->m_additional[0],  &status));
          break;
        case DM_DHCP_EXPERT_MODE_CONFIG_REMOVE:
          ICALL(dhcp_expert_mode_config_remove((dhcp_file_t *) &a_req_msg->m_additional[0],  &status));
          break;
        case DM_DHCP_FO_STATE_CHANGE_REQUEST:
          ICALL(dhcp_fo_state_change_request((dhcp_fo_state_change_req_t *) &a_req_msg->m_additional[0],  &status));
          break;
        case DM_DHCP_LEASE_STATE_CHANGE_REQUEST:
          ICALL(dhcp_lease_state_change_request((dhcp_lease_state_change_req_t *) &a_req_msg->m_additional[0],  &status));
          break;
        case DM_DHCP_CLEAR_MS_LEASE_REQUEST:
          ICALL(dhcp_clear_ms_lease_request((dhcp_clear_ms_lease_req_t *) &a_req_msg->m_additional[0], &clear_ms_lease_response));
          resp_size = sizeof(clear_ms_lease_response);
          resp_data = (char *)&clear_ms_lease_response;
          break;
        case DM_DHCP_STATS_REQUEST:
          ICALL(dhcp_stats_request(a_req_msg->m_additional, &xfile_flag, src_file, sizeof (src_file), dest_file, sizeof (dest_file), TRUE, &status));
          break;
        case DM_DHCP_V6_STATS_REQUEST:
          ICALL(dhcp_stats_request(a_req_msg->m_additional, &xfile_flag, src_file, sizeof (src_file), dest_file, sizeof (dest_file), FALSE, &status));
          break;
        case DM_DHCP_CLEAR_NAC_AUTH_CACHE_REQUEST:
          ICALL(dhcp_clear_nac_auth_cache((nac_clear_cache_t*)&a_req_msg->m_additional[0], &status));
          break;
        case DM_DHCP_SET_NAC_FILTERS_SWITCH:
            ICALL(dhcp_set_nac_filters_switch((nac_filters_switch_t*)&a_req_msg->m_additional[0], &status));
          break;
        default:
          status   = IERR_ARG;
        }

      if ((xfile_flag == TRUE) && (status == IERR_SUCCESS))
        {
          ICALL(one_xfer_text_file(a_req_msg->m_local_request, dest_file, src_file, FALSE, &rc));

          if (rc != 0)
            status = IERR_FILE_XFER_FAILED;

          IDEBUG("File Xfer status = %x", status);
        }


      if (a_req_msg->m_reply)
        {
          ICALL (inm_calloc (1, sizeof (remote_response_t)+ resp_size, (void**)&resp_msg));
          resp_msg->m_magic    = DM_PROTOCOL_MAGIC;
          resp_msg->m_status   = status;
          resp_msg->m_data_length = resp_size;
          if (resp_size > 0)
            memcpy(resp_msg->m_additional, resp_data, resp_size);
          ICALL (dm_send_timed_return_message (a_session,
					       a_remote_address, resp_msg,
					       sizeof (remote_response_t) +
						   resp_msg->m_data_length,
					       a_req_msg->m_timeout));
          if (resp_msg)
            inm_free ((void **)&resp_msg);
        }

 cleanup:
  if (resp_msg)
    {
      inm_free ((void **)&resp_msg);
    }

  ILEAVE;
}

ib_return_t
dns_get_opcodes_definition(const dm_opcode_definition_t** a_list, ib_uint32_t* a_count)
{
    IENTER;

    /* Test all inputs arguments */
    ITEST ((a_list), IERR_ARG);
    ITEST ((a_count), IERR_ARG);

    static const dm_opcode_definition_t definitions[] = {
        {.m_opcode = DM_VIEW_DNS_STATS_REQUEST,
         .m_current_version = DM_VIEW_DNS_STATS_REQUEST_VERSION,
         .m_support_cross_version = TRUE},
        {.m_opcode = DM_CLEAR_DNS_CACHE_REQUEST,
         .m_current_version = DM_CLEAR_DNS_CACHE_REQUEST_VERSION,
         .m_support_cross_version = FALSE},
        {.m_opcode = DM_DNS_ZONE_DIG_REQUEST,
         .m_current_version = DM_DNS_ZONE_DIG_REQUEST_VERSION,
         .m_support_cross_version = TRUE},
        {.m_opcode = DM_DNS_CLEAR_SEC_DB_REQUEST,
         .m_current_version = DM_DNS_CLEAR_SEC_DB_REQUEST_VERSION,
         .m_support_cross_version = TRUE},
        {.m_opcode = DM_DNS_ZONE_SERIALNO_REQUEST,
         .m_current_version = DM_DNS_ZONE_SERIALNO_REQUEST_VERSION,
         .m_support_cross_version = TRUE},
        {.m_opcode = DM_DISCOVERY_CONTROL_REQUEST,
         .m_current_version = DM_DISCOVERY_CONTROL_REQUEST_VERSION,
         .m_support_cross_version = FALSE},
        {.m_opcode = DM_DNS_PA_PING_REQUEST,
         .m_current_version = DM_DNS_PA_PING_REQUEST_VERSION,
         .m_support_cross_version = FALSE}, //GridMaster only
        {.m_opcode = DM_VDISCOVERY_CONNECTIVITY,
         .m_current_version = DM_VDISCOVERY_CONNECTIVITY_VERSION,
         .m_support_cross_version = FALSE},
        {.m_opcode = DM_DNS_PA_PING6_REQUEST,
         .m_current_version = DM_DNS_PA_PING6_REQUEST_VERSION,
         .m_support_cross_version = FALSE}, //GridMaster only
        {.m_opcode = DM_DNS_CACHE_HIT_RATIO_REQUEST,
         .m_current_version = DM_DNS_CACHE_HIT_RATIO_REQUEST_VERSION,
         .m_support_cross_version = TRUE},
        {.m_opcode = DM_DNS_CHECK_NETWORK_MONITORING,
         .m_current_version = DM_DNS_CHECK_NETWORK_MONITORING_VERSION,
         .m_support_cross_version = TRUE},
        {.m_opcode = DM_TAE_UPDATE_PROXY_CONF_REQUEST,
         .m_current_version = DM_TAE_UPDATE_PROXY_CONF_REQUEST_VERSION,
         .m_support_cross_version = FALSE}, //GridMaster only
        {.m_opcode = DM_VIEW_DDOS_STATS_REQUEST,
         .m_current_version = DM_VIEW_DDOS_STATS_REQUEST_VERSION,
         .m_support_cross_version = TRUE},
        {.m_opcode = DM_DNS_PARENT_CHECK_REQUEST,
         .m_current_version = DM_DNS_PARENT_CHECK_REQUEST_VERSION,
         .m_support_cross_version = TRUE},
        {.m_opcode = DM_IDNS_QUERY,
         .m_current_version = DM_IDNS_QUERY_VERSION,
         .m_support_cross_version = TRUE},
        {.m_opcode = DM_IDNS_HEALTH_CHECK_MANUAL,
         .m_current_version = DM_IDNS_HEALTH_CHECK_MANUAL_VERSION},
        {.m_opcode = DM_DNS_FQDN_DIG_REQUEST,
         .m_current_version = DM_DNS_FQDN_DIG_REQUEST_VERSION,
         .m_support_cross_version = TRUE},
        {.m_opcode = DM_IDNS_SET_DISABLED,
         .m_current_version = DM_IDNS_SET_DISABLED_VERSION},
    };

    *a_list = definitions;
    *a_count = sizeof(definitions)/sizeof(dm_opcode_definition_t);

cleanup:
    ILEAVE;
}

ib_return_t
dhcp_get_opcodes_definition(const dm_opcode_definition_t** a_list, ib_uint32_t* a_count)
{
  IENTER;

  /* Test all inputs arguments */
  ITEST ((a_list), IERR_ARG);
  ITEST ((a_count), IERR_ARG);

  static const dm_opcode_definition_t definitions[] = { {.m_opcode = DM_DHCP_LEASE_HISTORY_TRANSFER,
                                              .m_current_version = DM_DHCP_LEASE_HISTORY_TRANSFER_VERSION,
                                              .m_support_cross_version = TRUE},
                                                        {.m_opcode = DM_DHCP_LEASE_HISTORY_DOWNLOAD,
                                              .m_current_version = DM_DHCP_LEASE_HISTORY_DOWNLOAD_VERSION,
                                              .m_support_cross_version = TRUE},
                                                        {.m_opcode = DM_DHCP_LEASE_HISTORY_IMPORT, // GridMaster only
                                              .m_current_version = DM_DHCP_LEASE_HISTORY_DOWNLOAD_VERSION,
                                              .m_support_cross_version = TRUE},
                                                        {.m_opcode = DM_DHCP_EXPERT_MODE_CONFIG_UPLOAD,
                                              .m_current_version = DM_DHCP_EXPERT_MODE_CONFIG_UPLOAD_VERSION,
                                              .m_support_cross_version = FALSE},
                                                        {.m_opcode = DM_DHCP_EXPERT_MODE_CONFIG_DOWNLOAD,
                                              .m_current_version = DM_DHCP_EXPERT_MODE_CONFIG_DOWNLOAD_VERSION,
                                              .m_support_cross_version = FALSE},
                                                        {.m_opcode = DM_DHCP_EXPERT_MODE_CONFIG_REMOVE,
                                              .m_current_version = DM_DHCP_EXPERT_MODE_CONFIG_REMOVE_VERSION,
                                              .m_support_cross_version = FALSE},
                                                        {.m_opcode = DM_DHCP_FO_STATE_CHANGE_REQUEST,
                                              .m_current_version = DM_DHCP_FO_STATE_CHANGE_REQUEST_VERSION,
                                              .m_support_cross_version = TRUE},
                                                        {.m_opcode = DM_DHCP_LEASE_STATE_CHANGE_REQUEST,
                                              .m_current_version = DM_DHCP_LEASE_STATE_CHANGE_REQUEST_VERSION,
                                              .m_support_cross_version = TRUE},
                                                        {.m_opcode = DM_DHCP_CLEAR_MS_LEASE_REQUEST,
                                              .m_current_version = DM_DHCP_CLEAR_MS_LEASE_REQUEST_VERSION,
                                              .m_support_cross_version = FALSE},
                                                        {.m_opcode = DM_DHCP_STATS_REQUEST,
                                              .m_current_version = DM_DHCP_STATS_REQUEST_VERSION,
                                              .m_support_cross_version = TRUE},
                                                        {.m_opcode = DM_DHCP_CLEAR_NAC_AUTH_CACHE_REQUEST,
                                              .m_current_version = DM_DHCP_CLEAR_NAC_AUTH_CACHE_REQUEST_VERSION,
                                              .m_support_cross_version = TRUE},
                                                        {.m_opcode = DM_DHCP_V6_STATS_REQUEST,
                                              .m_current_version = DM_DHCP_V6_STATS_REQUEST_VERSION,
                                              .m_support_cross_version = TRUE},
                                                        {.m_opcode = DM_DHCP_SET_NAC_FILTERS_SWITCH,
                                              .m_current_version = DM_DHCP_SET_NAC_FILTERS_SWITCH_VERSION,
                                              .m_support_cross_version = TRUE},
                                  };

  *a_list = definitions;
  *a_count = sizeof(definitions)/sizeof(dm_opcode_definition_t);

cleanup:
  ILEAVE;
}
