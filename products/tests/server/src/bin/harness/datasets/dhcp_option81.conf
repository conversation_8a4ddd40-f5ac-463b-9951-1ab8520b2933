# dhcpd conf file for dhcp_option81.sh
local-address 127.0.0.1;
server-identifier 127.0.0.1;
ddns-update-style interim;
option domain-name "test.com";
not authoritative;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
log-facility daemon;
ping-check false;

ddns-updates on;
deny client-updates;
ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
ddns-hostname = pick ( option fqdn.hostname, option host-name );
option host-name = config-option server.ddns-hostname;
update-static-leases false;

subnet ********* netmask ********* {
        pool {
                infoblox-range ********** **********;
                range ********** **********;
                range ********** **********;
        }
}
zone "test.com." {
        primary 127.0.0.1;
}
