<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
  <PROPERTY NAME="record_type_num" VALUE="65"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1742012804"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="enable_host_name_policy" VALUE="false"/>
  <PROPERTY NAME="__timestamp" VALUE="1742012804409128"/>
  <PROPERTY NAME="zone" VALUE="._default.com.test"/>
  <PROPERTY NAME="name" VALUE="unk"/>
  <PROPERTY NAME="record_type" VALUE="TYPE65"/>
  <PROPERTY NAME="record_rdata" VALUE="16 foo.example.org. mandatory=&quot;alpn,ipv4hint&quot; alpn=&quot;h2,h3-19&quot; port=1234 ech=AEn+DQBFKwAgACABWIHUGj4u+PIggYXcR5JF0gYk3dCRioBW8uJq9H4mKAAIAAEAAQABAANAEnB1YmxpYy50bHMtZWNoLmRldgAA ipv4hint=********* ipv6hint=2001:db8::2 no-default-alpn key65333=&quot;ex1&quot; key65444=&quot;ex2&quot;"/>
  <PROPERTY NAME="record_rdata_hash" VALUE="3c164943e35dc550cd6cdbae0d4a050c16289fedef91c4c03ed92b9564fad4ec638a4636648ceea4fd5640cee840c9bb79a2b8fe824d12253b848af42d6d3465"/>
  <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
  <PROPERTY NAME="display_name" VALUE="unk"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
  <PROPERTY NAME="record_type_num" VALUE="65"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1742012805"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="enable_host_name_policy" VALUE="false"/>
  <PROPERTY NAME="__timestamp" VALUE="1742012804409128"/>
  <PROPERTY NAME="zone" VALUE="._default.com.temp"/>
  <PROPERTY NAME="name" VALUE="unk"/>
  <PROPERTY NAME="record_type" VALUE="TYPE65"/>
  <PROPERTY NAME="record_rdata" VALUE="100 example.org. mandatory=alpn,ipv6hint alpn=&quot;h2,http/1.1&quot; port=1234 ech=AEn+DQBFKwAgACABWIHUGj4u+PIggYXcR5JF0gYk3dCRioBW8uJq9H4mKAAIAAEAAQABAANAEnB1YmxpYy50bHMtZWNoLmRldgAA ipv4hint=&quot;***********&quot; ipv6hint=2000:db6::8 no-default-alpn key33=&quot;ex1&quot; key40=&quot;ex2&quot;"/>
  <PROPERTY NAME="record_rdata_hash" VALUE="cb074eedea5bf66eecfbb29027da7e47029531e7224ef958273a523e0e9c208128628bb2bc660a92d5ba810fd0b270728d88ffc5378a40c52a77b2d713c4669b"/>
  <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
  <PROPERTY NAME="display_name" VALUE="unk"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="object" VALUE=".com.infoblox.dns.bind_resource_record$._default.com.temp/unk/65/cb074eedea5bf66eecfbb29027da7e47029531e7224ef958273a523e0e9c208128628bb2bc660a92d5ba810fd0b270728d88ffc5378a40c52a77b2d713c4669b"/>
  <PROPERTY NAME="tag" VALUE=".IB Discovery Owned"/>
  <PROPERTY NAME="value" VALUE="abcd"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.bind_resource_record$._default.com.temp/unk/65/cb074eedea5bf66eecfbb29027da7e47029531e7224ef958273a523e0e9c208128628bb2bc660a92d5ba810fd0b270728d88ffc5378a40c52a77b2d713c4669b"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.test-admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
</DATABASE>
