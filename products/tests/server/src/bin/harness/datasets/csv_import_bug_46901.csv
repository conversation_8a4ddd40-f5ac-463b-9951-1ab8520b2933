Header-HostRecord,fqdn,view,addresses,ipv6_addresses,aliases,configure_for_dns,comment,disabled,ttl,configure_for_dhcp,mac_address,match_option,routers
HostRecord,h1.test.com,default,"********",,,TRUE,Host record,False,,TRUE,11:11:11:11:11:11,MAC_ADDRESS,********
HostRecord,h2.test.com,default,"********,********",,,TRUE,Host record,False,,,,,
HostRecord,h3.test.com,default,"********,********",,,TRUE,,,,,,,
Header-<PERSON><PERSON><PERSON><PERSON>,parent,,address,,,,,,,,configure_for_dhcp,mac_address,match_option,routers
HostAddress,h3.test.com,,********,,,,,,,,TRUE,44:44:44:44:44:44,MAC_ADDRESS,
