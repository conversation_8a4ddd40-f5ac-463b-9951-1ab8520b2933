# Start mem del->LRB
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0b:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0b:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0b:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0b:63";
  binding_state "free";
}
# End mem del->LRB
# Start mem->mem
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0c:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0c:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0c:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0c:63";
  binding_state "free";
}
# End mem->mem
# Start mem->FA (sec same)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0d:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0d:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0d:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0d:63";
  binding_state "free";
}
# End mem->FA (sec same)
# Start mem->FA (pri same)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0e:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0e:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0e:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0e:63";
  binding_state "free";
}
# End mem->FA (pri same)
# Start mem->FA (neither)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0f:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0f:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0f:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:0f:63";
  binding_state "free";
}
# End mem->FA (neither)
# Start mem->FA (ext sec)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:10:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:10:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:10:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:10:63";
  binding_state "free";
}
# End mem->FA (ext sec)
# Start mem->FA (ext pri)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:11:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:11:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:11:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:11:63";
  binding_state "free";
}
# End mem->FA (ext pri)
# Start mem del->no recycle
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:12:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:12:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:12:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:12:63";
  binding_state "free";
}
# End mem del->no recycle
# Start range extent + mem del->LRB
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:6f:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:6f:11";
  binding_state "free";
}
lease {
  ip_address "192.168.111.21";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:6f:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:6f:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem del->LRB
# Start range extent + mem->mem
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:70:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:70:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:70:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:70:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem->mem
# Start range extent + mem->FA (sec same)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:71:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:71:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:71:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:71:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem->FA (sec same)
# Start range extent + mem->FA (pri same)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:72:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:72:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:72:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:72:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem->FA (pri same)
# Start range extent + mem->FA (neither)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:73:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:73:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:73:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:73:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem->FA (neither)
# Start range extent + mem->FA (ext sec)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:74:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:74:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:74:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:74:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem->FA (ext sec)
# Start range extent + mem->FA (ext pri)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:75:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:75:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:75:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:75:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem->FA (ext pri)
# Start range extent + mem del->no recycle
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:76:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:76:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:76:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:76:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem del->no recycle
# Start FA del->LRB
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:15:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:15:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:15:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:15:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:15:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:15:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:15:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:15:63";
  binding_state "backup";
}
# End FA del->LRB
# Start FA->mem (partly same)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:16:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:16:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:16:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:16:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:16:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:16:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:16:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:16:63";
  binding_state "backup";
}
# End FA->mem (partly same)
# Start FA->mem (neither)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:17:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:17:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:17:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:17:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:17:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:17:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:17:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:17:63";
  binding_state "backup";
}
# End FA->mem (neither)
# Start FA->FA (pri same)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:18:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:18:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:18:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:18:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:18:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:18:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:18:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:18:63";
  binding_state "backup";
}
# End FA->FA (pri same)
# Start FA->FA (pri same+ext)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:19:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:19:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:19:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:19:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:19:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:19:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:19:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:19:63";
  binding_state "backup";
}
# End FA->FA (pri same+ext)
# Start FA->FA (pri->sec)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1a:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1a:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1a:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1a:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1a:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1a:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1a:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1a:63";
  binding_state "backup";
}
# End FA->FA (pri->sec)
# Start FA->FA (pri->sec+ext)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1b:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1b:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1b:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1b:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1b:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1b:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1b:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1b:63";
  binding_state "backup";
}
# End FA->FA (pri->sec+ext)
# Start FA->FA (ext pri, neither)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1c:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1c:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1c:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1c:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1c:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1c:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1c:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1c:63";
  binding_state "backup";
}
# End FA->FA (ext pri, neither)
# Start FA del->no recycle
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1d:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1d:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1d:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1d:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1d:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1d:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1d:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1d:63";
  binding_state "backup";
}
# End FA del->no recycle
# Start range extent + FA del->LRB
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:79:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:79:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:79:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:79:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:79:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:79:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:79:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:79:19";
  binding_state "backup";
}
# End range extent + FA del->LRB
# Start range extent + FA->mem (partly same)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7a:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7a:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7a:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7a:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7a:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7a:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7a:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7a:19";
  binding_state "backup";
}
# End range extent + FA->mem (partly same)
# Start range extent + FA->mem (neither)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7b:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7b:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7b:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7b:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7b:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7b:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7b:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7b:19";
  binding_state "backup";
}
# End range extent + FA->mem (neither)
# Start range extent + FA->FA (pri same)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7c:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7c:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7c:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7c:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7c:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7c:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7c:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7c:19";
  binding_state "backup";
}
# End range extent + FA->FA (pri same)
# Start range extent + FA->FA (pri same+ext)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/19************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7d:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/19************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7d:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/19************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7d:15";
  binding_state "backup";
}
lease {
  ip_address "19************";
  node_id "0";
  dhcp_range "*************/19************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7d:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/19************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7d:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/19************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7d:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/19************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7d:15";
  binding_state "backup";
}
lease {
  ip_address "19************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/19************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7d:19";
  binding_state "backup";
}
# End range extent + FA->FA (pri same+ext)
# Start range extent + FA->FA (pri->sec)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7e:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7e:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7e:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7e:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7e:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7e:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7e:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7e:19";
  binding_state "backup";
}
# End range extent + FA->FA (pri->sec)
# Start range extent + FA->FA (pri->sec+ext)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7f:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7f:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7f:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7f:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7f:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7f:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7f:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:7f:19";
  binding_state "backup";
}
# End range extent + FA->FA (pri->sec+ext)
# Start range extent + FA->FA (ext pri, neither)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:80:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:80:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:80:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:80:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:80:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:80:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:80:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:80:19";
  binding_state "backup";
}
# End range extent + FA->FA (ext pri, neither)
# Start range extent + FA del->no recycle
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:81:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:81:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:81:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:81:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:81:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:81:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:81:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:81:19";
  binding_state "backup";
}
# End range extent + FA del->no recycle
# Start mem del->LRB->mem
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:29:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:29:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:29:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:29:63";
  binding_state "free";
}
# End mem del->LRB->mem
# Start mem del->LRB->FA pri (in-grid)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2a:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2a:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2a:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2a:63";
  binding_state "free";
}
# End mem del->LRB->FA pri (in-grid)
# Start mem del->LRB->FA pri (ext sec)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2b:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2b:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2b:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2b:63";
  binding_state "free";
}
# End mem del->LRB->FA pri (ext sec)
# Start mem del->LRB->FA sec (ext pri)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2c:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2c:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2c:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2c:63";
  binding_state "free";
}
# End mem del->LRB->FA sec (ext pri)
# Start mem del->LRB->unassigned
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2d:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2d:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2d:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:2d:63";
  binding_state "free";
}
# End mem del->LRB->unassigned
# Start range extent + mem del->LRB->mem
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8d:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8d:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8d:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8d:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem del->LRB->mem
# Start range extent + mem del->LRB->FA pri (in-grid)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8e:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8e:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8e:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8e:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem del->LRB->FA pri (in-grid)
# Start range extent + mem del->LRB->FA pri (ext sec)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8f:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8f:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8f:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:8f:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem del->LRB->FA pri (ext sec)
# Start range extent + mem del->LRB->FA sec (ext pri)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:90:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:90:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:90:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:90:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem del->LRB->FA sec (ext pri)
# Start range extent + mem del->LRB->unassigned
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:91:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:91:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:91:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:91:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + mem del->LRB->unassigned
# Start FA del->LRB->mem
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:33:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:33:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:33:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:33:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:33:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:33:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:33:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:33:63";
  binding_state "backup";
}
# End FA del->LRB->mem
# Start FA del->LRB->FA (in-grid)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:36:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:36:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:36:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:36:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:36:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:36:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:36:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:36:63";
  binding_state "backup";
}
# End FA del->LRB->FA (in-grid)
# Start FA del->LRB->FA (ext sec)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:39:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:39:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:39:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:39:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:39:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:39:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:39:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:39:63";
  binding_state "backup";
}
# End FA del->LRB->FA (ext sec)
# Start FA del->LRB->FA (ext pri)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3c:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3c:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3c:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3c:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3c:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3c:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3c:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3c:63";
  binding_state "backup";
}
# End FA del->LRB->FA (ext pri)
# Start FA del->LRB->unassigned
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3f:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3f:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3f:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3f:63";
  binding_state "backup";
}
lease {
  ip_address "************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3f:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3f:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3f:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3f:63";
  binding_state "backup";
}
# End FA del->LRB->unassigned
# Start range extent + FA del->LRB->mem
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:97:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:97:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:97:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:97:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:97:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:97:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:97:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:97:19";
  binding_state "backup";
}
# End range extent + FA del->LRB->mem
# Start range extent + FA del->LRB->FA (in-grid)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9a:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9a:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9a:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9a:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9a:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9a:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9a:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9a:19";
  binding_state "backup";
}
# End range extent + FA del->LRB->FA (in-grid)
# Start range extent + FA del->LRB->FA (ext sec)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9d:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9d:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9d:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9d:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9d:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9d:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9d:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9d:19";
  binding_state "backup";
}
# End range extent + FA del->LRB->FA (ext sec)
# Start range extent + FA del->LRB->FA (ext pri)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a0:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a0:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a0:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a0:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a0:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a0:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a0:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a0:19";
  binding_state "backup";
}
# End range extent + FA del->LRB->FA (ext pri)
# Start range extent + FA del->LRB->unassigned
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a3:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a3:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a3:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a3:19";
  binding_state "backup";
}
lease {
  ip_address "*************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a3:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a3:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a3:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100001_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a3:19";
  binding_state "backup";
}
# End range extent + FA del->LRB->unassigned
# Start FA (ext sec)->LRB->mem
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:34:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:34:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:34:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:34:63";
  binding_state "backup";
}
# End FA (ext sec)->LRB->mem
# Start FA (ext sec)->LRB->FA (in-grid)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:37:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:37:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:37:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:37:63";
  binding_state "backup";
}
# End FA (ext sec)->LRB->FA (in-grid)
# Start FA (ext sec)->LRB->FA (ext sec)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3a:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3a:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3a:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3a:63";
  binding_state "backup";
}
# End FA (ext sec)->LRB->FA (ext sec)
# Start FA (ext sec)->LRB->FA (ext pri)
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3d:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3d:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3d:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3d:63";
  binding_state "backup";
}
# End FA (ext sec)->LRB->FA (ext pri)
# Start FA (ext sec)->LRB->unassigned
lease {
  ip_address "************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:40:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:40:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:40:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:40:63";
  binding_state "backup";
}
# End FA (ext sec)->LRB->unassigned
# Start range extent + FA (ext sec)->LRB->mem
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:98:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:98:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:98:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:98:19";
  binding_state "backup";
}
# End range extent + FA (ext sec)->LRB->mem
# Start range extent + FA (ext sec)->LRB->FA (in-grid)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9b:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9b:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9b:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9b:19";
  binding_state "backup";
}
# End range extent + FA (ext sec)->LRB->FA (in-grid)
# Start range extent + FA (ext sec)->LRB->FA (ext sec)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9e:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9e:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9e:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9e:19";
  binding_state "backup";
}
# End range extent + FA (ext sec)->LRB->FA (ext sec)
# Start range extent + FA (ext sec)->LRB->FA (ext pri)
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a1:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a1:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a1:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a1:19";
  binding_state "backup";
}
# End range extent + FA (ext sec)->LRB->FA (ext pri)
# Start range extent + FA (ext sec)->LRB->unassigned
lease {
  ip_address "*************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a4:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a4:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a4:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "0";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a4:19";
  binding_state "backup";
}
# End range extent + FA (ext sec)->LRB->unassigned
# Start unassigned del->LRB
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1f:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1f:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1f:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:1f:63";
  binding_state "free";
}
# End unassigned del->LRB
# Start unassigned->mem
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:20:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:20:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:20:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:20:63";
  binding_state "free";
}
# End unassigned->mem
# Start unassigned->FA (in-grid)
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:21:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:21:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:21:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:21:63";
  binding_state "free";
}
# End unassigned->FA (in-grid)
# Start unassigned->FA (ext sec)
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:22:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:22:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:22:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:22:63";
  binding_state "free";
}
# End unassigned->FA (ext sec)
# Start unassigned->FA (ext pri)
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:23:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:23:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:23:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:23:63";
  binding_state "free";
}
# End unassigned->FA (ext pri)
# Start unassigned del->no recycle
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:24:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:24:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:24:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:24:63";
  binding_state "free";
}
# End unassigned del->no recycle
# Start range extent + unassigned del->LRB
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:83:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:83:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:83:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:83:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned del->LRB
# Start range extent + unassigned->mem
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:84:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:84:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:84:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:84:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned->mem
# Start range extent + unassigned->FA (in-grid)
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:85:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:85:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:85:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:85:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned->FA (in-grid)
# Start range extent + unassigned->FA (ext sec)
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:86:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:86:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:86:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:86:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned->FA (ext sec)
# Start range extent + unassigned->FA (ext pri)
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:87:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:87:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:87:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:87:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned->FA (ext pri)
# Start range extent + unassigned del->no recycle
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:88:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:88:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:88:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:88:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned del->no recycle
# Start unassigned del->LRB->mem
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:47:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:47:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:47:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:47:63";
  binding_state "free";
}
# End unassigned del->LRB->mem
# Start unassigned del->LRB->FA (in-grid)
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:48:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:48:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:48:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:48:63";
  binding_state "free";
}
# End unassigned del->LRB->FA (in-grid)
# Start unassigned del->LRB->FA (ext sec)
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:49:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:49:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:49:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:49:63";
  binding_state "free";
}
# End unassigned del->LRB->FA (ext sec)
# Start unassigned del->LRB->FA (ext pri)
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:4a:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:4a:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:4a:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:4a:63";
  binding_state "free";
}
# End unassigned del->LRB->FA (ext pri)
# Start unassigned del->LRB->unassigned
lease {
  ip_address "************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:4b:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:4b:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:4b:2a";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:4b:63";
  binding_state "free";
}
# End unassigned del->LRB->unassigned
# Start range extent + unassigned del->LRB->mem
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ab:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ab:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ab:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ab:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned del->LRB->mem
# Start range extent + unassigned del->LRB->FA (in-grid)
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ac:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ac:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ac:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ac:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned del->LRB->FA (in-grid)
# Start range extent + unassigned del->LRB->FA (ext sec)
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ad:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ad:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ad:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ad:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned del->LRB->FA (ext sec)
# Start range extent + unassigned del->LRB->FA (ext pri)
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ae:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ae:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ae:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:ae:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned del->LRB->FA (ext pri)
# Start range extent + unassigned del->LRB->unassigned
lease {
  ip_address "*************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:af:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:af:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:af:15";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "**************";
  node_id "";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:01";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:af:19";
  binding_state "active";
  next_binding_state "free";
}
# End range extent + unassigned del->LRB->unassigned
# Start FA (ext pri) del->LRB->mem
lease {
  ip_address "************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:35:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:35:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:35:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:35:63";
  binding_state "backup";
}
# End FA (ext pri) del->LRB->mem
# Start FA (ext pri) del->LRB->FA (in-grid)
lease {
  ip_address "************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:38:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:38:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:38:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:38:63";
  binding_state "backup";
}
# End FA (ext pri) del->LRB->FA (in-grid)
# Start FA (ext pri) del->LRB->FA (ext sec)
lease {
  ip_address "************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3b:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3b:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3b:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3b:63";
  binding_state "backup";
}
# End FA (ext pri) del->LRB->FA (ext sec)
# Start FA (ext pri) del->LRB->FA (ext pri)
lease {
  ip_address "************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3e:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "************7";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3e:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3e:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:3e:63";
  binding_state "backup";
}
# End FA (ext pri) del->LRB->FA (ext pri)
# Start FA (ext pri) del->LRB->unassigned
lease {
  ip_address "************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:41:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:41:11";
  binding_state "free";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:41:2a";
  binding_state "expired";
}
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:41:63";
  binding_state "backup";
}
# End FA (ext pri) del->LRB->unassigned
# Start range extent + FA (ext pri) del->LRB->mem
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:99:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:99:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:99:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:99:19";
  binding_state "backup";
}
# End range extent + FA (ext pri) del->LRB->mem
# Start range extent + FA (ext pri) del->LRB->FA (in-grid)
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9c:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9c:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9c:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9c:19";
  binding_state "backup";
}
# End range extent + FA (ext pri) del->LRB->FA (in-grid)
# Start range extent + FA (ext pri) del->LRB->FA (ext sec)
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9f:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9f:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9f:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:9f:19";
  binding_state "backup";
}
# End range extent + FA (ext pri) del->LRB->FA (ext sec)
# Start range extent + FA (ext pri) del->LRB->FA (ext pri)
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a2:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a2:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a2:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a2:19";
  binding_state "backup";
}
# End range extent + FA (ext pri) del->LRB->FA (ext pri)
# Start range extent + FA (ext pri) del->LRB->unassigned
lease {
  ip_address "*************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a5:01";
  binding_state "active";
  next_binding_state "free";
}
lease {
  ip_address "*************7";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a5:11";
  binding_state "free";
}
lease {
  ip_address "**************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a5:15";
  binding_state "backup";
}
lease {
  ip_address "**************";
  node_id "$NODE100003_OID";
  dhcp_range "*************/**************///0/";
  starts "1 2006/04/03 11:56:02";
  ends "4 2036/12/04 23:59:59";
  tstp "4 2036/12/04 23:59:59";
  hardware "ethernet aa:bb:cc:dd:a5:19";
  binding_state "backup";
}
# End range extent + FA (ext pri) del->LRB->unassigned
