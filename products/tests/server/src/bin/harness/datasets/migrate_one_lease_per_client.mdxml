<MDXML>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.cluster_dhcp_properties">
        <MEMBER-NAME-CHANGE pre-xform-value="enable_one_lease_per_client" post-xform-value="lease_per_client_settings"/>
        <MEMBER-VALUE-CHANGE member-name="enable_one_lease_per_client" pre-xform-value="true" post-xform-value="ONE_LEASE_PER_CLIENT"/>
        <MEMBER-VALUE-CHANGE member-name="enable_one_lease_per_client" pre-xform-value="false" post-xform-value="RELEASE_MATCHING_ID"/>
    </STRUCTURE-TRANSFORM>

    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.member_dhcp_properties">
        <MEMBER-NAME-CHANGE pre-xform-value="enable_one_lease_per_client" post-xform-value="lease_per_client_settings"/>
        <MEMBER-NAME-CHANGE pre-xform-value="override_enable_one_lease_per_client" post-xform-value="use_lease_per_client_settings"/>
        <MEMBER-VALUE-CHANGE member-name="enable_one_lease_per_client" pre-xform-value="true" post-xform-value="ONE_LEASE_PER_CLIENT"/>
        <MEMBER-VALUE-CHANGE member-name="enable_one_lease_per_client" pre-xform-value="false" post-xform-value="RELEASE_MATCHING_ID"/>
    </STRUCTURE-TRANSFORM>
</MDXML>
