<DATABASE NAME="onedb" VERSION="MDXMLTEST">

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.vconnector_cluster"/>
<PROPERTY NAME="feature_init" VALUE="2"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
<PROPERTY NAME="name" VALUE="default"/>
<PROPERTY NAME="id" VALUE="0"/>
<PROPERTY NAME="netmri_id" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
<PROPERTY NAME="name" VALUE="netview1"/>
<PROPERTY NAME="id" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
<PROPERTY NAME="name" VALUE="netview2"/>
<PROPERTY NAME="id" VALUE="2"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container"/>
<PROPERTY NAME="cidr" VALUE="64"/>
<PROPERTY NAME="parent" VALUE=".com.infoblox.dns.network_view$0"/>
<PROPERTY NAME="is_ipv4" VALUE="false"/>
<PROPERTY NAME="address" VALUE="2016:2016::"/>
<PROPERTY NAME="network_view" VALUE="0"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container"/>
<PROPERTY NAME="cidr" VALUE="8"/>
<PROPERTY NAME="parent" VALUE=".com.infoblox.dns.network_view$0"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="address" VALUE="10.0.0.0"/>
<PROPERTY NAME="network_view" VALUE="0"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container"/>
<PROPERTY NAME="cidr" VALUE="8"/>
<PROPERTY NAME="parent" VALUE=".com.infoblox.dns.network_view$1"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="address" VALUE="10.0.0.0"/>
<PROPERTY NAME="network_view" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container"/>
<PROPERTY NAME="cidr" VALUE="16"/>
<PROPERTY NAME="parent" VALUE=".com.infoblox.dns.network_container$10.0.0.0/8/1"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="address" VALUE="*********"/>
<PROPERTY NAME="network_view" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container"/>
<PROPERTY NAME="cidr" VALUE="8"/>
<PROPERTY NAME="parent" VALUE=".com.infoblox.dns.network_view$2"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="address" VALUE="20.0.0.0"/>
<PROPERTY NAME="network_view" VALUE="2"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container"/>
<PROPERTY NAME="cidr" VALUE="16"/>
<PROPERTY NAME="parent" VALUE=".com.infoblox.dns.network_container$20.0.0.0/8/2"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="address" VALUE="*********"/>
<PROPERTY NAME="network_view" VALUE="2"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.vconnector_directory"/>
<PROPERTY NAME="type" VALUE="NETWORK"/>
<PROPERTY NAME="vco_member" VALUE="3"/>
<PROPERTY NAME="vco_delegated_entity" VALUE=".com.infoblox.dns.network_container$2016:2016::/64/0"/>
<PROPERTY NAME="is_ipv4" VALUE="false"/>
<PROPERTY NAME="network_view" VALUE="0"/>
<PROPERTY NAME="address" VALUE="2016:2016::"/>
<PROPERTY NAME="end_address" VALUE="2016:2016:0000:0000:ffff:ffff:ffff:ffff"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.vconnector_directory"/>
<PROPERTY NAME="type" VALUE="NETWORK"/>
<PROPERTY NAME="vco_member" VALUE="1"/>
<PROPERTY NAME="vco_delegated_entity" VALUE=".com.infoblox.dns.network_container$10.0.0.0/8/1"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="network_view" VALUE="1"/>
<PROPERTY NAME="address" VALUE="10.0.0.0"/>
<PROPERTY NAME="end_address" VALUE="**************"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.vconnector_directory"/>
<PROPERTY NAME="type" VALUE="NETWORK_VIEW"/>
<PROPERTY NAME="vco_member" VALUE="2"/>
<PROPERTY NAME="vco_delegated_entity" VALUE=".com.infoblox.dns.network_view$2"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="network_view" VALUE="2"/>
<PROPERTY NAME="address" VALUE=""/>
<PROPERTY NAME="end_address" VALUE=""/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view_delegation_root"/>
<PROPERTY NAME="network_view" VALUE="0"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view_delegation_root"/>
<PROPERTY NAME="network_view" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view_delegation_root"/>
<PROPERTY NAME="network_view" VALUE="2"/>
<PROPERTY NAME="vco_member" VALUE="2"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container_delegation_root"/>
<PROPERTY NAME="network_container" VALUE="2016:2016::/64/0"/>
<PROPERTY NAME="network_view" VALUE="0"/>
<PROPERTY NAME="vco_member" VALUE="3"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container_delegation_root"/>
<PROPERTY NAME="network_container" VALUE="10.0.0.0/8/0"/>
<PROPERTY NAME="network_view" VALUE="0"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container_delegation_root"/>
<PROPERTY NAME="network_container" VALUE="*********/16/1"/>
<PROPERTY NAME="network_view" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container_delegation_root"/>
<PROPERTY NAME="network_container" VALUE="10.0.0.0/8/1"/>
<PROPERTY NAME="network_view" VALUE="1"/>
<PROPERTY NAME="vco_member" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container_delegation_root"/>
<PROPERTY NAME="network_container" VALUE="20.0.0.0/8/2"/>
<PROPERTY NAME="network_view" VALUE="2"/>
<PROPERTY NAME="vco_member" VALUE="2"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_container_delegation_root"/>
<PROPERTY NAME="network_container" VALUE="*********/16/2"/>
<PROPERTY NAME="network_view" VALUE="2"/>
<PROPERTY NAME="vco_member" VALUE="2"/>
</OBJECT>

</DATABASE>
