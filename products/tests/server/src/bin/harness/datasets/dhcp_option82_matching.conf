# dhcpd conf file for dhcp_option82_matching.sh
local-address 127.0.0.1;
server-identifier 127.0.0.1;
ddns-update-style interim;
not authoritative;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
log-facility daemon;
ping-check false;

ddns-updates off;
ignore client-updates;

# set lt (a printable version of the lease time) to either the actual lease time, or undefined 
set lt = pick(binary-to-ascii(10, 32, "", option dhcp-lease-time), "undefined");

if (exists agent.circuit-id and exists agent.remote-id) {
        log(info,
                concat("Option 82: ",
                        "received a REQUEST DHCP packet from relay-agent ",
                        binary-to-ascii(10, 8, ".", packet(24, 4)),
                        " with a circuit-id of \"",
                        binary-to-ascii(16, 8, ":", option agent.circuit-id),
                        "\" and remote-id of \"",
                        binary-to-ascii(16, 8, ":", option agent.remote-id),
                        "\" for ",
                        binary-to-ascii(10, 8, ".", leased-address),
                        " \(", binary-to-ascii(16, 8, ":", packet(28,6)), "\)",
                        " lease time is ", lt, " seconds."
                        )
                );
}

else if exists agent.circuit-id {
        log(info,
                concat("Option 82: ",
                        "received a REQUEST DHCP packet from relay-agent ",
                        binary-to-ascii(10, 8, ".", packet(24, 4)),
                        " with a circuit-id of \"",
                        binary-to-ascii(16, 8, ":", option agent.circuit-id),
                        "\" for ",
                        binary-to-ascii(10, 8, ".", leased-address),
                        " \(", binary-to-ascii(16, 8, ":", packet(28,6)), "\)",
                        " lease time is ", lt, " seconds."
                        )
                );
}

else if exists agent.remote-id {
        log(info,
                concat("Option 82: ",
                        "received a REQUEST DHCP packet from relay-agent ",
                        binary-to-ascii(10, 8, ".", packet(24, 4)),
                        " with a remote-id of \"",
                        binary-to-ascii(16, 8, ":", option agent.remote-id),
                        "\" for ",
                        binary-to-ascii(10, 8, ".", leased-address),
                        " \(", binary-to-ascii(16, 8, ":", packet(28,6)), "\)",
                        " lease time is ", lt, " seconds."
                        )
                );
}


subnet ********* netmask ********* {
        host fixed-host-cid-1 {
                dynamic;
                fixed-address *********;
                host-identifier option agent.circuit-id "string cid";
        }
        host fixed-host-cid-2 {
                dynamic;
                fixed-address *********;
                host-identifier option agent.circuit-id aa:bb:cc:dd:ee:ff;
        }
        host fixed-host-rid-3 {
                dynamic;
                fixed-address *********;
                host-identifier option agent.remote-id "string rid";
        }
        host fixed-host-rid-4 {
                dynamic;
                fixed-address *********;
                host-identifier option agent.remote-id 11:22:33:44:55:66;
        }
}
