// Inspired by examples in chapter 3 of the BIND ARM

acl "loopbacknet" { *********/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";
	recursion no;			// Do not provide recursive service
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

view "VIEW_NAME" {

// Root server hints
zone "." { type hint; file "root.hint"; };

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	file "localhost.rev";
	notify no;
};

zone "1.168.192.in-addr.arpa" {
	type master;
	notify no;
	file "1.168.192.in-addr.arpa";
	allow-update { "loopbacknet"; };
};

zone "2.168.192.in-addr.arpa" {
        type master;
        notify no;
        file "2.168.192.in-addr.arpa";
};

zone "3.in-addr.arpa" {
	type master;
	notify no;
	file "3.in-addr.arpa";
};

zone "10.in-addr.arpa" {
	type master;
	notify no;
	file "10.in-addr.arpa";
};

zone "d.c.b.a.ip6.arpa" {
	type master;
	notify no;
	file "d.c.b.a.ip6.arpa";
	allow-update { "loopbacknet"; };
};

};
