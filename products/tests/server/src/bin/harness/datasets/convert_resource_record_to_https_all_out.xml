<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_https"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1742012804"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.test"/>
  <PROPERTY NAME="name" VALUE="unk"/>
  <PROPERTY NAME="display_name" VALUE="unk"/>
  <PROPERTY NAME="priority" VALUE="16"/>
  <PROPERTY NAME="target" VALUE="foo.example.org"/>
  <PROPERTY NAME="display_target" VALUE="foo.example.org"/>
  <PROPERTY NAME="is_rpz_rule" VALUE="false"/>
  <PROPERTY NAME="ipv4_hint" VALUE="*********"/>
  <PROPERTY NAME="ipv6_hint" VALUE="2001:db8::2"/>
  <PROPERTY NAME="mandatory" VALUE="alpn,ipv4hint"/>
  <PROPERTY NAME="alpn" VALUE="h2,h3-19"/>
  <PROPERTY NAME="port" VALUE="1234"/>
  <PROPERTY NAME="no_default_alpn" VALUE="true"/>
  <PROPERTY NAME="ech" VALUE="AEn+DQBFKwAgACABWIHUGj4u+PIggYXcR5JF0gYk3dCRioBW8uJq9H4mKAAIAAEAAQABAANAEnB1YmxpYy50bHMtZWNoLmRldgAA"/>
  <PROPERTY NAME="rdata_hash" VALUE="3c164943e35dc550cd6cdbae0d4a050c16289fedef91c4c03ed92b9564fad4ec638a4636648ceea4fd5640cee840c9bb79a2b8fe824d12253b848af42d6d3465"/>
  <PROPERTY NAME="additional_params" VALUE="key65333=&quot;ex1&quot; key65444=&quot;ex2&quot;"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_https"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1742012805"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.temp"/>
  <PROPERTY NAME="name" VALUE="unk"/>
  <PROPERTY NAME="display_name" VALUE="unk"/>
  <PROPERTY NAME="priority" VALUE="100"/>
  <PROPERTY NAME="target" VALUE="example.org"/>
  <PROPERTY NAME="display_target" VALUE="example.org"/>
  <PROPERTY NAME="is_rpz_rule" VALUE="false"/>
  <PROPERTY NAME="ipv4_hint" VALUE="***********"/>
  <PROPERTY NAME="ipv6_hint" VALUE="2000:db6::8"/>
  <PROPERTY NAME="mandatory" VALUE="alpn,ipv6hint"/>
  <PROPERTY NAME="alpn" VALUE="h2,http/1.1"/>
  <PROPERTY NAME="port" VALUE="1234"/>
  <PROPERTY NAME="no_default_alpn" VALUE="true"/>
  <PROPERTY NAME="ech" VALUE="AEn+DQBFKwAgACABWIHUGj4u+PIggYXcR5JF0gYk3dCRioBW8uJq9H4mKAAIAAEAAQABAANAEnB1YmxpYy50bHMtZWNoLmRldgAA"/>
  <PROPERTY NAME="rdata_hash" VALUE="cb074eedea5bf66eecfbb29027da7e47029531e7224ef958273a523e0e9c208128628bb2bc660a92d5ba810fd0b270728d88ffc5378a40c52a77b2d713c4669b"/>
  <PROPERTY NAME="additional_params" VALUE="key33=&quot;ex1&quot; key40=&quot;ex2&quot;"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="object" VALUE=".com.infoblox.dns.bind_https$._default.com.temp.unk.cb074eedea5bf66eecfbb29027da7e47029531e7224ef958273a523e0e9c208128628bb2bc660a92d5ba810fd0b270728d88ffc5378a40c52a77b2d713c4669b"/>
  <PROPERTY NAME="tag" VALUE=".IB Discovery Owned"/>
  <PROPERTY NAME="value" VALUE="abcd"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.bind_https$._default.com.temp.unk.cb074eedea5bf66eecfbb29027da7e47029531e7224ef958273a523e0e9c208128628bb2bc660a92d5ba810fd0b270728d88ffc5378a40c52a77b2d713c4669b"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.test-admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
</DATABASE>
