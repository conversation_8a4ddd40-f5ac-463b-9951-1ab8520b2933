ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/tmp/dhcp_updater.key";

option space myspace1;

# Option filter "option_filter1"
class "option_filter1" {
	match if option  vendor-class-identifier="myrule1";
	vendor-option-space myspace1;
}

subnet *********** netmask ************* {
        pool {
                infoblox-range ************ ************;
                range ************ ************;
                allow members of "option_filter1";
        }
}

subnet *********** netmask ************* {
	not authoritative;
}

#End of dhcpd.conf file
