header-memberdns,add_client_ip_mac_options,allow_forwarder,allow_gss_tsig_zone_updates,allow_query,allow_recursive_query,allow_transfer,allow_update,allow_update_forwarding,atc_forwarding_enable,auto_sort_views,blackhole,blacklist_action,blacklist_log_query,blacklist_redirect_addresses,blacklist_redirect_ttl,blacklist_rulesets,carryover_client_ip_mac_options,concurrent_recursive_clients,disable_edns,dns64_groups,dns_over_lan2,dns_over_mgmt,dns_over_v6_lan,dns_over_v6_lan2,dns_over_v6_mgmt,enable_blackhole,enable_blacklist,enable_custom_root_server,enable_dns64,enable_notify_source_port,enable_nxdomain_redirect,enable_query_source_port,excluded_servers,filter_aaaa,filter_aaaa_list,forwarders_only,lame_ttl,limit_concurrent_recursive_clients,max_cache_ttl,max_ncache_ttl,member_view_nats,member_views,minimal_response,notify_delay,notify_source_port,nxdomain_log_query,nxdomain_redirect_addresses,nxdomain_redirect_ttl,nxdomain_rulesets,parent,query_rewrite_enabled,query_source_port,recursion_enabled,root_name_servers,serial_query_rate,transfers_in,transfers_out,transfers_per_ns,zone_transfer_format_option
memberdns,True,"***********,***********",False,********/24/ALLOW,MemberDNSAC,********/24/ALLOW,MemberDNSAC,,True,False,MemberDNSAC,REDIRECT,False,"***********,***********",60,"memberblack ruleset 1,memberblack ruleset 2",True,1000,,"MDdns64_group_1,MDdns64_group_2",False,False,False,False,False,True,True,False,True,,True,,,YES,********/24/ALLOW,False,,False,604800,10800,False/INTERFACE/None/default,"External,Internal,view.x1",True,5,,False,"***********,***********",60,"membernxruleset 1,membernxruleset 2",infoblox.localdomain,,,True,memberdnssampleroot.com/*******,,,,,
