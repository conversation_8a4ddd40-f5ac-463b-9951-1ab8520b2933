<DATABASE NAME="onedb" VERSION="MDXMLTEST">
  <OBJECT>
    <PROPERTY NAME="lease_per_client_settings" VALUE="RELEASE_MATCHING_ID"/>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dhcp_properties"/>
    <PROPERTY NAME="cluster" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="lease_per_client_settings" VALUE="ONE_LEASE_PER_CLIENT"/>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dhcp_properties"/>
    <PROPERTY NAME="cluster" VALUE="1"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="lease_per_client_settings" VALUE="RELEASE_MATCHING_ID"/>
    <PROPERTY NAME="use_lease_per_client_settings" VALUE="false"/>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
    <PROPERTY NAME="virtual_node" VALUE="1"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="lease_per_client_settings" VALUE="RELEASE_MATCHING_ID"/>
    <PROPERTY NAME="use_lease_per_client_settings" VALUE="true"/>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
    <PROPERTY NAME="virtual_node" VALUE="2"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="lease_per_client_settings" VALUE="ONE_LEASE_PER_CLIENT"/>
    <PROPERTY NAME="use_lease_per_client_settings" VALUE="false"/>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
    <PROPERTY NAME="virtual_node" VALUE="3"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="lease_per_client_settings" VALUE="ONE_LEASE_PER_CLIENT"/>
    <PROPERTY NAME="use_lease_per_client_settings" VALUE="true"/>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
    <PROPERTY NAME="virtual_node" VALUE="4"/>
  </OBJECT>
</DATABASE>
