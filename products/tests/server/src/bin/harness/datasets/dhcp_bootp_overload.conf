ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
local-address 127.0.0.1;
server-identifier 127.0.0.1;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;

ddns-updates off;
ignore client-updates;

subnet ********* netmask ************* {
	host 127.0.0.13 {
		dynamic;
		hardware ethernet 11:22:33:44:55:63;
		fixed-address 127.0.0.13;
		option host-name "host.com";
		option extensions-path "/ext/";
		option root-path "/root/";
		option nis-domain "nis.test";
	}
	host 127.0.0.14 {
		dynamic;
		hardware ethernet 11:22:33:44:55:64;
		fixed-address 127.0.0.14;
		option extensions-path "/short/ext/";
		option root-path "/short/root/";
		option nis-domain "nisdomain.test";
	}
	host 127.0.0.15 {
		dynamic;
		hardware ethernet 11:22:33:44:55:65;
		fixed-address 127.0.0.15;
		option host-name "hostnames.folsjaklgjsjdfgs.test.com";
		option extensions-path "/extpath/sample_path";
		option root-path "/rootpath/just_an_long_long_long_long_rootpath";
		option nis-domain "domainnisdomainnisdomainnisdomainnisdomainnisdomain.test";
	}
	host 127.0.0.16 {
		dynamic;
		hardware ethernet 11:22:33:44:55:66;
		fixed-address 127.0.0.16;
		option host-name "hostnames.folsjaklgjsjdfgs.test.com";
		option extensions-path "/extpath/sample_path";
		option root-path "/rootpath/just_an_long_long_long_long_rootpath";
		option merit-dump "merit-dump1234567890abcdefghijklmnopqrstuvwxyz01234567890abcdefghijklmnopqrstuvwxyz1234567890";
		option nis-domain "nisdomainnisdomainnisdomainnisdomainnisdomainnisdomainnisdomainnisdomainnisdomainnisdomainnisdomainnisdomainnisdomain.test";
		option vendor-encapsulated-options "vendor-encapsulated-options jglfjsdlgjdfljksdffghkljsfddgjsdlfgjlksdfjglsdfjkglsdfjgkljsdfgkljsdlfklsdfkj";
	}
}

#End of dhcpd.conf file
