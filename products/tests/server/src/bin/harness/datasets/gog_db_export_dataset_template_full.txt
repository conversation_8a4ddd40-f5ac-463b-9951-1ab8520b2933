
db_import;db;full;;{gen_id{}}:N

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGrid">
					<name>Infoblox</name>
					<uuid>{rec_id{.com.infoblox.one.cluster$0}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkViewParent">
					<uuid>{rec_id{.com.infoblox.dns.network_view_parent$/}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Network</item>
						<item>IPv6Network</item>
					</allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>Building</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.Building}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Network</item>
						<item>IPv6Network</item>
					</allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>Country</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.Country}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Network</item>
						<item>IPv6Network</item>
					</allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>Region</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.Region}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types></allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>Site</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.Site}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Network</item>
						<item>IPv6Network</item>
					</allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>State</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.State}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Network</item>
						<item>IPv6Network</item>
					</allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>VLAN</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.VLAN}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types></allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags>R</flags>
					<value_syntax></value_syntax>
					<name>IB Discovery Owned</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.IB Discovery Owned}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types></allowed_object_types>
					<attribute_type>INTEGER</attribute_type>
					<comment>Sub grid int ext_ettr_def</comment>
					<gui_default_value></gui_default_value>
					<flags>V</flags>
					<value_syntax></value_syntax>
					<name>int_ead</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.int_ead}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types></allowed_object_types>
					<attribute_type>DATE</attribute_type>
					<comment>Sub grid date ext_ettr_def</comment>
					<gui_default_value></gui_default_value>
					<flags>V</flags>
					<value_syntax></value_syntax>
					<name>date_ead</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.date_ead}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types></allowed_object_types>
					<attribute_type>ENUM</attribute_type>
					<comment>Sub grid enum ext_ettr_def</comment>
					<gui_default_value></gui_default_value>
					<enum_values>
						<item>
							<enum_value>RED</enum_value>
						</item>
						<item>
							<enum_value>YELLOW</enum_value>
						</item>
						<item>
							<enum_value>GREEN</enum_value>
						</item>
					</enum_values>
					<flags>V</flags>
					<value_syntax></value_syntax>
					<name>enum_ead</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.enum_ead}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridMember">
					<comment></comment>
					<ha_enabled>false</ha_enabled>
					<host_name>infoblox.localdomain</host_name>
					<is_master>true</is_master>
					<master_candidate_enabled>true</master_candidate_enabled>
					<member_type>INFOBLOX</member_type>
					<virtual_ip>***********</virtual_ip>
					<virtual_oid>0</virtual_oid>
					<uuid>{rec_id{.com.infoblox.one.virtual_node$0}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridMember">
					<comment>Sub grid member object 00</comment>
					<ha_enabled>false</ha_enabled>
					<host_name>foo-00.com</host_name>
					<is_master>false</is_master>
					<master_candidate_enabled>false</master_candidate_enabled>
					<member_type>INFOBLOX</member_type>
					<virtual_ip>10.0.0.0</virtual_ip>
					<virtual_oid>1</virtual_oid>
					<uuid>{rec_id{.com.infoblox.one.virtual_node$1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridMember">
					<comment>Sub grid member object 01</comment>
					<ha_enabled>false</ha_enabled>
					<host_name>foo-01.com</host_name>
					<is_master>false</is_master>
					<master_candidate_enabled>false</master_candidate_enabled>
					<member_type>INFOBLOX</member_type>
					<virtual_ip>********</virtual_ip>
					<virtual_oid>2</virtual_oid>
					<uuid>{rec_id{.com.infoblox.one.virtual_node$2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<comment></comment>
					<name>default</name>
					<id>0</id>
					<uuid>{rec_id{.com.infoblox.dns.network_view$0}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<comment>Sub grid network View 00</comment>
					<name>netview-00</name>
					<id>1</id>
					<uuid>{rec_id{.com.infoblox.dns.network_view$1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<comment>Sub grid network View 01</comment>
					<name>netview-01</name>
					<id>2</id>
					<uuid>{rec_id{.com.infoblox.dns.network_view$2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<comment>Sub grid network View ext_attrs</comment>
					<name>netview-ext-attrs</name>
					<id>3</id>
					<uuid>{rec_id{.com.infoblox.dns.network_view$3}}</uuid>
					<extensible_attributes>
						<item>
							<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.Site}}</uuid>
							<values>
								<item>
									<value_string>Vancouver</value_string>
								</item>
							</values>
						</item>
						<item>
							<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.date_ead}}</uuid>
							<values>
								<item>
									<value_date>1999-12-31T23:59:59Z</value_date>
								</item>
								<item>
									<value_date>1997-07-01T23:59:59Z</value_date>
								</item>
							</values>
						</item>
						<item>
							<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.enum_ead}}</uuid>
							<values>
								<item>
									<value_enum>GREEN</value_enum>
								</item>
								<item>
									<value_enum>RED</value_enum>
								</item>
								<item>
									<value_enum>YELLOW</value_enum>
								</item>
							</values>
						</item>
						<item>
							<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.int_ead}}</uuid>
							<values>
								<item>
									<value_integer>123</value_integer>
								</item>
								<item>
									<value_integer>456</value_integer>
								</item>
							</values>
						</item>
					</extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 00</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$********/24/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 01</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$********/24/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 02</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$********/24/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 00</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$********/24/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 01</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$********/24/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 02</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$********/24/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:2000::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 00</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$dead:beef:2000::/64/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:2001::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 01</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$dead:beef:2001::/64/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:2002::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 02</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$dead:beef:2002::/64/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:2000::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 00</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$dead:beef:2000::/64/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:2001::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 01</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$dead:beef:2001::/64/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:2002::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 02</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network_container$dead:beef:2002::/64/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 02</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$********/16/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 00</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$********/16/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 01</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$********/16/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 02</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$********/16/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 00</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$********/16/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 01</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$********/16/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:1001::</address>
					<cidr>120</cidr>
					<comment>Sub grid IPv6 network 01</comment>
					<is_container>false</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$dead:beef:1001::/120/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:1002::</address>
					<cidr>120</cidr>
					<comment>Sub grid IPv6 network 02</comment>
					<is_container>false</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$dead:beef:1002::/120/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:1000::</address>
					<cidr>120</cidr>
					<comment>Sub grid IPv6 network 00</comment>
					<is_container>false</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$dead:beef:1000::/120/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:1001::</address>
					<cidr>120</cidr>
					<comment>Sub grid IPv6 network 01</comment>
					<is_container>false</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$dead:beef:1001::/120/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:1002::</address>
					<cidr>120</cidr>
					<comment>Sub grid IPv6 network 02</comment>
					<is_container>false</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$2}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$dead:beef:1002::/120/2}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:1000::</address>
					<cidr>120</cidr>
					<comment>Sub grid IPv6 network 00</comment>
					<is_container>false</is_container>
					<is_ipv4>false</is_ipv4>
					<disabled>false</disabled>
					<sg_network_view>{rec_id{.com.infoblox.dns.network_view$1}}</sg_network_view>
					<uuid>{rec_id{.com.infoblox.dns.network$dead:beef:1000::/120/1}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:AdminGroup">
					<comment></comment>
					<disabled>false</disabled>
					<name>admin-group</name>
					<superuser>true</superuser>
					<uuid>{rec_id{.com.infoblox.one.admin_group$.admin-group}}</uuid>
					<extensible_attributes></extensible_attributes>
					<access_rights></access_rights>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:AdminGroup">
					<comment>Admins allowed to perform API request on Cloud API with superuser permissions</comment>
					<disabled>false</disabled>
					<name>cloud-api-superuser</name>
					<superuser>true</superuser>
					<uuid>{rec_id{.com.infoblox.one.admin_group$.cloud-api-superuser}}</uuid>
					<extensible_attributes></extensible_attributes>
					<access_rights></access_rights>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:AdminGroup">
					<comment>Sub grid admin group</comment>
					<disabled>false</disabled>
					<name>vassa</name>
					<superuser>false</superuser>
					<uuid>{rec_id{.com.infoblox.one.admin_group$.vassa}}</uuid>
					<extensible_attributes></extensible_attributes>
					<access_rights>
						<item>
							<data>{rec_id{.com.infoblox.dns.network$********/16/1}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>true</allow_read>
							<allow_modify>false</allow_modify>
							<allow_delete>false</allow_delete>
							<allow_create>false</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network$********/16/2}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>true</allow_read>
							<allow_modify>true</allow_modify>
							<allow_delete>true</allow_delete>
							<allow_create>true</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network_container$dead:beef:2002::/64/2}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>false</allow_read>
							<allow_modify>false</allow_modify>
							<allow_delete>false</allow_delete>
							<allow_create>false</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network_container$dead:beef:2001::/64/1}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>true</allow_read>
							<allow_modify>true</allow_modify>
							<allow_delete>true</allow_delete>
							<allow_create>true</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network$********/16/2}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>false</allow_read>
							<allow_modify>false</allow_modify>
							<allow_delete>false</allow_delete>
							<allow_create>false</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network_view$1}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>true</allow_read>
							<allow_modify>false</allow_modify>
							<allow_delete>false</allow_delete>
							<allow_create>false</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network_container$dead:beef:2002::/64/1}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>false</allow_read>
							<allow_modify>false</allow_modify>
							<allow_delete>false</allow_delete>
							<allow_create>false</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network$********/16/2}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>true</allow_read>
							<allow_modify>false</allow_modify>
							<allow_delete>false</allow_delete>
							<allow_create>false</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network_view$2}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>true</allow_read>
							<allow_modify>true</allow_modify>
							<allow_delete>true</allow_delete>
							<allow_create>true</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network$********/16/1}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>false</allow_read>
							<allow_modify>false</allow_modify>
							<allow_delete>false</allow_delete>
							<allow_create>false</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network_container$dead:beef:2000::/64/2}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>true</allow_read>
							<allow_modify>false</allow_modify>
							<allow_delete>false</allow_delete>
							<allow_create>false</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network$********/16/1}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>true</allow_read>
							<allow_modify>true</allow_modify>
							<allow_delete>true</allow_delete>
							<allow_create>true</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network_container$dead:beef:2000::/64/1}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>true</allow_read>
							<allow_modify>false</allow_modify>
							<allow_delete>false</allow_delete>
							<allow_create>false</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
						<item>
							<data>{rec_id{.com.infoblox.dns.network_container$dead:beef:2001::/64/2}}</data>
							<sub_type></sub_type>
							<comment></comment>
							<allow_read>true</allow_read>
							<allow_modify>true</allow_modify>
							<allow_delete>true</allow_delete>
							<allow_create>true</allow_create>
							<unmanaged_flag>false</unmanaged_flag>
						</item>
					</access_rights>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
        <SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
                <ib:ObjectReadResponse>
                        <total_size>1</total_size>
                        <objects>
                                <item xsi:type="ib:ExtensibleAttributeDefinition">
                                        <allowed_object_types>
                                                <item>Member</item>
                                        </allowed_object_types>
                                        <attribute_type>ENUM</attribute_type>
                                        <comment></comment>
                                        <gui_default_value>
                                                <value_enum>site1</value_enum>
                                        </gui_default_value>
                                        <enum_values>
                                                <item>
                                                        <enum_value>site1</enum_value>
                                                </item>
                                                <item>
                                                        <enum_value>site2</enum_value>
                                                </item>
                                                <item>
                                                        <enum_value>site3</enum_value>
                                                </item>
                                                <item>
                                                        <enum_value>site4</enum_value>
                                                </item>
                                                <item>
                                                        <enum_value>site5</enum_value>
                                                </item>
                                        </enum_values>
                                        <flags>R</flags>
                                        <value_syntax></value_syntax>
                                        <name>ReportingSite</name>
                                        <uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.ReportingSite}}</uuid>
                                </item>
                        </objects>
                </ib:ObjectReadResponse>
        </SOAP-ENV:Body>
</SOAP-ENV:Envelope>

