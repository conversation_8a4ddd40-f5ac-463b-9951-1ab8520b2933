local-address ***********;
server-identifier ***********;
ddns-update-style interim;
not authoritative;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
ping-number 1;
ping-timeout 1;
log-facility daemon;
option testing code 150 = text; 
option e2k-pprof code 224 = text; 
option e2k-sprof code 225 = string; 
option testing "\\abc\t\xaa\\ef\123\\45";
option e2k-pprof "\\\\ESEKINT007\\Profiles";
option e2k-sprof "\\\\ESKINT006\\Profiles\\\\\test1\\\\\\\\test2";
option bootfile-name "Bstrap\x5cx86pc\x5cBstrap.0";
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/storage/tmp/dhcp_updater.key";

# set lt (a printable version of the lease time) to either the actual lease time, or undefined 
set lt = pick(binary-to-ascii(10, 32, "", option dhcp-lease-time), "undefined");

if (exists agent.circuit-id and exists agent.remote-id) {
	log(info,
		concat("Option 82: ",
			"received a REQUEST DHCP packet from relay-agent ",
			binary-to-ascii(10, 8, ".", packet(24, 4)),
			" with a circuit-id of \"",
			binary-to-ascii(16, 8, ":", option agent.circuit-id),
			"\" and remote-id of \"",
			binary-to-ascii(16, 8, ":", option agent.remote-id),
			"\" for ",
			binary-to-ascii(10, 8, ".", leased-address),
			" \(", binary-to-ascii(16, 8, ":", packet(28,6)), "\)",
			" lease time is ", lt, " seconds."
			)
		);
}

else if exists agent.circuit-id {
	log(info,
		concat("Option 82: ",
			"received a REQUEST DHCP packet from relay-agent ",
			binary-to-ascii(10, 8, ".", packet(24, 4)),
			" with a circuit-id of \"",
			binary-to-ascii(16, 8, ":", option agent.circuit-id),
			"\" for ",
			binary-to-ascii(10, 8, ".", leased-address),
			" \(", binary-to-ascii(16, 8, ":", packet(28,6)), "\)",
			" lease time is ", lt, " seconds."
			)
		);
}

else if exists agent.remote-id {
	log(info,
		concat("Option 82: ",
			"received a REQUEST DHCP packet from relay-agent ",
			binary-to-ascii(10, 8, ".", packet(24, 4)),
			" with a remote-id of \"",
			binary-to-ascii(16, 8, ":", option agent.remote-id),
			"\" for ",
			binary-to-ascii(10, 8, ".", leased-address),
			" \(", binary-to-ascii(16, 8, ":", packet(28,6)), "\)",
			" lease time is ", lt, " seconds."
			)
		);
}

class "option_filter1" {
	match if (option  bootfile-name = "Bstrap\x5cx86pc\x5cBstrap.0");
	option bootfile-name "Bstrap\x5cx86pc\x5cBstrap.0";
	option e2k-sprof "\\\\ESKINT006\\Profiles\\\\\test1\\\\\\\\test2";
	option e2k-pprof "\\\\ESEKINT007\\Profiles";
}

class "option_filter2" {
	match if (option  e2k-sprof = "\\\\ESKINT006\\Profiles\\\\\test1\\\\\\\\test2");
	option e2k-pprof "\\\\ESEKINT007\\Profiles";
	option e2k-sprof "\\\\ESKINT006\\Profiles\\\\\test1\\\\\\\\test2";
}

subnet 10.0.0.0 netmask ********* {
	option bootfile-name "Bstrap\x5cx86pc\x5cBstrap.0";
	option testing "\\abc\t\xaa\\ef\123\\45";
	option e2k-pprof "\\\\ESEKINT007\\Profiles";
	option e2k-sprof "\\\\ESKINT006\\Profiles\\\\\test1\\\\\\\\test2";
}

subnet *********** netmask ************* {
	not authoritative;
}

#End of dhcpd.conf file
