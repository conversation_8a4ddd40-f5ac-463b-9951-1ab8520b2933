<DATABASE NAME="onedb" VERSION="MDXMLTEST">

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.vlan"/>
  <PROPERTY NAME="id" VALUE="25"/>
  <PROPERTY NAME="reserved" VALUE="false"/>
  <PROPERTY NAME="status" VALUE="UNASSIGNED"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.dns.vlan_range$vlan_view.1.100.vlan_range.1.50"/>
  <PROPERTY NAME="vlan_view" VALUE="vlan_view.1.100"/>
  <PROPERTY NAME="name" VALUE="vlan"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.vlan"/>
  <PROPERTY NAME="id" VALUE="30"/>
  <PROPERTY NAME="reserved" VALUE="false"/>
  <PROPERTY NAME="status" VALUE="UNASSIGNED"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.dns.vlan_range$vlan_view.1.100.vlan_range2.2.49"/>
  <PROPERTY NAME="vlan_view" VALUE="vlan_view.1.100"/>
  <PROPERTY NAME="name" VALUE="vlan0"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.vlan"/>
  <PROPERTY NAME="id" VALUE="25"/>
  <PROPERTY NAME="reserved" VALUE="false"/>
  <PROPERTY NAME="status" VALUE="UNASSIGNED"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.dns.vlan_range$vlan_view.1.100.vlan_range2.2.49"/>
  <PROPERTY NAME="vlan_view" VALUE="vlan_view.1.100"/>
  <PROPERTY NAME="name" VALUE="vlan"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>

</DATABASE>
