ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

failover peer "test-fa(1260842156)"
{
  secondary;
  address ***********;
  port 519;

  peer address ********;
  peer port 519;

  max-response-delay 60;
  max-unacked-updates 10;
 load balance max seconds 3;
 infoblox-in-grid;
}
ddns-updates off;
ignore client-updates;

include "/storage/tmp/dhcp_updater.key";

subnet ******** netmask ********* {
	pool {
		failover peer "test-fa(1260842156)";
		deny dynamic bootp clients;
		infoblox-range ********* ********0;
		range ********* *********;
		range ********* ********0;
	}
}

subnet *********** netmask ************* {
	not authoritative;
}

#End of dhcpd.conf file
