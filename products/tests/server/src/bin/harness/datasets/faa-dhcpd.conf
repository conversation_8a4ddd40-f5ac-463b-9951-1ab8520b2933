ignore bootp;
ddns-updates false;
ddns-update-style interim;
option domain-name-servers 127.0.0.1;
local-address 127.0.0.1;
server-identifier 127.0.0.1;
authoritative;
max-lease-time 30;
default-lease-time 30;
ping-check false;

# Allocate leases from the loopback network
subnet ********* netmask ********* {
	pool {
		# Quarantine range
		max-lease-time 1;
		default-lease-time 1;
		infoblox-range ********* ***********;
		range ********* ***********;
	}
	pool {
		# Guest range
		max-lease-time 1;
		default-lease-time 1;
		infoblox-range ********* ***********;
		range ********* ***********;
	}
	pool {
		# Authenticated range
		max-lease-time 1;
		default-lease-time 1;
		infoblox-range ********* ***********;
		range ********* ***********;
	}

	# Fixed-address hosts
	host fixed-host-1 {
		dynamic;
		hardware ethernet 00:00:00:00:00:01;
		# Some of the hosts have the fixed-address deliberately deactivated.
		# fixed-address *********;
	}
	host fixed-host-4 {
		dynamic;
		uid "\000\004";
		# hardware ethernet 00:00:00:00:00:04;
		fixed-address *********;
	}
	host fixed-host-7 {
		dynamic;
		hardware ethernet 00:00:00:00:00:07;
		# fixed-address *********;
	}
	host fixed-host-9 {
		dynamic;
		hardware ethernet 00:00:00:00:00:09;
		# Note, this address is in the guest range
		fixed-address *********;
	}
	host fixed-host-23 {
		dynamic;
		hardware ethernet 00:00:00:00:00:23;
		# fixed-address **********;
	}
	host fixed-host-42 {
		dynamic;
		hardware ethernet 00:00:00:00:00:42;
		fixed-address **********;
	}
}
