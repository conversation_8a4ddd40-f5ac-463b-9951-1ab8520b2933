<DATABASE NAME="onedb" VERSION="MDXMLTEST">
 <OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.saml-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/> 
 </OBJECT>
 <OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$SAML Admin"/>
  <PROPERTY NAME="sub_type" VALUE="one.saml_auth_service"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
 </OBJECT> 
</DATABASE>
