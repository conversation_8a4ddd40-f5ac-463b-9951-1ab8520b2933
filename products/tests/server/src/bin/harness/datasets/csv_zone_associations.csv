Header-AuthZone,fqdn*,,,grid_primaries,view,external_secondaries,allow_transfer,allow_query,zone_type,allow_active_dir,allow_update,zone_format,notify_delay,disabled,soa_negative_ttl,soa_mnames,soa_default_ttl,soa_retry,,create_underscore_zones,soa_serial_number,soa_email,comment,soa_expire,soa_refresh,is_multimaster
AuthZone,test_csv.com,,,infoblox.localdomain,default,test.infoblox.com/*******/TRUE,"*********/Deny,1234::/64/Allow","*********/Deny,1234::/64/Allow,TSIG-key1/hhWzttOTZa8spbIfh41gBsoZasxwJXPD7tLXCp47dcWv7oTWbWxzdkuTJZk5e+VT%2FYkya3C0KqfJZbeUn20gnA==/Allow",Authoritative,*******,"1234::/64/Allow",FORWARD,100,FALSE,400,infoblox.localdomain/mname1,900,800,FALSE,FALSE,1,<EMAIL>,comment for zone1,100,200,TRUE

Header-Network,address,netmask,domain_name_servers,dhcp_members,routers,comment,broadcast_address,disabled,next_server,auto_create_reversezone,,ignore_client_requested_options,enable_option81,pxe_lease_time,,ddns_ttl,enable_ddns,boot_server,deny_bootp,enable_pxe_lease_time,update_static_leases,range_low_water_mark,range_high_water_mark,range_low_water_mark_reset,range_high_water_mark_reset,threshold_email_addresses,enable_thresholds,lease_time,enable_threshold_snmp_warnings,enable_threshold_email_warnings,generate_hostname,is_authoritative,domain_name,always_update_dns,dhcp_members,routers,zone_associations,ddns_domainname,,,recycle_leases
Network,*******,*********,"*******,*******",infoblox.localdomain/TRUE/TRUE/TRUE,"*******,*******",comment for network1,*******,FALSE,*******,FALSE,TRUE,FALSE,TRUE,100,NONE,300,TRUE,*******,TRUE,TRUE,TRUE,60,70,63,68,"<EMAIL>,<EMAIL>",TRUE,100,TRUE,TRUE,FALSE,TRUE,host,TRUE,infoblox.localdomain,"",test_csv.com/TRUE,domainname,TRUE,shnetname1,TRUE
