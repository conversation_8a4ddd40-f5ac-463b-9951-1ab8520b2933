ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
ddns-local-address4 ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
option domain-name "member.com";
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates on;
# DNS update retry interval: 5
deny client-updates;
ddns-domainname =  config-option domain-name;
ddns-hostname = pick ( option fqdn.hostname, option host-name );
option host-name = config-option server.ddns-hostname;
update-static-leases true;

include "/infoblox/var/dhcpd_conf/dhcp_updater.key";

subnet ********* netmask ************* {
	ddns-domainname = config-option domain-name;
	ddns-hostname = pick ( option fqdn.hostname, option host-name );
	option host-name = config-option server.ddns-hostname;

}

subnet ********* netmask ************* {
ddns-domainname =  config-option domain-name;
ddns-hostname = pick ( option fqdn.hostname, option host-name );
option host-name = config-option server.ddns-hostname;
}

subnet ********* netmask *************** {
	option domain-name "localoverride.com";
	ddns-domainname = config-option domain-name;
	ddns-hostname = pick ( option fqdn.hostname, option host-name );
	option host-name = config-option server.ddns-hostname;
}

subnet ********* netmask ************* {
	ddns-domainname = config-option domain-name;
	ddns-hostname = pick ( option fqdn.hostname, option host-name );
	option host-name = config-option server.ddns-hostname;

	host ********* {
		dynamic;
		hardware ethernet 11:11:11:11:11:11;
		fixed-address *********;
		option domain-name "fixedaddress.com";
	}
	host ********* {
		dynamic;
		hardware ethernet 10:11:11:11:11:11;
		fixed-address *********;
	}
	pool {
		infoblox-range ********* *********;
		range ********* *********;
		option domain-name "range.com";
                ddns-domainname = config-option domain-name;
	}
	pool {
		infoblox-range ********* *********;
		range ********* *********;
                ddns-domainname = config-option domain-name;
	}
}

subnet *********** netmask ************* {
	not authoritative;
}

shared-network "shared" {
		option domain-name "shared.com";

subnet ********* netmask ************* {
		ddns-domainname = config-option domain-name;
		ddns-hostname = pick ( option fqdn.hostname, option host-name );
		option host-name = config-option server.ddns-hostname;
	}
}


shared-network "shared2" {
ddns-updates off;

subnet ********* netmask ************* {
	}
}

zone "localoverride.com." {
	primary *******;
}
zone "nonclusterzone.com." {
	primary *******;
}
zone "_underscore.com." {
        primary *******;
}
zone "int._under.com." {
        primary *******;
}
zone "9.9.9.in-addr.arpa." {
	primary *******;
}
zone "0.11.11.in-addr.arpa." {
        primary 127.0.0.1;
        key DHCP_UPDATER_default;
}
zone "0.10.10.in-addr.arpa." {
        primary 127.0.0.1;
        key DHCP_UPDATER_default;
}
zone "1.10.10.in-addr.arpa." {
        primary 127.0.0.1;
        key DHCP_UPDATER_default;
}
zone "2.10.10.in-addr.arpa." {
        primary 127.0.0.1;
        key DHCP_UPDATER_default;
}
zone "3.10.10.in-addr.arpa." {
        primary 127.0.0.1;
        key DHCP_UPDATER_default;
}
zone "0.12.12.in-addr.arpa." {
        primary 127.0.0.1;
        key DHCP_UPDATER_default;
}
zone "0.13.13.in-addr.arpa." {
        primary 127.0.0.1;
        key DHCP_UPDATER_default;
}
zone "0.14.14.in-addr.arpa." {
        primary 127.0.0.1;
        key DHCP_UPDATER_default;
}
zone "0.15.15.in-addr.arpa." {
        primary 127.0.0.1;
        key DHCP_UPDATER_default;
}

#End of dhcpd.conf file
