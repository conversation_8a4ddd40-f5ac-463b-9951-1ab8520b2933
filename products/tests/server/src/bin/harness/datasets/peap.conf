#
#  ./eapol_test -c peap.conf -a *********** -s testing123
#

# Catch all example that allows more or less all configuration modes
network={
        ssid="example"
        scan_ssid=1
        key_mgmt=WPA-EAP WPA-PSK IEEE8021X NONE
        pairwise=CCMP TKIP
        group=CCMP TKIP WEP104 WEP40
        psk="very secret passphrase"
        eap=PEAP
        identity="bôb"
        password="bôb"
        ca_cert="./cacert.pem"
        phase1="peaplabel=0"
}
