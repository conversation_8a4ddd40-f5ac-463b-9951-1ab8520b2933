#!/bin/bash       

############################################################################
#automatically generated iptables for syslog proxy client access control
#
#Do not edit!!!! 
############################################################################

. /infoblox/one/bin/one_common

PROXY_CHAIN=proxyacl  
TCP_PORT=666
UDP_PORT=555
TCP_RULE="INPUT --protocol tcp --dport ${TCP_PORT} -j ${PROXY_CHAIN}"
UDP_RULE="INPUT --protocol udp --dport ${UDP_PORT} -j ${PROXY_CHAIN}"

case "$1" in 

    start) 

     $IPTABLES -N $PROXY_CHAIN 
     if [ $? != "0" ]; then
        echo "Error adding new chain" | $IB_LOG 
        exit 1
     fi;

     $IPTABLES -A $TCP_RULE 
     if [ $? != "0" ]; then
        echo "Error adding tcp rule to input chain" | $IB_LOG 
        exit 1
     fi;

     $IPTABLES -A $UDP_RULE 
     if [ $? != "0" ]; then
        echo "Error adding udp rule to input chain" | $IB_LOG 
        exit 1
     fi;

     $IPTABLES -A $PROXY_CHAIN -j DROP
     if [ $? != "0" ]; then
        echo "Error adding access control rule to proxyacl chain" | $IB_LOG 
        exit 1
     fi;

     $IP6TABLES -N $PROXY_CHAIN 
     if [ $? != "0" ]; then
        echo "Error adding new ipv6 chain" | $IB_LOG 
        exit 1
     fi;

     $IP6TABLES -A $TCP_RULE 
     if [ $? != "0" ]; then
        echo "Error adding tcp rule to ipv6 input chain" | $IB_LOG 
        exit 1
     fi;

     $IP6TABLES -A $UDP_RULE 
     if [ $? != "0" ]; then
        echo "Error adding udp rule to ipv6 input chain" | $IB_LOG 
        exit 1
     fi;

     $IP6TABLES -A $PROXY_CHAIN -s 1234::0/64 -j ACCEPT
     if [ $? != "0" ]; then
        echo "Error adding access control rule to ipv6 proxyacl chain" | $IB_LOG 
        exit 1
     fi;

     $IP6TABLES -A $PROXY_CHAIN -s 1234::5678 -j ACCEPT
     if [ $? != "0" ]; then
        echo "Error adding access control rule to ipv6 proxyacl chain" | $IB_LOG 
        exit 1
     fi;

     $IP6TABLES -A $PROXY_CHAIN -j DROP
     if [ $? != "0" ]; then
        echo "Error adding access control rule to ipv6 proxyacl chain" | $IB_LOG 
        exit 1
     fi;


     ;;
    stop)

     $IPTABLES -F $PROXY_CHAIN 
     $IPTABLES -D $TCP_RULE 
     $IPTABLES -D $UDP_RULE 
     $IPTABLES -X $PROXY_CHAIN 
     $IP6TABLES -F $PROXY_CHAIN 
     $IP6TABLES -D $TCP_RULE 
     $IP6TABLES -D $UDP_RULE 
     $IP6TABLES -X $PROXY_CHAIN 

     ;;

esac

exit 0 
