header-aaaarecord,address*,_new_address,fqdn*,_new_fqdn,comment,create_ptr,creator,ddns_principal,ddns_protected,disabled,ttl,view
header-arecord,address*,_new_address,fqdn*,_new_fqdn,comment,create_ptr,creator,ddns_principal,ddns_protected,disabled,ttl,view
header-cnamerecord,canonical_name*,fqdn*,_new_fqdn,comment,creator,ddns_principal,ddns_protected,disabled,ttl,view
header-dnamerecord,fqdn*,_new_fqdn,target*,comment,creator,ddns_principal,ddns_protected,disabled,ttl,view
header-hostaddress,address*,_new_address,parent*,boot_file,boot_server,broadcast_address,configure_for_dhcp,configure_for_dns,deny_bootp,domain_name,domain_name_servers,ignore_dhcp_param_request_list,lease_time,mac_address,match_option,network_view,next_server,option_logic_filters,pxe_lease_time,pxe_lease_time_enabled,routers,use_for_ea_inheritance,view
header-hostrecord,fqdn*,_new_fqdn,addresses,aliases,cli_credentials,comment,configure_for_dns,_new_configure_for_dns,created_timestamp,creator_member,ddns_protected,disabled,enable_discovery,enable_immediate_discovery,ipv6_addresses,network_view,override_cli_credentials,override_credential,snmpv1v2_credential,snmpv3_credential,ttl,use_snmpv3_credential,view
header-ipv6hostaddress,address*,_new_address,parent*,address_type,configure_for_dhcp,configure_for_dns,domain_name,domain_name_servers,duid,ipv6_prefix,_new_ipv6_prefix,ipv6_prefix_bits,match_option,network_view,preferred_lifetime,use_for_ea_inheritance,valid_lifetime,view
header-mxrecord,fqdn*,_new_fqdn,mx*,_new_mx,priority*,_new_priority,comment,creator,ddns_principal,ddns_protected,disabled,ttl,view
header-naptrrecord,fqdn*,_new_fqdn,order*,_new_order,preference*,_new_preference,replacement*,_new_replacement,comment,creator,ddns_principal,ddns_protected,disabled,flags,_new_flags,regexp,_new_regexp,services,_new_services,ttl,view
header-nsrecord,dname*,_new_dname,fqdn*,zone_nameservers*,view
header-srvrecord,fqdn*,_new_fqdn,port*,_new_port,priority*,_new_priority,target*,_new_target,weight*,_new_weight,comment,creator,ddns_principal,ddns_protected,disabled,ttl,view
header-txtrecord,fqdn*,_new_fqdn,text*,_new_text,comment,creator,ddns_principal,ddns_protected,disabled,ttl,view
nsrecord,dname1.test.com,,test_csv.com,"*******/True,*******/False",default
nsrecord,dname.test.com,,test_csv.com,"*******/True,*******/False",default
nsrecord,dname2.test.com,,test_csv.com,"*******/True,*******/False",default
arecord,*********,,a1.test_csv.com,,A record modified,False,STATIC,,,False,7200,default
arecord,*********,,a11.test_csv.com,,A11 record,False,STATIC,,,False,,default
arecord,*********,,a12.test_csv.com,,A12 record modified,False,STATIC,,,False,4200,default
aaaarecord,2001::123,,a4.test_csv.com,,AAAA record,False,STATIC,,,False,7200,default
cnamerecord,a1.test_csv.com,c1.test_csv.com,,C1 record,STATIC,,,False,3600,default
dnamerecord,d1.test_csv.com,,target.test_csv.com,D1 record,STATIC,,,True,100,default
mxrecord,d1.test_csv.com,,webmaster.infoblox.com,,10,,D1 record,STATIC,,,True,500,default
hostrecord,h1.test_csv.com,,"*******,*******,*******","alias1.test.com.test_csv.com,alias2.test.com.test_csv.com",False,TXT1 record,True,,,,,False,True,False,"fc00::,2000:db8:85a3::8a2e:370:7334",default,False,False,,,6667,False,default
hostaddress,*******,,h1.test_csv.com,,,,False,True,,,,,,,,default,,,,,,True,default
hostaddress,*******,,h1.test_csv.com,,,,False,True,,,,,,,,default,,,,,,False,default
hostaddress,*******,,h1.test_csv.com,,,,False,True,,,,,,00:01:02:03:04:05,,default,,,,,,False,default
ipv6hostaddress,fc00::,,h1.test_csv.com,,False,True,,,,,,,,default,,False,,default
ipv6hostaddress,2000:db8:85a3::8a2e:370:7334,,h1.test_csv.com,,False,True,,,,,,,,default,,False,,default
naptrrecord,n1.test_csv.com,,10,,20,,test.com,,NAPTR1 record,STATIC,,,False,U,,,,SIP+D2U,,1000,default
txtrecord,t1.test_csv.com,,hello world,,TXT1 record,STATIC,,,False,6667,default

