<MDXML>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.delegation_ns_group_server">
    <MEMBER-NAME-CHANGE PRE-XFORM-VALUE="delegation_ns_group" POST-XFORM-VALUE="ns_group"/>
  </STRUCTURE-TRANSFORM>

  <REMOVE-TYPES REMOVE-CALLBACK="gsnsgrp_remove_delegation_ns_group">
    <TYPE name=".com.infoblox.dns.delegation_ns_group"/>
  </REMOVE-TYPES>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.delegation_ns_group_server"
        POST-STRUCT-CALLBACK="gsnsgrp_cache_nsgroup_servers"/>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone"
        POST-STRUCT-CALLBACK="gsnsgrp_dns_zone_common_callback">
     <REMOVE-MEMBER MEMBER-NAME="delegation_ns_group"/>
  </STRUCTURE-TRANSFORM>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.cluster_dns_properties"
        POST-STRUCT-CALLBACK="gsnsgrp_cluster_dnsprops_cache"/>

  <POST-PROCESSING PROCESS-FUNCTION="gsnsgrp_zone_nsgroup_links"/>

</MDXML>
