ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
ddns-local-address4 ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/storage/tmp/bug30353/dhcp_updater.key";

subnet 50.0.0.0 netmask *********** {
	pool {
		infoblox-range ******** *********;
		range ******** *********;
		option pop-server *******;
	}
}

subnet *********** netmask ************* {
	not authoritative;
}

shared-network "sn1" {

	subnet 40.0.0.0 netmask *********** {
		}
		pool {
			option dhcp-parameter-request-list = null;
			infoblox-range ******** *********;
			range ******** *********;
			option pop-server *******;
	}
}

#End of dhcpd.conf file
