db_import;db;full;;{gen_id{}}:N

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGrid">
					<name>Infoblox</name>
					<uuid>{rec_id{.com.infoblox.one.cluster$0}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkViewParent">
					<uuid>{rec_id{.com.infoblox.dns.network_view_parent$/}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Network</item>
						<item>IPv6Network</item>
					</allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>Building</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.Building}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Network</item>
						<item>IPv6Network</item>
					</allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>Country</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.Country}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Network</item>
						<item>IPv6Network</item>
					</allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>Region</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.Region}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types></allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>Site</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.Site}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Network</item>
						<item>IPv6Network</item>
					</allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>State</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.State}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Network</item>
						<item>IPv6Network</item>
					</allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags></flags>
					<value_syntax></value_syntax>
					<name>VLAN</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.VLAN}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types></allowed_object_types>
					<attribute_type>STRING</attribute_type>
					<comment></comment>
					<gui_default_value></gui_default_value>
					<flags>R</flags>
					<value_syntax></value_syntax>
					<name>IB Discovery Owned</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.IB Discovery Owned}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types>
						<item>Member</item>
					</allowed_object_types>
					<attribute_type>ENUM</attribute_type>
					<comment></comment>
					<gui_default_value>
						<value_enum>site1</value_enum>
					</gui_default_value>
					<enum_values>
						<item>
							<enum_value>site1</enum_value>
						</item>
						<item>
							<enum_value>site2</enum_value>
						</item>
						<item>
							<enum_value>site3</enum_value>
						</item>
						<item>
							<enum_value>site4</enum_value>
						</item>
						<item>
							<enum_value>site5</enum_value>
						</item>
					</enum_values>
					<flags>R</flags>
					<value_syntax></value_syntax>
					<name>ReportingSite</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.ReportingSite}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types></allowed_object_types>
					<attribute_type>INTEGER</attribute_type>
					<comment>Sub grid int 1 ext_ettr_def</comment>
					<gui_default_value></gui_default_value>
					<flags>V</flags>
					<value_syntax></value_syntax>
					<name>int_ead1</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.int_ead1}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridMember">
					<comment></comment>
					<ha_enabled>false</ha_enabled>
					<host_name>infoblox.localdomain</host_name>
					<is_master>true</is_master>
					<master_candidate_enabled>true</master_candidate_enabled>
					<member_type>INFOBLOX</member_type>
					<virtual_ip>***********</virtual_ip>
					<virtual_oid>0</virtual_oid>
					<uuid>{rec_id{.com.infoblox.one.virtual_node$0}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<comment></comment>
					<name>default</name>
					<id>0</id>
					<uuid>{rec_id{.com.infoblox.dns.network_view$0}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<comment>Sub grid network View 1</comment>
					<name>netview-1</name>
					<id>8</id>
					<uuid>{rec_id{.com.infoblox.dns.network_view$8}}</uuid>
					<extensible_attributes>
						<item>
							<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.int_ead1}}</uuid>
							<values>
								<item>
									<value_integer>123</value_integer>
								</item>
							</values>
						</item>
					</extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<comment>Sub grid network View 2</comment>
					<name>netview-2</name>
					<id>9</id>
					<uuid>{rec_id{.com.infoblox.dns.network_view$9}}</uuid>
					<extensible_attributes></extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:AdminGroup">
					<comment></comment>
					<disabled>false</disabled>
					<name>admin-group</name>
					<superuser>true</superuser>
					<uuid>{rec_id{.com.infoblox.one.admin_group$.admin-group}}</uuid>
					<extensible_attributes></extensible_attributes>
					<access_rights></access_rights>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:AdminGroup">
					<comment>Admins allowed to perform API request on Cloud API with superuser permissions</comment>
					<disabled>false</disabled>
					<name>cloud-api-superuser</name>
					<superuser>true</superuser>
					<uuid>{rec_id{.com.infoblox.one.admin_group$.cloud-api-superuser}}</uuid>
					<extensible_attributes></extensible_attributes>
					<access_rights></access_rights>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

