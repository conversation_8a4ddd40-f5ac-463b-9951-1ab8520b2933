<MDXML>
<STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.analytics.cluster_analytics_module_set" POST-STRUCT-CALLBACK="remove_current_analytics_moduleset">
</STRUCTURE-TRANSFORM>

<STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.analytics.analytics_module_set" POST-STRUCT-CALLBACK="remove_analytics_moduleset_entries">
  <!-- TI-TODO: need to check what value can be set for secondary_version as its key along with version field in this table. -->
  <!-- We will add this field when secondary detectors are included -->
  <!-- <NEW-MEMBER MEMBER-NAME="secondary_version" VALUE=""/> -->
  <NEW-MEMBER MEMBER-NAME="kafka_version" VALUE=""/>
  <NEW-MEMBER MEMBER-NAME="spark_version" VALUE=""/>
  <NEW-MEMBER MEMBER-NAME="middleware_version" VALUE=""/>
</STRUCTURE-TRANSFORM>

<STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.analytics.cluster_analytics_properties" POST-STRUCT-CALLBACK="reset_latest_moduleset">
  <NEW-MEMBER MEMBER-NAME="last_checked_for_package_update" VALUE=""/>
  <NEW-MEMBER MEMBER-NAME="last_updated_package_version" VALUE=""/>
</STRUCTURE-TRANSFORM>

</MDXML>
