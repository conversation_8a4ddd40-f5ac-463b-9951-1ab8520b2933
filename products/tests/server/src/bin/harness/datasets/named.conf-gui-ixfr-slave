options {
	directory "/storage/tmp/GUI-IXFR";	// Working directory
	pid-file "named.pid-slave";		// Put pid file in working dir
	recursion no;				// Do not provide recursive service
	listen-on port 53 { *********; };
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 954
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	channel slave_syslog {
		syslog daemon;
		severity debug;
	};
	category default { slave_syslog; };
};

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	file "localhost.rev";
	notify no;
};

zone "test.com" {
	type slave;
	masters port 53 { MASTERIP; };
	file "db.test.com";
	notify no;
};

zone "rrset.com" {
	type slave;
	masters port 53 { MASTERIP; };
	file "db.rrset.com";
	notify no;
};

zone "rrset-nsg.com" {
	type slave;
	masters port 53 { MASTERIP; };
	file "db.rrset-nsg.com";
	notify no;
};

zone "test-alias.com" {
	type slave;
	masters port 53 { MASTERIP; };
	file "db.test-alias.com";
	notify no;
};

zone "test1.com" {
	type slave;
	masters port 53 { MASTERIP; };
	file "db.test1.com";
	notify no;
};

zone "test2.com" {
	type slave;
	masters port 53 { MASTERIP; };
	file "db.test2.com";
	notify no;
};

zone "192.in-addr.arpa" {
	type slave;
	masters port 53 { MASTERIP; };
	file "db.192.in-addr.arpa";
	notify no;
};
