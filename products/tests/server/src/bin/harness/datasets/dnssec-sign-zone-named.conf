acl "loopbacknet" { 127.0.0.0/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";
	recursion no;			// Do not provide recursive service
	dnssec-enable yes;
	dnssec-validation yes;
	dnssec-accept-expired no;
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

view "VIEW_NAME" {

	// Root server hints
	zone "." { type hint; file "root.hint"; };

	// Reverse mapping for loopback address
	zone "0.0.127.in-addr.arpa" {
		type master;
		file "localhost.rev";
		notify no;
	};

	zone "signtest.com" {
		type master;
		notify no;
		sig-validity-interval 30 22;
		file "dnssec-db-signtest.com";
		allow-update { "loopbacknet"; };
	};

	zone "1.168.192.in-addr.arpa" {
		type master;
		notify no;
		sig-validity-interval 30 22;
		file "dnssec-db-1.168.192.in-addr.arpa";
		allow-update { "loopbacknet"; };
	};

	zone "d.c.b.a.ip6.arpa" {
		type master;
		notify no;
		sig-validity-interval 30 22;
		file "dnssec-db-d.c.b.a.ip6.arpa";
		allow-update { "loopbacknet"; };
	};

	zone "sign test/42.com" {
		type master;
		notify no;
		sig-validity-interval 30 22;
		file "dnssec-db-sign032test.com";
		allow-update { "loopbacknet"; };
	};
};
