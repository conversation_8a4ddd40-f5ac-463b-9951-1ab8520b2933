// Inspired by examples in chapter 3 of the BIND ARM

key "key1" {
  algorithm hmac-md5;
  secret "CdhtOnXxkZeuHP5saggSnA==";
};

key "key2" {
  algorithm hmac-md5;
  secret "MxuDAibo+rXNf9Cp9I4V6g==";
};

// This key is not used in any match-clients list
key "key-not-in-use" {
  algorithm hmac-md5;
  secret "xo9Qz0Tq435DBzZvXYVRsA==";
};

acl "loopbacknet" { 127.0.0.0/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";
	recursion no;			// Do not provide recursive service
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};


view "another_view" {
    match-clients { key "key1"; key "key2"; };
    zone "bulk.infoblox.com" {
        type master;
        database infoblox_zdb;
        notify no;
        file "bulk.infoblox.com.db-another_view";
    };
    zone "2.168.192.in-addr.arpa" {
	type master;
	database infoblox_zdb;
	notify no;
	file "2.168.192.in-addr.arpa-another_view";
	allow-update { "loopbacknet"; };
    };
};

view "VIEW_NAME" {

// Root server hints
zone "." { type hint; file "root.hint"; };

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	database infoblox_zdb;
	file "localhost.rev";
	notify no;
};

zone "test.infoblox.com" {
	type master;
	database infoblox_zdb;
	notify no;
	file "test.infoblox.com.db";
	allow-update { "loopbacknet"; };
};

zone " test escape .infoblox.com" {
	type master;
	database infoblox_zdb;
	notify no;
	file "test-escape.infoblox.com.db";
	allow-update { "loopbacknet"; };
};

zone "1.168.192.in-addr.arpa" {
	type master;
	database infoblox_zdb;
	notify no;
	file "1.168.192.in-addr.arpa";
	allow-update { "loopbacknet"; };
};

zone "b0003.grainger.com" {
	type master;
	database infoblox_zdb;
// Don't notify; the NS records are probably bogus for this test zone
	notify no;
	file "b0003.grainger.com";
	allow-update { "loopbacknet"; };
};

zone "bulk.infoblox.com" {
        type master;
        database infoblox_zdb;
        notify no;
        file "bulk.infoblox.com.db";
	allow-update { "loopbacknet"; };
};

zone "2.168.192.in-addr.arpa" {
        type master;
        database infoblox_zdb;
        notify no;
        file "2.168.192.in-addr.arpa";
};

zone "3.in-addr.arpa" {
	type master;
	database infoblox_zdb;
	notify no;
	file "3.in-addr.arpa";
	allow-update { "loopbacknet"; };
};

zone "10.in-addr.arpa" {
	type master;
	database infoblox_zdb;
	notify no;
	file "10.in-addr.arpa";
};

zone "apo.infoblox.com" {
	type master;
	database infoblox_zdb;
	notify no;
	file "apo.infoblox.com.db";
};

zone "d.c.b.a.ip6.arpa" {
	type master;
	database infoblox_zdb;
	notify no;
	file "d.c.b.a.ip6.arpa";
	allow-update { "loopbacknet"; };
};

zone "naptr.test" {
	type master;
	database infoblox_zdb;
	notify no;
	file "naptr.test.db";
	allow-update { "loopbacknet"; };
};

zone "xn--caf-dma.fr" {
	type master;
	database infoblox_zdb;
	notify no;
	file "db.xn--caf-dma.fr";
	allow-update { "loopbacknet"; };
};

};
