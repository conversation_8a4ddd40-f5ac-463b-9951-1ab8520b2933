"version 1.0",,,,,,,

Header-AuthZone,fqdn,,,grid_primaries,view,,zone_format
AuthZone,test_csv_import.com,,,infoblox.localdomain,default,,FORWARD
AuthZone,20.0.0.0/24,,,infoblox.localdomain,default,,IPV4
AuthZone,test_data_import.com ,,,infoblox.localdomain,default,,FORWARD

Header-AaaaRecord,fqdn*,view,address*,,comment,disabled,ttl
AaaaRecord,a4.test_csv_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,a1.test_csv_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,a2.test_csv_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,a3.test_csv_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200

AaaaRecord,a6.test_csv_import.com,default,2001::123,nul column test,,No,3600
AaaaRecord,a5.test_csv_import.com,default,2001::456,nul column test,AAAA record,FALSE,7200
AaaaRecord,a8.test_csv_import.com,default,,nul column test,AAAA record,FALSE,7200
AaaaRecord,ay.test_csv_import.com,default,2001::,nul column test,AAAA record,FALSE,7200
AaaaRecord,au.test_csv_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,aa.test_csv_import.com,default,2001::783,nul column test,AAAA record,FALSE,7200
AaaaRecord,as.test_csv_import.com,default,2001::FF,nul column test,AAAA record,FALSE,aaaa
AaaaRecord,ab.test_csv_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,ac.test_csv_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,ad.test_csv_import.com,default,2001::243,nul column test,AAAA record,FALSE,7200
AaaaRecord,ae.test_csv_import.com,default,2001::126,nul column test,AAAA record,FALSE,7200
AaaaRecord,ar.test_csv_import.com,default,2001::237,nul column test,AAAA record,FALSE,7200
AaaaRecord,at.test_csv_import.com,default,,nul column test,AAAA record,FALSE,7200
AaaaRecord,az.test_csv_import.com,default,2001::176,nul column test,AAAA record,FALSE,7200
AaaaRecord,aj.test_csv_import.com,default,2001::124,nul column test,AAAA record,FALSE,7200
AaaaRecord,ak.test_csv_import.com,default,2001::135,nul column test,AAAA record,FALSE,7200
AaaaRecord,al.test_csv_import.com,default,2001::273,nul column test,AAAA record,FALSE,7200
AaaaRecord,ao.test_csv_import.com,default,2001::873,nul column test,AAAA record,FALSE,7200
AaaaRecord,al4.test_csv_import.com,default,2001::143,nul column test,AAAA record,FALSE,7200
AaaaRecord,aw.test_csv_import.com,default,2001::163,nul column test,AAAA record,FALSE,7200
AaaaRecord,aq.test_csv_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,av.test_csv_import.com,default,2001::223,nul column test,AAAA record,FALSE,7200
AaaaRecord,ai.test_csv_import.com,default,2001::1234,nul column test,AAAA record,FALSE,7200
AaaaRecord,ap.test_csv_import.com,default,2001::567,nul column test,AAAA record,FALSE,7200

AaaaRecord,a4.test_data_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,a1.test_data_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,a2.test_data_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,a3.test_data_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200

AaaaRecord,a6.test_data_import.com,default,2001::123,nul column test,,Yes,3600
AaaaRecord,a5.test_data_import.com,default,2001::456,nul column test,AAAA record,FALSE,7200
AaaaRecord,a8.test_data_import.com,default,,nul column test,AAAA record,FALSE,7200
AaaaRecord,ay.test_data_import.com,default,2001::,nul column test,AAAA record,FALSE,7200
AaaaRecord,au.test_data_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,aa.test_data_import.com,default,2001::783,nul column test,AAAA record,FALSE,7200
AaaaRecord,as.test_data_import.com,default,2001::FF,nul column test,AAAA record,FALSE,aaaa
AaaaRecord,ab.test_data_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,ac.test_data_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,ad.test_data_import.com,default,2001::243,nul column test,AAAA record,FALSE,7200
AaaaRecord,ae.test_data_import.com,default,2001::126,nul column test,AAAA record,FALSE,7200
AaaaRecord,ar.test_data_import.com,default,2001::237,nul column test,AAAA record,FALSE,7200
AaaaRecord,at.test_data_import.com,default,,nul column test,AAAA record,FALSE,7200
AaaaRecord,az.test_data_import.com,default,2001::176,nul column test,AAAA record,FALSE,7200
AaaaRecord,aj.test_data_import.com,default,2001::124,nul column test,AAAA record,FALSE,7200
AaaaRecord,ak.test_data_import.com,default,2001::135,nul column test,AAAA record,FALSE,7200
AaaaRecord,al.test_data_import.com,default,2001::273,nul column test,AAAA record,FALSE,7200
AaaaRecord,ao.test_data_import.com,default,2001::873,nul column test,AAAA record,FALSE,7200
AaaaRecord,al4.test_data_import.com,default,2001::143,nul column test,AAAA record,FALSE,7200
AaaaRecord,aw.test_data_import.com,default,2001::163,nul column test,AAAA record,FALSE,7200
AaaaRecord,aq.test_data_import.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,av.test_data_import.com,default,2001::223,nul column test,AAAA record,FALSE,7200
AaaaRecord,ai.test_data_import.com,default,2001::1234,nul column test,AAAA record,FALSE,7200
AaaaRecord,ap.test_data_import.com,default,2001::567,nul column test,AAAA record,FALSE,7200

Header-CnameRecord,fqdn*,view,canonical_name*,comment,disabled,ttl
Header-DnameRecord,fqdn*,view,target*,,,,,,comment,disabled,ttl,EA-Site
CnameRecord,c1.test_data_import.com,default,www.test.com,C11 Record,FALSE,28800
DnameRecord,d1.test_data_import.com,default,d1.foo.com,,,,,,,FALSE,28800,
CnameRecord,c21.test_data_import.com,default,www.test.com,C21 Record,FALSE,28800
DnameRecord,d21.test_data_import.com,default,d1.foo.com,,,,,,,FALSE,28800,

Header-ARecord,fqdn*,view,address*,comment,disabled,ttl
ARecord,c1.test_data_import.com,default,*********,C1 record,FALSE,2800
ARecord,c2.test_data_import.com,default,*********,C2 record,FALSE,6400
ARecord,c2.test_data_import.com,default,*********,C2 record,FALSE,6400
ARecord,c2.test_data_import.com,default,*********,C2 record,FALSE,6400
ARecord,c2.test_data_import.com,default,*********,C2 record,FALSE,6400
ARecord,c2.test_data_import.com,default,*********,C2 record,FALSE,6400

Header-DnameRecord,fqdn*,view,target*,comment,disabled,ttl
DnameRecord,h1.test_data_import.com,default,d1.foo.com,h1 Dname Record,FALSE,1234
DnameRecord,h2.test_data_import.com,default,d2.foo.com,h2 Dname Recoed,FALSE,4567
DnameRecord,i1.test_csv_import.com,default,e1.foo.com,i1 Dname Record of CSV import,FALSE,1234
DnameRecord,i2.test_csv_import.com,default,e2.foo.com,i2 Dname Recoed of CSV import,FALSE,4567

Header-NaptrRecord,fqdn,view,preference,services,regexp,flags,order,replacement,comment,disabled,ttl
NaptrRecord,n1.test_csv_import.com,default,20,SIP+D2U,,U,10,test_csv.com,NAPTR1 record for CSVimport,FALSE,1000
NaptrRecord,n2.test_csv_import.com,default,30,SIP+D2U,,U,20,test_csv.com,NAPTR2 record for CSVimport,FALSE,2000
NaptrRecord,n20.test_cv_import.com,default,30,SIP+D2U,,U,20,test_csv.com,NAPTR20 record for CSVimport,FALSE,2000
NaptrRecord,n21.test_csv_import.com,default1,30,SIP+D2U,,U,20,test_csv.com,NAPTR21 record for CSVimport,FALSE,2000
NaptrRecord,n22.test_csv_import.com,default,abc,SIP+D2U,,U,20,test_csv.com,NAPTR22 record for CSVimport,FALSE,2000
NaptrRecord,n24.test_csv_import.com,default,30,SIP+D2U,AaaaRecord,U,20,test_csv.com,NAPTR24 record for CSVimport,FALSE,2000
NaptrRecord,n25.test_csv_import.com,default,30,SIP+D2U,,1`786@./,20,test_csv.com,NAPTR25 record for CSVimport,FALSE,2000
NaptrRecord,n26.test_csv_import.com,default,30,SIP+D2U,,U,str,test_csv.com,NAPTR26 record for CSVimport,FALSE,2000
NaptrRecord,n27.test_csv_import.com,default,30,SIP+D2U,,U,20,,NAPTR27 record for CSVimport,FALSE,2000
NaptrRecord,n29.test_csv_import.com,default,30,SIP+D2U,,U,20,test_csv.com,NAPTR29 record for CSVimport,BOOL,2000
NaptrRecord,n200.test_csv_import.com,default,30,SIP+D2U,,U,20,test_csv.com,NAPTR200 record for CSVimport,FALSE,abcd

DnameRecord,d12.test_csv_import.com,default,..123,D12 record,TRUE,100
CnameRecord,c112.test_csv_import.com,default,..11a1sv,C12 record,FALSE,3600
CnameRecord,c12.test_csv_import.com,default,cname,C12 record,FALSE,3600
ARecord,ar1.test_csv_import.com,default,2,A record with Ipv6 address,FALSE,7200
AaaaRecord,aIP4.test_csv_import.com,default,10..56,nul column test,AAAA record with Ipv4 address,FALSE,7200

Header-HostRecord,fqdn,view,addresses,ipv6_addresses,aliases,configure_for_dns,comment,disabled,ttl
HostRecord,v11.test_csv_import.com,default,"*******,*******","2001:0db8:85a3:0000:0000:8a2e:0370:7334,fc00::","alias1.test_import.com,alias2.test_import.com",TRUE,V11 record,False,6667
HostRecord,v12.test_csv_import.com,default,"*******,*******","2001:0db8:85a3:0000:0000:8a2e:0370:7334,fc00::",\;>">,TRUE,V12 record,False,6667
HostRecord,v13.test_csv_import.com,default,"*******,*******","2001:0db8:85a3:0000:0000:8a2e:0370:7334,fc00::","alias1.test_import.com,alias2.test_import.com",ANY,V13 record,False,6667
HostRecord,v14.test_csv_import.com,default,"*******,*******","2001:0db8:85a3:0000:0000:8a2e:0370:7334,fc00::","alias1.test_import.com,alias2.test_import.com",1000,V14 record,False,6667

Header-IPv6HostAddress,parent,address
IPv6HostAddress,v11.test_csv_import.com,2000::123
IPv6HostAddress,,2001:cdb8:85a3:0000:0000:8a2e:0370:7224
IPv6HostAddress,hh13.test_csv_import.com,2000:0db8:85a3:0000:0000:8a2e:0370:7334

Header-MxRecord,fqdn,view,mx,priority,comment,disabled,ttl
MxRecord,d11.test_csv_import.com,default,webmaster.infoblox.com,10,D11 record,TRUE,500
MxRecord,d12.test_csv_import.com,default,..123,10,D12 record,TRUE,500
MxRecord,d13.test_csv_import.com,default,webmaster.infoblox.com,wer,D13 record,TRUE,500

Header-NsRecord,fqdn,view,dname,zone_nameservers
NsRecord,test_csv_import.com,default,dname.test_import.com,"*******/FALSE,*******/TRUE"
NsRecord,n12.test_csv_import.com,default,..,"*******/FALSE,*******/TRUE"
NsRecord,n13.test_csv_import.com,default,dname.test_import.com,../
NsRecord,n14.test_csv_import.com,default,dname.test_import.com,"*******/FALSE,*******/TRUE"

Header-SrvRecord,fqdn,view,target,port,weight,priority,comment,disabled,ttl
SrvRecord,s1.test_csv_import.com,default,target.test_import.com,6666,5,10,SRV1 record,FALSE,500
SrvRecord,s2.test_csv_import.com,default,target.test_import.com,portA,5,10,SRV1 record,FALSE,500
SrvRecord,s3.test_csv_import.com,default,target.test_import.com,6666,FIVE,10,SRV1 record,FALSE,500

Header-TxtRecord,fqdn,view,text,comment,disabled,ttl
TxtRecord,t11.test_csv_import.com,default,hello world,TXT11 record,FALSE,6667
TxtRecord,t12.test_csv_import.com,default,,TXT12 record,FALSE,6667

Header-PtrRecord,fqdn*,address*,dname*,comment,disabled,ttl
PtrRecord,********.in-addr.arpa,********,p1.com,PTR11 record,FALSE,28800
PtrRecord,20.0.0.0/24,*********,sv.dr.fg,PTR12 record,FALSE,32800

Header-DhcpMacFilter,comment,never_expires,name,expiration_interval,enforce_expiration_time
DhcpMacFilter,MAC1 filter for DHCP,TRUE,MAC1 Filter,3624,FALSE
DhcpMacFilter,MAC2 filter for DHCP,DENY,MAC2 Filter,3624,FALSE
DhcpMacFilter,MAC3 filter for DHCP,TRUE,MAC3 Filter,aaa,FALSE
DhcpMacFilter,MAC4 filter for DHCP,TRUE,MAC4 Filter,3624,BOOL

Header-IPv6Network,comment,address,cidr,auto_create_reversezone
IPv6Network,This is an IPv6 Network2,fc01:0:0:1::,64,TRUE
IPv6Network,This is an IPv6 Network2,2001::123,/32,TRUE
IPv6Network,This is an IPv6 Network3,2001::456,24,ANY

Header-NacFilter,comment,name,expression,,
NacFilter,First Nac Filter,nac him,"Sophos.ComplianceState=""Compliant""",,
NacFilter,Second Nac Filter,nac her,xyz,,

Header-OptionSpace,comment,name,
OptionSpace,Option space for DEF-corp,DEF-co_options,VENDOR_SPACE
OptionSpace,Option space for ABC-corp,ABC-co_options,VENDOR_SPACE

Header-OptionFilter,comment,lease_time,name,boot_server,option_space,next_server,pxe_lease_time,boot_file
OptionFilter,Option Filter1 for XYZ,7200,Option filter1,abc.domain.com,DEF-co_options,blue.domain.com,3600,bootfile1
OptionFilter,Option Filter2 for ANY,7200,Option filter2,abc.domain.com,infoblox_DHCp,blue.domain.com,ANY,bootfile1

Header-OptionDefinition,name,space,type,code
OptionDefinition,optiondef1,DEF-co_options,T_TEXT,254

Header-MacFilterAddress,comment,parent,is_registered_user,guest_custom_field4,guest_custom_field2,guest_custom_field3,guest_custom_field1,expire_time,guest_middle_name,never_expires,guest_email,registered_user,guest_first_name,guest_last_name,mac_address
MacFilterAddress,our man1,MAC1 Filter,TRUE,four,2,3,one,2010-09-16T18:40:00Z,Doe,FALSE,<EMAIL>,John Doe,John,Doe,aa:11:bb:22:cc:33
MacFilterAddress,our man2,MAC1 Filter,ONE,four,2,3,one,2011,Doe,FALSE,<EMAIL>,John Doe,John,Doe,aa:11:bb:22:cc:33
MacFilterAddress,our man3,MAC1 Filter,TRUE,four,2,3,one,2011,Doe,FALSE,<EMAIL>,John Doe,John,Doe,aa:11:bb:22:cc:33
MacFilterAddress,our man4,MAC1 Filter,TRUE,four,2,3,one,2011,Doe,FALSE,<EMAIL>,John Doe,John,Doe,aa:11:bb:22:cc:33

Header-RelayAgentFilter,comment,circuit_id_rule,name,circuit_id,remote_id,remote_id_rule
RelayAgentFilter,First Relay Agent Filter,MATCHES_VALUE,RelayAgent Filter1,circuit_name,50,MATCHES_VALUE
RelayAgentFilter,Second Relay Agent Filter,MATCHES_VALUE,RelayAgent Filter2,circuit_name,,MATCHES_VALUE
RelayAgentFilter,Third Relay Agent Filter,MATCHES_VALUE,,circuit_name,50,MATCHES_VALUE

"Header-Network","address*","netmask*","network_view","comment","auto_create_reversezone","is_authoritative","boot_file","boot_server","ddns_domainname","generate_hostname","always_update_dns","update_static_leases","ddns_ttl","enable_option81","deny_bootp","disabled","enable_ddns","enable_thresholds","enable_threshold_email_warnings","enable_threshold_snmp_warnings","range_high_water_mark","range_high_water_mark_reset","ignore_client_requested_options","range_low_water_mark","range_low_water_mark_reset","next_server","pxe_lease_time","recycle_leases","threshold_email_addresses","dhcp_members","routers","domain_name_servers"
"Network","20.0.0.0","***********","default"," IPv4 network0",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,FALSE,FALSE,FALSE,TRUE,TRUE,FALSE,80,70,TRUE,10,20,"blue.domain.com",1100,FALSE,"<EMAIL>,<EMAIL>","infoblox.localdomain","********,********00","********,********"
"Network","********","*********","default"," IPv4 network1",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,FALSE,FALSE,FALSE,TRUE,TRUE,FALSE,80,70,TRUE,TEN,20,"blue.domain.com",1100,FALSE,"<EMAIL>,<EMAIL>","infoblox.localdomain","********,*********0","********,********"
"Network","********","***********","default"," IPv4 network2",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,FALSE,FALSE,FALSE,TRUE,TRUE,FALSE,EIGHt,70,TRUE,10,20,"blue.domain.com",1100,FALSE,"<EMAIL>,<EMAIL>","infoblox.localdomain","********,*********0","********,********"
"Network","********","***********","default"," IPv4 network3",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,FALSE,FALSE,FALSE,TRUE,TRUE,FALSE,80,70,TRUE,10,20,"blue.domain.com",1100,FALSE,aaa,"infoblox.localdomain","********,*********0","********,********"
"Network","********","***********","default"," IPv4 network4",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,FALSE,FALSE,FALSE,222,TRUE,FALSE,80,70,TRUE,10,20,"blue.domain.com",1100,FALSE,"<EMAIL>,<EMAIL>","infoblox.localdomain","********,*********0","********,********"
"Network","********","***********","default"," IPv4 network5",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,FALSE,FALSE,FALSE,TRUE,TRUE,5,80,70,TRUE,10,20,"blue.domain.com",1100,FALSE,"<EMAIL>,<EMAIL>","infoblox.localdomain","********,*********0","********,********"
"Network","********","***********","default"," IPv4 network6",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,FALSE,FALSE,FALSE,TRUE,8,FALSE,80,TRUE,10,"blue.domain.com",1100,FALSE,"<EMAIL>,<EMAIL>","infoblox.localdomain","********,*********0","********,********"
"Network","********0","***********","default"," IPv4 network7",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,FALSE,FALSE,FALSE,TRUE,TRUE,FALSE,80,TRUE,10,"blue.domain.com",1100,FALSE,"<EMAIL>,<EMAIL>",678,"********,*********0","********,********"
"Network","********1","***********","default"," IPv4 network8",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,FALSE,FALSE,FALSE,TRUE,TRUE,FALSE,80,TRUE,10,"blue.domain.com",1100,999,"<EMAIL>,<EMAIL>","infoblox.localdomain","********,*********0","********,********"

Header-SharedNetwork,comment,disabled,generate_hostname,enable_option81,is_authoritative,networks,next_server,domain_name,ignore_client_requested_options,boot_file,always_update_dns,ddns_ttl,routers,enable_ddns,boot_server,pxe_lease_time,network_view,name,deny_bootp,update_static_leases
SharedNetwork,Shared Network for building 1 ,FALSE,TRUE,TRUE,FALSE,"20.0.0.0/***********",blue.domain.com,test_csv_import.com,FALSE,bootfile1,FALSE,1200,"********",TRUE,abc.domain.com,1100,"default",Site1 Network,FALSE,TRUE
SharedNetwork,Shared Network for building 2 ,FALSE,TRUE,TRUE,FALSE,[10/8],blue.domain.com,test_csv_import.com,FALSE,bootfile1,FALSE,1200,"********",TRUE,abc.domain.com,1100,"default",Site2 Network,FALSE,TRUE
SharedNetwork,Shared Network for building 3 ,FALSE,TRUE,TRUE,FALSE,"20.0.0.0/***********,20.0.0.0/***********",blue.domain.com,test_csv_import.com,FALSE,bootfile1,FALSE,rrr,"********",TRUE,abc.domain.com,1100,"default",Site3 Network,FALSE,TRUE
SharedNetwork,Shared Network for building 4 ,FALSE,TRUE,TRUE,FALSE,"30.0.0.0/***********,20.0.0.0/***********",blue.domain.com,test_csv_import.com,FALSE,bootfile1,FALSE,1200,255,TRUE,abc.domain.com,1100,"default",Site4 Network,FALSE,TRUE
SharedNetwork,Shared Network for building 5 ,FALSE,TRUE,TRUE,FALSE,"40.0.0.0/***********,20.0.0.0/***********",blue.domain.com,test_csv_import.com,FALSE,bootfile1,FALSE,1200,"********",TRUE,abc.domain.com,1100,"default",Site Network,FALSE,1002

Header-DhcpRange,domain_name_servers,exclusion_ranges,name,start_address,end_address,member,routers
DhcpRange,"********,********","********0-********5/exclusion comment",range1,********,********00,infoblox.localdomain,"********,********00"
DhcpRange,"********,********",0023::3.3.3-3300::3.3.11,range2,*********,**********,infoblox.localdomain,"*******,*******"
DhcpRange,"*******,*******","*******-********",range3,*******,endaddress,infoblox.localdomain,"*******,*******"

Header-FixedAddress,comment,lease_time,broadcast_address,disabled,next_server,prepend_zero,dhcp_client_identifier,match_option,domain_name_servers,domain_name,,ignore_client_requested_options,,mac_address,boot_file,ddns_hostname,always_update_dns,routers,enable_ddns,boot_server,pxe_lease_time,enable_pxe_lease_time,ip_address,ddns_domainname,name,network_view,deny_bootp,
FixedAddress,Printer 1,1100,***************,FALSE,blue.domain.com,FALSE,,MAC_ADDRESS,,domainname.com,FALSE,FALSE,TRUE,11:22:33:44:55:66,bootfile1,ddns_host,TRUE,"********,*********",TRUE,abc.domain.com,1100,TRUE,********1,ddns_domain,Fixed IP,"default",FALSE,xyz
FixedAddress,Printer 2,1100,***************,FALSE,blue.domain.com,YES,,'MAC_ADDRESS',,domainname.com,FALSE,FALSE,TRUE,11:22:33:44:55:66,bootfile1,ddns_host,TRUE,"*******,*******",TRUE,abc.domain.com,1100,TRUE,*********,ddns_domain,Fixed IP,"default",FALSE,xyz

Header-HostAddress,parent*,address*,mac_address,configure_for_dhcp,deny_bootp,broadcast_address,boot_file,next_server,lease_time,OPTION-125
HostAddress,ho3.test_csv_import.com,10.0.0.2,22:11:11:11:11:22,TRUE,FALSE,***************,boot_file1,blue.domain.com,12345,"CSVimport"
HostAddress,ho4.test_csv_import.com,10.0.0.5,22:11:11:11:11:22,CONFIGURE,FALSE,***************,boot_file1,bluestar.domain.com,56789,"CSVimport"

Header-AuthZone,fqdn*,grid_primaries,view,external_secondaries,allow_transfer,allow_query,zone_type,allow_active_dir,allow_update,zone_format
AuthZone,test_csv_import1.com,infoblox.localdomain,default,test.infoblox.com/*******/TRUE,"12.0.0.12/Deny,1234::/64/Allow","12.0.0.12/Deny,1234::/64/Allow",Authoritative,1.2.3.4,"1234::/64/Allow",FORWARD
AuthZone,test_csv_import2.com,infoblox.localdomain,default,test.infoblox.com/*******/TRUE,"12.0.0.12/TRUE,1234::/64/FALSE","12.0.0.12/Deny,1234::/64/Allow",Authoritative,1.2.3.4,"1234::/64/Allow",FORWARD
AuthZone,test_csv_import3.com,infoblox.localdomain,default,test.infoblox.com/*******/TRUE,"12.0.0.12/Deny,1234::/64/Allow","12.0.0.12/Deny,1234::/64/Allow",Authoritative,1.2.3.4,"1234::/64/VALUE",FORWARD

ARecord,ta1.test_csv_import1.com,default,*********,A record,FALSE,7200
ARecord,ta2.test_csv_import1.com,a2.test.com,10.0.0.20,A record update,ALLOW,4000
