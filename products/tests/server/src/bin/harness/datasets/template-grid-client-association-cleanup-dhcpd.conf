ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
ddns-local-address4 ***********;
server-identifier ***********;
infoblox-client-association-grace-period 777600;
authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
option domain-name "infoblox.com";
option broadcast-address ***********;
default-lease-time 3600;
min-lease-time 3600;
max-lease-time 3600;
one-lease-per-client false;
server-name "a.b.com";
next-server *******;
filename "filename";
if substring (option vendor-class-identifier, 0, 9) = "PXEClient" {
	default-lease-time 60;
	min-lease-time 60;
	max-lease-time 60;
}
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/storage/tmp/TestClientAssociationCleanup/dhcp_updater.key";

subnet *********** netmask ************* {
	not authoritative;
}

shared-network "infoblox" {
	infoblox-client-association-grace-period 1900800;

subnet ********* netmask ********* {
	authoritative;
	option domain-name "infoblox.com";
	option broadcast-address ***********;
		default-lease-time 3600;
		min-lease-time 3600;
		max-lease-time 3600;
	infoblox-client-association-grace-period 2851200;
		server-name "a.b.com";
		next-server *******;
		filename "filename";
		if substring (option vendor-class-identifier, 0, 9) = "PXEClient" {
			default-lease-time 60;
			min-lease-time 60;
			max-lease-time 60;
		}
	}
	pool {
		infoblox-range ********** **********;
		range ********** **********;
		infoblox-client-association-grace-period 3801600;
	}
}


#End of dhcpd.conf file
