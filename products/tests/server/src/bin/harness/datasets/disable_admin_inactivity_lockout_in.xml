<DATABASE NAME="onedb" VERSION="MDXMLTEST">

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
<PROPERTY NAME="name" VALUE="test_group"/>
<PROPERTY NAME="superuser" VALUE="false"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
</OBJECT>


<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
<PROPERTY NAME="name" VALUE="super_test_group"/>
<PROPERTY NAME="superuser" VALUE="true"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
<PROPERTY NAME="name" VALUE="cloud-api-only"/>
<PROPERTY NAME="superuser" VALUE="true"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
</OBJECT>
</DATABASE>
