<DATABASE NAME="onedb" VERSION="MDXMLTEST">
 <OBJECT>
   <PROPERTY NAME="__type" VALUE=".com.infoblox.reporting.csp_threatdb_properties"/>
   <PROPERTY NAME="parent" VALUE="0"/>
   <PROPERTY NAME="csp_threatdb_local_cache_status" VALUE="false"/>
   <PROPERTY NAME="csp_threatdb_enable_first" VALUE="false"/>
   <PROPERTY NAME="csp_activetrust_configured_status" VALUE="false"/>
   <PROPERTY NAME="csp_threatdb_incr_download_interval" VALUE="2"/>
   <PROPERTY NAME="csp_threatdb_wholedb_download_interval" VALUE="168"/>
   <PROPERTY NAME="csp_threatdb_update_policy" VALUE="AUTOMATIC"/>
   <PROPERTY NAME="csp_threatdb_first_download_time" VALUE=""/>
   <PROPERTY NAME="csp_threatdb_storage_size" VALUE="12288"/>
   <PROPERTY NAME="csp_last_threatdb_incr_update_time" VALUE=""/>
   <PROPERTY NAME="csp_download_attempt_time" VALUE=""/>
   <PROPERTY NAME="csp_last_update_attempt_time" VALUE=""/>
   <PROPERTY NAME="csp_whole_db_next_scheduled_download" VALUE=""/>
   <PROPERTY NAME="csp_db_download_status" VALUE="WHOLE_DB_PENDING"/>
   <PROPERTY NAME="csp_active_db_index" VALUE="NONE"/>
   <PROPERTY NAME="csp_pnode" VALUE=""/>
 </OBJECT>
</DATABASE>
