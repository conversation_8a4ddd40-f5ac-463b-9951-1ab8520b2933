<?xml version="1.0" encoding="utf-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ZSI="http://www.zolera.com/schemas/ZSI/" xmlns:ib="urn:ibap.infoblox.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
  <SOAP-ENV:Header></SOAP-ENV:Header>
  <SOAP-ENV:Body>
    <ib:ObjectReadResponse>
      <total_size xsi:type="xsd:int">1</total_size>
      <objects SOAP-ENC:arrayType="ib:object[]">
        <item xsi:type="ib:SubGridMemberMonitorData">
          <monitors SOAP-ENC:arrayType="ib:object[]">
            <item>
              <monitor_type xsi:type="xsd:string">OVERALL</monitor_type>
              <status xsi:type="xsd:string">WORKING</status>
              <description xsi:type="xsd:string"></description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">NTP</monitor_type>
              <status xsi:type="xsd:string">INACTIVE</status>
              <description xsi:type="xsd:string">NTP Service is inactive</description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">TFTP</monitor_type>
              <status xsi:type="xsd:string">INACTIVE</status>
              <description xsi:type="xsd:string">Hard Disk: 0% - TFTP Service is inactive</description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">HTTP_FILE_DIST</monitor_type>
              <status xsi:type="xsd:string">INACTIVE</status>
              <description xsi:type="xsd:string">Hard Disk: 0% - HTTP File Dist Service is inactive</description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">IMC</monitor_type>
              <status xsi:type="xsd:string">INACTIVE</status>
              <description xsi:type="xsd:string">Hard Disk: 0% - IMC Service is inactive</description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">FTP</monitor_type>
              <status xsi:type="xsd:string">INACTIVE</status>
              <description xsi:type="xsd:string">Hard Disk: 0% - FTP Service is inactive</description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">BGP</monitor_type>
              <status xsi:type="xsd:string">INACTIVE</status>
              <description xsi:type="xsd:string"></description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">DHCP</monitor_type>
              <status xsi:type="xsd:string">INACTIVE</status>
              <description xsi:type="xsd:string">UNLICENSED</description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">DNS</monitor_type>
              <status xsi:type="xsd:string">INACTIVE</status>
              <description xsi:type="xsd:string">UNLICENSED</description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">CAPTIVE_PORTAL</monitor_type>
              <status xsi:type="xsd:string">INACTIVE</status>
              <description xsi:type="xsd:string">UNLICENSED</description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">REPORTING</monitor_type>
              <status xsi:type="xsd:string">INACTIVE</status>
              <description xsi:type="xsd:string">UNLICENSED</description>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">BOOT_TIME</monitor_type>
              <status xsi:type="xsd:string">WORKING</status>
            </item>
            <item>
              <monitor_type xsi:type="xsd:string">HA_STATUS</monitor_type>
              <status xsi:type="xsd:string">WORKING</status>
              <description xsi:type="xsd:string">NA</description>
            </item>
          </monitors>
        </item>
      </objects>
    </ib:ObjectReadResponse>
  </SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="utf-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ZSI="http://www.zolera.com/schemas/ZSI/" xmlns:ib="urn:ibap.infoblox.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
  <SOAP-ENV:Header></SOAP-ENV:Header>
  <SOAP-ENV:Body>
    <ib:ObjectReadResponse>
      <total_size xsi:type="xsd:int">1</total_size>
      <objects SOAP-ENC:arrayType="ib:object[]">
        <item xsi:type="ib:SubGridUpgradeStatus">
          <status xsi:type="xsd:string">DEFAULT</status>
          <upgrade_test_status xsi:type="xsd:string">NONE</upgrade_test_status>
          <alternate_version xsi:type="xsd:string"></alternate_version>
          <upload_version xsi:type="xsd:string"></upload_version>
          <completed_members xsi:type="xsd:unsignedInt">0</completed_members>
        </item>
      </objects>
    </ib:ObjectReadResponse>
  </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
