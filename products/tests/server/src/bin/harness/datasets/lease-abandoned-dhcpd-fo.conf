# dhcpd conf file for lease_abandoned.sh
local-address MY-ADDRESS;
server-identifier MY-ADDRESS;
ddns-update-style interim;
not authoritative;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
ping-number 1;
ping-timeout 1;
log-facility daemon;

ddns-updates off;
ignore client-updates;

failover peer "f1"
{
  MY-ROLE;
  address MY-ADDRESS;
  port 519;
  peer address PEER-ADDRESS;
  peer port 519;
  max-response-delay 60;
  max-unacked-updates 10;
  MCLT
  SPLIT
}

# Allocate leases from the loopback network
subnet ********* netmask ********* {
	pool {
                failover peer "f1";
                deny dynamic bootp clients;
                infoblox-range ********* *********;
                range ********* *********;
        }
}

