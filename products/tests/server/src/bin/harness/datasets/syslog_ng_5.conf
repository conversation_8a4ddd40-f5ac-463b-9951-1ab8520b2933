source s_external {
    tcp ( port(666) max_connections(100) );
    tcp6 ( port(666) max_connections(100) );
    udp ( port(555) );
    udp6 ( port(555) );  };

destination d_internal_1 {  udp6 ( "1234::1111" port(514) localip("::") template("<$PRI>$DATE [IPV6_ADDRESS] $MSGHDR$MSG\n") template_escape(no) persist-name("dinternal1")); };
destination d_external_1 {  udp6 ( "1234::1111" port(514) localip("::") ); };
destination d_internal_2 {  tcp6 ( "1234::2222" port(2222) localip("::") template("<$PRI>$DATE [IPV6_ADDRESS] $MSGHDR$MSG\n") template_escape(no) persist-name("dinternal2")); };
destination d_external_3 {  udp6 ( "1234::3333" port(3333) localip("[IPV6_ADDRESS]") ); };

log { source(s_syslogng); filter(f_debug); destination(d_internal_1); };
log { source(s_syslogng); filter(f_err); destination(d_internal_2); };

log { source(s_external); filter(f_debug); destination(d_external_1); };
log { source(s_external); filter(f_alert); destination(d_external_3); };
