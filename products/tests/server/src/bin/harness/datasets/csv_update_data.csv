Header-AuthZone,fqdn*,,,grid_primaries,view,external_secondaries,allow_transfer,allow_query,zone_type,allow_active_dir,allow_update,zone_format,notify_delay,disabled,soa_negative_ttl,soa_mnames,soa_default_ttl,soa_retry,,create_underscore_zones,soa_serial_number,soa_email,comment,soa_expire,soa_refresh,is_multimaster
AuthZone,test.com,,,infoblox.localdomain,default,test.infoblox.com/*******/TRUE,"*********/Deny,1234::/64/Allow","*********/Deny,1234::/64/Allow",Authoritative,*******,"1234::/64/Allow",FORWARD,100,FALSE,400,infoblox.localdomain/mname1,900,800,FALSE,FALSE,1,<EMAIL>,"Update zone",100,200,TRUE
AuthZone,20.0.0.0/24,,,infoblox.localdomain,default,,,,,,,IPV4,,,,,,,,,,,,,,FALSE

Header-ARecord,fqdn,_new_fqdn,view,address*,_new_address,comment,disabled,ttl
ARecord,a1.test.com,,default,"***********","***********0","Update A record",,7200
ARecord,a2.test.com,aa2.test.com,default,"***********",,"Update A record",,7200
ARecord,a3.test.com,,default,**********,,"Update FAILED",,

Header-AaaaRecord,fqdn*,_new_fqdn,view,address,_new_address,comment,disabled,ttl
AaaaRecord,aa1.test.com,aaaa.test.com,default,2001::123,2001::456,Update Aaaa record,FALSE,2700
Header-CnameRecord,fqdn,_new_fqdn,view,canonical_name,comment,disabled,ttl
CnameRecord,c1.test.com,cc1.test.com,default,a1.test_csv.com,Update Cname record,FALSE,3600
Header-DnameRecord,fqdn,_new_fqdn,view,target,comment,disabled,ttl
DnameRecord,d1.test.com,dd1.test.com,default,target.test_csv.com,Update Dname record,TRUE,100
Header-MxRecord,fqdn,_new_fqdn,view,mx,_new_mx,priority,_new_priority,comment,disabled,ttl
MxRecord,m1.test.com,mm1.test.com,default,webmaster.infoblox.com,web.infoblox.com,10,15,Update Mx record,TRUE,500
Header-NaptrRecord,fqdn,_new_fqdn,view,preference,_new_preference,services,_new_services,regexp,_new_regexp,flags,_new_flags,order,_new_order,replacement,_new_replacement,comment,disabled,ttl
NaptrRecord,test.com,,default,20,10,SIP+D2U,,,,U,V,10,5,test.com,n.test.com,Update Naptr record,FALSE,1000

Header-NsRecord,fqdn,view,dname,_new_dname,zone_nameservers
NsRecord,test.com,default,dname.com,dname2.com,"*******/FALSE"

Header-SrvRecord,fqdn,_new_fqdn,view,target,_new_target,port,_new_port,weight,_new_weight,priority,_new_priority,comment,disabled,ttl
SrvRecord,s1.test.com,ss1.test.com,default,target.test.com,target1.test.com,6666,9999,5,7,10,20,Update Srv record,FALSE,500

Header-TxtRecord,fqdn,_new_fqdn,view,text,_new_text,comment,disabled,ttl
TxtRecord,t1.test.com,tt1.test.com,default,hello world,Hello World,Update Txt record,FALSE,6667

Header-PtrRecord,fqdn*,_new_fqdn,address*,dname*,_new_dname,comment,disabled,ttl
PtrRecord,********.in-addr.arpa,********.in-addr.arpa,********,p1.com,p2.com,Update Ptr record,FALSE,28800

Header-RelayAgentFilter,comment,circuit_id_rule,name,_new_name,circuit_id,remote_id,remote_id_rule
RelayAgentFilter,Update RelayAgentFilter,MATCHES_VALUE,RelayAgent Filter1,RELAYAGENT Filter,circuit_name,50,MATCHES_VALUE
Header-SharedNetwork,comment,disabled,generate_hostname,enable_option81,is_authoritative,networks,next_server,domain_name,ignore_client_requested_options,boot_file,always_update_dns,ddns_ttl,routers,enable_ddns,boot_server,pxe_lease_time,network_view,name,_new_name,deny_bootp,update_static_leases
SharedNetwork,Update SharedNetwork,FALSE,TRUE,TRUE,FALSE,"10.0.0.0/***********",blue.domain.com,test_csv_import.com,FALSE,bootfile1,FALSE,1200,"********",TRUE,abc.domain.com,1100,"default",Site1 Network,SITE Network,FALSE,TRUE

Header-OptionSpace,comment,name,_new_name,
OptionSpace,Update option space,MySpace,MyNewSpace,VENDOR_SPACE

Header-OptionDefinition,name,_new_name,space,_new_space,type,code
OptionDefinition,optiondef1,OptionDef,MyNewSpace,,T_TEXT,254
OptionDefinition,OptionDef,,MyNewSpace,MyNewerSpace,T_TEXT,254
Header-OptionFilter,comment,lease_time,name,_new_name,boot_server,option_space,next_server,pxe_lease_time,boot_file
OptionFilter,Update OptionFilter,7200,Option filter1,OPTIONFilter,abc.domain.com,MyNewSpace,blue.domain.com,3600,bootfile1

Header-DhcpMacFilter,comment,never_expires,name,_new_name,expiration_interval,enforce_expiration_time
DhcpMacFilter,Update DhcpMacFilter,TRUE,mac filter,MAC1 Filter,3624,FALSE

Header-MacFilterAddress,comment,parent,is_registered_user,guest_custom_field4,guest_custom_field2,guest_custom_field3,guest_custom_field1,expire_time,guest_middle_name,never_expires,guest_email,registered_user,guest_first_name,guest_last_name,mac_address,_new_mac_address
MacFilterAddress,Update MacFilter,MAC1 Filter,TRUE,four,2,3,one,2010-09-16T18:40:00Z,Doe,FALSE,<EMAIL>,John Doe,John,Doe,aa:11:bb:22:cc:33,bb:22:cc:33:dd:44
Header-NacFilter,comment,name,_new_name,expression,,
NacFilter,Update NacFilter,nac him,NAC Him,"Sophos.ComplianceState=""Compliant""",,

Header-HostAddress,parent*,address*,_new_address,mac_address,configure_for_dhcp,deny_bootp,broadcast_address,boot_file,next_server,lease_time,view
HostAddress,v11.test.com,"********",,"a1:a1:a1:a1:a1:a1",TRUE,FALSE,"***************",boot_file1,blue.domain.com,12345,

Header-IPv6HostAddress,parent,address,view
IPv6HostAddress,v11.test.com,"fc01:0:0:1::",default

Header-DhcpRange,domain_name_servers,exclusion_ranges,name,start_address,_new_start_address,end_address,_new_end_address,member,routers
DhcpRange,"********,********","*********-*********/exclusion comment",range1,********,********,*********,**********,infoblox.localdomain,"********,**********"

Header-FixedAddress,comment,lease_time,broadcast_address,disabled,next_server,prepend_zero,dhcp_client_identifier,match_option,domain_name_servers,domain_name,,ignore_client_requested_options,,mac_address,boot_file,ddns_hostname,always_update_dns,routers,enable_ddns,boot_server,pxe_lease_time,enable_pxe_lease_time,ip_address,_new_ip_address,ddns_domainname,name,network_view,deny_bootp,
FixedAddress,Update FixedAddress,1100,***************,FALSE,blue.domain.com,FALSE,,MAC_ADDRESS,,domainname.com,FALSE,FALSE,TRUE,11:22:33:44:55:66,bootfile1,ddns_host,TRUE,"********,*********",TRUE,abc.domain.com,1100,TRUE,********1,*********,ddns_domain,Fixed IP,"default",FALSE,xyz

