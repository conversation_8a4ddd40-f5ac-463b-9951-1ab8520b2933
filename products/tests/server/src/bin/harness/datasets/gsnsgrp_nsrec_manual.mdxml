<MDXML>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone"
        POST-STRUCT-CALLBACK="dns_zone_common_callback"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.bind_ns"
        POST-STRUCT-CALLBACK="dns_bind_ns_common_callback"/>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.virtual_node"
        POST-STRUCT-CALLBACK="one_virtual_node_common_callback"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_grid_primary"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_cluster_secondary_server"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_forwarding_server"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_ext_primary"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_ext_secondary_server"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_ms_primary_server"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_ms_secondary_server"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_nameserver"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>

  <POST-PROCESSING PROCESS-FUNCTION="gsnsgrp_nsrec_post_processing"/>
</MDXML>
