server-duid ll ethernet 01:02:03:04:05:06;
ddns-update-style interim;
log-facility daemon;
default-lease-time 43200;
preferred-lifetime 27000;
# IF-MAP enabled
infoblox-enable-dhcp-publishing 1;
ddns-updates off;
ignore client-updates;

subnet6 ee90::/64 {
        infoblox-enable-dhcp-publishing 0;
	default-lease-time 10;
	range6 ee90::100 ee90::ffff;
	prefix6 ee90:: ee90:: /16;
}

#End of dhcpdv6.conf file
