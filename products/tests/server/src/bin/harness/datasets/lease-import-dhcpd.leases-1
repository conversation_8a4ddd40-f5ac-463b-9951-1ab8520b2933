# All times in this file are in UTC (GMT), not your local timezone.   This is
# not a bug, so please don't ask about it.   There is no portable way to
# store leases in the local timezone, so please don't request this as a
# feature.   If this is inconvenient or confusing to you, we sincerely
# apologize.   Seriously, though - don't ask.
# The format of this file is documented in the dhcpd.leases(5) manual page.
# This lease file was written by isc-dhcp-V3.0.1rc12

# The following data is for test purposes only, and is not meant to
# be used to actually give out an IP address to any client.
lease ************* {
  starts 1 2004/02/23 21:48:42;
  ends 6 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state active;
  hardware ethernet 00:e0:81:27:cd:4d;
  option agent.circuit-id "ab";
  option agent.remote-id "aa";
}
# This lease should fall outside the DHCP range, and generate an error
lease ************* {
  starts 1 2004/02/23 21:48:41;
  ends 0 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state active;
  hardware ethernet 00:e0:81:27:cd:4d;
  option agent.circuit-id "ab";
  option agent.remote-id "aa";
}
# These leases belong to a range which is unassigned
lease ************ {
  starts 1 2004/02/23 21:48:41;
  ends 0 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state active;
  hardware ethernet aa:bb:cc:00:11:22;
}
lease ************ {
  starts 1 2004/02/23 13:19:09;
  ends 0 2009/02/28 13:19:09;
  tstp 1 2004/02/23 21:52:25;
  binding state active;
  hardware ethernet 11:00:11:11:00:11;
}
lease 172.16.28.40 {
  starts 1 2004/02/23 01:01:42;
  ends 0 2009/02/28 01:01:42;
  tstp 1 2004/02/23 21:52:25;
  binding state active;
  hardware ethernet de:ad:be:ef:00:01;
}
lease 172.16.29.17 {
  starts 2 2005/04/12 16:10:12;
  ends 0 2009/02/28 16:10:12;
  tstp 2 2005/04/12 16:10:12;
  binding state active;
  hardware ethernet ab:ba:90:e5:3e:51;
}
lease 172.16.29.99 {
  starts 2 2005/04/12 09:17:18;
  ends 0 2009/02/28 09:17:18;
  tstp 2 2005/04/12 16:10:14;
  binding state active;
  hardware ethernet ab:ba:90:e5:3e:51;
}
lease 172.16.31.100 {
  starts 3 2005/04/13 15:11:48;
  ends 0 2009/02/28 15:11:48;
  tstp 2 2005/04/13 16:10:14;
  binding state active;
  hardware ethernet ba:ab:09:5e:e3:15;
}
lease 172.16.31.70 {
  starts 3 2005/04/13 15:12:57;
  ends 0 2009/02/28 15:12:57;
  tstp 2 2005/04/13 16:10:16;
  binding state active;
  hardware ethernet ab:ab:90:5e:3e:15;
}
lease 172.16.32.32 {
  starts 3 2005/04/13 15:14:03;
  ends 0 2009/02/28 15:14:03;
  tstp 2 2005/04/13 16:10:16;
  binding state active;
  hardware ethernet ba:ba:90:5e:e3:51;
}
lease 172.16.32.23 {
  starts 3 2005/04/13 15:14:07;
  ends 0 2009/02/28 15:14:07;
  tstp 2 2005/04/13 16:10:16;
  binding state active;
  hardware ethernet ba:ba:90:5e:3e:67;
}
lease 172.16.33.1 {
  starts 3 2005/04/13 15:14:07;
  ends 0 2009/02/28 15:14:07;
  tstp 2 2005/04/13 16:10:16;
  binding state active;
  hardware ethernet ba:ee:ff:5e:3e:67;
}
lease 172.16.33.33 {
  starts 3 2005/04/13 15:15:59;
  ends 0 2009/02/28 15:15:59;
  tstp 2 2005/04/13 16:10:19;
  binding state active;
  hardware ethernet 88:ba:77:5e:66:67;
}
