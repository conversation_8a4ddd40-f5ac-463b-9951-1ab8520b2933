version 1.0

Header-AuthZone,fqdn*,,,grid_primaries,view,external_secondaries,allow_transfer,allow_query,zone_type,allow_active_dir,allow_update,zone_format,notify_delay,disabled,soa_negative_ttl,soa_mnames,soa_default_ttl,soa_retry,,create_underscore_zones,soa_serial_number,soa_email,comment,soa_expire,soa_refresh
AuthZone,test_csv.com,,,infoblox.localdomain,default,test.infoblox.com/*******/TRUE,"*********/Deny,1234::/64/Allow","*********/Deny,1234::/64/Allow,TSIG-key1/hhWzttOTZa8spbIfh41gBsoZasxwJXPD7tLXCp47dcWv7oTWbWxzdkuTJZk5e+VT%2FYkya3C0KqfJZbeUn20gnA==/Allow",Authoritative,*******,"1234::/64/Allow",FORWARD,100,FALSE,400,infoblox.localdomain/mname1,900,800,FALSE,FALSE,1,<EMAIL>,comment for zone1,100,200
AuthZone,test_tsig_csv.com,,,infoblox.localdomain,default,test.infoblox.com/*******/TRUE/TRUE/TRUE/foo/sdfsdf86ews3,,,,,,FORWARD,200,TRUE,600,,500,900,FALSE,FALSE,2,<EMAIL>,comment for zone2,100,200

Header-ARecord,fqdn,view,address*,comment,disabled,ttl
ARecord,a1.test_csv.com,default,*********,A record,FALSE,7200

Header-AaaaRecord,fqdn*,view,address,,comment,disabled,ttl
AaaaRecord,a4.test_csv.com,default,2001::123,nul column test,AAAA record,FALSE,7200

Header-ARecord,fqdn,view,address,comment,disabled,ttl,create_ptr
ARecord,a11.test_csv.com,default,*********,A11 record,FALSE,,TRUE

Header-ARecord,fqdn,view,address,comment,disabled,ttl
ARecord,a12.test_csv.com,default,*********,A12 record,FALSE,4200

Header-CnameRecord,fqdn,view,canonical_name,comment,disabled,ttl
CnameRecord,c1.test_csv.com,default,a1.test_csv.com,C1 record,FALSE,3600

Header-DnameRecord,fqdn,view,target,comment,disabled,ttl
DnameRecord,d1.test_csv.com,default,target.test_csv.com,D1 record,TRUE,100

Header-MxRecord,fqdn,view,mx,priority,comment,disabled,ttl
MxRecord,d1.test_csv.com,default,webmaster.infoblox.com,10,D1 record,TRUE,500

Header-NaptrRecord,fqdn,view,preference,services,regexp,flags,order,replacement,comment,disabled,ttl
NaptrRecord,n1.test_csv.com,default,20,SIP+D2U,,U,10,test.com,NAPTR1 record,FALSE,1000

Header-NsRecord,fqdn,view,dname,zone_nameservers
NsRecord,test_csv.com,default,dname.test.com,"*******/FALSE,*******/TRUE"
NsRecord,test_csv.com,default,dname2.test.com,"""*******/FALSE,*******/TRUE"""

Header-SrvRecord,fqdn,view,target,port,weight,priority,comment,disabled,ttl
SrvRecord,s1.test_csv.com,default,target.test.com,6666,5,10,SRV1 record,FALSE,500

Header-TxtRecord,fqdn,view,text,comment,disabled,ttl
TxtRecord,t1.test_csv.com,default,hello world,TXT1 record,FALSE,6667

Header-HostRecord,fqdn,view,addresses,ipv6_addresses,aliases,configure_for_dns,comment,disabled,ttl
HostRecord,h1.test_csv.com,default,"*******,*******","2001:0db8:85a3:0000:0000:8a2e:0370:7334,fc00::","alias1.test.com,alias2.test.com",TRUE,TXT1 record,False,6667

Header-HostAddress,parent,address,pxe_lease_time_enabled,lease_time,broadcast_address,pxe_lease_time,ignore_dhcp_param_request_list,match_option,domain_name_servers,domain_name,mac_address,routers,boot_server,configure_for_dhcp,deny_bootp,next_server
HostAddress,h1.test_csv.com,*******,TRUE,40,*******,50,FALSE,FALSE,"*******,*******",hostaddr1,00:01:02:03:04:05,"*******,*******",*******,FALSE,FALSE,*******

Header-IPv6HostAddress,parent,address
IPv6HostAddress,h1.test_csv.com,2000:0db8:85a3:0000:0000:8a2e:0370:7334

Header-Network,address,netmask,domain_name_servers,dhcp_members,routers,comment,broadcast_address,disabled,next_server,auto_create_reversezone,,ignore_client_requested_options,enable_option81,pxe_lease_time,,ddns_ttl,enable_ddns,boot_server,deny_bootp,enable_pxe_lease_time,update_static_leases,range_low_water_mark,range_high_water_mark,range_low_water_mark_reset,range_high_water_mark_reset,threshold_email_addresses,enable_thresholds,lease_time,enable_threshold_snmp_warnings,enable_threshold_email_warnings,generate_hostname,is_authoritative,domain_name,always_update_dns,dhcp_members,routers,zone_associations,ddns_domainname,,,recycle_leases
Network,*******,*********,"*******,*******",infoblox.localdomain/TRUE/TRUE/TRUE,"*******,*******",comment for network1,*******,FALSE,*******,FALSE,TRUE,FALSE,TRUE,100,NONE,300,TRUE,*******,TRUE,TRUE,TRUE,60,70,64,65,"<EMAIL>,<EMAIL>",TRUE,100,TRUE,TRUE,FALSE,TRUE,host,TRUE,infoblox.localdomain,"",,domainname,TRUE,shnetname1,TRUE
Network,*******,*********,"*******,*******",infoblox.localdomain/TRUE/TRUE/TRUE,"*******,*******",comment for network1,*******,FALSE,*******,FALSE,TRUE,FALSE,TRUE,100,NONE,300,TRUE,*******,TRUE,TRUE,TRUE,60,70,64,65,"<EMAIL>,<EMAIL>",TRUE,100,TRUE,TRUE,FALSE,TRUE,host,TRUE,infoblox.localdomain,"",,domainname,TRUE,shnetname1,TRUE

Header-IPv6Network,address,cidr,comment,zone_associations,auto_create_reversezone
IPv6Network,2000::,16,comment for ipv6 network,,FALSE

Header-SharedNetwork,name,networks
SharedNetwork,shared1,*******/*********
SharedNetwork,shared2,*******/8

Header-DhcpMacFilter,name,comment,never_expires,expiration_interval,enforce_expiration_time
DhcpMacFilter,macfilter1,comment for macfilter1,FALSE,10000,TRUE

Header-NacFilter,name,comment
NacFilter,nacfilter1,comment for nacfilter

Header-OptionSpace,name,comment,
OptionSpace,space1,comment,PREDEFINED_DHCP

Header-OptionFilter,name,comment,lease_time,boot_server,option_space,next_server,pxe_lease_time
OptionFilter,optionfilter1,comment1,40,*******,space1,*******,100

Header-OptionDefinition,name,space,type,code
OptionDefinition,optiondef1,space1,T_TEXT,254

Header-DhcpRange,domain_name_servers,mac_filter_rules,exclusion_ranges,name,start_address,end_address,member,nac_filter_rules,option_filter_rules,routers,comment,broadcast_address,unknown_clients_option,disabled,next_server,deny_all_clients,ignore_client_requested_options,pxe_lease_time,server_association_type,enable_ddns,deny_bootp,enable_pxe_lease_time,range_low_water_mark,range_high_water_mark,range_low_water_mark_reset,range_high_water_mark_reset,enable_thresholds,lease_time,enable_threshold_snmp_warnings,enable_threshold_email_warnings,known_clients_option,boot_server,generate_hostname,threshold_email_addresses,domain_name,always_update_dns,ddns_domainname,recycle_leases
DhcpRange,"*******,*******","macfilter1/Allow","*******-********/'comment1 with \'quotes\' and \, separator',********-********/'comment with quote at the end \'',********-*******0,********-********",range1,*******,********4,infoblox.localdomain,"nacfilter1/Allow","optionfilter1/Allow","*******,*******",comment for range1,*******,Allow,FALSE,*******,FALSE,FALSE,456,NONE,FALSE,FALSE,FALSE,30,70,35,65,TRUE,2000,FALSE,FALSE,Allow,*******,FALSE,"<EMAIL>,<EMAIL>",domain1.com,TRUE,ddnsdomain1,TRUE

Header-FixedAddress,ip_address,mac_address,domain_name_servers,routers,comment,lease_time,broadcast_address,disabled,next_server,prepend_zero,dhcp_client_identifier,match_option,ddns_hostname,always_update_dns,enable_ddns,boot_server,pxe_lease_time,enable_pxe_lease_time,ddns_domainname,name,deny_bootp,
FixedAddress,*******,00:01:02:03:04:05,"*******,*******","*******,*******",comment for fa1,100,*******,FALSE,*******,FALSE,client1,MAC_ADDRESS,fahost1,TRUE,TRUE,*******,100,TRUE,fadomain1,fa1,TRUE,clienthost1

Header-MacFilterAddress,comment,parent,is_registered_user,guest_custom_field4,guest_custom_field2,guest_custom_field3,guest_custom_field1,expire_time,guest_middle_name,never_expires,guest_email,registered_user,guest_first_name,guest_last_name,mac_address
MacFilterAddress,comment,macfilter1,FALSE,field4,field2,field3,field1,2010-09-16T18:40:00Z,middle,FALSE,<EMAIL>,user1,first,last,00:01:02:03:04:05
