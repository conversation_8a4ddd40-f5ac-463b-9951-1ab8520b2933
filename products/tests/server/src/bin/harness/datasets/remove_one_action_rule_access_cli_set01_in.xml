<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="755f81c30c63427e9fa480ebe6059352"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="admin-group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="93da7ad557764c50a475df7ed3e07944"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="cloud-api-only"/>
  <PROPERTY NAME="comment" VALUE="Admins allowed to perform API request on Cloud API"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="736014a726224796ad38181513d0fd1c"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="cloud-api-superuser"/>
  <PROPERTY NAME="comment" VALUE="Admins allowed to perform API request on Cloud API with superuser permissions"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="a13624ae2aa64dab93ebe1949f6f5ae5"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="saml-group"/>
  <PROPERTY NAME="comment" VALUE="Admins allowed to perform authentication request through SAML"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="ac8e935c011440eba8f496f699ad01ae"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="splunk-reporting-group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="755f81c30c63427e9fa480ebe6059353"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="test_group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="755f81c30c63427e9fa480ebe6059354"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="super_test_group"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="action" VALUE="access_cli"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>

</DATABASE>
