# dhcpd conf file for lease_abandoned.sh
local-address MY-ADDRESS;
server-identifier MY-ADDRESS;
ddns-update-style interim;
not authoritative;
default-lease-time 10;
min-lease-time 10;
max-lease-time 10;
ping-check false;
log-facility daemon;

ddns-updates off;
ignore client-updates;

failover peer "f1"
{
  MY-ROLE;
  address MY-ADDRESS;
  port 519;
  peer address PEER-ADDRESS;
  peer port 519;
  max-response-delay 60;
  max-unacked-updates 10;
  MCLT
  SPLIT
}

# Allocate leases from the loopback network
subnet ********* netmask ********* {
	pool {
                failover peer "f1";
                deny dynamic bootp clients;
                infoblox-range ********* ***********;
                range ********* ***********;
        }
}
