#ignore unknown-clients;
omapi-key DHCP_UPDATER;
omapi-port 7911;
ignore bootp;
ddns-updates DDNS-UPDATES;
ddns-update-style interim;
DDNS-CONFIG
update-static-leases true;
option domain-name "ZONE-NAME";
option domain-name-servers DOMAIN-NS;
#always-broadcast true;
local-address MY-ADDRESS;
server-identifier MY-ADDRESS;
authoritative;
max-lease-time LEASE-TIME;
default-lease-time LEASE-TIME;
ping-check false;
infoblox-ignore-uid IGNORE-UID;
infoblox-ignore-macaddr IGNORE-MACADDR;

# Include a generated key-file (slightly closer to the "real thing")
include "WORKING-DIR/dhcp_updater.key";

MY-NETWORKS

# Where to send the DNS updates
zone "ZONE-NAME." {
    primary DOMAIN-NS;
    key DHCP_UPDATER;
}

zone "127.in-addr.arpa." {
    primary DOMAIN-NS;
    key DHCP_UPDATER;
}
