destination d_internal_1 {  udp ( "*******" port(514) localip("0.0.0.0") template("<$PRI>$DATE [VIRTUAL_IP] $MSGHDR$MSG\n") template_escape(no) persist-name("dinternal1")); };
destination d_internal_2 {  tcp ( "*******" port(2222) localip("0.0.0.0") template("<$PRI>$DATE [VIRTUAL_IP] $MSGHDR$MSG\n") template_escape(no) persist-name("dinternal2")); };

log { source(s_syslogng); filter(f_debug); destination(d_internal_1); };
log { source(s_syslogng); filter(f_err); destination(d_internal_2); };
