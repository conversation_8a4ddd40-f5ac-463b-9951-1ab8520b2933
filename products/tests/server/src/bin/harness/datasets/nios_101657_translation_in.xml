<DATABASE NAME="onedb" VERSION="MDXMLTEST">
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cdiscovery_task"/>
      <PROPERTY NAME="name" VALUE="cdiscovery_task"/>
      <PROPERTY NAME="member" VALUE="0"/>
      <PROPERTY NAME="driver_type" VALUE="GCP"/>
      <PROPERTY NAME="service_account_file" VALUE="ibcloudteam.json"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cdiscovery_task"/>
      <PROPERTY NAME="name" VALUE="aws_cdiscovery_task"/>
      <PROPERTY NAME="member" VALUE="1"/>
      <PROPERTY NAME="driver_type" VALUE="AWS"/>
      <PROPERTY NAME="service_account_file" VALUE="ibcloudteam.json"/>
  </OBJECT>
</DATABASE>
