<MDXML>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.virtual_node"
            POST-STRUCT-CALLBACK="save_virtual_node_objects_and_keep_db">
    </STRUCTURE-TRANSFORM>
    <POST-PROCESSING PROCESS-FUNCTION="insert_cloud_dns_sync_properties"/>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.aws_rte53_task_group"  POST-STRUCT-CALLBACK="save_aws_task_grps_and_keep_db">
       <NEW-MEMBER MEMBER-NAME="sync_child_accounts" VALUE="false"/>
       <NEW-MEMBER MEMBER-NAME="role_arn" VALUE=""/>
    </STRUCTURE-TRANSFORM>
</MDXML>
