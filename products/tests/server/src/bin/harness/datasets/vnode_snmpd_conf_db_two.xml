<DATABASE NAME="onedb" VERSION="3.0.0-ivan-test-R1">
<OBJECT>
  <PROPERTY NAME="cluster_parent" VALUE="."/>
  <PROPERTY NAME="virtual_node_count" VALUE="0"/>
  <PROPERTY NAME="shared_secret" VALUE="test"/>
  <PROPERTY NAME="cluster_oid" VALUE="0"/>
  <PROPERTY NAME="physical_node_count" VALUE="0"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster"/>
  <PROPERTY NAME="name" VALUE="Infoblox"/>
</OBJECT>
<OBJECT><PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node_parent"/><PROPERTY NAME="cluster" VALUE="0"/></OBJECT>
<OBJECT>
  <PROPERTY NAME="ha_enabled" VALUE="0"/>
  <PROPERTY NAME="virtual_ip" VALUE="***********"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="subnet_mask" VALUE="*************"/>
  <PROPERTY NAME="is_master" VALUE="true"/>
  <PROPERTY NAME="gateway" VALUE="***********"/>
  <PROPERTY NAME="virtual_oid" VALUE="0"/>
  <PROPERTY NAME="host_name" VALUE="master.infoblox.com"/>
  <PROPERTY NAME="member_type" VALUE="INFOBLOX"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="enable_auto_lan" VALUE="true"/>
  <PROPERTY NAME="physical_oid" VALUE="0"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
  <PROPERTY NAME="public_ip_address" VALUE="***********"/>
  <PROPERTY NAME="mgmt_port_enabled" VALUE="false"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="position" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="password" VALUE="infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin"/>
  <PROPERTY NAME="name" VALUE="admin"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="notify_sys_admin" VALUE="false"/>
  <PROPERTY NAME="community_string" VALUE="snmp test community string2"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.vnode_monitor"/>
  <PROPERTY NAME="enable_snmp" VALUE="true"/>
  <PROPERTY NAME="enable_snmp_get" VALUE="true"/>
  <PROPERTY NAME="community_str_for_get" VALUE="snmp test community string2"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="enable_syslog_server" VALUE="false"/>
  <PROPERTY NAME="override_snmp_settings" VALUE="true"/>
  <PROPERTY NAME="override_syslog_settings" VALUE="false"/>
  <PROPERTY NAME="administrator" VALUE="Infoblox Admin2"/>
  <PROPERTY NAME="email_address" VALUE="<EMAIL>"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="address" VALUE="*******"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.vnode_trap_receiver"/>
  <PROPERTY NAME="vnode_monitor" VALUE="0"/>
  <PROPERTY NAME="position" VALUE="2"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="address" VALUE="*******"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.vnode_trap_receiver"/>
  <PROPERTY NAME="vnode_monitor" VALUE="0"/>
  <PROPERTY NAME="position" VALUE="1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="address" VALUE="*******"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.vnode_trap_receiver"/>
  <PROPERTY NAME="vnode_monitor" VALUE="0"/>
  <PROPERTY NAME="position" VALUE="0"/>
</OBJECT>
</DATABASE>
