<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.dhcp_option_82_filter"/>
<PROPERTY NAME="cluster_dhcp_properties" VALUE="0"/>
<PROPERTY NAME="name" VALUE="MATCH_CID"/>
<PROPERTY NAME="is_circuit_id" VALUE="Matches_Value"/>
<PROPERTY NAME="circuit_id_name" VALUE="1E:54"/>
<PROPERTY NAME="circuit_id_is_substring" VALUE="false"/>
<PROPERTY NAME="is_remote_id" VALUE="Any"/>
<PROPERTY NAME="remote_id_is_substring" VALUE="false"/>
<PROPERTY NAME="comment" VALUE="Comment"/>
</OBJECT>
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.dhcp_option_82_filter"/>
<PROPERTY NAME="cluster_dhcp_properties" VALUE="0"/>
<PROPERTY NAME="name" VALUE="MATCH_RID"/>
<PROPERTY NAME="is_circuit_id" VALUE="Not_Set"/>
<PROPERTY NAME="circuit_id_is_substring" VALUE="false"/>
<PROPERTY NAME="is_remote_id" VALUE="Matches_Value"/>
<PROPERTY NAME="remote_id_name" VALUE="2E:67"/>
<PROPERTY NAME="remote_id_is_substring" VALUE="false"/>
<PROPERTY NAME="comment" VALUE="Comment"/>
</OBJECT>
</DATABASE>