<DATABASE NAME="onedb" VERSION="CURRENT_RELEASE">

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.member_delete_struct"/>
  <PROPERTY NAME="prop1" VALUE="val11"/>
  <PROPERTY NAME="prop2" VALUE="val12"/>
  <PROPERTY NAME="prop3" VALUE="val13"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.member_delete_struct"/>
  <PROPERTY NAME="prop1" VALUE="val21"/>
  <PROPERTY NAME="prop2" VALUE="val22"/>
  <PROPERTY NAME="prop3" VALUE="val23"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.member_delete_struct"/>
  <PROPERTY NAME="prop1" VALUE="val31"/>
  <PROPERTY NAME="prop2" VALUE="val32"/>
  <PROPERTY NAME="prop3" VALUE="val33"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.member_delete_struct"/>
  <PROPERTY NAME="prop1" VALUE="val41"/>
  <PROPERTY NAME="prop2" VALUE="val42"/>
  <PROPERTY NAME="prop3" VALUE="val43"/>
</OBJECT>

<!-- member metadata objects -->
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.member_metadata_test"/>
  <PROPERTY NAME="name" VALUE="name1"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.member_metadata_test"/>
  <PROPERTY NAME="name" VALUE="name2"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.member_metadata_test"/>
  <PROPERTY NAME="name" VALUE="name3"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.member_metadata_test"/>
  <PROPERTY NAME="name" VALUE="name4"/>
</OBJECT>

</DATABASE>
