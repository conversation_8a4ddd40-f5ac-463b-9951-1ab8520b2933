"Header-Network","address*","netmask*","network_view","comment","auto_create_reversezone","is_authoritative","boot_file","boot_server","ddns_domainname","generate_hostname","always_update_dns","update_static_leases","ddns_ttl","enable_option81","deny_bootp","disabled","enable_ddns","enable_thresholds","enable_threshold_email_warnings","enable_threshold_snmp_warnings","range_high_water_mark","range_high_water_mark_reset","ignore_client_requested_options","range_low_water_mark","range_low_water_mark_reset","next_server","pxe_lease_time","recycle_leases","threshold_email_addresses","dhcp_members","routers","update_dns_on_lease_renewal"
"Network","*******","*********","default"," IPv4 network0",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,<PERSON>LS<PERSON>,FALSE,FALSE,TRUE,TRUE,FALSE,80,70,TRUE,10,20,"blue.domain.com",1100,FALSE,"<EMAIL>,<EMAIL>","infoblox.localdomain","*******,*******","TRUE"

