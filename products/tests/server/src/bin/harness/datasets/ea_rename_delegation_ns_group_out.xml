<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
<PROPERTY NAME="attribute_type" VALUE="STRING"/>
<PROPERTY NAME="allowed_object_types" VALUE="Network"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
<PROPERTY NAME="name" VALUE="ea1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="allowed_object_types" VALUE="IPv6Network,NetworkView,HostRecord,View,IPv6DhcpRange,IPv6FixedAddress,Network,FixedAddress,ResourceRecord,DhcpRange,BaseZone"/>
<PROPERTY NAME="group_flag" VALUE="false"/>
<PROPERTY NAME="name" VALUE="ea2"/>
<PROPERTY NAME="uuid" VALUE="28490d9efb754b0fa2eca791bf2ff892"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
<PROPERTY NAME="ea_namespace" VALUE="CLOUD"/>
<PROPERTY NAME="gog_revision" VALUE="12"/>
<PROPERTY NAME="attribute_type" VALUE="STRING"/>
<PROPERTY NAME="flags" VALUE="CR"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="allowed_object_types" VALUE="IPv6Network,NetworkView,HostRecord,View,IPv6DhcpRange,IPv6FixedAddress,NsGroup,Network,FixedAddress,ResourceRecord,DhcpRange,BaseZone"/>
<PROPERTY NAME="group_flag" VALUE="false"/>
<PROPERTY NAME="name" VALUE="ea3"/>
<PROPERTY NAME="uuid" VALUE="28490d9efb754b0fa2eca791bf2ff893"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
<PROPERTY NAME="ea_namespace" VALUE="CLOUD"/>
<PROPERTY NAME="gog_revision" VALUE="12"/>
<PROPERTY NAME="attribute_type" VALUE="STRING"/>
<PROPERTY NAME="flags" VALUE="CR"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="allowed_object_types" VALUE="NsGroup"/>
<PROPERTY NAME="group_flag" VALUE="false"/>
<PROPERTY NAME="name" VALUE="Tenant ID3"/>
<PROPERTY NAME="uuid" VALUE="28490d9efb754b0fa2eca791bf2ff894"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
<PROPERTY NAME="ea_namespace" VALUE="CLOUD"/>
<PROPERTY NAME="gog_revision" VALUE="12"/>
<PROPERTY NAME="attribute_type" VALUE="STRING"/>
<PROPERTY NAME="flags" VALUE="CR"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="ea_namespace" VALUE="default"/>
<PROPERTY NAME="gog_revision" VALUE="12"/>
<PROPERTY NAME="attribute_type" VALUE="STRING"/>
<PROPERTY NAME="allowed_object_types" VALUE="IPv6Network,NsGroup,Network"/>
<PROPERTY NAME="group_flag" VALUE="false"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
<PROPERTY NAME="uuid" VALUE="45bfc1a5633f4ec1aaf523bcbf09fb3a"/>
<PROPERTY NAME="name" VALUE="City"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="ea_namespace" VALUE="default"/>
<PROPERTY NAME="gog_revision" VALUE="13"/>
<PROPERTY NAME="attribute_type" VALUE="STRING"/>
<PROPERTY NAME="allowed_object_types" VALUE="IPv6Network,NsGroup,Network"/>
<PROPERTY NAME="group_flag" VALUE="false"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
<PROPERTY NAME="uuid" VALUE="a08f07ea54e34ec5865384dc1bc04f0f"/>
<PROPERTY NAME="name" VALUE="Region"/>
</OBJECT>

</DATABASE>
