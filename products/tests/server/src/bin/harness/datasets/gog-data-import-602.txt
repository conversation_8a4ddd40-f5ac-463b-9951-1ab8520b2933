db_import;db;full;;2011:1
<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
    <SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
        <ib:ObjectReadResponse>
            <total_size>1</total_size>
            <objects>
                <item xsi:type="ib:SubGrid">
                    <name>DataSetSubGrid</name>
                    <uuid>420590412bbf4601a626fe8007c9c108</uuid>
                </item>
            </objects>
        </ib:ObjectReadResponse>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridMember">
					<ha_enabled>false</ha_enabled>
					<host_name>infoblox.localdomain</host_name>
					<is_master>true</is_master>
					<master_candidate_enabled>true</master_candidate_enabled>
					<virtual_ip>********</virtual_ip>
					<virtual_oid>0</virtual_oid>
					<uuid>6453a51badde25f058b564770f556057</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridMember">
					<comment>Sub grid member object 00</comment>
					<ha_enabled>false</ha_enabled>
					<host_name>foo-00.com</host_name>
					<is_master>false</is_master>
					<master_candidate_enabled>false</master_candidate_enabled>
					<virtual_ip>********</virtual_ip>
					<virtual_oid>1</virtual_oid>
					<uuid>da646f16b127cb3d38f10c50966b0f41</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridMember">
					<comment>Sub grid member object 01</comment>
					<ha_enabled>false</ha_enabled>
					<host_name>foo-01.com</host_name>
					<is_master>false</is_master>
					<master_candidate_enabled>false</master_candidate_enabled>
					<virtual_ip>********</virtual_ip>
					<virtual_oid>2</virtual_oid>
					<uuid>16b1733713017843cb68a24ceaf8cf1f</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<name>EAD1</name>
					<comment>Comment for EAD 1</comment>
					<attribute_type>STRING</attribute_type>
					<flags>V</flags>
					<allowed_object_types>
						<item>Network</item>
					</allowed_object_types>
					<uuid>b559d9f036801112c67fc1fd3e492a8d</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<name>EAD2</name>
					<comment>Comment for EAD 2</comment>
					<attribute_type>ENUM</attribute_type>
					<flags>MV</flags>
					<enum_values>
						<item>
							<enum_value>EnumValue1</enum_value>
						</item>
						<item>
							<enum_value>EnumValue2</enum_value>
						</item>
						<item>
							<enum_value>EnumValue3</enum_value>
						</item>
						<item>
							<enum_value>EnumValue4</enum_value>
						</item>
					</enum_values>
					<uuid>13e3bd9b8075d8b642dd1e2ac5737704</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<name>EAD3</name>
					<comment>Comment for EAD 3</comment>
					<attribute_type>DATE</attribute_type>
					<uuid>9f66da3ff3336364d3f2e782387482e2</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?><SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
    <SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
        <ib:ObjectReadResponse>
            <total_size>1</total_size>
            <objects>
                <item xsi:type="ib:SubGridNetworkViewParent">
                    <uuid>1d4490acb465d21237d30eac9718d05e</uuid>
                </item>
            </objects>
        </ib:ObjectReadResponse>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<name>default</name>
					<id>0</id>
					<uuid>21f8ba875c53fe3a04ed900280540861</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<comment>Sub grid network View 00</comment>
					<name>netview-00</name>
					<id>1</id>
					<uuid>2779dfd3e012a5f617c23e1205082568</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<comment>Sub grid network View 01</comment>
					<name>netview-01</name>
					<id>2</id>
					<uuid>8b7e5f708d33ccb674d1fd8501f03281</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetworkView">
					<comment>Sub grid network View ext_attrs</comment>
					<name>netview-ext-attrs</name>
					<id>3</id>
					<uuid>72bd845df9ba4898c80b9063170066ce</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>***********</address>
					<cidr>16</cidr>
					<comment>Sub grid network container 00</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>2779dfd3e012a5f617c23e1205082568</sg_network_view>
					<uuid>3e4ce7f171663fb5fee2e5e090618971</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>***********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 01</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>2779dfd3e012a5f617c23e1205082568</sg_network_view>
					<uuid>4693f55ecb2b6da5757239e425d2ed14</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>***********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 02</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>2779dfd3e012a5f617c23e1205082568</sg_network_view>
					<uuid>0698628613d360d342326cd73755b8c0</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>***********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 00</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>8b7e5f708d33ccb674d1fd8501f03281</sg_network_view>
					<uuid>1903269d4ca639cd43540407be2fbc7b</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>***********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 01</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>8b7e5f708d33ccb674d1fd8501f03281</sg_network_view>
					<uuid>a8fc5885b698f43d6c74022e99cda4cd</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>***********</address>
					<cidr>24</cidr>
					<comment>Sub grid network container 02</comment>
					<is_container>true</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>8b7e5f708d33ccb674d1fd8501f03281</sg_network_view>
					<uuid>862e2cac33b4e91fb1b5e8e6e5682266</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:0000:0001::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 00</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<sg_network_view>2779dfd3e012a5f617c23e1205082568</sg_network_view>
					<uuid>d5862b8e8b02dd8db578119d8b189da2</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:0000:0002::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 01</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<sg_network_view>2779dfd3e012a5f617c23e1205082568</sg_network_view>
					<uuid>8c26851849e005dd2e2ee07e0f6def20</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:0000:0003::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 02</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<sg_network_view>2779dfd3e012a5f617c23e1205082568</sg_network_view>
					<uuid>48f0d1523b824b73f344264d50c8bdc0</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:0000:0004::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 00</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<sg_network_view>8b7e5f708d33ccb674d1fd8501f03281</sg_network_view>
					<uuid>a94d7da2edb8f6ddc31e8c4dfbe2edd5</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:0000:0005::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 01</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<sg_network_view>8b7e5f708d33ccb674d1fd8501f03281</sg_network_view>
					<uuid>3886c649718b4b35fafd94c4a2d1410d</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>dead:beef:0000:0006::</address>
					<cidr>64</cidr>
					<comment>Sub grid IPv6 network container 02</comment>
					<is_container>true</is_container>
					<is_ipv4>false</is_ipv4>
					<sg_network_view>8b7e5f708d33ccb674d1fd8501f03281</sg_network_view>
					<uuid>92f7f352b145683321c32c6864671f2a</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>10.0.0.0</address>
					<cidr>16</cidr>
					<comment>Sub grid network 00</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>2779dfd3e012a5f617c23e1205082568</sg_network_view>
					<uuid>9f882a89a00cbf15c87b350a952a6cdd</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 01</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>2779dfd3e012a5f617c23e1205082568</sg_network_view>
					<uuid>42df1559c57726dd0b01899469034fd3</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 02</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>2779dfd3e012a5f617c23e1205082568</sg_network_view>
					<uuid>a0166a17807fa37cd8ca2c1d4e24c816</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 00</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>8b7e5f708d33ccb674d1fd8501f03281</sg_network_view>
					<uuid>299e214011b714c1e5e2a825a50cae03</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 01</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>8b7e5f708d33ccb674d1fd8501f03281</sg_network_view>
					<uuid>9626c341734c29da83185f2e512dc697</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>********</address>
					<cidr>16</cidr>
					<comment>Sub grid network 02</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>8b7e5f708d33ccb674d1fd8501f03281</sg_network_view>
					<uuid>ae12f30ffde2da8f3a74029f53ada786</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:SubGridNetwork">
					<address>*********</address>
					<cidr>16</cidr>
					<comment>Sub grid network EA</comment>
					<is_container>false</is_container>
					<is_ipv4>true</is_ipv4>
					<sg_network_view>8b7e5f708d33ccb674d1fd8501f03281</sg_network_view>
					<uuid>c8659b5a08984fa174f13599c9bfb1bc</uuid>
					<extensible_attributes>
						<item>
							<uuid>13e3bd9b8075d8b642dd1e2ac5737704</uuid>
							<values>
								<item>
									<value_enum>EnumValue3</value_enum>
								</item>
								<item>
									<value_enum>EnumValue1</value_enum>
								</item>
							</values>
						</item>
						<item>
							<uuid>9f66da3ff3336364d3f2e782387482e2</uuid>
							<values>
								<item>
									<value_date>2011-03-17T11:01:00Z</value_date>
								</item>
							</values>
						</item>
					</extensible_attributes>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
        <ib:ObjectReadResponse>
            <total_size>1</total_size>
            <objects>
                <item xsi:type="ib:SubGridMember">
                    <ha_enabled>false</ha_enabled>
                    <host_name>member.ea</host_name>
                    <is_master>true</is_master>
                    <master_candidate_enabled>true</master_candidate_enabled>
                    <virtual_ip>********</virtual_ip>
                    <virtual_oid>75</virtual_oid>
					<uuid>ea85165cab05f0aa535b0f868935e8d0</uuid>
					<extensible_attributes>
						<item>
							<uuid>9f66da3ff3336364d3f2e782387482e2</uuid>
							<values>
								<item>
									<value_date>2011-03-17T11:01:00Z</value_date>
								</item>
							</values>
						</item>
					</extensible_attributes>
                </item>
            </objects>
        </ib:ObjectReadResponse>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
        <ib:ObjectReadResponse>
            <total_size>1</total_size>
            <objects>
                <item xsi:type="ib:AdminGroup">
                    <name>Sub Admin</name>
                    <disabled>true</disabled>
                    <comment>Slightly less admin than a Super Admin</comment>
                    <superuser>false</superuser>
                    <uuid>1413eefe038179d0ac2b0b7348209a8a</uuid>
					<extensible_attributes>
						<item>
							<uuid>9f66da3ff3336364d3f2e782387482e2</uuid>
							<values>
								<item>
									<value_date>2011-03-17T11:01:00Z</value_date>
								</item>
							</values>
						</item>
					</extensible_attributes>
					<access_rights>
						<item>
						    <data>c8659b5a08984fa174f13599c9bfb1bc</data>
						    <sub_type></sub_type>
						    <synthetic_data></synthetic_data>
						    <comment>RW on network *********/16</comment>
						    <allow_read>true</allow_read>
						    <allow_modify>true</allow_modify>
						    <allow_delete>true</allow_delete>
						    <allow_create>true</allow_create>
						</item>
						<item>
						    <data>ae12f30ffde2da8f3a74029f53ada786</data>
						    <sub_type></sub_type>
						    <synthetic_data></synthetic_data>
						    <comment>RO on network ********/16</comment>
						    <allow_read>true</allow_read>
						    <allow_modify>false</allow_modify>
						    <allow_delete>false</allow_delete>
						    <allow_create>false</allow_create>
						</item>
					</access_rights>
                </item>
            </objects>
        </ib:ObjectReadResponse>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>


