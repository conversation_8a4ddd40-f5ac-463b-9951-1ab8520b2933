header-dhcprange,end_address*,_new_end_address,start_address*,_new_start_address,always_update_dns,boot_file,boot_server,broadcast_address,comment,ddns_domainname,deny_all_clients,deny_bootp,disabled,domain_name,domain_name_servers,enable_ddns,enable_pxe_lease_time,enable_threshold_email_warnings,enable_threshold_snmp_warnings,enable_thresholds,exclusion_ranges,failover_association,fingerprint_filter_rules,generate_hostname,ignore_client_requested_options,known_clients_option,lease_time,mac_filter_rules,member,ms_server,_new_ms_server,nac_filter_rules,name,network_view,next_server,option_filter_rules,pxe_lease_time,range_high_water_mark,range_high_water_mark_reset,range_low_water_mark,range_low_water_mark_reset,recycle_leases,relay_agent_filter_rules,routers,server_association_type,threshold_email_addresses,unknown_clients_option,update_dns_on_lease_renewal
dhcprange,*********,,********,,False,,,,,,False,,False,,,,,False,False,,,fo_check,,,,,691200,,,*******,,,,default,,,,95,85,0,10,,,,MS_FAILOVER,,,
