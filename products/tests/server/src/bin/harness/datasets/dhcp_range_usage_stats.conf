# dhcpd conf file for dhcp_range_usage_stats.sh
local-address 127.0.0.1;
server-identifier 127.0.0.1;
ddns-update-style interim;
not authoritative;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
log-facility daemon;
ping-check false;

ddns-updates off;
ignore client-updates;

subnet ********* netmask ********* {
        pool {
                infoblox-range ********** **********;
                range ********** **********;
                range ********** **********;
        }
        host fixed-host-1 {
                dynamic;
                fixed-address **********;
                hardware ethernet 18:18:18:18:18:18;
        }
        host fixed-host-2 {
                dynamic;
                fixed-address **********;
                hardware ethernet 99:99:99:99:99:99;
        }
        host fixed-host-3 {
                dynamic;
                fixed-address **********;
                hardware ethernet 20:20:20:20:20:20;
        }
}
