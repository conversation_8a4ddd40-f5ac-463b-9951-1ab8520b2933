options {
	directory "/storage/tmp/sec-stub-zone";	// Working directory
	pid-file "named.pid-sec-stub-zone";		// Put pid file in working dir
	recursion no;				// Do not provide recursive service
	allow-transfer { any; };
	listen-on port 53 { *********; };
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 954
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	channel import_syslog {
		syslog daemon;
		severity debug;
	};
	category default { import_syslog; };
};

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	file "sec-stub-zone-localhost.db";
	notify no;
};

zone "test-sec1.com." {
	type master;
	file "sec-stub-zone.test-sec1.com.db";
	notify no;
};

zone "test sec2.com." {
	type master;
	file "sec-stub-zone.test-sec2.com.db";
	notify no;
};
zone "test-stub1.com." {
	type master;
	file "sec-stub-zone.test-stub1.com.db";
	notify no;
};

zone "168.1.192.in-addr.arpa." {
	type master;
	file "sec-stub-zone.168.1.192.db";
	notify no;
};


zone "1/************.in-addr.arpa." {
	type master;
	file "sec-stub-zone.1_************.db";
	notify no;
};

zone "0/***********.in-addr.arpa." {
	type master;
	file "db.0#***********.in-addr.arpa._default";
	notify no;
};

zone "169.1.192.in-addr.arpa." {
	type master;
	file "sec-stub-zone.169.1.192.db";
	notify no;
};

zone "0.0.0.0.b.0.a.*******.*******.0.ip6.arpa." {
	type master;
	file "sec-stub-zone.0000.b0a0.4030.2010.db";
	notify no;
};


zone "0.0.0.0.d.0.c.0.0.0.0.0.b.0.a.*******.*******.0.ip6.arpa." {
	type master;
	file "sec-stub-zone.0000.d0c0.0000.b0a0.4030.2010.db";
	notify no;
};

zone "test-sec3.com." {
	type master;
	file "sec-stub-zone.test-sec3.com.db";
	notify no;
};

zone "test-unsupported.com." {
	type master;
	file "sec-stub-zone.test-unsupported.com.db";
	notify no;
};

zone "test-paging.com." {
	type master;
	file "sec-stub-zone.test-paging.com.db";
	notify no;
};

zone "test-bug45594.com." {
       type master;
       file "sec-stub-zone.test-bug45594.com.db";
       notify no;
};

zone "nios-62275.com." {
       type master;
       file "sec-stub-zone.nios-62275.com.db";
       notify no;
};
