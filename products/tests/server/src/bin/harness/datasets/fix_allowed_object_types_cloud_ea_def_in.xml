<DATABASE NAME="onedb" VERSION="MDXMLTEST">

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="uuid" VALUE="dd60e67079de49a1b14f217c5cbb31a6"/>
  <PROPERTY NAME="gog_revision" VALUE="12"/>
  <PROPERTY NAME="group_flag" VALUE="false"/>
  <PROPERTY NAME="ea_namespace" VALUE="CLOUD"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="Tenant ID"/>
  <PROPERTY NAME="flags" VALUE="CR"/>
  <PROPERTY NAME="allowed_object_types" VALUE="NetworkView,Network,DhcpRange,Fi<PERSON><PERSON><PERSON>ress,IPv6Network,IPv6DhcpRang<PERSON>,IPv6FixedAddress,View,BaseZone,HostRecord,ResourceRecord"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="uuid" VALUE="2be2d5d8b37f4495b63681c31b9a690e"/>
  <PROPERTY NAME="gog_revision" VALUE="12"/>
  <PROPERTY NAME="group_flag" VALUE="false"/>
  <PROPERTY NAME="ea_namespace" VALUE="CLOUD"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="VPC ID"/>
  <PROPERTY NAME="flags" VALUE="CR"/>
  <PROPERTY NAME="allowed_object_types" VALUE="NetworkContainer"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="uuid" VALUE="e051625a318147a7a1b98e009ef2529b"/>
  <PROPERTY NAME="gog_revision" VALUE="12"/>
  <PROPERTY NAME="group_flag" VALUE="false"/>
  <PROPERTY NAME="ea_namespace" VALUE="CLOUD"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="VPC Name"/>
  <PROPERTY NAME="flags" VALUE="C"/>
  <PROPERTY NAME="allowed_object_types" VALUE="NetworkContainer"/>
</OBJECT>

</DATABASE>