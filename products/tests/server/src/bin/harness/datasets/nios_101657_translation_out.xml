<DATABASE NAME="onedb" VERSION="MDXMLTEST">
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cdiscovery_task"/>
    <PROPERTY NAME="name" VALUE="cdiscovery_task"/>
    <PROPERTY NAME="member" VALUE="0"/>
    <PROPERTY NAME="driver_type" VALUE="GCP"/>
    <PROPERTY NAME="service_account_file" VALUE="ibcloudteam.json"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cdiscovery_task"/>
    <PROPERTY NAME="name" VALUE="aws_cdiscovery_task"/>
    <PROPERTY NAME="member" VALUE="1"/>
    <PROPERTY NAME="driver_type" VALUE="AWS"/>
    <PROPERTY NAME="service_account_file" VALUE="ibcloudteam.json"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.gcp_credentials"/>
    <PROPERTY NAME="parent_task" VALUE="cdiscovery_task"/>
    <PROPERTY NAME="type" VALUE="service_account"/>
    <PROPERTY NAME="project_id" VALUE="ib-cloudteam-qa-178213"/>
    <PROPERTY NAME="private_key_id" VALUE="b857e3cbd58288e60c907d053a186859c2cc08f2"/>
    <PROPERTY NAME="private_key" VALUE="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"/>
    <PROPERTY NAME="client_email" VALUE="<EMAIL>"/>
    <PROPERTY NAME="client_id" VALUE="108002720182340350311"/>
    <PROPERTY NAME="file_name" VALUE="ibcloudteam.json"/>
    <PROPERTY NAME="auth_uri" VALUE="https://accounts.google.com/o/oauth2/auth"/>
    <PROPERTY NAME="token_uri" VALUE="https://oauth2.googleapis.com/token"/>
    <PROPERTY NAME="auth_provider_x509_cert_url" VALUE="https://www.googleapis.com/oauth2/v1/certs"/>
    <PROPERTY NAME="client_x509_cert_url" VALUE="https://www.googleapis.com/robot/v1/metadata/x509/for-gcp-vdiscovery-2%40ib-cloudteam-qa-178213.iam.gserviceaccount.com"/>
  </OBJECT>
</DATABASE>
