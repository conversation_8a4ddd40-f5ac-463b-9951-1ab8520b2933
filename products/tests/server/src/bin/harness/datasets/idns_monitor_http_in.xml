<DATABASE NAME="onedb" VERSION="7.3.0-Alpha-leonid-2016-03-01-16-16" MD5="51c70078456e23ce003c8f67bbdc5829" SCHEMA-MD5="ba036ee0cb98bf787ac1b497f35f9743" INT-VERSION="7.3.6000-999999">
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="1"/>
        <PROPERTY NAME="request" VALUE="GET /"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="2"/>
        <PROPERTY NAME="request" VALUE="GET /&#xd;&#xa;&#xd;&#xa;"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="3"/>
        <PROPERTY NAME="request" VALUE="GET / HTTP/1.1"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="4"/>
        <PROPERTY NAME="request" VALUE="GET / HTTP/1.1&#xd;&#xa;&#xd;&#xa;"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="5"/>
        <PROPERTY NAME="request" VALUE="GET / HTTP/1.0 HTTP/1.1&#xd;&#xa;&#xd;&#xa;"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="6"/>
        <PROPERTY NAME="request" VALUE="GET HTTP/1.1"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="7"/>
        <PROPERTY NAME="request" VALUE="GET / HTTP/1.1&#xa;X-Magic-Header: Connection: close"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="8"/>
        <PROPERTY NAME="request" VALUE="GET / HTTP/1.1&#xa;&#xa;Connection: close"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="9"/>
        <PROPERTY NAME="request" VALUE="GET / HTTP/1.1&#xa;Connection: keap-alive"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="10"/>
        <PROPERTY NAME="request" VALUE="&#xa;GET /"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="11"/>
        <PROPERTY NAME="request" VALUE="POST /"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="12"/>
        <PROPERTY NAME="request" VALUE="HEAD /"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.idns_monitor_http"/>
        <PROPERTY NAME="name" VALUE="13"/>
        <PROPERTY NAME="request" VALUE="GET /logoutcomplete.html  HTTP/1.1\r\nHost: alphaiso.socgen.com\r\nConnection: Close\r\n\r\n"/>
    </OBJECT>
</DATABASE>
