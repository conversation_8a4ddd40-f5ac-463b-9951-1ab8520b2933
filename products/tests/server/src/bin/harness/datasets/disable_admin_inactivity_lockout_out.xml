<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
<PROPERTY NAME="name" VALUE="test_group"/>
<PROPERTY NAME="superuser" VALUE="false"/>
<PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="false"/>
<PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
<PROPERTY NAME="inactive_days" VALUE="30"/>
<PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
<PROPERTY NAME="name" VALUE="super_test_group"/>
<PROPERTY NAME="superuser" VALUE="true"/>
<PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
<PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
<PROPERTY NAME="inactive_days" VALUE="30"/>
<PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
</OBJECT>
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
<PROPERTY NAME="name" VALUE="cloud-api-only"/>
<PROPERTY NAME="superuser" VALUE="true"/>
<PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
<PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
<PROPERTY NAME="inactive_days" VALUE="30"/>
<PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
<PROPERTY NAME="sub_grid" VALUE="."/>
</OBJECT>
</DATABASE>
