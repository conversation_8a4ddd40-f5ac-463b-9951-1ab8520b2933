; We use this file to test reverse zone import with an alternate bulk host ip format.
; The bulk ip format for this test is "[#x#x#x#]".
; Use various case mixes in the names that will become bulk hosts, in order to prove
; that the case doesn't affect the import result.
*************.in-addr.arpa. 13333 IN	PTR	Bulk[192x168x002x175].Bulk.Infoblox.COM.
*************.in-addr.arpa. 13333 IN	PTR	bUlk[192x168x002x176].BULK.infobloX.com.
*************.in-addr.arpa. 13333 IN	PTR	buLK[192x168x002x177].bULk.infoblox.cOm.
*************.in-addr.arpa. 13333 IN	PTR	BULK[192x168x002x178].bulk.infoblox.coM.
2.168.192.in-addr.arpa.	86400	IN	NS	dummy.bulk.infoblox.com.
2.168.192.in-addr.arpa.	86400	IN	SOA	dummy.bulk.infoblox.com. root.test.infoblox.com. 2004031101 43200 1800 604800 259200
2.168.192.in-addr.arpa.	86400	IN	SOA	dummy.bulk.infoblox.com. root.test.infoblox.com. 2004031101 43200 1800 604800 259200
