@version: 3.35
@include "scl.conf"

# Syslog-ng configuration file, compatible with default Debian syslogd
# installation.

# First, set some global options.
# First, set some global options.
options { chain_hostnames(off); flush_lines(0); use_dns(no); use_fqdn(yes); keep-hostname(yes);
      dns_cache(no); owner("root"); group("adm"); perm(0640);
      stats_freq(0); bad_hostname("^gconfd$");
};

########################
# Sources
########################
# This is the default behavior of sysklogd package
# Logs may come from unix stream, but not from another machine.
#
source s_syslogng {
      file ("/proc/kmsg" program_override("kernel: "));
      unix-stream ("/run/systemd/journal/syslog" max-connections(200));
};

source s_internal {
       internal();
  };

template t_filetmpl { template("$ISODATE $FACILITY $HOST $PROGRAM[$PID]: $LEVEL $MSGONLY\n"); template_escape(no); };

########################
# Destinations
########################
# First some standard logfile
destination d_messages { file("/var/log/syslog" template(t_filetmpl)); };
destination d_support_mesg { file("/var/log/support_syslog" template(t_filetmpl));};

########################
# Filters
########################
# Here's come the filter options. With this rules, we can set which
# message go where.
filter f_debug    { level(debug..emerg); };
filter f_info     { level(info..emerg); };
filter f_notice   { level(notice..emerg); };
filter f_warning  { level(warning..emerg); };
filter f_err      { level(err..emerg); };
filter f_crit     { level(crit..emerg); };
filter f_alert    { level(alert..emerg); };
filter f_emerg    { level(emerg); };
filter f_safenet  { not ((program(vtl) or program(httpd) or program(named)) and level(info) and message('SSL Error is (5 : 32 )')); };
filter f_support  { (not program(dhcpd) and not program(named)) or level(notice..emerg); };

########################
# Log paths
########################
log { source(s_syslogng); source(s_internal); filter(f_safenet); destination(d_messages); };

log { source(s_syslogng); source (s_internal);
        filter(f_support);
        destination(d_support_mesg); };

###
# Include all config files in /etc/syslog-ng/conf.d/
###
@include "/etc/syslog-ng/conf.d/*.conf"
