<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
	<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
	<PROPERTY NAME="is_ipv4" VALUE="false"/>
	<PROPERTY NAME="space" VALUE="DHCPv6..false"/>
	<PROPERTY NAME="globally_enabled" VALUE="true"/>
	<PROPERTY NAME="code" VALUE="18"/>
	<PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
	<PROPERTY NAME="type" VALUE="string"/>
	<PROPERTY NAME="name" VALUE="dhcp6.interface-id"/>
</OBJECT>
<OBJECT>
	<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
	<PROPERTY NAME="is_ipv4" VALUE="false"/>
	<PROPERTY NAME="space" VALUE="DHCPv6..false"/>
	<PROPERTY NAME="globally_enabled" VALUE="true"/>
	<PROPERTY NAME="code" VALUE="37"/>
	<PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
	<PROPERTY NAME="type" VALUE="string"/>
	<PROPERTY NAME="name" VALUE="dhcp6.remote-id"/>
</OBJECT>
<OBJECT>
	<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
	<PROPERTY NAME="is_ipv4" VALUE="false"/>
	<PROPERTY NAME="space" VALUE="DHCPv6..false"/>
	<PROPERTY NAME="globally_enabled" VALUE="true"/>
	<PROPERTY NAME="code" VALUE="38"/>
	<PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
	<PROPERTY NAME="type" VALUE="string"/>
	<PROPERTY NAME="name" VALUE="dhcp6.subscriber-id"/>
</OBJECT>
<OBJECT>
	<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
	<PROPERTY NAME="is_ipv4" VALUE="false"/>
	<PROPERTY NAME="space" VALUE="DHCPv6..false"/>
	<PROPERTY NAME="globally_enabled" VALUE="true"/>
	<PROPERTY NAME="code" VALUE="40"/>
	<PROPERTY NAME="option_space_type" VALUE="custom-dhcp"/>
	<PROPERTY NAME="type" VALUE="string"/>
	<PROPERTY NAME="name" VALUE="dhcp6.foo"/>
</OBJECT>
</DATABASE>

