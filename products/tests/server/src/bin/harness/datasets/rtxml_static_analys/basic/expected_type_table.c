/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */
#include "infoblox/auto/type_table.h"
#include "infoblox/util.h"

typedef struct __type_table_entry_t
{
  ib_uint32_t m_id;
  struct __type_table_entry_t * m_next;
} type_table_entry_t;

static type_table_entry_t * g_type_table_table[13];
static const char * g_type_table_array[2];

ib_return_t
type_table_id_to_string(type_table_id_t a_id,
			   const char ** a_pstr,
			   ib_bool_t * a_found)
{
  IENTER;

  ITEST(a_pstr, IERR_ARG);
  ITEST(a_found, IERR_ARG);

  if (a_id < 2)
    {
      *a_pstr = g_type_table_array[a_id];
      *a_found = TRUE;
    }
  else
    {
      *a_found = FALSE;
    }

 cleanup:
  ILEAVE;
}

ib_return_t
type_table_string_to_id(const char * a_string,
			   type_table_id_t * a_id,
			   ib_bool_t * a_found)
{
  IENTER;
  ib_uint32_t hashval = 0;

  ITEST(a_string, IERR_ARG);
  ITEST(a_id, IERR_ARG);
  ITEST(a_found, IERR_ARG);

  *a_found = FALSE;

  ICALL(iutil_hash_function(a_string, strlen(a_string), &hashval));
  hashval = hashval % 13;

  if (g_type_table_table[hashval] != NULL)
    {
      type_table_entry_t * entry = g_type_table_table[hashval];
      while (1)
	{
	  if (strcmp(g_type_table_array[entry->m_id], a_string) == 0)
	    {
	      *a_id = entry->m_id;
	      *a_found = TRUE;
	      break;
	    }
	  else if (entry->m_next == NULL)
	    {
	      break;
	    }
	  else
	    {
	      entry = entry->m_next;
	    }
	}
    }

 cleanup:
  ILEAVE;
}

/*
 * Auto-generated data follows
 */

/* ###################################################################### */
/* this is the array that maps id number to string value */
/* ###################################################################### */
static const char * g_type_table_array[2] = 
  {
    ".com.infoblox.one.mock_type",  // 9
    ".com.infoblox.one.virtual_node" // 12
  };
/* ###################################################################### */
/* statically declared hash table entries */
/* ###################################################################### */
static type_table_entry_t e0 = { m_id:0, m_next:NULL };
static type_table_entry_t e1 = { m_id:1, m_next:NULL };
/* ###################################################################### */
/* this is hash table used to map string value to id */
/* ###################################################################### */
static type_table_entry_t * g_type_table_table[13] = 
  {
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, &e0, NULL, NULL, 
    &e1
  };
/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */
