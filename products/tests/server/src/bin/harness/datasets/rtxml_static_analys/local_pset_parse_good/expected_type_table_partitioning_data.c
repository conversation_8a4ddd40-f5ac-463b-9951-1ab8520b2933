/**************************************************************************
 *
 * This file was AUTOMATICALLY GENERATED 
 * by the Java program 'MetaRepartition'.
 *
 * You probably DON'T want to make any edits here. THEY WILL BE LOST.
 *
 **************************************************************************/

/*
 * Copyright (C) 2006-2010 Infoblox, Inc.  All Rights Reserved.
 */
#include "infoblox/auto/type_table_partitioning_data.h"
#include "infoblox/util.h"
#include "../onedb/partition_private.h"

static const onedb_rtxml_ref_t g_empty = { 0, NULL, NULL };
static const type_table_id_t g_dependents_empty[] = { TYPE_unknown };
static const onedb_pset_definition_t g_psets_empty[] = { { NULL, g_dependents_empty } };
static const onedb_pset_action_t g_epilogue_empty[] = { { ONEDB_PARTITION_ACTION_INVALID, NULL } };
static const onedb_type_partitioning_data_t g_type_table_partition_data_array[11];

ib_return_t
type_table_id_to_partitioning_data(type_table_id_t a_id,
                                   const onedb_type_partitioning_data_t **a_data,
                                   ib_bool_t *a_found)
{
    IENTER;
    if( a_id < 11 ) {
        *a_data = &g_type_table_partition_data_array[a_id];
        *a_found = TRUE;
    } else {
        *a_found = FALSE;
    }
cleanup:
    ILEAVE;
}

ib_return_t
type_table_string_to_partitioning_data(const char *a_type,
                                       const onedb_type_partitioning_data_t **a_data,
                                       ib_bool_t *a_found)
{
    IENTER;
    type_table_id_t type_id;
    ICALL( type_table_string_to_id(a_type, &type_id, a_found) );
    if( *a_found ) {
        ICALL( type_table_id_to_partitioning_data(type_id, a_data, a_found) );
    }
cleanup:
    ILEAVE;
}

const char **
type_table_get_explicit_types(void)
{
    static const char *types[] =
    {
        ".com.infoblox.one.mock_type_duplicate_refs",
        ".com.infoblox.one.mock_type_one",
        ".com.infoblox.one.mock_type_reverse_sorted_1",
        ".com.infoblox.one.mock_type_reverse_sorted_2",
        ".com.infoblox.one.mock_type_reverse_sorted_3",
        ".com.infoblox.one.mock_type_three",
        NULL,
    };
    return types;
}

static const onedb_local_pset_action_definition_t fwd_pset_uses_com_infoblox_one_mock_type_duplicate_refs_ref[] = { { "pset_2", TYPE_unknown, NULL }, { "pset_3", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t rev_pset_inherits_com_infoblox_one_mock_type_duplicate_refs_ref[] = { { "pset_three_2", TYPE_unknown, NULL }, { "pset_three_1", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_inherits_com_infoblox_one_mock_type_duplicate_refs_ref[] = { { "pset_2", TYPE_unknown, NULL }, { "pset_3", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_saves_com_infoblox_one_mock_type_duplicate_refs_grid_member[] = { { "pset_three_2", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_saves_com_infoblox_one_mock_type_duplicate_refs_ref[] = { { "pset_three_1", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_pset_action_t pset_epilogue_com_infoblox_one_mock_type_five_repart_only[] = { { ONEDB_PARTITION_ACTION_EXTEND, "type__one__mock_type_one" }, { ONEDB_PARTITION_ACTION_INVALID, NULL } };
static const onedb_pset_action_t pset_epilogue_com_infoblox_one_mock_type_four_extended[] = { { ONEDB_PARTITION_ACTION_EXTEND, "type__one__mock_type_one" }, { ONEDB_PARTITION_ACTION_INVALID, NULL } };
static const onedb_local_pset_action_definition_t fwd_pset_saves_com_infoblox_one_mock_type_one_grid_member[] = { { "pset_1", TYPE_unknown, NULL }, { "pset_2", TYPE_unknown, NULL }, { "pset_3", TYPE_unknown, NULL }, { "pset_4", TYPE_unknown, NULL }, { "pset_5", TYPE_unknown, pset_5_cond_cb }, { "pset_unqualified", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t rev_pset_inherits_com_infoblox_one_mock_type_reverse_sorted_1_ref1[] = { { "pset_rev_sort_1", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_saves_com_infoblox_one_mock_type_reverse_sorted_1_ref2[] = { { "pset_rev_sort_1", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t rev_pset_uses_com_infoblox_one_mock_type_reverse_sorted_2_ref1[] = { { "pset_rev_sort_2", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_saves_com_infoblox_one_mock_type_reverse_sorted_2_ref2[] = { { "pset_rev_sort_2", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t rev_pset_saves_com_infoblox_one_mock_type_reverse_sorted_3_ref1[] = { { "pset_rev_sort_3", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_saves_com_infoblox_one_mock_type_reverse_sorted_3_ref1[] = { { "pset_rev_sort_3", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_uses_com_infoblox_one_mock_type_three_multi[] = { { "pset_2", TYPE_com_infoblox_one_mock_type_one, NULL }, { "pset_3", TYPE_com_infoblox_one_mock_type_one, NULL }, { "pset_unqualified", TYPE_unknown, NULL }, { "gridwide", TYPE_com_infoblox_one_mock_type_two, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_inherits_com_infoblox_one_mock_type_three_multi[] = { { "pset_2", TYPE_unknown, NULL }, { "pset_3", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t rev_pset_inherits_com_infoblox_one_mock_type_three_multi[] = { { "pset_three_2", TYPE_unknown, NULL }, { "pset_three_1", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_saves_com_infoblox_one_mock_type_three_grid_member[] = { { "pset_three_2", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_saves_com_infoblox_one_mock_type_three_multi[] = { { "pset_three_1", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_uses_com_infoblox_one_mock_type_two_parent[] = { { "pset_4", TYPE_unknown, NULL }, { "pset_1", TYPE_unknown, NULL }, { "pset_5", TYPE_unknown, NULL }, { "default", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const onedb_local_pset_action_definition_t fwd_pset_inherits_com_infoblox_one_mock_type_two_parent[] = { { "pset_1", TYPE_unknown, NULL }, { "pset_2", TYPE_unknown, NULL }, { "pset_3", TYPE_unknown, NULL }, { "pset_4", TYPE_unknown, NULL }, { NULL, TYPE_unknown, NULL} };
static const type_table_id_t pset_deps_type__one__mock_type_one[] = { TYPE_com_infoblox_one_mock_type_five_repart_only, TYPE_com_infoblox_one_mock_type_four_extended, TYPE_unknown };
static const onedb_pset_definition_t psets_com_infoblox_one_mock_type_one[] = {  { "type__one__mock_type_one", pset_deps_type__one__mock_type_one },  { NULL, g_dependents_empty } };
static const onedb_rtxml_ref_t fwd_com_infoblox_one_mock_type_duplicate_refs[] = { { TYPE_com_infoblox_one_mock_type_one, ".com.infoblox.one.mock_type_one", "ref", FALSE, FALSE, FALSE, FALSE, fwd_pset_saves_com_infoblox_one_mock_type_duplicate_refs_ref, fwd_pset_uses_com_infoblox_one_mock_type_duplicate_refs_ref, fwd_pset_inherits_com_infoblox_one_mock_type_duplicate_refs_ref }, { TYPE_com_infoblox_one_mock_type_one, ".com.infoblox.one.mock_type_one", "ref_duplicate", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { TYPE_com_infoblox_one_virtual_node, ".com.infoblox.one.virtual_node", "grid_member", FALSE, FALSE, FALSE, FALSE, fwd_pset_saves_com_infoblox_one_mock_type_duplicate_refs_grid_member, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t fwd_com_infoblox_one_mock_type_five_repart_only[] = { { TYPE_com_infoblox_one_mock_type_three, ".com.infoblox.one.mock_type_three", "parent", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t fwd_com_infoblox_one_mock_type_four_extended[] = { { TYPE_com_infoblox_one_mock_type_three, ".com.infoblox.one.mock_type_three", "parent", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t fwd_com_infoblox_one_mock_type_one[] = { { TYPE_com_infoblox_one_virtual_node, ".com.infoblox.one.virtual_node", "grid_member", FALSE, FALSE, FALSE, FALSE, fwd_pset_saves_com_infoblox_one_mock_type_one_grid_member, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t rev_com_infoblox_one_mock_type_one[] = { { TYPE_com_infoblox_one_mock_type_reverse_sorted_3, ".com.infoblox.one.mock_type_reverse_sorted_3", "ref1", FALSE, FALSE, FALSE, FALSE, rev_pset_saves_com_infoblox_one_mock_type_reverse_sorted_3_ref1, NULL, NULL }, { TYPE_com_infoblox_one_mock_type_duplicate_refs, ".com.infoblox.one.mock_type_duplicate_refs", "ref", FALSE, FALSE, FALSE, FALSE, NULL, NULL, rev_pset_inherits_com_infoblox_one_mock_type_duplicate_refs_ref }, { TYPE_com_infoblox_one_mock_type_duplicate_refs, ".com.infoblox.one.mock_type_duplicate_refs", "ref_duplicate", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { TYPE_com_infoblox_one_mock_type_reverse_sorted_1, ".com.infoblox.one.mock_type_reverse_sorted_1", "ref1", FALSE, FALSE, FALSE, FALSE, NULL, NULL, rev_pset_inherits_com_infoblox_one_mock_type_reverse_sorted_1_ref1 }, { TYPE_com_infoblox_one_mock_type_three, ".com.infoblox.one.mock_type_three", "multi", TRUE, FALSE, FALSE, FALSE, NULL, NULL, rev_pset_inherits_com_infoblox_one_mock_type_three_multi }, { TYPE_com_infoblox_one_mock_type_reverse_sorted_2, ".com.infoblox.one.mock_type_reverse_sorted_2", "ref1", FALSE, FALSE, FALSE, FALSE, NULL, rev_pset_uses_com_infoblox_one_mock_type_reverse_sorted_2_ref1, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t imp_com_infoblox_one_mock_type_one[] = { { TYPE_com_infoblox_one_mock_type_two, ".com.infoblox.one.mock_type_two", "parent", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t fwd_com_infoblox_one_mock_type_reverse_sorted_1[] = { { TYPE_com_infoblox_one_mock_type_two, ".com.infoblox.one.mock_type_two", "ref2", FALSE, FALSE, FALSE, FALSE, fwd_pset_saves_com_infoblox_one_mock_type_reverse_sorted_1_ref2, NULL, NULL }, { TYPE_com_infoblox_one_mock_type_one, ".com.infoblox.one.mock_type_one", "ref1", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t fwd_com_infoblox_one_mock_type_reverse_sorted_2[] = { { TYPE_com_infoblox_one_mock_type_two, ".com.infoblox.one.mock_type_two", "ref2", FALSE, FALSE, FALSE, FALSE, fwd_pset_saves_com_infoblox_one_mock_type_reverse_sorted_2_ref2, NULL, NULL }, { TYPE_com_infoblox_one_mock_type_one, ".com.infoblox.one.mock_type_one", "ref1", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t fwd_com_infoblox_one_mock_type_reverse_sorted_3[] = { { TYPE_com_infoblox_one_mock_type_one, ".com.infoblox.one.mock_type_one", "ref1", FALSE, FALSE, FALSE, FALSE, fwd_pset_saves_com_infoblox_one_mock_type_reverse_sorted_3_ref1, NULL, NULL }, { TYPE_com_infoblox_one_mock_type_two, ".com.infoblox.one.mock_type_two", "ref2", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t fwd_com_infoblox_one_mock_type_three[] = { { TYPE_MULTI_REF, TYPE_MULTI_REF_STR, "multi", TRUE, FALSE, FALSE, FALSE, fwd_pset_saves_com_infoblox_one_mock_type_three_multi, fwd_pset_uses_com_infoblox_one_mock_type_three_multi, fwd_pset_inherits_com_infoblox_one_mock_type_three_multi }, { TYPE_com_infoblox_one_virtual_node, ".com.infoblox.one.virtual_node", "grid_member", FALSE, FALSE, FALSE, FALSE, fwd_pset_saves_com_infoblox_one_mock_type_three_grid_member, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t imp_com_infoblox_one_mock_type_three[] = { { TYPE_com_infoblox_one_mock_type_five_repart_only, ".com.infoblox.one.mock_type_five_repart_only", "parent", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { TYPE_com_infoblox_one_mock_type_four_extended, ".com.infoblox.one.mock_type_four_extended", "parent", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t fwd_com_infoblox_one_mock_type_two[] = { { TYPE_com_infoblox_one_mock_type_one, ".com.infoblox.one.mock_type_one", "parent", FALSE, FALSE, FALSE, FALSE, NULL, fwd_pset_uses_com_infoblox_one_mock_type_two_parent, fwd_pset_inherits_com_infoblox_one_mock_type_two_parent }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t rev_com_infoblox_one_mock_type_two[] = { { TYPE_com_infoblox_one_mock_type_three, ".com.infoblox.one.mock_type_three", "multi", TRUE, FALSE, FALSE, FALSE, NULL, NULL, rev_pset_inherits_com_infoblox_one_mock_type_three_multi }, { TYPE_com_infoblox_one_mock_type_reverse_sorted_1, ".com.infoblox.one.mock_type_reverse_sorted_1", "ref2", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { TYPE_com_infoblox_one_mock_type_reverse_sorted_2, ".com.infoblox.one.mock_type_reverse_sorted_2", "ref2", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { TYPE_com_infoblox_one_mock_type_reverse_sorted_3, ".com.infoblox.one.mock_type_reverse_sorted_3", "ref2", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t rev_com_infoblox_one_virtual_node[] = { { TYPE_com_infoblox_one_mock_type_duplicate_refs, ".com.infoblox.one.mock_type_duplicate_refs", "grid_member", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { TYPE_com_infoblox_one_mock_type_one, ".com.infoblox.one.mock_type_one", "grid_member", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { TYPE_com_infoblox_one_mock_type_three, ".com.infoblox.one.mock_type_three", "grid_member", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };

static const onedb_type_partitioning_data_t g_type_table_partition_data_array[11] = 
{
  { ONEDB_PARTITION_CLASS_DIRECT, ONEDB_PARTITION_HINT_NONE, fwd_com_infoblox_one_mock_type_duplicate_refs, &g_empty, &g_empty, NULL, NULL },
  { ONEDB_PARTITION_CLASS_IMPLICIT, ONEDB_PARTITION_HINT_NONE, fwd_com_infoblox_one_mock_type_five_repart_only, &g_empty, &g_empty, NULL, pset_epilogue_com_infoblox_one_mock_type_five_repart_only },
  { ONEDB_PARTITION_CLASS_IMPLICIT, ONEDB_PARTITION_HINT_NONE, fwd_com_infoblox_one_mock_type_four_extended, &g_empty, &g_empty, NULL, pset_epilogue_com_infoblox_one_mock_type_four_extended },
  { ONEDB_PARTITION_CLASS_DIRECT, ONEDB_PARTITION_HINT_NONE, fwd_com_infoblox_one_mock_type_one, rev_com_infoblox_one_mock_type_one, imp_com_infoblox_one_mock_type_one, psets_com_infoblox_one_mock_type_one, NULL },
  { ONEDB_PARTITION_CLASS_EXPLICIT, ONEDB_PARTITION_HINT_NONE, fwd_com_infoblox_one_mock_type_reverse_sorted_1, &g_empty, &g_empty, NULL, NULL },
  { ONEDB_PARTITION_CLASS_EXPLICIT, ONEDB_PARTITION_HINT_NONE, fwd_com_infoblox_one_mock_type_reverse_sorted_2, &g_empty, &g_empty, NULL, NULL },
  { ONEDB_PARTITION_CLASS_EXPLICIT, ONEDB_PARTITION_HINT_NONE, fwd_com_infoblox_one_mock_type_reverse_sorted_3, &g_empty, &g_empty, NULL, NULL },
  { ONEDB_PARTITION_CLASS_DIRECT, ONEDB_PARTITION_HINT_NONE, fwd_com_infoblox_one_mock_type_three, &g_empty, imp_com_infoblox_one_mock_type_three, NULL, NULL },
  { ONEDB_PARTITION_CLASS_IMPLICIT, ONEDB_PARTITION_HINT_NONE, fwd_com_infoblox_one_mock_type_two, rev_com_infoblox_one_mock_type_two, &g_empty, NULL, NULL },
  { ONEDB_PARTITION_CLASS_DIRECT, ONEDB_PARTITION_HINT_NONE, &g_empty, &g_empty, &g_empty, NULL, NULL },
  { ONEDB_PARTITION_CLASS_ROOT, ONEDB_PARTITION_HINT_NONE, &g_empty, rev_com_infoblox_one_virtual_node, &g_empty, NULL, NULL },
};
