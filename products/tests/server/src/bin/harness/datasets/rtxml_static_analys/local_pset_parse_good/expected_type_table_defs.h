/* Auto-generated. Editing is futile. */

#ifndef TYPE_header_included
#define TYPE_header_included

#define TYPE_count		11
#define TYPE_unknown		(~((type_table_id_t)0))
#define TYPE_com_infoblox_one_mock_type_duplicate_refs	0
#define TYPE_com_infoblox_one_mock_type_five_repart_only	1
#define TYPE_com_infoblox_one_mock_type_four_extended	2
#define TYPE_com_infoblox_one_mock_type_one	3
#define TYPE_com_infoblox_one_mock_type_reverse_sorted_1	4
#define TYPE_com_infoblox_one_mock_type_reverse_sorted_2	5
#define TYPE_com_infoblox_one_mock_type_reverse_sorted_3	6
#define TYPE_com_infoblox_one_mock_type_three	7
#define TYPE_com_infoblox_one_mock_type_two	8
#define TYPE_com_infoblox_one_partition_set	9
#define TYPE_com_infoblox_one_virtual_node	10

#endif /* TYPE_header_included */
