/* Auto generated.  Editing is futile. */

#include <stddef.h>
#include <infoblox/type_digest.h>


/* Default limits for integer types (32 bits) */
static const char *intlimits[] __attribute__((unused)) = {
  "-2147483648",
  "2147483647",
  NULL,
};
static const char *uintlimits[] __attribute__((unused)) = {
  "0",
  "4294967295",
  NULL,
};

/* Default limits for unsigned long long types (64 bits) */
static const char *ulonglonglimits[] __attribute__((unused)) = {
  "0",
  "18446744073709551615",
  NULL,
};

/* .com.infoblox.one.mock_type_duplicate_refs#ref */
static const char *values_0_0[] = {
  ".com.infoblox.one.mock_type_one",
  NULL,
};

/* .com.infoblox.one.mock_type_duplicate_refs#ref_duplicate */
static const char *values_0_1[] = {
  ".com.infoblox.one.mock_type_one",
  NULL,
};

/* .com.infoblox.one.mock_type_duplicate_refs#grid_member */
static const char *values_0_2[] = {
  ".com.infoblox.one.virtual_node",
  NULL,
};

/* .com.infoblox.one.mock_type_duplicate_refs */
static const member_type members_0[] = {
  { "ref", MT_REF, MT_KEY, 0, 0, NULL, 1, values_0_0, 0, NULL },
  { "ref_duplicate", MT_REF, MT_KEY, 0, 0, NULL, 1, values_0_1, 0, NULL },
  { "grid_member", MT_REF, MT_KEY, 0, 0, NULL, 1, values_0_2, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.mock_type_five_repart_only#parent */
static const char *values_1_0[] = {
  ".com.infoblox.one.mock_type_three",
  NULL,
};

/* .com.infoblox.one.mock_type_five_repart_only */
static const member_type members_1[] = {
  { "parent", MT_REF, MT_KEY, 0, 0, NULL, 1, values_1_0, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.mock_type_four_extended#parent */
static const char *values_2_0[] = {
  ".com.infoblox.one.mock_type_three",
  NULL,
};

/* .com.infoblox.one.mock_type_four_extended */
static const member_type members_2[] = {
  { "parent", MT_REF, MT_KEY, 0, 0, NULL, 1, values_2_0, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.mock_type_one#grid_member */
static const char *values_3_0[] = {
  ".com.infoblox.one.virtual_node",
  NULL,
};

/* .com.infoblox.one.mock_type_one */
static const member_type members_3[] = {
  { "grid_member", MT_REF, MT_KEY, 0, 0, NULL, 1, values_3_0, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.mock_type_reverse_sorted_1#ref1 */
static const char *values_4_0[] = {
  ".com.infoblox.one.mock_type_one",
  NULL,
};

/* .com.infoblox.one.mock_type_reverse_sorted_1#ref2 */
static const char *values_4_1[] = {
  ".com.infoblox.one.mock_type_two",
  NULL,
};

/* .com.infoblox.one.mock_type_reverse_sorted_1 */
static const member_type members_4[] = {
  { "ref1", MT_REF, MT_KEY, 0, 0, NULL, 1, values_4_0, 0, NULL },
  { "ref2", MT_REF, MT_KEY, 0, 0, NULL, 1, values_4_1, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.mock_type_reverse_sorted_2#ref1 */
static const char *values_5_0[] = {
  ".com.infoblox.one.mock_type_one",
  NULL,
};

/* .com.infoblox.one.mock_type_reverse_sorted_2#ref2 */
static const char *values_5_1[] = {
  ".com.infoblox.one.mock_type_two",
  NULL,
};

/* .com.infoblox.one.mock_type_reverse_sorted_2 */
static const member_type members_5[] = {
  { "ref1", MT_REF, MT_KEY, 0, 0, NULL, 1, values_5_0, 0, NULL },
  { "ref2", MT_REF, MT_KEY, 0, 0, NULL, 1, values_5_1, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.mock_type_reverse_sorted_3#ref1 */
static const char *values_6_0[] = {
  ".com.infoblox.one.mock_type_one",
  NULL,
};

/* .com.infoblox.one.mock_type_reverse_sorted_3#ref2 */
static const char *values_6_1[] = {
  ".com.infoblox.one.mock_type_two",
  NULL,
};

/* .com.infoblox.one.mock_type_reverse_sorted_3 */
static const member_type members_6[] = {
  { "ref1", MT_REF, MT_KEY, 0, 0, NULL, 1, values_6_0, 0, NULL },
  { "ref2", MT_REF, MT_KEY, 0, 0, NULL, 1, values_6_1, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.mock_type_three#multi */
static const char *values_7_0[] = {
  ".com.infoblox.one.mock_type_one",
  ".com.infoblox.one.mock_type_two",
  NULL,
};

/* .com.infoblox.one.mock_type_three#grid_member */
static const char *values_7_1[] = {
  ".com.infoblox.one.virtual_node",
  NULL,
};

/* .com.infoblox.one.mock_type_three */
static const member_type members_7[] = {
  { "multi", MT_REF, MT_KEY, 0, 0, NULL, 2, values_7_0, 0, NULL },
  { "grid_member", MT_REF, MT_KEY, 0, 0, NULL, 1, values_7_1, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.mock_type_two#parent */
static const char *values_8_0[] = {
  ".com.infoblox.one.mock_type_one",
  NULL,
};

/* .com.infoblox.one.mock_type_two */
static const member_type members_8[] = {
  { "parent", MT_REF, MT_KEY, 0, 0, NULL, 1, values_8_0, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.partition_set */
static const member_type members_9[] = {
  { "name", MT_STRING, MT_KEY, 0, 0, NULL, 0, NULL, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.virtual_node#virtual_oid */
static const char *syntaxes_10_0[] = {
  "text",
  NULL,
};

/* .com.infoblox.one.virtual_node */
static const member_type members_10[] = {
  { "virtual_oid", MT_STRING, MT_KEY, 0, 16, NULL, 0, NULL, 1, syntaxes_10_0 },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

static const member_type *member_types[] = {
  members_0,
  members_1,
  members_2,
  members_3,
  members_4,
  members_5,
  members_6,
  members_7,
  members_8,
  members_9,
  members_10,
};
#define NTYPES (sizeof(member_types)/sizeof(member_types[0]))

static const char *syntax_names[] = {
  "text",
  NULL
};
#define NSYNTAXES (sizeof(syntax_names) / sizeof(syntax_names[0]) - 1)

const member_type *
ib_get_digested_member_types(unsigned type_id)
{
    if (type_id < NTYPES)
        return member_types[type_id];
    return NULL;
}

/* Will be indexed with member_type_t */
static const char *member_names[] = {
    "unknown",
    "str",
    "bool",
    "ref",
    "enum",
    "int",
    "uint",
};

const char *
ib_get_digested_type_name(member_type_t type_id)
{
    if (type_id > MT_UNKNOWN && type_id < MT_TYPE_COUNT)
        return member_names[type_id];
    return NULL;
}

const char **
ib_get_digested_syntax_names(int *count)
{
    if (count != NULL)
        *count = NSYNTAXES;
    return syntax_names;
}
