/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */

#include "../onedb/rec.h"
#include "../onedb/sqlengine.h"
/* field record array for .com.infoblox.one.mock_type_duplicate_refs */
static onedb_rs_field_record_t fr0[] = 
{
  /* .com.infoblox.one.mock_type_duplicate_refs#bitmap */
  { typeid:0, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_duplicate_refs#pset_2 */
  { typeid:0, fieldid:1, field_name:"pset_2", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 589, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_duplicate_refs#pset_3 */
  { typeid:0, fieldid:2, field_name:"pset_3", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 590, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_duplicate_refs#pset_three_1 */
  { typeid:0, fieldid:3, field_name:"pset_three_1", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1219, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_duplicate_refs#pset_three_2 */
  { typeid:0, fieldid:4, field_name:"pset_three_2", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1220, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_duplicate_refs#ref */
  { typeid:0, fieldid:5, field_name:"ref", field_type:ONEDB_RS_VARBOB, field_sum: 317, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_one", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_duplicate_refs#ref_duplicate */
  { typeid:0, fieldid:6, field_name:"ref_duplicate", field_type:ONEDB_RS_VARBOB, field_sum: 1367, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_one", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_duplicate_refs#grid_member */
  { typeid:0, fieldid:7, field_name:"grid_member", field_type:ONEDB_RS_VARBOB, field_sum: 1149, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.virtual_node", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_duplicate_refs#__key */
  { typeid:0, fieldid:8, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.mock_type_duplicate_refs */
static int fdns0[] = 
{
-1
};
/* field record array for .com.infoblox.one.mock_type_five_repart_only */
static onedb_rs_field_record_t fr1[] = 
{
  /* .com.infoblox.one.mock_type_five_repart_only#bitmap */
  { typeid:1, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_five_repart_only#parent */
  { typeid:1, fieldid:1, field_name:"parent", field_type:ONEDB_RS_VARBOB, field_sum: 650, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_three", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_five_repart_only#__key */
  { typeid:1, fieldid:2, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.mock_type_five_repart_only */
static int fdns1[] = 
{
-1
};
/* field record array for .com.infoblox.one.mock_type_four_extended */
static onedb_rs_field_record_t fr2[] = 
{
  /* .com.infoblox.one.mock_type_four_extended#bitmap */
  { typeid:2, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_four_extended#parent */
  { typeid:2, fieldid:1, field_name:"parent", field_type:ONEDB_RS_VARBOB, field_sum: 650, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_three", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_four_extended#__key */
  { typeid:2, fieldid:2, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.mock_type_four_extended */
static int fdns2[] = 
{
-1
};
/* field record array for .com.infoblox.one.mock_type_one */
static onedb_rs_field_record_t fr3[] = 
{
  /* .com.infoblox.one.mock_type_one#bitmap */
  { typeid:3, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#pset_1 */
  { typeid:3, fieldid:1, field_name:"pset_1", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 588, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#pset_2 */
  { typeid:3, fieldid:2, field_name:"pset_2", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 589, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#pset_3 */
  { typeid:3, fieldid:3, field_name:"pset_3", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 590, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#pset_4 */
  { typeid:3, fieldid:4, field_name:"pset_4", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 591, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#pset_5 */
  { typeid:3, fieldid:5, field_name:"pset_5", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 592, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#pset_rev_sort_1 */
  { typeid:3, fieldid:6, field_name:"pset_rev_sort_1", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1567, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#pset_rev_sort_3 */
  { typeid:3, fieldid:7, field_name:"pset_rev_sort_3", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1569, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#pset_three_1 */
  { typeid:3, fieldid:8, field_name:"pset_three_1", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1219, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#pset_three_2 */
  { typeid:3, fieldid:9, field_name:"pset_three_2", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1220, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#pset_unqualified */
  { typeid:3, fieldid:10, field_name:"pset_unqualified", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1714, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_one#grid_member */
  { typeid:3, fieldid:11, field_name:"grid_member", field_type:ONEDB_RS_VARBOB, field_sum: 1149, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.virtual_node", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_one#__key */
  { typeid:3, fieldid:12, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.mock_type_one */
static int fdns3[] = 
{
-1
};
/* field record array for .com.infoblox.one.mock_type_reverse_sorted_1 */
static onedb_rs_field_record_t fr4[] = 
{
  /* .com.infoblox.one.mock_type_reverse_sorted_1#bitmap */
  { typeid:4, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_1#pset_rev_sort_1 */
  { typeid:4, fieldid:1, field_name:"pset_rev_sort_1", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1567, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_1#ref1 */
  { typeid:4, fieldid:2, field_name:"ref1", field_type:ONEDB_RS_VARBOB, field_sum: 366, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_one", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_1#ref2 */
  { typeid:4, fieldid:3, field_name:"ref2", field_type:ONEDB_RS_VARBOB, field_sum: 367, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_two", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_1#__key */
  { typeid:4, fieldid:4, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.mock_type_reverse_sorted_1 */
static int fdns4[] = 
{
-1
};
/* field record array for .com.infoblox.one.mock_type_reverse_sorted_2 */
static onedb_rs_field_record_t fr5[] = 
{
  /* .com.infoblox.one.mock_type_reverse_sorted_2#bitmap */
  { typeid:5, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_2#pset_rev_sort_2 */
  { typeid:5, fieldid:1, field_name:"pset_rev_sort_2", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1568, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_2#ref1 */
  { typeid:5, fieldid:2, field_name:"ref1", field_type:ONEDB_RS_VARBOB, field_sum: 366, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_one", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_2#ref2 */
  { typeid:5, fieldid:3, field_name:"ref2", field_type:ONEDB_RS_VARBOB, field_sum: 367, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_two", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_2#__key */
  { typeid:5, fieldid:4, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.mock_type_reverse_sorted_2 */
static int fdns5[] = 
{
-1
};
/* field record array for .com.infoblox.one.mock_type_reverse_sorted_3 */
static onedb_rs_field_record_t fr6[] = 
{
  /* .com.infoblox.one.mock_type_reverse_sorted_3#bitmap */
  { typeid:6, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_3#pset_rev_sort_3 */
  { typeid:6, fieldid:1, field_name:"pset_rev_sort_3", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1569, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_3#ref1 */
  { typeid:6, fieldid:2, field_name:"ref1", field_type:ONEDB_RS_VARBOB, field_sum: 366, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_one", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_3#ref2 */
  { typeid:6, fieldid:3, field_name:"ref2", field_type:ONEDB_RS_VARBOB, field_sum: 367, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_two", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_reverse_sorted_3#__key */
  { typeid:6, fieldid:4, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.mock_type_reverse_sorted_3 */
static int fdns6[] = 
{
-1
};
/* field record array for .com.infoblox.one.mock_type_three */
static onedb_rs_field_record_t fr7[] = 
{
  /* .com.infoblox.one.mock_type_three#bitmap */
  { typeid:7, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_three#pset_2 */
  { typeid:7, fieldid:1, field_name:"pset_2", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 589, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_three#pset_3 */
  { typeid:7, fieldid:2, field_name:"pset_3", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 590, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_three#pset_three_1 */
  { typeid:7, fieldid:3, field_name:"pset_three_1", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1219, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_three#pset_three_2 */
  { typeid:7, fieldid:4, field_name:"pset_three_2", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1220, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_three#multi */
  { typeid:7, fieldid:5, field_name:"multi", field_type:ONEDB_RS_VARBOB, field_sum: 555, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_one .com.infoblox.one.mock_type_two", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_three#grid_member */
  { typeid:7, fieldid:6, field_name:"grid_member", field_type:ONEDB_RS_VARBOB, field_sum: 1149, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.virtual_node", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_three#__key */
  { typeid:7, fieldid:7, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.mock_type_three */
static int fdns7[] = 
{
-1
};
/* field record array for .com.infoblox.one.mock_type_two */
static onedb_rs_field_record_t fr8[] = 
{
  /* .com.infoblox.one.mock_type_two#bitmap */
  { typeid:8, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_two#pset_1 */
  { typeid:8, fieldid:1, field_name:"pset_1", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 588, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_two#pset_2 */
  { typeid:8, fieldid:2, field_name:"pset_2", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 589, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_two#pset_3 */
  { typeid:8, fieldid:3, field_name:"pset_3", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 590, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_two#pset_4 */
  { typeid:8, fieldid:4, field_name:"pset_4", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 591, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_two#pset_three_1 */
  { typeid:8, fieldid:5, field_name:"pset_three_1", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1219, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_two#pset_three_2 */
  { typeid:8, fieldid:6, field_name:"pset_three_2", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 1220, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type_two#parent */
  { typeid:8, fieldid:7, field_name:"parent", field_type:ONEDB_RS_VARBOB, field_sum: 650, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.mock_type_one", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type_two#__key */
  { typeid:8, fieldid:8, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.mock_type_two */
static int fdns8[] = 
{
-1
};
/* field record array for .com.infoblox.one.partition_set */
static onedb_rs_field_record_t fr9[] = 
{
  /* .com.infoblox.one.partition_set#bitmap */
  { typeid:9, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.partition_set#type__one__mock_type_one */
  { typeid:9, fieldid:1, field_name:"type__one__mock_type_one", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 2540, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.partition_set#name */
  { typeid:9, fieldid:2, field_name:"name", field_type:ONEDB_RS_VARBOB, field_sum: 417, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_NONE, ref_check: ONEDB_REF_CHK_NA, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: "rtxml.string", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.partition_set#__key */
  { typeid:9, fieldid:3, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.partition_set */
static int fdns9[] = 
{
-1
};
/* field record array for .com.infoblox.one.virtual_node */
static onedb_rs_field_record_t fr10[] = 
{
  /* .com.infoblox.one.virtual_node#bitmap */
  { typeid:10, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.virtual_node#virtual_oid */
  { typeid:10, fieldid:1, field_name:"virtual_oid", field_type:ONEDB_RS_VARBOB, field_sum: 1186, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_NONE, ref_check: ONEDB_REF_CHK_NA, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: "rtxml.string", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.virtual_node#__key */
  { typeid:10, fieldid:2, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.virtual_node */
static int fdns10[] = 
{
-1
};
/* field record array for .com.infoblox.dns.zone_search_index */
static onedb_rs_field_record_t fr11[] = 
{
  { typeid:11, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.dns.zone_search_index#start_cookie */
  { typeid:11, fieldid:1, field_name:"start_cookie", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.dns.zone_search_index#__key */
  { typeid:11, fieldid:2, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE}
};
  /* Global index list */
onedb_rs_index_record_t*  g_global_ss_index_array[] = { 
};
int g_num_global_ss_indexes = 0;
  /* End Global index list */
  /* .com.infoblox.one.mock_type_duplicate_refs#index list*/
  /* .com.infoblox.one.mock_type_five_repart_only#index list*/
  /* .com.infoblox.one.mock_type_four_extended#index list*/
  /* .com.infoblox.one.mock_type_one#index list*/
  /* .com.infoblox.one.mock_type_reverse_sorted_1#index list*/
  /* .com.infoblox.one.mock_type_reverse_sorted_2#index list*/
  /* .com.infoblox.one.mock_type_reverse_sorted_3#index list*/
  /* .com.infoblox.one.mock_type_three#index list*/
  /* .com.infoblox.one.mock_type_two#index list*/
  /* .com.infoblox.one.partition_set#index list*/
  /* .com.infoblox.one.virtual_node#index list*/
int g_num_types  = 11;
static onedb_rs_type_record_t st_onedb_rs_type_record_array[] = 
{
  /* .com.infoblox.one.mock_type_duplicate_refs */
  { typeid:0, is_internal:0, type_name:".com.infoblox.one.mock_type_duplicate_refs", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:9, no_of_fixed_length_fields:0, no_of_variable_length_fields:9, field_array:fr0, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns0, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.mock_type_five_repart_only */
  { typeid:1, is_internal:0, type_name:".com.infoblox.one.mock_type_five_repart_only", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:3, no_of_fixed_length_fields:0, no_of_variable_length_fields:3, field_array:fr1, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns1, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.mock_type_four_extended */
  { typeid:2, is_internal:0, type_name:".com.infoblox.one.mock_type_four_extended", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:3, no_of_fixed_length_fields:0, no_of_variable_length_fields:3, field_array:fr2, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns2, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.mock_type_one */
  { typeid:3, is_internal:0, type_name:".com.infoblox.one.mock_type_one", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:13, no_of_fixed_length_fields:0, no_of_variable_length_fields:13, field_array:fr3, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns3, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.mock_type_reverse_sorted_1 */
  { typeid:4, is_internal:0, type_name:".com.infoblox.one.mock_type_reverse_sorted_1", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:5, no_of_fixed_length_fields:0, no_of_variable_length_fields:5, field_array:fr4, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns4, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.mock_type_reverse_sorted_2 */
  { typeid:5, is_internal:0, type_name:".com.infoblox.one.mock_type_reverse_sorted_2", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:5, no_of_fixed_length_fields:0, no_of_variable_length_fields:5, field_array:fr5, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns5, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.mock_type_reverse_sorted_3 */
  { typeid:6, is_internal:0, type_name:".com.infoblox.one.mock_type_reverse_sorted_3", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:5, no_of_fixed_length_fields:0, no_of_variable_length_fields:5, field_array:fr6, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns6, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.mock_type_three */
  { typeid:7, is_internal:0, type_name:".com.infoblox.one.mock_type_three", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:8, no_of_fixed_length_fields:0, no_of_variable_length_fields:8, field_array:fr7, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns7, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.mock_type_two */
  { typeid:8, is_internal:0, type_name:".com.infoblox.one.mock_type_two", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:9, no_of_fixed_length_fields:0, no_of_variable_length_fields:9, field_array:fr8, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns8, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.partition_set */
  { typeid:9, is_internal:0, type_name:".com.infoblox.one.partition_set", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:4, no_of_fixed_length_fields:0, no_of_variable_length_fields:4, field_array:fr9, partition_type: ONEDB_TYPE_PART_YES, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns9, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.virtual_node */
  { typeid:10, is_internal:0, type_name:".com.infoblox.one.virtual_node", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:3, no_of_fixed_length_fields:0, no_of_variable_length_fields:3, field_array:fr10, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns10, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},


/*Below are fake types generated for special indexes*/
/*These are not counted towards g_num_types, since*/
/*we do not want these to be treated like regular types*/
/*fake types for cross, ip and ssindex in that order*/


  { typeid:11, is_internal:1, type_name:".com.infoblox.dns.zone_search_index", key_builder:0, recursion_end:0, key_builder_byte: ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:1, select_auth_assumed:1, no_of_fields:3, no_of_fixed_length_fields:0, no_of_variable_length_fields:3, field_array:fr11, partition_type: ONEDB_TYPE_PART_NO, no_of_indexes:0, indexes:NULL}
};
onedb_rs_type_record_t *g_onedb_rs_type_record_array = st_onedb_rs_type_record_array;
int g_num_types_plus_faketypes  = 12;
/* end of rs_type.c */
/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */

