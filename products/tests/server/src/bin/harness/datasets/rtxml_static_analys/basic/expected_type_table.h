/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */

/*
 * Copyright (C) 1999-2013 Infoblox Inc. All rights Reserved.
 */

#ifndef type_table_h_included_
#define type_table_h_included_

#include "infoblox/types.h"

typedef ib_uint32_t type_table_id_t;

extern ib_return_t
type_table_id_to_string(type_table_id_t a_id,
			   const char ** a_pstr,
			   ib_bool_t * a_found);

extern ib_return_t
type_table_string_to_id(const char * a_string,
			   type_table_id_t * a_id,
			   ib_bool_t * a_found);

#define type_table_get_id(ptr, id)					\
  do									\
    {									\
      ib_bool_t __f = FALSE;						\
      ICALL(type_table_string_to_id(ptr, id, &__f));			\
      ITEST(__f, IERR_FAILURE);						\
    }									\
  while(0)

#define type_table_get_string(id, ptr)				\
  do									\
    {									\
      ib_bool_t __f = FALSE;						\
      ICALL(type_table_id_to_string(id, ptr, &__f));			\
      ITEST(__f, IERR_FAILURE);						\
    }									\
  while(0)

#endif /* type_table_h_included_ */

/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */
