/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */

#include "../onedb/partition_private.h"
#include <infoblox/auto/type_table_defs.h>


onedb_partitioner_instruction_t g_instructions_get_effective_bitmap[] =
{
  /* mock_type */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_DIRECT,
    m_structure_id:0xffffffff,
    m_member_name:NULL,
    m_vco_only:FALSE
  },
  /* virtual_node */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_ROOT,
    m_structure_id:0xffffffff,
    m_member_name:NULL,
    m_vco_only:FALSE
  }
};
/* end of instruction_get_effective_bitmap.c */
/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */

