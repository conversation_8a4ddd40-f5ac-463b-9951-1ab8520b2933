/* Auto generated.  Editing is futile. */

#include <stddef.h>
#include <infoblox/type_digest.h>


/* Default limits for integer types (32 bits) */
static const char *intlimits[] __attribute__((unused)) = {
  "-2147483648",
  "2147483647",
  NULL,
};
static const char *uintlimits[] __attribute__((unused)) = {
  "0",
  "4294967295",
  NULL,
};

/* Default limits for unsigned long long types (64 bits) */
static const char *ulonglonglimits[] __attribute__((unused)) = {
  "0",
  "18446744073709551615",
  NULL,
};

/* .com.infoblox.one.mock_type#grid_member */
static const char *values_0_0[] = {
  ".com.infoblox.one.virtual_node",
  NULL,
};

/* .com.infoblox.one.mock_type */
static const member_type members_0[] = {
  { "grid_member", MT_REF, MT_KEY, 0, 0, NULL, 1, values_0_0, 0, NULL },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

/* .com.infoblox.one.virtual_node#virtual_oid */
static const char *syntaxes_1_0[] = {
  "text",
  NULL,
};

/* .com.infoblox.one.virtual_node */
static const member_type members_1[] = {
  { "virtual_oid", MT_STRING, MT_KEY, 0, 16, NULL, 0, NULL, 1, syntaxes_1_0 },
  { NULL, MT_UNKNOWN, 0, 0, 0, NULL, 0, NULL, 0, NULL }
};

static const member_type *member_types[] = {
  members_0,
  members_1,
};
#define NTYPES (sizeof(member_types)/sizeof(member_types[0]))

static const char *syntax_names[] = {
  "text",
  NULL
};
#define NSYNTAXES (sizeof(syntax_names) / sizeof(syntax_names[0]) - 1)

const member_type *
ib_get_digested_member_types(unsigned type_id)
{
    if (type_id < NTYPES)
        return member_types[type_id];
    return NULL;
}

/* Will be indexed with member_type_t */
static const char *member_names[] = {
    "unknown",
    "str",
    "bool",
    "ref",
    "enum",
    "int",
    "uint",
};

const char *
ib_get_digested_type_name(member_type_t type_id)
{
    if (type_id > MT_UNKNOWN && type_id < MT_TYPE_COUNT)
        return member_names[type_id];
    return NULL;
}

const char **
ib_get_digested_syntax_names(int *count)
{
    if (count != NULL)
        *count = NSYNTAXES;
    return syntax_names;
}
