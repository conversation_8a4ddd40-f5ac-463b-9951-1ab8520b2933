<?xml version="1.0"?>
<!DOCTYPE package PUBLIC "-//Infoblox//DTD RTXML XML V1.0//EN" "/infoblox/tests/datasets/rtxml.dtd">
<!-- Copyright (c) 1999-2013 Infoblox Inc. All Rights Reserved. -->
<package name=".com.infoblox.one">
  <structure name="virtual_node" count="1" structure-id="10" fixed-size-member-count="0" variable-size-member-count="1" member-count="1">
    <member name="virtual_oid" type="rtxml.string" key-type="key" tuple-value-order="1" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING">
            <syntax name="text" param="max=16" />
            <description>unique identifier of this virtual node</description>
    </member>
  </structure>
  <structure name="mock_type_one" count="1" structure-id="3" fixed-size-member-count="0" variable-size-member-count="1" member-count="1">
    <member name="grid_member" type=".com.infoblox.one.virtual_node" ref-type="deep" key-type="key" partition-save="pset_1; pset_2 forward; pset_3; pset_4 forward; pset_5 conditional-cb pset_5_cond_cb; pset_unqualified" tuple-value-order="11" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
  </structure>
  <structure name="mock_type_two" count="1" structure-id="8" fixed-size-member-count="0" variable-size-member-count="1" member-count="1">
    <member name="parent" type=".com.infoblox.one.mock_type_one" ref-type="deep" key-type="key" partition-inherit="pset_1; pset_2; pset_3; pset_4" partition-use="pset_4; pset_1; pset_5; default" tuple-value-order="7" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
  </structure>
  <structure name="mock_type_three" count="1" structure-id="7" fixed-size-member-count="0" variable-size-member-count="2" member-count="2">
    <member name="multi" type=".com.infoblox.one.mock_type_one .com.infoblox.one.mock_type_two" ref-type="deep" key-type="key" partition-save="pset_three_1" partition-inherit="pset_three_2 reverse;pset_three_1 reverse;pset_2 forward; pset_3" partition-use="pset_unqualified; type one.mock_type_one; pset_2; pset_3; type .com.infoblox.one.mock_type_two; gridwide" tuple-value-order="5" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
    <member name="grid_member" type=".com.infoblox.one.virtual_node" ref-type="deep" key-type="key" partition-save="pset_three_2" tuple-value-order="6" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
  </structure>

  <!-- Sorted reverse hop-->
  <structure name="mock_type_reverse_sorted_1" count="1" structure-id="4" fixed-size-member-count="0" variable-size-member-count="2" member-count="2">
    <member name="ref1" type=".com.infoblox.one.mock_type_one" ref-type="deep" key-type="key" partition-inherit="pset_rev_sort_1 reverse" tuple-value-order="2" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
    <member name="ref2" type=".com.infoblox.one.mock_type_two" ref-type="deep" key-type="key" partition-save="pset_rev_sort_1" tuple-value-order="3" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
  </structure>
  <structure name="mock_type_reverse_sorted_2" count="1" structure-id="5" fixed-size-member-count="0" variable-size-member-count="2" member-count="2">
    <member name="ref1" type=".com.infoblox.one.mock_type_one" ref-type="deep" key-type="key" partition-use="pset_rev_sort_2 reverse" tuple-value-order="2" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
    <member name="ref2" type=".com.infoblox.one.mock_type_two" ref-type="deep" key-type="key" partition-save="pset_rev_sort_2" tuple-value-order="3" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
  </structure>
  <structure name="mock_type_reverse_sorted_3" count="1" structure-id="6" fixed-size-member-count="0" variable-size-member-count="2" member-count="2">
    <member name="ref1" type=".com.infoblox.one.mock_type_one" ref-type="deep" key-type="key" partition-save="pset_rev_sort_3; pset_rev_sort_3 reverse" tuple-value-order="2" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
    <member name="ref2" type=".com.infoblox.one.mock_type_two" ref-type="deep" key-type="key" tuple-value-order="3" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
  </structure>


  <structure name="mock_type_four_extended" count="1" partition-extend="type one.mock_type_one" structure-id="2" fixed-size-member-count="0" variable-size-member-count="1" member-count="1">
    <member name="parent" type=".com.infoblox.one.mock_type_three" ref-type="deep" key-type="key" tuple-value-order="1" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
  </structure>

  <structure name="mock_type_five_repart_only" count="1" partition-extend="type one.mock_type_one" structure-id="1" fixed-size-member-count="0" variable-size-member-count="1" member-count="1">
    <member name="parent" type=".com.infoblox.one.mock_type_three" ref-type="deep" key-type="key" repartition-only="reverse" tuple-value-order="1" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
  </structure>

  <structure name="partition_set" count="1" partition-hint="limited" structure-id="9" fixed-size-member-count="0" variable-size-member-count="1" member-count="1">
    <member name="name" type="rtxml.string" key-type="key" tuple-value-order="2" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING">
      <description>Name of the object for easy retrieval</description>
    </member>
  </structure>

  <!-- NIOS-60295 -->
  <structure name="mock_type_duplicate_refs" count="1" structure-id="0" fixed-size-member-count="0" variable-size-member-count="3" member-count="3">
    <member name="ref" type=".com.infoblox.one.mock_type_one" ref-type="deep" key-type="key" partition-save="pset_three_1" partition-inherit="pset_three_2 reverse;pset_three_1 reverse;pset_2 forward; pset_3" partition-use="pset_2; pset_3" tuple-value-order="5" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
    <member name="ref_duplicate" type=".com.infoblox.one.mock_type_one" ref-type="deep" key-type="key" tuple-value-order="6" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
    <member name="grid_member" type=".com.infoblox.one.virtual_node" ref-type="deep" key-type="key" partition-save="pset_three_2" tuple-value-order="7" tuple-value-size="0" tuple-value-type="ONEDB_RS_VARBOB" onedb-value-type="ONEDB_VALUE_TYPE_STRING" />
  </structure>

</package>
