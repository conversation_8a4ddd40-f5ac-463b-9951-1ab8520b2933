/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */

#include "../onedb/partition_private.h"
#include <infoblox/auto/type_table_defs.h>


onedb_partitioner_instruction_t g_instructions_get_effective_bitmap[] =
{
  /* mock_type_duplicate_refs */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_DIRECT,
    m_structure_id:0xffffffff,
    m_member_name:NULL,
    m_vco_only:FALSE
  },
  /* mock_type_five_repart_only */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_IMPLICIT,
    m_structure_id:7, /* .com.infoblox.one.mock_type_three */
    m_member_name:"parent",
    m_vco_only:FALSE
  },
  /* mock_type_four_extended */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_IMPLICIT,
    m_structure_id:7, /* .com.infoblox.one.mock_type_three */
    m_member_name:"parent",
    m_vco_only:FALSE
  },
  /* mock_type_one */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_DIRECT,
    m_structure_id:0xffffffff,
    m_member_name:NULL,
    m_vco_only:FALSE
  },
  /* mock_type_reverse_sorted_1 */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_EXPLICIT,
    m_structure_id:0xffffffff,
    m_member_name:NULL,
    m_vco_only:FALSE
  },
  /* mock_type_reverse_sorted_2 */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_EXPLICIT,
    m_structure_id:0xffffffff,
    m_member_name:NULL,
    m_vco_only:FALSE
  },
  /* mock_type_reverse_sorted_3 */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_EXPLICIT,
    m_structure_id:0xffffffff,
    m_member_name:NULL,
    m_vco_only:FALSE
  },
  /* mock_type_three */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_DIRECT,
    m_structure_id:0xffffffff,
    m_member_name:NULL,
    m_vco_only:FALSE
  },
  /* mock_type_two */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_IMPLICIT,
    m_structure_id:3, /* .com.infoblox.one.mock_type_one */
    m_member_name:"parent",
    m_vco_only:FALSE
  },
  /* partition_set */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_DIRECT,
    m_structure_id:0xffffffff,
    m_member_name:NULL,
    m_vco_only:FALSE
  },
  /* virtual_node */
  {
    m_partition_class:ONEDB_PARTITION_CLASS_ROOT,
    m_structure_id:0xffffffff,
    m_member_name:NULL,
    m_vco_only:FALSE
  }
};
/* end of instruction_get_effective_bitmap.c */
/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */

