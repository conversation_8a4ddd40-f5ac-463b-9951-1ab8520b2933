<partition-hops>
  <type name=".com.infoblox.one.mock_type_duplicate_refs" partition-class="direct">
    <hop direction="fwd" member-name="ref" type=".com.infoblox.one.mock_type_one">
      <pset id="pset_2" action="use" />
      <pset id="pset_3" action="use" />
      <pset id="pset_2" action="inherit" />
      <pset id="pset_3" action="inherit" />
      <pset id="pset_three_1" action="save" />
    </hop>
    <hop direction="fwd" member-name="ref_duplicate" type=".com.infoblox.one.mock_type_one"/>
    <hop direction="fwd" member-name="grid_member" type=".com.infoblox.one.virtual_node">
      <pset id="pset_three_2" action="save" />
    </hop>
  </type>
  <type name=".com.infoblox.one.mock_type_five_repart_only" partition-class="indirect-implicit">
    <pset id="type__one__mock_type_one" action="extend"/>
    <hop direction="fwd" member-name="parent" type=".com.infoblox.one.mock_type_three"/>
  </type>
  <type name=".com.infoblox.one.mock_type_four_extended" partition-class="indirect-implicit">
    <pset id="type__one__mock_type_one" action="extend"/>
    <hop direction="fwd" member-name="parent" type=".com.infoblox.one.mock_type_three"/>
  </type>
  <type name=".com.infoblox.one.mock_type_one" partition-class="direct">
    <hop direction="fwd" member-name="grid_member" type=".com.infoblox.one.virtual_node">
      <pset id="pset_1" action="save" />
      <pset id="pset_2" action="save" />
      <pset id="pset_3" action="save" />
      <pset id="pset_4" action="save" />
      <pset id="pset_5" action="save" conditional="true"/>
      <pset id="pset_unqualified" action="save" />
    </hop>
    <hop direction="rev" member-name="ref1" type=".com.infoblox.one.mock_type_reverse_sorted_3">
      <pset id="pset_rev_sort_3" action="save" />
    </hop>
    <hop direction="rev" member-name="ref" type=".com.infoblox.one.mock_type_duplicate_refs">
      <pset id="pset_three_2" action="inherit" />
      <pset id="pset_three_1" action="inherit" />
    </hop>
    <hop direction="rev" member-name="ref_duplicate" type=".com.infoblox.one.mock_type_duplicate_refs">
      <pset id="pset_three_2" action="inherit" />
      <pset id="pset_three_1" action="inherit" />
    </hop>
    <hop direction="rev" member-name="ref1" type=".com.infoblox.one.mock_type_reverse_sorted_1">
      <pset id="pset_rev_sort_1" action="inherit" />
    </hop>
    <hop direction="rev" member-name="multi" type=".com.infoblox.one.mock_type_three" multi-ref="true">
      <pset id="pset_three_2" action="inherit" />
      <pset id="pset_three_1" action="inherit" />
    </hop>
    <hop direction="rev" member-name="ref1" type=".com.infoblox.one.mock_type_reverse_sorted_2">
      <pset id="pset_rev_sort_2" action="use" />
    </hop>
    <hop direction="imp" member-name="parent" type=".com.infoblox.one.mock_type_two"/>
  </type>
  <type name=".com.infoblox.one.mock_type_reverse_sorted_1" partition-class="indirect-explicit">
    <hop direction="fwd" member-name="ref2" type=".com.infoblox.one.mock_type_two">
      <pset id="pset_rev_sort_1" action="save" />
    </hop>
    <hop direction="fwd" member-name="ref1" type=".com.infoblox.one.mock_type_one"/>
  </type>
  <type name=".com.infoblox.one.mock_type_reverse_sorted_2" partition-class="indirect-explicit">
    <hop direction="fwd" member-name="ref2" type=".com.infoblox.one.mock_type_two">
      <pset id="pset_rev_sort_2" action="save" />
    </hop>
    <hop direction="fwd" member-name="ref1" type=".com.infoblox.one.mock_type_one"/>
  </type>
  <type name=".com.infoblox.one.mock_type_reverse_sorted_3" partition-class="indirect-explicit">
    <hop direction="fwd" member-name="ref1" type=".com.infoblox.one.mock_type_one">
      <pset id="pset_rev_sort_3" action="save" />

    </hop>
    <hop direction="fwd" member-name="ref2" type=".com.infoblox.one.mock_type_two"/>
  </type>
  <type name=".com.infoblox.one.mock_type_three" partition-class="direct">
    <hop direction="fwd" member-name="multi" type=".com.infoblox.one.mock_type_one" multi-ref="true">
      <pset id="pset_2" action="use" />
      <pset id="pset_3" action="use" />
      <pset id="pset_unqualified" action="use" />
      <pset id="pset_2" action="inherit" />
      <pset id="pset_3" action="inherit" />
      <pset id="pset_three_1" action="save" />
    </hop>
    <hop direction="fwd" member-name="multi" type=".com.infoblox.one.mock_type_two" multi-ref="true">
      <pset id="gridwide" action="use" />
      <pset id="pset_unqualified" action="use" />
      <pset id="pset_2" action="inherit" />
      <pset id="pset_3" action="inherit" />
      <pset id="pset_three_1" action="save" />
    </hop>
    <hop direction="fwd" member-name="grid_member" type=".com.infoblox.one.virtual_node">
      <pset id="pset_three_2" action="save" />
    </hop>
    <hop direction="imp" member-name="parent" type=".com.infoblox.one.mock_type_five_repart_only"/>
    <hop direction="imp" member-name="parent" type=".com.infoblox.one.mock_type_four_extended"/>
  </type>
  <type name=".com.infoblox.one.mock_type_two" partition-class="indirect-implicit">
    <hop direction="fwd" member-name="parent" type=".com.infoblox.one.mock_type_one">
      <pset id="pset_4" action="use" />
      <pset id="pset_1" action="use" />
      <pset id="pset_5" action="use" />
      <pset id="default" action="use" />
      <pset id="pset_1" action="inherit" />
      <pset id="pset_2" action="inherit" />
      <pset id="pset_3" action="inherit" />
      <pset id="pset_4" action="inherit" />
    </hop>
    <hop direction="rev" member-name="multi" type=".com.infoblox.one.mock_type_three" multi-ref="true">
      <pset id="pset_three_2" action="inherit" />
      <pset id="pset_three_1" action="inherit" />
    </hop>
    <hop direction="rev" member-name="ref2" type=".com.infoblox.one.mock_type_reverse_sorted_1"/>
    <hop direction="rev" member-name="ref2" type=".com.infoblox.one.mock_type_reverse_sorted_2"/>
    <hop direction="rev" member-name="ref2" type=".com.infoblox.one.mock_type_reverse_sorted_3"/>
  </type>
  <type name=".com.infoblox.one.partition_set" partition-class="direct">
  </type>
  <type name=".com.infoblox.one.virtual_node" partition-class="root">
    <hop direction="rev" member-name="grid_member" type=".com.infoblox.one.mock_type_duplicate_refs"/>
    <hop direction="rev" member-name="grid_member" type=".com.infoblox.one.mock_type_one"/>
    <hop direction="rev" member-name="grid_member" type=".com.infoblox.one.mock_type_three"/>
  </type>
</partition-hops>
