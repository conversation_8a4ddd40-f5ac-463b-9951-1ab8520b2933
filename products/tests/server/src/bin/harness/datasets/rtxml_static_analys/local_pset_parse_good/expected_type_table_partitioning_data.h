/**************************************************************************
 *
 * This file was AUTOMATICALLY GENERATED 
 * by the Java program 'MetaRepartition'.
 *
 * You probably DON'T want to make any edits here. THEY WILL BE LOST.
 *
 **************************************************************************/

/*
 * Copyright (C) 2006-2010 Infoblox, Inc.  All Rights Reserved.
 */
#ifndef _TYPE_TABLE_PARTITIONING_DATA_H_
#define _TYPE_TABLE_PARTITIONING_DATA_H_
#include "infoblox/types.h"
#include "infoblox/onedb_partition_bitmap.h"
#include "infoblox/auto/type_table.h"

#include "infoblox/auto/type_table_defs.h"

ib_return_t
type_table_id_to_partitioning_data(type_table_id_t a_id,
                                   const onedb_type_partitioning_data_t **a_data,
                                   ib_bool_t *a_found);

ib_return_t
type_table_string_to_partitioning_data(const char *a_type,
                                       const onedb_type_partitioning_data_t **a_data,
                                       ib_bool_t *a_found);

const char **
type_table_get_explicit_types(void);

#endif /* _TYPE_TABLE_PARTITIONING_DATA_H_ */
