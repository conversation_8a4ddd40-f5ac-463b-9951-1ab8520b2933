/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */

#include "../onedb/rec.h"
#include "../onedb/sqlengine.h"
/* field record array for .com.infoblox.one.mock_type */
static onedb_rs_field_record_t fr0[] = 
{
  /* .com.infoblox.one.mock_type#bitmap */
  { typeid:0, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.mock_type#grid_member */
  { typeid:0, fieldid:1, field_name:"grid_member", field_type:ONEDB_RS_VARBOB, field_sum: 1149, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_DEEP, ref_check: ONEDB_REF_CHK_ALL, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: ".com.infoblox.one.virtual_node", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.mock_type#__key */
  { typeid:0, fieldid:2, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.mock_type */
static int fdns0[] = 
{
-1
};
/* field record array for .com.infoblox.one.virtual_node */
static onedb_rs_field_record_t fr1[] = 
{
  /* .com.infoblox.one.virtual_node#bitmap */
  { typeid:1, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, field_sum: 637, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.one.virtual_node#virtual_oid */
  { typeid:1, fieldid:1, field_name:"virtual_oid", field_type:ONEDB_RS_VARBOB, field_sum: 1186, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0, key_type: 1, ref_type: ONEDB_REF_NONE, ref_check: ONEDB_REF_CHK_NA, no_of_conditional_refs: 0, conditional_ref_array: NULL, is_counted: 0, refered_types: "rtxml.string", auth_level_required: ONEDB_AUTH_READ_WRITE, auth_parent: 0, ignore_recursion_end: 1, ignore_for_keybuild: (const char*)0, dont_insert_for_trailing_keychar: 0, index_key_transform: ONEDB_RS_XFORM_INVALID, indirect_ssindex: 0, num_metadata_members: 0, metadata_members: NULL, metadata_of: -1, auto_uid: 0, is_uniqueid: 0, is_versionid: 0, allow_empty:FALSE, default_value: NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: FALSE, m_ro: FALSE, m_seqid: FALSE, synthetic_direct:0, synthetic_direct_func:NULL, is_dns_data:0, is_timestamp:0, is_propagate: 0, to_str_func:"null", from_str_func:"null", tombstone_meta: FALSE, create_tombstone: TRUE},
  /* .com.infoblox.one.virtual_node#__key */
  { typeid:1, fieldid:2, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, field_sum: 519, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, metadata_of:-1, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
};
/* dns-data field ids for .com.infoblox.one.virtual_node */
static int fdns1[] = 
{
-1
};
/* field record array for .com.infoblox.dns.zone_search_index */
static onedb_rs_field_record_t fr2[] = 
{
  { typeid:2, fieldid:0, field_name:"bitmap", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_PARTITION_BITMAP, null_allowed:TRUE, field_length: 0, m_string_list:NULL, m_string_list_size:0, num_metadata_members: 0 , metadata_members: NULL, metadata_of: -1 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.dns.zone_search_index#start_cookie */
  { typeid:2, fieldid:1, field_name:"start_cookie", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE},
  /* .com.infoblox.dns.zone_search_index#__key */
  { typeid:2, fieldid:2, field_name:"__key", field_type:ONEDB_RS_VARBOB, m_value_type:ONEDB_VALUE_TYPE_STRING, null_allowed:FALSE, field_length: 0, m_string_list:NULL, m_string_list_size:0 , allow_empty:FALSE, default_value:NULL, hash: ONEDB_RS_HASH_NONE, encryption: ONEDB_RS_ENC_NONE, internal_field: TRUE}
};
  /* Global index list */
onedb_rs_index_record_t*  g_global_ss_index_array[] = { 
};
int g_num_global_ss_indexes = 0;
  /* End Global index list */
  /* .com.infoblox.one.mock_type#index list*/
  /* .com.infoblox.one.virtual_node#index list*/
int g_num_types  = 2;
static onedb_rs_type_record_t st_onedb_rs_type_record_array[] = 
{
  /* .com.infoblox.one.mock_type */
  { typeid:0, is_internal:0, type_name:".com.infoblox.one.mock_type", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:3, no_of_fixed_length_fields:0, no_of_variable_length_fields:3, field_array:fr0, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns0, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},
  /* .com.infoblox.one.virtual_node */
  { typeid:1, is_internal:0, type_name:".com.infoblox.one.virtual_node", key_builder:0, recursion_end:0, key_builder_byte:ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:0, select_auth_assumed:0, no_of_fields:3, no_of_fixed_length_fields:0, no_of_variable_length_fields:3, field_array:fr1, partition_type: ONEDB_TYPE_PART_NO, zone_data:ONEDB_ZONE_DATA_NONE, is_timestamp:0, has_seqid:0, no_of_dns_data_fields:0, dns_data_field_ids:fdns1, no_of_indexes:0, indexes: NULL, has_propagate_field: 0, has_encrypted_fields: 0, version_enabling_feature: NULL},


/*Below are fake types generated for special indexes*/
/*These are not counted towards g_num_types, since*/
/*we do not want these to be treated like regular types*/
/*fake types for cross, ip and ssindex in that order*/


  { typeid:2, is_internal:1, type_name:".com.infoblox.dns.zone_search_index", key_builder:0, recursion_end:0, key_builder_byte: ONEDB_INVALID_KEY_BUILDER_BYTE, unreferencable:1, select_auth_assumed:1, no_of_fields:3, no_of_fixed_length_fields:0, no_of_variable_length_fields:3, field_array:fr2, partition_type: ONEDB_TYPE_PART_NO, no_of_indexes:0, indexes:NULL}
};
onedb_rs_type_record_t *g_onedb_rs_type_record_array = st_onedb_rs_type_record_array;
int g_num_types_plus_faketypes  = 3;
/* end of rs_type.c */
/* ###################################################################### */
/* AUTOMATICALLY GENERATED - DO NOT EDIT */
/* ###################################################################### */

