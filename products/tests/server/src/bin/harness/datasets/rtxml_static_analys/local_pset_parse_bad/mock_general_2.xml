<?xml version="1.0" ?>
<!DOCTYPE package PUBLIC "-//Infoblox//DTD RTXML XML V1.0//EN" "/infoblox/tests/datasets/rtxml.dtd">
<!-- Copyright (c) 1999-2013 Infoblox Inc. All Rights Reserved. -->
<package name=".com.infoblox.one">
  <structure name="virtual_node" count="1">
    <member name="virtual_oid" type="rtxml.string" key-type="key">
            <syntax name="text" param="max=16"/>
            <description>unique identifier of this virtual node</description>
    </member>
  </structure>
  <structure name="mock_type_one" count="1">
    <member name="grid_member" type=".com.infoblox.one.virtual_node" ref-type="deep" key-type="key"
            partition-save="pset_1; pset_2 forward; pset_3; pset_4 forward; pset_5 conditional-cb pset_5_cond_cb; pset_unqualified"
            />
  </structure>
  <structure name="mock_type_two" count="1">
    <member name="parent" type=".com.infoblox.one.mock_type_one" ref-type="deep" key-type="key"
            partition-inherit="pset_1; pset_2; pset_3; pset_4"
            partition-use="pset_4; pset_1; pset_5 conditional-cb smth_cb"
            />
  </structure>
  <structure name="mock_type_three" count="1">
    <member name="multi" type=".com.infoblox.one.mock_type_one .com.infoblox.one.mock_type_two" ref-type="deep" key-type="key"
            partition-save="pset_three_1"
            partition-inherit="pset_three_2 reverse;pset_three_1 reverse;pset_2 forward; pset_3"
            partition-use="pset_unqualified; type one.mock_type_one; pset_2; pset_3; type .com.infoblox.one.mock_type_two; pset_4;"
    />
    <member name="grid_member" type=".com.infoblox.one.virtual_node" ref-type="deep" key-type="key"
            partition-save="pset_three_2"
            />
  </structure>

  <structure name="mock_type_four_extended" count="1" partition-extend="type one.mock_type_one">
    <member name="parent" type=".com.infoblox.one.mock_type_three" ref-type="deep" key-type="key"/>
  </structure>

  <structure name="partition_set" count="1" partition-hint="limited">
    <member name="name" type="rtxml.string" key-type="key">
      <description>Name of the object for easy retrieval</description>
    </member>
  </structure>

</package>
