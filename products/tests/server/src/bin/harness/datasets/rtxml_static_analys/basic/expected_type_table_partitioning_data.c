/**************************************************************************
 *
 * This file was AUTOMATICALLY GENERATED 
 * by the Java program 'MetaRepartition'.
 *
 * You probably DON'T want to make any edits here. THEY WILL BE LOST.
 *
 **************************************************************************/

/*
 * Copyright (C) 2006-2010 Infoblox, Inc.  All Rights Reserved.
 */
#include "infoblox/auto/type_table_partitioning_data.h"
#include "infoblox/util.h"
#include "../onedb/partition_private.h"

static const onedb_rtxml_ref_t g_empty = { 0, NULL, NULL };
static const type_table_id_t g_dependents_empty[] = { TYPE_unknown };
static const onedb_pset_definition_t g_psets_empty[] = { { NULL, g_dependents_empty } };
static const onedb_pset_action_t g_epilogue_empty[] = { { ONEDB_PARTITION_ACTION_INVALID, NULL } };
static const onedb_type_partitioning_data_t g_type_table_partition_data_array[2];

ib_return_t
type_table_id_to_partitioning_data(type_table_id_t a_id,
                                   const onedb_type_partitioning_data_t **a_data,
                                   ib_bool_t *a_found)
{
    IENTER;
    if( a_id < 2 ) {
        *a_data = &g_type_table_partition_data_array[a_id];
        *a_found = TRUE;
    } else {
        *a_found = FALSE;
    }
cleanup:
    ILEAVE;
}

ib_return_t
type_table_string_to_partitioning_data(const char *a_type,
                                       const onedb_type_partitioning_data_t **a_data,
                                       ib_bool_t *a_found)
{
    IENTER;
    type_table_id_t type_id;
    ICALL( type_table_string_to_id(a_type, &type_id, a_found) );
    if( *a_found ) {
        ICALL( type_table_id_to_partitioning_data(type_id, a_data, a_found) );
    }
cleanup:
    ILEAVE;
}

const char **
type_table_get_explicit_types(void)
{
    static const char *types[] =
    {
        ".com.infoblox.one.mock_type",
        NULL,
    };
    return types;
}

static const onedb_rtxml_ref_t fwd_com_infoblox_one_mock_type[] = { { TYPE_com_infoblox_one_virtual_node, ".com.infoblox.one.virtual_node", "grid_member", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };
static const onedb_rtxml_ref_t rev_com_infoblox_one_virtual_node[] = { { TYPE_com_infoblox_one_mock_type, ".com.infoblox.one.mock_type", "grid_member", FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL }, { 0, NULL, NULL, FALSE, FALSE, FALSE, FALSE, NULL, NULL, NULL } };

static const onedb_type_partitioning_data_t g_type_table_partition_data_array[2] = 
{
  { ONEDB_PARTITION_CLASS_DIRECT, ONEDB_PARTITION_HINT_NONE, fwd_com_infoblox_one_mock_type, &g_empty, &g_empty, NULL, NULL }, 
  { ONEDB_PARTITION_CLASS_ROOT, ONEDB_PARTITION_HINT_NONE, &g_empty, rev_com_infoblox_one_virtual_node, &g_empty, NULL, NULL }, 
};
