#ignore unknown-clients;
ignore bootp;
ddns-updates DDNS-UPDATES;
ddns-update-style DDNS_UPDATE_STYLE;
update-static-leases true;
update-optimization UPDATE-OPTIMIZATION;
option domain-name "test.infoblox.com";
option domain-name-servers 127.0.0.1;
#always-broadcast true;
local-address MY-ADDRESS;
server-identifier MY-ADDRESS;
authoritative;
max-lease-time LEASE-TIME;
default-lease-time LEASE-TIME;
ping-check false;

# Include a generated key-file (slightly closer to the "real thing")
include "/tmp/dhcp_updater.key";

failover peer "f1"
{
  MY-ROLE;
  address MY-ADDRESS;
  port 519;
  peer address PEER-ADDRESS;
  peer port 519;
  max-response-delay 60;
  max-unacked-updates 10;
  MCLT
  SPLIT
}

# Allocate leases from the loopback network
subnet ********* netmask ********* {
	pool {
		failover peer "f1";
                deny dynamic bootp clients;
		infoblox-range *********** *************;
		range *********** *************;
	}
}

# Where to send the DNS updates
zone "test.infoblox.com." {
	primary 127.0.0.1;
	key DHCP_UPDATER;
}

zone "127.in-addr.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER;
}
