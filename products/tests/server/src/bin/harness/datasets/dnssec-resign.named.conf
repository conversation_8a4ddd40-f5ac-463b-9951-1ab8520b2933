options {
    pid-file "named.pid";
    directory ".";
    recursion no;
    listen-on { 127.0.0.1; 192.168.1.2; };
    allow-query { any; };
    allow-transfer { any; };
    dnssec-enable yes;
    dnssec-validation yes;
    dnssec-accept-expired yes;
};

key "rndc-key" {
    algorithm hmac-md5;       
    secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
    inet 127.0.0.1 port 953
    allow { 127.0.0.1; } keys { "rndc-key"; };
};

zone "azone" in {
    type master;
    file "dnssec-isign.azone.db.signed";
    sig-validity-interval 1800; # secure zone
};

zone "10.in-addr.arpa" in {
    type master;
    file "dnssec-isign.10.db.signed";
    sig-validity-interval 1800; # secure zone
};

zone "a.ip6.arpa" {
    type master;
    file "dnssec-isign.a.ip6.db.signed";
    sig-validity-interval 1800; # secure zone
};

zone "azone3" in {
    type master;
    file "dnssec-isign.azone3.db.signed";
    sig-validity-interval 1800; # secure zone
};

zone "30.in-addr.arpa" in {
    type master;
    file "dnssec-isign.30.db.signed";
    sig-validity-interval 1800; # secure zone
};

zone "c.ip6.arpa" {
    type master;
    file "dnssec-isign.c.ip6.db.signed";
    sig-validity-interval 1800; # secure zone
};
