# All times in this file are in UTC (GMT), not your local timezone.   This is
# not a bug, so please don't ask about it.   There is no portable way to
# store leases in the local timezone, so please don't request this as a
# feature.   If this is inconvenient or confusing to you, we sincerely
# apologize.   Seriously, though - don't ask.
# The format of this file is documented in the dhcpd.leases(5) manual page.
# This lease file was written by isc-dhcp-V3.0.1rc12

# The following data is for test purposes only, and is not meant to
# be used to actually give out an IP address to any client.
lease ************* {
  starts 1 2004/02/23 21:48:41;
  ends 5 2009/02/27 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state active;
  hardware ethernet 00:e0:81:27:cd:4d;
  option agent.circuit-id "ab";
  option agent.remote-id "aa";
}
# This lease should fall outside the DHCP range, and generate an error
lease ************* {
  starts 1 2004/02/23 21:48:41;
  ends 0 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state active;
  hardware ethernet 00:e0:81:27:cd:4d;
  option agent.circuit-id "ab";
  option agent.remote-id "aa";
}
lease ************ {
  starts 2 2005/04/12 16:10:12;
  ends 0 2009/02/28 16:10:17;
  tstp 2 2005/04/12 16:10:12;
  binding state active;
  hardware ethernet ab:ba:90:e5:3e:51;
}
lease ************ {
  starts 2 2005/04/12 09:17:21;
  ends 0 2009/02/28 09:17:09;
  tstp 2 2005/04/12 16:10:14;
  binding state active;
  hardware ethernet ab:ba:90:e5:3e:51;
}
