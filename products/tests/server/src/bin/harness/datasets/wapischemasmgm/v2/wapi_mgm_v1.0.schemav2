[{"requested_version": "1.0", "supported_objects": ["search"]}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "search_string", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "search", "version": "1.0", "wapi_primitive": "object"}]