[{"requested_version": "2.9.1", "supported_objects": ["ad_auth_service", "admingroup", "adminrole", "adminuser", "authpolicy", "cacertificate", "certificate:authservice", "csvimporttask", "extensibleattributedef", "fileop", "grid", "grid:x509certificate", "ldap_auth_service", "localuser:authservice", "member", "member:license", "mgm:grid", "mgm:member", "mgm:monitordata", "mgm:network", "mgm:networkview", "mgm:usermapping", "<PERSON><PERSON><PERSON>", "natgroup", "permission", "radius:authservice", "request", "scheduledtask", "search", "smartfolder:children", "smartfolder:global", "smartfolder:personal", "snmpuser", "tacacsplus:authservice"]}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "ad_domain", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disabled", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "domain_controllers", "standard_field": false, "supports": "rwu", "type": ["ad_auth_server"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "nested_group_querying", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["scheduling", "csv"], "type": "ad_auth_service", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["GUI", "API", "TAXII", "CLOUD_API"], "is_array": true, "name": "access_method", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "email_addresses", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_restricted_user_access", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "roles", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "superuser", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "user_access", "standard_field": false, "supports": "rwu", "type": ["addressac"]}], "restrictions": ["scheduling", "csv"], "type": "admingroup", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["scheduling"], "type": "adminrole", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "admin_groups", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["LOCAL", "RADIUS", "REMOTE"], "is_array": false, "name": "auth_type", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "ca_certificate_issuer", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "client_certificate_serial_number", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "email", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_certificate_authentication", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "password", "standard_field": false, "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "time_zone", "overridden_by": "use_time_zone", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_time_zone", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": ["scheduling", "csv"], "type": "adminuser", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "admin_groups", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "auth_services", "standard_field": false, "supports": "rwu", "type": ["localuser:authservice", "ldap_auth_service", "radius:authservice", "tacacsplus:authservice", "ad_auth_service", "certificate:authservice"]}, {"is_array": false, "name": "default_group", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FULL", "AUTH_ONLY"], "is_array": false, "name": "usage_type", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}], "restrictions": ["delete", "create", "global search", "scheduling", "csv"], "type": "authpolicy", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "distinguished_name", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "issuer", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "serial", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "used_by", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "valid_not_after", "standard_field": true, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "valid_not_before", "standard_field": true, "supports": "r", "type": ["timestamp"]}], "restrictions": ["create", "update", "global search", "scheduling", "csv"], "type": "cacertificate", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["SERIAL_NUMBER", "S_DN_CN", "S_DN_EMAIL", "SAN_UPN", "SAN_EMAIL", "AD_SUBJECT_ISSUER"], "is_array": false, "name": "auto_populate_login", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": true, "name": "ca_certificates", "standard_field": false, "supports": "rwu", "type": ["cacertificate"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disabled", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_password_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_remote_lookup", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "max_retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["MANUAL", "AIA_ONLY", "AIA_AND_MANUAL", "DISABLED"], "is_array": false, "name": "ocsp_check", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": true, "name": "ocsp_responders", "standard_field": false, "supports": "rwu", "type": ["ocsp_responder"]}, {"is_array": false, "name": "recovery_interval", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "remote_lookup_password", "standard_field": false, "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "remote_lookup_service", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "remote_lookup_username", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "response_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"enum_values": ["DIRECT", "DELEGATED"], "is_array": false, "name": "trust_model", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["DIRECT_MATCH", "AUTO_MATCH"], "is_array": false, "name": "user_match_type", "standard_field": false, "supports": "rwu", "type": ["enum"]}], "restrictions": ["scheduling", "csv"], "type": "certificate:authservice", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["START", "SAVE"], "is_array": false, "name": "action", "standard_field": true, "supports": "wu", "type": ["enum"]}, {"is_array": false, "name": "admin_name", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "end_time", "standard_field": true, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "file_name", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "file_size", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "import_id", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["uint"]}, {"is_array": false, "name": "lines_failed", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "lines_processed", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "lines_warning", "standard_field": true, "supports": "r", "type": ["uint"]}, {"enum_values": ["CONTINUE", "STOP"], "is_array": false, "name": "on_error", "standard_field": true, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["INSERT", "UPDATE", "REPLACE", "DELETE", "CUSTOM"], "is_array": false, "name": "operation", "standard_field": true, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["COMMA", "SEMICOLON", "SPACE", "TAB"], "is_array": false, "name": "separator", "standard_field": true, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "start_time", "standard_field": true, "supports": "r", "type": ["timestamp"]}, {"enum_values": ["COMPLETED", "FAILED", "PENDING", "RUNNING", "STOPPED", "UPLOADED", "TEST_COMPLETED", "TEST_FAILED", "TEST_RUNNING", "TEST_STOPPED"], "is_array": false, "name": "status", "standard_field": true, "supports": "r", "type": ["enum"]}, {"enum_values": ["MERGE", "OVERRIDE"], "is_array": false, "name": "update_method", "standard_field": true, "supports": "rwu", "type": ["enum"]}], "restrictions": ["create", "delete", "permissions", "global search", "scheduling", "csv"], "type": "csvimporttask", "version": "2.9.1"}, {"cloud_additional_restrictions": [], "fields": [{"is_array": true, "name": "allowed_object_types", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "default_value", "standard_field": true, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "descendants_action", "standard_field": false, "supports": "wu", "type": ["extensibleattributedef:descendants"]}, {"is_array": false, "name": "flags", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "list_values", "standard_field": false, "supports": "rwu", "type": ["extensibleattributedef:listvalues"]}, {"is_array": false, "name": "max", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "min", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["CLOUD", "CLOUD_GM", "MSADSITES", "RIPE", "default"], "is_array": false, "name": "namespace", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["enum"]}, {"enum_values": ["STRING", "INTEGER", "EMAIL", "DATE", "ENUM", "URL"], "is_array": false, "name": "type", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}], "restrictions": ["global search", "scheduling", "csv"], "type": "extensibleattributedef", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [], "restrictions": ["create", "read", "delete", "update", "permissions", "global search", "csv"], "type": "fileop", "version": "2.9.1"}, {"cloud_additional_restrictions": ["update"], "fields": [{"enum_values": ["DETAILED", "BRIEF"], "is_array": false, "name": "audit_log_format", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "audit_to_syslog_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "consent_banner_setting", "standard_field": false, "supports": "rwu", "type": ["grid:consentbannersetting"]}, {"is_array": false, "name": "dns_resolver_setting", "standard_field": false, "supports": "rwu", "type": ["setting:dnsresolver"]}, {"is_array": false, "name": "dscp", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "email_setting", "standard_field": false, "supports": "rwu", "type": ["setting:email"]}, {"is_array": false, "name": "enable_gui_api_for_lan_vip", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_lom", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_member_redirect", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_recycle_bin", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "external_syslog_backup_servers", "standard_field": false, "supports": "rwu", "type": ["extsyslogbackupserver"]}, {"is_array": false, "name": "external_syslog_server_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "http_proxy_server_setting", "standard_field": false, "supports": "rwu", "type": ["setting:httpproxyserver"]}, {"is_array": false, "name": "informational_banner_setting", "standard_field": false, "supports": "rwu", "type": ["grid:informationalbannersetting"]}, {"is_array": false, "name": "is_grid_visualization_visible", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "name", "searchable_by": "~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "nat_groups", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ntp_setting", "standard_field": false, "supports": "rwu", "type": ["grid:ntp"]}, {"is_array": false, "name": "password_setting", "standard_field": false, "supports": "rwu", "type": ["setting:password"]}, {"is_array": false, "name": "scheduled_backup", "standard_field": false, "supports": "rwu", "type": ["scheduledbackup"]}, {"is_array": false, "name": "secret", "standard_field": false, "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "security_banner_setting", "standard_field": false, "supports": "rwu", "type": ["setting:securitybanner"]}, {"is_array": false, "name": "security_setting", "standard_field": false, "supports": "rwu", "type": ["setting:security"]}, {"is_array": false, "name": "snmp_setting", "standard_field": false, "supports": "rwu", "type": ["setting:snmp"]}, {"enum_values": ["DAEMON", "LOCAL0", "LOCAL1", "LOCAL2", "LOCAL3", "LOCAL4", "LOCAL5", "LOCAL6", "LOCAL7"], "is_array": false, "name": "syslog_facility", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": true, "name": "syslog_servers", "standard_field": false, "supports": "rwu", "type": ["syslogserver"]}, {"is_array": false, "name": "syslog_size", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "threshold_traps", "standard_field": false, "supports": "rwu", "type": ["thresholdtrap"]}, {"is_array": false, "name": "time_zone", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "token_usage_delay", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "trap_notifications", "standard_field": false, "supports": "rwu", "type": ["trapnotification"]}, {"is_array": false, "name": "vpn_port", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["create", "delete", "permissions", "global search", "scheduling", "csv"], "type": "grid", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "issuer", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "serial", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "subject", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "valid_not_after", "searchable_by": "=!<>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "valid_not_before", "searchable_by": "=!<>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}], "restrictions": ["create", "update", "delete", "permissions", "global search", "scheduling", "csv"], "type": "grid:x509certificate", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "ea_mapping", "standard_field": false, "supports": "rwu", "type": ["ldap_eamapping"]}, {"is_array": false, "name": "ldap_group_attribute", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"enum_values": ["GROUP_ATTRIBUTE", "POSIX_GROUP"], "is_array": false, "name": "ldap_group_authentication_type", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "ldap_user_attribute", "standard_field": true, "supports": "rwu", "type": ["string"]}, {"enum_values": ["ORDERED_LIST", "ROUND_ROBIN"], "is_array": false, "name": "mode", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "recovery_interval", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"enum_values": ["BASE", "ONELEVEL", "SUBTREE"], "is_array": false, "name": "search_scope", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": true, "name": "servers", "standard_field": false, "supports": "rwu", "type": ["ldap_server"]}, {"is_array": false, "name": "timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["scheduling", "csv"], "type": "ldap_auth_service", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "disabled", "standard_field": true, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "name", "standard_field": true, "supports": "r", "type": ["string"]}], "restrictions": ["delete", "create", "update", "permissions", "global search", "scheduling", "csv"], "type": "localuser:authservice", "version": "2.9.1"}, {"cloud_additional_restrictions": [], "fields": [{"is_array": false, "name": "active_position", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": true, "name": "additional_ip_list", "standard_field": false, "supports": "rwu", "type": ["interface"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"enum_values": ["IPV4", "IPV6", "BOTH"], "is_array": false, "name": "config_addr_type", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "dns_resolver_setting", "overridden_by": "use_dns_resolver_setting", "standard_field": false, "supports": "rwu", "type": ["setting:dnsresolver"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "email_setting", "overridden_by": "use_email_setting", "standard_field": false, "supports": "rwu", "type": ["setting:email"]}, {"is_array": false, "name": "enable_ha", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "enable_member_redirect", "overridden_by": "use_enable_member_redirect", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ro_api_access", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "external_syslog_backup_servers", "overridden_by": "use_external_syslog_backup_servers", "standard_field": false, "supports": "rwu", "type": ["extsyslogbackupserver"]}, {"is_array": false, "name": "external_syslog_server_enable", "overridden_by": "use_syslog_proxy_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "host_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6_setting", "standard_field": false, "supports": "rwu", "type": ["ipv6setting"]}, {"is_array": true, "name": "ipv6_static_routes", "standard_field": false, "supports": "rwu", "type": ["ipv6setting"]}, {"is_array": false, "name": "is_dscp_capable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "lan2_enabled", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "lan2_port_setting", "standard_field": false, "supports": "rwu", "type": ["lan2portsetting"]}, {"is_array": false, "name": "master_candidate", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "member_service_communication", "standard_field": false, "supports": "rwu", "type": ["memberservicecommunication"]}, {"is_array": false, "name": "mgmt_port_setting", "standard_field": false, "supports": "rwu", "type": ["mgmtportsetting"]}, {"is_array": true, "name": "node_info", "standard_field": false, "supports": "rwu", "type": ["nodeinfo"]}, {"is_array": false, "name": "ntp_setting", "standard_field": false, "supports": "rwu", "type": ["member:ntp"]}, {"is_array": false, "name": "passive_ha_arp_enabled", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"enum_values": ["INFOBLOX", "RIVERBED", "CISCO", "IBVM", "VNIOS"], "is_array": false, "name": "platform", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "pre_provisioning", "standard_field": false, "supports": "rwu", "type": ["preprovision"]}, {"is_array": false, "name": "preserve_if_owns_delegation", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "remote_console_access_enable", "overridden_by": "use_remote_console_access_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "router_id", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["uint"]}, {"is_array": true, "name": "service_status", "standard_field": false, "supports": "r", "type": ["memberservice<PERSON><PERSON>"]}, {"enum_values": ["ALL_V4", "ALL_V6", "CUSTOM"], "is_array": false, "name": "service_type_configuration", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "snmp_setting", "overridden_by": "use_snmp_setting", "standard_field": false, "supports": "rwu", "type": ["setting:snmp"]}, {"is_array": true, "name": "static_routes", "standard_field": false, "supports": "rwu", "type": ["setting:network"]}, {"is_array": false, "name": "support_access_enable", "overridden_by": "use_support_access_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "support_access_info", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "syslog_proxy_setting", "overridden_by": "use_syslog_proxy_setting", "standard_field": false, "supports": "rwu", "type": ["setting:syslogproxy"]}, {"is_array": true, "name": "syslog_servers", "overridden_by": "use_syslog_proxy_setting", "standard_field": false, "supports": "rwu", "type": ["syslogserver"]}, {"is_array": false, "name": "syslog_size", "overridden_by": "use_syslog_proxy_setting", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "threshold_traps", "overridden_by": "use_threshold_traps", "standard_field": false, "supports": "rwu", "type": ["thresholdtrap"]}, {"is_array": false, "name": "time_zone", "overridden_by": "use_time_zone", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "trap_notifications", "overridden_by": "use_trap_notifications", "standard_field": false, "supports": "rwu", "type": ["trapnotification"]}, {"is_array": false, "name": "upgrade_group", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_dns_resolver_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_dscp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_email_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_member_redirect", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_external_syslog_backup_servers", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_remote_console_access_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_snmp_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_support_access_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_syslog_proxy_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_threshold_traps", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_time_zone", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_trap_notifications", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_v4_vrrp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "vip_setting", "standard_field": false, "supports": "rwu", "type": ["setting:network"]}, {"is_array": false, "name": "vpn_mtu", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["scheduling", "csv"], "type": "member", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["NOT_EXPIRED", "EXPIRED", "PERMANENT", "EXPIRING_SOON", "EXPIRING_VERY_SOON", "DELETED"], "is_array": false, "name": "expiration_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "expiry_date", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "hwid", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "key", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"enum_values": ["Static", "Dynamic", "<PERSON>g", "Gridwide"], "is_array": false, "name": "kind", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "limit", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"enum_values": ["NONE", "LEASES", "MODEL", "TIER"], "is_array": false, "name": "limit_context", "standard_field": false, "supports": "r", "type": ["enum"]}, {"enum_values": {"ANYCAST": "ANYCAST", "CLOUD": "CLOUD", "CLOUD_API": "CLOUD_API", "DCA": "DCA", "DDI_TRIAL": "DDI_TRIAL", "DHCP": "DHCP", "DISCOVERY": "DISCOVERY", "DNS": "DNS", "DNSQRW": "DNSQRW", "DNS_CACHE_ACCEL": "DNS_CACHE_ACCEL", "DTC": "DTC", "FIREEYE": "FIREEYE", "FLEX_GRID_ACTIVATION": "ORGANIZATION", "FLEX_GRID_ACTIVATION_MS": "FLEX_GRID_MS", "FREQ_CONTROL": "FREQ_CONTROL", "GRID": "GRID", "GRID_MAINTENANCE": "GRID_MAINTENANCE", "IPAM": "IPAM", "IPAM_FREEWARE": "IPAM_FREEWARE", "LDAP": "LDAP", "LOAD_BALANCER": "LOAD_BALANCER", "MGM": "MGM", "MSMGMT": "MSMGMT", "NIOS": "NIOS", "NIOS_MAINTENANCE": "NIOS_MAINTENANCE", "NTP": "NTP", "OEM": "OEM", "QRD": "QRD", "REPORTING": "REPORTING", "REPORTING_SUB": "REPORTING_SUB", "RPZ": "RPZ", "SECURITY_ECOSYSTEM": "SECURITY_ECOSYSTEM", "SW_TP": "SW_TP", "TAE": "TAE", "TFTP": "TFTP", "THREAT_INSIGHT": "THREAT_INSIGHT", "TP": "TP", "TP_SUB": "TP_SUB", "VNIOS": "VNIOS"}, "is_array": false, "name": "type", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}], "restrictions": ["create", "update", "global search", "scheduling", "csv"], "type": "member:license", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "alternate_version", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "completed_members", "standard_field": false, "supports": "r", "type": ["uint"]}, {"enum_values": ["DETACHING", "ATTACHING", "DETACHED", "ATTACHED", "DETACHED_FAILED", "ATTACHED_FAILED", "SNAPSHOT_FAILED"], "is_array": false, "name": "connection_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "gm_host_name", "searchable_by": "~:=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "gm_virtual_ip", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "id", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "is_strict_delegate_mode", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "join_error_string", "standard_field": false, "supports": "r", "type": ["string"]}, {"enum_values": ["INITIAL", "JOINED", "JOIN_FAILED"], "is_array": false, "name": "join_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "join_token", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "join_token_expiration", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_sync_time", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_upgrade_date", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "name", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FAILED", "WARNING", "WORKING", "INACTIVE", "UNKNOWN", "OFFLINE"], "is_array": false, "name": "overall_service_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"enum_values": ["UNKNOWN", "FAILED", "WARNING", "WORKING"], "is_array": false, "name": "sync_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "total_members", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "total_network_containers", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "total_networks", "standard_field": false, "supports": "r", "type": ["uint"]}, {"enum_values": ["NONE", "DEFAULT", "DISTRIBUTING", "DISTRIBUTING_COMPLETE", "DISTRIBUTING_ENDED", "DISTRIBUTING_FAILED", "DISTRIBUTING_PAUSED", "DOWNGRADING_COMPLETE", "DOWNGRADING_FAILED", "REVERTING", "REVERTING_COMPLETE", "REVERTING_FAILED", "TEST_UPGRADING", "UPGRADING", "UPGRADING_COMPLETE", "UPGRADING_FAILED", "UPLOADED", "DISTRIBUTION_SCHEDULED", "UPGRADE_SCHEDULED", "UPGRADING_PAUSED"], "is_array": false, "name": "upgrade_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "upgrade_status_time", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "version", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["csv"], "type": "mgm:grid", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "enable_ha", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "r", "type": ["extattr"]}, {"is_array": false, "name": "grid", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"enum_values": ["ONLINE", "SYNCHRONIZING", "OFFLINE", "NA"], "is_array": false, "name": "ha_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "host_name", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "is_master", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "master_candidate_enabled", "standard_field": false, "supports": "r", "type": ["bool"]}, {"enum_values": ["FAILED", "WARNING", "WORKING", "INACTIVE", "OFFLINE"], "is_array": false, "name": "member_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"enum_values": ["CISCO", "IBVM", "INFOBLOX", "RIVERBED", "VNIOS"], "is_array": false, "name": "member_type", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": true, "name": "service_status", "standard_field": false, "supports": "r", "type": ["memberservice<PERSON><PERSON>"]}, {"is_array": false, "name": "uptime", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "virtual_ip", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "virtual_oid", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["create", "update", "delete", "permissions", "scheduling", "csv"], "type": "mgm:member", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "grid", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "monitors", "standard_field": false, "supports": "r", "type": ["mgm:monitorentry"]}, {"is_array": false, "name": "virtual_node", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "delete", "permissions", "global search", "scheduling", "csv"], "type": "mgm:monitordata", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "searchable_by": "~=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "grid", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "is_container", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "is_ipv4", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "netmask", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["delete", "csv"], "type": "mgm:network", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "r", "type": ["extattr"]}, {"is_array": false, "name": "grid", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "id", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "~:=", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "delete", "permissions", "scheduling", "csv"], "type": "mgm:networkview", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auth_admin_group", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "auth_user_name", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "grid", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ignore", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "user_name", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "delete", "permissions", "global search", "scheduling", "csv"], "type": "mgm:usermapping", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "access_list", "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exploded_access_list", "standard_field": false, "supports": "r", "type": ["addressac", "tsigac"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["scheduling", "csv"], "type": "<PERSON><PERSON><PERSON>", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["csv"], "type": "natgroup", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "group", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "object", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"enum_values": {"DENY": "DENY", "READ": "RO", "WRITE": "RW"}, "is_array": false, "name": "permission", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"enum_values": ["CLUSTER", "MEMBER", "MEMBER_CLOUD", "SUB_GRID", "SUB_GRID_NETWORK_VIEW_PARENT", "SG_NETWORK_VIEW", "SG_IPV4_NETWORK", "SG_IPV6_NETWORK", "MSSERVER", "VIEW", "ZONE", "A", "AAAA", "ALIAS", "CNAME", "DNAME", "MX", "PTR", "SRV", "TXT", "HOST", "BULKHOST", "NAPTR", "TLSA", "CAA", "Unknown", "SHARED_RECORD_GROUP", "SHARED_A", "SHARED_AAAA", "SHARED_MX", "SHARED_SRV", "SHARED_TXT", "SHARED_CNAME", "NETWORK_VIEW", "NETWORK", "IPV6_NETWORK", "NETWORK_CONTAINER", "IPV6_NETWORK_CONTAINER", "RANGE", "IPV6_RANGE", "FIXED_ADDRESS", "IPV6_FIXED_ADDRESS", "ROAMING_HOST", "DHCP_MAC_FILTER", "SHARED_NETWORK", "IPV6_SHARED_NETWORK", "TEMPLATE", "IPV6_TEMPLATE", "NETWORK_TEMPLATE", "IPV6_NETWORK_TEMPLATE", "RANGE_TEMPLATE", "IPV6_RANGE_TEMPLATE", "FIXED_ADDRESS_TEMPLATE", "IPV6_FIXED_ADDRESS_TEMPLATE", "OPTION_SPACE", "RESTORABLE_OPERATION", "CSV_IMPORT_TASK", "DHCP_LEASE_HISTORY", "IPV6_DHCP_LEASE_HISTORY", "GRID_FILE_DIST_PROPERTIES", "MEMBER_FILE_DIST_PROPERTIES", "FILE_DIST_DIRECTORY", "HSM_GROUP", "GRID_AAA_PROPERTIES", "AAA_EXTERNAL_SERVICE", "NETWORK_DISCOVERY", "SCHEDULE_TASK", "MS_SUPERSCOPE", "MEMBER_DNS_PROPERTIES", "MEMBER_DHCP_PROPERTIES", "MEMBER_SECURITY_PROPERTIES", "MEMBER_ANALYTICS_PROPERTIES", "RESTART_SERVICE", "GRID_DNS_PROPERTIES", "GRID_DHCP_PROPERTIES", "GRID_REPORTING_PROPERTIES", "GRID_SECURITY_PROPERTIES", "IMC_PROPERTIES", "IMC_SITE", "IMC_AVP", "GRID_ANALYTICS_PROPERTIES", "RULESET", "DNS64_SYNTHESIS_GROUP", "DASHBOARD_TASK", "REPORTING_DASHBOARD", "REPORTING_SEARCH", "OCSP_SERVICE", "CA_CERTIFICATE", "RESPONSE_POLICY_ZONE", "RESPONSE_POLICY_RULE", "DHCP_FINGERPRINT", "DEFINED_ACL", "FIREEYE_PUBLISH_ALERT", "HOST_ADDRESS", "IPV6_HOST_ADDRESS", "PORT_CONTROL", "DEVICE", "KERBEROS_KEY", "BFD_TEMPLATE", "MS_ADSITES_DOMAIN", "IDNS_LBDN", "IDNS_LBDN_RECORD", "IDNS_POOL", "IDNS_SERVER", "IDNS_TOPOLOGY", "IDNS_MONITOR", "IDNS_CERTIFICATE", "IDNS_GEO_IP", "TENANT", "RECLAMATION", "SUPER_HOST", "ADD_A_RR_WITH_EMPTY_HOSTNAME", "DATACOLLECTOR_CLUSTER", "DELETED_OBJS_INFO_TRACKING", "SAML_AUTH_SERVICE", "VLAN_VIEW", "VLAN_RANGE", "VLAN_OBJECTS"], "is_array": false, "name": "resource_type", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "role", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["global search", "scheduling", "csv"], "type": "permission", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "acct_retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "acct_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "auth_retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "auth_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "cache_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_cache", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"enum_values": ["HUNT_GROUP", "ROUND_ROBIN"], "is_array": false, "name": "mode", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "name", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "recovery_interval", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "servers", "standard_field": false, "supports": "rwu", "type": ["radius:server"]}], "restrictions": ["scheduling", "csv"], "type": "radius:authservice", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["APPROVED", "NONE", "PENDING", "REJECTED"], "is_array": false, "name": "approval_status", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "approver", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "approver_comment", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "automatic_restart", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "changed_objects", "standard_field": false, "supports": "r", "type": ["changedobject"]}, {"is_array": true, "name": "dependent_tasks", "standard_field": false, "supports": "r", "type": ["scheduledtask"]}, {"is_array": false, "name": "execute_now", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": true, "name": "execution_details", "standard_field": false, "supports": "r", "type": ["string"]}, {"enum_values": ["NONE", "WARNING"], "is_array": false, "name": "execution_details_type", "standard_field": false, "supports": "r", "type": ["enum"]}, {"enum_values": ["COMPLETED", "FAILED", "PENDING", "WAITING_EXECUTION"], "is_array": false, "name": "execution_status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "execution_time", "searchable_by": "<=>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "is_network_insight_task", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "member", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "predecessor_task", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "re_execute_task", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "scheduled_time", "searchable_by": "<=>", "standard_field": false, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "submit_time", "searchable_by": "<=>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "submitter", "searchable_by": "=~:", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "submitter_comment", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "task_id", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["uint"]}, {"enum_values": ["OBJECT_CHANGE", "PORT_CONTROL"], "is_array": false, "name": "task_type", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "ticket_number", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["create", "permissions", "global search", "scheduling", "csv"], "type": "scheduledtask", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "search", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "resource", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "value", "standard_field": true, "supports": "r", "type": ["smartfolder:queryitemvalue"]}, {"enum_values": ["STRING", "INTEGER", "BOOLEAN", "DATE", "ENUM", "EMAIL", "URL", "OBJTYPE"], "is_array": false, "name": "value_type", "standard_field": true, "supports": "r", "type": ["enum"]}], "restrictions": ["create", "update", "delete", "global search", "scheduling", "csv"], "type": "smartfolder:children", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "group_bys", "standard_field": false, "supports": "rwu", "type": ["smartfolder:groupby"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "query_items", "standard_field": false, "supports": "rwu", "type": ["smartfolder:queryitem"]}], "restrictions": ["global search", "csv"], "type": "smartfolder:global", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "group_bys", "standard_field": false, "supports": "rwu", "type": ["smartfolder:groupby"]}, {"is_array": false, "name": "is_shortcut", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["bool"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "query_items", "standard_field": false, "supports": "rwu", "type": ["smartfolder:queryitem"]}], "restrictions": ["global search", "csv"], "type": "smartfolder:personal", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "authentication_password", "standard_field": false, "supports": "wu", "type": ["string"]}, {"enum_values": ["NONE", "MD5", "SHA"], "is_array": false, "name": "authentication_protocol", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "privacy_password", "standard_field": false, "supports": "wu", "type": ["string"]}, {"enum_values": ["NONE", "DES", "AES"], "is_array": false, "name": "privacy_protocol", "standard_field": false, "supports": "rwu", "type": ["enum"]}], "restrictions": ["scheduling", "csv"], "type": "snmpuser", "version": "2.9.1"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "acct_retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "acct_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "auth_retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "auth_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "name", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "servers", "standard_field": false, "supports": "rwu", "type": ["tacacsplus:server"]}], "restrictions": ["scheduling", "csv"], "type": "tacacsplus:authservice", "version": "2.9.1"}]