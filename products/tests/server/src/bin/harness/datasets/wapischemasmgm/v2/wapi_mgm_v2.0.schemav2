[{"requested_version": "2.0", "supported_objects": ["admingroup", "adminuser", "csvimporttask", "extensibleattributedef", "fileop", "grid:x509certificate", "<PERSON><PERSON><PERSON>", "permission", "scheduledtask", "search", "snmpuser"]}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["GUI", "API"], "is_array": true, "name": "access_method", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "email_addresses", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "roles", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "superuser", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}], "restrictions": ["scheduling", "csv"], "type": "admingroup", "version": "2.0", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "admin_groups", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "email", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "password", "standard_field": false, "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "role", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "time_zone", "overridden_by": "use_time_zone", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_time_zone", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": ["scheduling", "csv"], "type": "adminuser", "version": "2.0", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["START", "SAVE"], "is_array": false, "name": "action", "standard_field": true, "supports": "wu", "type": ["enum"]}, {"is_array": false, "name": "admin_name", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "end_time", "standard_field": true, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "file_name", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "file_size", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "import_id", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["uint"]}, {"is_array": false, "name": "lines_failed", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "lines_processed", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "lines_warning", "standard_field": true, "supports": "r", "type": ["uint"]}, {"enum_values": ["CONTINUE", "STOP"], "is_array": false, "name": "on_error", "standard_field": true, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["INSERT", "UPDATE"], "is_array": false, "name": "operation", "standard_field": true, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["COMMA", "SEMICOLON", "SPACE", "TAB"], "is_array": false, "name": "separator", "standard_field": true, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "start_time", "standard_field": true, "supports": "r", "type": ["timestamp"]}, {"enum_values": ["COMPLETED", "FAILED", "PENDING", "RUNNING", "STOPPED", "UPLOADED", "TEST_COMPLETED", "TEST_FAILED", "TEST_RUNNING", "TEST_STOPPED"], "is_array": false, "name": "status", "standard_field": true, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "stop", "schema": {"input_fields": [], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["stopcsv"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"enum_values": ["MERGE", "OVERRIDE"], "is_array": false, "name": "update_method", "standard_field": true, "supports": "rwu", "type": ["enum"]}], "restrictions": ["create", "delete", "permissions", "global search", "scheduling", "csv"], "type": "csvimporttask", "version": "2.0", "wapi_primitive": "object"}, {"cloud_additional_restrictions": [], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "default_value", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"enum_values": ["CLOUD", "CLOUD_GM", "MSADSITES", "RIPE", "default"], "is_array": false, "name": "namespace", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["enum"]}, {"enum_values": ["STRING", "INTEGER", "EMAIL", "DATE", "ENUM", "URL"], "is_array": false, "name": "type", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "extensibleattributedef", "version": "2.0", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"func_type": "download", "is_array": false, "name": "csv_error_log", "schema": {"input_fields": [{"is_array": false, "name": "import_id", "supports": "w", "type": ["uint"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["csverrorlog"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "csv_export", "schema": {"input_fields": [{"is_array": false, "name": "_object", "supports": "w", "type": ["string"]}, {"enum_values": ["COMMA", "SEMICOLON", "SPACE", "TAB"], "is_array": false, "name": "_separator", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "_filename", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "_gzipfile", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "_fileprefix", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["csvexport"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "csv_import", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "doimport", "supports": "w", "type": ["bool"]}, {"enum_values": ["COMMA", "SEMICOLON", "SPACE", "TAB"], "is_array": false, "name": "separator", "supports": "w", "type": ["enum"]}, {"enum_values": ["MERGE", "OVERRIDE"], "is_array": false, "name": "update_method", "supports": "w", "type": ["enum"]}, {"enum_values": ["INSERT", "UPDATE", "REPLACE", "DELETE"], "is_array": false, "name": "operation", "supports": "w", "type": ["enum"]}, {"enum_values": ["START", "TEST"], "is_array": false, "name": "action", "supports": "w", "type": ["enum"]}, {"enum_values": ["CONTINUE", "STOP"], "is_array": false, "name": "on_error", "supports": "w", "type": ["enum"]}], "output_fields": [{"is_array": false, "name": "csv_import_task", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["csvimport"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "csv_snapshot_file", "schema": {"input_fields": [{"is_array": false, "name": "import_id", "supports": "w", "type": ["uint"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["csvsnapshot"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "csv_uploaded_file", "schema": {"input_fields": [{"is_array": false, "name": "import_id", "supports": "w", "type": ["uint"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["csvuploaded"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "downloadcomplete", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["datagetcomplete"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "getgriddata", "schema": {"input_fields": [{"enum_values": ["NTP_KEY_FILE", "SNMP_MIBS_FILE", "BACKUP"], "is_array": false, "name": "type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "remote_url", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "nios_data", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "discovery_data", "supports": "w", "type": ["bool"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["getgriddata"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "read", "schema": {"input_fields": [{"is_array": false, "name": "_object", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "_filename", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "_return_fields", "supports": "w", "type": ["string"]}, {"enum_values": ["JSON", "XML"], "is_array": false, "name": "_encoding", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "_max_results", "supports": "w", "type": ["int"]}, {"is_array": false, "name": "_gzipfile", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "_fileprefix", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["read"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "setfiledest", "schema": {"input_fields": [{"enum_values": ["TFTP_FILE"], "is_array": false, "name": "type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "dest_path", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "extract", "supports": "w", "type": ["bool"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["setdatafiledest"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "uploadinit", "schema": {"input_fields": [{"is_array": false, "name": "filename", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "url", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "token", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["datauploadinit"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}], "restrictions": ["create", "read", "delete", "update", "permissions", "global search", "csv"], "type": "fileop", "version": "2.0", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "issuer", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "serial", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "subject", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "valid_not_after", "searchable_by": "=!<>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "valid_not_before", "searchable_by": "=!<>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}], "restrictions": ["create", "update", "delete", "permissions", "global search", "scheduling", "csv"], "type": "grid:x509certificate", "version": "2.0", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "access_list", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exploded_access_list", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "r", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["scheduling", "csv"], "type": "<PERSON><PERSON><PERSON>", "version": "2.0", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "group", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "object", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"enum_values": ["DENY", "READ", "WRITE"], "is_array": false, "name": "permission", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"enum_values": ["CLUSTER", "MEMBER", "MEMBER_CLOUD", "SUB_GRID", "SUB_GRID_NETWORK_VIEW_PARENT", "SG_NETWORK_VIEW", "SG_IPV4_NETWORK", "SG_IPV6_NETWORK", "MSSERVER", "VIEW", "ZONE", "A", "AAAA", "ALIAS", "CNAME", "DNAME", "MX", "PTR", "SRV", "TXT", "HOST", "BULKHOST", "NAPTR", "TLSA", "CAA", "Unknown", "SHARED_RECORD_GROUP", "SHARED_A", "SHARED_AAAA", "SHARED_MX", "SHARED_SRV", "SHARED_TXT", "SHARED_CNAME", "NETWORK_VIEW", "NETWORK", "IPV6_NETWORK", "NETWORK_CONTAINER", "IPV6_NETWORK_CONTAINER", "RANGE", "IPV6_RANGE", "FIXED_ADDRESS", "IPV6_FIXED_ADDRESS", "ROAMING_HOST", "DHCP_MAC_FILTER", "SHARED_NETWORK", "IPV6_SHARED_NETWORK", "TEMPLATE", "IPV6_TEMPLATE", "NETWORK_TEMPLATE", "IPV6_NETWORK_TEMPLATE", "RANGE_TEMPLATE", "IPV6_RANGE_TEMPLATE", "FIXED_ADDRESS_TEMPLATE", "IPV6_FIXED_ADDRESS_TEMPLATE", "OPTION_SPACE", "RESTORABLE_OPERATION", "CSV_IMPORT_TASK", "DHCP_LEASE_HISTORY", "IPV6_DHCP_LEASE_HISTORY", "GRID_FILE_DIST_PROPERTIES", "MEMBER_FILE_DIST_PROPERTIES", "FILE_DIST_DIRECTORY", "HSM_GROUP", "GRID_AAA_PROPERTIES", "AAA_EXTERNAL_SERVICE", "NETWORK_DISCOVERY", "SCHEDULE_TASK", "MS_SUPERSCOPE", "MEMBER_DNS_PROPERTIES", "MEMBER_DHCP_PROPERTIES", "MEMBER_SECURITY_PROPERTIES", "MEMBER_ANALYTICS_PROPERTIES", "RESTART_SERVICE", "GRID_DNS_PROPERTIES", "GRID_DHCP_PROPERTIES", "GRID_REPORTING_PROPERTIES", "GRID_SECURITY_PROPERTIES", "IMC_PROPERTIES", "IMC_SITE", "IMC_AVP", "GRID_ANALYTICS_PROPERTIES", "RULESET", "DNS64_SYNTHESIS_GROUP", "DASHBOARD_TASK", "REPORTING_DASHBOARD", "REPORTING_SEARCH", "OCSP_SERVICE", "CA_CERTIFICATE", "RESPONSE_POLICY_ZONE", "RESPONSE_POLICY_RULE", "DHCP_FINGERPRINT", "DEFINED_ACL", "FIREEYE_PUBLISH_ALERT", "HOST_ADDRESS", "IPV6_HOST_ADDRESS", "PORT_CONTROL", "DEVICE", "KERBEROS_KEY", "BFD_TEMPLATE", "MS_ADSITES_DOMAIN", "IDNS_LBDN", "IDNS_LBDN_RECORD", "IDNS_POOL", "IDNS_SERVER", "IDNS_TOPOLOGY", "IDNS_MONITOR", "IDNS_CERTIFICATE", "IDNS_GEO_IP", "TENANT", "RECLAMATION", "SUPER_HOST", "ADD_A_RR_WITH_EMPTY_HOSTNAME", "DATACOLLECTOR_CLUSTER", "DELETED_OBJS_INFO_TRACKING", "SAML_AUTH_SERVICE", "VLAN_VIEW", "VLAN_RANGE", "VLAN_OBJECTS"], "is_array": false, "name": "resource_type", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "role", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["global search", "scheduling", "csv"], "type": "permission", "version": "2.0", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["APPROVED", "NONE", "PENDING", "REJECTED"], "is_array": false, "name": "approval_status", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "approver", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "approver_comment", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "automatic_restart", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "changed_objects", "schema": {"fields": [{"enum_values": ["Convert IPv4 Lease", "Delete", "Restart Services", "Add", "Convert IPv6 Lease", "Lock/Unlock Zone", "Reset Grid", "Configure Grid", "Restart Services", "Network Discovery", "Upgrade Grid", "Modify"], "is_array": false, "name": "action", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "object_type", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "properties", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["changedobject"], "wapi_primitive": "struct"}, {"is_array": false, "name": "changed_objects.action", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "changed_objects.name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "changed_objects.object_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "changed_objects.type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": true, "name": "dependent_tasks", "standard_field": false, "supports": "r", "type": ["scheduledtask"]}, {"is_array": false, "name": "execute_now", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": true, "name": "execution_details", "standard_field": false, "supports": "r", "type": ["string"]}, {"enum_values": ["NONE", "WARNING"], "is_array": false, "name": "execution_details_type", "standard_field": false, "supports": "r", "type": ["enum"]}, {"enum_values": ["COMPLETED", "FAILED", "PENDING", "WAITING_EXECUTION"], "is_array": false, "name": "execution_status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "execution_time", "searchable_by": "<=>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "is_network_insight_task", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "predecessor_task", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "re_execute_task", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "scheduled_time", "searchable_by": "<=>", "standard_field": false, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "submit_time", "searchable_by": "<=>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "submitter", "searchable_by": "=~:", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "submitter_comment", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "task_id", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["uint"]}, {"enum_values": ["OBJECT_CHANGE", "PORT_CONTROL"], "is_array": false, "name": "task_type", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "ticket_number", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["create", "permissions", "global search", "scheduling", "csv"], "type": "scheduledtask", "version": "2.0", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["admingroup", "adminuser", "<PERSON><PERSON><PERSON>", "snmpuser"], "is_array": false, "name": "objtype", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["enum"]}, {"is_array": false, "name": "search_string", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "search", "version": "2.0", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["NONE", "MD5", "SHA"], "is_array": false, "name": "authentication_protocol", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "comment", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": true, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "name", "standard_field": true, "supports": "r", "type": ["string"]}, {"enum_values": ["NONE", "DES", "AES"], "is_array": false, "name": "privacy_protocol", "standard_field": false, "supports": "r", "type": ["enum"]}], "restrictions": ["create", "delete", "update", "read", "global search", "scheduling", "csv"], "type": "snmpuser", "version": "2.0", "wapi_primitive": "object"}]