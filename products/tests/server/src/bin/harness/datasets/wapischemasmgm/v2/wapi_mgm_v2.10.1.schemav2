[{"requested_version": "2.10.1", "supported_objects": ["ad_auth_service", "admingroup", "adminrole", "adminuser", "authpolicy", "cacertificate", "certificate:authservice", "csvimporttask", "extensibleattributedef", "fileop", "grid", "grid:x509certificate", "ldap_auth_service", "localuser:authservice", "member", "member:license", "mgm:grid", "mgm:member", "mgm:monitordata", "mgm:network", "mgm:networkview", "mgm:usermapping", "<PERSON><PERSON><PERSON>", "natgroup", "permission", "radius:authservice", "request", "saml:authservice", "scheduledtask", "search", "smartfolder:children", "smartfolder:global", "smartfolder:personal", "snmpuser", "tacacsplus:authservice"]}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "ad_domain", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disabled", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "domain_controllers", "schema": {"fields": [{"is_array": false, "name": "fqdn_or_ip", "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "auth_port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "comment", "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disabled", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["SSL", "NONE"], "is_array": false, "name": "encryption", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "mgmt_port", "overridden_by": "use_mgmt_port", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_mgmt_port", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["ad_auth_server"], "wapi_primitive": "struct"}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "nested_group_querying", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["scheduling", "csv"], "type": "ad_auth_service", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["GUI", "API", "TAXII", "CLOUD_API"], "is_array": true, "name": "access_method", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable_concurrent_login", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "email_addresses", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_restricted_user_access", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "inactivity_lockout_setting", "overridden_by": "use_account_inactivity_lockout_enable", "schema": {"fields": [{"is_array": false, "name": "account_inactivity_lockout_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "inactive_days", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "reminder_days", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "reactivate_via_serial_console_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "reactivate_via_remote_console_enable", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:inactivelockout"], "wapi_primitive": "struct"}, {"is_array": false, "name": "lockout_setting", "overridden_by": "use_lockout_setting", "schema": {"fields": [{"is_array": false, "name": "enable_sequential_failed_login_attempts_lockout", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "sequential_attempts", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "failed_lockout_duration", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "never_unlock_user", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["admingroup:lockoutsetting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "password_setting", "overridden_by": "use_password_setting", "schema": {"fields": [{"is_array": false, "name": "expire_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "expire_days", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "reminder_days", "supports": "rwu", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["admingroup:passwordsetting"], "wapi_primitive": "struct"}, {"is_array": true, "name": "roles", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "saml_setting", "schema": {"fields": [{"is_array": false, "name": "auto_create_user", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "persist_auto_created_user", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["admingroup:samlsetting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "superuser", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "use_account_inactivity_lockout_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_disable_concurrent_login", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_lockout_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_password_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "user_access", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac"], "wapi_primitive": "struct"}], "restrictions": ["scheduling", "csv"], "type": "admingroup", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["scheduling"], "type": "adminrole", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "admin_groups", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["LOCAL", "RADIUS", "REMOTE", "SAML", "SAML_LOCAL"], "is_array": false, "name": "auth_type", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "ca_certificate_issuer", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "client_certificate_serial_number", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "email", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_certificate_authentication", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "password", "standard_field": false, "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "role", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"enum_values": ["ACTIVE", "INACTIVE", "LOCKED", "DISABLED"], "is_array": false, "name": "status", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "time_zone", "overridden_by": "use_time_zone", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_time_zone", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": ["scheduling", "csv"], "type": "adminuser", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "admin_groups", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "auth_services", "standard_field": false, "supports": "rwu", "type": ["localuser:authservice", "ldap_auth_service", "radius:authservice", "tacacsplus:authservice", "ad_auth_service", "certificate:authservice", "saml:authservice"]}, {"is_array": false, "name": "default_group", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FULL", "AUTH_ONLY"], "is_array": false, "name": "usage_type", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}], "restrictions": ["delete", "create", "global search", "scheduling", "csv"], "type": "authpolicy", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "distinguished_name", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "issuer", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "serial", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "used_by", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "valid_not_after", "standard_field": true, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "valid_not_before", "standard_field": true, "supports": "r", "type": ["timestamp"]}], "restrictions": ["create", "update", "global search", "scheduling", "csv"], "type": "cacertificate", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["SERIAL_NUMBER", "S_DN_CN", "S_DN_EMAIL", "SAN_UPN", "SAN_EMAIL", "AD_SUBJECT_ISSUER"], "is_array": false, "name": "auto_populate_login", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": true, "name": "ca_certificates", "standard_field": false, "supports": "rwu", "type": ["cacertificate"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disabled", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_password_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_remote_lookup", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "max_retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["MANUAL", "AIA_ONLY", "AIA_AND_MANUAL", "DISABLED"], "is_array": false, "name": "ocsp_check", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": true, "name": "ocsp_responders", "schema": {"fields": [{"is_array": false, "name": "fqdn_or_ip", "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "comment", "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "certificate", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "certificate_token", "supports": "wu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["ocsp_responder"], "wapi_primitive": "struct"}, {"is_array": false, "name": "recovery_interval", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "remote_lookup_password", "standard_field": false, "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "remote_lookup_service", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "remote_lookup_username", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "response_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "test_ocsp_responder_settings", "schema": {"input_fields": [{"is_array": false, "name": "certificate_auth_service", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "ocsp_responder", "schema": {"fields": [{"is_array": false, "name": "fqdn_or_ip", "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "comment", "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "certificate", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "certificate_token", "supports": "wu", "type": ["string"]}]}, "supports": "w", "type": ["ocsp_responder"], "wapi_primitive": "struct"}], "output_fields": [{"enum_values": ["CANNOT_RESOLVE_FQDN", "CANNOT_CONNECT", "FAILED_TEST", "TEST_OK"], "is_array": false, "name": "result", "supports": "r", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["testocsprespondersettings"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"enum_values": ["DIRECT", "DELEGATED"], "is_array": false, "name": "trust_model", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["DIRECT_MATCH", "AUTO_MATCH"], "is_array": false, "name": "user_match_type", "standard_field": false, "supports": "rwu", "type": ["enum"]}], "restrictions": ["scheduling", "csv"], "type": "certificate:authservice", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["START", "SAVE"], "is_array": false, "name": "action", "standard_field": true, "supports": "wu", "type": ["enum"]}, {"is_array": false, "name": "admin_name", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "end_time", "standard_field": true, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "file_name", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "file_size", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "import_id", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["uint"]}, {"is_array": false, "name": "lines_failed", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "lines_processed", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "lines_warning", "standard_field": true, "supports": "r", "type": ["uint"]}, {"enum_values": ["CONTINUE", "STOP"], "is_array": false, "name": "on_error", "standard_field": true, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["INSERT", "UPDATE", "REPLACE", "DELETE", "CUSTOM"], "is_array": false, "name": "operation", "standard_field": true, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["COMMA", "SEMICOLON", "SPACE", "TAB"], "is_array": false, "name": "separator", "standard_field": true, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "start_time", "standard_field": true, "supports": "r", "type": ["timestamp"]}, {"enum_values": ["COMPLETED", "FAILED", "PENDING", "RUNNING", "STOPPED", "UPLOADED", "TEST_COMPLETED", "TEST_FAILED", "TEST_RUNNING", "TEST_STOPPED"], "is_array": false, "name": "status", "standard_field": true, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "stop", "schema": {"input_fields": [], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["stopcsv"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"enum_values": ["MERGE", "OVERRIDE"], "is_array": false, "name": "update_method", "standard_field": true, "supports": "rwu", "type": ["enum"]}], "restrictions": ["create", "delete", "permissions", "global search", "scheduling", "csv"], "type": "csvimporttask", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": [], "fields": [{"is_array": true, "name": "allowed_object_types", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "default_value", "standard_field": true, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "descendants_action", "schema": {"fields": [{"enum_values": ["RETAIN", "CONVERT", "INHERIT"], "is_array": false, "name": "option_with_ea", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["INHERIT", "NOT_INHERIT"], "is_array": false, "name": "option_without_ea", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["RETAIN", "REMOVE"], "is_array": false, "name": "option_delete_ea", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "wu", "type": ["extensibleattributedef:descendants"], "wapi_primitive": "struct"}, {"is_array": false, "name": "flags", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "list_values", "schema": {"fields": [{"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["extensibleattributedef:listvalues"], "wapi_primitive": "struct"}, {"is_array": false, "name": "max", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "min", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["CLOUD", "CLOUD_GM", "MSADSITES", "RIPE", "default"], "is_array": false, "name": "namespace", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["enum"]}, {"enum_values": ["STRING", "INTEGER", "EMAIL", "DATE", "ENUM", "URL"], "is_array": false, "name": "type", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}], "restrictions": ["global search", "scheduling", "csv"], "type": "extensibleattributedef", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"func_type": "download", "is_array": false, "name": "csv_error_log", "schema": {"input_fields": [{"is_array": false, "name": "import_id", "supports": "w", "type": ["uint"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["csverrorlog"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "csv_export", "schema": {"input_fields": [{"is_array": false, "name": "_object", "supports": "w", "type": ["string"]}, {"enum_values": ["COMMA", "SEMICOLON", "SPACE", "TAB"], "is_array": false, "name": "_separator", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "_filename", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "_gzipfile", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "_fileprefix", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["csvexport"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "csv_import", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "doimport", "supports": "w", "type": ["bool"]}, {"enum_values": ["COMMA", "SEMICOLON", "SPACE", "TAB"], "is_array": false, "name": "separator", "supports": "w", "type": ["enum"]}, {"enum_values": ["MERGE", "OVERRIDE"], "is_array": false, "name": "update_method", "supports": "w", "type": ["enum"]}, {"enum_values": ["INSERT", "UPDATE", "REPLACE", "DELETE", "CUSTOM"], "is_array": false, "name": "operation", "supports": "w", "type": ["enum"]}, {"enum_values": ["START", "TEST"], "is_array": false, "name": "action", "supports": "w", "type": ["enum"]}, {"enum_values": ["CONTINUE", "STOP"], "is_array": false, "name": "on_error", "supports": "w", "type": ["enum"]}], "output_fields": [{"is_array": false, "name": "csv_import_task", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["csvimport"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "csv_snapshot_file", "schema": {"input_fields": [{"is_array": false, "name": "import_id", "supports": "w", "type": ["uint"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["csvsnapshot"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "csv_uploaded_file", "schema": {"input_fields": [{"is_array": false, "name": "import_id", "supports": "w", "type": ["uint"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["csvuploaded"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "download_atp_rule_update", "schema": {"input_fields": [], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["emptyparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "download_pool_status", "schema": {"input_fields": [], "output_fields": [{"is_array": false, "name": "url", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "token", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["downloadpoolstatus"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "downloadcertificate", "schema": {"input_fields": [{"enum_values": ["ADMIN", "CAPTIVE_PORTAL", "SFNT_CLIENT_CERT", "IFMAP_DHCP", "EAP_CA", "TAE_CA"], "is_array": false, "name": "certificate_usage", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "member", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["downloadcertificate"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "downloadcomplete", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["datagetcomplete"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "generatecsr", "schema": {"input_fields": [{"enum_values": ["ADMIN", "CAPTIVE_PORTAL", "SFNT_CLIENT_CERT", "IFMAP_DHCP"], "is_array": false, "name": "certificate_usage", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "key_size", "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "cn", "supports": "w", "type": ["string"]}, {"enum_values": ["SHA-1", "SHA-256", "SHA-384", "SHA-512"], "is_array": false, "name": "algorithm", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "org", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "org_unit", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "locality", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "state", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "country", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "email", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "w", "type": ["string"]}, {"is_array": true, "name": "subject_alternative_names", "schema": {"fields": [{"enum_values": ["EMAIL", "URI", "DNS", "IP"], "is_array": false, "name": "type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}]}, "supports": "w", "type": ["subjectalternativename"], "wapi_primitive": "struct"}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["generatecsr"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "generatedxlendpointcerts", "schema": {"input_fields": [], "output_fields": [{"is_array": false, "name": "client_certificate_token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "client_certificate_url", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ca_certificate_token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ca_certificate_url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["generatedxlendpointcerts"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "generatesafenetclientcert", "schema": {"input_fields": [{"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"enum_values": ["RSASHA1", "RSASHA256"], "is_array": false, "name": "algorithm", "supports": "w", "type": ["enum"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["generatesafenetclientcert"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "generateselfsignedcert", "schema": {"input_fields": [{"is_array": false, "name": "days_valid", "supports": "w", "type": ["uint"]}, {"enum_values": ["ADMIN", "CAPTIVE_PORTAL", "SFNT_CLIENT_CERT", "IFMAP_DHCP"], "is_array": false, "name": "certificate_usage", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "key_size", "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "cn", "supports": "w", "type": ["string"]}, {"enum_values": ["SHA-1", "SHA-256", "SHA-384", "SHA-512"], "is_array": false, "name": "algorithm", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "org", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "org_unit", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "locality", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "state", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "country", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "email", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "w", "type": ["string"]}, {"is_array": true, "name": "subject_alternative_names", "schema": {"fields": [{"enum_values": ["EMAIL", "URI", "DNS", "IP"], "is_array": false, "name": "type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}]}, "supports": "w", "type": ["subjectalternativename"], "wapi_primitive": "struct"}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["generateselfsignedcert"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "get_file_url", "schema": {"input_fields": [{"is_array": false, "name": "task_id", "supports": "w", "type": ["uint"]}], "output_fields": [{"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["get<PERSON><PERSON><PERSON>l"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "get_last_uploaded_atp_ruleset", "schema": {"input_fields": [], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["getlastuploadedruleset"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "get_log_files", "schema": {"input_fields": [{"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "msserver", "supports": "w", "type": ["string"]}, {"enum_values": ["ACTIVE", "BACKUP"], "is_array": false, "name": "node_type", "supports": "w", "type": ["enum"]}, {"enum_values": ["SYSLOG", "AUDITLOG", "MSMGMTLOG", "DELTALOG", "OUTBOUND"], "is_array": false, "name": "log_type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "include_rotated", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "endpoint", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["getlogfiles"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "get_support_bundle", "schema": {"input_fields": [{"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "log_files", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "core_files", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "rotate_log_files", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "cached_zone_data", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "recursive_cache_file", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "nm_snmp_logs", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "remote_url", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["supportbundle"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "getgriddata", "schema": {"input_fields": [{"enum_values": ["NTP_KEY_FILE", "SNMP_MIBS_FILE", "BACKUP", "BACKUP_DTC"], "is_array": false, "name": "type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "remote_url", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "nios_data", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "discovery_data", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "use_keys", "supports": "w", "type": ["bool"]}, {"enum_values": ["id_rsa", "id_ecdsa"], "is_array": false, "name": "key_type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "upload_keys", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "download_keys", "supports": "w", "type": ["bool"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["getgriddata"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "getleasehistoryfiles", "schema": {"input_fields": [{"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "start_time", "supports": "w", "type": ["timestamp"]}, {"is_array": false, "name": "end_time", "supports": "w", "type": ["timestamp"]}, {"is_array": false, "name": "remote_url", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["getleasehistoryfiles"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "getmemberdata", "schema": {"input_fields": [{"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"enum_values": ["NTP_KEY_FILE", "DNS_CFG", "DHCP_CFG", "DHCPV6_CFG", "RADIUS_CFG", "DNS_CACHE", "DNS_ACCEL_CACHE", "DHCP_EXPERT_MODE_CFG", "TRAFFIC_CAPTURE_FILE", "DNS_STATS", "DNS_RECURSING_CACHE"], "is_array": false, "name": "type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "remote_url", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["getmemberdata"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "getsafenetclientcert", "schema": {"input_fields": [{"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"enum_values": ["RSASHA1", "RSASHA256"], "is_array": false, "name": "algorithm", "supports": "w", "type": ["enum"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["getsafenetclientcert"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "read", "schema": {"input_fields": [{"is_array": false, "name": "_object", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "_filename", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "_return_fields", "supports": "w", "type": ["string"]}, {"enum_values": ["JSON", "XML", "ROWJSON", "ROWXML"], "is_array": false, "name": "_encoding", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "_max_results", "supports": "w", "type": ["int"]}, {"is_array": false, "name": "_gzipfile", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "_fileprefix", "supports": "w", "type": ["string"]}, {"enum_values": ["FILE_DISTRIBUTION", "LOCAL"], "is_array": false, "name": "_output_location", "supports": "w", "type": ["enum"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["read"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "restapi_template_export", "schema": {"input_fields": [{"is_array": false, "name": "restapi_template", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["restapitemplateexportparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "restapi_template_export_schema", "schema": {"input_fields": [{"enum_values": ["REST_ENDPOINT", "REST_EVENT"], "is_array": false, "name": "schema_type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "version", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["restapitemplateexportschemaparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "restapi_template_import", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "overwrite", "supports": "w", "type": ["bool"]}], "output_fields": [{"enum_values": ["FAILED", "SUCCESS"], "is_array": false, "name": "overall_status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "error_message", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["restapitemplateimportparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "restoredatabase", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"enum_values": ["NORMAL", "FORCED", "CLONE"], "is_array": false, "name": "mode", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "keep_grid_ip", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "nios_data", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "discovery_data", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "splunk_app_data", "supports": "w", "type": ["bool"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["restoredatabase"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "restoredtcconfig", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "forced", "supports": "w", "type": ["bool"]}], "output_fields": [{"is_array": false, "name": "aborted", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "warning_list", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["restoredtcconfig"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "set_captive_portal_file", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "filename", "supports": "w", "type": ["string"]}, {"enum_values": ["IMG_LOGO", "IMG_FOOTER", "IMG_HEADER", "AUP"], "is_array": false, "name": "type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "override", "supports": "w", "type": ["bool"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["setcaptiveportalfile"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "set_dhcp_leases", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"enum_values": ["KEEP_NEWEST", "KEEP_PREVIOUS", "REPLACE_PREVIOUS"], "is_array": false, "name": "lease_precedence", "supports": "w", "type": ["enum"]}, {"enum_values": ["ISC_LEASE"], "is_array": false, "name": "lease_format", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "network_view", "supports": "w", "type": ["string"]}, {"enum_values": ["IPv4", "IPv6"], "is_array": false, "name": "protocol", "supports": "w", "type": ["enum"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["setdhcpleases"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "set_downgrade_file", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["downgrade"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "set_last_uploaded_atp_ruleset", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "rw", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "rw", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["setlastuploadedruleset"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "set_upgrade_file", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["upgrade"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "setdiscoverycsv", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "network_view", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "merge_data", "supports": "w", "type": ["bool"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["setdiscoverycsv"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "setfiledest", "schema": {"input_fields": [{"enum_values": ["TFTP_FILE"], "is_array": false, "name": "type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "dest_path", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "extract", "supports": "w", "type": ["bool"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["setdatafiledest"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "setleasehistoryfiles", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["setleasehistoryfiles"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "setmemberdata", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"enum_values": ["DHCP_EXPERT_MODE_CFG"], "is_array": false, "name": "type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "member", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["setmemberdata"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "update_atp_ruleset", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "error_message", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["updateatprulesetparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "update_licenses", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["updatelicenses"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "uploadcertificate", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"enum_values": ["ADMIN", "CAPTIVE_PORTAL", "SFNT_CLIENT_CERT", "IFMAP_DHCP", "EAP_CA", "TAE_CA"], "is_array": false, "name": "certificate_usage", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "member", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["uploadcertificate"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "uploadinit", "schema": {"input_fields": [{"is_array": false, "name": "filename", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "url", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "token", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["datauploadinit"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "uploadserviceaccount", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "service_account_file", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["uploadserviceaccount"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}], "restrictions": ["create", "read", "delete", "update", "permissions", "global search", "csv"], "type": "fileop", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["update"], "fields": [{"enum_values": ["DETAILED", "BRIEF"], "is_array": false, "name": "audit_log_format", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "audit_to_syslog_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "consent_banner_setting", "schema": {"fields": [{"is_array": false, "name": "enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "message", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["grid:consentbannersetting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "control_ip_address", "schema": {"input_fields": [{"is_array": false, "name": "network_view", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "exclude", "supports": "w", "type": ["bool"]}, {"is_array": true, "name": "addresses", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["controlipaddress"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "dns_resolver_setting", "schema": {"fields": [{"is_array": true, "name": "resolvers", "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "search_domains", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:dnsresolver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "dscp", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "email_setting", "schema": {"fields": [{"is_array": false, "name": "enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "relay_enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "relay", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:email"], "wapi_primitive": "struct"}, {"is_array": false, "name": "empty_recycle_bin", "schema": {"input_fields": [], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["emptyparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "enable_gui_api_for_lan_vip", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_lom", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_member_redirect", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_recycle_bin", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "external_syslog_backup_servers", "schema": {"fields": [{"is_array": false, "name": "directory_path", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "password", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"enum_values": ["FTP", "SCP"], "is_array": false, "name": "protocol", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "username", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["extsyslogbackupserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "external_syslog_server_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "get_all_template_vendor_id", "schema": {"input_fields": [], "output_fields": [{"is_array": true, "name": "vendor_identifiers", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["getvendoridentifiersparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "http_proxy_server_setting", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "enable_proxy", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_content_inspection", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "verify_cname", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "username", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "password", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "certificate", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "enable_username_and_password", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:httpproxyserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "informational_banner_setting", "schema": {"fields": [{"is_array": false, "name": "enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "message", "supports": "rwu", "type": ["string"]}, {"enum_values": ["GREEN", "BLACK", "BLUE", "BROWN", "CYAN", "MAGENTA", "ORANGE", "PURPLE", "RED", "YELLOW"], "is_array": false, "name": "color", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["grid:informationalbannersetting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "is_grid_visualization_visible", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "member_upgrade", "schema": {"input_fields": [{"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"enum_values": ["UPGRADE", "REVERT"], "is_array": false, "name": "action", "supports": "w", "type": ["enum"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["memberupgrade"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "name", "searchable_by": "~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "nat_groups", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ntp_setting", "schema": {"fields": [{"is_array": false, "name": "enable_ntp", "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "ntp_servers", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_authentication", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ntp_key_number", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "preferred", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "burst", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "iburst", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["ntpserver"], "wapi_primitive": "struct"}, {"is_array": true, "name": "ntp_keys", "schema": {"fields": [{"is_array": false, "name": "number", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "string", "supports": "rwu", "type": ["string"]}, {"enum_values": ["MD5_ASCII", "DES_HEX", "DES_ASCII", "DES_NTP", "SHA1_ASCII"], "is_array": false, "name": "type", "supports": "rwu", "type": ["enum"]}]}, "supports": "rwu", "type": ["ntpkey"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ntp_kod", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "gm_local_ntp_stratum", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "local_ntp_stratum", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_default_stratum", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["grid:ntp"], "wapi_primitive": "struct"}, {"is_array": false, "name": "password_setting", "schema": {"fields": [{"is_array": false, "name": "password_min_length", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "num_lower_char", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "num_upper_char", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "num_numeric_char", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "num_symbol_char", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "chars_to_change", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "expire_days", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "reminder_days", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "force_reset_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "expire_enable", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:password"], "wapi_primitive": "struct"}, {"is_array": false, "name": "query_fqdn_on_member", "schema": {"input_fields": [{"is_array": false, "name": "fqdn", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "member", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "name_server", "supports": "w", "type": ["string"]}, {"enum_values": ["ANY", "A", "AAAA", "CNAME", "DNAME", "MX", "NAPTR", "NS", "PTR", "SRV", "TXT", "AXFR"], "is_array": false, "name": "record_type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "recursive_query", "supports": "w", "type": ["bool"]}], "output_fields": [{"enum_values": ["NOERROR", "FORMERR", "SERVFAIL", "NXDOMAIN", "NOTIMP", "REFUSED", "INTERNAL_ERROR"], "is_array": false, "name": "result", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "result_text", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dig_started", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "source_address", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["queryfqdnonmemberparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "scheduled_backup", "schema": {"fields": [{"enum_values": ["IDLE", "ROLL", "ROLLED", "TRIGGERED", "SCHEDULING", "IN_PROGRESS", "FINISHED", "FAILED", "ABORTED"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"enum_values": ["TRIGGER"], "is_array": false, "name": "execute", "supports": "w", "type": ["enum"]}, {"enum_values": ["NONE", "BACKUP", "RESTORE"], "is_array": false, "name": "operation", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["LOCAL", "TFTP", "FTP", "SCP"], "is_array": false, "name": "backup_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "keep_local_copy", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["WEEKLY", "DAILY", "HOURLY"], "is_array": false, "name": "backup_frequency", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["SATURDAY", "SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"], "is_array": false, "name": "weekday", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "hour_of_day", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "minutes_past_hour", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "username", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "password", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "backup_server", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "path", "supports": "rwu", "type": ["string"]}, {"enum_values": ["FTP", "SCP"], "is_array": false, "name": "restore_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "restore_server", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "restore_username", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "restore_password", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "restore_path", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "nios_data", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovery_data", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "splunk_app_data", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_keys", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["id_rsa", "id_ecdsa"], "is_array": false, "name": "key_type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "upload_keys", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "download_keys", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["scheduledbackup"], "wapi_primitive": "struct"}, {"is_array": false, "name": "secret", "standard_field": false, "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "security_banner_setting", "schema": {"fields": [{"enum_values": ["BLACK", "BLUE", "BROWN", "CYAN", "GREEN", "MAGENTA", "ORANGE", "PURPLE", "RED", "YELLOW"], "is_array": false, "name": "color", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["CONFIDENTIAL", "RESTRICTED", "SECRET", "TOP_SECRET", "UNCLASSIFIED"], "is_array": false, "name": "level", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "message", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:securitybanner"], "wapi_primitive": "struct"}, {"is_array": false, "name": "security_setting", "schema": {"fields": [{"is_array": false, "name": "audit_log_rolling_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "admin_access_items", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "http_redirect_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "login_banner_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "login_banner_text", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "remote_console_access_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "security_access_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "security_access_remote_console_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "session_timeout", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ssh_perm_enable", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "support_access_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "support_access_info", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:security"], "wapi_primitive": "struct"}, {"is_array": false, "name": "skip_member_upgrade", "schema": {"input_fields": [{"is_array": false, "name": "member", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["skipmemberupgrade"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "snmp_setting", "schema": {"fields": [{"is_array": true, "name": "engine_id", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "queries_community_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "queries_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "snmpv3_queries_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "snmpv3_queries_users", "schema": {"fields": [{"is_array": false, "name": "user", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "supports": "rwu", "type": ["queriesuser"], "wapi_primitive": "struct"}, {"is_array": false, "name": "snmpv3_traps_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "syscontact", "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "sysdescr", "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "syslocation", "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "sysname", "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "trap_receivers", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "user", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "supports": "rwu", "type": ["trapreceiver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "traps_community_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "traps_enable", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:snmp"], "wapi_primitive": "struct"}, {"is_array": false, "name": "start_discovery", "schema": {"input_fields": [{"is_array": true, "name": "objects", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["startdiscovery"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"enum_values": ["DAEMON", "LOCAL0", "LOCAL1", "LOCAL2", "LOCAL3", "LOCAL4", "LOCAL5", "LOCAL6", "LOCAL7"], "is_array": false, "name": "syslog_facility", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": true, "name": "syslog_servers", "schema": {"fields": [{"is_array": false, "name": "certificate", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "certificate_token", "supports": "wu", "type": ["string"]}, {"enum_values": ["TCP", "UDP", "STCP"], "is_array": false, "name": "connection_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"enum_values": ["ANY", "LAN", "MGMT"], "is_array": false, "name": "local_interface", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["ANY", "EXTERNAL", "INTERNAL"], "is_array": false, "name": "message_source", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["LAN", "MGMT", "HOSTNAME", "IP_HOSTNAME"], "is_array": false, "name": "message_node_id", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["ALERT", "CRIT", "DEBUG", "EMERG", "INFO", "NOTICE", "WARNING"], "is_array": false, "name": "severity", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["NON_CATEGORIZED", "ATP", "DNS_CLIENT", "DNS_CONFIG", "DNS_DATABASE", "DNS_DNSSEC", "DNS_GENERAL", "DNS_LAME_SERVERS", "DNS_NETWORK", "DNS_NOTIFY", "DNS_QUERIES", "DNS_QUERY_REWRITE", "DNS_RESOLVER", "DNS_RESPONSES", "DNS_RPZ", "DNS_SCAVENGING", "DNS_SECURITY", "DNS_UPDATE", "DNS_UPDATE_SECURITY", "DNS_XFER_IN", "DNS_XFER_OUT", "DTC_HEALTHD", "DTC_IDNSD", "DHCPD", "NTP", "FTPD", "TFTPD", "CLOUD_API", "MS_DNS_SERVER", "MS_CONNECT_STATUS", "MS_DNS_ZONE", "MS_DHCP_SERVER", "MS_DHCP_LEASE", "MS_DHCP_CLEAR_LEASE", "MS_SITES", "MS_AD_USERS", "AUTH_COMMON", "AUTH_NON_SYSTEM", "AUTH_UI_API", "AUTH_ACTIVE_DIRECTORY", "AUTH_RADIUS", "AUTH_TACACS", "AUTH_LDAP"], "is_array": true, "name": "category_list", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "only_category_list", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["syslogserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "syslog_size", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "test_syslog_backup_server_connection", "schema": {"input_fields": [{"is_array": false, "name": "syslog_backup_server", "schema": {"fields": [{"is_array": false, "name": "directory_path", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "password", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"enum_values": ["FTP", "SCP"], "is_array": false, "name": "protocol", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "username", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}]}, "supports": "w", "type": ["extsyslogbackupserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "member", "supports": "w", "type": ["string"]}], "output_fields": [{"enum_values": ["CANNOT_CONNECT", "TEST_OK"], "is_array": false, "name": "result", "supports": "r", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["testsyslogbackup"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "test_syslog_connection", "schema": {"input_fields": [{"is_array": false, "name": "syslog_server", "schema": {"fields": [{"is_array": false, "name": "certificate", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "certificate_token", "supports": "wu", "type": ["string"]}, {"enum_values": ["TCP", "UDP", "STCP"], "is_array": false, "name": "connection_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"enum_values": ["ANY", "LAN", "MGMT"], "is_array": false, "name": "local_interface", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["ANY", "EXTERNAL", "INTERNAL"], "is_array": false, "name": "message_source", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["LAN", "MGMT", "HOSTNAME", "IP_HOSTNAME"], "is_array": false, "name": "message_node_id", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["ALERT", "CRIT", "DEBUG", "EMERG", "INFO", "NOTICE", "WARNING"], "is_array": false, "name": "severity", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["NON_CATEGORIZED", "ATP", "DNS_CLIENT", "DNS_CONFIG", "DNS_DATABASE", "DNS_DNSSEC", "DNS_GENERAL", "DNS_LAME_SERVERS", "DNS_NETWORK", "DNS_NOTIFY", "DNS_QUERIES", "DNS_QUERY_REWRITE", "DNS_RESOLVER", "DNS_RESPONSES", "DNS_RPZ", "DNS_SCAVENGING", "DNS_SECURITY", "DNS_UPDATE", "DNS_UPDATE_SECURITY", "DNS_XFER_IN", "DNS_XFER_OUT", "DTC_HEALTHD", "DTC_IDNSD", "DHCPD", "NTP", "FTPD", "TFTPD", "CLOUD_API", "MS_DNS_SERVER", "MS_CONNECT_STATUS", "MS_DNS_ZONE", "MS_DHCP_SERVER", "MS_DHCP_LEASE", "MS_DHCP_CLEAR_LEASE", "MS_SITES", "MS_AD_USERS", "AUTH_COMMON", "AUTH_NON_SYSTEM", "AUTH_UI_API", "AUTH_ACTIVE_DIRECTORY", "AUTH_RADIUS", "AUTH_TACACS", "AUTH_LDAP"], "is_array": true, "name": "category_list", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "only_category_list", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}]}, "supports": "w", "type": ["syslogserver"], "wapi_primitive": "struct"}], "output_fields": [{"enum_values": ["CANNOT_CONNECT", "TEST_OK", "CERTIFICATE_IS_NOT_VALID"], "is_array": false, "name": "result", "supports": "r", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["testsyslog"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": true, "name": "threshold_traps", "schema": {"fields": [{"enum_values": ["DBObjects", "Disk", "Memory", "NetworkCapacity", "Rootfs", "Tmpfs", "CpuUsage", "Reporting", "ReportingVolume", "FDUsage", "ExtStorage", "SwapUsage", "TcpUdpFloodAlertRate", "TcpUdpFloodDropRate", "RecursiveClients", "ThreatProtectionTotalTraffic", "ThreatProtectionDroppedTraffic", "FastpathDroppedTraffic", "Fastpathmbuffdepletion", "IPAMUtilization", "RPZHitRate"], "is_array": false, "name": "trap_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "trap_reset", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "trap_trigger", "supports": "rwu", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["thresholdtrap"], "wapi_primitive": "struct"}, {"is_array": false, "name": "time_zone", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "token_usage_delay", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "trap_notifications", "schema": {"fields": [{"enum_values": ["Fan", "Disk", "Memory", "CPU", "MGM", "HSM", "<PERSON><PERSON>", "PowerSupply", "FTP", "TFTP", "HTTP", "NTP", "DNS", "DHCP", "RootFS", "TmpFS", "Database", "RAID", "HA", "HAOnCloud", "MSServer", "Backup", "Clear", "SNMP", "LCD", "SSH", "SerialConsole", "ENAT", "Network", "Cluster", "Controld", "OSPF", "OSPF6", "IFMAP", "BGP", "CaptivePortal", "DuplicateIP", "License", "System", "Syslog", "DiscoveryConflict", "Reporting", "FDUsage", "OCSPResponders", "DisconnectedGrid", "LDAPServers", "RIRSWIP", "SwapUsage", "Discovery", "ThreatProtection", "DNSIntegrityCheck", "DNSIntegrityCheckConnection", "CloudAPI", "RecursiveClients", "DiscoveryUnmanaged", "IPMIDevice", "CiscoISEServer", "ThreatInsight", "AnalyticsRPZ", "DNSAttack", "IMC", "Taxii", "BFD", "Outbound", "AutomatedTrafficCapture", "IPAMUtilization", "RPZHitRate"], "is_array": false, "name": "trap_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "enable_email", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_trap", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["trapnotification"], "wapi_primitive": "struct"}, {"is_array": false, "name": "upgrade", "schema": {"input_fields": [{"enum_values": ["UPGRADE_PAUSE", "UPGRADE_RESUME", "DISTRIBUTION_PAUSE", "DISTRIBUTION_RESUME", "DISTRIBUTION_START", "DISTRIBUTION_STOP", "DOWNGRADE", "REVERT", "UPGRADE", "UPGRADE_TEST_START", "UPGRADE_TEST_STOP", "UPLOAD"], "is_array": false, "name": "action", "supports": "w", "type": ["enum"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["gridupgrade"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "vpn_port", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["create", "delete", "permissions", "global search", "scheduling", "csv"], "type": "grid", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "issuer", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "serial", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "subject", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "valid_not_after", "searchable_by": "=!<>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "valid_not_before", "searchable_by": "=!<>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}], "restrictions": ["create", "update", "delete", "permissions", "global search", "scheduling", "csv"], "type": "grid:x509certificate", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "check_ldap_server_settings", "schema": {"input_fields": [{"is_array": false, "name": "ldap_server", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ANONYMOUS", "AUTHENTICATED"], "is_array": false, "name": "authentication_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "base_dn", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bind_password", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "bind_user_dn", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "disable", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["NONE", "SSL"], "is_array": false, "name": "encryption", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_mgmt_port", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["V2", "V3"], "is_array": false, "name": "version", "supports": "rwu", "type": ["enum"]}]}, "supports": "w", "type": ["ldap_server"], "wapi_primitive": "struct"}, {"is_array": false, "name": "timeout", "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "ldap_authservice", "supports": "w", "type": ["string"]}], "output_fields": [{"enum_values": ["SUCCESS", "FAILED"], "is_array": false, "name": "overall_status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "error_message", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["checkldapserversettings"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "ea_mapping", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "mapped_ea", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["ldap_eamapping"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ldap_group_attribute", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"enum_values": ["GROUP_ATTRIBUTE", "POSIX_GROUP"], "is_array": false, "name": "ldap_group_authentication_type", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "ldap_user_attribute", "standard_field": true, "supports": "rwu", "type": ["string"]}, {"enum_values": ["ORDERED_LIST", "ROUND_ROBIN"], "is_array": false, "name": "mode", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "recovery_interval", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"enum_values": ["BASE", "ONELEVEL", "SUBTREE"], "is_array": false, "name": "search_scope", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": true, "name": "servers", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ANONYMOUS", "AUTHENTICATED"], "is_array": false, "name": "authentication_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "base_dn", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bind_password", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "bind_user_dn", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "disable", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["NONE", "SSL"], "is_array": false, "name": "encryption", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_mgmt_port", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["V2", "V3"], "is_array": false, "name": "version", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["ldap_server"], "wapi_primitive": "struct"}, {"is_array": false, "name": "timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["scheduling", "csv"], "type": "ldap_auth_service", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "disabled", "standard_field": true, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "name", "standard_field": true, "supports": "r", "type": ["string"]}], "restrictions": ["delete", "create", "update", "permissions", "global search", "scheduling", "csv"], "type": "localuser:authservice", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": [], "fields": [{"is_array": false, "name": "active_position", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": true, "name": "additional_ip_list", "schema": {"fields": [{"is_array": false, "name": "anycast", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ipv4_network_setting", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "vlan_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "primary", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "lan_subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "lan_gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_dscp", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["setting:network"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ipv6_network_setting", "schema": {"fields": [{"is_array": false, "name": "enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "virtual_ip", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "cidr_prefix", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "auto_router_config_enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "vlan_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "primary", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_dscp", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["ipv6setting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_bgp", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ospf", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["LOOPBACK", "LAN_HA", "LAN2", "MGMT"], "is_array": false, "name": "interface", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["interface"], "wapi_primitive": "struct"}, {"is_array": false, "name": "capture_traffic_control", "schema": {"input_fields": [{"enum_values": ["START", "STOP"], "is_array": false, "name": "action", "supports": "w", "type": ["enum"]}, {"enum_values": ["ALL", "HA", "LAN1", "LAN2", "MGMT"], "is_array": false, "name": "interface", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "seconds_to_run", "supports": "w", "type": ["uint"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["membercapturecontrolparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "capture_traffic_status", "schema": {"input_fields": [], "output_fields": [{"enum_values": ["STOPPED", "RUNNING", "UNKNOWN"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "file_exists", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "file_size", "supports": "r", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["membercapturestatusparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"enum_values": ["IPV4", "IPV6", "BOTH"], "is_array": false, "name": "config_addr_type", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "create_token", "schema": {"input_fields": [], "output_fields": [{"is_array": true, "name": "pnode_tokens", "schema": {"fields": [{"is_array": false, "name": "physical_oid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "token_exp_date", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "token", "supports": "r", "type": ["string"]}]}, "supports": "r", "type": ["member:p<PERSON><PERSON><PERSON>"], "wapi_primitive": "struct"}]}, "standard_field": false, "supports": "rwu", "type": ["pnodetokenoperation"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "dns_resolver_setting", "overridden_by": "use_dns_resolver_setting", "schema": {"fields": [{"is_array": true, "name": "resolvers", "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "search_domains", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:dnsresolver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "email_setting", "overridden_by": "use_email_setting", "schema": {"fields": [{"is_array": false, "name": "enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "relay_enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "relay", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:email"], "wapi_primitive": "struct"}, {"is_array": false, "name": "enable_ha", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "enable_member_redirect", "overridden_by": "use_enable_member_redirect", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ro_api_access", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "external_syslog_backup_servers", "overridden_by": "use_external_syslog_backup_servers", "schema": {"fields": [{"is_array": false, "name": "directory_path", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "password", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"enum_values": ["FTP", "SCP"], "is_array": false, "name": "protocol", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "username", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["extsyslogbackupserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "external_syslog_server_enable", "overridden_by": "use_syslog_proxy_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "host_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv4_address", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "ipv6_address", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "ipv6_setting", "schema": {"fields": [{"is_array": false, "name": "enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "virtual_ip", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "cidr_prefix", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "auto_router_config_enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "vlan_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "primary", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_dscp", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["ipv6setting"], "wapi_primitive": "struct"}, {"is_array": true, "name": "ipv6_static_routes", "schema": {"fields": [{"is_array": false, "name": "enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "virtual_ip", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "cidr_prefix", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "auto_router_config_enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "vlan_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "primary", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_dscp", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["ipv6setting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "is_dscp_capable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "lan2_enabled", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "lan2_port_setting", "schema": {"fields": [{"is_array": false, "name": "virtual_router_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "network_setting", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "vlan_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "primary", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "lan_subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "lan_gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_dscp", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["setting:network"], "wapi_primitive": "struct"}, {"is_array": false, "name": "v6_network_setting", "schema": {"fields": [{"is_array": false, "name": "enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "virtual_ip", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "cidr_prefix", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "auto_router_config_enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "vlan_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "primary", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_dscp", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["ipv6setting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "nic_failover_enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "nic_failover_enable_primary", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["lan2portsetting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "master_candidate", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "member_service_communication", "schema": {"fields": [{"enum_values": ["GRID", "GRID_BACKUP", "NTP", "REPORTING", "REPORTING_BACKUP", "OCSP", "MAIL", "AD"], "is_array": false, "name": "service", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["IPV4", "IPV6"], "is_array": false, "name": "type", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["FORCE", "PREFER"], "is_array": false, "name": "option", "supports": "r", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["memberservicecommunication"], "wapi_primitive": "struct"}, {"is_array": false, "name": "mgmt_port_setting", "schema": {"fields": [{"is_array": false, "name": "enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "vpn_enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "security_access_enabled", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["mgmtportsetting"], "wapi_primitive": "struct"}, {"is_array": true, "name": "node_info", "schema": {"fields": [{"is_array": true, "name": "service_status", "schema": {"fields": [{"is_array": false, "name": "description", "supports": "r", "type": ["string"]}, {"enum_values": ["FAILED", "WARNING", "WORKING", "INACTIVE"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"enum_values": ["JOIN_STATUS", "NODE_STATUS", "DISK_USAGE", "ENET_LAN", "ENET_LAN2", "ENET_HA", "ENET_MGMT", "LCD", "REPLICATION", "DB_OBJECT", "RAID_SUMMARY", "RAID_DISK1", "RAID_DISK2", "RAID_DISK3", "RAID_DISK4", "RAID_DISK5", "RAID_DISK6", "RAID_DISK7", "RAID_DISK8", "FAN1", "FAN2", "FAN3", "FAN4", "FAN5", "FAN6", "FAN7", "FAN8", "POWER_SUPPLY", "POWER1", "POWER2", "POWER3", "POWER4", "NTP_SYNC", "CPU1_TEMP", "CPU2_TEMP", "SYS_TEMP", "RAID_BATTERY", "CPU_USAGE", "OSPF", "OSPF6", "BGP", "BFD", "AUTH_NAMED", "CORE_FILES", "MGM_SERVICE", "SUBGRID_CONN", "NETWORK_CAPACITY", "DISK_SIZE", "SFP_MGMT", "SFP_LAN", "SFP_HA", "SFP_LAN2", "SNIC_UTIL", "SNIC_PCB_TEMP", "SNIC_CHIP_TEMP", "SNIC_CORE_UTIL", "FP_CORE_UTIL", "CPU_USAGE", "MEMORY", "EXTERNAL_STORAGE", "SWAP_USAGE", "DISCOVERY_CAPACITY", "PASSIVE_HA_CONNECTIVITY", "VPN_CERT"], "is_array": false, "name": "service", "supports": "r", "type": ["enum"]}]}, "supports": "r", "type": ["servicestatus"], "wapi_primitive": "struct"}, {"is_array": false, "name": "physical_oid", "supports": "r", "type": ["string"]}, {"enum_values": ["ACTIVE", "PASSIVE", "NOT_CONFIGURED"], "is_array": false, "name": "ha_status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "hwid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "hwmodel", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "hwtype", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "paid_nios", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "mgmt_network_setting", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "vlan_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "primary", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "lan_subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "lan_gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_dscp", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["setting:network"], "wapi_primitive": "struct"}, {"is_array": false, "name": "lan_ha_port_setting", "schema": {"fields": [{"is_array": false, "name": "mgmt_lan", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "mgmt_ipv6addr", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ha_ip_address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "lan_port_setting", "schema": {"fields": [{"is_array": false, "name": "auto_port_setting_enabled", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["10", "100", "1000"], "is_array": false, "name": "speed", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["HALF", "FULL"], "is_array": false, "name": "duplex", "supports": "rwu", "type": ["enum"]}]}, "supports": "rwu", "type": ["physicalportsetting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ha_port_setting", "schema": {"fields": [{"is_array": false, "name": "auto_port_setting_enabled", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["10", "100", "1000"], "is_array": false, "name": "speed", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["HALF", "FULL"], "is_array": false, "name": "duplex", "supports": "rwu", "type": ["enum"]}]}, "supports": "rwu", "type": ["physicalportsetting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ha_cloud_attribute", "supports": "rwu", "type": ["string"]}]}, "supports": "rwu", "type": ["lanhaportsetting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "mgmt_physical_setting", "schema": {"fields": [{"is_array": false, "name": "auto_port_setting_enabled", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["10", "100", "1000"], "is_array": false, "name": "speed", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["HALF", "FULL"], "is_array": false, "name": "duplex", "supports": "rwu", "type": ["enum"]}]}, "supports": "rwu", "type": ["physicalportsetting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "lan2_physical_setting", "schema": {"fields": [{"is_array": false, "name": "auto_port_setting_enabled", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["10", "100", "1000"], "is_array": false, "name": "speed", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["HALF", "FULL"], "is_array": false, "name": "duplex", "supports": "rwu", "type": ["enum"]}]}, "supports": "rwu", "type": ["physicalportsetting"], "wapi_primitive": "struct"}, {"is_array": false, "name": "nat_external_ip", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "v6_mgmt_network_setting", "schema": {"fields": [{"is_array": false, "name": "enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "virtual_ip", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "cidr_prefix", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "auto_router_config_enabled", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "vlan_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "primary", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_dscp", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["ipv6setting"], "wapi_primitive": "struct"}, {"enum_values": ["KVM", "VMWARE", "AWS", "XEN", "HYPERV", "AZURE", "PHYSICAL", "UNKNOWN"], "is_array": false, "name": "hwplatform", "supports": "r", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["nodeinfo"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ntp_setting", "schema": {"fields": [{"is_array": false, "name": "enable_ntp", "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "ntp_servers", "overridden_by": "use_ntp_servers", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_authentication", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ntp_key_number", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "preferred", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "burst", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "iburst", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["ntpserver"], "wapi_primitive": "struct"}, {"is_array": true, "name": "ntp_keys", "overridden_by": "use_ntp_keys", "schema": {"fields": [{"is_array": false, "name": "number", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "string", "supports": "rwu", "type": ["string"]}, {"enum_values": ["MD5_ASCII", "DES_HEX", "DES_ASCII", "DES_NTP", "SHA1_ASCII"], "is_array": false, "name": "type", "supports": "rwu", "type": ["enum"]}]}, "supports": "rwu", "type": ["ntpkey"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ntp_kod", "overridden_by": "use_ntp_kod", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_external_ntp_servers", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_local_ntp_stratum", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "local_ntp_stratum", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_default_stratum", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ntp_servers", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ntp_keys", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ntp_kod", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["member:ntp"], "wapi_primitive": "struct"}, {"is_array": false, "name": "passive_ha_arp_enabled", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"enum_values": ["INFOBLOX", "RIVERBED", "CISCO", "IBVM", "VNIOS"], "is_array": false, "name": "platform", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "pre_provisioning", "schema": {"fields": [{"is_array": true, "name": "hardware_info", "schema": {"fields": [{"enum_values": ["IB-2210", "IB-4030-10GE", "PT-2205", "IB-2215", "IB-4030", "PT-4000-10GE", "IB-4020", "PT-2200", "IB-4025", "IB-V825", "PT-4000", "IB-2220", "IB-100", "IB-2225", "IB-4015", "IB-1415", "IB-V2225", "IB-1410", "IB-V2215", "IB-V1425", "IB-4010", "IB-RSP2", "IB-VNIOS", "PT-1405", "IB-820", "IB-V4025", "PT-1400", "IB-V4015", "IB-825", "IB-1420", "IB-810", "IB-V1415", "IB-1425", "IB-815", "IB-V815", "IB-FLEX"], "is_array": false, "name": "hwtype", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["Rev1", "Rev2", "IB-VM-100", "IB-VM-810", "IB-VM-820", "IB-VM-RSP", "IB-VM-1410", "IB-VM-1420", "IB-VM-2210", "IB-VM-2220", "IB-VM-4010", "CP-V800", "CP-V1400", "CP-V2200"], "is_array": false, "name": "hwmodel", "supports": "rwu", "type": ["enum"]}]}, "supports": "rwu", "type": ["preprovisionhardware"], "wapi_primitive": "struct"}, {"enum_values": ["rpz", "fireeye", "ms_management", "sw_tp", "tp_sub", "nios", "dtc", "vnios", "cloud_api", "dns", "enterprise", "dhcp"], "is_array": true, "name": "licenses", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["preprovision"], "wapi_primitive": "struct"}, {"is_array": false, "name": "preserve_if_owns_delegation", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "read_token", "schema": {"input_fields": [], "output_fields": [{"is_array": true, "name": "pnode_tokens", "schema": {"fields": [{"is_array": false, "name": "physical_oid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "token_exp_date", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "token", "supports": "r", "type": ["string"]}]}, "supports": "r", "type": ["member:p<PERSON><PERSON><PERSON>"], "wapi_primitive": "struct"}]}, "standard_field": false, "supports": "rwu", "type": ["pnodetokenoperation"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "remote_console_access_enable", "overridden_by": "use_remote_console_access_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "router_id", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["uint"]}, {"is_array": true, "name": "service_status", "schema": {"fields": [{"is_array": false, "name": "description", "supports": "r", "type": ["string"]}, {"enum_values": ["FAILED", "WARNING", "WORKING", "INACTIVE", "UNKNOWN", "OFFLINE"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"enum_values": ["DHCP", "DNS", "DFP", "DOT_DOH", "NTP", "TFTP", "HTTP_FILE_DIST", "FTP", "CAPTIVE_PORTAL", "HSM", "REPORTING", "DNS_CACHE_ACCELERATION", "DISCOVERY", "ATP", "CLOUD_API", "IMC", "IMC_DCA_BWL", "ANALYTICS", "TAXII"], "is_array": false, "name": "service", "supports": "r", "type": ["enum"]}]}, "standard_field": false, "supports": "r", "type": ["memberservice<PERSON><PERSON>"], "wapi_primitive": "struct"}, {"enum_values": ["ALL_V4", "ALL_V6", "CUSTOM"], "is_array": false, "name": "service_type_configuration", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "snmp_setting", "overridden_by": "use_snmp_setting", "schema": {"fields": [{"is_array": true, "name": "engine_id", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "queries_community_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "queries_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "snmpv3_queries_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "snmpv3_queries_users", "schema": {"fields": [{"is_array": false, "name": "user", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "supports": "rwu", "type": ["queriesuser"], "wapi_primitive": "struct"}, {"is_array": false, "name": "snmpv3_traps_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "syscontact", "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "sysdescr", "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "syslocation", "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "sysname", "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "trap_receivers", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "user", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "supports": "rwu", "type": ["trapreceiver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "traps_community_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "traps_enable", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:snmp"], "wapi_primitive": "struct"}, {"is_array": true, "name": "static_routes", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "vlan_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "primary", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "lan_subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "lan_gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_dscp", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:network"], "wapi_primitive": "struct"}, {"is_array": false, "name": "support_access_enable", "overridden_by": "use_support_access_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "support_access_info", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "syslog_proxy_setting", "overridden_by": "use_syslog_proxy_setting", "schema": {"fields": [{"is_array": false, "name": "enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "tcp_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "tcp_port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "udp_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "udp_port", "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "client_acls", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}]}, "standard_field": false, "supports": "rwu", "type": ["setting:syslogproxy"], "wapi_primitive": "struct"}, {"is_array": true, "name": "syslog_servers", "overridden_by": "use_syslog_proxy_setting", "schema": {"fields": [{"is_array": false, "name": "certificate", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "certificate_token", "supports": "wu", "type": ["string"]}, {"enum_values": ["TCP", "UDP", "STCP"], "is_array": false, "name": "connection_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"enum_values": ["ANY", "LAN", "MGMT"], "is_array": false, "name": "local_interface", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["ANY", "EXTERNAL", "INTERNAL"], "is_array": false, "name": "message_source", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["LAN", "MGMT", "HOSTNAME", "IP_HOSTNAME"], "is_array": false, "name": "message_node_id", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["ALERT", "CRIT", "DEBUG", "EMERG", "INFO", "NOTICE", "WARNING"], "is_array": false, "name": "severity", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["NON_CATEGORIZED", "ATP", "DNS_CLIENT", "DNS_CONFIG", "DNS_DATABASE", "DNS_DNSSEC", "DNS_GENERAL", "DNS_LAME_SERVERS", "DNS_NETWORK", "DNS_NOTIFY", "DNS_QUERIES", "DNS_QUERY_REWRITE", "DNS_RESOLVER", "DNS_RESPONSES", "DNS_RPZ", "DNS_SCAVENGING", "DNS_SECURITY", "DNS_UPDATE", "DNS_UPDATE_SECURITY", "DNS_XFER_IN", "DNS_XFER_OUT", "DTC_HEALTHD", "DTC_IDNSD", "DHCPD", "NTP", "FTPD", "TFTPD", "CLOUD_API", "MS_DNS_SERVER", "MS_CONNECT_STATUS", "MS_DNS_ZONE", "MS_DHCP_SERVER", "MS_DHCP_LEASE", "MS_DHCP_CLEAR_LEASE", "MS_SITES", "MS_AD_USERS", "AUTH_COMMON", "AUTH_NON_SYSTEM", "AUTH_UI_API", "AUTH_ACTIVE_DIRECTORY", "AUTH_RADIUS", "AUTH_TACACS", "AUTH_LDAP"], "is_array": true, "name": "category_list", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "only_category_list", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["syslogserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "syslog_size", "overridden_by": "use_syslog_proxy_setting", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "threshold_traps", "overridden_by": "use_threshold_traps", "schema": {"fields": [{"enum_values": ["DBObjects", "Disk", "Memory", "NetworkCapacity", "Rootfs", "Tmpfs", "CpuUsage", "Reporting", "ReportingVolume", "FDUsage", "ExtStorage", "SwapUsage", "TcpUdpFloodAlertRate", "TcpUdpFloodDropRate", "RecursiveClients", "ThreatProtectionTotalTraffic", "ThreatProtectionDroppedTraffic", "FastpathDroppedTraffic", "Fastpathmbuffdepletion", "IPAMUtilization", "RPZHitRate"], "is_array": false, "name": "trap_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "trap_reset", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "trap_trigger", "supports": "rwu", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["thresholdtrap"], "wapi_primitive": "struct"}, {"is_array": false, "name": "time_zone", "overridden_by": "use_time_zone", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "trap_notifications", "overridden_by": "use_trap_notifications", "schema": {"fields": [{"enum_values": ["Fan", "Disk", "Memory", "CPU", "MGM", "HSM", "<PERSON><PERSON>", "PowerSupply", "FTP", "TFTP", "HTTP", "NTP", "DNS", "DHCP", "RootFS", "TmpFS", "Database", "RAID", "HA", "HAOnCloud", "MSServer", "Backup", "Clear", "SNMP", "LCD", "SSH", "SerialConsole", "ENAT", "Network", "Cluster", "Controld", "OSPF", "OSPF6", "IFMAP", "BGP", "CaptivePortal", "DuplicateIP", "License", "System", "Syslog", "DiscoveryConflict", "Reporting", "FDUsage", "OCSPResponders", "DisconnectedGrid", "LDAPServers", "RIRSWIP", "SwapUsage", "Discovery", "ThreatProtection", "DNSIntegrityCheck", "DNSIntegrityCheckConnection", "CloudAPI", "RecursiveClients", "DiscoveryUnmanaged", "IPMIDevice", "CiscoISEServer", "ThreatInsight", "AnalyticsRPZ", "DNSAttack", "IMC", "Taxii", "BFD", "Outbound", "AutomatedTrafficCapture", "IPAMUtilization", "RPZHitRate"], "is_array": false, "name": "trap_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "enable_email", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_trap", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["trapnotification"], "wapi_primitive": "struct"}, {"is_array": false, "name": "upgrade_group", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_dns_resolver_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_dscp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_email_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_member_redirect", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_external_syslog_backup_servers", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_remote_console_access_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_snmp_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_support_access_enable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_syslog_proxy_setting", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_threshold_traps", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_time_zone", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_trap_notifications", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_v4_vrrp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "vip_setting", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "vlan_id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "primary", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dscp", "overridden_by": "use_dscp", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "lan_subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "lan_gateway", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_dscp", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["setting:network"], "wapi_primitive": "struct"}, {"is_array": false, "name": "vpn_mtu", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["scheduling", "csv"], "type": "member", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["NOT_EXPIRED", "EXPIRED", "PERMANENT", "EXPIRING_SOON", "EXPIRING_VERY_SOON", "DELETED"], "is_array": false, "name": "expiration_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "expiry_date", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "hwid", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "key", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"enum_values": ["Static", "Dynamic", "<PERSON>g", "Gridwide"], "is_array": false, "name": "kind", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "limit", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"enum_values": ["NONE", "LEASES", "MODEL", "TIER"], "is_array": false, "name": "limit_context", "standard_field": false, "supports": "r", "type": ["enum"]}, {"enum_values": ["ANYCAST", "CLOUD", "CLOUD_API", "DCA", "DDI_TRIAL", "DHCP", "DISCOVERY", "DNS", "DNSQRW", "DNS_CACHE_ACCEL", "DTC", "FIREEYE", "FLEX_GRID_ACTIVATION", "FLEX_GRID_ACTIVATION_MS", "FREQ_CONTROL", "GRID", "GRID_MAINTENANCE", "IPAM", "IPAM_FREEWARE", "LDAP", "LOAD_BALANCER", "MGM", "MSMGMT", "NIOS", "NIOS_MAINTENANCE", "NTP", "OEM", "QRD", "REPORTING", "REPORTING_SUB", "RPZ", "SECURITY_ECOSYSTEM", "SW_TP", "TAE", "TFTP", "THREAT_INSIGHT", "TP", "TP_SUB", "VNIOS"], "is_array": false, "name": "type", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}], "restrictions": ["create", "update", "global search", "scheduling", "csv"], "type": "member:license", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "alternate_version", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "completed_members", "standard_field": false, "supports": "r", "type": ["uint"]}, {"enum_values": ["DETACHING", "ATTACHING", "DETACHED", "ATTACHED", "DETACHED_FAILED", "ATTACHED_FAILED", "SNAPSHOT_FAILED"], "is_array": false, "name": "connection_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "download_join_file", "schema": {"input_fields": [], "output_fields": [{"is_array": false, "name": "url", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "token", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["downloadjoinfile"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "generate_join_info", "schema": {"input_fields": [], "output_fields": [{"is_array": false, "name": "join_token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "expiration", "supports": "r", "type": ["timestamp"]}]}, "standard_field": false, "supports": "rwu", "type": ["generatejoininfo"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "gm_host_name", "searchable_by": "~:=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "gm_virtual_ip", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "id", "standard_field": true, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "is_strict_delegate_mode", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "join_error_string", "standard_field": false, "supports": "r", "type": ["string"]}, {"enum_values": ["INITIAL", "JOINED", "JOIN_FAILED"], "is_array": false, "name": "join_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "join_token", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "join_token_expiration", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_sync_time", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_upgrade_date", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "name", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FAILED", "WARNING", "WORKING", "INACTIVE", "UNKNOWN", "OFFLINE"], "is_array": false, "name": "overall_service_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"enum_values": ["UNKNOWN", "FAILED", "WARNING", "WORKING"], "is_array": false, "name": "sync_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "total_members", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "total_network_containers", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "total_networks", "standard_field": false, "supports": "r", "type": ["uint"]}, {"enum_values": ["NONE", "DEFAULT", "DISTRIBUTING", "DISTRIBUTING_COMPLETE", "DISTRIBUTING_ENDED", "DISTRIBUTING_FAILED", "DISTRIBUTING_PAUSED", "DOWNGRADING_COMPLETE", "DOWNGRADING_FAILED", "REVERTING", "REVERTING_COMPLETE", "REVERTING_FAILED", "TEST_UPGRADING", "UPGRADING", "UPGRADING_COMPLETE", "UPGRADING_FAILED", "UPLOADED", "DISTRIBUTION_SCHEDULED", "UPGRADE_SCHEDULED", "UPGRADING_PAUSED"], "is_array": false, "name": "upgrade_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "upgrade_status_time", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "user_mapping_control", "schema": {"input_fields": [{"is_array": true, "name": "sub_grids", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "ignore", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "auth_user_name", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "password", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": true, "name": "success", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "failure", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "ignored", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["usermappingcontrol"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "version", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["csv"], "type": "mgm:grid", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "enable_ha", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "r", "type": ["extattr"]}, {"is_array": false, "name": "grid", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"enum_values": ["ONLINE", "SYNCHRONIZING", "OFFLINE", "NA"], "is_array": false, "name": "ha_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "host_name", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "is_master", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "master_candidate_enabled", "standard_field": false, "supports": "r", "type": ["bool"]}, {"enum_values": ["FAILED", "WARNING", "WORKING", "INACTIVE", "OFFLINE"], "is_array": false, "name": "member_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"enum_values": ["CISCO", "IBVM", "INFOBLOX", "RIVERBED", "VNIOS"], "is_array": false, "name": "member_type", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": true, "name": "service_status", "schema": {"fields": [{"is_array": false, "name": "description", "supports": "r", "type": ["string"]}, {"enum_values": ["FAILED", "WARNING", "WORKING", "INACTIVE", "UNKNOWN", "OFFLINE"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"enum_values": ["DHCP", "DNS", "DFP", "DOT_DOH", "NTP", "TFTP", "HTTP_FILE_DIST", "FTP", "CAPTIVE_PORTAL", "HSM", "REPORTING", "DNS_CACHE_ACCELERATION", "DISCOVERY", "ATP", "CLOUD_API", "IMC", "IMC_DCA_BWL", "ANALYTICS", "TAXII"], "is_array": false, "name": "service", "supports": "r", "type": ["enum"]}]}, "standard_field": false, "supports": "r", "type": ["memberservice<PERSON><PERSON>"], "wapi_primitive": "struct"}, {"is_array": false, "name": "uptime", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "virtual_ip", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "virtual_oid", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["create", "update", "delete", "permissions", "scheduling", "csv"], "type": "mgm:member", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "grid", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "monitors", "schema": {"fields": [{"enum_values": ["OVERALL", "DHCP", "DNS", "NTP", "TFTP", "HTTP_FILE_DIST", "FTP", "CAPTIVE_PORTAL", "BGP", "BOOT_TIME", "HA_STATUS", "REPORTING"], "is_array": false, "name": "monitor_type", "supports": "r", "type": ["enum"]}, {"enum_values": ["UNKNOWN", "WORKING", "WARNING", "FAILED", "INACTIVE", "OFFLINE"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "description", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["mgm:monitorentry"], "wapi_primitive": "struct"}, {"is_array": false, "name": "virtual_node", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "delete", "permissions", "global search", "scheduling", "csv"], "type": "mgm:monitordata", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "searchable_by": "~=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "contains_address", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "grid", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "is_container", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "is_ipv4", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "netmask", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "resize", "schema": {"input_fields": [{"is_array": false, "name": "prefix", "supports": "w", "type": ["uint"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["resize"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}], "restrictions": ["delete", "csv"], "type": "mgm:network", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "r", "type": ["extattr"]}, {"is_array": false, "name": "grid", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "id", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "~:=", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "delete", "permissions", "scheduling", "csv"], "type": "mgm:networkview", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auth_admin_group", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "auth_user_name", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "grid", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ignore", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "user_name", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "delete", "permissions", "global search", "scheduling", "csv"], "type": "mgm:usermapping", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "access_list", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exploded_access_list", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "r", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"func_type": "download", "is_array": false, "name": "validate_acl_items", "schema": {"input_fields": [], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["validateaclitems"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}], "restrictions": ["scheduling", "csv"], "type": "<PERSON><PERSON><PERSON>", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["csv"], "type": "natgroup", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "group", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "object", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"enum_values": ["DENY", "READ", "WRITE"], "is_array": false, "name": "permission", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"enum_values": ["CLUSTER", "MEMBER", "MEMBER_CLOUD", "SUB_GRID", "SUB_GRID_NETWORK_VIEW_PARENT", "SG_NETWORK_VIEW", "SG_IPV4_NETWORK", "SG_IPV6_NETWORK", "MSSERVER", "VIEW", "ZONE", "A", "AAAA", "ALIAS", "CNAME", "DNAME", "MX", "PTR", "SRV", "TXT", "HOST", "BULKHOST", "NAPTR", "TLSA", "CAA", "Unknown", "SHARED_RECORD_GROUP", "SHARED_A", "SHARED_AAAA", "SHARED_MX", "SHARED_SRV", "SHARED_TXT", "SHARED_CNAME", "NETWORK_VIEW", "NETWORK", "IPV6_NETWORK", "NETWORK_CONTAINER", "IPV6_NETWORK_CONTAINER", "RANGE", "IPV6_RANGE", "FIXED_ADDRESS", "IPV6_FIXED_ADDRESS", "ROAMING_HOST", "DHCP_MAC_FILTER", "SHARED_NETWORK", "IPV6_SHARED_NETWORK", "TEMPLATE", "IPV6_TEMPLATE", "NETWORK_TEMPLATE", "IPV6_NETWORK_TEMPLATE", "RANGE_TEMPLATE", "IPV6_RANGE_TEMPLATE", "FIXED_ADDRESS_TEMPLATE", "IPV6_FIXED_ADDRESS_TEMPLATE", "OPTION_SPACE", "RESTORABLE_OPERATION", "CSV_IMPORT_TASK", "DHCP_LEASE_HISTORY", "IPV6_DHCP_LEASE_HISTORY", "GRID_FILE_DIST_PROPERTIES", "MEMBER_FILE_DIST_PROPERTIES", "FILE_DIST_DIRECTORY", "HSM_GROUP", "GRID_AAA_PROPERTIES", "AAA_EXTERNAL_SERVICE", "NETWORK_DISCOVERY", "SCHEDULE_TASK", "MS_SUPERSCOPE", "MEMBER_DNS_PROPERTIES", "MEMBER_DHCP_PROPERTIES", "MEMBER_SECURITY_PROPERTIES", "MEMBER_ANALYTICS_PROPERTIES", "RESTART_SERVICE", "GRID_DNS_PROPERTIES", "GRID_DHCP_PROPERTIES", "GRID_REPORTING_PROPERTIES", "GRID_SECURITY_PROPERTIES", "IMC_PROPERTIES", "IMC_SITE", "IMC_AVP", "GRID_ANALYTICS_PROPERTIES", "RULESET", "DNS64_SYNTHESIS_GROUP", "DASHBOARD_TASK", "REPORTING_DASHBOARD", "REPORTING_SEARCH", "OCSP_SERVICE", "CA_CERTIFICATE", "RESPONSE_POLICY_ZONE", "RESPONSE_POLICY_RULE", "DHCP_FINGERPRINT", "DEFINED_ACL", "FIREEYE_PUBLISH_ALERT", "HOST_ADDRESS", "IPV6_HOST_ADDRESS", "PORT_CONTROL", "DEVICE", "KERBEROS_KEY", "BFD_TEMPLATE", "MS_ADSITES_DOMAIN", "IDNS_LBDN", "IDNS_LBDN_RECORD", "IDNS_POOL", "IDNS_SERVER", "IDNS_TOPOLOGY", "IDNS_MONITOR", "IDNS_CERTIFICATE", "IDNS_GEO_IP", "TENANT", "RECLAMATION", "SUPER_HOST", "ADD_A_RR_WITH_EMPTY_HOSTNAME", "DATACOLLECTOR_CLUSTER", "DELETED_OBJS_INFO_TRACKING", "SAML_AUTH_SERVICE", "VLAN_VIEW", "VLAN_RANGE", "VLAN_OBJECTS"], "is_array": false, "name": "resource_type", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "role", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["global search", "scheduling", "csv"], "type": "permission", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "acct_retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "acct_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "auth_retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "auth_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "cache_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "check_radius_server_settings", "schema": {"input_fields": [{"is_array": false, "name": "radius_server", "schema": {"fields": [{"is_array": false, "name": "acct_port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "auth_port", "supports": "rwu", "type": ["uint"]}, {"enum_values": ["CHAP", "PAP"], "is_array": false, "name": "auth_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "disable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "shared_secret", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "use_accounting", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_mgmt_port", "supports": "rwu", "type": ["bool"]}]}, "supports": "w", "type": ["radius:server"], "wapi_primitive": "struct"}, {"is_array": false, "name": "auth_timeout", "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "acct_timeout", "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "radius_authservice", "supports": "w", "type": ["string"]}], "output_fields": [{"enum_values": ["SUCCESS", "FAILED"], "is_array": false, "name": "overall_status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "error_message", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["checkradiusserversettings"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_cache", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"enum_values": ["HUNT_GROUP", "ROUND_ROBIN"], "is_array": false, "name": "mode", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "name", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "recovery_interval", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "servers", "schema": {"fields": [{"is_array": false, "name": "acct_port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "auth_port", "supports": "rwu", "type": ["uint"]}, {"enum_values": ["CHAP", "PAP"], "is_array": false, "name": "auth_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "disable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "shared_secret", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "use_accounting", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_mgmt_port", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["radius:server"], "wapi_primitive": "struct"}], "restrictions": ["scheduling", "csv"], "type": "radius:authservice", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "idp", "schema": {"fields": [{"enum_values": ["OKTA", "AZURE_SSO", "SHIBBOLETH_SSO", "PING_IDENTITY", "OTHER"], "is_array": false, "name": "idp_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "metadata_url", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "metadata_token", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "groupname", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "sso_redirect_url", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["saml:idp"], "wapi_primitive": "struct"}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "session_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["scheduling", "csv"], "type": "saml:authservice", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["APPROVED", "NONE", "PENDING", "REJECTED"], "is_array": false, "name": "approval_status", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "approver", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "approver_comment", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "automatic_restart", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "changed_objects", "schema": {"fields": [{"enum_values": ["Convert IPv4 Lease", "Delete", "Restart Services", "Add", "Convert IPv6 Lease", "Lock/Unlock Zone", "Reset Grid", "Configure Grid", "Restart Services", "Network Discovery", "Upgrade Grid", "Modify"], "is_array": false, "name": "action", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "object_type", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "properties", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["changedobject"], "wapi_primitive": "struct"}, {"is_array": false, "name": "changed_objects.action", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "changed_objects.name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "changed_objects.object_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "changed_objects.type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": true, "name": "dependent_tasks", "standard_field": false, "supports": "r", "type": ["scheduledtask"]}, {"is_array": false, "name": "execute_now", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": true, "name": "execution_details", "standard_field": false, "supports": "r", "type": ["string"]}, {"enum_values": ["NONE", "WARNING"], "is_array": false, "name": "execution_details_type", "standard_field": false, "supports": "r", "type": ["enum"]}, {"enum_values": ["COMPLETED", "FAILED", "PENDING", "WAITING_EXECUTION"], "is_array": false, "name": "execution_status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "execution_time", "searchable_by": "<=>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "is_network_insight_task", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "member", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "predecessor_task", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "re_execute_task", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "scheduled_time", "searchable_by": "<=>", "standard_field": false, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "submit_time", "searchable_by": "<=>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "submitter", "searchable_by": "=~:", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "submitter_comment", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "task_id", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["uint"]}, {"enum_values": ["OBJECT_CHANGE", "PORT_CONTROL"], "is_array": false, "name": "task_type", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "ticket_number", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["create", "permissions", "global search", "scheduling", "csv"], "type": "scheduledtask", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "duid", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "fqdn", "searchable_by": ":~", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "mac_address", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"enum_values": ["ad_auth_service", "admingroup", "adminrole", "adminuser", "certificate:authservice", "ldap_auth_service", "member", "mgm:grid", "mgm:member", "mgm:network", "mgm:networkview", "<PERSON><PERSON><PERSON>", "natgroup", "radius:authservice", "saml:authservice", "snmpuser", "tacacsplus:authservice"], "is_array": false, "name": "objtype", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["enum"]}, {"is_array": false, "name": "search_string", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "search", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "group_by_values", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}]}, "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["smartfolder:groupbyvalue"], "wapi_primitive": "struct"}, {"is_array": true, "name": "group_bys", "schema": {"fields": [{"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"enum_values": ["NORMAL", "EXTATTR"], "is_array": false, "name": "value_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "enable_grouping", "supports": "rwu", "type": ["bool"]}]}, "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["smartfolder:groupby"], "wapi_primitive": "struct"}, {"is_array": true, "name": "query_items", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"enum_values": ["NORMAL", "EXTATTR"], "is_array": false, "name": "field_type", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["EQ", "GT", "GEQ", "LT", "LEQ", "HAS_VALUE", "BEGINS_WITH", "ENDS_WITH", "CONTAINS", "RELATIVE_DATE", "RISES_BY", "DROPS_BY", "INHERITANCE_STATE_EQUALS", "IP_ADDR_WITHIN", "SUFFIX_MATCH", "MATCH_EXPR"], "is_array": false, "name": "operator", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "op_match", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["STRING", "INTEGER", "BOOLEAN", "DATE", "ENUM", "EMAIL", "URL", "OBJTYPE"], "is_array": false, "name": "value_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "value", "schema": {"fields": [{"is_array": false, "name": "value_integer", "supports": "rwu", "type": ["int"]}, {"is_array": false, "name": "value_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value_date", "supports": "rwu", "type": ["timestamp"]}, {"is_array": false, "name": "value_boolean", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["smartfolder:queryitemvalue"], "wapi_primitive": "struct"}]}, "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["smartfolder:queryitem"], "wapi_primitive": "struct"}, {"is_array": false, "name": "resource", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "smart_folder", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "value", "schema": {"fields": [{"is_array": false, "name": "value_integer", "supports": "rwu", "type": ["int"]}, {"is_array": false, "name": "value_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value_date", "supports": "rwu", "type": ["timestamp"]}, {"is_array": false, "name": "value_boolean", "supports": "rwu", "type": ["bool"]}]}, "standard_field": true, "supports": "r", "type": ["smartfolder:queryitemvalue"], "wapi_primitive": "struct"}, {"enum_values": ["STRING", "INTEGER", "BOOLEAN", "DATE", "ENUM", "EMAIL", "URL", "OBJTYPE"], "is_array": false, "name": "value_type", "standard_field": true, "supports": "r", "type": ["enum"]}], "restrictions": ["create", "update", "delete", "global search", "scheduling", "csv"], "type": "smartfolder:children", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "group_bys", "schema": {"fields": [{"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"enum_values": ["NORMAL", "EXTATTR"], "is_array": false, "name": "value_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "enable_grouping", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["smartfolder:groupby"], "wapi_primitive": "struct"}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "query_items", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"enum_values": ["NORMAL", "EXTATTR"], "is_array": false, "name": "field_type", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["EQ", "GT", "GEQ", "LT", "LEQ", "HAS_VALUE", "BEGINS_WITH", "ENDS_WITH", "CONTAINS", "RELATIVE_DATE", "RISES_BY", "DROPS_BY", "INHERITANCE_STATE_EQUALS", "IP_ADDR_WITHIN", "SUFFIX_MATCH", "MATCH_EXPR"], "is_array": false, "name": "operator", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "op_match", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["STRING", "INTEGER", "BOOLEAN", "DATE", "ENUM", "EMAIL", "URL", "OBJTYPE"], "is_array": false, "name": "value_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "value", "schema": {"fields": [{"is_array": false, "name": "value_integer", "supports": "rwu", "type": ["int"]}, {"is_array": false, "name": "value_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value_date", "supports": "rwu", "type": ["timestamp"]}, {"is_array": false, "name": "value_boolean", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["smartfolder:queryitemvalue"], "wapi_primitive": "struct"}]}, "standard_field": false, "supports": "rwu", "type": ["smartfolder:queryitem"], "wapi_primitive": "struct"}, {"is_array": false, "name": "save_as", "schema": {"input_fields": [{"is_array": false, "name": "is_shortcut", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "name", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "global_flag", "supports": "w", "type": ["bool"]}], "output_fields": [{"is_array": false, "name": "result", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["smartfoldersaveasparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}], "restrictions": ["global search", "csv"], "type": "smartfolder:global", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "group_bys", "schema": {"fields": [{"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"enum_values": ["NORMAL", "EXTATTR"], "is_array": false, "name": "value_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "enable_grouping", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["smartfolder:groupby"], "wapi_primitive": "struct"}, {"is_array": false, "name": "is_shortcut", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["bool"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "query_items", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"enum_values": ["NORMAL", "EXTATTR"], "is_array": false, "name": "field_type", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["EQ", "GT", "GEQ", "LT", "LEQ", "HAS_VALUE", "BEGINS_WITH", "ENDS_WITH", "CONTAINS", "RELATIVE_DATE", "RISES_BY", "DROPS_BY", "INHERITANCE_STATE_EQUALS", "IP_ADDR_WITHIN", "SUFFIX_MATCH", "MATCH_EXPR"], "is_array": false, "name": "operator", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "op_match", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["STRING", "INTEGER", "BOOLEAN", "DATE", "ENUM", "EMAIL", "URL", "OBJTYPE"], "is_array": false, "name": "value_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "value", "schema": {"fields": [{"is_array": false, "name": "value_integer", "supports": "rwu", "type": ["int"]}, {"is_array": false, "name": "value_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value_date", "supports": "rwu", "type": ["timestamp"]}, {"is_array": false, "name": "value_boolean", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["smartfolder:queryitemvalue"], "wapi_primitive": "struct"}]}, "standard_field": false, "supports": "rwu", "type": ["smartfolder:queryitem"], "wapi_primitive": "struct"}, {"is_array": false, "name": "save_as", "schema": {"input_fields": [{"is_array": false, "name": "is_shortcut", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "name", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "global_flag", "supports": "w", "type": ["bool"]}], "output_fields": [{"is_array": false, "name": "result", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["smartfoldersaveasparams"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}], "restrictions": ["global search", "csv"], "type": "smartfolder:personal", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "authentication_password", "standard_field": false, "supports": "wu", "type": ["string"]}, {"enum_values": ["NONE", "MD5", "SHA"], "is_array": false, "name": "authentication_protocol", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "privacy_password", "standard_field": false, "supports": "wu", "type": ["string"]}, {"enum_values": ["NONE", "DES", "AES"], "is_array": false, "name": "privacy_protocol", "standard_field": false, "supports": "rwu", "type": ["enum"]}], "restrictions": ["scheduling", "csv"], "type": "snmpuser", "version": "2.10.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "acct_retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "acct_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "auth_retries", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "auth_timeout", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "check_tacacsplus_server_settings", "schema": {"input_fields": [{"is_array": false, "name": "tacacsplus_server", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "shared_secret", "supports": "wu", "type": ["string"]}, {"enum_values": ["ASCII", "PAP", "CHAP"], "is_array": false, "name": "auth_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "disable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_mgmt_port", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_accounting", "supports": "rwu", "type": ["bool"]}]}, "supports": "w", "type": ["tacacsplus:server"], "wapi_primitive": "struct"}, {"is_array": false, "name": "auth_timeout", "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "acct_timeout", "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "tacacsplus_authservice", "supports": "w", "type": ["string"]}], "output_fields": [{"enum_values": ["SUCCESS", "FAILED"], "is_array": false, "name": "overall_status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "error_message", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "auth_time", "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "acct_time", "supports": "r", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["checktacacsplusserversettings"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "comment", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "name", "searchable_by": "~:=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "servers", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "port", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "shared_secret", "supports": "wu", "type": ["string"]}, {"enum_values": ["ASCII", "PAP", "CHAP"], "is_array": false, "name": "auth_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "disable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_mgmt_port", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_accounting", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["tacacsplus:server"], "wapi_primitive": "struct"}], "restrictions": ["scheduling", "csv"], "type": "tacacsplus:authservice", "version": "2.10.1", "wapi_primitive": "object"}]