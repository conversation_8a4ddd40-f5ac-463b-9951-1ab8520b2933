source s_external {
    tcp ( port(666) max_connections(100) );
    udp ( port(555) );  };

destination d_internal_1 {  udp ( "*******" port(514) localip("0.0.0.0") template("<$PRI>$DATE [VIRTUAL_IP] $MSGHDR$MSG\n") template_escape(no) persist-name("dinternal1")); };
destination d_external_1 {  udp ( "*******" port(514) localip("0.0.0.0") ); };
destination d_internal_2 {  tcp ( "*******" port(2222) localip("0.0.0.0") template("<$PRI>$DATE [VIRTUAL_IP] $MSGHDR$MSG\n") template_escape(no) persist-name("dinternal2")); };
destination d_external_3 {  udp ( "*******" port(3333) localip("[VIRTUAL_IP]") ); };

log { source(s_syslogng); filter(f_debug); destination(d_internal_1); };
log { source(s_syslogng); filter(f_err); destination(d_internal_2); };

log { source(s_external); filter(f_debug); destination(d_external_1); };
log { source(s_external); filter(f_alert); destination(d_external_3); };
