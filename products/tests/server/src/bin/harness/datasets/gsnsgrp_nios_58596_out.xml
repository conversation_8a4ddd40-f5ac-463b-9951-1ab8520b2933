<DATABASE NAME="onedb" VERSION="6.12.11-294244" MD5="8aa43bbbceb051633ab04b98961c293a" SCHEMA-MD5="9481b3a8b93224959ddba5794a2684ef" INT-VERSION="6.12.6011-294244">
    <!--Note: Most object are stripped of properties not relevant for the test. -->
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dns_properties"/>
        <PROPERTY NAME="cluster" VALUE="0"/>
        <PROPERTY NAME="srgs_count" VALUE="1"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
        <PROPERTY NAME="virtual_oid" VALUE="0"/>
        <PROPERTY NAME="host_name" VALUE="gm3.shell.com"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell"/>
        <PROPERTY NAME="name" VALUE="africa-me"/>
        <PROPERTY NAME="primary_type" VALUE="Microsoft"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="africa-me.shell.com"/>
        <PROPERTY NAME="display_name" VALUE="africa-me"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="zone" VALUE="._default"/>
        <PROPERTY NAME="name" VALUE="com.shell"/>
        <PROPERTY NAME="primary_type" VALUE="Microsoft"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="shell.com"/>
        <PROPERTY NAME="display_name" VALUE="com.shell"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell.africa-me"/>
        <PROPERTY NAME="name" VALUE="bad"/>
        <PROPERTY NAME="primary_type" VALUE="Grid"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="bad.africa-me.shell.com"/>
        <PROPERTY NAME="assigned_ns_group" VALUE="nsg3"/>
        <PROPERTY NAME="display_name" VALUE="bad"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell.africa-me"/>
        <PROPERTY NAME="name" VALUE="good"/>
        <PROPERTY NAME="primary_type" VALUE="Grid"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="good.africa-me.shell.com"/>
        <PROPERTY NAME="display_name" VALUE="good"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="w2k3r2s32qa34"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.w2k3r2s32qa34"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PROPERTY NAME="manually_created" VALUE="false"/>
        <PROPERTY NAME="dns_service_status" VALUE=""/>
        <PROPERTY NAME="shared_record" VALUE="false"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell"/>
        <PROPERTY NAME="name" VALUE="africa-me"/>
        <PROPERTY NAME="dname" VALUE="w2k3r2s32qa34"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.w2k3r2s32qa34"/>
        <PROPERTY NAME="display_name" VALUE="africa-me"/>
        <PROPERTY NAME="manually_created" VALUE="false"/>
        <PROPERTY NAME="dns_service_status" VALUE=""/>
        <PROPERTY NAME="shared_record" VALUE="false"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell.africa-me"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="w2k3r2s32qa34"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.w2k3r2s32qa34"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PROPERTY NAME="manually_created" VALUE="false"/>
        <PROPERTY NAME="dns_service_status" VALUE=""/>
        <PROPERTY NAME="shared_record" VALUE="false"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell.africa-me"/>
        <PROPERTY NAME="name" VALUE="bad"/>
        <PROPERTY NAME="dname" VALUE="gm3.shell.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.shell.gm3"/>
        <PROPERTY NAME="display_name" VALUE="bad"/>
        <PROPERTY NAME="manually_created" VALUE="false"/>
        <PROPERTY NAME="dns_service_status" VALUE=""/>
        <PROPERTY NAME="shared_record" VALUE="false"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell.africa-me"/>
        <PROPERTY NAME="name" VALUE="good"/>
        <PROPERTY NAME="dname" VALUE="gm3.shell.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.shell.gm3"/>
        <PROPERTY NAME="display_name" VALUE="good"/>
        <PROPERTY NAME="manually_created" VALUE="false"/>
        <PROPERTY NAME="dns_service_status" VALUE=""/>
        <PROPERTY NAME="shared_record" VALUE="false"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell.africa-me.good"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="gm3.shell.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.shell.gm3"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PROPERTY NAME="manually_created" VALUE="false"/>
        <PROPERTY NAME="dns_service_status" VALUE="0"/>
        <PROPERTY NAME="shared_record" VALUE="false"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ns_group"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="cluster_dns_properties" VALUE="0"/>
        <PROPERTY NAME="group_name" VALUE="nsg3"/>
        <PROPERTY NAME="group_type" VALUE="AUTH"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ns_group_grid_primary"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="ns_group" VALUE="nsg3"/>
        <PROPERTY NAME="grid_member" VALUE="0"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ms_primary_server"/>
        <PROPERTY NAME="is_master" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell.africa-me"/>
        <PROPERTY NAME="ms_server" VALUE="0"/>
        <PROPERTY NAME="ns_ip" VALUE="***************"/>
        <PROPERTY NAME="ns_name" VALUE="w2k3r2s32qa34"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ms_primary_server"/>
        <PROPERTY NAME="is_master" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell"/>
        <PROPERTY NAME="ms_server" VALUE="0"/>
        <PROPERTY NAME="ns_ip" VALUE="***************"/>
        <PROPERTY NAME="ns_name" VALUE="w2k3r2s32qa34"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_grid_primary"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell.africa-me.good"/>
        <PROPERTY NAME="grid_member" VALUE="0"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="zone_type" VALUE="SharedRecordGroup"/>
        <PROPERTY NAME="zone" VALUE=".srg_root"/>
        <PROPERTY NAME="name" VALUE="1"/>
        <PROPERTY NAME="fqdn" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="1"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.srg"/>
        <PROPERTY NAME="zone" VALUE=".srg_root.1"/>
        <PROPERTY NAME="name" VALUE="nsg3"/>
        <PROPERTY NAME="ns_group" VALUE="nsg3"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="shared_record" VALUE="true"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE=".srg_root.1"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="gm3.shell.com"/>
        <PROPERTY NAME="reversed_dname" VALUE=".srg_root.com.shell.gm3"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="dns_service_status" VALUE="0"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.srg_zone_linking"/>
        <PROPERTY NAME="srg" VALUE=".srg_root.1"/>
        <PROPERTY NAME="zone" VALUE="._default.com.shell.africa-me.bad"/>
        <PROPERTY NAME="delegated_name" VALUE=""/>
    </OBJECT>
</DATABASE>
