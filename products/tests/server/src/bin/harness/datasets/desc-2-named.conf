acl "loopbacknet" { *********/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";
	recursion no;			// Do not provide recursive service
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

// Root server hints (not used)
// zone "." { type hint; file "root.hint"; };

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	file "localhost.rev";
	notify no;
};

zone "test.com" {
	type master;
	file "desc-test.com.db";
	notify no;
	allow-update { "loopbacknet"; };
};

zone "42.in-addr.arpa" {
	type master;
	file "desc-42.db";
	notify no;
};

zone "d.c.b.a.ip6.arpa" {
	type master;
	file "desc-ip6.db";
	notify no;
};
