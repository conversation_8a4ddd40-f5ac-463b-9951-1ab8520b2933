<DATABASE NAME="onedb" VERSION="MDXMLTEST">
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_txt"/>
        <PROPERTY NAME="zone" VALUE="._default.com.nios80186"/>
        <PROPERTY NAME="name" VALUE="txt-test1"/>
        <PROPERTY NAME="txt_string" VALUE="&quot;text1 with ; semicolon&quot;"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_txt"/>
        <PROPERTY NAME="zone" VALUE="._default.com.nios80186"/>
        <PROPERTY NAME="name" VALUE="txt-test2"/>
        <PROPERTY NAME="txt_string" VALUE="&quot;text2 with ; semicolon&quot;"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_naptr"/>
        <PROPERTY NAME="order" VALUE="10"/>
        <PROPERTY NAME="preference" VALUE="10"/>
        <PROPERTY NAME="zone" VALUE="._default.com.nios80186"/>
        <PROPERTY NAME="name" VALUE="naptr-test1"/>
        <PROPERTY NAME="flag" VALUE="U"/>
        <PROPERTY NAME="services" VALUE="http+E2U"/>
        <PROPERTY NAME="replacement" VALUE="."/>
        <PROPERTY NAME="regexp" VALUE="regexp1 with ; semicolon"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_naptr"/>
        <PROPERTY NAME="order" VALUE="10"/>
        <PROPERTY NAME="preference" VALUE="10"/>
        <PROPERTY NAME="zone" VALUE="._default.com.nios80186"/>
        <PROPERTY NAME="name" VALUE="naptr-test2"/>
        <PROPERTY NAME="flag" VALUE="U"/>
        <PROPERTY NAME="services" VALUE="http+E2U"/>
        <PROPERTY NAME="replacement" VALUE="."/>
        <PROPERTY NAME="regexp" VALUE="regexp2 with ; semicolon"/>
    </OBJECT>
</DATABASE>
