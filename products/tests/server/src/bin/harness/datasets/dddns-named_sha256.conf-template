// Inspired by examples in chapter 3 of the BIND ARM

key "key1" {
  algorithm HMAC-SHA256;
  secret "CdhtOnXxkZeuHP5saggSnA==";
};

key "key2" {
  algorithm HMAC-SHA256;
  secret "MxuDAibo+rXNf9Cp9I4V6g==";
};

// This key is not used in any match-clients list
key "key-not-in-use" {
  algorithm HMAC-SHA256;
  secret "xo9Qz0Tq435DBzZvXYVRsA==";
};

// Include a generated key-file (slightly closer to the "real thing")
include "/tmp/dhcp_updater.key";

acl "loopbacknet" { *********/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";
	recursion no;			// Do not provide recursive service
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};


view "VIEW_NAME" {

// Root server hints
zone "." { type hint; file "root.hint"; };

// Reverse mapping for loopback address
zone "127.in-addr.arpa" {
	type master;
	file "127.in-addr.arpa";
	notify no;
	allow-update { key DHCP_UPDATER; };
};

zone "test.infoblox.com" {
	type master;
	notify no;
	file "test.infoblox.com.db";
	allow-update { key DHCP_UPDATER; };
};

zone " test escape .infoblox.com" {
	type master;
	notify no;
	file "test-escape.infoblox.com.db";
	allow-update { "loopbacknet"; };
};

zone "1.168.192.in-addr.arpa" {
	type master;
	notify no;
	file "1.168.192.in-addr.arpa";
	allow-update { "loopbacknet"; };
};

zone "b0003.grainger.com" {
	type master;
// Don't notify; the NS records are probably bogus for this test zone
	notify no;
	file "b0003.grainger.com";
};

zone "bulk.infoblox.com" {
        type master;
        notify no;
        file "bulk.infoblox.com.db";
	allow-update { "loopbacknet"; };
};

zone "2.168.192.in-addr.arpa" {
        type master;
        notify no;
        file "2.168.192.in-addr.arpa";
};

zone "3.in-addr.arpa" {
	type master;
	notify no;
	file "3.in-addr.arpa";
	allow-update { "loopbacknet"; };
};

zone "10.in-addr.arpa" {
	type master;
	notify no;
	file "10.in-addr.arpa";
};

zone "apo.infoblox.com" {
	type master;
	notify no;
	file "apo.infoblox.com.db";
};

};
