header-network,address*,netmask*,always_update_dns,boot_file,boot_server,broadcast_address,comment,ddns_domainname,ddns_ttl,deny_bootp,dhcp_members,disabled,domain_name,domain_name_servers,enable_ddns,enable_option81,enable_pxe_lease_time,enable_threshold_email_warnings,enable_threshold_snmp_warnings,enable_thresholds,generate_hostname,ignore_client_requested_options,is_authoritative,lease_time,network_view,next_server,pxe_lease_time,range_high_water_mark,range_high_water_mark_reset,range_low_water_mark,range_low_water_mark_reset,recycle_leases,routers,threshold_email_addresses,update_dns_on_lease_renewal,update_static_leases,zone_associations
network,*******,*********,,,,,test,,,,,True,,,,,,False,False,,,,,,,,,95,85,0,10,,,,,,
