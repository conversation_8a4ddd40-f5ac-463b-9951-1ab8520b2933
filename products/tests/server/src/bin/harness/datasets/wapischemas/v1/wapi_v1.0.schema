[{"requested_version": "1.0", "supported_objects": ["ipv4address", "ipv6address", "ipv6network", "ipv6networkcontainer", "ipv6range", "macfilteraddress", "network", "networkcontainer", "networkview", "range", "record:a", "record:aaaa", "record:cname", "record:host", "record:host_ipv4addr", "record:mx", "record:ptr", "record:srv", "record:txt", "search"]}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "dhcp_client_identifier", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "fingerprint", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ip_address", "searchable_by": "=<>", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "is_conflict", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["bool"]}, {"is_array": false, "name": "lease_state", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "mac_address", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "names", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "objects", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "types", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "usage", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "username", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "permissions", "global search", "scheduling", "csv"], "type": "ipv4address", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "duid", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ip_address", "searchable_by": "<=>", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "is_conflict", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["bool"]}, {"is_array": false, "name": "lease_state", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "names", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "objects", "standard_field": true, "supports": "r", "type": ["string"]}, {"enum_values": ["USED", "UNUSED"], "is_array": false, "name": "status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}, {"is_array": true, "name": "types", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "usage", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "permissions", "global search", "scheduling", "csv"], "type": "ipv6address", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_enable_option_fqdn", "overridden_by": "use_ddns_enable_option_fqdn", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_server_always_updates", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_ttl", "overridden_by": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "domain_name", "overridden_by": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "domain_name_servers", "overridden_by": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ifmap_publishing", "overridden_by": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "members", "standard_field": false, "supports": "rwu", "type": ["dhcpmember"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "preferred_lifetime", "overridden_by": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_enable_option_fqdn", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "valid_lifetime", "overridden_by": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["zoneassociation"]}], "restrictions": [], "type": "ipv6network", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "wsr", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["zoneassociation"]}], "restrictions": ["csv"], "type": "ipv6networkcontainer", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "end_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exclude", "standard_field": false, "supports": "rwu", "type": ["exclusionrange"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv6_end_prefix", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6_prefix_bits", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "ipv6_start_prefix", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "member", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["dhcpmember"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "server_association_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "start_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": [], "type": "ipv6range", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "authentication_time", "searchable_by": "=<>!", "standard_field": true, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "expiration_time", "searchable_by": "=<>!", "standard_field": true, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "filter", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field1", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field2", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field3", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field4", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_email", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_first_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_last_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_middle_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_phone", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "is_registered_user", "standard_field": true, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "mac", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "never_expires", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "reserved_for_infoblox", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "username", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["permissions"], "type": "macfilteraddress", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "authority", "overridden_by": "use_authority", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_server_always_updates", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_ttl", "overridden_by": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ddns_update_fixed_addresses", "overridden_by": "use_ddns_update_fixed_addresses", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_use_option81", "overridden_by": "use_ddns_use_option81", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "email_list", "overridden_by": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_dhcp_thresholds", "overridden_by": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_email_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ifmap_publishing", "overridden_by": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_snmp_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "high_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "high_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ignore_dhcp_option_list_request", "overridden_by": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "lease_scavenge_time", "overridden_by": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["int"]}, {"is_array": false, "name": "low_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "low_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "members", "standard_field": false, "supports": "rwu", "type": ["dhcpmember", "msdhcpserver"]}, {"is_array": false, "name": "netmask", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_authority", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_update_fixed_addresses", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_use_option81", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["zoneassociation"]}], "restrictions": [], "type": "network", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "wsr", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["zoneassociation"]}], "restrictions": ["csv"], "type": "networkcontainer", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "is_default", "searchable_by": "=", "standard_field": true, "supports": "sr", "type": ["bool"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["scheduling", "csv"], "type": "networkview", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "always_update_dns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "authority", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_all_clients", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "email_list", "overridden_by": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_dhcp_thresholds", "overridden_by": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_email_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ifmap_publishing", "overridden_by": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_snmp_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "end_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exclude", "standard_field": false, "supports": "rwu", "type": ["exclusionrange"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "failover_association", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "fingerprint_filter_rules", "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"]}, {"is_array": false, "name": "high_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "high_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ignore_dhcp_option_list_request", "overridden_by": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "is_split_scope", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "known_clients", "overridden_by": "use_known_clients", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "lease_scavenge_time", "overridden_by": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["int"]}, {"is_array": true, "name": "logic_filter_rules", "standard_field": false, "supports": "rwu", "type": ["logicfilterrule"]}, {"is_array": false, "name": "low_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "low_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "mac_filter_rules", "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"]}, {"is_array": false, "name": "member", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["dhcpmember"]}, {"is_array": true, "name": "ms_options", "standard_field": false, "supports": "rwu", "type": ["msdhcpoption"]}, {"is_array": false, "name": "ms_server", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["msdhcpserver"]}, {"is_array": true, "name": "nac_filter_rules", "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"]}, {"is_array": false, "name": "name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "option_filter_rules", "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "relay_agent_filter_rules", "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"]}, {"enum_values": ["NONE", "MEMBER", "FAILOVER", "MS_SERVER", "MS_FAILOVER"], "is_array": false, "name": "server_association_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "split_member", "standard_field": false, "supports": "w", "type": ["msdhcpserver"]}, {"is_array": false, "name": "split_scope_exclusion_percent", "standard_field": false, "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "start_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "unknown_clients", "overridden_by": "use_unknown_clients", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_authority", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_known_clients", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_unknown_clients", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": [], "type": "range", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:a", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv6addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:aaaa", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "canonical", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:cname", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "aliases", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "configure_for_dns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "ipv4addrs", "standard_field": true, "supports": "rwu", "type": ["record:host_ipv4addr"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "rrset_order", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:host", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "configure_for_dhcp", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "enable_pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "host", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ignore_client_requested_options", "overridden_by": "use_ignore_client_requested_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "last_queried", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "match_client", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "network", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "pxe_lease_time", "overridden_by": "use_pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_client_requested_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": ["create", "delete", "permissions", "scheduling", "csv"], "type": "record:host_ipv4addr", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "mail_exchanger", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "preference", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:mx", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ptrdname", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:ptr", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "port", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "priority", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "target", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "weight", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:srv", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extensible_attributes", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "text", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:txt", "version": "1.0"}, {"cloud_additional_restrictions": ["all"], "fields": [], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "search", "version": "1.0"}]