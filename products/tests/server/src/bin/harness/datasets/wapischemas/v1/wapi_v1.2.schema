[{"requested_version": "1.2", "supported_objects": ["fixedaddress", "grid", "ipv4address", "ipv6address", "ipv6fixedaddress", "ipv6network", "ipv6networkcontainer", "ipv6range", "lease", "macfilteraddress", "member", "<PERSON><PERSON><PERSON>", "network", "networkcontainer", "networkview", "range", "record:a", "record:aaaa", "record:cname", "record:host", "record:host_ipv4addr", "record:host_ipv6addr", "record:mx", "record:ptr", "record:srv", "record:txt", "restartservicestatus", "scheduledtask", "search", "view", "zone_auth", "zone_delegated", "zone_forward", "zone_stub"]}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "agent_circuit_id", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "agent_remote_id", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "always_update_dns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "client_identifier_prepend_zero", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_hostname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dhcp_client_identifier", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ignore_dhcp_option_list_request", "overridden_by": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "mac", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"enum_values": ["MAC_ADDRESS", "CLIENT_ID", "RESERVED", "CIRCUIT_ID", "REMOTE_ID"], "is_array": false, "name": "match_client", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": true, "name": "ms_options", "standard_field": false, "supports": "rwu", "type": ["msdhcpoption"]}, {"is_array": false, "name": "ms_server", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["msdhcpserver"]}, {"is_array": false, "name": "name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": [], "type": "fixedaddress", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "grid", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "dhcp_client_identifier", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "fingerprint", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ip_address", "searchable_by": "=<>", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "is_conflict", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["bool"]}, {"is_array": false, "name": "lease_state", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "mac_address", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "names", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "objects", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "types", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "usage", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "username", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "permissions", "global search", "scheduling", "csv"], "type": "ipv4address", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "duid", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ip_address", "searchable_by": "<=>", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "is_conflict", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["bool"]}, {"is_array": false, "name": "lease_state", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "names", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "objects", "standard_field": true, "supports": "r", "type": ["string"]}, {"enum_values": ["USED", "UNUSED"], "is_array": false, "name": "status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}, {"is_array": true, "name": "types", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "usage", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "permissions", "global search", "scheduling", "csv"], "type": "ipv6address", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["ADDRESS", "PREFIX", "BOTH"], "is_array": false, "name": "address_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "domain_name", "overridden_by": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "domain_name_servers", "overridden_by": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "duid", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv6addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6prefix", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6prefix_bits", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "preferred_lifetime", "overridden_by": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "valid_lifetime", "overridden_by": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": [], "type": "ipv6fixedaddress", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_enable_option_fqdn", "overridden_by": "use_ddns_enable_option_fqdn", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_server_always_updates", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_ttl", "overridden_by": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "domain_name", "overridden_by": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "domain_name_servers", "overridden_by": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ifmap_publishing", "overridden_by": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "members", "standard_field": false, "supports": "rwu", "type": ["dhcpmember"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "preferred_lifetime", "overridden_by": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_enable_option_fqdn", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "valid_lifetime", "overridden_by": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["zoneassociation"]}], "restrictions": [], "type": "ipv6network", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "wsr", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["zoneassociation"]}], "restrictions": ["csv"], "type": "ipv6networkcontainer", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "end_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exclude", "standard_field": false, "supports": "rwu", "type": ["exclusionrange"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv6_end_prefix", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6_prefix_bits", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "ipv6_start_prefix", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "member", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["dhcpmember"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "server_association_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "start_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": [], "type": "ipv6range", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "searchable_by": "=<>~!", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "billing_class", "standard_field": false, "supports": "r", "type": ["string"]}, {"enum_values": ["ABANDONED", "ACTIVE", "BACKUP", "DECLINED", "EXPIRED", "FREE", "OFFERED", "RELEASED", "RESET", "STATIC"], "is_array": false, "name": "binding_state", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "client_hostname", "searchable_by": "=~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "cltt", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "ends", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "hardware", "searchable_by": "=~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ipv6_duid", "searchable_by": "=~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ipv6_iaid", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ipv6_preferred_lifetime", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "ipv6_prefix_bits", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["uint"]}, {"is_array": false, "name": "network", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "never_ends", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "never_starts", "standard_field": false, "supports": "r", "type": ["bool"]}, {"enum_values": ["ABANDONED", "ACTIVE", "BACKUP", "DECLINED", "EXPIRED", "FREE", "OFFERED", "RELEASED", "RESET", "STATIC"], "is_array": false, "name": "next_binding_state", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "on_commit", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "on_expiry", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "on_release", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "option", "standard_field": false, "supports": "r", "type": ["string"]}, {"enum_values": ["BOTH", "IPV4", "IPV6"], "is_array": false, "name": "protocol", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "served_by", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "server_host_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "starts", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "tsfp", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "tstp", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "uid", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "username", "searchable_by": "=~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "variable", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["create", "update", "permissions", "scheduling"], "type": "lease", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "authentication_time", "searchable_by": "=<>!", "standard_field": true, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "expiration_time", "searchable_by": "=<>!", "standard_field": true, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "filter", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field1", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field2", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field3", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field4", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_email", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_first_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_last_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_middle_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_phone", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "is_registered_user", "standard_field": true, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "mac", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "never_expires", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "reserved_for_infoblox", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "username", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["permissions"], "type": "macfilteraddress", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "host_name", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "use_remote_console_access_enable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "use_snmp_setting", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "use_support_access_enable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "use_syslog_proxy_setting", "standard_field": false, "supports": "r", "type": ["bool"]}], "restrictions": ["create", "update", "delete", "scheduling", "csv"], "type": "member", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "access_list", "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exploded_access_list", "standard_field": false, "supports": "r", "type": ["addressac", "tsigac"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["scheduling", "csv"], "type": "<PERSON><PERSON><PERSON>", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "authority", "overridden_by": "use_authority", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_server_always_updates", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_ttl", "overridden_by": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ddns_update_fixed_addresses", "overridden_by": "use_ddns_update_fixed_addresses", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_use_option81", "overridden_by": "use_ddns_use_option81", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "email_list", "overridden_by": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_dhcp_thresholds", "overridden_by": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_email_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ifmap_publishing", "overridden_by": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_snmp_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "high_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "high_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ignore_dhcp_option_list_request", "overridden_by": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "lease_scavenge_time", "overridden_by": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["int"]}, {"is_array": false, "name": "low_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "low_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "members", "standard_field": false, "supports": "rwu", "type": ["dhcpmember", "msdhcpserver"]}, {"is_array": false, "name": "netmask", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_authority", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_update_fixed_addresses", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_use_option81", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["zoneassociation"]}], "restrictions": [], "type": "network", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "wsr", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["zoneassociation"]}], "restrictions": ["csv"], "type": "networkcontainer", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "is_default", "searchable_by": "=", "standard_field": true, "supports": "sr", "type": ["bool"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["scheduling", "csv"], "type": "networkview", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "always_update_dns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "authority", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_all_clients", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "email_list", "overridden_by": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_dhcp_thresholds", "overridden_by": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_email_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ifmap_publishing", "overridden_by": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_snmp_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "end_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exclude", "standard_field": false, "supports": "rwu", "type": ["exclusionrange"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "failover_association", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "fingerprint_filter_rules", "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"]}, {"is_array": false, "name": "high_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "high_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ignore_dhcp_option_list_request", "overridden_by": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "is_split_scope", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "known_clients", "overridden_by": "use_known_clients", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "lease_scavenge_time", "overridden_by": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["int"]}, {"is_array": true, "name": "logic_filter_rules", "standard_field": false, "supports": "rwu", "type": ["logicfilterrule"]}, {"is_array": false, "name": "low_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "low_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "mac_filter_rules", "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"]}, {"is_array": false, "name": "member", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["dhcpmember"]}, {"is_array": true, "name": "ms_options", "standard_field": false, "supports": "rwu", "type": ["msdhcpoption"]}, {"is_array": false, "name": "ms_server", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["msdhcpserver"]}, {"is_array": true, "name": "nac_filter_rules", "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"]}, {"is_array": false, "name": "name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "option_filter_rules", "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "relay_agent_filter_rules", "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"]}, {"enum_values": ["NONE", "MEMBER", "FAILOVER", "MS_SERVER", "MS_FAILOVER"], "is_array": false, "name": "server_association_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "split_member", "standard_field": false, "supports": "w", "type": ["msdhcpserver"]}, {"is_array": false, "name": "split_scope_exclusion_percent", "standard_field": false, "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "start_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "unknown_clients", "overridden_by": "use_unknown_clients", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_authority", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_known_clients", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_unknown_clients", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": [], "type": "range", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:a", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv6addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:aaaa", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "canonical", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dns_canonical", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:cname", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "aliases", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "configure_for_dns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "dns_aliases", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "ipv4addrs", "standard_field": true, "supports": "rwu", "type": ["record:host_ipv4addr"]}, {"is_array": true, "name": "ipv6addrs", "standard_field": true, "supports": "rwu", "type": ["record:host_ipv6addr"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "rrset_order", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:host", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "configure_for_dhcp", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "enable_pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "host", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ignore_client_requested_options", "overridden_by": "use_ignore_client_requested_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "last_queried", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "match_client", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "network", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "pxe_lease_time", "overridden_by": "use_pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_for_ea_inheritance", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_client_requested_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": ["create", "delete", "permissions", "scheduling", "csv"], "type": "record:host_ipv4addr", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["ADDRESS", "PREFIX", "BOTH"], "is_array": false, "name": "address_type", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "configure_for_dhcp", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "domain_name", "overridden_by": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "domain_name_servers", "overridden_by": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "duid", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "host", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ipv6addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6prefix", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6prefix_bits", "searchable_by": "=<>", "standard_field": false, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "match_client", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "standard_field": false, "supports": "rwu", "type": ["dhcpoption"]}, {"is_array": false, "name": "preferred_lifetime", "overridden_by": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_for_ea_inheritance", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "valid_lifetime", "overridden_by": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["create", "delete", "permissions", "scheduling", "csv"], "type": "record:host_ipv6addr", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dns_mail_exchanger", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "mail_exchanger", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "preference", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:mx", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "standard_field": false, "supports": "r", "type": ["discoverydata"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_ptrdname", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6addr", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ptrdname", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:ptr", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_target", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "port", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "priority", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "target", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "weight", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:srv", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "text", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:txt", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["CONFIG_ERROR", "DISABLED", "NO", "NO_PERMISSION", "NO_REQUEST", "OFFLINE", "REQUESTING", "RESTART_PENDING", "YES"], "is_array": false, "name": "dhcp_status", "standard_field": true, "supports": "r", "type": ["enum"]}, {"enum_values": ["CONFIG_ERROR", "DISABLED", "NO", "NO_PERMISSION", "NO_REQUEST", "OFFLINE", "REQUESTING", "RESTART_PENDING", "YES"], "is_array": false, "name": "dns_status", "standard_field": true, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "member", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"enum_values": ["CONFIG_ERROR", "DISABLED", "NO", "NO_PERMISSION", "NO_REQUEST", "OFFLINE", "REQUESTING", "RESTART_PENDING", "YES"], "is_array": false, "name": "reporting_status", "standard_field": true, "supports": "r", "type": ["enum"]}], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "restartservicestatus", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["APPROVED", "NONE", "PENDING", "REJECTED"], "is_array": false, "name": "approval_status", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "approver", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "approver_comment", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "automatic_restart", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "changed_objects", "standard_field": false, "supports": "r", "type": ["changedobject"]}, {"is_array": false, "name": "execute_now", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"enum_values": ["COMPLETED", "FAILED", "PENDING", "WAITING_EXECUTION"], "is_array": false, "name": "execution_status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "execution_time", "searchable_by": "<=>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "scheduled_time", "searchable_by": "<=>", "standard_field": false, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "submit_time", "searchable_by": "<=>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "submitter", "searchable_by": "=~:", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "submitter_comment", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "task_id", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["uint"]}, {"is_array": false, "name": "ticket_number", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["create", "permissions", "global search", "scheduling", "csv"], "type": "scheduledtask", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "search", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["REDIRECT", "REFUSE"], "is_array": false, "name": "blacklist_action", "overridden_by": "use_blacklist", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "blacklist_log_query", "overridden_by": "use_blacklist", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "blacklist_redirect_addresses", "overridden_by": "use_blacklist", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "blacklist_redirect_ttl", "overridden_by": "use_blacklist", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "blacklist_rulesets", "overridden_by": "use_blacklist", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "custom_root_name_servers", "overridden_by": "use_root_name_server", "standard_field": false, "supports": "rwu", "type": ["extserver"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dns64_enabled", "overridden_by": "use_dns64", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "dns64_groups", "overridden_by": "use_dns64", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "dnssec_enabled", "overridden_by": "use_dnssec", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "dnssec_expired_signatures_enabled", "overridden_by": "use_dnssec", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "dnssec_trusted_keys", "overridden_by": "use_dnssec", "standard_field": false, "supports": "rwu", "type": ["dns<PERSON><PERSON>ed<PERSON>"]}, {"is_array": false, "name": "dnssec_validation_enabled", "overridden_by": "use_dnssec", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "enable_blacklist", "overridden_by": "use_blacklist", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"enum_values": ["YES", "NO", "BREAK_DNSSEC"], "is_array": false, "name": "filter_aaaa", "overridden_by": "use_filter_aaaa", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": true, "name": "filter_aaaa_list", "overridden_by": "use_filter_aaaa", "standard_field": false, "supports": "rwu", "type": ["addressac"]}, {"is_array": false, "name": "forward_only", "overridden_by": "use_forwarders", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "forwarders", "overridden_by": "use_forwarders", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "is_default", "searchable_by": "=", "standard_field": true, "supports": "sr", "type": ["bool"]}, {"is_array": false, "name": "lame_ttl", "overridden_by": "use_lame_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "match_clients", "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"]}, {"is_array": true, "name": "match_destinations", "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "notify_delay", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "nxdomain_log_query", "overridden_by": "use_nxdomain_redirect", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "nxdomain_redirect", "overridden_by": "use_nxdomain_redirect", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "nxdomain_redirect_addresses", "overridden_by": "use_nxdomain_redirect", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "nxdomain_redirect_ttl", "overridden_by": "use_nxdomain_redirect", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "nxdomain_rulesets", "overridden_by": "use_nxdomain_redirect", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "recursion", "overridden_by": "use_recursion", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"enum_values": ["CUSTOM", "INTERNET"], "is_array": false, "name": "root_name_server_type", "overridden_by": "use_root_name_server", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": true, "name": "sortlist", "overridden_by": "use_sortlist", "standard_field": false, "supports": "rwu", "type": ["sortlist"]}, {"is_array": false, "name": "use_blacklist", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_dns64", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_dnssec", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_filter_aaaa", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_forwarders", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_lame_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nxdomain_redirect", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recursion", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_root_name_server", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_sortlist", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": ["csv"], "type": "view", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": true, "name": "allow_active_dir", "overridden_by": "use_allow_active_dir", "standard_field": false, "supports": "rwu", "type": ["addressac"]}, {"is_array": false, "name": "allow_gss_tsig_for_underscore_zone", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "allow_gss_tsig_zone_updates", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "allow_query", "overridden_by": "use_allow_query", "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"]}, {"is_array": true, "name": "allow_transfer", "overridden_by": "use_allow_transfer", "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"]}, {"is_array": true, "name": "allow_update", "overridden_by": "use_allow_update", "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"]}, {"is_array": false, "name": "allow_update_forwarding", "overridden_by": "use_allow_update_forwarding", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "copy_xfer_to_notify", "overridden_by": "use_copy_xfer_to_notify", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "create_ptr_for_bulk_hosts", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "create_ptr_for_hosts", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "create_underscore_zones", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable_forwarding", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "display_domain", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_fqdn", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_soa_email", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_soa_mname", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dnssec_key_params", "overridden_by": "use_dnssec_key_params", "standard_field": false, "supports": "rwu", "type": ["dnsseckeyparams"]}, {"is_array": false, "name": "do_host_abstraction", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"enum_values": ["FAIL", "WARN"], "is_array": false, "name": "effective_check_names_policy", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "effective_record_name_policy", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "external_primaries", "standard_field": false, "supports": "rwu", "type": ["extserver"]}, {"is_array": true, "name": "external_secondaries", "standard_field": false, "supports": "rwu", "type": ["extserver"]}, {"is_array": false, "name": "fqdn", "searchable_by": "=~", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": true, "name": "grid_primary", "standard_field": false, "supports": "rwu", "type": ["memberserver"]}, {"is_array": false, "name": "grid_primary_shared_with_ms_parent_delegation", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": true, "name": "grid_secondaries", "standard_field": false, "supports": "rwu", "type": ["memberserver"]}, {"is_array": false, "name": "import_from", "overridden_by": "use_import_from", "standard_field": false, "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "is_dnssec_enabled", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "is_dnssec_signed", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "last_queried", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "locked", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "locked_by", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mask_prefix", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ms_ad_integrated", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "ms_allow_transfer", "standard_field": false, "supports": "rwu", "type": ["addressac"]}, {"enum_values": ["ADDRESS_AC", "ANY", "ANY_NS", "NONE"], "is_array": false, "name": "ms_allow_transfer_mode", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["ANY", "NONE", "SECURE"], "is_array": false, "name": "ms_ddns_mode", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["AUTH_BOTH", "AUTH_PRIMARY", "AUTH_SECONDARY", "NONE", "STUB"], "is_array": false, "name": "ms_managed", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": true, "name": "ms_primaries", "standard_field": false, "supports": "rwu", "type": ["msdnsserver"]}, {"is_array": false, "name": "ms_read_only", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": true, "name": "ms_secondaries", "standard_field": false, "supports": "rwu", "type": ["msdnsserver"]}, {"is_array": false, "name": "ms_sync_master_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": true, "name": "network_associations", "standard_field": false, "supports": "r", "type": ["network", "networkcontainer", "ipv6network", "ipv6networkcontainer"]}, {"is_array": false, "name": "network_view", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "notify_delay", "overridden_by": "use_notify_delay", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ns_group", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "parent", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "prefix", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"enum_values": ["External", "Grid", "Microsoft", "None"], "is_array": false, "name": "primary_type", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "record_name_policy", "overridden_by": "use_record_name_policy", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "records_monitored", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "rr_not_queried_enabled_time", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "set_soa_serial_number", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "soa_default_ttl", "overridden_by": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "soa_email", "overridden_by": "use_soa_email", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "soa_expire", "overridden_by": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "soa_mname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "soa_negative_ttl", "overridden_by": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "soa_refresh", "overridden_by": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "soa_retry", "overridden_by": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "soa_serial_number", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "srgs", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "update_forwarding", "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"]}, {"is_array": false, "name": "use_allow_active_dir", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_allow_query", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_allow_transfer", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_allow_update", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_allow_update_forwarding", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_check_names_policy", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_copy_xfer_to_notify", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_dnssec_key_params", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_external_primary", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_import_from", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_notify_delay", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_record_name_policy", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_soa_email", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_soa_mname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "using_srg_associations", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FORWARD", "IPV4", "IPV6"], "is_array": false, "name": "zone_format", "standard_field": false, "supports": "rw", "type": ["enum"]}, {"is_array": false, "name": "zone_not_queried_enabled_time", "standard_field": false, "supports": "r", "type": ["timestamp"]}], "restrictions": ["global search"], "type": "zone_auth", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "delegate_to", "standard_field": true, "supports": "rwu", "type": ["extserver"]}, {"is_array": false, "name": "delegated_ttl", "overridden_by": "use_delegated_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "display_domain", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_fqdn", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "enable_rfc2317_exclusion", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "fqdn", "searchable_by": "=~", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "locked", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "locked_by", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mask_prefix", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ms_ad_integrated", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"enum_values": ["ANY", "NONE", "SECURE"], "is_array": false, "name": "ms_ddns_mode", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["AUTH_BOTH", "AUTH_PRIMARY", "AUTH_SECONDARY", "NONE", "STUB"], "is_array": false, "name": "ms_managed", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "ms_read_only", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "ms_sync_master_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "parent", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "prefix", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_delegated_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "using_srg_associations", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FORWARD", "IPV4", "IPV6"], "is_array": false, "name": "zone_format", "standard_field": false, "supports": "rw", "type": ["enum"]}], "restrictions": ["global search", "csv"], "type": "zone_delegated", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "display_domain", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_fqdn", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "forward_to", "standard_field": true, "supports": "rwu", "type": ["extserver"]}, {"is_array": false, "name": "forwarders_only", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "forwarding_servers", "standard_field": false, "supports": "rwu", "type": ["forwardingmemberserver"]}, {"is_array": false, "name": "fqdn", "searchable_by": "=~", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "locked", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "locked_by", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mask_prefix", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ms_ad_integrated", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"enum_values": ["ANY", "NONE", "SECURE"], "is_array": false, "name": "ms_ddns_mode", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["AUTH_BOTH", "AUTH_PRIMARY", "AUTH_SECONDARY", "NONE", "STUB"], "is_array": false, "name": "ms_managed", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "ms_read_only", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "ms_sync_master_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "parent", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "prefix", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "using_srg_associations", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FORWARD", "IPV4", "IPV6"], "is_array": false, "name": "zone_format", "standard_field": false, "supports": "rw", "type": ["enum"]}], "restrictions": ["global search", "csv"], "type": "zone_forward", "version": "1.2"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable_forwarding", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "display_domain", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_fqdn", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "fqdn", "searchable_by": "=~", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "locked", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "locked_by", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mask_prefix", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ms_ad_integrated", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"enum_values": ["ANY", "NONE", "SECURE"], "is_array": false, "name": "ms_ddns_mode", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["AUTH_BOTH", "AUTH_PRIMARY", "AUTH_SECONDARY", "NONE", "STUB"], "is_array": false, "name": "ms_managed", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "ms_read_only", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "ms_sync_master_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "parent", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "prefix", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "soa_email", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "soa_expire", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "soa_mname", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "soa_negative_ttl", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "soa_refresh", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "soa_retry", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "soa_serial_number", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": true, "name": "stub_from", "standard_field": true, "supports": "rwu", "type": ["extserver"]}, {"is_array": true, "name": "stub_members", "standard_field": false, "supports": "rwu", "type": ["memberserver"]}, {"is_array": true, "name": "stub_msservers", "standard_field": false, "supports": "rwu", "type": ["msdnsserver"]}, {"is_array": false, "name": "using_srg_associations", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FORWARD", "IPV4", "IPV6"], "is_array": false, "name": "zone_format", "standard_field": false, "supports": "rw", "type": ["enum"]}], "restrictions": ["global search", "csv"], "type": "zone_stub", "version": "1.2"}]