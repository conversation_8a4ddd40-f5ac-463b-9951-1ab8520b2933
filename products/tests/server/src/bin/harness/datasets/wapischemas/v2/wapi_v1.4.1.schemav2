[{"requested_version": "1.4.1", "supported_objects": ["allrecords", "discovery:device", "discovery:deviceinterface", "discovery:<PERSON><PERSON><PERSON><PERSON>", "discovery:status", "fileop", "fixedaddress", "grid", "grid:dhcpproperties", "ipv4address", "ipv6address", "ipv6fixedaddress", "ipv6network", "ipv6networkcontainer", "ipv6range", "ipv6sharednetwork", "lease", "macfilteraddress", "member", "<PERSON><PERSON><PERSON>", "network", "networkcontainer", "networkview", "permission", "range", "record:a", "record:aaaa", "record:cname", "record:host", "record:host_ipv4addr", "record:host_ipv6addr", "record:mx", "record:ptr", "record:srv", "record:txt", "restartservicestatus", "scheduledtask", "search", "sharednetwork", "snmpuser", "view", "zone_auth", "zone_delegated", "zone_forward", "zone_stub"]}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "record", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ttl", "standard_field": false, "supports": "r", "type": ["uint"]}, {"enum_values": ["ALL", "record:a", "record:aaaa", "record:cname", "record:dname", "record:host", "record:host_ipv4addr", "record:host_ipv6addr", "record:mx", "record:naptr", "record:ptr", "record:srv", "record:txt", "sharedrecord:a", "sharedrecord:aaaa", "sharedrecord:mx", "sharedrecord:srv", "sharedrecord:txt"], "is_array": false, "name": "type", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "delete", "permissions", "global search", "scheduling"], "type": "allrecords", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "address_ref", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "description", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": true, "name": "interfaces", "standard_field": false, "supports": "r", "type": ["discovery:deviceinterface"]}, {"is_array": false, "name": "location", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "model", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "neighbors", "standard_field": false, "supports": "r", "type": ["discovery:<PERSON><PERSON><PERSON><PERSON>"]}, {"is_array": false, "name": "network", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "networks", "standard_field": false, "supports": "r", "type": ["network", "ipv6network"]}, {"is_array": false, "name": "os_version", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "type", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "vendor", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "delete", "scheduling", "csv"], "type": "discovery:device", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["UP", "DOWN"], "is_array": false, "name": "admin_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "description", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "device", "standard_field": false, "supports": "r", "type": ["string"]}, {"enum_values": ["FULL", "HALF", "UNSUPPORTED", "UNKNOWN"], "is_array": false, "name": "duplex", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": true, "name": "ifaddr_infos", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "address_object", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discovery:ifaddrinfo"], "wapi_primitive": "struct"}, {"is_array": false, "name": "index", "standard_field": false, "supports": "r", "type": ["int"]}, {"is_array": false, "name": "last_change", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "link_aggregation", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "mac", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"enum_values": ["UP", "DOWN"], "is_array": false, "name": "oper_status", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["enum"]}, {"enum_values": ["ENABLED", "DISABLED"], "is_array": false, "name": "port_fast", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "speed", "searchable_by": "=<>!", "standard_field": false, "supports": "rs", "type": ["uint"]}, {"enum_values": ["ON", "OFF"], "is_array": false, "name": "trunk_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "type", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "vlan_infos", "schema": {"fields": [{"is_array": false, "name": "id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discovery:vlaninfo"], "wapi_primitive": "struct"}], "restrictions": ["create", "delete", "scheduling", "csv"], "type": "discovery:deviceinterface", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "address_ref", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "interface", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mac", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "name", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": true, "name": "vlan_infos", "schema": {"fields": [{"is_array": false, "name": "id", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discovery:vlaninfo"], "wapi_primitive": "struct"}], "restrictions": ["create", "update", "delete", "global search", "scheduling", "csv"], "type": "discovery:<PERSON><PERSON><PERSON><PERSON>", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "existence_info", "schema": {"fields": [{"enum_values": ["OK", "ERROR", "RUNNING"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "message", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "timestamp", "supports": "r", "type": ["timestamp"]}]}, "standard_field": false, "supports": "r", "type": ["discovery:statusinfo"], "wapi_primitive": "struct"}, {"is_array": false, "name": "fingerprint_enabled", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "fingerprint_info", "schema": {"fields": [{"enum_values": ["OK", "ERROR", "RUNNING"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "message", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "timestamp", "supports": "r", "type": ["timestamp"]}]}, "standard_field": false, "supports": "r", "type": ["discovery:statusinfo"], "wapi_primitive": "struct"}, {"is_array": false, "name": "first_seen", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_action", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "last_seen", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_timestamp", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "name", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "reachable_info", "schema": {"fields": [{"enum_values": ["OK", "ERROR", "RUNNING"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "message", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "timestamp", "supports": "r", "type": ["timestamp"]}]}, "standard_field": false, "supports": "r", "type": ["discovery:statusinfo"], "wapi_primitive": "struct"}, {"is_array": false, "name": "snmp_collection_enabled", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "snmp_collection_info", "schema": {"fields": [{"enum_values": ["OK", "ERROR", "RUNNING"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "message", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "timestamp", "supports": "r", "type": ["timestamp"]}]}, "standard_field": false, "supports": "r", "type": ["discovery:statusinfo"], "wapi_primitive": "struct"}, {"is_array": false, "name": "snmp_credential_info", "schema": {"fields": [{"enum_values": ["OK", "ERROR", "RUNNING"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "message", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "timestamp", "supports": "r", "type": ["timestamp"]}]}, "standard_field": false, "supports": "r", "type": ["discovery:statusinfo"], "wapi_primitive": "struct"}, {"enum_values": ["OK", "ERROR", "NOT_REACHABLE"], "is_array": false, "name": "status", "standard_field": true, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "type", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["create", "update", "delete", "global search", "scheduling", "csv"], "type": "discovery:status", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "downloadcomplete", "schema": {"input_fields": [{"is_array": false, "name": "token", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["datagetcomplete"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "download", "is_array": false, "name": "getgriddata", "schema": {"input_fields": [{"enum_values": ["NTP_KEY_FILE", "SNMP_MIBS_FILE", "BACKUP"], "is_array": false, "name": "type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "remote_url", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "nios_data", "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "discovery_data", "supports": "w", "type": ["bool"]}], "output_fields": [{"is_array": false, "name": "token", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "url", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["getgriddata"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"func_type": "upload", "is_array": false, "name": "setfiledest", "schema": {"input_fields": [{"enum_values": ["TFTP_FILE"], "is_array": false, "name": "type", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "token", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "dest_path", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "extract", "supports": "w", "type": ["bool"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["setdatafiledest"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "uploadinit", "schema": {"input_fields": [{"is_array": false, "name": "filename", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": false, "name": "url", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "token", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["datauploadinit"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}], "restrictions": ["create", "read", "delete", "update", "permissions", "global search", "csv"], "type": "fileop", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "agent_circuit_id", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "agent_remote_id", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "always_update_dns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "client_identifier_prepend_zero", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_hostname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dhcp_client_identifier", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "schema": {"fields": [{"is_array": false, "name": "device_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discovered_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discoverer", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "duid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "first_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mgmt_ip_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "netbios_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_ip", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "open_ports", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "os", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_duplex", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_link_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_speed", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_adapter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_cluster", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_datacenter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_host", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_switch", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discoverydata"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovered_data.device_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discovered_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discoverer", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.first_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.last_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.mac_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.mgmt_ip_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.netbios_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_ip", "searchable_by": "=~", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.network_component_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.open_ports", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.os", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_duplex", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_link_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_speed", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.v_adapter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_cluster", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_datacenter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_host", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_switch", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_immediate_discovery", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ignore_dhcp_option_list_request", "overridden_by": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "supports_inline_funccall": true, "type": ["string"]}, {"is_array": false, "name": "mac", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"enum_values": ["MAC_ADDRESS", "CLIENT_ID", "RESERVED", "CIRCUIT_ID", "REMOTE_ID"], "is_array": false, "name": "match_client", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": true, "name": "ms_options", "schema": {"fields": [{"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "user_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "type", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["msdhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ms_server", "schema": {"fields": [{"is_array": false, "name": "ipv4addr", "supports": "rwu", "type": ["string"]}]}, "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["msdhcpserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_option", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "snmp3_credential", "schema": {"fields": [{"is_array": false, "name": "user", "supports": "rwu", "type": ["string"]}, {"enum_values": ["NONE", "MD5", "SHA"], "is_array": false, "name": "authentication_protocol", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "authentication_password", "supports": "wu", "type": ["string"]}, {"enum_values": ["NONE", "DES", "3DES", "AES"], "is_array": false, "name": "privacy_protocol", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "privacy_password", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:snmp3credential"], "wapi_primitive": "struct"}, {"is_array": false, "name": "snmp_credential", "schema": {"fields": [{"is_array": false, "name": "community_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:snmpcredential"], "wapi_primitive": "struct"}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_snmp3_credential", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_snmp_credential", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": [], "type": "fixedaddress", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "audit_to_syslog_enable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "control_ip_address", "schema": {"input_fields": [{"is_array": false, "name": "network_view", "supports": "w", "type": ["string"]}, {"is_array": false, "name": "exclude", "supports": "w", "type": ["bool"]}, {"is_array": true, "name": "addresses", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["controlipaddress"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "external_syslog_server_enable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "password_setting", "schema": {"fields": [{"is_array": false, "name": "password_min_length", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "num_lower_char", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "num_upper_char", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "num_numeric_char", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "num_symbol_char", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "chars_to_change", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "expire_days", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "reminder_days", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "force_reset_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "expire_enable", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "r", "type": ["setting:password"], "wapi_primitive": "struct"}, {"is_array": false, "name": "requestrestartservicestatus", "schema": {"input_fields": [{"enum_values": ["ALL", "DHCP", "DNS"], "is_array": false, "name": "service_option", "supports": "w", "type": ["enum"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["requestgridservicestatus"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "restartservices", "schema": {"input_fields": [{"enum_values": ["SEQUENTIALLY", "SIMULTANEOUSLY"], "is_array": false, "name": "member_order", "supports": "w", "type": ["enum"]}, {"enum_values": ["FORCE_RESTART", "RESTART_IF_NEEDED"], "is_array": false, "name": "restart_option", "supports": "w", "type": ["enum"]}, {"enum_values": ["ALL", "DHCP", "DNS"], "is_array": false, "name": "service_option", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "sequential_delay", "supports": "w", "type": ["uint"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["gridrestartservices"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "security_banner_setting", "schema": {"fields": [{"enum_values": ["BLACK", "BLUE", "BROWN", "CYAN", "GREEN", "MAGENTA", "ORANGE", "PURPLE", "RED", "YELLOW"], "is_array": false, "name": "color", "supports": "rwu", "type": ["enum"]}, {"enum_values": ["CONFIDENTIAL", "RESTRICTED", "SECRET", "TOP_SECRET", "UNCLASSIFIED"], "is_array": false, "name": "level", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "message", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "r", "type": ["setting:securitybanner"], "wapi_primitive": "struct"}, {"is_array": false, "name": "security_setting", "schema": {"fields": [{"is_array": false, "name": "audit_log_rolling_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "admin_access_items", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "http_redirect_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "login_banner_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "login_banner_text", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "remote_console_access_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "security_access_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "security_access_remote_console_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "session_timeout", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ssh_perm_enable", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "support_access_enable", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "support_access_info", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["setting:security"], "wapi_primitive": "struct"}, {"is_array": false, "name": "snmp_setting", "schema": {"fields": [{"is_array": true, "name": "engine_id", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "queries_community_string", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "queries_enable", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "snmpv3_queries_enable", "supports": "r", "type": ["bool"]}, {"is_array": true, "name": "snmpv3_queries_users", "schema": {"fields": [{"is_array": false, "name": "user", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "r", "type": ["string"]}]}, "supports": "r", "type": ["queriesuser"], "wapi_primitive": "struct"}, {"is_array": false, "name": "snmpv3_traps_enable", "supports": "r", "type": ["bool"]}, {"is_array": true, "name": "syscontact", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "sysdescr", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "syslocation", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "sysname", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "trap_receivers", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "user", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "r", "type": ["string"]}]}, "supports": "r", "type": ["trapreceiver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "traps_community_string", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "traps_enable", "supports": "r", "type": ["bool"]}]}, "standard_field": false, "supports": "r", "type": ["setting:snmp"], "wapi_primitive": "struct"}, {"is_array": false, "name": "start_discovery", "schema": {"input_fields": [{"is_array": true, "name": "objects", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["startdiscovery"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"enum_values": ["DAEMON", "LOCAL0", "LOCAL1", "LOCAL2", "LOCAL3", "LOCAL4", "LOCAL5", "LOCAL6", "LOCAL7"], "is_array": false, "name": "syslog_facility", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": true, "name": "syslog_servers", "schema": {"fields": [{"enum_values": ["TCP", "UDP"], "is_array": false, "name": "connection_type", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "port", "supports": "r", "type": ["uint"]}, {"enum_values": ["ANY", "LAN", "MGMT"], "is_array": false, "name": "local_interface", "supports": "r", "type": ["enum"]}, {"enum_values": ["ANY", "EXTERNAL", "INTERNAL"], "is_array": false, "name": "message_source", "supports": "r", "type": ["enum"]}, {"enum_values": ["ALERT", "CRIT", "DEBUG", "EMERG", "INFO", "NOTICE", "WARNING"], "is_array": false, "name": "severity", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "address", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["syslogserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "syslog_size", "standard_field": false, "supports": "r", "type": ["uint"]}], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "grid", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "disable_all_nac_filters", "standard_field": true, "supports": "rwu", "type": ["bool"]}], "restrictions": ["create", "delete", "permissions", "scheduling", "csv"], "type": "grid:dhcpproperties", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "dhcp_client_identifier", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "discovered_data.open_ports", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "fingerprint", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ip_address", "searchable_by": "=<>", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "is_conflict", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["bool"]}, {"is_array": false, "name": "lease_state", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "mac_address", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "names", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "objects", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "types", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "usage", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "username", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "permissions", "global search", "scheduling", "csv"], "type": "ipv4address", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "discovered_data.open_ports", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "duid", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "fingerprint", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ip_address", "searchable_by": "<=>", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "is_conflict", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["bool"]}, {"is_array": false, "name": "lease_state", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "names", "searchable_by": "=:~", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "objects", "standard_field": true, "supports": "r", "type": ["string"]}, {"enum_values": ["USED", "UNUSED"], "is_array": false, "name": "status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}, {"is_array": true, "name": "types", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": true, "name": "usage", "searchable_by": "=:", "standard_field": true, "supports": "rs", "type": ["string"]}], "restrictions": ["create", "update", "permissions", "global search", "scheduling", "csv"], "type": "ipv6address", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["ADDRESS", "PREFIX", "BOTH"], "is_array": false, "name": "address_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "schema": {"fields": [{"is_array": false, "name": "device_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discovered_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discoverer", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "duid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "first_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mgmt_ip_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "netbios_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_ip", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "open_ports", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "os", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_duplex", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_link_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_speed", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_adapter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_cluster", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_datacenter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_host", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_switch", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discoverydata"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovered_data.device_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discovered_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discoverer", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.first_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.last_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.mac_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.mgmt_ip_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.netbios_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_ip", "searchable_by": "=~", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.network_component_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.open_ports", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.os", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_duplex", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_link_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_speed", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.v_adapter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_cluster", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_datacenter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_host", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_switch", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "domain_name", "overridden_by": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "domain_name_servers", "overridden_by": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "duid", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "enable_immediate_discovery", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv6addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "supports_inline_funccall": true, "type": ["string"]}, {"is_array": false, "name": "ipv6prefix", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6prefix_bits", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_option", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "preferred_lifetime", "overridden_by": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "snmp3_credential", "schema": {"fields": [{"is_array": false, "name": "user", "supports": "rwu", "type": ["string"]}, {"enum_values": ["NONE", "MD5", "SHA"], "is_array": false, "name": "authentication_protocol", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "authentication_password", "supports": "wu", "type": ["string"]}, {"enum_values": ["NONE", "DES", "3DES", "AES"], "is_array": false, "name": "privacy_protocol", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "privacy_password", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:snmp3credential"], "wapi_primitive": "struct"}, {"is_array": false, "name": "snmp_credential", "schema": {"fields": [{"is_array": false, "name": "community_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:snmpcredential"], "wapi_primitive": "struct"}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_snmp3_credential", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_snmp_credential", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "valid_lifetime", "overridden_by": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": [], "type": "ipv6fixedaddress", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_enable_option_fqdn", "overridden_by": "use_ddns_enable_option_fqdn", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_server_always_updates", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_ttl", "overridden_by": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovery_basic_poll_settings", "overridden_by": "use_discovery_basic_polling_settings", "schema": {"fields": [{"is_array": false, "name": "port_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "device_profile", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "snmp_collection", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "netbios_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "complete_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "smart_subnet_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "auto_arp_refresh_before_switch_port_polling", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["DISABLED", "PERIODIC", "SCHEDULED"], "is_array": false, "name": "switch_port_data_collection_polling", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "switch_port_data_collection_polling_schedule", "schema": {"fields": [{"enum_values": ["SATURDAY", "SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"], "is_array": true, "name": "weekdays", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "time_zone", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "recurring_time", "supports": "rwu", "type": ["timestamp"]}]}, "supports": "rwu", "type": ["setting:schedule"], "wapi_primitive": "struct"}, {"is_array": false, "name": "switch_port_data_collection_polling_interval", "supports": "rwu", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:basicpollsettings"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovery_member", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "domain_name", "overridden_by": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "domain_name_servers", "overridden_by": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_discovery", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ifmap_publishing", "overridden_by": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_immediate_discovery", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "members", "schema": {"fields": [{"is_array": false, "name": "ipv4addr", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpmember"], "wapi_primitive": "struct"}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "next_available_ip", "schema": {"input_fields": [{"is_array": false, "name": "num", "supports": "w", "type": ["uint"]}, {"is_array": true, "name": "exclude", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": true, "name": "ips", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["nextavailableip6"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "next_available_network", "schema": {"input_fields": [{"is_array": false, "name": "cidr", "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "num", "supports": "w", "type": ["uint"]}, {"is_array": true, "name": "exclude", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": true, "name": "networks", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["nextavailablenet6"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": true, "name": "options", "overridden_by": "use_options", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_option", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "preferred_lifetime", "overridden_by": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "shared_network_name", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_enable_option_fqdn", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_discovery_basic_polling_settings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "valid_lifetime", "overridden_by": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "schema": {"fields": [{"is_array": false, "name": "fqdn", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "is_default", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["zoneassociation"], "wapi_primitive": "struct"}], "restrictions": [], "type": "ipv6network", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "discovery_basic_poll_settings", "overridden_by": "use_discovery_basic_polling_settings", "schema": {"fields": [{"is_array": false, "name": "port_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "device_profile", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "snmp_collection", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "netbios_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "complete_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "smart_subnet_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "auto_arp_refresh_before_switch_port_polling", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["DISABLED", "PERIODIC", "SCHEDULED"], "is_array": false, "name": "switch_port_data_collection_polling", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "switch_port_data_collection_polling_schedule", "schema": {"fields": [{"enum_values": ["SATURDAY", "SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"], "is_array": true, "name": "weekdays", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "time_zone", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "recurring_time", "supports": "rwu", "type": ["timestamp"]}]}, "supports": "rwu", "type": ["setting:schedule"], "wapi_primitive": "struct"}, {"is_array": false, "name": "switch_port_data_collection_polling_interval", "supports": "rwu", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:basicpollsettings"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovery_member", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_discovery", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_immediate_discovery", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "wsr", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "use_discovery_basic_polling_settings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "schema": {"fields": [{"is_array": false, "name": "fqdn", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "is_default", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["zoneassociation"], "wapi_primitive": "struct"}], "restrictions": ["csv"], "type": "ipv6networkcontainer", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovery_basic_poll_settings", "overridden_by": "use_discovery_basic_polling_settings", "schema": {"fields": [{"is_array": false, "name": "port_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "device_profile", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "snmp_collection", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "netbios_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "complete_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "smart_subnet_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "auto_arp_refresh_before_switch_port_polling", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["DISABLED", "PERIODIC", "SCHEDULED"], "is_array": false, "name": "switch_port_data_collection_polling", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "switch_port_data_collection_polling_schedule", "schema": {"fields": [{"enum_values": ["SATURDAY", "SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"], "is_array": true, "name": "weekdays", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "time_zone", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "recurring_time", "supports": "rwu", "type": ["timestamp"]}]}, "supports": "rwu", "type": ["setting:schedule"], "wapi_primitive": "struct"}, {"is_array": false, "name": "switch_port_data_collection_polling_interval", "supports": "rwu", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:basicpollsettings"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovery_member", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_discovery", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_immediate_discovery", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "end_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exclude", "schema": {"fields": [{"is_array": false, "name": "start_address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "end_address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["exclusionrange"], "wapi_primitive": "struct"}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv6_end_prefix", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6_prefix_bits", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "ipv6_start_prefix", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "member", "schema": {"fields": [{"is_array": false, "name": "ipv4addr", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}]}, "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["dhcpmember"], "wapi_primitive": "struct"}, {"is_array": false, "name": "name", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "next_available_ip", "schema": {"input_fields": [{"is_array": false, "name": "num", "supports": "w", "type": ["uint"]}, {"is_array": true, "name": "exclude", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": true, "name": "ips", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["nextavailableip6"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "server_association_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "start_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "use_discovery_basic_polling_settings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": [], "type": "ipv6range", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_server_always_updates", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_ttl", "overridden_by": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ddns_use_option81", "overridden_by": "use_ddns_use_option81", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "domain_name", "overridden_by": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "domain_name_servers", "overridden_by": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": true, "name": "networks", "standard_field": true, "supports": "rwu", "type": ["ipv6network"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_option", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "preferred_lifetime", "overridden_by": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_use_option81", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "valid_lifetime", "overridden_by": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": [], "type": "ipv6sharednetwork", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "searchable_by": "=<>~!", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "billing_class", "standard_field": false, "supports": "r", "type": ["string"]}, {"enum_values": ["ABANDONED", "ACTIVE", "BACKUP", "DECLINED", "EXPIRED", "FREE", "OFFERED", "RELEASED", "RESET", "STATIC"], "is_array": false, "name": "binding_state", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "client_hostname", "searchable_by": "=~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "cltt", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data", "schema": {"fields": [{"is_array": false, "name": "device_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discovered_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discoverer", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "duid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "first_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mgmt_ip_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "netbios_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_ip", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "open_ports", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "os", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_duplex", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_link_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_speed", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_adapter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_cluster", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_datacenter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_host", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_switch", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discoverydata"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovered_data.device_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discovered_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discoverer", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.first_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.last_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.mac_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.mgmt_ip_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.netbios_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_ip", "searchable_by": "=~", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.network_component_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.open_ports", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.os", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_duplex", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_link_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_speed", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.v_adapter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_cluster", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_datacenter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_host", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_switch", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "ends", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "hardware", "searchable_by": "=~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ipv6_duid", "searchable_by": "=~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ipv6_iaid", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ipv6_preferred_lifetime", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "ipv6_prefix_bits", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["uint"]}, {"is_array": false, "name": "network", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "never_ends", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "never_starts", "standard_field": false, "supports": "r", "type": ["bool"]}, {"enum_values": ["ABANDONED", "ACTIVE", "BACKUP", "DECLINED", "EXPIRED", "FREE", "OFFERED", "RELEASED", "RESET", "STATIC"], "is_array": false, "name": "next_binding_state", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "on_commit", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "on_expiry", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "on_release", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "option", "standard_field": false, "supports": "r", "type": ["string"]}, {"enum_values": ["BOTH", "IPV4", "IPV6"], "is_array": false, "name": "protocol", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "served_by", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "server_host_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "starts", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "tsfp", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "tstp", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "uid", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "username", "searchable_by": "=~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "variable", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["create", "update", "permissions", "scheduling"], "type": "lease", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "authentication_time", "searchable_by": "=<>!", "standard_field": true, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "expiration_time", "searchable_by": "=<>!", "standard_field": true, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "filter", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "fingerprint", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field1", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field2", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field3", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_custom_field4", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_email", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_first_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_last_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_middle_name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "guest_phone", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "is_registered_user", "standard_field": true, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "mac", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "never_expires", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "reserved_for_infoblox", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "username", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["permissions"], "type": "macfilteraddress", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "external_syslog_server_enable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "host_name", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "ipv4_address", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "ipv6_address", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": true, "name": "node_info", "schema": {"fields": [{"is_array": true, "name": "service_status", "schema": {"fields": [{"is_array": false, "name": "description", "supports": "r", "type": ["string"]}, {"enum_values": ["FAILED", "WARNING", "WORKING", "INACTIVE"], "is_array": false, "name": "status", "supports": "r", "type": ["enum"]}, {"enum_values": ["JOIN_STATUS", "NODE_STATUS", "DISK_USAGE", "ENET_LAN", "ENET_LAN2", "ENET_HA", "ENET_MGMT", "LCD", "REPLICATION", "DB_OBJECT", "RAID_SUMMARY", "RAID_DISK1", "RAID_DISK2", "RAID_DISK3", "RAID_DISK4", "RAID_DISK5", "RAID_DISK6", "RAID_DISK7", "RAID_DISK8", "FAN1", "FAN2", "FAN3", "FAN4", "FAN5", "FAN6", "FAN7", "FAN8", "POWER_SUPPLY", "POWER1", "POWER2", "POWER3", "POWER4", "NTP_SYNC", "CPU1_TEMP", "CPU2_TEMP", "SYS_TEMP", "RAID_BATTERY", "CPU_USAGE", "OSPF", "OSPF6", "BGP", "BFD", "AUTH_NAMED", "CORE_FILES", "MGM_SERVICE", "SUBGRID_CONN", "NETWORK_CAPACITY", "DISK_SIZE", "SFP_MGMT", "SFP_LAN", "SFP_HA", "SFP_LAN2", "SNIC_UTIL", "SNIC_PCB_TEMP", "SNIC_CHIP_TEMP", "SNIC_CORE_UTIL", "FP_CORE_UTIL", "CPU_USAGE", "MEMORY", "EXTERNAL_STORAGE", "SWAP_USAGE", "DISCOVERY_CAPACITY", "PASSIVE_HA_CONNECTIVITY", "VPN_CERT"], "is_array": false, "name": "service", "supports": "r", "type": ["enum"]}]}, "supports": "r", "type": ["servicestatus"], "wapi_primitive": "struct"}]}, "standard_field": false, "supports": "r", "type": ["nodeinfo"], "wapi_primitive": "struct"}, {"is_array": false, "name": "remote_console_access_enable", "overridden_by": "use_remote_console_access_enable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "requestrestartservicestatus", "schema": {"input_fields": [{"enum_values": ["ALL", "DHCP", "DNS"], "is_array": false, "name": "service_option", "supports": "w", "type": ["enum"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["requestmemberservicestatus"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "restartservices", "schema": {"input_fields": [{"enum_values": ["FORCE_RESTART", "RESTART_IF_NEEDED"], "is_array": false, "name": "restart_option", "supports": "w", "type": ["enum"]}, {"enum_values": ["ALL", "DHCP", "DNS"], "is_array": false, "name": "service_option", "supports": "w", "type": ["enum"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["memberrestartservices"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "snmp_setting", "overridden_by": "use_snmp_setting", "schema": {"fields": [{"is_array": true, "name": "engine_id", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "queries_community_string", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "queries_enable", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "snmpv3_queries_enable", "supports": "r", "type": ["bool"]}, {"is_array": true, "name": "snmpv3_queries_users", "schema": {"fields": [{"is_array": false, "name": "user", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "r", "type": ["string"]}]}, "supports": "r", "type": ["queriesuser"], "wapi_primitive": "struct"}, {"is_array": false, "name": "snmpv3_traps_enable", "supports": "r", "type": ["bool"]}, {"is_array": true, "name": "syscontact", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "sysdescr", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "syslocation", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "sysname", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "trap_receivers", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "user", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "r", "type": ["string"]}]}, "supports": "r", "type": ["trapreceiver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "traps_community_string", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "traps_enable", "supports": "r", "type": ["bool"]}]}, "standard_field": false, "supports": "r", "type": ["setting:snmp"], "wapi_primitive": "struct"}, {"is_array": false, "name": "support_access_enable", "overridden_by": "use_support_access_enable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "syslog_proxy_setting", "overridden_by": "use_syslog_proxy_setting", "schema": {"fields": [{"is_array": false, "name": "enable", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "tcp_enable", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "tcp_port", "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "udp_enable", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "udp_port", "supports": "r", "type": ["uint"]}, {"is_array": true, "name": "client_acls", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "supports": "r", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}]}, "standard_field": false, "supports": "r", "type": ["setting:syslogproxy"], "wapi_primitive": "struct"}, {"is_array": true, "name": "syslog_servers", "schema": {"fields": [{"enum_values": ["TCP", "UDP"], "is_array": false, "name": "connection_type", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "port", "supports": "r", "type": ["uint"]}, {"enum_values": ["ANY", "LAN", "MGMT"], "is_array": false, "name": "local_interface", "supports": "r", "type": ["enum"]}, {"enum_values": ["ANY", "EXTERNAL", "INTERNAL"], "is_array": false, "name": "message_source", "supports": "r", "type": ["enum"]}, {"enum_values": ["ALERT", "CRIT", "DEBUG", "EMERG", "INFO", "NOTICE", "WARNING"], "is_array": false, "name": "severity", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "address", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["syslogserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "syslog_size", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "use_remote_console_access_enable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "use_snmp_setting", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "use_support_access_enable", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "use_syslog_proxy_setting", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "vip_setting", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "gateway", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "subnet_mask", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "lan_subnet_mask", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "lan_gateway", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["setting:network"], "wapi_primitive": "struct"}], "restrictions": ["create", "update", "delete", "scheduling", "csv"], "type": "member", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": true, "name": "access_list", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exploded_access_list", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "r", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["scheduling", "csv"], "type": "<PERSON><PERSON><PERSON>", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "authority", "overridden_by": "use_authority", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "contains_address", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_server_always_updates", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_ttl", "overridden_by": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ddns_update_fixed_addresses", "overridden_by": "use_ddns_update_fixed_addresses", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_use_option81", "overridden_by": "use_ddns_use_option81", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovery_basic_poll_settings", "overridden_by": "use_discovery_basic_polling_settings", "schema": {"fields": [{"is_array": false, "name": "port_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "device_profile", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "snmp_collection", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "netbios_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "complete_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "smart_subnet_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "auto_arp_refresh_before_switch_port_polling", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["DISABLED", "PERIODIC", "SCHEDULED"], "is_array": false, "name": "switch_port_data_collection_polling", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "switch_port_data_collection_polling_schedule", "schema": {"fields": [{"enum_values": ["SATURDAY", "SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"], "is_array": true, "name": "weekdays", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "time_zone", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "recurring_time", "supports": "rwu", "type": ["timestamp"]}]}, "supports": "rwu", "type": ["setting:schedule"], "wapi_primitive": "struct"}, {"is_array": false, "name": "switch_port_data_collection_polling_interval", "supports": "rwu", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:basicpollsettings"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovery_member", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "email_list", "overridden_by": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_dhcp_thresholds", "overridden_by": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_discovery", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_email_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ifmap_publishing", "overridden_by": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_immediate_discovery", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "enable_snmp_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "high_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "high_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ignore_dhcp_option_list_request", "overridden_by": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "lease_scavenge_time", "overridden_by": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["int"]}, {"is_array": false, "name": "low_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "low_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "member", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": true, "name": "members", "schema": {"fields": [{"is_array": false, "name": "ipv4addr", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ipv4addr", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpmember", "msdhcpserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "netmask", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "next_available_ip", "schema": {"input_fields": [{"is_array": false, "name": "num", "supports": "w", "type": ["uint"]}, {"is_array": true, "name": "exclude", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": true, "name": "ips", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["nextavailableip"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "next_available_network", "schema": {"input_fields": [{"is_array": false, "name": "cidr", "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "num", "supports": "w", "type": ["uint"]}, {"is_array": true, "name": "exclude", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": true, "name": "networks", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["nextavailablenet"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_option", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_authority", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_update_fixed_addresses", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_use_option81", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_discovery_basic_polling_settings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "schema": {"fields": [{"is_array": false, "name": "fqdn", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "is_default", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["zoneassociation"], "wapi_primitive": "struct"}], "restrictions": [], "type": "network", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "auto_create_reversezone", "standard_field": false, "supports": "w", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "discovery_basic_poll_settings", "overridden_by": "use_discovery_basic_polling_settings", "schema": {"fields": [{"is_array": false, "name": "port_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "device_profile", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "snmp_collection", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "netbios_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "complete_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "smart_subnet_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "auto_arp_refresh_before_switch_port_polling", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["DISABLED", "PERIODIC", "SCHEDULED"], "is_array": false, "name": "switch_port_data_collection_polling", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "switch_port_data_collection_polling_schedule", "schema": {"fields": [{"enum_values": ["SATURDAY", "SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"], "is_array": true, "name": "weekdays", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "time_zone", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "recurring_time", "supports": "rwu", "type": ["timestamp"]}]}, "supports": "rwu", "type": ["setting:schedule"], "wapi_primitive": "struct"}, {"is_array": false, "name": "switch_port_data_collection_polling_interval", "supports": "rwu", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:basicpollsettings"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovery_member", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_discovery", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_immediate_discovery", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "wsr", "type": ["string"]}, {"is_array": false, "name": "network_container", "searchable_by": "=", "standard_field": false, "supports": "sr", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "next_available_network", "schema": {"input_fields": [{"is_array": false, "name": "cidr", "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "num", "supports": "w", "type": ["uint"]}, {"is_array": true, "name": "exclude", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": true, "name": "networks", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["nextavailablenet"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "use_discovery_basic_polling_settings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_zone_associations", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "zone_associations", "overridden_by": "use_zone_associations", "schema": {"fields": [{"is_array": false, "name": "fqdn", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "is_default", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["zoneassociation"], "wapi_primitive": "struct"}], "restrictions": ["csv"], "type": "networkcontainer", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "is_default", "searchable_by": "=", "standard_field": true, "supports": "sr", "type": ["bool"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["scheduling", "csv"], "type": "networkview", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "group", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "object", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"enum_values": ["DENY", "READ", "WRITE"], "is_array": false, "name": "permission", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"enum_values": ["CLUSTER", "MEMBER", "MEMBER_CLOUD", "SUB_GRID", "SUB_GRID_NETWORK_VIEW_PARENT", "SG_NETWORK_VIEW", "SG_IPV4_NETWORK", "SG_IPV6_NETWORK", "MSSERVER", "VIEW", "ZONE", "A", "AAAA", "ALIAS", "CNAME", "DNAME", "MX", "PTR", "SRV", "TXT", "HOST", "BULKHOST", "NAPTR", "TLSA", "CAA", "Unknown", "SHARED_RECORD_GROUP", "SHARED_A", "SHARED_AAAA", "SHARED_MX", "SHARED_SRV", "SHARED_TXT", "SHARED_CNAME", "NETWORK_VIEW", "NETWORK", "IPV6_NETWORK", "NETWORK_CONTAINER", "IPV6_NETWORK_CONTAINER", "RANGE", "IPV6_RANGE", "FIXED_ADDRESS", "IPV6_FIXED_ADDRESS", "ROAMING_HOST", "DHCP_MAC_FILTER", "SHARED_NETWORK", "IPV6_SHARED_NETWORK", "TEMPLATE", "IPV6_TEMPLATE", "NETWORK_TEMPLATE", "IPV6_NETWORK_TEMPLATE", "RANGE_TEMPLATE", "IPV6_RANGE_TEMPLATE", "FIXED_ADDRESS_TEMPLATE", "IPV6_FIXED_ADDRESS_TEMPLATE", "OPTION_SPACE", "RESTORABLE_OPERATION", "CSV_IMPORT_TASK", "DHCP_LEASE_HISTORY", "IPV6_DHCP_LEASE_HISTORY", "GRID_FILE_DIST_PROPERTIES", "MEMBER_FILE_DIST_PROPERTIES", "FILE_DIST_DIRECTORY", "HSM_GROUP", "GRID_AAA_PROPERTIES", "AAA_EXTERNAL_SERVICE", "NETWORK_DISCOVERY", "SCHEDULE_TASK", "MS_SUPERSCOPE", "MEMBER_DNS_PROPERTIES", "MEMBER_DHCP_PROPERTIES", "MEMBER_SECURITY_PROPERTIES", "MEMBER_ANALYTICS_PROPERTIES", "RESTART_SERVICE", "GRID_DNS_PROPERTIES", "GRID_DHCP_PROPERTIES", "GRID_REPORTING_PROPERTIES", "GRID_SECURITY_PROPERTIES", "IMC_PROPERTIES", "IMC_SITE", "IMC_AVP", "GRID_ANALYTICS_PROPERTIES", "RULESET", "DNS64_SYNTHESIS_GROUP", "DASHBOARD_TASK", "REPORTING_DASHBOARD", "REPORTING_SEARCH", "OCSP_SERVICE", "CA_CERTIFICATE", "RESPONSE_POLICY_ZONE", "RESPONSE_POLICY_RULE", "DHCP_FINGERPRINT", "DEFINED_ACL", "FIREEYE_PUBLISH_ALERT", "HOST_ADDRESS", "IPV6_HOST_ADDRESS", "PORT_CONTROL", "DEVICE", "KERBEROS_KEY", "BFD_TEMPLATE", "MS_ADSITES_DOMAIN", "IDNS_LBDN", "IDNS_LBDN_RECORD", "IDNS_POOL", "IDNS_SERVER", "IDNS_TOPOLOGY", "IDNS_MONITOR", "IDNS_CERTIFICATE", "IDNS_GEO_IP", "TENANT", "RECLAMATION", "SUPER_HOST", "ADD_A_RR_WITH_EMPTY_HOSTNAME", "DATACOLLECTOR_CLUSTER", "DELETED_OBJS_INFO_TRACKING", "SAML_AUTH_SERVICE", "VLAN_VIEW", "VLAN_RANGE", "VLAN_OBJECTS"], "is_array": false, "name": "resource_type", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "role", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}], "restrictions": ["global search", "scheduling", "csv"], "type": "permission", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "always_update_dns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_domainname", "overridden_by": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_all_clients", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovery_basic_poll_settings", "overridden_by": "use_discovery_basic_polling_settings", "schema": {"fields": [{"is_array": false, "name": "port_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "device_profile", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "snmp_collection", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "netbios_scanning", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "complete_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "smart_subnet_ping_sweep", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "auto_arp_refresh_before_switch_port_polling", "supports": "rwu", "type": ["bool"]}, {"enum_values": ["DISABLED", "PERIODIC", "SCHEDULED"], "is_array": false, "name": "switch_port_data_collection_polling", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "switch_port_data_collection_polling_schedule", "schema": {"fields": [{"enum_values": ["SATURDAY", "SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"], "is_array": true, "name": "weekdays", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "time_zone", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "recurring_time", "supports": "rwu", "type": ["timestamp"]}]}, "supports": "rwu", "type": ["setting:schedule"], "wapi_primitive": "struct"}, {"is_array": false, "name": "switch_port_data_collection_polling_interval", "supports": "rwu", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:basicpollsettings"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovery_member", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "email_list", "overridden_by": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_dhcp_thresholds", "overridden_by": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_discovery", "overridden_by": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_email_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_ifmap_publishing", "overridden_by": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "enable_immediate_discovery", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "enable_snmp_warnings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "end_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "exclude", "schema": {"fields": [{"is_array": false, "name": "start_address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "end_address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["exclusionrange"], "wapi_primitive": "struct"}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "failover_association", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "fingerprint_filter_rules", "schema": {"fields": [{"is_array": false, "name": "filter", "supports": "rwu", "type": ["string"]}, {"enum_values": ["Allow", "<PERSON><PERSON>"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"], "wapi_primitive": "struct"}, {"is_array": false, "name": "high_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "high_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ignore_dhcp_option_list_request", "overridden_by": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "is_split_scope", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "known_clients", "overridden_by": "use_known_clients", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "lease_scavenge_time", "overridden_by": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["int"]}, {"is_array": true, "name": "logic_filter_rules", "schema": {"fields": [{"is_array": false, "name": "filter", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "type", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["logicfilterrule"], "wapi_primitive": "struct"}, {"is_array": false, "name": "low_water_mark", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "low_water_mark_reset", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "mac_filter_rules", "schema": {"fields": [{"is_array": false, "name": "filter", "supports": "rwu", "type": ["string"]}, {"enum_values": ["Allow", "<PERSON><PERSON>"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"], "wapi_primitive": "struct"}, {"is_array": false, "name": "member", "schema": {"fields": [{"is_array": false, "name": "ipv4addr", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}]}, "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["dhcpmember"], "wapi_primitive": "struct"}, {"is_array": true, "name": "ms_options", "schema": {"fields": [{"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "user_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "type", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["msdhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ms_server", "schema": {"fields": [{"is_array": false, "name": "ipv4addr", "supports": "rwu", "type": ["string"]}]}, "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["msdhcpserver"], "wapi_primitive": "struct"}, {"is_array": true, "name": "nac_filter_rules", "schema": {"fields": [{"is_array": false, "name": "filter", "supports": "rwu", "type": ["string"]}, {"enum_values": ["Allow", "<PERSON><PERSON>"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"], "wapi_primitive": "struct"}, {"is_array": false, "name": "name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "network", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "next_available_ip", "schema": {"input_fields": [{"is_array": false, "name": "num", "supports": "w", "type": ["uint"]}, {"is_array": true, "name": "exclude", "supports": "w", "type": ["string"]}], "output_fields": [{"is_array": true, "name": "ips", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["nextavailableip"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "option_filter_rules", "schema": {"fields": [{"is_array": false, "name": "filter", "supports": "rwu", "type": ["string"]}, {"enum_values": ["Allow", "<PERSON><PERSON>"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"], "wapi_primitive": "struct"}, {"is_array": true, "name": "options", "overridden_by": "use_options", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_option", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "recycle_leases", "overridden_by": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "relay_agent_filter_rules", "schema": {"fields": [{"is_array": false, "name": "filter", "supports": "rwu", "type": ["string"]}, {"enum_values": ["Allow", "<PERSON><PERSON>"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["<PERSON><PERSON><PERSON>"], "wapi_primitive": "struct"}, {"enum_values": ["NONE", "MEMBER", "FAILOVER", "MS_SERVER", "MS_FAILOVER"], "is_array": false, "name": "server_association_type", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "split_member", "schema": {"fields": [{"is_array": false, "name": "ipv4addr", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "w", "type": ["msdhcpserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "split_scope_exclusion_percent", "standard_field": false, "supports": "w", "type": ["uint"]}, {"is_array": false, "name": "start_addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "template", "standard_field": false, "supports": "w", "type": ["string"]}, {"is_array": false, "name": "unknown_clients", "overridden_by": "use_unknown_clients", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_domainname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_discovery_basic_polling_settings", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_email_list", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_dhcp_thresholds", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ifmap_publishing", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_known_clients", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recycle_leases", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_unknown_clients", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": [], "type": "range", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "schema": {"fields": [{"is_array": false, "name": "device_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discovered_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discoverer", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "duid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "first_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mgmt_ip_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "netbios_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_ip", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "open_ports", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "os", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_duplex", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_link_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_speed", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_adapter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_cluster", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_datacenter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_host", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_switch", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discoverydata"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovered_data.device_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discovered_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discoverer", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.first_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.last_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.mac_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.mgmt_ip_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.netbios_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_ip", "searchable_by": "=~", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.network_component_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.open_ports", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.os", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_duplex", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_link_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_speed", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.v_adapter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_cluster", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_datacenter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_host", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_switch", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "supports_inline_funccall": true, "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:a", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "schema": {"fields": [{"is_array": false, "name": "device_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discovered_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discoverer", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "duid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "first_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mgmt_ip_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "netbios_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_ip", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "open_ports", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "os", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_duplex", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_link_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_speed", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_adapter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_cluster", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_datacenter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_host", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_switch", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discoverydata"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovered_data.device_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discovered_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discoverer", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.duid", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.first_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.last_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.mac_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.mgmt_ip_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.netbios_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_ip", "searchable_by": "=~", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.network_component_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.open_ports", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.os", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_duplex", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_link_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_speed", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.v_adapter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_cluster", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_datacenter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_host", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_switch", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv6addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "supports_inline_funccall": true, "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:aaaa", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "canonical", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dns_canonical", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:cname", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "alias", "searchable_by": "!~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": true, "name": "aliases", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "configure_for_dns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable_discovery", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "dns_aliases", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "enable_immediate_discovery", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "!~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": true, "name": "ipv4addrs", "standard_field": true, "supports": "rwu", "type": ["record:host_ipv4addr"]}, {"is_array": false, "name": "ipv6addr", "searchable_by": "!~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": true, "name": "ipv6addrs", "standard_field": true, "supports": "rwu", "type": ["record:host_ipv6addr"]}, {"is_array": false, "name": "mac", "searchable_by": "!~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "rrset_order", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "snmp3_credential", "schema": {"fields": [{"is_array": false, "name": "user", "supports": "rwu", "type": ["string"]}, {"enum_values": ["NONE", "MD5", "SHA"], "is_array": false, "name": "authentication_protocol", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "authentication_password", "supports": "wu", "type": ["string"]}, {"enum_values": ["NONE", "DES", "3DES", "AES"], "is_array": false, "name": "privacy_protocol", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "privacy_password", "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:snmp3credential"], "wapi_primitive": "struct"}, {"is_array": false, "name": "snmp_credential", "schema": {"fields": [{"is_array": false, "name": "community_string", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["discovery:snmpcredential"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_snmp3_credential", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_snmp_credential", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:host", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "configure_for_dhcp", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "schema": {"fields": [{"is_array": false, "name": "device_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discovered_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discoverer", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "duid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "first_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mgmt_ip_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "netbios_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_ip", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "open_ports", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "os", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_duplex", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_link_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_speed", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_adapter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_cluster", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_datacenter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_host", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_switch", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discoverydata"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovered_data.device_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discovered_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discoverer", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.first_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.last_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.mac_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.mgmt_ip_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.netbios_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_ip", "searchable_by": "=~", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.network_component_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.open_ports", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.os", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_duplex", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_link_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_speed", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.v_adapter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_cluster", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_datacenter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_host", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_switch", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "enable_pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "host", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ignore_client_requested_options", "overridden_by": "use_ignore_client_requested_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "supports_inline_funccall": true, "type": ["string"]}, {"is_array": false, "name": "last_queried", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "match_client", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "network", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_option", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "pxe_lease_time", "overridden_by": "use_pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_for_ea_inheritance", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_client_requested_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": ["create", "delete", "permissions", "scheduling", "csv"], "type": "record:host_ipv4addr", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["ADDRESS", "PREFIX", "BOTH"], "is_array": false, "name": "address_type", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "configure_for_dhcp", "standard_field": true, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "schema": {"fields": [{"is_array": false, "name": "device_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discovered_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discoverer", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "duid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "first_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mgmt_ip_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "netbios_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_ip", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "open_ports", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "os", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_duplex", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_link_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_speed", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_adapter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_cluster", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_datacenter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_host", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_switch", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discoverydata"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovered_data.device_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discovered_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discoverer", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.duid", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.first_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.last_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.mac_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.mgmt_ip_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.netbios_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_ip", "searchable_by": "=~", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.network_component_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.open_ports", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.os", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_duplex", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_link_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_speed", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.v_adapter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_cluster", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_datacenter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_host", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_switch", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "domain_name", "overridden_by": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "domain_name_servers", "overridden_by": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "duid", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "host", "standard_field": true, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ipv6addr", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "supports_inline_funccall": true, "type": ["string"]}, {"is_array": false, "name": "ipv6prefix", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ipv6prefix_bits", "searchable_by": "=<>", "standard_field": false, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "match_client", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_option", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "preferred_lifetime", "overridden_by": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_domain_name", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_domain_name_servers", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_for_ea_inheritance", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_preferred_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "valid_lifetime", "overridden_by": "use_valid_lifetime", "standard_field": false, "supports": "rwu", "type": ["uint"]}], "restrictions": ["create", "delete", "permissions", "scheduling", "csv"], "type": "record:host_ipv6addr", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dns_mail_exchanger", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "mail_exchanger", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "preference", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:mx", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "discovered_data", "schema": {"fields": [{"is_array": false, "name": "device_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "device_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discovered_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "discoverer", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "duid", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "first_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "last_discovered", "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "mac_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mgmt_ip_address", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "netbios_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_ip", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_model", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_port_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "network_component_vendor", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "open_ports", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "os", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_duplex", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_link_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_speed", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_status", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_description", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "port_vlan_number", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_adapter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_cluster", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_datacenter", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_entity_type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_host", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "v_switch", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["discoverydata"], "wapi_primitive": "struct"}, {"is_array": false, "name": "discovered_data.device_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.device_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discovered_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.discoverer", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.first_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.last_discovered", "searchable_by": "=!<>", "standard_field": false, "supports": "s", "type": ["timestamp"]}, {"is_array": false, "name": "discovered_data.mac_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.mgmt_ip_address", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.netbios_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_ip", "searchable_by": "=~", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_model", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_port_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.network_component_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.network_component_vendor", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.open_ports", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.os", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_duplex", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_link_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_speed", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_status", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_description", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.port_vlan_number", "searchable_by": "=<>!", "standard_field": false, "supports": "s", "type": ["uint"]}, {"is_array": false, "name": "discovered_data.v_adapter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_cluster", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_datacenter", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_entity_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_host", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "discovered_data.v_switch", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_ptrdname", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ipv4addr", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "supports_inline_funccall": true, "type": ["string"]}, {"is_array": false, "name": "ipv6addr", "searchable_by": "=~", "standard_field": false, "supports": "rwus", "supports_inline_funccall": true, "type": ["string"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ptrdname", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:ptr", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_target", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "port", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "priority", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "target", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "weight", "searchable_by": "<=>", "standard_field": true, "supports": "rwus", "type": ["uint"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:srv", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dns_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "text", "searchable_by": "=~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ttl", "overridden_by": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "use_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "zone", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}], "restrictions": [], "type": "record:txt", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["CONFIG_ERROR", "DISABLED", "NO", "NO_PERMISSION", "NO_REQUEST", "OFFLINE", "REQUESTING", "RESTART_PENDING", "YES"], "is_array": false, "name": "dhcp_status", "standard_field": true, "supports": "r", "type": ["enum"]}, {"enum_values": ["CONFIG_ERROR", "DISABLED", "NO", "NO_PERMISSION", "NO_REQUEST", "OFFLINE", "REQUESTING", "RESTART_PENDING", "YES"], "is_array": false, "name": "dns_status", "standard_field": true, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "member", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["string"]}, {"enum_values": ["CONFIG_ERROR", "DISABLED", "NO", "NO_PERMISSION", "NO_REQUEST", "OFFLINE", "REQUESTING", "RESTART_PENDING", "YES"], "is_array": false, "name": "reporting_status", "standard_field": true, "supports": "r", "type": ["enum"]}], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "restartservicestatus", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["APPROVED", "NONE", "PENDING", "REJECTED"], "is_array": false, "name": "approval_status", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "approver", "searchable_by": "=:~", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "approver_comment", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "automatic_restart", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "changed_objects", "schema": {"fields": [{"enum_values": ["Convert IPv4 Lease", "Delete", "Restart Services", "Add", "Convert IPv6 Lease", "Lock/Unlock Zone", "Reset Grid", "Configure Grid", "Restart Services", "Network Discovery", "Upgrade Grid", "Modify"], "is_array": false, "name": "action", "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "name", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "type", "supports": "r", "type": ["string"]}, {"is_array": false, "name": "object_type", "supports": "r", "type": ["string"]}, {"is_array": true, "name": "properties", "supports": "r", "type": ["string"]}]}, "standard_field": false, "supports": "r", "type": ["changedobject"], "wapi_primitive": "struct"}, {"is_array": false, "name": "changed_objects.action", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "changed_objects.name", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "changed_objects.object_type", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "changed_objects.type", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}, {"is_array": false, "name": "execute_now", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"enum_values": ["COMPLETED", "FAILED", "PENDING", "WAITING_EXECUTION"], "is_array": false, "name": "execution_status", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["enum"]}, {"is_array": false, "name": "execution_time", "searchable_by": "<=>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "scheduled_time", "searchable_by": "<=>", "standard_field": false, "supports": "rwus", "type": ["timestamp"]}, {"is_array": false, "name": "submit_time", "searchable_by": "<=>", "standard_field": false, "supports": "rs", "type": ["timestamp"]}, {"is_array": false, "name": "submitter", "searchable_by": "=~:", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "submitter_comment", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "task_id", "searchable_by": "=", "standard_field": true, "supports": "rs", "type": ["uint"]}, {"is_array": false, "name": "ticket_number", "standard_field": false, "supports": "r", "type": ["string"]}], "restrictions": ["create", "permissions", "global search", "scheduling", "csv"], "type": "scheduledtask", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["discovery:device", "discovery:deviceinterface", "fixedaddress", "grid:dhcpproperties", "ipv6fixedaddress", "ipv6network", "ipv6networkcontainer", "ipv6range", "ipv6sharednetwork", "lease", "macfilteraddress", "member", "<PERSON><PERSON><PERSON>", "network", "networkcontainer", "networkview", "range", "record:a", "record:aaaa", "record:cname", "record:host", "record:host_ipv4addr", "record:host_ipv6addr", "record:mx", "record:ptr", "record:srv", "record:txt", "sharednetwork", "snmpuser", "view"], "is_array": false, "name": "objtype", "searchable_by": "=", "standard_field": false, "supports": "s", "type": ["enum"]}, {"is_array": false, "name": "search_string", "searchable_by": "=~:", "standard_field": false, "supports": "s", "type": ["string"]}], "restrictions": ["create", "delete", "update", "permissions", "global search", "scheduling", "csv"], "type": "search", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "authority", "overridden_by": "use_authority", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "bootfile", "overridden_by": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "bootserver", "overridden_by": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "ddns_generate_hostname", "overridden_by": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_server_always_updates", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_ttl", "overridden_by": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ddns_update_fixed_addresses", "overridden_by": "use_ddns_update_fixed_addresses", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ddns_use_option81", "overridden_by": "use_ddns_use_option81", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "deny_bootp", "overridden_by": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dhcp_utilization", "standard_field": false, "supports": "r", "type": ["uint"]}, {"enum_values": ["FULL", "HIGH", "LOW", "NORMAL"], "is_array": false, "name": "dhcp_utilization_status", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dynamic_hosts", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "enable_ddns", "overridden_by": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "ignore_client_identifier", "overridden_by": "use_ignore_client_identifier", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ignore_dhcp_option_list_request", "overridden_by": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "lease_scavenge_time", "overridden_by": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["int"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": true, "name": "networks", "standard_field": true, "supports": "rwu", "type": ["network"]}, {"is_array": false, "name": "nextserver", "overridden_by": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "options", "overridden_by": "use_options", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "num", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "vendor_class", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "value", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_option", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["dhcpoption"], "wapi_primitive": "struct"}, {"is_array": false, "name": "pxe_lease_time", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "static_hosts", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "total_hosts", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "update_dns_on_lease_renewal", "overridden_by": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_authority", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootfile", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_bootserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_generate_hostname", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_update_fixed_addresses", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ddns_use_option81", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_deny_bootp", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_enable_ddns", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_client_identifier", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_ignore_dhcp_option_list_request", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_lease_scavenge_time", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nextserver", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_options", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_update_dns_on_lease_renewal", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": [], "type": "sharednetwork", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["NONE", "MD5", "SHA"], "is_array": false, "name": "authentication_protocol", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "comment", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": true, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "name", "standard_field": true, "supports": "r", "type": ["string"]}, {"enum_values": ["NONE", "DES", "AES"], "is_array": false, "name": "privacy_protocol", "standard_field": false, "supports": "r", "type": ["enum"]}], "restrictions": ["create", "delete", "update", "read", "global search", "scheduling", "csv"], "type": "snmpuser", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"enum_values": ["REDIRECT", "REFUSE"], "is_array": false, "name": "blacklist_action", "overridden_by": "use_blacklist", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": false, "name": "blacklist_log_query", "overridden_by": "use_blacklist", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "blacklist_redirect_addresses", "overridden_by": "use_blacklist", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "blacklist_redirect_ttl", "overridden_by": "use_blacklist", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "blacklist_rulesets", "overridden_by": "use_blacklist", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "custom_root_name_servers", "overridden_by": "use_root_name_server", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "shared_with_ms_parent_delegation", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["extserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "dns64_enabled", "overridden_by": "use_dns64", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "dns64_groups", "overridden_by": "use_dns64", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "dnssec_enabled", "overridden_by": "use_dnssec", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "dnssec_expired_signatures_enabled", "overridden_by": "use_dnssec", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "dnssec_trusted_keys", "overridden_by": "use_dnssec", "schema": {"fields": [{"is_array": false, "name": "fqdn", "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "algorithm", "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "key", "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "secure_entry_point", "supports": "rwus", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["dns<PERSON><PERSON>ed<PERSON>"], "wapi_primitive": "struct"}, {"is_array": false, "name": "dnssec_validation_enabled", "overridden_by": "use_dnssec", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "enable_blacklist", "overridden_by": "use_blacklist", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"enum_values": ["YES", "NO", "BREAK_DNSSEC"], "is_array": false, "name": "filter_aaaa", "overridden_by": "use_filter_aaaa", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": true, "name": "filter_aaaa_list", "overridden_by": "use_filter_aaaa", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "forward_only", "overridden_by": "use_forwarders", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "forwarders", "overridden_by": "use_forwarders", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "is_default", "searchable_by": "=", "standard_field": true, "supports": "sr", "type": ["bool"]}, {"is_array": false, "name": "lame_ttl", "overridden_by": "use_lame_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "match_clients", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": true, "name": "match_destinations", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "max_cache_ttl", "overridden_by": "use_max_cache_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "max_ncache_ttl", "overridden_by": "use_max_ncache_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "name", "searchable_by": "=:~", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "network_view", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "notify_delay", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "nxdomain_log_query", "overridden_by": "use_nxdomain_redirect", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": false, "name": "nxdomain_redirect", "overridden_by": "use_nxdomain_redirect", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"is_array": true, "name": "nxdomain_redirect_addresses", "overridden_by": "use_nxdomain_redirect", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "nxdomain_redirect_ttl", "overridden_by": "use_nxdomain_redirect", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "nxdomain_rulesets", "overridden_by": "use_nxdomain_redirect", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "recursion", "overridden_by": "use_recursion", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["bool"]}, {"enum_values": ["CUSTOM", "INTERNET"], "is_array": false, "name": "root_name_server_type", "overridden_by": "use_root_name_server", "searchable_by": "=", "standard_field": false, "supports": "rwus", "type": ["enum"]}, {"is_array": true, "name": "sortlist", "overridden_by": "use_sortlist", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "match_list", "supports": "rwu", "type": ["string"]}]}, "standard_field": false, "supports": "rwu", "type": ["sortlist"], "wapi_primitive": "struct"}, {"is_array": false, "name": "use_blacklist", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_dns64", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_dnssec", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_filter_aaaa", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_forwarders", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_lame_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_max_cache_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_max_ncache_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_nxdomain_redirect", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_recursion", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_root_name_server", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_sortlist", "standard_field": false, "supports": "rwu", "type": ["bool"]}], "restrictions": ["csv"], "type": "view", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": true, "name": "allow_active_dir", "overridden_by": "use_allow_active_dir", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "allow_gss_tsig_for_underscore_zone", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "allow_gss_tsig_zone_updates", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "allow_query", "overridden_by": "use_allow_query", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": true, "name": "allow_transfer", "overridden_by": "use_allow_transfer", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": true, "name": "allow_update", "overridden_by": "use_allow_update", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "allow_update_forwarding", "overridden_by": "use_allow_update_forwarding", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "copy_xfer_to_notify", "overridden_by": "use_copy_xfer_to_notify", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "create_ptr_for_bulk_hosts", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "create_ptr_for_hosts", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "create_underscore_zones", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable_forwarding", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "display_domain", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_fqdn", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_soa_email", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_soa_mname", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dnssec_key_params", "overridden_by": "use_dnssec_key_params", "schema": {"fields": [{"enum_values": ["1", "10", "3", "5", "6", "7", "8"], "is_array": false, "name": "ksk_algorithm", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "ksk_rollover", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ksk_size", "supports": "rwu", "type": ["uint"]}, {"enum_values": ["NSEC", "NSEC3"], "is_array": false, "name": "next_secure_type", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "signature_expiration", "supports": "rwu", "type": ["uint"]}, {"enum_values": ["1", "10", "3", "5", "6", "7", "8"], "is_array": false, "name": "zsk_algorithm", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "zsk_rollover", "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "zsk_size", "supports": "rwu", "type": ["uint"]}]}, "standard_field": false, "supports": "rwu", "type": ["dnsseckeyparams"], "wapi_primitive": "struct"}, {"is_array": false, "name": "dnssec_operation", "schema": {"input_fields": [{"enum_values": ["IMPORT_DS", "ROLLOVER_KSK", "SIGN", "UNSIGN"], "is_array": false, "name": "operation", "supports": "w", "type": ["enum"]}, {"is_array": false, "name": "buffer", "supports": "w", "type": ["string"]}], "output_fields": []}, "standard_field": false, "supports": "rwu", "type": ["dnssecoperation"], "wapi_primitive": "<PERSON><PERSON><PERSON>"}, {"is_array": false, "name": "do_host_abstraction", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"enum_values": ["FAIL", "WARN"], "is_array": false, "name": "effective_check_names_policy", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "effective_record_name_policy", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "external_primaries", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "shared_with_ms_parent_delegation", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["extserver"], "wapi_primitive": "struct"}, {"is_array": true, "name": "external_secondaries", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "shared_with_ms_parent_delegation", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["extserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "fqdn", "searchable_by": "=~", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": true, "name": "grid_primary", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "grid_replicate", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "lead", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["memberserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "grid_primary_shared_with_ms_parent_delegation", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": true, "name": "grid_secondaries", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "grid_replicate", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "lead", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["memberserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "import_from", "overridden_by": "use_import_from", "standard_field": false, "supports": "wu", "type": ["string"]}, {"is_array": false, "name": "is_dnssec_enabled", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "is_dnssec_signed", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "last_queried", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "locked", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "locked_by", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mask_prefix", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ms_ad_integrated", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "ms_allow_transfer", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac"], "wapi_primitive": "struct"}, {"enum_values": ["ADDRESS_AC", "ANY", "ANY_NS", "NONE"], "is_array": false, "name": "ms_allow_transfer_mode", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["ANY", "NONE", "SECURE"], "is_array": false, "name": "ms_ddns_mode", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["AUTH_BOTH", "AUTH_PRIMARY", "AUTH_SECONDARY", "NONE", "STUB"], "is_array": false, "name": "ms_managed", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": true, "name": "ms_primaries", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "is_master", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ns_ip", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ns_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "shared_with_ms_parent_delegation", "supports": "r", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["msdnsserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ms_read_only", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": true, "name": "ms_secondaries", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "is_master", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ns_ip", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ns_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "shared_with_ms_parent_delegation", "supports": "r", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["msdnsserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "ms_sync_master_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": true, "name": "network_associations", "standard_field": false, "supports": "r", "type": ["network", "networkcontainer", "ipv6network", "ipv6networkcontainer"]}, {"is_array": false, "name": "network_view", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "notify_delay", "overridden_by": "use_notify_delay", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "ns_group", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "parent", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "prefix", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"enum_values": ["External", "Grid", "Microsoft", "None"], "is_array": false, "name": "primary_type", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "record_name_policy", "overridden_by": "use_record_name_policy", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "records_monitored", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "rr_not_queried_enabled_time", "standard_field": false, "supports": "r", "type": ["timestamp"]}, {"is_array": false, "name": "set_soa_serial_number", "standard_field": false, "supports": "wu", "type": ["bool"]}, {"is_array": false, "name": "soa_default_ttl", "overridden_by": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "soa_email", "overridden_by": "use_soa_email", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "soa_expire", "overridden_by": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "soa_mname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "soa_negative_ttl", "overridden_by": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "soa_refresh", "overridden_by": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "soa_retry", "overridden_by": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "soa_serial_number", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": true, "name": "srgs", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": true, "name": "update_forwarding", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"enum_values": ["ALLOW", "DENY"], "is_array": false, "name": "permission", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["addressac", "tsigac"], "wapi_primitive": "struct"}, {"is_array": false, "name": "use_allow_active_dir", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_allow_query", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_allow_transfer", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_allow_update", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_allow_update_forwarding", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_check_names_policy", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_copy_xfer_to_notify", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_dnssec_key_params", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_external_primary", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_grid_zone_timer", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_import_from", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_notify_delay", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_record_name_policy", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_soa_email", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "use_soa_mname", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "using_srg_associations", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FORWARD", "IPV4", "IPV6"], "is_array": false, "name": "zone_format", "standard_field": false, "supports": "rw", "type": ["enum"]}, {"is_array": false, "name": "zone_not_queried_enabled_time", "standard_field": false, "supports": "r", "type": ["timestamp"]}], "restrictions": ["global search"], "type": "zone_auth", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": true, "name": "delegate_to", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "shared_with_ms_parent_delegation", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": true, "supports": "rwu", "type": ["extserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "delegated_ttl", "overridden_by": "use_delegated_ttl", "standard_field": false, "supports": "rwu", "type": ["uint"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "display_domain", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_fqdn", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "enable_rfc2317_exclusion", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "fqdn", "searchable_by": "=~", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "locked", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "locked_by", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mask_prefix", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ms_ad_integrated", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"enum_values": ["ANY", "NONE", "SECURE"], "is_array": false, "name": "ms_ddns_mode", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["AUTH_BOTH", "AUTH_PRIMARY", "AUTH_SECONDARY", "NONE", "STUB"], "is_array": false, "name": "ms_managed", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "ms_read_only", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "ms_sync_master_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "parent", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "prefix", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_delegated_ttl", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "using_srg_associations", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FORWARD", "IPV4", "IPV6"], "is_array": false, "name": "zone_format", "standard_field": false, "supports": "rw", "type": ["enum"]}], "restrictions": ["global search", "csv"], "type": "zone_delegated", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "display_domain", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_fqdn", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": true, "name": "forward_to", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "shared_with_ms_parent_delegation", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": true, "supports": "rwu", "type": ["extserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "forwarders_only", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "forwarding_servers", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "forwarders_only", "supports": "rwu", "type": ["bool"]}, {"is_array": true, "name": "forward_to", "overridden_by": "use_override_forwarders", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "shared_with_ms_parent_delegation", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "supports": "rwu", "type": ["extserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "use_override_forwarders", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["forwardingmemberserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "fqdn", "searchable_by": "=~", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "locked", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "locked_by", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mask_prefix", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ms_ad_integrated", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"enum_values": ["ANY", "NONE", "SECURE"], "is_array": false, "name": "ms_ddns_mode", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["AUTH_BOTH", "AUTH_PRIMARY", "AUTH_SECONDARY", "NONE", "STUB"], "is_array": false, "name": "ms_managed", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "ms_read_only", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "ms_sync_master_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "parent", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "prefix", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "using_srg_associations", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FORWARD", "IPV4", "IPV6"], "is_array": false, "name": "zone_format", "standard_field": false, "supports": "rw", "type": ["enum"]}], "restrictions": ["global search", "csv"], "type": "zone_forward", "version": "1.4.1", "wapi_primitive": "object"}, {"cloud_additional_restrictions": ["all"], "fields": [{"is_array": false, "name": "address", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "comment", "searchable_by": "=:~", "standard_field": false, "supports": "rwus", "type": ["string"]}, {"is_array": false, "name": "disable", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "disable_forwarding", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "display_domain", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "dns_fqdn", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "extattrs", "standard_field": false, "supports": "rwu", "type": ["extattr"]}, {"is_array": false, "name": "fqdn", "searchable_by": "=~", "standard_field": true, "supports": "rws", "type": ["string"]}, {"is_array": false, "name": "locked", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "locked_by", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "mask_prefix", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "ms_ad_integrated", "standard_field": false, "supports": "rwu", "type": ["bool"]}, {"enum_values": ["ANY", "NONE", "SECURE"], "is_array": false, "name": "ms_ddns_mode", "standard_field": false, "supports": "rwu", "type": ["enum"]}, {"enum_values": ["AUTH_BOTH", "AUTH_PRIMARY", "AUTH_SECONDARY", "NONE", "STUB"], "is_array": false, "name": "ms_managed", "standard_field": false, "supports": "r", "type": ["enum"]}, {"is_array": false, "name": "ms_read_only", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "ms_sync_master_name", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "parent", "searchable_by": "=", "standard_field": false, "supports": "rs", "type": ["string"]}, {"is_array": false, "name": "prefix", "standard_field": false, "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "soa_email", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "soa_expire", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "soa_mname", "standard_field": false, "supports": "r", "type": ["string"]}, {"is_array": false, "name": "soa_negative_ttl", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "soa_refresh", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "soa_retry", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": false, "name": "soa_serial_number", "standard_field": false, "supports": "r", "type": ["uint"]}, {"is_array": true, "name": "stub_from", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "shared_with_ms_parent_delegation", "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "tsig_key", "supports": "rwu", "type": ["string"]}, {"enum_values": ["HMAC-MD5", "HMAC-SHA256"], "is_array": false, "name": "tsig_key_alg", "supports": "rwu", "type": ["enum"]}, {"is_array": false, "name": "tsig_key_name", "overridden_by": "use_tsig_key_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "use_tsig_key_name", "supports": "rwu", "type": ["bool"]}]}, "standard_field": true, "supports": "rwu", "type": ["extserver"], "wapi_primitive": "struct"}, {"is_array": true, "name": "stub_members", "schema": {"fields": [{"is_array": false, "name": "name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "grid_replicate", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "lead", "supports": "rwu", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["memberserver"], "wapi_primitive": "struct"}, {"is_array": true, "name": "stub_msservers", "schema": {"fields": [{"is_array": false, "name": "address", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "is_master", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "ns_ip", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "ns_name", "supports": "rwu", "type": ["string"]}, {"is_array": false, "name": "stealth", "supports": "rwu", "type": ["bool"]}, {"is_array": false, "name": "shared_with_ms_parent_delegation", "supports": "r", "type": ["bool"]}]}, "standard_field": false, "supports": "rwu", "type": ["msdnsserver"], "wapi_primitive": "struct"}, {"is_array": false, "name": "using_srg_associations", "standard_field": false, "supports": "r", "type": ["bool"]}, {"is_array": false, "name": "view", "searchable_by": "=", "standard_field": true, "supports": "rwus", "type": ["string"]}, {"enum_values": ["FORWARD", "IPV4", "IPV6"], "is_array": false, "name": "zone_format", "standard_field": false, "supports": "rw", "type": ["enum"]}], "restrictions": ["global search", "csv"], "type": "zone_stub", "version": "1.4.1", "wapi_primitive": "object"}]