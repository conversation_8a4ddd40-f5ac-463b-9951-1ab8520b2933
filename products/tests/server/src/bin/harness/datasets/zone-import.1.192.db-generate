$TTL	28800

@	IN	SOA	  ns1.foo.com my.test.foo.com. (
                   3 
                   10800 
                   3600 
                   2592000 
                   900)
                   
1.192.in-addr.arpa.	IN	NS	ns1.foo.com.
168.1.192.in-addr.arpa.	IN	NS	ns1.test.com.
168.1.192.in-addr.arpa.	IN	NS	ns2.test.com.
2.1.1.192.in-addr.arpa. IN      PTR     ns1.foo.com.
2.168.1.192.in-addr.arpa. IN    PTR     ns1.test.com.

$GENERATE 11-12 0.1.192.in-addr.arpa. NS SERVER$.EXAMPLE

$GENERATE 1-3 $.0.1.192.in-addr.arpa.  CNAME   $.0.0.1.192.in-addr.arpa.

