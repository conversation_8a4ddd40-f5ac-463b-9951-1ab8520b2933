<DATABASE NAME="onedb" VERSION="7.3.0-Alpha-leonid-2016-03-01-16-16" MD5="51c70078456e23ce003c8f67bbdc5829" SCHEMA-MD5="ba036ee0cb98bf787ac1b497f35f9743" INT-VERSION="7.3.6000-999999">
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
    <PROPERTY NAME="is_master" VALUE="true"/>
    <PROPERTY NAME="ha_enabled" VALUE="0"/>
    <PROPERTY NAME="active_position" VALUE="0"/>
    <PROPERTY NAME="virtual_oid" VALUE="0"/>

    <!-- Dependencies on other projects -->
    <PROPERTY NAME="upgrade_group" VALUE="Grid Master"/>
  </OBJECT>

  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
    <PROPERTY NAME="position" VALUE="0"/>
    <PROPERTY NAME="physical_oid" VALUE="0"/>
    <PROPERTY NAME="virtual_node" VALUE="0"/>
  </OBJECT>

  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.maxmind_db_info"/>
    <PROPERTY NAME="parent" VALUE="0"/>
  </OBJECT>
</DATABASE>
