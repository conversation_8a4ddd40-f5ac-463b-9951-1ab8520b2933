version 1.0

Header-AuthZone,fqdn*,grid_primaries,view,external_secondaries,allow_transfer,allow_query,zone_type,allow_active_dir,allow_update,zone_format,notify_delay,disabled,soa_negative_ttl,soa_mnames,soa_default_ttl,soa_retry,,create_underscore_zones,soa_serial_number,soa_email,comment,soa_expire,soa_refresh
AuthZone,test_csv_export.com,infoblox.localdomain,default,test_csv.infoblox.com/*******/TRUE,"*********/Deny,1234::/64/Allow","*********/Deny,1234::/64/Allow",,2.3.4.5,"4321::/64/Allow",FORWARD,100,FALSE,400,infoblox.localdomain/mname1,900,800,FALSE,FALSE,1,<EMAIL>,Authzone1,100,200
AuthZone,test_data_export.com,infoblox.localdomain,default,test_data.infoblox.com/*******/TRUE,"*********/Deny,1234::/64/Allow","*********/Deny,1234::/64/Allow",Authoritative,*******,"1234::/64/Allow",FORWARD,100,FALSE,100,infoblox.localdomain/mname2,300,600,FALSE,FALSE,1,<EMAIL>,Authzone2,200,500
AuthZone,20.0.0.0/24,infoblox.localdomain,default,test_csv1.infoblox.com/*******/TRUE,"*********/Deny,1234::/64/Allow","*********/Deny,1234::/64/Allow",Authoritative,*******,"3456::/64/Allow",IPV4,2000,FALSE,4000,infoblox.localdomain/mname3,9000,8000,FALSE,FALSE,1,<EMAIL>,Authzone3,1000,2000

Header-ARecord,fqdn,view,address*,comment,disabled,ttl
ARecord,a1.test_csv_export.com,default,*********,A record,FALSE,7200
ARecord,a2.test_csv_export.com,default,*********,A2 record,FALSE,3600

Header-AaaaRecord,fqdn*,view,address,,comment,disabled,ttl
AaaaRecord,aa1.test_csv_export.com,default,2001::123,nul column test,AAAA record,FALSE,7200
AaaaRecord,aa2.test_csv_export.com,default,2001::456,nul column test,AAAA2 record,FALSE,2700

Header-CnameRecord,fqdn,view,canonical_name,comment,disabled,ttl
CnameRecord,c1.test_csv_export.com,default,a1.test_csv.com,C1 record,FALSE,3600

Header-DnameRecord,fqdn,view,target,comment,disabled,ttl
DnameRecord,d1.test_csv_export.com,default,target.test_csv.com,D1 record,TRUE,100

Header-MxRecord,fqdn,view,mx,priority,comment,disabled,ttl
MxRecord,m1.test_csv_export.com,default,webmaster.infoblox.com,10,M1 record,TRUE,500

Header-NaptrRecord,fqdn,view,preference,services,regexp,flags,order,replacement,comment,disabled,ttl
NaptrRecord,n1.test_csv_export.com,default,20,SIP+D2U,,U,10,test.com,NAPTR1 record,FALSE,1000

Header-SrvRecord,fqdn,view,target,port,weight,priority,comment,disabled,ttl
SrvRecord,s1.test_csv_export.com,default,target.test.com,6666,5,10,SRV1 record,FALSE,500

Header-TxtRecord,fqdn,view,text,comment,disabled,ttl
TxtRecord,t1.test_csv_export.com,default,hello world,TXT1 record,FALSE,6667

Header-HostRecord,fqdn,view,addresses,ipv6_addresses,aliases,configure_for_dns,comment,disabled,ttl
HostRecord,h1.test_csv_export.com,default,"*******,*******","2001:0db8:85a3:0000:0000:8a2e:0370:7334,fc00::","alias1.test.com,alias2.test.com",TRUE,Host record,False,6667

Header-HostAddress,parent,address,configure_for_dhcp,mac_address,match_option,OPTION-14,OPTION-17
HostAddress,h1.test_csv_export.com,*******,True,11:22:33:4A:55:66,MAC_ADDRESS,DUMP,ROOT

Header-IPv6HostAddress,parent,address
IPv6HostAddress,h1.test_csv_export.com,2000:0db8:85a3:0000:0000:8a2e:0370:7334

Header-NsRecord,fqdn,view,dname,zone_nameservers
NsRecord,test_csv_export.com,default,dname1.test.com,"*******/FALSE,*******/TRUE"
NsRecord,test_csv_export.com,default,dname2.test.com,"""*******/FALSE,*******/TRUE"""

Header-PtrRecord,fqdn*,address*,dname*,comment,disabled,ttl
PtrRecord,********.in-addr.arpa,********,p1.com,PTR11 record,FALSE,28800

header-httpsrecord,fqdn*,priority*,target_name*,svc_parameters
httpsrecord,a2.test_csv_export.com,2,google.com,"alpn/[h2, h3]/False,port/[8877]/True"

header-svcbrecord,fqdn*,priority*,target_name*,svc_parameters
svcbrecord,a2.test_csv_export.com,1,hello.com,"alpn/[h2, h3]/False,port/[8443]/True,ipv4hint/[***********, ***********]/True"
