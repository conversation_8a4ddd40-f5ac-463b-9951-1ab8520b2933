<DATABASE NAME="onedb" VERSION="MDXMLTEST">
    <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_tlsa"/>
      <PROPERTY NAME="certificate_usage" VALUE="0"/>
      <PROPERTY NAME="selector" VALUE="0"/>
      <PROPERTY NAME="matched_type" VALUE="0"/>
      <PROPERTY NAME="ttl_option" VALUE="0"/>
      <PROPERTY NAME="disabled" VALUE="false"/>
      <PROPERTY NAME="zone" VALUE="._default.com.test"/>
      <PROPERTY NAME="name" VALUE="_tcp._443"/>
      <PROPERTY NAME="display_name" VALUE="_tcp._443"/>
      <PROPERTY NAME="certificate_data" VALUE="AAAA"/>
      <PROPERTY NAME="certificate_data_hash" VALUE="53b74be8b295b733fdfafbd7d2a22b1686733740de7fdc592b26cf3e1874cfce158170ce9230e24696331a61829244e5d9f48abdacc9ffa8c4cb498724844cf8"/>
    </OBJECT>
</DATABASE>

