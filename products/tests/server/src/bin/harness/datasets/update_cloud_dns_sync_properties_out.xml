<DATABASE NAME="onedb" VERSION="MDXMLTEST">
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_cloud_dns_sync_properties"/>
      <PROPERTY NAME="virtual_node" VALUE="0"/>
      <PROPERTY NAME="cloud_dns_sync_enabled" VALUE="true"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_cloud_dns_sync_properties"/>
      <PROPERTY NAME="virtual_node" VALUE="1"/>
      <PROPERTY NAME="cloud_dns_sync_enabled" VALUE="true"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_cloud_dns_sync_properties"/>
      <PROPERTY NAME="virtual_node" VALUE="2"/>
      <PROPERTY NAME="cloud_dns_sync_enabled" VALUE="false"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_cloud_dns_sync_properties"/>
      <PROPERTY NAME="virtual_node" VALUE="3"/>
      <PROPERTY NAME="cloud_dns_sync_enabled" VALUE="false"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_cloud_dns_sync_properties"/>
      <PROPERTY NAME="virtual_node" VALUE="4"/>
      <PROPERTY NAME="cloud_dns_sync_enabled" VALUE="false"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cdiscovery_task"/>
      <PROPERTY NAME="name" VALUE="cdiscovery_task"/>
      <PROPERTY NAME="member" VALUE="0"/>
      <PROPERTY NAME="driver_type" VALUE="AWS"/>
      <PROPERTY NAME="selected_regions" VALUE="us-west-1"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cdiscovery_task"/>
      <PROPERTY NAME="name" VALUE="cdiscovery_task"/>
      <PROPERTY NAME="member" VALUE="1"/>
      <PROPERTY NAME="driver_type" VALUE="AWS"/>
      <PROPERTY NAME="selected_regions" VALUE="us-east-1"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cdiscovery_task"/>
      <PROPERTY NAME="name" VALUE="cdiscovery_task"/>
      <PROPERTY NAME="member" VALUE="2"/>
      <PROPERTY NAME="fqdn_or_ip" VALUE="************"/>
      <PROPERTY NAME="driver_type" VALUE="VMWARE"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cdiscovery_task"/>
      <PROPERTY NAME="name" VALUE="cdiscovery_task"/>
      <PROPERTY NAME="driver_type" VALUE="AZURE"/>
      <PROPERTY NAME="fqdn_or_ip" VALUE="https://login.microsoftonline.com/ccf6dc4a-9fe9-4c20-85a6-46d2709e8435/oauth2/token"/>
      <PROPERTY NAME="member" VALUE="3"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cdiscovery_task"/>
      <PROPERTY NAME="name" VALUE="cdiscovery_task"/>
      <PROPERTY NAME="member" VALUE="4"/>
      <PROPERTY NAME="driver_type" VALUE="AWS"/>
      <PROPERTY NAME="selected_regions" VALUE="us-east-1"/>
  </OBJECT>
  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.aws_rte53_task_group"/>
      <PROPERTY NAME="name" VALUE="aws_rte53_task_group"/>
      <PROPERTY NAME="grid_member" VALUE="4"/>
  </OBJECT>

</DATABASE>
