ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
ddns-local-address4 ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/infoblox/var/dhcpd_conf/dhcp_updater.key";

subnet ********** netmask ************* {
       infoblox-ignore-uid true;
       infoblox-ignore-macaddr false;
}

subnet *********** netmask ************* {
       not authoritative;
}

shared-network "test1" {
               infoblox-ignore-uid true;
               infoblox-ignore-macaddr false;

subnet ********* netmask ************* {
       infoblox-ignore-uid true;
       infoblox-ignore-macaddr false;
       }

subnet 100.0.0.0 netmask ********* {
       infoblox-ignore-uid true;
       infoblox-ignore-macaddr false;
       }

subnet *********** netmask *************** {
       infoblox-ignore-uid true;
       infoblox-ignore-macaddr false;
       }
}

#End of dhcpd.conf file
