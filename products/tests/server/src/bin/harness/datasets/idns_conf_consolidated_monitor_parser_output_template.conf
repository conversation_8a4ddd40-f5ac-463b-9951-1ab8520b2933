# This template is used to check the output of
# "/infoblox/tests/bin/idns_conf_parser -i" utility
# for UT infoblox.test.dns.test_idns_conf.IdnsConfig.test_consolidated_monitor_configuration
# Don't update this file manually, use the logic in test_consolidated_monitor_configuration() in
# test_idns_conf.py.

{'config': {'cache_filename': '/tmp/idns/idnsd_cache',
             'dns_responses': 'DNS_RESPONSE_IF_NO_DTC',
             'dtc_cache_ha_sync': False,
             'dtc_dnssec_mode': 'SIGNED',
             'health_status_filename': 'HEALTH_STATUS_FILENAME_REPLACE',
             'log_facility': 'LOG_FACILITY_REPLACE',
             'log_idns_gslb': False,
             'health_checks_exist': True,
             'maxmind': {'EA': {'active_file': '/storage/maxmind/ea/active.mmdb',
                                'custom_file': '/storage/maxmind/ea/deployed.mmdb',
                                'default_file': '/storage/maxmind/ea/default.mmdb'},
                         'GEOIP': {'active_file': '/storage/maxmind/geoip/active.mmdb',
                                   'custom_file': '/storage/maxmind/geoip/deployed.mmdb',
                                   'default_file': '/storage/maxmind/geoip/default.mmdb'}},
             'persistent_cache': 32,
             'prefer_client_subnet': False,
             'report_gen_interval': 600,
             'status_gen_interval': 10,
             'topology_ea': {},
             'worker_thread_count': 'THREAD_COUNT_REPLACE'},
  'health_data': [{'health': 'UNKNOWN',
                   'monitor': 'MON_TCP_ID_REPLACE',
                   'monitor_type': 'TCP',
                   'server': 'serv6_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_TCP_ID_REPLACE',
                   'monitor_type': 'TCP',
                   'server': 'serv4_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_TCP_ID_REPLACE',
                   'monitor_type': 'TCP',
                   'server': 'serv8_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_TCP_ID_REPLACE',
                   'monitor_type': 'TCP',
                   'server': 'serv3_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_TCP_ID_REPLACE',
                   'monitor_type': 'TCP',
                   'server': 'serv1_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_TCP_ID_REPLACE',
                   'monitor_type': 'TCP',
                   'server': 'serv5_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_ICMP_ID_REPLACE',
                   'monitor_type': 'ICMP',
                   'server': 'serv6_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_ICMP_ID_REPLACE',
                   'monitor_type': 'ICMP',
                   'server': 'serv4_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_ICMP_ID_REPLACE',
                   'monitor_type': 'ICMP',
                   'server': 'serv2_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_ICMP_ID_REPLACE',
                   'monitor_type': 'ICMP',
                   'server': 'serv8_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_ICMP_ID_REPLACE',
                   'monitor_type': 'ICMP',
                   'server': 'serv3_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_ICMP_ID_REPLACE',
                   'monitor_type': 'ICMP',
                   'server': 'serv1_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_ICMP_ID_REPLACE',
                   'monitor_type': 'ICMP',
                   'server': 'serv5_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_HTTP_ID_REPLACE',
                   'monitor_type': 'HTTP',
                   'server': 'serv4_ID_REPLACE'},
                  {'health': 'UNKNOWN',
                   'monitor': 'MON_HTTP_ID_REPLACE',
                   'monitor_type': 'HTTP',
                   'server': 'serv3_ID_REPLACE'}],
  'lbdn_pools': [{'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                  'parent_lbdn->base.name': 'one.lbdn.com',
                  'pool->base.id': 'pool5_ID_REPLACE',
                  'pool->base.name': 'Pool5',
                  'ratio': 0,
                  'ratio_entries': {}},
                 {'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                  'parent_lbdn->base.name': 'one.lbdn.com',
                  'pool->base.id': 'pool2_ID_REPLACE',
                  'pool->base.name': 'Pool2',
                  'ratio': 0,
                  'ratio_entries': {}},
                 {'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                  'parent_lbdn->base.name': 'one.lbdn.com',
                  'pool->base.id': 'pool1_ID_REPLACE',
                  'pool->base.name': 'Pool1',
                  'ratio': 0,
                  'ratio_entries': {}},
                 {'parent_lbdn->base.id': 'lbdn2_ID_REPLACE',
                  'parent_lbdn->base.name': 'two.lbdn.com',
                  'pool->base.id': 'pool3_ID_REPLACE',
                  'pool->base.name': 'Pool3',
                  'ratio': 1,
                  'ratio_entries': {}}],
  'lbdns': [{'id': 'lbdn1_ID_REPLACE',
             'lb_method': 'TOPOLOGY',
             'lbdn_pools': [],
             'name': 'one.lbdn.com',
             'online_lbdn_pools': {},
             'online_lbdn_pools_ratio': {},
             'persistence': 0,
             'timestamp': 0,
             'topology': 'topo_lbdn_ID_REPLACE',
             'topology.lbdn_pools': [{'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                                      'parent_lbdn->base.name': 'one.lbdn.com',
                                      'pool->base.id': 'pool5_ID_REPLACE',
                                      'pool->base.name': 'Pool5',
                                      'ratio': 0,
                                      'ratio_entries': {}},
                                     {'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                                      'parent_lbdn->base.name': 'one.lbdn.com',
                                      'pool->base.id': 'pool2_ID_REPLACE',
                                      'pool->base.name': 'Pool2',
                                      'ratio': 0,
                                      'ratio_entries': {}},
                                     {'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                                      'parent_lbdn->base.name': 'one.lbdn.com',
                                      'pool->base.id': 'pool1_ID_REPLACE',
                                      'pool->base.name': 'Pool1',
                                      'ratio': 0,
                                      'ratio_entries': {}}],
             'topology.lbdn_rules': [{'dest_lbdn_pool': {'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                                                         'parent_lbdn->base.name': 'one.lbdn.com',
                                                         'pool->base.id': 'pool5_ID_REPLACE',
                                                         'pool->base.name': 'Pool5',
                                                         'ratio': 0,
                                                         'ratio_entries': {}},
                                      'topo_rule': {'dest_link': 'pool5_ID_REPLACE',
                                                    'dest_obj->base.id': 'pool5_ID_REPLACE',
                                                    'dest_obj->base.name': 'Pool5',
                                                    'dest_type': 'POOL',
                                                    'return_type': 'REGULAR',
                                                    'sources': []}},
                                     {'dest_lbdn_pool': {'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                                                         'parent_lbdn->base.name': 'one.lbdn.com',
                                                         'pool->base.id': 'pool2_ID_REPLACE',
                                                         'pool->base.name': 'Pool2',
                                                         'ratio': 0,
                                                         'ratio_entries': {}},
                                      'topo_rule': {'dest_link': 'pool2_ID_REPLACE',
                                                    'dest_obj->base.id': 'pool2_ID_REPLACE',
                                                    'dest_obj->base.name': 'Pool2',
                                                    'dest_type': 'POOL',
                                                    'return_type': 'REGULAR',
                                                    'sources': [{'source_op': 'IS',
                                                                 'source_type': 'SUBNET',
                                                                 'source_value': '110.0.0.0/8',
                                                                 'subnet_ip': '110.0.0.0',
                                                                 'subnet_mask': '*********'}]}},
                                     {'dest_lbdn_pool': {'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                                                         'parent_lbdn->base.name': 'one.lbdn.com',
                                                         'pool->base.id': 'pool1_ID_REPLACE',
                                                         'pool->base.name': 'Pool1',
                                                         'ratio': 0,
                                                         'ratio_entries': {}},
                                      'topo_rule': {'dest_link': 'pool1_ID_REPLACE',
                                                    'dest_obj->base.id': 'pool1_ID_REPLACE',
                                                    'dest_obj->base.name': 'Pool1',
                                                    'dest_type': 'POOL',
                                                    'return_type': 'REGULAR',
                                                    'sources': [{'source_op': 'IS_NOT',
                                                                 'source_type': 'SUBNET',
                                                                 'source_value': '110.0.0.0/8',
                                                                 'subnet_ip': '110.0.0.0',
                                                                 'subnet_mask': '*********'}]}}],
             'topology.online_lbdn_rules': {},
             'ttl': 0,
             'types': 'A AAAA',
             'use_ttl': False},
            {'id': 'lbdn2_ID_REPLACE',
             'lb_method': 'RATIO',
             'lbdn_pools': [{'parent_lbdn->base.id': 'lbdn2_ID_REPLACE',
                             'parent_lbdn->base.name': 'two.lbdn.com',
                             'pool->base.id': 'pool3_ID_REPLACE',
                             'pool->base.name': 'Pool3',
                             'ratio': 1,
                             'ratio_entries': {}}],
             'name': 'two.lbdn.com',
             'online_lbdn_pools': {},
             'online_lbdn_pools_ratio': {},
             'persistence': 0,
             'timestamp': 0,
             'topology': None,
             'topology.lbdn_pools': [],
             'topology.lbdn_rules': [],
             'topology.online_lbdn_rules': {},
             'ttl': 0,
             'types': 'A AAAA',
             'use_ttl': False}],
  'monitor': [{'id': 'MON_TCP_ID_REPLACE',
               'name': 'TCP monitor',
               'type': 'TCP'},
              {'id': 'MON_ICMP_ID_REPLACE',
               'name': 'ICMP monitor',
               'type': 'ICMP'},
              {'id': 'MON_HTTP_ID_REPLACE',
               'name': 'HTTP monitor',
               'type': 'HTTP'}],
  'pool_servers': [{'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_TCP_ID_REPLACE',
                                     'monitor_type': 'TCP',
                                     'server': 'serv6_ID_REPLACE'},
                                    {'health': 'UNKNOWN',
                                     'monitor': 'MON_ICMP_ID_REPLACE',
                                     'monitor_type': 'ICMP',
                                     'server': 'serv6_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool5_ID_REPLACE',
                    'parent_pool->base.name': 'Pool5',
                    'ratio': 2,
                    'ratio_entries': {},
                    'server->base.id': 'serv6_ID_REPLACE',
                    'server->base.name': 'Server6'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_TCP_ID_REPLACE',
                                     'monitor_type': 'TCP',
                                     'server': 'serv4_ID_REPLACE'},
                                    {'health': 'UNKNOWN',
                                     'monitor': 'MON_ICMP_ID_REPLACE',
                                     'monitor_type': 'ICMP',
                                     'server': 'serv4_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool5_ID_REPLACE',
                    'parent_pool->base.name': 'Pool5',
                    'ratio': 4,
                    'ratio_entries': {},
                    'server->base.id': 'serv4_ID_REPLACE',
                    'server->base.name': 'Server4'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_TCP_ID_REPLACE',
                                     'monitor_type': 'TCP',
                                     'server': 'serv4_ID_REPLACE'},
                                    {'health': 'UNKNOWN',
                                     'monitor': 'MON_HTTP_ID_REPLACE',
                                     'monitor_type': 'HTTP',
                                     'server': 'serv4_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool2_ID_REPLACE',
                    'parent_pool->base.name': 'Pool2',
                    'ratio': 3,
                    'ratio_entries': {},
                    'server->base.id': 'serv4_ID_REPLACE',
                    'server->base.name': 'Server4'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_TCP_ID_REPLACE',
                                     'monitor_type': 'TCP',
                                     'server': 'serv8_ID_REPLACE'},
                                    {'health': 'UNKNOWN',
                                     'monitor': 'MON_ICMP_ID_REPLACE',
                                     'monitor_type': 'ICMP',
                                     'server': 'serv8_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool5_ID_REPLACE',
                    'parent_pool->base.name': 'Pool5',
                    'ratio': 1,
                    'ratio_entries': {},
                    'server->base.id': 'serv8_ID_REPLACE',
                    'server->base.name': 'Server8'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_TCP_ID_REPLACE',
                                     'monitor_type': 'TCP',
                                     'server': 'serv3_ID_REPLACE'},
                                    {'health': 'UNKNOWN',
                                     'monitor': 'MON_ICMP_ID_REPLACE',
                                     'monitor_type': 'ICMP',
                                     'server': 'serv3_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool1_ID_REPLACE',
                    'parent_pool->base.name': 'Pool1',
                    'ratio': 0,
                    'ratio_entries': {},
                    'server->base.id': 'serv3_ID_REPLACE',
                    'server->base.name': 'Server3'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_TCP_ID_REPLACE',
                                     'monitor_type': 'TCP',
                                     'server': 'serv3_ID_REPLACE'},
                                    {'health': 'UNKNOWN',
                                     'monitor': 'MON_HTTP_ID_REPLACE',
                                     'monitor_type': 'HTTP',
                                     'server': 'serv3_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool2_ID_REPLACE',
                    'parent_pool->base.name': 'Pool2',
                    'ratio': 2,
                    'ratio_entries': {},
                    'server->base.id': 'serv3_ID_REPLACE',
                    'server->base.name': 'Server3'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_TCP_ID_REPLACE',
                                     'monitor_type': 'TCP',
                                     'server': 'serv1_ID_REPLACE'},
                                    {'health': 'UNKNOWN',
                                     'monitor': 'MON_ICMP_ID_REPLACE',
                                     'monitor_type': 'ICMP',
                                     'server': 'serv1_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool1_ID_REPLACE',
                    'parent_pool->base.name': 'Pool1',
                    'ratio': 0,
                    'ratio_entries': {},
                    'server->base.id': 'serv1_ID_REPLACE',
                    'server->base.name': 'Server1'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_TCP_ID_REPLACE',
                                     'monitor_type': 'TCP',
                                     'server': 'serv5_ID_REPLACE'},
                                    {'health': 'UNKNOWN',
                                     'monitor': 'MON_ICMP_ID_REPLACE',
                                     'monitor_type': 'ICMP',
                                     'server': 'serv5_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool5_ID_REPLACE',
                    'parent_pool->base.name': 'Pool5',
                    'ratio': 3,
                    'ratio_entries': {},
                    'server->base.id': 'serv5_ID_REPLACE',
                    'server->base.name': 'Server5'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_TCP_ID_REPLACE',
                                     'monitor_type': 'TCP',
                                     'server': 'serv5_ID_REPLACE'},
                                    {'health': 'UNKNOWN',
                                     'monitor': 'MON_ICMP_ID_REPLACE',
                                     'monitor_type': 'ICMP',
                                     'server': 'serv5_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool1_ID_REPLACE',
                    'parent_pool->base.name': 'Pool1',
                    'ratio': 0,
                    'ratio_entries': {},
                    'server->base.id': 'serv5_ID_REPLACE',
                    'server->base.name': 'Server5'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_ICMP_ID_REPLACE',
                                     'monitor_type': 'ICMP',
                                     'server': 'serv6_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool3_ID_REPLACE',
                    'parent_pool->base.name': 'Pool3',
                    'ratio': 3,
                    'ratio_entries': {},
                    'server->base.id': 'serv6_ID_REPLACE',
                    'server->base.name': 'Server6'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_ICMP_ID_REPLACE',
                                     'monitor_type': 'ICMP',
                                     'server': 'serv4_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool3_ID_REPLACE',
                    'parent_pool->base.name': 'Pool3',
                    'ratio': 2,
                    'ratio_entries': {},
                    'server->base.id': 'serv4_ID_REPLACE',
                    'server->base.name': 'Server4'},
                   {'health': 'UNKNOWN',
                    'health_data': [{'health': 'UNKNOWN',
                                     'monitor': 'MON_ICMP_ID_REPLACE',
                                     'monitor_type': 'ICMP',
                                     'server': 'serv2_ID_REPLACE'}],
                    'parent_pool->base.id': 'pool3_ID_REPLACE',
                    'parent_pool->base.name': 'Pool3',
                    'ratio': 1,
                    'ratio_entries': {},
                    'server->base.id': 'serv2_ID_REPLACE',
                    'server->base.name': 'Server2'}],
  'pools': [{'alternate_method': 'NONE',
             'alternate_topology': None,
             'alternate_topology.online_pool_rules': {},
             'alternate_topology.pool_rules': {},
             'availability': 'ANY',
             'consolidated_monitor_health': [{'availability': 'ALL',
                                              'full_health_communication': False,
                                              'members': [{'oid': 'MEMBER_OID_REPLACE'}],
                                              'monitor_id': 'MON_ICMP_ID_REPLACE'}],
             'health': 'OFFLINE',
             'id': 'pool1_ID_REPLACE',
             'name': 'Pool1',
             'online_pool_servers': {},
             'online_pool_servers_ratio': {},
             'parent_lbdn_pools': [{'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                                    'parent_lbdn->base.name': 'one.lbdn.com',
                                    'pool->base.id': 'pool1_ID_REPLACE',
                                    'pool->base.name': 'Pool1',
                                    'ratio': 0,
                                    'ratio_entries': {}}],
             'pool_monitors': [{'id': 'MON_TCP_ID_REPLACE',
                                'name': 'TCP monitor',
                                'type': 'TCP'},
                               {'id': 'MON_ICMP_ID_REPLACE',
                                'name': 'ICMP monitor',
                                'type': 'ICMP'}],
             'pool_server': [],
             'pool_servers': {},
             'preferred_method': 'TOPOLOGY',
             'preferred_topology': 'topo_pool_ID_REPLACE',
             'preferred_topology.online_pool_rules': {},
             'preferred_topology.pool_rules': {},
             'quorum': 0,
             'ttl': 0,
             'use_ttl': False},
            {'alternate_method': 'RATIO',
             'alternate_topology': None,
             'alternate_topology.online_pool_rules': {},
             'alternate_topology.pool_rules': {},
             'availability': 'ANY',
             'consolidated_monitor_health': [],
             'health': 'OFFLINE',
             'id': 'pool3_ID_REPLACE',
             'name': 'Pool3',
             'online_pool_servers': {},
             'online_pool_servers_ratio': {},
             'parent_lbdn_pools': [{'parent_lbdn->base.id': 'lbdn2_ID_REPLACE',
                                    'parent_lbdn->base.name': 'two.lbdn.com',
                                    'pool->base.id': 'pool3_ID_REPLACE',
                                    'pool->base.name': 'Pool3',
                                    'ratio': 1,
                                    'ratio_entries': {}}],
             'pool_monitors': [{'id': 'MON_ICMP_ID_REPLACE',
                                'name': 'ICMP monitor',
                                'type': 'ICMP'}],
             'pool_server': [{'health': 'UNKNOWN',
                              'health_data': [{'health': 'UNKNOWN',
                                               'monitor': 'MON_ICMP_ID_REPLACE',
                                               'monitor_type': 'ICMP',
                                               'server': 'serv6_ID_REPLACE'}],
                              'parent_pool->base.id': 'pool3_ID_REPLACE',
                              'parent_pool->base.name': 'Pool3',
                              'ratio': 3,
                              'ratio_entries': {},
                              'server->base.id': 'serv6_ID_REPLACE',
                              'server->base.name': 'Server6'},
                             {'health': 'UNKNOWN',
                              'health_data': [{'health': 'UNKNOWN',
                                               'monitor': 'MON_ICMP_ID_REPLACE',
                                               'monitor_type': 'ICMP',
                                               'server': 'serv4_ID_REPLACE'}],
                              'parent_pool->base.id': 'pool3_ID_REPLACE',
                              'parent_pool->base.name': 'Pool3',
                              'ratio': 2,
                              'ratio_entries': {},
                              'server->base.id': 'serv4_ID_REPLACE',
                              'server->base.name': 'Server4'},
                             {'health': 'UNKNOWN',
                              'health_data': [{'health': 'UNKNOWN',
                                               'monitor': 'MON_ICMP_ID_REPLACE',
                                               'monitor_type': 'ICMP',
                                               'server': 'serv2_ID_REPLACE'}],
                              'parent_pool->base.id': 'pool3_ID_REPLACE',
                              'parent_pool->base.name': 'Pool3',
                              'ratio': 1,
                              'ratio_entries': {},
                              'server->base.id': 'serv2_ID_REPLACE',
                              'server->base.name': 'Server2'}],
             'pool_servers': {},
             'preferred_method': 'GLOBAL_AVAILABILITY',
             'preferred_topology': None,
             'preferred_topology.online_pool_rules': {},
             'preferred_topology.pool_rules': {},
             'quorum': 0,
             'ttl': 0,
             'use_ttl': False},
            {'alternate_method': 'RATIO',
             'alternate_topology': None,
             'alternate_topology.online_pool_rules': {},
             'alternate_topology.pool_rules': {},
             'availability': 'ANY',
             'consolidated_monitor_health': [],
             'health': 'OFFLINE',
             'id': 'pool2_ID_REPLACE',
             'name': 'Pool2',
             'online_pool_servers': {},
             'online_pool_servers_ratio': {},
             'parent_lbdn_pools': [{'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                                    'parent_lbdn->base.name': 'one.lbdn.com',
                                    'pool->base.id': 'pool2_ID_REPLACE',
                                    'pool->base.name': 'Pool2',
                                    'ratio': 0,
                                    'ratio_entries': {}}],
             'pool_monitors': [{'id': 'MON_TCP_ID_REPLACE',
                                'name': 'TCP monitor',
                                'type': 'TCP'},
                               {'id': 'MON_HTTP_ID_REPLACE',
                                'name': 'HTTP monitor',
                                'type': 'HTTP'}],
             'pool_server': [{'health': 'UNKNOWN',
                              'health_data': [{'health': 'UNKNOWN',
                                               'monitor': 'MON_TCP_ID_REPLACE',
                                               'monitor_type': 'TCP',
                                               'server': 'serv4_ID_REPLACE'},
                                              {'health': 'UNKNOWN',
                                               'monitor': 'MON_HTTP_ID_REPLACE',
                                               'monitor_type': 'HTTP',
                                               'server': 'serv4_ID_REPLACE'}],
                              'parent_pool->base.id': 'pool2_ID_REPLACE',
                              'parent_pool->base.name': 'Pool2',
                              'ratio': 3,
                              'ratio_entries': {},
                              'server->base.id': 'serv4_ID_REPLACE',
                              'server->base.name': 'Server4'},
                             {'health': 'UNKNOWN',
                              'health_data': [{'health': 'UNKNOWN',
                                               'monitor': 'MON_TCP_ID_REPLACE',
                                               'monitor_type': 'TCP',
                                               'server': 'serv3_ID_REPLACE'},
                                              {'health': 'UNKNOWN',
                                               'monitor': 'MON_HTTP_ID_REPLACE',
                                               'monitor_type': 'HTTP',
                                               'server': 'serv3_ID_REPLACE'}],
                              'parent_pool->base.id': 'pool2_ID_REPLACE',
                              'parent_pool->base.name': 'Pool2',
                              'ratio': 2,
                              'ratio_entries': {},
                              'server->base.id': 'serv3_ID_REPLACE',
                              'server->base.name': 'Server3'}],
             'pool_servers': {},
             'preferred_method': 'GLOBAL_AVAILABILITY',
             'preferred_topology': None,
             'preferred_topology.online_pool_rules': {},
             'preferred_topology.pool_rules': {},
             'quorum': 0,
             'ttl': 0,
             'use_ttl': False},
            {'alternate_method': 'RATIO',
             'alternate_topology': None,
             'alternate_topology.online_pool_rules': {},
             'alternate_topology.pool_rules': {},
             'availability': 'ANY',
             'consolidated_monitor_health': [{'availability': 'ALL',
                                              'full_health_communication': True,
                                              'members': [{'oid': 'MEMBER_OID_REPLACE'}],
                                              'monitor_id': 'MON_TCP_ID_REPLACE'},
                                             {'availability': 'ANY',
                                              'full_health_communication': False,
                                              'members': [{'oid': 'MEMBER_OID_REPLACE'}],
                                              'monitor_id': 'MON_ICMP_ID_REPLACE'}],
             'health': 'OFFLINE',
             'id': 'pool5_ID_REPLACE',
             'name': 'Pool5',
             'online_pool_servers': {},
             'online_pool_servers_ratio': {},
             'parent_lbdn_pools': [{'parent_lbdn->base.id': 'lbdn1_ID_REPLACE',
                                    'parent_lbdn->base.name': 'one.lbdn.com',
                                    'pool->base.id': 'pool5_ID_REPLACE',
                                    'pool->base.name': 'Pool5',
                                    'ratio': 0,
                                    'ratio_entries': {}}],
             'pool_monitors': [{'id': 'MON_TCP_ID_REPLACE',
                                'name': 'TCP monitor',
                                'type': 'TCP'},
                               {'id': 'MON_ICMP_ID_REPLACE',
                                'name': 'ICMP monitor',
                                'type': 'ICMP'}],
             'pool_server': [{'health': 'UNKNOWN',
                              'health_data': [{'health': 'UNKNOWN',
                                               'monitor': 'MON_TCP_ID_REPLACE',
                                               'monitor_type': 'TCP',
                                               'server': 'serv6_ID_REPLACE'},
                                              {'health': 'UNKNOWN',
                                               'monitor': 'MON_ICMP_ID_REPLACE',
                                               'monitor_type': 'ICMP',
                                               'server': 'serv6_ID_REPLACE'}],
                              'parent_pool->base.id': 'pool5_ID_REPLACE',
                              'parent_pool->base.name': 'Pool5',
                              'ratio': 2,
                              'ratio_entries': {},
                              'server->base.id': 'serv6_ID_REPLACE',
                              'server->base.name': 'Server6'},
                             {'health': 'UNKNOWN',
                              'health_data': [{'health': 'UNKNOWN',
                                               'monitor': 'MON_TCP_ID_REPLACE',
                                               'monitor_type': 'TCP',
                                               'server': 'serv4_ID_REPLACE'},
                                              {'health': 'UNKNOWN',
                                               'monitor': 'MON_ICMP_ID_REPLACE',
                                               'monitor_type': 'ICMP',
                                               'server': 'serv4_ID_REPLACE'}],
                              'parent_pool->base.id': 'pool5_ID_REPLACE',
                              'parent_pool->base.name': 'Pool5',
                              'ratio': 4,
                              'ratio_entries': {},
                              'server->base.id': 'serv4_ID_REPLACE',
                              'server->base.name': 'Server4'},
                             {'health': 'UNKNOWN',
                              'health_data': [{'health': 'UNKNOWN',
                                               'monitor': 'MON_TCP_ID_REPLACE',
                                               'monitor_type': 'TCP',
                                               'server': 'serv8_ID_REPLACE'},
                                              {'health': 'UNKNOWN',
                                               'monitor': 'MON_ICMP_ID_REPLACE',
                                               'monitor_type': 'ICMP',
                                               'server': 'serv8_ID_REPLACE'}],
                              'parent_pool->base.id': 'pool5_ID_REPLACE',
                              'parent_pool->base.name': 'Pool5',
                              'ratio': 1,
                              'ratio_entries': {},
                              'server->base.id': 'serv8_ID_REPLACE',
                              'server->base.name': 'Server8'},
                             {'health': 'UNKNOWN',
                              'health_data': [{'health': 'UNKNOWN',
                                               'monitor': 'MON_TCP_ID_REPLACE',
                                               'monitor_type': 'TCP',
                                               'server': 'serv5_ID_REPLACE'},
                                              {'health': 'UNKNOWN',
                                               'monitor': 'MON_ICMP_ID_REPLACE',
                                               'monitor_type': 'ICMP',
                                               'server': 'serv5_ID_REPLACE'}],
                              'parent_pool->base.id': 'pool5_ID_REPLACE',
                              'parent_pool->base.name': 'Pool5',
                              'ratio': 3,
                              'ratio_entries': {},
                              'server->base.id': 'serv5_ID_REPLACE',
                              'server->base.name': 'Server5'}],
             'pool_servers': {},
             'preferred_method': 'GLOBAL_AVAILABILITY',
             'preferred_topology': None,
             'preferred_topology.online_pool_rules': {},
             'preferred_topology.pool_rules': {},
             'quorum': 0,
             'ttl': 0,
             'use_ttl': False}],
  'servers': [{'id': 'serv6_ID_REPLACE',
               'name': 'Server6',
               'parent_pool_servers': [{'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_TCP_ID_REPLACE',
                                                         'monitor_type': 'TCP',
                                                         'server': 'serv6_ID_REPLACE'},
                                                        {'health': 'UNKNOWN',
                                                         'monitor': 'MON_ICMP_ID_REPLACE',
                                                         'monitor_type': 'ICMP',
                                                         'server': 'serv6_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool5_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool5',
                                        'ratio': 2,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv6_ID_REPLACE',
                                        'server->base.name': 'Server6'},
                                       {'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_ICMP_ID_REPLACE',
                                                         'monitor_type': 'ICMP',
                                                         'server': 'serv6_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool3_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool3',
                                        'ratio': 3,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv6_ID_REPLACE',
                                        'server->base.name': 'Server6'}],
               'records': {}},
              {'id': 'serv4_ID_REPLACE',
               'name': 'Server4',
               'parent_pool_servers': [{'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_TCP_ID_REPLACE',
                                                         'monitor_type': 'TCP',
                                                         'server': 'serv4_ID_REPLACE'},
                                                        {'health': 'UNKNOWN',
                                                         'monitor': 'MON_ICMP_ID_REPLACE',
                                                         'monitor_type': 'ICMP',
                                                         'server': 'serv4_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool5_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool5',
                                        'ratio': 4,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv4_ID_REPLACE',
                                        'server->base.name': 'Server4'},
                                       {'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_TCP_ID_REPLACE',
                                                         'monitor_type': 'TCP',
                                                         'server': 'serv4_ID_REPLACE'},
                                                        {'health': 'UNKNOWN',
                                                         'monitor': 'MON_HTTP_ID_REPLACE',
                                                         'monitor_type': 'HTTP',
                                                         'server': 'serv4_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool2_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool2',
                                        'ratio': 3,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv4_ID_REPLACE',
                                        'server->base.name': 'Server4'},
                                       {'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_ICMP_ID_REPLACE',
                                                         'monitor_type': 'ICMP',
                                                         'server': 'serv4_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool3_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool3',
                                        'ratio': 2,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv4_ID_REPLACE',
                                        'server->base.name': 'Server4'}],
               'records': {}},
              {'id': 'serv2_ID_REPLACE',
               'name': 'Server2',
               'parent_pool_servers': [{'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_ICMP_ID_REPLACE',
                                                         'monitor_type': 'ICMP',
                                                         'server': 'serv2_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool3_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool3',
                                        'ratio': 1,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv2_ID_REPLACE',
                                        'server->base.name': 'Server2'}],
               'records': {}},
              {'id': 'serv8_ID_REPLACE',
               'name': 'Server8',
               'parent_pool_servers': [{'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_TCP_ID_REPLACE',
                                                         'monitor_type': 'TCP',
                                                         'server': 'serv8_ID_REPLACE'},
                                                        {'health': 'UNKNOWN',
                                                         'monitor': 'MON_ICMP_ID_REPLACE',
                                                         'monitor_type': 'ICMP',
                                                         'server': 'serv8_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool5_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool5',
                                        'ratio': 1,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv8_ID_REPLACE',
                                        'server->base.name': 'Server8'}],
               'records': {}},
              {'id': 'serv3_ID_REPLACE',
               'name': 'Server3',
               'parent_pool_servers': [{'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_TCP_ID_REPLACE',
                                                         'monitor_type': 'TCP',
                                                         'server': 'serv3_ID_REPLACE'},
                                                        {'health': 'UNKNOWN',
                                                         'monitor': 'MON_ICMP_ID_REPLACE',
                                                         'monitor_type': 'ICMP',
                                                         'server': 'serv3_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool1_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool1',
                                        'ratio': 0,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv3_ID_REPLACE',
                                        'server->base.name': 'Server3'},
                                       {'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_TCP_ID_REPLACE',
                                                         'monitor_type': 'TCP',
                                                         'server': 'serv3_ID_REPLACE'},
                                                        {'health': 'UNKNOWN',
                                                         'monitor': 'MON_HTTP_ID_REPLACE',
                                                         'monitor_type': 'HTTP',
                                                         'server': 'serv3_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool2_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool2',
                                        'ratio': 2,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv3_ID_REPLACE',
                                        'server->base.name': 'Server3'}],
               'records': {}},
              {'id': 'serv1_ID_REPLACE',
               'name': 'Server1',
               'parent_pool_servers': [{'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_TCP_ID_REPLACE',
                                                         'monitor_type': 'TCP',
                                                         'server': 'serv1_ID_REPLACE'},
                                                        {'health': 'UNKNOWN',
                                                         'monitor': 'MON_ICMP_ID_REPLACE',
                                                         'monitor_type': 'ICMP',
                                                         'server': 'serv1_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool1_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool1',
                                        'ratio': 0,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv1_ID_REPLACE',
                                        'server->base.name': 'Server1'}],
               'records': {}},
              {'id': 'serv5_ID_REPLACE',
               'name': 'Server5',
               'parent_pool_servers': [{'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_TCP_ID_REPLACE',
                                                         'monitor_type': 'TCP',
                                                         'server': 'serv5_ID_REPLACE'},
                                                        {'health': 'UNKNOWN',
                                                         'monitor': 'MON_ICMP_ID_REPLACE',
                                                         'monitor_type': 'ICMP',
                                                         'server': 'serv5_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool5_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool5',
                                        'ratio': 3,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv5_ID_REPLACE',
                                        'server->base.name': 'Server5'},
                                       {'health': 'UNKNOWN',
                                        'health_data': [{'health': 'UNKNOWN',
                                                         'monitor': 'MON_TCP_ID_REPLACE',
                                                         'monitor_type': 'TCP',
                                                         'server': 'serv5_ID_REPLACE'},
                                                        {'health': 'UNKNOWN',
                                                         'monitor': 'MON_ICMP_ID_REPLACE',
                                                         'monitor_type': 'ICMP',
                                                         'server': 'serv5_ID_REPLACE'}],
                                        'parent_pool->base.id': 'pool1_ID_REPLACE',
                                        'parent_pool->base.name': 'Pool1',
                                        'ratio': 0,
                                        'ratio_entries': {},
                                        'server->base.id': 'serv5_ID_REPLACE',
                                        'server->base.name': 'Server5'}],
               'records': {}}],
  'topologies': [{'id': 'topo_pool_ID_REPLACE',
                  'name': 'topo_pool',
                  'rules': [{'dest_link': 'serv3_ID_REPLACE',
                             'dest_obj->base.id': 'serv3_ID_REPLACE',
                             'dest_obj->base.name': 'Server3',
                             'dest_type': 'SERVER',
                             'return_type': 'REGULAR',
                             'sources': [{'source_op': 'IS',
                                          'source_type': 'SUBNET',
                                          'source_value': '210.0.0.0/8',
                                          'subnet_ip': '210.0.0.0',
                                          'subnet_mask': '*********'}]},
                            {'dest_link': 'serv1_ID_REPLACE',
                             'dest_obj->base.id': 'serv1_ID_REPLACE',
                             'dest_obj->base.name': 'Server1',
                             'dest_type': 'SERVER',
                             'return_type': 'REGULAR',
                             'sources': [{'source_op': 'IS',
                                          'source_type': 'SUBNET',
                                          'source_value': '110.0.0.0/8',
                                          'subnet_ip': '110.0.0.0',
                                          'subnet_mask': '*********'}]},
                            {'dest_link': 'serv5_ID_REPLACE',
                             'dest_obj->base.id': 'serv5_ID_REPLACE',
                             'dest_obj->base.name': 'Server5',
                             'dest_type': 'SERVER',
                             'return_type': 'REGULAR',
                             'sources': []}]},
                 {'id': 'topo_lbdn_ID_REPLACE',
                  'name': 'topo_lbdn',
                  'rules': [{'dest_link': 'pool5_ID_REPLACE',
                             'dest_obj->base.id': 'pool5_ID_REPLACE',
                             'dest_obj->base.name': 'Pool5',
                             'dest_type': 'POOL',
                             'return_type': 'REGULAR',
                             'sources': []},
                            {'dest_link': 'pool2_ID_REPLACE',
                             'dest_obj->base.id': 'pool2_ID_REPLACE',
                             'dest_obj->base.name': 'Pool2',
                             'dest_type': 'POOL',
                             'return_type': 'REGULAR',
                             'sources': [{'source_op': 'IS',
                                          'source_type': 'SUBNET',
                                          'source_value': '110.0.0.0/8',
                                          'subnet_ip': '110.0.0.0',
                                          'subnet_mask': '*********'}]},
                            {'dest_link': 'pool1_ID_REPLACE',
                             'dest_obj->base.id': 'pool1_ID_REPLACE',
                             'dest_obj->base.name': 'Pool1',
                             'dest_type': 'POOL',
                             'return_type': 'REGULAR',
                             'sources': [{'source_op': 'IS_NOT',
                                          'source_type': 'SUBNET',
                                          'source_value': '110.0.0.0/8',
                                          'subnet_ip': '110.0.0.0',
                                          'subnet_mask': '*********'}]}]}]}