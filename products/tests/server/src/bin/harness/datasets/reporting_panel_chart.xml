<dashboard stylesheet="custom.css">
  <label/>
  <row>
    <chart>
      <option name="charting.chart">bar</option>
      <option name="charting.legend.placement">none</option>
      <option name="charting.drilldown">none</option>
      <option name="charting.data.preview">true</option>
      <option name="charting.legend.labelStyle.overflowMode">ellipsisNone</option>
      <option name="charting.axisTitleX.text">Domain Name</option>
      <option name="charting.axisTitleY.text">Count</option>
      <option name="link.visible">false</option>
      <searchName>search-A-.-test_search_string</searchName>
    </chart>
  </row>
</dashboard>
