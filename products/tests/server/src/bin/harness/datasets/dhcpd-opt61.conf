#ignore unknown-clients;
#omapi-port 7911;
ignore bootp;
ddns-updates DDNS-UPDATES;
ddns-update-style interim;
update-static-leases true;
option domain-name "test.infoblox.com";
option domain-name-servers 127.0.0.1;
#always-broadcast true;
local-address 127.0.0.1;
server-identifier 127.0.0.1;
authoritative;
max-lease-time LEASE-TIME;
default-lease-time LEASE-TIME;
ping-check false;

# This key has the same name as the one used by the real dhcpd
# in a production environment, but not the same content.
key DHCP_UPDATER {
  algorithm hmac-md5;
  secret "pt4XkVpUFiQpGsgRBhh55w==";
};

# Allocate leases from the loopback network
subnet ********* netmask ********* {
	pool {
		infoblox-range ********* *************;
	        range ********* *************;
	}
        host fixed-host-1 {
		dynamic;
		fixed-address *********;
                hardware ethernet 00:00:00:00:00:02;
	}
        host fixed-host-1 {
		dynamic;
                hardware ethernet 00:00:00:00:00:03;
	}
        host fixed-host-2 {
		dynamic;
		fixed-address *********;
                option dhcp-client-identifier "foo";
	}
        host fixed-host-3 {
		dynamic;
		fixed-address *********;
                hardware ethernet 00:00:00:00:00:00;
	}
}

# Where to send the DNS updates
zone "test.infoblox.com." {
	primary 127.0.0.1;
	key DHCP_UPDATER;
}

zone "127.in-addr.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER;
}
