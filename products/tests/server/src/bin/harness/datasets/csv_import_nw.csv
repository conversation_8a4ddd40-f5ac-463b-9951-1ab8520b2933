"version 1.0",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
"Header-Network","address*","netmask*","network_view","comment","auto_create_reversezone","is_authoritative","boot_file","boot_server","ddns_domainname","generate_hostname","always_update_dns","update_static_leases","ddns_ttl","enable_option81","deny_bootp","disabled","enable_ddns","enable_thresholds","enable_threshold_email_warnings","enable_threshold_snmp_warnings","range_high_water_mark","range_high_water_mark_reset","ignore_client_requested_options","range_low_water_mark","range_low_water_mark_reset","next_server","pxe_lease_time","recycle_leases","threshold_email_addresses","dhcp_members","routers","domain_name_servers"
"Network","10.0.0.0","***********","default"," IPv4 network",FALSE,TRUE,"bootfile1","abc.domain.com","test_domain.com",TRUE,FALSE,TRUE,1200,TRUE,FALSE,FALSE,FALSE,TRUE,TRUE,FALSE,80,75,TRUE,10,20,"blue.domain.com",1100,FALSE,"<EMAIL>,<EMAIL>","infoblox.localdomain","********,********00","********,********"
"Network","20.0.0.0","*********",,"Container",,,,,,,,,,,,,,,,,,,,,,,,,,,,
"Network","20.0.0.0","***********","default",,FALSE,FALSE,,,,FALSE,,FALSE,,FALSE,,,,FALSE,FALSE,,,,FALSE,,,,,,,,,
"Header-IPv6Network","address*","cidr*","network_view","comment","auto_create_reversezone",,,,,,,,,,,,,,,,,,,,,,,,,
"IPv6Network","2001::",48,"default","this is an IPv6 network under IPAM",FALSE,,,,,,,,,,,,,,,,,,,,,,,,,
"IPv6Network","2001::",64,"default","this is an IPv6 network under IPAM",,,,,,,,,,,,,,,,,,,,,,,,,,
"Header-SharedNetwork","name*","networks","network_view","is_authoritative","boot_file","boot_server","comment","generate_hostname","always_update_dns","update_static_leases","ddns_ttl","enable_option81","deny_bootp","disabled","enable_ddns","ignore_client_requested_options","next_server","pxe_lease_time",,,,,,,,,,,,
"SharedNetwork","Site Network","10.0.0.0/***********,20.0.0.0/***********",,FALSE,"bootfile1","abc.domain.com","Shared Network for building 1",TRUE,FALSE,FALSE,1200,FALSE,FALSE,FALSE,FALSE,FALSE,"blue.domain.com",1100,,,,,,,,,,,,
"Header-DhcpRange","start_address*","end_address*","network_view","boot_file","boot_server","ddns_domainname","generate_hostname","deny_all_clients","deny_bootp","disabled","enable_ddns","enable_thresholds","enable_threshold_email_warnings","enable_threshold_snmp_warnings","range_high_water_mark","range_high_water_mark_reset","ignore_client_requested_options","range_low_water_mark","range_low_water_mark_reset","next_server","pxe_lease_time",,"unknown_clients_option",,"known_clients_option","recycle_leases","always_update_dns","exclusion_ranges","member","broadcast_address",
"DhcpRange","********","********00",,"bootfile1","abc.domain.com","test_domain.com",TRUE,TRUE,FALSE,FALSE,FALSE,TRUE,TRUE,FALSE,80,70,TRUE,10,20,"blue.domain.com",1100,TRUE,"Allow",TRUE,"Deny",FALSE,TRUE,"********0-********5/Exclusion Range","infoblox.localdomain","********",
"DhcpRange","********","********0",,,,,,,,,,,,,,,,,,,,,,,,,,,,,
"DhcpRange","*********","*********",,,,,,,,,,,,,,,,,,,,,,,,,,,,,
"Header-FixedAddress","ip_address*","network_view","always_update_dns","boot_file","boot_server","prepend_zero","comment","ddns_domainname","","deny_bootp","dhcp_client_identifier","disabled","enable_ddns","ignore_client_requested_options","mac_address","match_option",,"next_server","pxe_lease_time",,,,,,,,,,,
"FixedAddress","********",,TRUE,"bootfile1","abc.domain.com",FALSE,"Printer 1","test_domain.com",TRUE,FALSE,,FALSE,TRUE,FALSE,"11:22:33:44:55:66","MAC_ADDRESS",,"blue.domain.com",1100,,,,,,,,,,,
