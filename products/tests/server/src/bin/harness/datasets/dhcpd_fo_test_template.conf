#ignore unknown-clients;
omapi-port OMAPI_PORT;
ignore bootp;
ddns-updates DDNS-UPDATES;
ddns-update-style interim;
DDNS-CONFIG
update-static-leases true;
option domain-name "ZONE-NAME";
option domain-name-servers DOMAIN-NS;
#always-broadcast true;
local-address MY-ADDRESS;
server-identifier MY-ADDRESS;
authoritative;
max-lease-time LEASE-TIME;
default-lease-time LEASE-TIME;
min-lease-time LEASE-TIME;
ping-check false;
infoblox-ignore-uid IGNORE-UID;
infoblox-ignore-macaddr IGNORE-MACADDR;

# Include a generated key-file (slightly closer to the "real thing")
include "WORKING-DIR/dhcp_updater.key";

failover peer "FO-ASSOC-NAME"
{
  MY-ROLE;
  address MY-ADDRESS;
  port 519;
  peer address PEER-ADDRESS;
  peer port 519;
  max-response-delay 10;
  max-unacked-updates 10;
  auto-partner-down 10;
  MCLT
  SPLIT
  infoblox-in-grid;
}

MY-NETWORKS

# Where to send the DNS updates
zone "ZONE-NAME." {
    primary DOMAIN-NS;
    key DHCP_UPDATER;
}

zone "127.in-addr.arpa." {
    primary DOMAIN-NS;
    key DHCP_UPDATER;
}
