{
'options': {
    'source':'ANY',
    'source_has_ipv4': True,
    'source_has_ipv6': False,
    'socket_limit': 30000,
},
'checks': [
    {
    'id': '101456789abcdef0**********abcdef',
    'inuse': True,
    'monitor_id': '101456789abcdef0**********abcdef',
    'server_id': '31d6e5e640e84174bd92ee95f6aaa0e2',
    'host': '2:2:2::2',
    'host_format': 'IPV6'
    }, {
    'id': '103456789abcdef0**********abcdef',
    'inuse': True,
    'monitor_id': '103456789abcdef0**********abcdef',
    'server_id': 'f6c9fed96c7d49fba7e164fbfa71ec03',
    'host': '3:3:3::3',
    'host_format': 'IPV6'
    }, {
    'id': '102456789abcdef0**********abcdef',
    'inuse': True,
    'monitor_id': '102456789abcdef0**********abcdef',
    'server_id': 'dd7f08b689394e64835b6c14df3a6250',
    'host': '*******',
    'host_format': 'IPV4'
    }, {
    'id': '103456789abcdef0**********abcdef',
    'inuse': True,
    'monitor_id': '103456789abcdef0**********abcdef',
    'server_id': '31d6e5e640e84174bd92ee95f6aaa0e2',
    'host': '2:2:2::2',
    'host_format': 'IPV6'
    }, {
    'id': '102456789abcdef0**********abcdef',
    'inuse': True,
    'monitor_id': '102456789abcdef0**********abcdef',
    'server_id': '31d6e5e640e84174bd92ee95f6aaa0e2',
    'host': '2:2:2::2',
    'host_format': 'IPV6'
    }, {
    'id': '102456789abcdef0**********abcdef',
    'inuse': True,
    'monitor_id': '102456789abcdef0**********abcdef',
    'server_id': 'f6c9fed96c7d49fba7e164fbfa71ec03',
    'host': '3:3:3::3',
    'host_format': 'IPV6'
    }, {
    'id': '101456789abcdef0**********abcdef',
    'inuse': True,
    'monitor_id': '101456789abcdef0**********abcdef',
    'server_id': '74086b91b3ab49e988e9578dc1f47727',
    'host': '*******',
    'host_format': 'IPV4'
    }, {
    'id': '101456789abcdef0**********abcdef',
    'inuse': True,
    'monitor_id': '101456789abcdef0**********abcdef',
    'server_id': 'dd7f08b689394e64835b6c14df3a6250',
    'host': '*******',
    'host_format': 'IPV4'
    }, {
    'id': '102456789abcdef0**********abcdef',
    'inuse': True,
    'monitor_id': '102456789abcdef0**********abcdef',
    'server_id': '74086b91b3ab49e988e9578dc1f47727',
    'host': '*******',
    'host_format': 'IPV4'
    }, {
    'id': 'deadbeefdeadbeef1111111111111111',
    'inuse': True,
    'monitor_id': 'deadbeefdeadbeef1111111111111111',
    'server_id': '1111111111111111deadbeefdeadbeef',
    'host': 'snmp.by',
    'host_format': 'FQDN'
    }, {
    'id': 'deadbeefdeadbeef2222222222222222',
    'inuse': True,
    'monitor_id': 'deadbeefdeadbeef2222222222222222',
    'server_id': '2222222222222222deadbeefdeadbeef',
    'host': 'snmpv3.by',
    'host_format': 'FQDN'
    }, {
    'id': 'dead222222222222222222222222beef',
    'inuse': True,
    'monitor_id': 'dead222222222222222222222222beef',
    'server_id': '2222222222222222deadbeefdeadbeef',
    'host': 'snmpv3.by',
    'host_format': 'FQDN'
    }, {
    'id': 'abba**********abba**********abba',
    'inuse': True,
    'monitor_id': 'dead222222222222222222222222beef',
    'server_id': '2222222222222222deadbeefdeadbeef',
    'host': '*******',
    'host_format': 'IPV4'
    }, {
    'id': 'acdc**********acdc**********acdc',
    'inuse': True,
    'monitor_id': '103456789abcdef0**********abcdef',
    'server_id': 'dd7f08b689394e64835b6c14df3a6250',
    'host': 'sugar.meat',
    'host_format': 'FQDN'
    }, {
    'id': 'bbdd**********bbdd**********bbdd',
    'inuse': True,
    'monitor_id': '101456789abcdef0**********abcdef',
    'server_id': 'f6c9fed96c7d49fba7e164fbfa71ec03',
    'host': '15:16:23::42',
    'host_format': 'IPV6'
    }, {
    'id': 'bbddaa34567890bbdd**********bbdd',
    'inuse': True,
    'monitor_id': '101456789abcdef0**********abcdef',
    'server_id': 'f6c9fed96c7d49fba7e164fbfa71ec03',
    'host': '***********',
    'host_format': 'IPV4',
    'dynamic_ratio_settings': {
      'dynamic_ratio_method': 'ROUND_TRIP_DELAY'},
    }, {
    'id': 'bbddbb34567890bbdd**********bbdd',
    'inuse': True,
    'monitor_id': 'dead333333333333333333333333beef',
    'server_id': 'f6c9fed96c7d49fba7e164fbfa71ec03',
    'host': '11:11:11::11',
    'host_format': 'IPV6',
    'dynamic_ratio_settings': {
      'dynamic_ratio_method': 'MONITOR',
      'dynamic_ratio_metric': '.*******.1'},
    }, {
    'id': '102456789abcdef0**********abcdef',
    'inuse': False,
    'monitor_id': '102456789abcdef0**********abcdef',
    'server_id': 'dd7f08b689394e64835b6c14df3a6250',
    'host': '*******',
    'host_format': 'IPV4'
    },
    ],
    'monitors': [
        {
        'interval': 20,
        'name': 'tcp_monitor2',
        'retry_down': 1,
        'retry_up': 1,
        'timeout': 10,
        'type': 'TCP',
        'id': '102456789abcdef0**********abcdef',
        }, {
        'interval': 30,
        'name': 'icmp_monitor3',
        'retry_down': 1,
        'retry_up': 1,
        'timeout': 5,
        'type': 'ICMP',
        'id': '103456789abcdef0**********abcdef',
        },{
        'interval': 20,
        'name': 'snmp_monitor',
        'retry_down': 40,
        'retry_up': 50,
        'timeout': 30,
        'port': 60,
        'type': 'SNMP',
        'community': 'bublic',
        'version': 'V1',
        'oids': [
            {
              'snmp_oid': '.*******.1',
              'type': 'INTEGER',
              'condition': 'ANY',
            },
            {
              'snmp_oid': '.*******.2',
              'type': 'INTEGER',
              'condition': 'EXACT',
              'first': '12342',
            },
            {
              'snmp_oid': '.*******.3',
              'type': 'INTEGER',
              'condition': 'LEQ',
              'first': '12343',
            },
            {
              'snmp_oid': '.*******.4',
              'type': 'INTEGER',
              'condition': 'GEQ',
              'first': '12344',
            },
            {
              'snmp_oid': '.*******.5',
              'type': 'INTEGER',
              'condition': 'RANGE',
              'first': '12345',
              'last': '54321',
            },
            {
              'snmp_oid': '.*******.6',
              'type': 'STRING',
              'condition': 'RANGE',
              'first': "aaaa",
              'last': "zzzz",
            },
        ],
        'id': 'deadbeefdeadbeef1111111111111111',
        },{
        'interval': 10,
        'name': 'http_monitor1',
        'retry_down': 1,
        'request': 'GET /',
        'retry_up': 1,
        'id': '101456789abcdef0**********abcdef',
        'result': 'CODE_IS',
        'timeout': 2,
        'type': 'HTTP',
        'port': 80,
        'result_code': 200,
        'content_check': 'EXTRACT',
        'content_check_input': 'BODY',
        'content_check_op': 'LEQ',
        'content_extract_type': 'INTEGER',
        'content_extract_group': 1,
        'content_extract_value': '7',
        'content_check_regex': '[g-k] (a | b)',
        'ciphers': 'ALL',
        'client_cert': 'test_idns_parse_config_healthd_conf.crt',
        'private_key': 'test_idns_parse_config_healthd_conf.key',
        'secure': True,
        },{
        'interval': 11,
        'name': 'snmpv3_monitor',
        'retry_down': 13,
        'retry_up': 14,
        'id': 'deadbeefdeadbeef2222222222222222',
        'timeout': 12,
        'type': 'SNMP',
        'port': 15,
        'context': 'context',
        'engine_id': 'engine_id',
        'user': 'vslapik',
        'authentication_protocol': 'MD5',
        'privacy_protocol': 'AES',
        'authentication_password': '**********',
        'privacy_password': '**********',
        'version': 'V3',
        'oids': [
            {
              'snmp_oid': '.*******.1',
              'type': 'INTEGER',
              'condition': 'ANY',
            },
        ],
        },{
        'interval': 111,
        'name': 'snmpv3_monitor_no_auth',
        'retry_down': 13,
        'retry_up': 14,
        'id': 'dead222222222222222222222222beef',
        'timeout': 12,
        'type': 'SNMP',
        'port': 161,
        'context': '',
        'engine_id': '',
        'user': 'vslapik',
        'authentication_protocol': 'NONE',
        'privacy_protocol': 'NONE',
        'version': 'V3',
        'oids': [
            {
              'snmp_oid': '.*******.1',
              'type': 'INTEGER',
              'condition': 'ANY',
            },
        ],
        },{
        'interval': 14,
        'name': 'snmpv3_monitor_no_auth',
        'retry_down': 14,
        'retry_up': 14,
        'id': 'dead333333333333333333333333beef',
        'timeout': 14,
        'type': 'SNMP',
        'port': 161,
        'context': '',
        'engine_id': '',
        'user': 'vslapik',
        'authentication_protocol': 'NONE',
        'privacy_protocol': 'NONE',
        'version': 'V3',
        'oids': [
            {
              'snmp_oid': '.*******.1',
              'type': 'INTEGER',
              'condition': 'ANY',
            },
        ],
    }],
    'servers': [
        {
        'name': 'server3',
        'host_format': 'IPV6',
        'disabled': False,
        'host': '3:3:3::3',
        'id': 'f6c9fed96c7d49fba7e164fbfa71ec03',
        }, {
        'name': 'server4',
        'host_format': 'IPV4',
        'disabled': False,
        'host': '*******',
        'id': '74086b91b3ab49e988e9578dc1f47727',
        }, {
        'name': 'server2',
        'host_format': 'IPV6',
        'disabled': False,
        'host': '2:2:2::2',
        'id': '31d6e5e640e84174bd92ee95f6aaa0e2',
        }, {
        'name': 'server1',
        'host_format': 'IPV4',
        'disabled': False,
        'host': '*******',
        'id': 'dd7f08b689394e64835b6c14df3a6250',
        }, {
        'name': 'server5',
        'host_format': 'FQDN',
        'disabled': False,
        'host': 'snmp.by',
        'id': '1111111111111111deadbeefdeadbeef',
        }, {
        'name': 'server6',
        'host_format': 'FQDN',
        'disabled': False,
        'host': 'snmpv3.by',
        'id': '2222222222222222deadbeefdeadbeef',
        }
    ]
}
