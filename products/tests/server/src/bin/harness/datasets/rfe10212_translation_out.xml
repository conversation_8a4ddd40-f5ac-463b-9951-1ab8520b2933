<DATABASE NAME="onedb" VERSION="MDXMLTEST">
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dns_properties"/>
        <PROPERTY NAME="custom_root_server" VALUE="false"/>
        <PROPERTY NAME="default_ttl" VALUE="28800"/>
        <PROPERTY NAME="expire" VALUE="2419200"/>
        <PROPERTY NAME="negative_ttl" VALUE="900"/>
        <PROPERTY NAME="recursion_enabled" VALUE="false"/>
        <PROPERTY NAME="lame_ttl" VALUE="600"/>
        <PROPERTY NAME="refresh" VALUE="10800"/>
        <PROPERTY NAME="retry" VALUE="3600"/>
        <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_bulkhost_ddns" VALUE="0"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="facility" VALUE="daemon"/>
        <PROPERTY NAME="log_general" VALUE="true"/>
        <PROPERTY NAME="log_client" VALUE="true"/>
        <PROPERTY NAME="log_config" VALUE="true"/>
        <PROPERTY NAME="log_database" VALUE="true"/>
        <PROPERTY NAME="log_dnssec" VALUE="true"/>
        <PROPERTY NAME="log_lame_servers" VALUE="true"/>
        <PROPERTY NAME="log_network" VALUE="true"/>
        <PROPERTY NAME="log_notify" VALUE="true"/>
        <PROPERTY NAME="log_queries" VALUE="false"/>
        <PROPERTY NAME="log_resolver" VALUE="true"/>
        <PROPERTY NAME="log_security" VALUE="true"/>
        <PROPERTY NAME="log_update" VALUE="true"/>
        <PROPERTY NAME="log_xfer_in" VALUE="true"/>
        <PROPERTY NAME="log_xfer_out" VALUE="true"/>
        <PROPERTY NAME="log_update_security" VALUE="true"/>
        <PROPERTY NAME="log_rpz" VALUE="false"/>
        <PROPERTY NAME="log_responses" VALUE="false"/>
        <PROPERTY NAME="log_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="double_confirm_zone_deletion" VALUE="true"/>
        <PROPERTY NAME="enable_secondary_notify" VALUE="true"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="dtc_dns_queries_specific_behavior" VALUE="DNS_RESPONSE_IF_NO_DTC"/>
        <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="views_count" VALUE="0"/>
        <PROPERTY NAME="srgs_count" VALUE="2"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
        <PROPERTY NAME="lower_case_ptr_dname" VALUE="true"/>
        <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
        <PROPERTY NAME="transfers_out" VALUE="10"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="nxdomain_log_query" VALUE="false"/>
        <PROPERTY NAME="blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="blacklist_action" VALUE="REDIRECT"/>
        <PROPERTY NAME="blacklist_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="blacklist_log_query" VALUE="false"/>
        <PROPERTY NAME="enable_dns64" VALUE="false"/>
        <PROPERTY NAME="host_rrset_order" VALUE="false"/>
        <PROPERTY NAME="preserve_host_rrset_order_on_secondaries" VALUE="false"/>
        <PROPERTY NAME="enable_hsm_signing" VALUE="false"/>
        <PROPERTY NAME="dns_cache_ttl" VALUE="1"/>
        <PROPERTY NAME="filter_aaaa" VALUE="NO"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="transfers_in" VALUE="10"/>
        <PROPERTY NAME="transfers_per_ns" VALUE="2"/>
        <PROPERTY NAME="serial_query_rate" VALUE="20"/>
        <PROPERTY NAME="max_cached_lifetime" VALUE="86400"/>
        <PROPERTY NAME="max_cache_ttl" VALUE="604800"/>
        <PROPERTY NAME="max_ncache_ttl" VALUE="10800"/>
        <PROPERTY NAME="enable_ms_sticky_ip" VALUE="false"/>
        <PROPERTY NAME="disable_edns" VALUE="false"/>
        <PROPERTY NAME="query_rewrite_enabled" VALUE="false"/>
        <PROPERTY NAME="zrq_high_water_mark" VALUE="10"/>
        <PROPERTY NAME="latency_poll_interval" VALUE="30"/>
        <PROPERTY NAME="latency_response_timeout" VALUE="5"/>
        <PROPERTY NAME="master_sticky_time" VALUE="120"/>
        <PROPERTY NAME="master_sticky_delta" VALUE="20"/>
        <PROPERTY NAME="bind_tombstone_low_water_mark" VALUE="100"/>
        <PROPERTY NAME="bind_tombstone_high_water_mark" VALUE="10"/>
        <PROPERTY NAME="bind_tombstone_high_water_mark_additional_cleanup" VALUE="7"/>
        <PROPERTY NAME="bind_max_xfr_clients_tracked" VALUE="10000"/>
        <PROPERTY NAME="dnssec_blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nxdomain_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_rpz_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_dns64_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="log_idns_gslb" VALUE="false"/>
        <PROPERTY NAME="log_idns_health" VALUE="false"/>
        <PROPERTY NAME="cluster" VALUE="0"/>
        <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="record_name_policy" VALUE="Allow Underscore"/>
        <PROPERTY NAME="bulk_host_name_template" VALUE="Four Octets"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dns_properties"/>
        <PROPERTY NAME="custom_root_server" VALUE="false"/>
        <PROPERTY NAME="default_ttl" VALUE="28800"/>
        <PROPERTY NAME="expire" VALUE="2419200"/>
        <PROPERTY NAME="negative_ttl" VALUE="900"/>
        <PROPERTY NAME="recursion_enabled" VALUE="false"/>
        <PROPERTY NAME="lame_ttl" VALUE="600"/>
        <PROPERTY NAME="refresh" VALUE="10800"/>
        <PROPERTY NAME="retry" VALUE="3600"/>
        <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_bulkhost_ddns" VALUE="0"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="facility" VALUE="daemon"/>
        <PROPERTY NAME="log_general" VALUE="true"/>
        <PROPERTY NAME="log_client" VALUE="true"/>
        <PROPERTY NAME="log_config" VALUE="true"/>
        <PROPERTY NAME="log_database" VALUE="true"/>
        <PROPERTY NAME="log_dnssec" VALUE="true"/>
        <PROPERTY NAME="log_lame_servers" VALUE="true"/>
        <PROPERTY NAME="log_network" VALUE="true"/>
        <PROPERTY NAME="log_notify" VALUE="true"/>
        <PROPERTY NAME="log_queries" VALUE="false"/>
        <PROPERTY NAME="log_resolver" VALUE="true"/>
        <PROPERTY NAME="log_security" VALUE="true"/>
        <PROPERTY NAME="log_update" VALUE="true"/>
        <PROPERTY NAME="log_xfer_in" VALUE="true"/>
        <PROPERTY NAME="log_xfer_out" VALUE="true"/>
        <PROPERTY NAME="log_update_security" VALUE="true"/>
        <PROPERTY NAME="log_rpz" VALUE="false"/>
        <PROPERTY NAME="log_responses" VALUE="false"/>
        <PROPERTY NAME="log_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="double_confirm_zone_deletion" VALUE="true"/>
        <PROPERTY NAME="enable_secondary_notify" VALUE="true"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="dtc_dns_queries_specific_behavior" VALUE="DTC_RESPONSE_ANYWAY"/>
        <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="views_count" VALUE="0"/>
        <PROPERTY NAME="srgs_count" VALUE="2"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
        <PROPERTY NAME="lower_case_ptr_dname" VALUE="true"/>
        <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
        <PROPERTY NAME="transfers_out" VALUE="10"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="nxdomain_log_query" VALUE="false"/>
        <PROPERTY NAME="blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="blacklist_action" VALUE="REDIRECT"/>
        <PROPERTY NAME="blacklist_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="blacklist_log_query" VALUE="false"/>
        <PROPERTY NAME="enable_dns64" VALUE="false"/>
        <PROPERTY NAME="host_rrset_order" VALUE="false"/>
        <PROPERTY NAME="preserve_host_rrset_order_on_secondaries" VALUE="false"/>
        <PROPERTY NAME="enable_hsm_signing" VALUE="false"/>
        <PROPERTY NAME="dns_cache_ttl" VALUE="1"/>
        <PROPERTY NAME="filter_aaaa" VALUE="NO"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="transfers_in" VALUE="10"/>
        <PROPERTY NAME="transfers_per_ns" VALUE="2"/>
        <PROPERTY NAME="serial_query_rate" VALUE="20"/>
        <PROPERTY NAME="max_cached_lifetime" VALUE="86400"/>
        <PROPERTY NAME="max_cache_ttl" VALUE="604800"/>
        <PROPERTY NAME="max_ncache_ttl" VALUE="10800"/>
        <PROPERTY NAME="enable_ms_sticky_ip" VALUE="false"/>
        <PROPERTY NAME="disable_edns" VALUE="false"/>
        <PROPERTY NAME="query_rewrite_enabled" VALUE="false"/>
        <PROPERTY NAME="zrq_high_water_mark" VALUE="10"/>
        <PROPERTY NAME="latency_poll_interval" VALUE="30"/>
        <PROPERTY NAME="latency_response_timeout" VALUE="5"/>
        <PROPERTY NAME="master_sticky_time" VALUE="120"/>
        <PROPERTY NAME="master_sticky_delta" VALUE="20"/>
        <PROPERTY NAME="bind_tombstone_low_water_mark" VALUE="100"/>
        <PROPERTY NAME="bind_tombstone_high_water_mark" VALUE="10"/>
        <PROPERTY NAME="bind_tombstone_high_water_mark_additional_cleanup" VALUE="7"/>
        <PROPERTY NAME="bind_max_xfr_clients_tracked" VALUE="10000"/>
        <PROPERTY NAME="dnssec_blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nxdomain_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_rpz_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_dns64_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="log_idns_gslb" VALUE="false"/>
        <PROPERTY NAME="log_idns_health" VALUE="false"/>
        <PROPERTY NAME="cluster" VALUE="0"/>
        <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="record_name_policy" VALUE="Allow Underscore"/>
        <PROPERTY NAME="bulk_host_name_template" VALUE="Four Octets"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
    </OBJECT>
</DATABASE>
