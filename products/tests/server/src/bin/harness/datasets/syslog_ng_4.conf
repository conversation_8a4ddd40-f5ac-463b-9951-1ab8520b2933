source s_external {
    tcp ( port(666) max_connections(100) );
    udp ( port(555) );  };

destination d_external_1 {  udp ( "*********" port(514) localip("0.0.0.0") ); };
destination d_internal_2 {  tcp ( "*********" port(1022) localip("[VIRTUAL_IP]") template("<$PRI>$DATE [VIRTUAL_IP] $MSGHDR$MSG\n") template_escape(no) persist-name("dinternal2")); };
destination d_external_2 {  tcp ( "*********" port(1022) localip("[VIRTUAL_IP]") ); };

log { source(s_syslogng); filter(f_warning); destination(d_internal_2); };

log { source(s_external); filter(f_info); destination(d_external_1); };
log { source(s_external); filter(f_warning); destination(d_external_2); };
