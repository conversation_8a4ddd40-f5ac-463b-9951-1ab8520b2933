<DATABASE NAME="onedb" VERSION="7.1.0-cyoung-2015-03-06-09-50" MD5="9755d0d79d1d79ffafc74fbbdc8a9aed" SCHEMA-MD5="5211c7faa9993f0adf24c8c87bf64b6b" INT-VERSION="7.1.6000-999999">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
  <PROPERTY NAME="uuid" VALUE="24d8472dfe784dc1b859ce9e9d27a1f3"/>
  <PROPERTY NAME="revision_id" VALUE="12"/>
  <PROPERTY NAME="member_type" VALUE="INFOBLOX"/>
  <PROPERTY NAME="virtual_ip" VALUE="*******"/>
  <PROPERTY NAME="subnet_mask" VALUE="*************"/>
  <PROPERTY NAME="gateway" VALUE="*********"/>
  <PROPERTY NAME="is_master" VALUE="false"/>
  <PROPERTY NAME="ha_enabled" VALUE="0"/>
  <PROPERTY NAME="lan2_enabled" VALUE="false"/>
  <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
  <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
  <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
  <PROPERTY NAME="override_support_access" VALUE="false"/>
  <PROPERTY NAME="support_access_enabled" VALUE="false"/>
  <PROPERTY NAME="is_potential_master" VALUE="false"/>
  <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
  <PROPERTY NAME="nat_enabled" VALUE="false"/>
  <PROPERTY NAME="upgrade_position" VALUE="0"/>
  <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
  <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
  <PROPERTY NAME="override_member_redirect" VALUE="false"/>
  <PROPERTY NAME="enable_member_redirect" VALUE="false"/>
  <PROPERTY NAME="revert_window_start" VALUE="0"/>
  <PROPERTY NAME="revert_window_end" VALUE="0"/>
  <PROPERTY NAME="default_route" VALUE="LAN1"/>
  <PROPERTY NAME="use_dscp" VALUE="false"/>
  <PROPERTY NAME="dscp" VALUE="0"/>
  <PROPERTY NAME="use_lan1_dscp" VALUE="false"/>
  <PROPERTY NAME="lan1_dscp" VALUE="0"/>
  <PROPERTY NAME="is_pre_provisioned" VALUE="false"/>
  <PROPERTY NAME="passive_ha_arp_enabled" VALUE="false"/>
  <PROPERTY NAME="v6_router_discovery_enabled" VALUE="false"/>
  <PROPERTY NAME="v6_use_lan1_dscp" VALUE="false"/>
  <PROPERTY NAME="v6_lan1_dscp" VALUE="0"/>
  <PROPERTY NAME="use_v4_vrrp" VALUE="true"/>
  <PROPERTY NAME="config_addr_type" VALUE="IPV4"/>
  <PROPERTY NAME="service_type_configuration" VALUE="ALL_V4"/>
  <PROPERTY NAME="is_vconnector" VALUE="false"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="virtual_oid" VALUE="1"/>
  <PROPERTY NAME="host_name" VALUE="ns0.vcopart.biz"/>
  <PROPERTY NAME="active_position" VALUE="0"/>
  <PROPERTY NAME="upgrade_group" VALUE="Default"/>
  <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
  <PROPERTY NAME="use_lom_users" VALUE="false"/>
  <PROPERTY NAME="lom_enabled" VALUE="true"/>
  <PROPERTY NAME="use_lom_enabled" VALUE="false"/>
  <PROPERTY NAME="_update_id" VALUE="0:0:bdadf054:0:80000328"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
  <PROPERTY NAME="uuid" VALUE="6c490fb7a1304a6888d9317245efc6e9"/>
  <PROPERTY NAME="revision_id" VALUE="12"/>
  <PROPERTY NAME="member_type" VALUE="INFOBLOX"/>
  <PROPERTY NAME="virtual_ip" VALUE="*******"/>
  <PROPERTY NAME="subnet_mask" VALUE="*************"/>
  <PROPERTY NAME="gateway" VALUE="*********"/>
  <PROPERTY NAME="is_master" VALUE="false"/>
  <PROPERTY NAME="ha_enabled" VALUE="0"/>
  <PROPERTY NAME="lan2_enabled" VALUE="false"/>
  <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
  <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
  <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
  <PROPERTY NAME="override_support_access" VALUE="false"/>
  <PROPERTY NAME="support_access_enabled" VALUE="false"/>
  <PROPERTY NAME="is_potential_master" VALUE="false"/>
  <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
  <PROPERTY NAME="nat_enabled" VALUE="false"/>
  <PROPERTY NAME="upgrade_position" VALUE="1"/>
  <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
  <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
  <PROPERTY NAME="override_member_redirect" VALUE="false"/>
  <PROPERTY NAME="enable_member_redirect" VALUE="false"/>
  <PROPERTY NAME="revert_window_start" VALUE="0"/>
  <PROPERTY NAME="revert_window_end" VALUE="0"/>
  <PROPERTY NAME="default_route" VALUE="LAN1"/>
  <PROPERTY NAME="use_dscp" VALUE="false"/>
  <PROPERTY NAME="dscp" VALUE="0"/>
  <PROPERTY NAME="use_lan1_dscp" VALUE="false"/>
  <PROPERTY NAME="lan1_dscp" VALUE="0"/>
  <PROPERTY NAME="is_pre_provisioned" VALUE="false"/>
  <PROPERTY NAME="passive_ha_arp_enabled" VALUE="false"/>
  <PROPERTY NAME="v6_router_discovery_enabled" VALUE="false"/>
  <PROPERTY NAME="v6_use_lan1_dscp" VALUE="false"/>
  <PROPERTY NAME="v6_lan1_dscp" VALUE="0"/>
  <PROPERTY NAME="use_v4_vrrp" VALUE="true"/>
  <PROPERTY NAME="config_addr_type" VALUE="IPV4"/>
  <PROPERTY NAME="service_type_configuration" VALUE="ALL_V4"/>
  <PROPERTY NAME="is_vconnector" VALUE="false"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="virtual_oid" VALUE="2"/>
  <PROPERTY NAME="host_name" VALUE="ns1.vcopart.biz"/>
  <PROPERTY NAME="active_position" VALUE="0"/>
  <PROPERTY NAME="upgrade_group" VALUE="Default"/>
  <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
  <PROPERTY NAME="use_lom_users" VALUE="false"/>
  <PROPERTY NAME="lom_enabled" VALUE="true"/>
  <PROPERTY NAME="use_lom_enabled" VALUE="false"/>
  <PROPERTY NAME="_update_id" VALUE="0:0:bdadf054:0:80000328"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
  <PROPERTY NAME="uuid" VALUE="c4f5a6bd5b7a47b693f8c1815c3122b1"/>
  <PROPERTY NAME="revision_id" VALUE="12"/>
  <PROPERTY NAME="member_type" VALUE="INFOBLOX"/>
  <PROPERTY NAME="virtual_ip" VALUE="*******"/>
  <PROPERTY NAME="subnet_mask" VALUE="*************"/>
  <PROPERTY NAME="gateway" VALUE="*********"/>
  <PROPERTY NAME="is_master" VALUE="false"/>
  <PROPERTY NAME="ha_enabled" VALUE="0"/>
  <PROPERTY NAME="lan2_enabled" VALUE="false"/>
  <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
  <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
  <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
  <PROPERTY NAME="override_support_access" VALUE="false"/>
  <PROPERTY NAME="support_access_enabled" VALUE="false"/>
  <PROPERTY NAME="is_potential_master" VALUE="false"/>
  <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
  <PROPERTY NAME="nat_enabled" VALUE="false"/>
  <PROPERTY NAME="upgrade_position" VALUE="2"/>
  <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
  <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
  <PROPERTY NAME="override_member_redirect" VALUE="false"/>
  <PROPERTY NAME="enable_member_redirect" VALUE="false"/>
  <PROPERTY NAME="revert_window_start" VALUE="0"/>
  <PROPERTY NAME="revert_window_end" VALUE="0"/>
  <PROPERTY NAME="default_route" VALUE="LAN1"/>
  <PROPERTY NAME="use_dscp" VALUE="false"/>
  <PROPERTY NAME="dscp" VALUE="0"/>
  <PROPERTY NAME="use_lan1_dscp" VALUE="false"/>
  <PROPERTY NAME="lan1_dscp" VALUE="0"/>
  <PROPERTY NAME="is_pre_provisioned" VALUE="false"/>
  <PROPERTY NAME="passive_ha_arp_enabled" VALUE="false"/>
  <PROPERTY NAME="v6_router_discovery_enabled" VALUE="false"/>
  <PROPERTY NAME="v6_use_lan1_dscp" VALUE="false"/>
  <PROPERTY NAME="v6_lan1_dscp" VALUE="0"/>
  <PROPERTY NAME="use_v4_vrrp" VALUE="true"/>
  <PROPERTY NAME="config_addr_type" VALUE="IPV4"/>
  <PROPERTY NAME="service_type_configuration" VALUE="ALL_V4"/>
  <PROPERTY NAME="is_vconnector" VALUE="false"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="virtual_oid" VALUE="3"/>
  <PROPERTY NAME="host_name" VALUE="ns2.vcopart.biz"/>
  <PROPERTY NAME="active_position" VALUE="0"/>
  <PROPERTY NAME="upgrade_group" VALUE="Default"/>
  <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
  <PROPERTY NAME="use_lom_users" VALUE="false"/>
  <PROPERTY NAME="lom_enabled" VALUE="true"/>
  <PROPERTY NAME="use_lom_enabled" VALUE="false"/>
  <PROPERTY NAME="_update_id" VALUE="0:0:bdadf054:0:80000328"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_direct"/>
  <PROPERTY NAME="vnode" VALUE="2"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_direct"/>
  <PROPERTY NAME="vnode" VALUE="3"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_direct_alternate"/>
  <PROPERTY NAME="vnode" VALUE="1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_explicit"/>
  <PROPERTY NAME="value1" VALUE="false"/>
  <PROPERTY NAME="parent" VALUE="2"/>
  <PROPERTY NAME="alternate" VALUE="1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_explicit"/>
  <PROPERTY NAME="value1" VALUE="true"/>
  <PROPERTY NAME="parent" VALUE="3"/>
  <PROPERTY NAME="alternate" VALUE="1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_implicit"/>
  <PROPERTY NAME="parent" VALUE="2.1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_inherit_one"/>
  <PROPERTY NAME="parent" VALUE="2.1"/>
  <PROPERTY NAME="alternate" VALUE="."/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_inherit_two"/>
  <PROPERTY NAME="parent" VALUE="2.1."/>
  <PROPERTY NAME="alternate" VALUE="1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_multi_ref"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.tests.pset_local_test_direct$2"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_multi_ref"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.tests.pset_local_test_implicit$2.1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_multi_ref_use"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.tests.pset_local_test_direct$2"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_multi_ref_use"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.tests.pset_local_test_implicit$2.1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_multi_ref_use_type_qualified"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.tests.pset_local_test_direct$2"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_multi_ref_use_type_qualified"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.tests.pset_local_test_explicit$2.1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.tests.pset_local_test_orphan"/>
  <PROPERTY NAME="name" VALUE="orphan_test"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
  <PROPERTY NAME="virtual_node" VALUE="1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network"/>
  <PROPERTY NAME="cidr" VALUE="24"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.dns.network_view$0"/>
  <PROPERTY NAME="is_ipv4" VALUE="true"/>
  <PROPERTY NAME="address" VALUE="*******"/>
  <PROPERTY NAME="network_view" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.dhcp_member"/>
  <PROPERTY NAME="network" VALUE="*******/24/0"/>
  <PROPERTY NAME="member" VALUE="1"/>
</OBJECT>
</DATABASE>
