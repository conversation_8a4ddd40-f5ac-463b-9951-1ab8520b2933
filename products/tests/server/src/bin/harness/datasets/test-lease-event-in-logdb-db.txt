<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.dns.insert_lease_event_into_logdb">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.lease_event"/>
<PROPERTY NAME="time_stamp" VALUE="2005/05/17 04:00:14.881"/>
<PROPERTY NAME="event" VALUE="Freed"/>
<PROPERTY NAME="ip_address" VALUE="********"/>
<PROPERTY NAME="mac_address" VALUE="02:04:06:08:0a:0c"/>
<PROPERTY NAME="host_name" VALUE="foo.test.com"/>
<PROPERTY NAME="member_ip" VALUE="********"/>
<PROPERTY NAME="starts" VALUE="2005/05/17 02:00:14"/>
<PROPERTY NAME="ends" VALUE="2005/05/17 04:00:14"/>
<PROPERTY NAME="billing_class" VALUE=""/>
<PROPERTY NAME="binding_state" VALUE="free"/>
<PROPERTY NAME="next_binding_state" VALUE="free"/>
<PROPERTY NAME="uid" VALUE="01:02:04:06:08:0a:0c"/>
<PROPERTY NAME="tstp" VALUE=""/>
<PROPERTY NAME="tsfp" VALUE=""/>
<PROPERTY NAME="cltt" VALUE=""/>
<PROPERTY NAME="option" VALUE=""/>
<PROPERTY NAME="comment" VALUE=""/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.dns.insert_lease_event_into_logdb">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.lease_event"/>
<PROPERTY NAME="time_stamp" VALUE="2005/05/17 03:11:52.019"/>
<PROPERTY NAME="event" VALUE="Freed"/>
<PROPERTY NAME="ip_address" VALUE="********"/>
<PROPERTY NAME="mac_address" VALUE="01:02:03:04:05:06"/>
<PROPERTY NAME="host_name" VALUE="bar.test.com"/>
<PROPERTY NAME="member_ip" VALUE="********"/>
<PROPERTY NAME="starts" VALUE="2005/05/17 01:43:28"/>
<PROPERTY NAME="ends" VALUE="2005/05/17 03:11:52"/>
<PROPERTY NAME="billing_class" VALUE=""/>
<PROPERTY NAME="binding_state" VALUE="free"/>
<PROPERTY NAME="next_binding_state" VALUE="free"/>
<PROPERTY NAME="uid" VALUE="01:01:02:03:04:05:06"/>
<PROPERTY NAME="tstp" VALUE=""/>
<PROPERTY NAME="tsfp" VALUE=""/>
<PROPERTY NAME="cltt" VALUE=""/>
<PROPERTY NAME="option" VALUE=""/>
<PROPERTY NAME="comment" VALUE=""/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.dns.insert_lease_event_into_logdb">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.lease_event"/>
<PROPERTY NAME="time_stamp" VALUE="2005/05/17 02:00:14.364"/>
<PROPERTY NAME="event" VALUE="Renewed"/>
<PROPERTY NAME="ip_address" VALUE="********"/>
<PROPERTY NAME="mac_address" VALUE="02:04:06:08:0a:0c"/>
<PROPERTY NAME="host_name" VALUE="foo.test.com"/>
<PROPERTY NAME="member_ip" VALUE="********"/>
<PROPERTY NAME="starts" VALUE="2005/05/17 02:00:14"/>
<PROPERTY NAME="ends" VALUE="2005/05/17 04:00:14"/>
<PROPERTY NAME="billing_class" VALUE=""/>
<PROPERTY NAME="binding_state" VALUE="active"/>
<PROPERTY NAME="next_binding_state" VALUE="free"/>
<PROPERTY NAME="uid" VALUE="01:02:04:06:08:0a:0c"/>
<PROPERTY NAME="tstp" VALUE=""/>
<PROPERTY NAME="tsfp" VALUE=""/>
<PROPERTY NAME="cltt" VALUE=""/>
<PROPERTY NAME="option" VALUE=""/>
<PROPERTY NAME="comment" VALUE=""/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.dns.insert_lease_event_into_logdb">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.lease_event"/>
<PROPERTY NAME="time_stamp" VALUE="2005/05/17 01:43:28.862"/>
<PROPERTY NAME="event" VALUE="Issued"/>
<PROPERTY NAME="ip_address" VALUE="********"/>
<PROPERTY NAME="mac_address" VALUE="01:02:03:04:05:06"/>
<PROPERTY NAME="host_name" VALUE="bar.test.com"/>
<PROPERTY NAME="member_ip" VALUE="********"/>
<PROPERTY NAME="starts" VALUE="2005/05/17 01:43:28"/>
<PROPERTY NAME="ends" VALUE="2005/05/17 03:43:28"/>
<PROPERTY NAME="billing_class" VALUE=""/>
<PROPERTY NAME="binding_state" VALUE="active"/>
<PROPERTY NAME="next_binding_state" VALUE="free"/>
<PROPERTY NAME="uid" VALUE="01:01:02:03:04:05:06"/>
<PROPERTY NAME="tstp" VALUE=""/>
<PROPERTY NAME="tsfp" VALUE=""/>
<PROPERTY NAME="cltt" VALUE=""/>
<PROPERTY NAME="option" VALUE=""/>
<PROPERTY NAME="comment" VALUE=""/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.dns.insert_lease_event_into_logdb">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.lease_event"/>
<PROPERTY NAME="time_stamp" VALUE="2005/05/17 01:00:02.593"/>
<PROPERTY NAME="event" VALUE="Issued"/>
<PROPERTY NAME="ip_address" VALUE="********"/>
<PROPERTY NAME="mac_address" VALUE="02:04:06:08:0a:0c"/>
<PROPERTY NAME="host_name" VALUE="foo.test.com"/>
<PROPERTY NAME="member_ip" VALUE="********"/>
<PROPERTY NAME="starts" VALUE="2005/05/17 01:00:02"/>
<PROPERTY NAME="ends" VALUE="2005/05/17 03:00:02"/>
<PROPERTY NAME="billing_class" VALUE=""/>
<PROPERTY NAME="binding_state" VALUE="active"/>
<PROPERTY NAME="next_binding_state" VALUE="free"/>
<PROPERTY NAME="uid" VALUE="01:02:04:06:08:0a:0c"/>
<PROPERTY NAME="tstp" VALUE=""/>
<PROPERTY NAME="tsfp" VALUE=""/>
<PROPERTY NAME="cltt" VALUE=""/>
<PROPERTY NAME="option" VALUE=""/>
<PROPERTY NAME="comment" VALUE=""/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>

