<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_svcb"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1741586128"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.test"/>
  <PROPERTY NAME="name" VALUE="unk11"/>
  <PROPERTY NAME="display_name" VALUE="unk11"/>
  <PROPERTY NAME="priority" VALUE="16"/>
  <PROPERTY NAME="no_default_alpn" VALUE="true"/>
  <PROPERTY NAME="mandatory" VALUE="alpn,ipv4hint"/>
  <PROPERTY NAME="target" VALUE="foo.example.org"/>
  <PROPERTY NAME="display_target" VALUE="foo.example.org"/>
  <PROPERTY NAME="is_rpz_rule" VALUE="false"/>
  <PROPERTY NAME="port" VALUE="53"/>
  <PROPERTY NAME="alpn" VALUE="h2,h3-19"/>
  <PROPERTY NAME="ipv4_hint" VALUE="**********,************"/>
  <PROPERTY NAME="ipv6_hint" VALUE="2001:db8:122:344::1"/>
  <PROPERTY NAME="ech" VALUE="AEn+DQBFKwAgACABWIHUGj4u+PIggYXcR5JF0gYk3dCRioBW8uJq9H4mKAAIAAEAAQABAANAEnB1YmxpYy50bHMtZWNoLmRldgAA"/>
  <PROPERTY NAME="rdata_hash" VALUE="081d64ed02a7f98681740125d509d8571d8ae3d7cac7495f221bce1d5eae199933cc5711f9ea235bef165dcef8db9fa8ac4c70d9ae336eda4a39d2ce8dfdeb17"/>
  <PROPERTY NAME="additional_params" VALUE="key65333=&quot;ex1&quot; key65444=&quot;ex2&quot;"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_svcb"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="17415861289"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.temp"/>
  <PROPERTY NAME="name" VALUE="unk"/>
  <PROPERTY NAME="display_name" VALUE="unk"/>
  <PROPERTY NAME="priority" VALUE="600"/>
  <PROPERTY NAME="no_default_alpn" VALUE="true"/>
  <PROPERTY NAME="mandatory" VALUE="alpn,ipv4hint"/>
  <PROPERTY NAME="target" VALUE="test.com"/>
  <PROPERTY NAME="display_target" VALUE="test.com"/>
  <PROPERTY NAME="is_rpz_rule" VALUE="false"/>
  <PROPERTY NAME="port" VALUE="10443"/>
  <PROPERTY NAME="alpn" VALUE="h2,h3"/>
  <PROPERTY NAME="ipv4_hint" VALUE="*********"/>
  <PROPERTY NAME="ipv6_hint" VALUE="2001:db8::1"/>
  <PROPERTY NAME="ech" VALUE="AEn+DQBFKwAgACABWIHUGj4u+PIggYXcR5JF0gYk3dCRioBW8uJq9H4mKAAIAAEAAQABAANAEnB1YmxpYy50bHMtZWNoLmRldgAA"/>
  <PROPERTY NAME="rdata_hash" VALUE="8f6cbe1258a6f6eb04550de76ff7e1d5b81dc47ecf193c25d08d0133cdf9616d77d33479cfa57b71b3fde9c78caef6eb782d1635db72f8d5e735cce6b4f1f5fd"/>
  <PROPERTY NAME="additional_params" VALUE="key64=&quot;ex2&quot; key65=&quot;ex1&quot;"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_svcb"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="17415861289"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.temp"/>
  <PROPERTY NAME="name" VALUE="new_unk"/>
  <PROPERTY NAME="display_name" VALUE="new_unk"/>
  <PROPERTY NAME="priority" VALUE="1"/>
  <PROPERTY NAME="no_default_alpn" VALUE="true"/>
  <PROPERTY NAME="mandatory" VALUE="alpn,ech"/>
  <PROPERTY NAME="target" VALUE="google.com"/>
  <PROPERTY NAME="display_target" VALUE="google.com"/>
  <PROPERTY NAME="is_rpz_rule" VALUE="false"/>
  <PROPERTY NAME="port" VALUE="43636"/>
  <PROPERTY NAME="alpn" VALUE="h2"/>
  <PROPERTY NAME="ipv4_hint" VALUE="***********,***********"/>
  <PROPERTY NAME="ipv6_hint" VALUE="2002::1"/>
  <PROPERTY NAME="ech" VALUE="abcd"/>
  <PROPERTY NAME="rdata_hash" VALUE="6f5f2cf22c0aa3f1f9b81812b897b65e0c7efdb7943cff1d8a91700f7754853bd959da06e88b2427c76082b129dabf27075cb14d1bc666bd498b64894c4623e6"/>
  <PROPERTY NAME="additional_params" VALUE="key7=&quot;/q{?dns}&quot;"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="object" VALUE=".com.infoblox.dns.zone$._default.com.temp"/>
  <PROPERTY NAME="tag" VALUE=".Site"/>
  <PROPERTY NAME="value" VALUE="Bengaluru"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="object" VALUE=".com.infoblox.dns.bind_svcb$._default.com.temp.new_unk.6f5f2cf22c0aa3f1f9b81812b897b65e0c7efdb7943cff1d8a91700f7754853bd959da06e88b2427c76082b129dabf27075cb14d1bc666bd498b64894c4623e6"/>
  <PROPERTY NAME="tag" VALUE=".Site"/>
  <PROPERTY NAME="value" VALUE="Bangalore"/>
  <PROPERTY NAME="inheritance_source" VALUE=".com.infoblox.dns.zone$._default.com.temp"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.test-admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.bind_resource_record"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.bind_svcb$._default.com.temp.new_unk.6f5f2cf22c0aa3f1f9b81812b897b65e0c7efdb7943cff1d8a91700f7754853bd959da06e88b2427c76082b129dabf27075cb14d1bc666bd498b64894c4623e6"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.test-admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
</DATABASE>
