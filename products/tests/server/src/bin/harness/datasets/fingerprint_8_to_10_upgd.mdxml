<MDXML>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.lease"
        POST-STRUCT-CALLBACK="fp_upg_8_0_0_to_10_0_0">
    </STRUCTURE-TRANSFORM>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.lease_event"
        POST-STRUCT-CALLBACK="fp_upg_8_0_0_to_10_0_0">
    </STRUCTURE-TRANSFORM>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.dhcp_host"
        POST-STRUCT-CALLBACK="fp_upg_8_0_0_to_10_0_0">
    </STRUCTURE-TRANSFORM>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.dhcp_fingerprint"
        POST-STRUCT-CALLBACK="custom_fp_upg_8_0_0_to_10_0_0"/>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.smart_folder_query_item"
        POST-STRUCT-CALLBACK="smart_folder_query_fp_upg_8_0_0_to_10_0_0">
    </STRUCTURE-TRANSFORM>
</MDXML>
