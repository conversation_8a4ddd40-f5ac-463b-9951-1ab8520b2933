options {
	directory "TEST_WORKING_DIRECTORY";
	pid-file "secondary-named.pid";
	recursion no;
	listen-on port NAMED_PORT { 127.0.0.1; };
};

include "/infoblox/var/named_conf/rndc.key";

controls {
        inet 127.0.0.1 port RNDC_PORT
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

view "VIEW_NAME" {
	match-clients { *********; };
	query-source address *********;
	zone "sec.net" {
		type master;
		notify no;
		file "rndc-sc-2-sec-sec.net";
	};
	zone "recurs.test.net" {
		type master;
		notify no;
		file "rndc-sc-2-sec-test.net";
	};
};

view "_default" {
	query-source address 127.0.0.1;
	zone "sec.net" {
		type master;
		notify no;
		file "rndc-sc-def-sec-sec.net";
	};
	zone "recurs.test.net" {
		type master;
		notify no;
		file "rndc-sc-def-sec-test.net";
	};
};
