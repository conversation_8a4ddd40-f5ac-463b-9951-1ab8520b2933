$ORIGIN .
$TTL 86400	; 1 day
rrtest.foo		IN SOA	rrtest.foo. root.localhost. (
				2007040905 ; serial
				28800      ; refresh (8 hours)
				14400      ; retry (4 hours)
				3600000    ; expire (5 weeks 6 days 16 hours)
				86400      ; minimum (1 day)
				)
			NS	localhost.
$ORIGIN rrtest.foo.
$TTL 4711	; 1 hour 18 minutes 31 seconds

reca IN A *******
reca1 IN A ********
rectxt IN TXT "foobar"
rectxt2 IN TXT ( "allanfoo" "bar" "baz" )
rectxtquote IN TXT "\"foo\\bar"
aaaa1 IN AAAA a:b:c:d:1:2:3:4
aaaa2 IN AAAA 2000::1:2:3:4

@ IN NS ns1.test.com.
 IN NS ns2.test.com.

txt1 IN type16 "Some text"
txt2 IN TXT ("v=spf1 ip4:***********/24 ip4:***********/24 ip4:************/24 ip4:************/24" " mx a:xmail.wiley.com a:xmail2.wiley.com a:xmail3.wiley.com a:xmail4.wiley.com")

r1 IN TXT "( \"foo\" \"bar\" )"
r2 IN TXT ( "f\000oo" "bar" )
r3 IN TXT "( &quot;foo&quot; &quot;bar&quot; )"

t99 IN      TYPE99  \# 15 0E763D73706631206D78202D616C6C

afsdbrec IN AFSDB 47 foo.bar.baz
