ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
local-address 127.0.0.1;
server-identifier 127.0.0.1;
authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;

# Custom options
option test-1 code 161 = text;
option test-2 code 162 = text;
option test-3 code 163 = text;
option test-4 code 164 = unsigned integer 8;

ddns-updates off;
ignore client-updates;

subnet ********* netmask ************* {
    option dhcp-max-message-size 1024;

    # options 'test-1' and 'test-2' have length 450 bytes
    option test-1 "11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111";
    option test-2 "22222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222";
    option test-3 "333";
    option test-4 4;

	host 127.0.0.11 {
		dynamic;
		hardware ethernet 11:22:33:44:55:63;
		fixed-address 127.0.0.13;
		option host-name "host.com";
		option extensions-path "/ext/";
		option root-path "/root/";
		option nis-domain "nis.test";
	}
}

#End of dhcpd.conf file
