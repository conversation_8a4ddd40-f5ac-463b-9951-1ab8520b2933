<DATABASE NAME="onedb" VERSION="7.0.0" MD5="ef5089054c00609637c8ac1d6dd2d010" SCHEMA-MD5="1d242c1b992e44922d4b6b532c167e4b">
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
    <PROPERTY NAME="uuid" VALUE="315cc3c9b4b7456399472778c88c51b1"/>
    <PROPERTY NAME="revision_id" VALUE="14"/>
    <PROPERTY NAME="member_type" VALUE="INFOBLOX"/>
    <PROPERTY NAME="virtual_ip" VALUE="***********"/>
    <PROPERTY NAME="subnet_mask" VALUE="*************"/>
    <PROPERTY NAME="gateway" VALUE="**********"/>
    <PROPERTY NAME="is_master" VALUE="true"/>
    <PROPERTY NAME="ha_enabled" VALUE="0"/>
    <PROPERTY NAME="lan2_enabled" VALUE="false"/>
    <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
    <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
    <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
    <PROPERTY NAME="override_support_access" VALUE="false"/>
    <PROPERTY NAME="support_access_enabled" VALUE="false"/>
    <PROPERTY NAME="is_potential_master" VALUE="true"/>
    <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
    <PROPERTY NAME="nat_enabled" VALUE="false"/>
    <PROPERTY NAME="upgrade_position" VALUE="0"/>
    <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
    <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
    <PROPERTY NAME="override_member_redirect" VALUE="false"/>
    <PROPERTY NAME="enable_member_redirect" VALUE="false"/>
    <PROPERTY NAME="revert_window_start" VALUE="0"/>
    <PROPERTY NAME="revert_window_end" VALUE="0"/>
    <PROPERTY NAME="default_route" VALUE="LAN1"/>
    <PROPERTY NAME="use_dscp" VALUE="false"/>
    <PROPERTY NAME="dscp" VALUE="0"/>
    <PROPERTY NAME="use_lan1_dscp" VALUE="false"/>
    <PROPERTY NAME="lan1_dscp" VALUE="0"/>
    <PROPERTY NAME="is_pre_provisioned" VALUE="false"/>
    <PROPERTY NAME="passive_ha_arp_enabled" VALUE="false"/>
    <PROPERTY NAME="v6_router_discovery_enabled" VALUE="false"/>
    <PROPERTY NAME="v6_use_lan1_dscp" VALUE="false"/>
    <PROPERTY NAME="v6_lan1_dscp" VALUE="0"/>
    <PROPERTY NAME="use_v4_vrrp" VALUE="true"/>
    <PROPERTY NAME="config_addr_type" VALUE="IPV4"/>
    <PROPERTY NAME="service_type_configuration" VALUE="ALL_V4"/>
    <PROPERTY NAME="is_vconnector" VALUE="false"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="virtual_oid" VALUE="0"/>
    <PROPERTY NAME="host_name" VALUE="grid.top.zn"/>
    <PROPERTY NAME="active_position" VALUE="0"/>
    <PROPERTY NAME="upgrade_group" VALUE="Grid Master"/>
    <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
    <PROPERTY NAME="use_lom_users" VALUE="false"/>
    <PROPERTY NAME="lom_enabled" VALUE="true"/>
    <PROPERTY NAME="use_lom_enabled" VALUE="false"/>
    <PROPERTY NAME="_update_id" VALUE="0:0:48f68a8f:0:80004a91"/>
    <PROPERTY NAME="proxy_auth_key" VALUE="{0}_{aes}_MAAAAImytZ3j9dr5NOEgRB8Nc4uJVxsWfwVXV6XHUkLieEvCpZhanK+rz37bixSn3A4R9A=="/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
    <PROPERTY NAME="is_external_primary" VALUE="false"/>
    <PROPERTY NAME="active_directory_option" VALUE="0"/>
    <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
    <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
    <PROPERTY NAME="underscore_zone" VALUE="false"/>
    <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
    <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
    <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
    <PROPERTY NAME="disabled" VALUE="false"/>
    <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
    <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
    <PROPERTY NAME="primary_type" VALUE="Grid"/>
    <PROPERTY NAME="zone_qal_option" VALUE="0"/>
    <PROPERTY NAME="import_zone_option" VALUE="false"/>
    <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
    <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
    <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
    <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
    <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
    <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
    <PROPERTY NAME="locked" VALUE="false"/>
    <PROPERTY NAME="check_names_policy" VALUE="1"/>
    <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
    <PROPERTY NAME="override_notify_delay" VALUE="false"/>
    <PROPERTY NAME="notify_delay" VALUE="5"/>
    <PROPERTY NAME="forwarders_only" VALUE="false"/>
    <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
    <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
    <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
    <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
    <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
    <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
    <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
    <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
    <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
    <PROPERTY NAME="ms_last_sync_time" VALUE="1433257446"/>
    <PROPERTY NAME="ms_last_sync_status" VALUE="OK"/>
    <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
    <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
    <PROPERTY NAME="ms_sync_retry_offset" VALUE="0"/>
    <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
    <PROPERTY NAME="enable_lb_link" VALUE="false"/>
    <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
    <PROPERTY NAME="rpz_policy" VALUE="Given"/>
    <PROPERTY NAME="is_multimaster" VALUE="false"/>
    <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
    <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
    <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
    <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
    <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
    <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
    <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
    <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
    <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
    <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
    <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
    <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
    <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
    <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
    <PROPERTY NAME="zone" VALUE="._default"/>
    <PROPERTY NAME="name" VALUE="zn.top"/>
    <PROPERTY NAME="override_global_system_name" VALUE="false"/>
    <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
    <PROPERTY NAME="fqdn" VALUE="top.zn"/>
    <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
    <PROPERTY NAME="display_name" VALUE="zn.top"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ext_secondary_server"/>
    <PROPERTY NAME="tsig_option" VALUE="false"/>
    <PROPERTY NAME="x_tsig_option" VALUE="false"/>
    <PROPERTY NAME="ext_sec_stealth" VALUE="false"/>
    <PROPERTY NAME="position" VALUE="0"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="ext_sec_address" VALUE="*******"/>
    <PROPERTY NAME="ext_sec_name" VALUE="ext.top.zn"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ext_secondary_server"/>
    <PROPERTY NAME="tsig_option" VALUE="false"/>
    <PROPERTY NAME="x_tsig_option" VALUE="false"/>
    <PROPERTY NAME="ext_sec_stealth" VALUE="false"/>
    <PROPERTY NAME="position" VALUE="1"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="ext_sec_address" VALUE="*******"/>
    <PROPERTY NAME="ext_sec_name" VALUE="ext.top.zn"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ext_secondary_server"/>
    <PROPERTY NAME="tsig_option" VALUE="false"/>
    <PROPERTY NAME="x_tsig_option" VALUE="false"/>
    <PROPERTY NAME="ext_sec_stealth" VALUE="false"/>
    <PROPERTY NAME="position" VALUE="2"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="ext_sec_address" VALUE="*******"/>
    <PROPERTY NAME="ext_sec_name" VALUE="ext2.top.zn"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_grid_primary"/>
    <PROPERTY NAME="stealth" VALUE="false"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="grid_member" VALUE="0"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ms_secondary_server"/>
    <PROPERTY NAME="is_master" VALUE="true"/>
    <PROPERTY NAME="stealth" VALUE="false"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="ms_server" VALUE="0"/>
    <PROPERTY NAME="ns_ip" VALUE="*******"/>
    <PROPERTY NAME="ns_name" VALUE="ms.top.zn"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_nameserver"/>
    <PROPERTY NAME="auto_create_ptr" VALUE="true"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="dname" VALUE="ms.top.zn"/>
    <PROPERTY NAME="address" VALUE="*******"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_nameserver"/>
    <PROPERTY NAME="auto_create_ptr" VALUE="true"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="dname" VALUE="ext.top.zn"/>
    <PROPERTY NAME="address" VALUE="*******"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_nameserver"/>
    <PROPERTY NAME="auto_create_ptr" VALUE="true"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="dname" VALUE="ext.top.zn"/>
    <PROPERTY NAME="address" VALUE="*******"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_nameserver"/>
    <PROPERTY NAME="auto_create_ptr" VALUE="true"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="dname" VALUE="man1.top.zn"/>
    <PROPERTY NAME="address" VALUE="*******"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_nameserver"/>
    <PROPERTY NAME="auto_create_ptr" VALUE="true"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="dname" VALUE="grid.top.zn"/>
    <PROPERTY NAME="address" VALUE="*******"/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
    <PROPERTY NAME="ttl" VALUE="3600"/>
    <PROPERTY NAME="ttl_option" VALUE="1"/>
    <PROPERTY NAME="disabled" VALUE="false"/>
    <PROPERTY NAME="auto_created" VALUE="true"/>
    <PROPERTY NAME="manually_created" VALUE="false"/>
    <PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.127.0.0"/>
    <PROPERTY NAME="name" VALUE=""/>
    <PROPERTY NAME="dname" VALUE="cluster"/>
    <PROPERTY NAME="reversed_dname" VALUE="._default.cluster"/>
    <PROPERTY NAME="display_name" VALUE=""/>
    <PROPERTY NAME="dns_service_status" VALUE=""/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
    <PROPERTY NAME="ttl_option" VALUE="0"/>
    <PROPERTY NAME="disabled" VALUE="false"/>
    <PROPERTY NAME="auto_created" VALUE="true"/>
    <PROPERTY NAME="manually_created" VALUE="true"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="name" VALUE=""/>
    <PROPERTY NAME="dname" VALUE="ext.top.zn"/>
    <PROPERTY NAME="reversed_dname" VALUE="._default.zn.top.ext"/>
    <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
    <PROPERTY NAME="display_name" VALUE=""/>
    <PROPERTY NAME="dns_service_status" VALUE=""/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
    <PROPERTY NAME="ttl_option" VALUE="0"/>
    <PROPERTY NAME="disabled" VALUE="false"/>
    <PROPERTY NAME="auto_created" VALUE="true"/>
    <PROPERTY NAME="manually_created" VALUE="false"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="name" VALUE=""/>
    <PROPERTY NAME="dname" VALUE="ext2.top.zn"/>
    <PROPERTY NAME="reversed_dname" VALUE="._default.zn.top.ext2"/>
    <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
    <PROPERTY NAME="display_name" VALUE=""/>
    <PROPERTY NAME="dns_service_status" VALUE=""/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
    <PROPERTY NAME="ttl_option" VALUE="0"/>
    <PROPERTY NAME="disabled" VALUE="false"/>
    <PROPERTY NAME="auto_created" VALUE="true"/>
    <PROPERTY NAME="manually_created" VALUE="true"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="name" VALUE=""/>
    <PROPERTY NAME="dname" VALUE="ms.top.zn"/>
    <PROPERTY NAME="reversed_dname" VALUE="._default.zn.top.ms"/>
    <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
    <PROPERTY NAME="display_name" VALUE=""/>
    <PROPERTY NAME="dns_service_status" VALUE=""/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
    <PROPERTY NAME="ttl_option" VALUE="0"/>
    <PROPERTY NAME="disabled" VALUE="false"/>
    <PROPERTY NAME="auto_created" VALUE="false"/>
    <PROPERTY NAME="manually_created" VALUE="true"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="name" VALUE=""/>
    <PROPERTY NAME="dname" VALUE="man1.top.zn"/>
    <PROPERTY NAME="reversed_dname" VALUE="._default.zn.top.man1"/>
    <PROPERTY NAME="comment" VALUE="Auto-created by Add NS"/>
    <PROPERTY NAME="display_name" VALUE=""/>
    <PROPERTY NAME="dns_service_status" VALUE=""/>
</OBJECT>
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
    <PROPERTY NAME="ttl_option" VALUE="0"/>
    <PROPERTY NAME="disabled" VALUE="false"/>
    <PROPERTY NAME="auto_created" VALUE="true"/>
    <PROPERTY NAME="manually_created" VALUE="true"/>
    <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
    <PROPERTY NAME="name" VALUE=""/>
    <PROPERTY NAME="dname" VALUE="grid.top.zn"/>
    <PROPERTY NAME="reversed_dname" VALUE="._default.zn.top.grid"/>
    <PROPERTY NAME="comment" VALUE="Auto-created by Add NS"/>
    <PROPERTY NAME="display_name" VALUE=""/>
    <PROPERTY NAME="dns_service_status" VALUE="0"/>
</OBJECT>
</DATABASE>
