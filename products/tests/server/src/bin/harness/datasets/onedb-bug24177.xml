<DATABASE NAME="onedb" VERSION="4.x-36380">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.fixed_address"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="match_option" VALUE="0"/>
  <PROPERTY NAME="prepend_zero" VALUE="false"/>
  <PROPERTY NAME="override_boot_file" VALUE="false"/>
  <PROPERTY NAME="override_boot_server" VALUE="false"/>
  <PROPERTY NAME="override_next_server" VALUE="false"/>
  <PROPERTY NAME="override_broadcast_address" VALUE="false"/>
  <PROPERTY NAME="override_custom_options" VALUE="false"/>
  <PROPERTY NAME="override_domain_name" VALUE="false"/>
  <PROPERTY NAME="override_domain_name_servers" VALUE="false"/>
  <PROPERTY NAME="override_lease_time" VALUE="false"/>
  <PROPERTY NAME="override_pxe_lease_time" VALUE="false"/>
  <PROPERTY NAME="override_routers" VALUE="false"/>
  <PROPERTY NAME="pxe_lease_time_enabled" VALUE="false"/>
  <PROPERTY NAME="override_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="ddns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="override_deny_bootp_request" VALUE="false"/>
  <PROPERTY NAME="deny_bootp" VALUE="false"/>
  <PROPERTY NAME="override_ignore_dhcp_param_request_list" VALUE="false"/>
  <PROPERTY NAME="ignore_dhcp_param_request_list" VALUE="false"/>
  <PROPERTY NAME="ip_address" VALUE="************"/>
  <PROPERTY NAME="mac_address" VALUE="01:cc:cc:dd:ee:ff"/>
  <PROPERTY NAME="dhcp_client_identifier" VALUE=""/>
  <PROPERTY NAME="network" VALUE="***********/24/1"/>
  <PROPERTY NAME="network_view" VALUE="1"/>
  <PROPERTY NAME="ms_server" VALUE="."/>
  <PROPERTY NAME="is_ipv4" VALUE="true"/>
  <PROPERTY NAME="v6_prefix" VALUE=""/>
  <PROPERTY NAME="__key" VALUE="************.01:cc:cc:dd:ee:ff..***********/24/1."/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network"/>
  <PROPERTY NAME="address" VALUE="***********"/>
  <PROPERTY NAME="cidr" VALUE="24"/>
  <PROPERTY NAME="is_ipv4" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="authoritative" VALUE="0"/>
  <PROPERTY NAME="pxe_lease_time_enabled" VALUE="false"/>
  <PROPERTY NAME="override_boot_file" VALUE="false"/>
  <PROPERTY NAME="override_boot_server" VALUE="false"/>
  <PROPERTY NAME="override_next_server" VALUE="false"/>
  <PROPERTY NAME="override_broadcast_address" VALUE="false"/>
  <PROPERTY NAME="override_routers" VALUE="false"/>
  <PROPERTY NAME="override_custom_options" VALUE="false"/>
  <PROPERTY NAME="override_domain_name" VALUE="false"/>
  <PROPERTY NAME="override_authoritative" VALUE="false"/>
  <PROPERTY NAME="override_domain_name_servers" VALUE="false"/>
  <PROPERTY NAME="override_lease_time" VALUE="false"/>
  <PROPERTY NAME="override_pxe_lease_time" VALUE="false"/>
  <PROPERTY NAME="override_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="ddns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="override_ddns_use_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_use_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="override_ddns_no_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_no_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="override_ddns_server_use_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_server_use_fqdn" VALUE="false"/>
  <PROPERTY NAME="override_update_static_leases" VALUE="false"/>
  <PROPERTY NAME="update_static_leases" VALUE="false"/>
  <PROPERTY NAME="enable_dhcp_thresholds" VALUE="false"/>
  <PROPERTY NAME="range_high_water_mark" VALUE="95"/>
  <PROPERTY NAME="range_low_water_mark" VALUE="0"/>
  <PROPERTY NAME="enable_threshold_email_warnings" VALUE="false"/>
  <PROPERTY NAME="enable_threshold_snmp_warnings" VALUE="false"/>
  <PROPERTY NAME="override_threshold_email_notification" VALUE="false"/>
  <PROPERTY NAME="override_recycle_leases" VALUE="false"/>
  <PROPERTY NAME="recycle_leases" VALUE="true"/>
  <PROPERTY NAME="ddns_ttl" VALUE="0"/>
  <PROPERTY NAME="override_ignore_dhcp_param_request_list" VALUE="false"/>
  <PROPERTY NAME="ignore_dhcp_param_request_list" VALUE="false"/>
  <PROPERTY NAME="override_deny_bootp_request" VALUE="false"/>
  <PROPERTY NAME="deny_bootp" VALUE="false"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.dns.network_view$1"/>
  <PROPERTY NAME="network_view" VALUE="1"/>
  <PROPERTY NAME="__key" VALUE="***********/24/1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
  <PROPERTY NAME="parent" VALUE="/"/>
  <PROPERTY NAME="name" VALUE="nview1"/>
  <PROPERTY NAME="id" VALUE="1"/>
  <PROPERTY NAME="__key" VALUE="1"/>
  <PROPERTY NAME="comment" VALUE=""/>
</OBJECT>
</DATABASE>
