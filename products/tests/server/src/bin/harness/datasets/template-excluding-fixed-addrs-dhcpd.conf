ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
ddns-local-address4 ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/infoblox/var/dhcpd_conf/dhcp_updater.key";

subnet ********* netmask ********* {
	host ********** {
		dynamic;
		hardware ethernet a1:b2:c3:d4:e5:f6;
		fixed-address **********;
	}
	host ********** {
		dynamic;
		hardware ethernet b1:c2:d3:e4:f5:a6;
		fixed-address **********;
	}
	host ********** {
		dynamic;
		hardware ethernet d1:d2:d3:d4:d5:d6;
		fixed-address **********;
	}
	host **********0 {
		dynamic;
		hardware ethernet c1:d2:e3:f4:a5:b6;
		fixed-address **********0;
	}
	pool {
		infoblox-range ********* **********4;
		range ********* **********4;
	}
	pool {
		infoblox-range ********** ***********;
		range ********** ***********;
	}
}

subnet 30.0.0.0 netmask ********* {
	host ********** {
		dynamic;
		hardware ethernet c1:d2:e3:f4:a5:b6;
		fixed-address **********;
	}
	host ********** {
		dynamic;
		hardware ethernet e1:d2:d3:d4:d5:d6;
		fixed-address **********;
	}
	host ********* {
		dynamic;
		hardware ethernet a1:b2:c3:d4:e5:f6;
		fixed-address *********;
	}
	host ********* {
		dynamic;
		hardware ethernet b1:c2:d3:e4:f5:a6;
		fixed-address *********;
	}
	host ********* {
		dynamic;
		hardware ethernet 00:00:00:00:00:00;
		fixed-address *********;
	}
	host ********* {
		dynamic;
		hardware ethernet d1:d2:d3:d4:d5:d6;
		fixed-address *********;
	}
	host ******** {
		dynamic;
		hardware ethernet cc:dd:ed:f4:a5:b6;
		fixed-address ********;
	}
	pool {
		infoblox-range ******** **********;
		range ******** **********;
	}
	pool {
		infoblox-range ********* **********;
		range ********* **********;
	}
	pool {
		infoblox-range ********** **********;
		range ********** **********;
	}
	pool {
		infoblox-range ************ ************;
		range ************ ************;
	}
}

subnet ********* netmask *********** {
	pool {
		infoblox-range *********** ***********;
		range *********** ***********;
	}
}

subnet *********** netmask ************* {
	not authoritative;
}

#End of dhcpd.conf file
