$ORIGIN .
$TTL 86400	; 1 day
rrtest.foo		IN SOA	rrtest.foo. root.localhost. (
				2007040905 ; serial
				28800      ; refresh (8 hours)
				14400      ; retry (4 hours)
				3600000    ; expire (5 weeks 6 days 16 hours)
				86400      ; minimum (1 day)
				)
			NS	localhost.
                        NS      ns1.test.com.
                        NS      ns2.test.com.
                        NS      ns3.test.com.

dd1.rrtest.foo                     IN NS   xxx.com.
dd1.rrtest.foo                     IN NS   yyy.com.
dd2.rrtest.foo                     IN NS   zzz.com.

$ORIGIN rrtest.foo.
$TTL 4711	; 1 hour 18 minutes 31 seconds


reca1 IN A *******
reca2 IN A *******
reca3 IN A *******
rectxt IN TXT "foobar"
rectxt2 IN TXT ( "allanfoo" "bar" "baz" )
rectxtquote IN TXT "\"foo\\bar"
aaaa1 IN AAAA a:b:c:d:1:2:3:4


