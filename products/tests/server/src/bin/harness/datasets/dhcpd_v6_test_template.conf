ddns-update-style interim;
# ddns txt record handling - isc_transitional
log-facility daemon;
option dhcp6.preference 100;
option dhcp6.rapid-commit;
option dhcp6.info-refresh-time 111;
option dhcp6.nisp-servers 2011::2011;
option dhcp6.server-id 00:55;
option domain-name "ZONE-NAME";
default-lease-time LEASE-TIME;
preferred-lifetime LEASE-TIME;
option dhcp6.name-servers MY-ADDRESS;
omapi-key DHCP_UPDATER;
omapi-port 7912;
ddns-updates DDNS-UPDATES;
# DNS update retry interval: 5
allow client-updates;
ddns-ttl 9000;
ddns-domainname = pick ( option fqdn.domainname, "ddns.ZONE-NAME" );
ddns-hostname = pick ( option fqdn.hostname,option host-name,
    concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
option host-name = config-option server.ddns-hostname;
ping-check false;

include "WORKING-DIR/dhcp_updater.key";

option space vendor6 code width 2 length width 2;
option vsio.vendor6 code 1045 = encapsulate vendor6;
option vendor6.name62 code 2 = string;
option vendor6.name61 code 1 = string;

MY-NETWORKS

# Where to send the DNS updates
zone "ZONE-NAME." {
    primary DOMAIN-NS;
    key DHCP_UPDATER;
}

zone "0.0.0.0.0.0.0.0.0.0.0.0.0.9.e.e.ip6.arpa." {
    primary DOMAIN-NS;
    key DHCP_UPDATER;
}

