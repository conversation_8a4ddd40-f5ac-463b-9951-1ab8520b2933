
      
"Header-ARecord",fqdn*,,view,address*,,,,,,,,,,,,comment,disabled,ttl,EA-Site,EA-Country,ADMGRP-test,,,,,,
Header-AaaaRecord,fqdn*,,view,address*,,,,,,,,,,,,comment,disabled,ttl,EA-Site,EA-Country,,,,,,,
Header-CnameRecord,fqdn*,,view,canonical_name*,,,,,,,,,,,,comment,disabled,ttl,EA-Site,,,,,,,,
Header-DnameRecord,fqdn*,,view,target*,,,,,,,,,,,,comment,disabled,ttl,,EA-Country,,,,,,,
Header-MxRecord,fqdn*,,view,mx*,,priority*,,,,,,,,,,comment,disabled,ttl,,,ADMGRP-test,,,,,,
Header-NaptrRecord,fqdn*,,view,order*,,preference*,,flags,,services,,regexp,,replacement*,,comment,disabled,ttl,EA-Site,,,,,,,,
Header-NsRecord,fqdn*,,view,dname*,,zone_nameservers*,,,,,,,,,,,,,,,,,,,,,
Header-PtrRecord,fqdn*,,view,address*,,dname*,,,,,,,,,,comment,disabled,ttl,EA-Site,,,,,,,,
Header-TxtRecord,fqdn*,,view,text*,,,,,,,,,,,,comment,disabled,ttl,EA-Site,,,,,,,,
Header-SrvRecord,fqdn*,,view,priority*,,weight*,,port*,,target*,,,,,,comment,disabled,ttl,EA-Site,,,,,,,,
Header-HostRecord,fqdn*,,view,addresses*,ipv6_addresses,aliases,configure_for_dns,comment,disabled,ttl,,,,,,,,,,,,,,,,,EA-Site
Header-HostAddress,parent*,,,address*,mac_address,configure_for_dhcp,deny_bootp,broadcast_address,boot_file,boot_server,next_server,lease_time,pxe_lease_time,domain_name,domain_name_servers,routers,match_option,OPTION-1,OPTION-2,,,,,,,,
Header-IPv6HostAddress,parent*,,,address*,,,,,,,,,,,,,,,,,,,,,,
ARecord,a1.test.com,,default,*********,,,,,,,,,,,,A record,FALSE,7200,San Jose,USA,RW,,,,,,
ARecord,a1.test.com,a2.test.com,,*********,********0,,,,,,,,,,,A record update,,,San Jose,,,,,,,,
AaaaRecord,a1.test.com,,default,1000::10,,,,,,,,,,,,,FALSE,7200,San Jose,,,,,,,,
AaaaRecord,a1.test.com,a2.test.com,default,1000::10,1000::20,,,,,,,,,,,,TRUE,,San Jose,Canada,,,,,,,
CnameRecord,c1.test.com,,default,www.test.com,,,,,,,,,,,,,,,,,,,,,,,
CnameRecord,c1.test.com,,default,www.test.com,www.testing.com,,,,,,,,,,,,,,Santa Clara,,,,,,,,
DnameRecord,d1.test.com,,default,d1.foo.com,,,,,,,,,,,,,FALSE,28800,,,,,,,,,
MxRecord,m1.test.com,,default,mailer.foo.com,11,,,,,,,,,,,,FALSE,28800,,,,,,,,,
NaptrRecord,test.com,,default,10,,20,,U,,,,,,host1.foo.com,,,FALSE,28800,,,,,,,,,
NsRecord,test.com,,default,host1.test.com,,"['ip':'*******','auto_create_ptr':'true']",,,,,,,,,,,,,,,,,,,,,
PtrRecord,*********.in-addr.arpa,,default,*********,,ss.dd.ff,,,,,,,,,,,FALSE,28800,,,,,,,,,
TxtRecord,t1.test.com,,default,some text,,,,,,,,,,,,,FALSE,28800,Sunnyvale,,,,,,,,
SrvRecord,_http._tcp.s1.test.com,,default,10,,10,,80,,foo.test.com,,,,,,SRV record,FALSE,7200,,,,,,,,,
HostRecord,h1.test.com,,default,"['********','********']",['1001::11'],,TRUE,,,,,FALSE,,,,,,,,,,,,,,,
HostRecord,h2.test.com,,default,['***********'],,['www.foo.com'],TRUE,,,,11:12:13:14:15:16,TRUE,,,,,,,,,,,,,*********,,
HostRecord,h3.test.com,,default,"['********','********']",['1001::11'],,TRUE,,,,,TRUE,,,,,,,,,,,,,,,
HostAddress,h3.test.com,,default,********,11:11:11:11:11:11,TRUE,FALSE,,,,,12345,,,,,,***********,,,,,,,,,
HostAddress,h3.test.com,,default,********,22:11:11:11:11:22,TRUE,FALSE,,boot_file1,,,12345,,,,,,***********,,,,,,,,,
