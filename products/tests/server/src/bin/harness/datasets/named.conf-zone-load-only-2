// Inspired by examples in chapter 3 of the BIND ARM

acl "loopbacknet" { 127.0.0.0/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";			// Put pid file in working dir
	recursion no;				// Do not provide recursive service
	listen-on port 5353 { 127.0.0.1; };	// Put master on non-standard port
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

view "VIEW_NAME" {

// Root server hints
zone "." { type hint; file "root.hint"; };

zone "b0003.grainger.com" {
	type master;
	file "b0003.grainger.com-zone-load-only-2";
	notify no;
};

};
