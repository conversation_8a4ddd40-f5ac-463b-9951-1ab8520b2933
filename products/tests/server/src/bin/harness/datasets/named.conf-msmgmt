
options {
	masterfile-format text;
	zone-statistics yes;
	version none;
	hostname none;
	recursion no;
	minimal-responses yes;
	# for service restart: allow_bulkhost_ddns = Refusal
	allow-transfer { !any; };
	transfer-format many-answers;
};

# Worker threads: default

# Bulk Host Name Templates:
#	Four Octets: 		"-$1-$2-$3-$4" (Default)
#	One Octet: 		"-$4"
#	Three Octets: 		"-$2-$3-$4"
#	Two Octets: 		"-$3-$4"



controls {
	inet 127.0.0.1 port 953
	allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	 channel ib_syslog { 
		 syslog daemon; 
		 severity info; 
	};
	 category default { ib_syslog; };
};

acl all_dns_views_updater_keys {  key DHCP_UPDATER_default; };

# default
view "_default" {  # default
    match-clients { key DHCP_UPDATER_default; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    zone "0.0.127.in-addr.arpa" in {
	type master;
    };
    zone "msp.mp" in {
	type slave;
	masters { *******; };
	allow-update-forwarding { key DHCP_UPDATER_default;  none; };
	file "db.msp.mp._default";
	notify explicit;
    };
    zone "ns-ansz.mp" in {
	type slave;
	masters { *******; };
	allow-update-forwarding { key DHCP_UPDATER_default;  none; };
	file "db.ns-ansz.mp._default";
	notify explicit;
    };
    zone "ns-ansz1.mp" in {
	type slave;
	masters { *******;  };
	allow-update-forwarding { key DHCP_UPDATER_default;  none; };
	file "db.ns-ansz1.mp._default";
	notify explicit;
    };
    zone "ns-anszno_ip.mp" in {
	type slave;
	masters { ***************;  };
	allow-update-forwarding { key DHCP_UPDATER_default;  none; };
	file "db.ns-anszno_ip.mp._default";
	notify explicit;
    };
    zone "azs.ms" in {
	type master;
	also-notify { *******; *******; };
	allow-transfer { *******; *******; };
	notify yes;
    };
    zone "ns-ansz.ms" in {
	type master;
	also-notify { *******; *******; };
	allow-transfer { *******; *******; };
	notify yes;
    };
    zone "ns-ansz1.ms" in {
	type master;
	also-notify { *******; };
	allow-transfer { *******; };
	notify yes;
    };
    zone "ns-anszno_ip.ms" in {
	type master;
	also-notify { ***************; };
	allow-transfer { ***************; };
	notify yes;
    };
};
