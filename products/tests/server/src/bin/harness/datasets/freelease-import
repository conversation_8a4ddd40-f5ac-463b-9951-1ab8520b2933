# All times in this file are in UTC (GMT), not your local timezone.   This is
# not a bug, so please don't ask about it.   There is no portable way to
# store leases in the local timezone, so please don't request this as a
# feature.   If this is inconvenient or confusing to you, we sincerely
# apologize.   Seriously, though - don't ask.
# The format of this file is documented in the dhcpd.leases(5) manual page.
# This lease file was written by isc-dhcp-V3.0.1rc12

# The following data is for test purposes only, and is not meant to
# be used to actually give out an IP address to any client.
lease ********* {
  starts 1 2004/02/23 21:48:42;
  ends 6 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state free;
  hardware ethernet ff:e0:81:27:cd:41;
}
lease ********* {
  starts 1 2004/02/23 21:48:42;
  ends 6 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state free;
}
lease ********* {
  starts 1 2004/02/23 21:48:42;
  ends 6 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state backup;
  hardware ethernet ba:e0:81:27:cd:43;
}
lease ********* {
  starts 1 2004/02/23 21:48:42;
  ends 6 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state backup;
}
lease ********* {
  starts 1 2004/02/23 21:48:42;
  ends 6 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state free;
}
lease ********* {
  starts 1 2004/02/23 21:48:42;
  ends 6 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state free;
}
lease ********* {
  starts 1 2004/02/23 21:48:42;
  ends 6 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state backup;
}
lease 127.1.0.8 {
  starts 1 2004/02/23 21:48:42;
  ends 6 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state backup;
  hardware ethernet ba:e0:81:27:cd:48;
}
lease 127.1.0.9 {
  starts 1 2004/02/23 21:48:42;
  ends 6 2009/02/28 21:52:25;
  tstp 1 2004/02/23 21:52:25;
  binding state free;
  hardware ethernet ff:e0:81:27:cd:49;
}
