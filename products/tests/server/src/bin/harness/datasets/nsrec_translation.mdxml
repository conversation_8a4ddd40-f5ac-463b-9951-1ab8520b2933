<MDXML>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.bind_ns"
        POST-STRUCT-CALLBACK="gsnsgrp_nsrec_cache_objects">
    <NEW-MEMBER MEMBER-NAME="shared_record" value="false"/>
    <NEW-MEMBER MEMBER-NAME="dns_service_status" value=""/>
  </STRUCTURE-TRANSFORM>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone"
        POST-STRUCT-CALLBACK="gsnsgrp_zones_cache_objects"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.virtual_node"
        POST-STRUCT-CALLBACK="gsnsgrp_vnodes_cache_objects"/>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_grid_primary"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_cluster_secondary_server"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone_forwarding_server"
        POST-STRUCT-CALLBACK="gsnsgrp_zone_gridmembers_cache_objects"/>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.ns_group_grid_primary"
        POST-STRUCT-CALLBACK="gsnsgrp_cache_nsgroup_servers"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.ns_group_secondary_server"
        POST-STRUCT-CALLBACK="gsnsgrp_cache_nsgroup_servers"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.ns_group_ext_primary"
        POST-STRUCT-CALLBACK="gsnsgrp_cache_nsgroup_servers"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.ns_group_ext_secondary_server"
        POST-STRUCT-CALLBACK="gsnsgrp_cache_nsgroup_servers"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.delegation_ns_group_server"
        POST-STRUCT-CALLBACK="gsnsgrp_cache_nsgroup_servers"/>

  <POST-PROCESSING PROCESS-FUNCTION="gsnsgrp_nsrec_post_processing"/>

</MDXML>
