<!-- Copyright (c) 2015 Infoblox Inc. All Rights Reserved. -->
<DATABASE NAME="onedb" VERSION="4.x-asehgal-2008-04-14-11-35" MD5="5ae012ad731ab144d65f31f0c1d7df1b">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="byronizes0"/>
  <PROPERTY NAME="address" VALUE="**************"/>
  <PROPERTY NAME="comment" VALUE="recounts encrust"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="byronizes0"/>
  <PROPERTY NAME="address" VALUE="**************"/>
  <PROPERTY NAME="comment" VALUE="recounts encrust"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="babul1"/>
  <PROPERTY NAME="address" VALUE="*************"/>
  <PROPERTY NAME="comment" VALUE="maintain claim Whitfield advancement"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="issuer2"/>
  <PROPERTY NAME="address" VALUE="**************"/>
  <PROPERTY NAME="comment" VALUE="variable obese relents"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="bonus3"/>
  <PROPERTY NAME="address" VALUE="*************"/>
  <PROPERTY NAME="comment" VALUE="cobblestone roundest distempered dew"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="timeshare4"/>
  <PROPERTY NAME="address" VALUE="***************"/>
  <PROPERTY NAME="comment" VALUE="contractor stereo scarlet gilded Maseru"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="astonishingly5"/>
  <PROPERTY NAME="address" VALUE="**************"/>
  <PROPERTY NAME="comment" VALUE="electrocutes Mona"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="meddle6"/>
  <PROPERTY NAME="address" VALUE="**************"/>
  <PROPERTY NAME="comment" VALUE="rods cellular cycloidal deputies idea"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="antaeus7"/>
  <PROPERTY NAME="address" VALUE="***************"/>
  <PROPERTY NAME="comment" VALUE="herpes"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="platonism8"/>
  <PROPERTY NAME="address" VALUE="**************"/>
  <PROPERTY NAME="comment" VALUE="burgeoned clinked appear"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="packer9"/>
  <PROPERTY NAME="address" VALUE="***********"/>
  <PROPERTY NAME="comment" VALUE="general upholsterer totallers humidify hindrance"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="pranks0"/>
  <PROPERTY NAME="address" VALUE="*************"/>
  <PROPERTY NAME="comment" VALUE="chopping"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000--001"/>
  <PROPERTY NAME="name" VALUE="eagle1"/>
  <PROPERTY NAME="address" VALUE="************"/>
  <PROPERTY NAME="comment" VALUE="pronouncement actually nettle Saskatchewan"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="delusions2"/>
  <PROPERTY NAME="address" VALUE="*************"/>
  <PROPERTY NAME="comment" VALUE="hells"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-000000jjj01"/>
  <PROPERTY NAME="name" VALUE="formula3"/>
  <PROPERTY NAME="address" VALUE="**************"/>
  <PROPERTY NAME="comment" VALUE="predecessors notwithstanding safely"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="cam4"/>
  <PROPERTY NAME="address" VALUE="*************"/>
  <PROPERTY NAME="comment" VALUE="fillable assaulting argues archivist employ"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="stapling5"/>
  <PROPERTY NAME="address" VALUE="************"/>
  <PROPERTY NAME="comment" VALUE="chaos it"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="reviving6"/>
  <PROPERTY NAME="address" VALUE="57.222.71.118sdfdf"/>
  <PROPERTY NAME="comment" VALUE="Morgan smelt extemporaneous yon Hun"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="sparcstation7"/>
  <PROPERTY NAME="address" VALUE="*************"/>
  <PROPERTY NAME="comment" VALUE="securely"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="inside8"/>
  <PROPERTY NAME="address" VALUE="************"/>
  <PROPERTY NAME="comment" VALUE="poignancy"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00dfdf000001"/>
  <PROPERTY NAME="name" VALUE="riordan9"/>
  <PROPERTY NAME="address" VALUE="***************"/>
  <PROPERTY NAME="comment" VALUE="betters fight preen"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="milquetoast0"/>
  <PROPERTY NAME="address" VALUE="***************"/>
  <PROPERTY NAME="comment" VALUE="dormitories oversimplify postmark balloons kinship"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="coronets1"/>
  <PROPERTY NAME="address" VALUE="************"/>
  <PROPERTY NAME="comment" VALUE="dove Sterno resynchronized founders"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="cultural2"/>
  <PROPERTY NAME="address" VALUE="*************"/>
  <PROPERTY NAME="comment" VALUE="divorcee groundwork unclaimed underlying quails"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="striding3"/>
  <PROPERTY NAME="address" VALUE="***************"/>
  <PROPERTY NAME="comment" VALUE="Susie Pabst disregarded gleam Huntley"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="frivolous4"/>
  <PROPERTY NAME="address" VALUE="************"/>
  <PROPERTY NAME="comment" VALUE="lesson"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="anaphora5"/>
  <PROPERTY NAME="address" VALUE="************"/>
  <PROPERTY NAME="comment" VALUE="inference"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="rainier6"/>
  <PROPERTY NAME="address" VALUE="**************"/>
  <PROPERTY NAME="comment" VALUE="dominion"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="lenient7"/>
  <PROPERTY NAME="address" VALUE="***************"/>
  <PROPERTY NAME="comment" VALUE="subverted adhesion sock anxiety Bender"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="apt8"/>
  <PROPERTY NAME="address" VALUE="************"/>
  <PROPERTY NAME="comment" VALUE="Quinn admiringly prank wafer blot"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="displaces9"/>
  <PROPERTY NAME="address" VALUE="*************"/>
  <PROPERTY NAME="comment" VALUE="examines forecastle"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="e535:3f54:7730:3fe0:15f0:98e1:3901:584c"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="ted0"/>
  <PROPERTY NAME="comment" VALUE="replacer"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="4cbc:c3dc:9acd:c511:1f49:d3c2:cbbc:2dbf"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="desperation1"/>
  <PROPERTY NAME="comment" VALUE="reign seemly"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="95bb:9ea:cfb9:dcba:319e:8d1f:ddfd:81bf"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="asynchronism2"/>
  <PROPERTY NAME="comment" VALUE="hopes Ptolemy vigilance"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="a53c:7bdd:28ad:d97f:4e1d:7be4:3ad2:df2e"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="whining3"/>
  <PROPERTY NAME="comment" VALUE="update outvoted"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="ca9d:984e:e89:3d7d:ba50:8cc8:80f0:8cb7"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="satires4"/>
  <PROPERTY NAME="comment" VALUE="Gullah doubting catered brainwashed"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="8751:f4b3:9737:5ea3:1b34:491a:62bf:6062"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="restaurateur5"/>
  <PROPERTY NAME="comment" VALUE="ascetics whitened disgustedly braining dutiful"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="c813:e30:a7b7:3b4a:d40:1aff:43f:2e3c"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="rhea6"/>
  <PROPERTY NAME="comment" VALUE="ripely"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="9656:da88:cabf:3396:fa26:60d4:b0a2:6f61"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="german7"/>
  <PROPERTY NAME="comment" VALUE="taunter empires potentiating give"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="43eb:79a1:1b0c:8a0:ec61:d:8461:3e26"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="eying8"/>
  <PROPERTY NAME="comment" VALUE="select offers answers Durango"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="a4f2:ce03:8ab:5a32:1a6d:8e84:795e:69ca"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.teams-00000000"/>
  <PROPERTY NAME="name" VALUE="affirms9"/>
  <PROPERTY NAME="comment" VALUE="feeler slim girth wanderings candidates"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="9d58:d08b:9731:2284:adec:cf98:702c:29da"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="connote0"/>
  <PROPERTY NAME="comment" VALUE="anecdotes twos dreadful wildness"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="cd5e:429:28e7:921e:3dd:6c4:30f1:946b"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="clearness1"/>
  <PROPERTY NAME="comment" VALUE="blimps symbolized executor"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="6cc8:b903:bc1e:1d30:c8d7:9244:3f4a:50eb"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="southfield2"/>
  <PROPERTY NAME="comment" VALUE="dismemberment counterexample boast"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="79af:f9db:3ae2:e78f:f5fe:b41d:b69e:e193"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="bows3"/>
  <PROPERTY NAME="comment" VALUE="ambassadors buckled rare danced disallowed"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="b0c6:cb77:5123:1e82:c2e:8038:fae5:4738"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="sulphured4"/>
  <PROPERTY NAME="comment" VALUE="planetarium"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="4e90:e278:a766:9188:d969:ae85:d6f0:d56f"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="digit5"/>
  <PROPERTY NAME="comment" VALUE="relegates Erlenmeyer"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="419e:f1c0:e892:c164:f6d9:215a:fa56:78d5"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="canto6"/>
  <PROPERTY NAME="comment" VALUE="assuages waffle axiomatizes regimes"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="4df:fe4f:ffba:aded:28e0:2cbd:c108:c037"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="practical7"/>
  <PROPERTY NAME="comment" VALUE="revealed"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="d3f1:dd9a:e7a5:534a:dea1:64f7:3a32:55a8"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="snub8"/>
  <PROPERTY NAME="comment" VALUE="stucco"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="cf4d:a3e3:514b:3f0b:af23:8fad:cd4a:c690"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.respondent-00000001"/>
  <PROPERTY NAME="name" VALUE="twinkler9"/>
  <PROPERTY NAME="comment" VALUE="bustling"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="5f58:aeb:add5:1310:4307:5f40:56cb:4dd5"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="java0"/>
  <PROPERTY NAME="comment" VALUE="port chairman assailants"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="6724:45c9:f2f:6af3:1513:9f93:de0:fb87"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="brazil1"/>
  <PROPERTY NAME="comment" VALUE="anchors coexisting courtiers captive"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="e32f:b42a:6264:5020:7123:324:f953:234e"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="throng2"/>
  <PROPERTY NAME="comment" VALUE="burps Beardsley Peters insincere"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="e5ba:af9e:5535:568c:c4ab:4022:b6f7:36cc"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="amos3"/>
  <PROPERTY NAME="comment" VALUE="disarms griever braking emulations newer"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="7a21:be2:12c7:aa01:1abf:fa3:b0ad:7771"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="supervising4"/>
  <PROPERTY NAME="comment" VALUE="reviewer bowing modularly"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="7c1b:e5e3:ed31:c523:2f18:a897:9fda:7f22"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="walrus5"/>
  <PROPERTY NAME="comment" VALUE="connection kidnap"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="e034:aec1:11b3:f0f1:1d89:3863:da2d:28a"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="colonially6"/>
  <PROPERTY NAME="comment" VALUE="tribal"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="fd2c:60e4:6fd3:f7c8:393:2fc7:829e:8971"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="reflected7"/>
  <PROPERTY NAME="comment" VALUE="undergo delight"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="c2d5:14a6:2e80:404b:c1fe:3f1:92f6:c5e2"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="botanists8"/>
  <PROPERTY NAME="comment" VALUE="hour quarters"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_aaaa"/>
  <PROPERTY NAME="address" VALUE="7801:bc6:49ac:ee8f:67e2:d9d4:f21:debc"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.conrad-00000002"/>
  <PROPERTY NAME="name" VALUE="tours9"/>
  <PROPERTY NAME="comment" VALUE="jerk primate"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="true"/>
  <PROPERTY NAME="stealth" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.127.0.0"/>
  <PROPERTY NAME="name" VALUE=""/>
  <PROPERTY NAME="dname" VALUE="cluster"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ptr"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.127.0.0"/>
  <PROPERTY NAME="name" VALUE="1"/>
  <PROPERTY NAME="dname" VALUE="localhost"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_soa"/>
  <PROPERTY NAME="default_ttl" VALUE="3600"/>
  <PROPERTY NAME="expire" VALUE="604800"/>
  <PROPERTY NAME="negative_ttl" VALUE="3600"/>
  <PROPERTY NAME="refresh" VALUE="10800"/>
  <PROPERTY NAME="retry" VALUE="3600"/>
  <PROPERTY NAME="serial_number" VALUE="1"/>
  <PROPERTY NAME="use_global_email" VALUE="true"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="override_mname" VALUE="false"/>
  <PROPERTY NAME="override_serial_number" VALUE="false"/>
  <PROPERTY NAME="app_serial_number" VALUE="1"/>
  <PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.127.0.0"/>
  <PROPERTY NAME="mname" VALUE="cluster"/>
  <PROPERTY NAME="soa_email" VALUE="<EMAIL>"/>
  <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bulk_host_name_template"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="template_name" VALUE="Four Octets"/>
  <PROPERTY NAME="template_format" VALUE="-$1-$2-$3-$4"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bulk_host_name_template"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="template_name" VALUE="Three Octets"/>
  <PROPERTY NAME="template_format" VALUE="-$2-$3-$4"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bulk_host_name_template"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="template_name" VALUE="Two Octets"/>
  <PROPERTY NAME="template_format" VALUE="-$3-$4"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bulk_host_name_template"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="template_name" VALUE="One Octet"/>
  <PROPERTY NAME="template_format" VALUE="-$4"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dhcp_properties"/>
  <PROPERTY NAME="lease_time" VALUE="43200"/>
  <PROPERTY NAME="pxe_lease_time" VALUE="43200"/>
  <PROPERTY NAME="pxe_lease_time_enabled" VALUE="false"/>
  <PROPERTY NAME="facility" VALUE="daemon"/>
  <PROPERTY NAME="ddns_use_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_no_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_server_use_fqdn" VALUE="false"/>
  <PROPERTY NAME="ping_number" VALUE="1"/>
  <PROPERTY NAME="ping_timeout" VALUE="1"/>
  <PROPERTY NAME="double_confirm_network_deletion" VALUE="true"/>
  <PROPERTY NAME="recycle_leases" VALUE="true"/>
  <PROPERTY NAME="log_lease_events" VALUE="false"/>
  <PROPERTY NAME="enable_expert_mode" VALUE="false"/>
  <PROPERTY NAME="enable_dhcp_thresholds" VALUE="false"/>
  <PROPERTY NAME="range_high_water_mark" VALUE="95"/>
  <PROPERTY NAME="range_low_water_mark" VALUE="0"/>
  <PROPERTY NAME="enable_threshold_email_warnings" VALUE="false"/>
  <PROPERTY NAME="enable_threshold_snmp_warnings" VALUE="false"/>
  <PROPERTY NAME="override_threshold_email_notification" VALUE="false"/>
  <PROPERTY NAME="retry_dns_updates" VALUE="true"/>
  <PROPERTY NAME="retry_dns_updates_interval" VALUE="5"/>
  <PROPERTY NAME="rate_limit_dhcp_expirations" VALUE="true"/>
  <PROPERTY NAME="dhcpd_startup_expiration_time_limit" VALUE="5"/>
  <PROPERTY NAME="ddns_txt_record_handling" VALUE="isc"/>
  <PROPERTY NAME="ddns_ttl" VALUE="0"/>
  <PROPERTY NAME="ignore_dhcp_param_request_list" VALUE="false"/>
  <PROPERTY NAME="update_dns_on_renew" VALUE="false"/>
  <PROPERTY NAME="fixed_address_obeys_mac_filter" VALUE="false"/>
  <PROPERTY NAME="enable_leasequery" VALUE="false"/>
  <PROPERTY NAME="deny_bootp" VALUE="false"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dns_properties"/>
  <PROPERTY NAME="custom_root_server" VALUE="false"/>
  <PROPERTY NAME="default_ttl" VALUE="28800"/>
  <PROPERTY NAME="expire" VALUE="2592000"/>
  <PROPERTY NAME="negative_ttl" VALUE="900"/>
  <PROPERTY NAME="recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="refresh" VALUE="10800"/>
  <PROPERTY NAME="retry" VALUE="3600"/>
  <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_bulkhost_ddns" VALUE="0"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="facility" VALUE="daemon"/>
  <PROPERTY NAME="log_general" VALUE="true"/>
  <PROPERTY NAME="log_client" VALUE="true"/>
  <PROPERTY NAME="log_config" VALUE="true"/>
  <PROPERTY NAME="log_database" VALUE="true"/>
  <PROPERTY NAME="log_dnssec" VALUE="true"/>
  <PROPERTY NAME="log_lame_servers" VALUE="true"/>
  <PROPERTY NAME="log_network" VALUE="true"/>
  <PROPERTY NAME="log_notify" VALUE="true"/>
  <PROPERTY NAME="log_queries" VALUE="false"/>
  <PROPERTY NAME="log_resolver" VALUE="true"/>
  <PROPERTY NAME="log_security" VALUE="true"/>
  <PROPERTY NAME="log_update" VALUE="true"/>
  <PROPERTY NAME="log_xfer_in" VALUE="true"/>
  <PROPERTY NAME="log_xfer_out" VALUE="true"/>
  <PROPERTY NAME="log_update_security" VALUE="true"/>
  <PROPERTY NAME="double_confirm_zone_deletion" VALUE="true"/>
  <PROPERTY NAME="enable_secondary_notify" VALUE="true"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
  <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
  <PROPERTY NAME="max_replication_send_queue_size_for_gui_ixfr" VALUE="1000"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="record_name_policy" VALUE="Allow Underscore"/>
  <PROPERTY NAME="bulk_host_name_template" VALUE="Four Octets"/>
  <PROPERTY NAME="views_count" VALUE="0"/>
  <PROPERTY NAME="srgs_count" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_ipam_properties"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.dhcp_mac_filter_parent"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
  <PROPERTY NAME="authoritative" VALUE="0"/>
  <PROPERTY NAME="override_authoritative" VALUE="false"/>
  <PROPERTY NAME="override_boot_file" VALUE="false"/>
  <PROPERTY NAME="override_boot_server" VALUE="false"/>
  <PROPERTY NAME="override_next_server" VALUE="false"/>
  <PROPERTY NAME="override_broadcast_address" VALUE="false"/>
  <PROPERTY NAME="override_custom_options" VALUE="false"/>
  <PROPERTY NAME="override_option60_match_rules" VALUE="false"/>
  <PROPERTY NAME="override_domain_name" VALUE="false"/>
  <PROPERTY NAME="override_domain_name_servers" VALUE="false"/>
  <PROPERTY NAME="override_lease_time" VALUE="false"/>
  <PROPERTY NAME="override_pxe_lease_time" VALUE="false"/>
  <PROPERTY NAME="override_routers" VALUE="false"/>
  <PROPERTY NAME="pxe_lease_time_enabled" VALUE="false"/>
  <PROPERTY NAME="override_local_dns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="local_ddns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="override_remote_dns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="remote_ddns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="override_update_static_leases" VALUE="false"/>
  <PROPERTY NAME="update_static_leases" VALUE="false"/>
  <PROPERTY NAME="service_enabled" VALUE="false"/>
  <PROPERTY NAME="force_restart_service" VALUE="false"/>
  <PROPERTY NAME="test_restart_service" VALUE="false"/>
  <PROPERTY NAME="restart_service_status" VALUE="No-request"/>
  <PROPERTY NAME="restart_in_0" VALUE="-1"/>
  <PROPERTY NAME="restart_in_1" VALUE="-1"/>
  <PROPERTY NAME="override_cluster_facility" VALUE="false"/>
  <PROPERTY NAME="facility" VALUE="daemon"/>
  <PROPERTY NAME="override_ddns_use_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_use_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="override_ddns_no_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_no_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="override_ddns_server_use_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_server_use_fqdn" VALUE="false"/>
  <PROPERTY NAME="override_ping_number" VALUE="false"/>
  <PROPERTY NAME="ping_number" VALUE="1"/>
  <PROPERTY NAME="override_ping_timeout" VALUE="false"/>
  <PROPERTY NAME="ping_timeout" VALUE="1"/>
  <PROPERTY NAME="override_recycle_leases" VALUE="false"/>
  <PROPERTY NAME="recycle_leases" VALUE="true"/>
  <PROPERTY NAME="override_log_lease_events" VALUE="false"/>
  <PROPERTY NAME="log_lease_events" VALUE="false"/>
  <PROPERTY NAME="enable_dhcp_thresholds" VALUE="false"/>
  <PROPERTY NAME="range_high_water_mark" VALUE="95"/>
  <PROPERTY NAME="range_low_water_mark" VALUE="0"/>
  <PROPERTY NAME="enable_threshold_email_warnings" VALUE="false"/>
  <PROPERTY NAME="enable_threshold_snmp_warnings" VALUE="false"/>
  <PROPERTY NAME="override_threshold_email_notification" VALUE="false"/>
  <PROPERTY NAME="retry_dns_updates" VALUE="true"/>
  <PROPERTY NAME="retry_dns_updates_interval" VALUE="5"/>
  <PROPERTY NAME="retry_dns_updates_override" VALUE="false"/>
  <PROPERTY NAME="ddns_ttl" VALUE="0"/>
  <PROPERTY NAME="override_ignore_dhcp_param_request_list" VALUE="false"/>
  <PROPERTY NAME="ignore_dhcp_param_request_list" VALUE="false"/>
  <PROPERTY NAME="is_scheduled" VALUE="false"/>
  <PROPERTY NAME="override_deny_bootp_request" VALUE="false"/>
  <PROPERTY NAME="deny_bootp" VALUE="false"/>
  <PROPERTY NAME="override_fixed_address_obeys_mac_filter" VALUE="false"/>
  <PROPERTY NAME="fixed_address_obeys_mac_filter" VALUE="false"/>
  <PROPERTY NAME="override_enable_leasequery" VALUE="false"/>
  <PROPERTY NAME="enable_leasequery" VALUE="false"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_properties"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="limit_recursive_clients" VALUE="false"/>
  <PROPERTY NAME="numberof_recursive_clients" VALUE="1000"/>
  <PROPERTY NAME="override_cluster_sortlist" VALUE="false"/>
  <PROPERTY NAME="override_cluster_transfer_list" VALUE="false"/>
  <PROPERTY NAME="override_cluster_transfer_format" VALUE="false"/>
  <PROPERTY NAME="override_cluster_qacl" VALUE="false"/>
  <PROPERTY NAME="override_cluster_rqacl" VALUE="false"/>
  <PROPERTY NAME="override_cluster_ddns_updaters" VALUE="false"/>
  <PROPERTY NAME="override_cluster_forwarders" VALUE="false"/>
  <PROPERTY NAME="recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
  <PROPERTY NAME="service_enabled" VALUE="true"/>
  <PROPERTY NAME="force_restart_service" VALUE="false"/>
  <PROPERTY NAME="restart_service_status" VALUE="No-request"/>
  <PROPERTY NAME="restart_in_0" VALUE="-1"/>
  <PROPERTY NAME="restart_in_1" VALUE="-1"/>
  <PROPERTY NAME="minimal_response" VALUE="true"/>
  <PROPERTY NAME="override_cluster_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="override_cluster_facility" VALUE="false"/>
  <PROPERTY NAME="facility" VALUE="daemon"/>
  <PROPERTY NAME="override_cluster_logging" VALUE="false"/>
  <PROPERTY NAME="log_general" VALUE="true"/>
  <PROPERTY NAME="log_client" VALUE="true"/>
  <PROPERTY NAME="log_config" VALUE="true"/>
  <PROPERTY NAME="log_database" VALUE="true"/>
  <PROPERTY NAME="log_dnssec" VALUE="true"/>
  <PROPERTY NAME="log_lame_servers" VALUE="true"/>
  <PROPERTY NAME="log_network" VALUE="true"/>
  <PROPERTY NAME="log_notify" VALUE="true"/>
  <PROPERTY NAME="log_queries" VALUE="false"/>
  <PROPERTY NAME="log_resolver" VALUE="true"/>
  <PROPERTY NAME="log_security" VALUE="true"/>
  <PROPERTY NAME="log_update" VALUE="true"/>
  <PROPERTY NAME="log_xfer_in" VALUE="true"/>
  <PROPERTY NAME="log_xfer_out" VALUE="true"/>
  <PROPERTY NAME="log_update_security" VALUE="true"/>
  <PROPERTY NAME="auto_sort_views" VALUE="false"/>
  <PROPERTY NAME="use_nat_address" VALUE="0"/>
  <PROPERTY NAME="dns_over_mgmt" VALUE="false"/>
  <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_cluster_notify_query_sport" VALUE="false"/>
  <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
  <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="is_scheduled" VALUE="false"/>
  <PROPERTY NAME="named_worker_threads" VALUE="0"/>
  <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
  <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="gss_tsig_principal_name" VALUE="None"/>
  <PROPERTY NAME="gsstsig_key_expiration_time" VALUE="3600"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_view_nat_item"/>
  <PROPERTY NAME="glue_record_address_choice" VALUE="0"/>
  <PROPERTY NAME="attach_empty_recursive_view" VALUE="true"/>
  <PROPERTY NAME="member_dns_properties" VALUE="0"/>
  <PROPERTY NAME="view" VALUE="._default"/>
  <PROPERTY NAME="is_ipv4" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="1"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="subnet-mask"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="2"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="time-offset"/>
  <PROPERTY NAME="type" VALUE="32-bit signed integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="3"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="routers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="4"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="time-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="5"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="ien116-name-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="6"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="domain-name-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="7"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="log-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="8"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="cookie-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="9"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="lpr-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="10"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="impress-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="11"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="resource-location-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="12"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="host-name"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="13"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="boot-size"/>
  <PROPERTY NAME="type" VALUE="16-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="14"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="merit-dump"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="15"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="domain-name"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="16"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="swap-server"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="17"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="root-path"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="18"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="extensions-path"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="19"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="ip-forwarding"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="20"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="non-local-source-routing"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="21"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="policy-filter"/>
  <PROPERTY NAME="type" VALUE="array of ip-address pair"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="22"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="max-dgram-reassembly"/>
  <PROPERTY NAME="type" VALUE="16-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="23"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="default-ip-ttl"/>
  <PROPERTY NAME="type" VALUE="8-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="24"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="path-mtu-aging-timeout"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="25"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="path-mtu-plateau-table"/>
  <PROPERTY NAME="type" VALUE="array of 16-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="26"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="interface-mtu"/>
  <PROPERTY NAME="type" VALUE="16-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="27"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="all-subnets-local"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="28"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="broadcast-address"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="29"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="perform-mask-discovery"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="30"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="mask-supplier"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="31"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="router-discovery"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="32"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="router-solicitation-address"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="33"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="static-routes"/>
  <PROPERTY NAME="type" VALUE="array of ip-address pair"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="34"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="trailer-encapsulation"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="35"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="arp-cache-timeout"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="36"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="ieee802-3-encapsulation"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="37"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="default-tcp-ttl"/>
  <PROPERTY NAME="type" VALUE="8-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="38"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="tcp-keepalive-interval"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="39"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="tcp-keepalive-garbage"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="40"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="nis-domain"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="41"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="nis-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="42"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="ntp-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="43"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="vendor-encapsulated-options"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="44"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="netbios-name-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="45"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="netbios-dd-server"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="46"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="netbios-node-type"/>
  <PROPERTY NAME="type" VALUE="8-bit unsigned integer (1,2,4,8)"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="47"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="netbios-scope"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="48"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="font-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="49"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="x-display-manager"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="50"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-requested-address"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="51"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-lease-time"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="52"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-option-overload"/>
  <PROPERTY NAME="type" VALUE="8-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="53"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-message-type"/>
  <PROPERTY NAME="type" VALUE="8-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="54"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-server-identifier"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="55"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-parameter-request-list"/>
  <PROPERTY NAME="type" VALUE="array of 8-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="56"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-message"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="57"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-max-message-size"/>
  <PROPERTY NAME="type" VALUE="16-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="58"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-renewal-time"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="59"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-rebinding-time"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="60"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="vendor-class-identifier"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="61"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="dhcp-client-identifier"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="62"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="nwip-domain"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="63"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="nwip-suboptions"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="64"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="nisplus-domain"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="65"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="nisplus-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="66"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="tftp-server-name"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="67"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="bootfile-name"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="68"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="mobile-ip-home-agent"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="69"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="smtp-server"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="70"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="pop-server"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="71"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="nntp-server"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="72"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="www-server"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="73"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="finger-server"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="74"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="irc-server"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="75"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="streettalk-server"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="76"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="streettalk-directory-assistance-server"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="77"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="user-class"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="78"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="slp-directory-agent"/>
  <PROPERTY NAME="type" VALUE="boolean array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="79"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="slp-service-scope"/>
  <PROPERTY NAME="type" VALUE="boolean-text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="81"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="fqdn"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="82"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="relay-agent-information"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="85"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="nds-servers"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="86"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="nds-tree-name"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="87"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="nds-context"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="98"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="uap-servers"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="code" VALUE="118"/>
  <PROPERTY NAME="space" VALUE="DHCP..false"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="name" VALUE="subnet-selection"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_space"/>
  <PROPERTY NAME="parent" VALUE="."/>
  <PROPERTY NAME="name" VALUE="DHCP"/>
  <PROPERTY NAME="is_ipv4" VALUE="true"/>
  <PROPERTY NAME="ms_is_user_class" VALUE="false"/>
  <PROPERTY NAME="ms_server" VALUE="."/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.record_name_policy"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="policy_name" VALUE="Strict Hostname Checking"/>
  <PROPERTY NAME="policy_regex" VALUE="^[a-zA-Z0-9]$|^[a-zA-Z0-9][-a-zA-Z0-9]*[a-zA-Z0-9]$"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.record_name_policy"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="policy_name" VALUE="Allow Underscore"/>
  <PROPERTY NAME="policy_regex" VALUE="^[-a-zA-Z0-9_]+$"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.record_name_policy"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="policy_name" VALUE="Allow Any"/>
  <PROPERTY NAME="policy_regex" VALUE=".+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.shared_network_parent"/>
  <PROPERTY NAME="name" VALUE="/"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.template_parent"/>
  <PROPERTY NAME="name" VALUE="."/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.view"/>
  <PROPERTY NAME="override_grid_and_member_recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
  <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default"/>
  <PROPERTY NAME="displayname" VALUE="default"/>
  <PROPERTY NAME="network_view" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="View"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE=".."/>
  <PROPERTY NAME="name" VALUE="_default"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="SharedRecordGroup"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE=".."/>
  <PROPERTY NAME="name" VALUE="srg_root"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="Dummy"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="1"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default"/>
  <PROPERTY NAME="name" VALUE="arpa.in-addr.127.0.0"/>
  <PROPERTY NAME="revzone_address" VALUE="*********"/>
  <PROPERTY NAME="revzone_netmask" VALUE="*************"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default"/>
  <PROPERTY NAME="name" VALUE="teams-00000000"/>
  <PROPERTY NAME="member_primary" VALUE="0"/>
  <PROPERTY NAME="comment" VALUE="rooting pitying sunny mechanizing"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default"/>
  <PROPERTY NAME="name" VALUE="respondent-00000001"/>
  <PROPERTY NAME="member_primary" VALUE="0"/>
  <PROPERTY NAME="comment" VALUE="habitual slackly stressed"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default"/>
  <PROPERTY NAME="name" VALUE="conrad-00000002"/>
  <PROPERTY NAME="member_primary" VALUE="0"/>
  <PROPERTY NAME="comment" VALUE="fortified draughts informative"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="action" VALUE="access_gui"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="action" VALUE="access_papi"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.**ALL USERS**"/>
  <PROPERTY NAME="action" VALUE="access_gui"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.**ALL USERS**"/>
  <PROPERTY NAME="action" VALUE="access_papi"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="auth_type" VALUE="LOCAL"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="override_auto_detect_time_zone" VALUE="false"/>
  <PROPERTY NAME="time_zone" VALUE="(UTC) Coordinated Universal Time"/>
  <PROPERTY NAME="name" VALUE="admin"/>
  <PROPERTY NAME="password" VALUE="{ssha}_HAAAALRUbUyVKoqlMX0bNi1VkzxnlEg8i0GjQoLJKnM="/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_auth_method"/>
  <PROPERTY NAME="auth_method_type" VALUE="Local"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="remote_admin_policy" VALUE="0"/>
  <PROPERTY NAME="auth_method_name" VALUE="Local Admin"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE=""/>
  <PROPERTY NAME="name" VALUE="admin-group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE=""/>
  <PROPERTY NAME="name" VALUE="**ALL USERS**"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group_link"/>
  <PROPERTY NAME="admin" VALUE="admin"/>
  <PROPERTY NAME="admin_group" VALUE=".admin-group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_radius_properties"/>
  <PROPERTY NAME="auth_retry_period" VALUE="5"/>
  <PROPERTY NAME="auth_max_retries" VALUE="6"/>
  <PROPERTY NAME="acct_retry_period" VALUE="5"/>
  <PROPERTY NAME="acct_max_retries" VALUE="1000"/>
  <PROPERTY NAME="enabled" VALUE="false"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="group_name" VALUE="None"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster"/>
  <PROPERTY NAME="vpn_port_number" VALUE="1194"/>
  <PROPERTY NAME="enable_recycle_bin" VALUE="true"/>
  <PROPERTY NAME="audit_log_format" VALUE="0"/>
  <PROPERTY NAME="restart_option" VALUE="1"/>
  <PROPERTY NAME="member_restart_sequential_delay" VALUE="10"/>
  <PROPERTY NAME="cluster_parent" VALUE="."/>
  <PROPERTY NAME="cluster_oid" VALUE="0"/>
  <PROPERTY NAME="name" VALUE="Infoblox"/>
  <PROPERTY NAME="shared_secret" VALUE="test"/>
  <PROPERTY NAME="virtual_node_count" VALUE="0"/>
  <PROPERTY NAME="physical_node_count" VALUE="0"/>
  <PROPERTY NAME="prompt_type" VALUE="Infoblox"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_email"/>
  <PROPERTY NAME="email_notify" VALUE="false"/>
  <PROPERTY NAME="specify_email_relay" VALUE="false"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_monitor"/>
  <PROPERTY NAME="enable_syslog_server" VALUE="false"/>
  <PROPERTY NAME="enable_snmp" VALUE="false"/>
  <PROPERTY NAME="enable_snmp_get" VALUE="false"/>
  <PROPERTY NAME="audit_to_syslog" VALUE="false"/>
  <PROPERTY NAME="syslog_facility" VALUE="daemon"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_resolver"/>
  <PROPERTY NAME="resolver_enabled" VALUE="false"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_security"/>
  <PROPERTY NAME="enable_http_redirect" VALUE="false"/>
  <PROPERTY NAME="security_enabled" VALUE="false"/>
  <PROPERTY NAME="session_timeout" VALUE="600"/>
  <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
  <PROPERTY NAME="support_access_enabled" VALUE="false"/>
  <PROPERTY NAME="login_banner_enabled" VALUE="false"/>
  <PROPERTY NAME="audit_log_rolling_enabled" VALUE="true"/>
  <PROPERTY NAME="ssh_perm_disabled" VALUE="false"/>
  <PROPERTY NAME="password_min_length" VALUE="4"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_time"/>
  <PROPERTY NAME="ntp_enabled" VALUE="false"/>
  <PROPERTY NAME="time_zone" VALUE="(UTC - 5:00) Eastern Time (US and Canada)"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.grid_upgrade"/>
  <PROPERTY NAME="grid_distribute_type" VALUE="NONE"/>
  <PROPERTY NAME="grid_distribution_active" VALUE="false"/>
  <PROPERTY NAME="grid_distribution_state" VALUE="NONE"/>
  <PROPERTY NAME="grid_upgrade_type" VALUE="NONE"/>
  <PROPERTY NAME="grid_upgrade_active" VALUE="false"/>
  <PROPERTY NAME="upgrade_mode" VALUE="NON_LITE"/>
  <PROPERTY NAME="member_upgrade_timeout" VALUE="10"/>
  <PROPERTY NAME="member_revert_window" VALUE="172800"/>
  <PROPERTY NAME="grid_upgrade_oid" VALUE="0"/>
  <PROPERTY NAME="release" VALUE="NO_RELEASE"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.option_space_parent$."/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.restorable_operation_parent$."/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_tftp_properties"/>
  <PROPERTY NAME="tftp_enabled" VALUE="false"/>
  <PROPERTY NAME="tftp_port" VALUE="69"/>
  <PROPERTY NAME="http_enabled" VALUE="false"/>
  <PROPERTY NAME="http_acl_enabled" VALUE="false"/>
  <PROPERTY NAME="ftp_enabled" VALUE="false"/>
  <PROPERTY NAME="ftp_port" VALUE="21"/>
  <PROPERTY NAME="ftp_passive_enabled" VALUE="true"/>
  <PROPERTY NAME="ftp_filelist_enabled" VALUE="false"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="ftp_banner" VALUE="Restricted Access Only"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
  <PROPERTY NAME="hwtype" VALUE="IB-0000youareinchroot"/>
  <PROPERTY NAME="enable_auto_lan" VALUE="true"/>
  <PROPERTY NAME="enable_auto_ha" VALUE="true"/>
  <PROPERTY NAME="ha_port_speed" VALUE="10"/>
  <PROPERTY NAME="enable_auto_mgmt" VALUE="true"/>
  <PROPERTY NAME="mgmt_port_enabled" VALUE="false"/>
  <PROPERTY NAME="restrict_sec_access_to_mgmt_port" VALUE="false"/>
  <PROPERTY NAME="mgmt_port_vpn_enabled" VALUE="false"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="master_notification_required" VALUE="false"/>
  <PROPERTY NAME="physical_oid" VALUE="0"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="hwid" VALUE="a6f7422bf0a22e27cb474a8a4a59f33"/>
  <PROPERTY NAME="hwmodel" VALUE=""/>
  <PROPERTY NAME="public_ip_address" VALUE="***********"/>
  <PROPERTY NAME="serial_number" VALUE="0001youareinchroot"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.product_license"/>
  <PROPERTY NAME="pnode" VALUE="0"/>
  <PROPERTY NAME="license_type" VALUE="dns"/>
  <PROPERTY NAME="license_string" VALUE="EQAAAKsmItQcEWiFb6csg/+2VeQ7"/>
  <PROPERTY NAME="expiry_date" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.remote_admin_policy"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.restorable_operation_parent"/>
  <PROPERTY NAME="dummy" VALUE="."/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.scheduled_backups"/>
  <PROPERTY NAME="source" VALUE="DB"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="backup_type" VALUE="LOCAL"/>
  <PROPERTY NAME="backup_frequency" VALUE="Daily"/>
  <PROPERTY NAME="minutes_past_hour" VALUE="0"/>
  <PROPERTY NAME="hours_past_day" VALUE="3"/>
  <PROPERTY NAME="weekday" VALUE="Saturday"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.system_state"/>
  <PROPERTY NAME="publish_changes" VALUE="false"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.tftp_storage"/>
  <PROPERTY NAME="storage_limit" VALUE="500"/>
  <PROPERTY NAME="backup_storage" VALUE="true"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.upgrade_group"/>
  <PROPERTY NAME="mem_dist_policy" VALUE="SIMULTANEOUSLY"/>
  <PROPERTY NAME="dist_time" VALUE="0"/>
  <PROPERTY NAME="mem_upgd_policy" VALUE="SEQUENTIALLY"/>
  <PROPERTY NAME="upgd_time" VALUE="0"/>
  <PROPERTY NAME="name" VALUE="Grid Master"/>
  <PROPERTY NAME="comment" VALUE="Grid Master Group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.upgrade_group"/>
  <PROPERTY NAME="mem_dist_policy" VALUE="SIMULTANEOUSLY"/>
  <PROPERTY NAME="dist_time" VALUE="0"/>
  <PROPERTY NAME="mem_upgd_policy" VALUE="SIMULTANEOUSLY"/>
  <PROPERTY NAME="upgd_time" VALUE="0"/>
  <PROPERTY NAME="name" VALUE="Default"/>
  <PROPERTY NAME="comment" VALUE="Default Upgrade Group "/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
  <PROPERTY NAME="subnet_mask" VALUE="*************"/>
  <PROPERTY NAME="gateway" VALUE="192.168.1.1"/>
  <PROPERTY NAME="is_master" VALUE="true"/>
  <PROPERTY NAME="ha_enabled" VALUE="0"/>
  <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
  <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
  <PROPERTY NAME="override_support_access" VALUE="false"/>
  <PROPERTY NAME="support_access_enabled" VALUE="false"/>
  <PROPERTY NAME="is_potential_master" VALUE="true"/>
  <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
  <PROPERTY NAME="nat_enabled" VALUE="false"/>
  <PROPERTY NAME="upgrade_position" VALUE="0"/>
  <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
  <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="virtual_oid" VALUE="0"/>
  <PROPERTY NAME="virtual_ip" VALUE="***********"/>
  <PROPERTY NAME="host_name" VALUE="infoblox.localdomain"/>
  <PROPERTY NAME="active_position" VALUE="0"/>
  <PROPERTY NAME="upgrade_group" VALUE="Grid Master"/>
  <PROPERTY NAME="member_type" VALUE="INFOBLOX"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node_parent"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.vnode_time"/>
  <PROPERTY NAME="ntp_service_enabled" VALUE="false"/>
  <PROPERTY NAME="enable_authentication" VALUE="false"/>
  <PROPERTY NAME="override_cluster_ntp_key" VALUE="false"/>
  <PROPERTY NAME="enable_member_ntp_query" VALUE="false"/>
  <PROPERTY NAME="override_cluster_ntp_acl" VALUE="false"/>
  <PROPERTY NAME="override_timezone" VALUE="false"/>
  <PROPERTY NAME="time_zone" VALUE="(UTC - 5:00) Eastern Time (US and Canada)"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.webui_host"/>
  <PROPERTY NAME="webui_state" VALUE="UNINITALIZED"/>
  <PROPERTY NAME="ui_memory" VALUE="256"/>
  <PROPERTY NAME="member" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.webui_host_grid"/>
  <PROPERTY NAME="http_enabled" VALUE="true"/>
  <PROPERTY NAME="http_port" VALUE="444"/>
  <PROPERTY NAME="ftp_port" VALUE="26"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="ftp_enabled" VALUE="false"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.wizard_data_record"/>
  <PROPERTY NAME="value" VALUE="1"/>
  <PROPERTY NAME="name" VALUE="appliance_flag"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.aaa_parent"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
  <PROPERTY NAME="parent" VALUE="/"/>
  <PROPERTY NAME="name" VALUE="default"/>
  <PROPERTY NAME="id" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view_parent"/>
  <PROPERTY NAME="name" VALUE="/"/>
</OBJECT>
</DATABASE>
