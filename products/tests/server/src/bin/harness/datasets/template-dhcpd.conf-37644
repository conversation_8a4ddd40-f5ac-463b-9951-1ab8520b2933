ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/storage/tmp/dhcp_updater.key";

subnet ********* netmask *********** {
	option domain-name "abc.com";
	host ********** {
		dynamic;
		hardware ethernet 11:22:33:44:55:61;
		fixed-address **********;
		ddns-updates on;
		update-static-leases true;
	}
}

subnet ******** netmask *********** {
	option domain-name "test.com";
	pool {
		infoblox-range ********* **********;
		range ********* **********;
		ddns-updates on;
		ddns-domainname = config-option domain-name;
	}
}

subnet *********** netmask ************* {
	not authoritative;
}
zone "test.com." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
zone "abc.com." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}

#End of dhcpd.conf file
