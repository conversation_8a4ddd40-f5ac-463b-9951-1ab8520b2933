ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/tmp/dhcp_updater.key";

subnet ******** netmask *********** {
	pool {
		infoblox-range ********* *********;
		range ********* *********;
		ddns-updates on;
		ddns-domainname "my.test.com";
		ddns-hostname = pick ( option fqdn.hostname,option host-name,
			concat ("dhcp-",binary-to-ascii(10,8,"-", leased-address)));
		option host-name = config-option server.ddns-hostname;
	}
}

subnet *********** netmask ************* {
	not authoritative;
}

#End of dhcpd.conf file

