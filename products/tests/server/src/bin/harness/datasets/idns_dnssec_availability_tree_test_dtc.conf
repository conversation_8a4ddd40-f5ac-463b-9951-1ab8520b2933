{
  'servers': [
    {
      'id': '********************************',
      'disabled': False,
      'inuse': True,
      'records': {
        'A': [{
          'rdata': '3.3.3.1'
        }]
      },
      'timestamp': 1524595169951597,
      'name': 'server3'
    },
    {
      'id': '********************************',
      'disabled': False,
      'inuse': True,
      'records': {
        'A': [{
          'rdata': '2.2.2.1'
        }]
      },
      'timestamp': 1524595169951597,
      'name': 'server2'
    },
    {
      'id': '10000000000000000000000000004101',
      'disabled': False,
      'inuse': True,
      'records': {
        'A': [{
          'rdata': '1.1.1.101'
        }]
      },
      'timestamp': 1524595169951597,
      'name': 'server-101'
    },
    {
      'id': '********************************',
      'disabled': False,
      'inuse': True,
      'records': {
        'A': [{
          'rdata': '1.1.1.1'
        }]
      },
      'timestamp': 1524595169951597,
      'name': 'server1'
    },
    {
      'id': '********************************',
      'disabled': False,
      'inuse': True,
      'records': {
        'CNAME': [{
          'rdata': 'server_cname.com'
        }]
      },
      'timestamp': 1524595169951597,
      'name': 'server_cname'
    }
  ],
  'config': {
    'worker_thread_count': 8,
    'health_status_filename': '/infoblox/var/idns_conf/health_status.txt',
    'log_facility': 29,
    'prefer_client_subnet': False,
    'dtc_dnssec_mode': 'SIGNED',
    'report_gen_interval': 600,
    'dns_responses': 'DNS_RESPONSE_IF_NO_DTC',
    'persistent_cache': 32,
    'log_idns_gslb': False,
    'maxmind': {
      'EA': {
        'active_file': '/storage/maxmind/ea/active.mmdb',
        'custom_file': '/storage/maxmind/ea/deployed.mmdb',
        'default_file': '/storage/maxmind/ea/default.mmdb'
      },
      'GEOIP': {
        'active_file': '/storage/maxmind/geoip/active.mmdb',
        'custom_file': '/storage/maxmind/geoip/deployed.mmdb',
        'default_file': '/storage/maxmind/geoip/default.mmdb'
      }
    },
    'status_gen_interval': 60,
    'cache_filename': '/tmp/idns/idnsd_cache'
  },
  'pools': [
    {
      'use_ttl': False,
      'quorum': 0,
      'id': '23232323232323232323232323232323',
      'disabled': False,
      'inuse': True,
      'preferred_topology': '51515151515151515151515151515151',
      'alternate_method': 'RATIO',
      'preferred_method': 'TOPOLOGY',
      'name': 'pool3',
      'availability': 'ANY',
      'pool_servers': [{
        'server': '********************************',
        'ratio': 5,
        'position': 0
      }, {
        'server': '********************************',
        'ratio': 3,
        'position': 1
      }]
    },
    {
      'use_ttl': False,
      'quorum': 0,
      'id': '25252525252525252525252525252525',
      'disabled': False,
      'inuse': True,
      'alternate_method': 'RATIO',
      'preferred_method': 'GLOBAL_AVAILABILITY',
      'name': 'pool_cname',
      'availability': 'ANY',
      'pool_servers': [{
        'server': '********************************',
        'ratio': 1,
        'position': 0
      }]
    },
    {
      'use_ttl': False,
      'quorum': 0,
      'id': '24242424242424242424242424242424',
      'disabled': False,
      'inuse': True,
      'alternate_topology': '52525252525252525252525252525252',
      'alternate_method': 'TOPOLOGY',
      'preferred_method': 'RATIO',
      'name': 'pool4',
      'availability': 'ANY',
      'pool_servers': [{
        'server': '********************************',
        'ratio': 7,
        'position': 0
      }, {
        'server': '********************************',
        'ratio': 1,
        'position': 1
      }]
    },
    {
      'use_ttl': False,
      'quorum': 0,
      'id': '21212121212121212121212121212121',
      'disabled': False,
      'inuse': True,
      'alternate_method': 'RATIO',
      'preferred_method': 'GLOBAL_AVAILABILITY',
      'name': 'pool1',
      'availability': 'ANY',
      'pool_servers': [{
        'server': '********************************',
        'ratio': 1,
        'position': 0
      }]
    },
    {
      'use_ttl': False,
      'quorum': 0,
      'id': '10000000000000000000000000002101',
      'disabled': False,
      'inuse': True,
      'pool_monitors': [{
        'id': '10000000000000000000000000003101',
        'type': 'HTTP',
        'name': 'monitor-101'
      }],
      'alternate_method': 'RATIO',
      'preferred_method': 'GLOBAL_AVAILABILITY',
      'name': 'pool-101',
      'availability': 'ANY',
      'pool_servers': [{
        'server': '10000000000000000000000000004101',
        'ratio': 1,
        'position': 0
      }]
    },
    {
      'use_ttl': False,
      'quorum': 0,
      'id': '22222222222222222222222222222222',
      'disabled': False,
      'inuse': True,
      'alternate_method': 'RATIO',
      'preferred_method': 'GLOBAL_AVAILABILITY',
      'name': 'pool2',
      'availability': 'ANY',
      'pool_servers': [{
        'server': '********************************',
        'ratio': 1,
        'position': 0
      }, {
        'server': '********************************',
        'ratio': 1,
        'position': 1
      }]
    },
    {
      'use_ttl': False,
      'quorum': 0,
      'id': '10000000000000000000000000002102',
      'disabled': False,
      'inuse': True,
      'pool_monitors': [{
        'id': '10000000000000000000000000003102',
        'type': 'ICMP',
        'name': 'monitor-102'
      }],
      'alternate_method': 'RATIO',
      'preferred_method': 'GLOBAL_AVAILABILITY',
      'name': 'pool-102',
      'availability': 'ANY',
      'pool_servers': [{
        'server': '10000000000000000000000000004101',
        'ratio': 1,
        'position': 0
      }]
    }
  ],
  'lbdns': [
    {
      'use_ttl': False,
      'id': '********************************',
      'disabled': False,
      'inuse': True,
      'persistence': 0,
      'lb_method': 'GLOBAL_AVAILABILITY',
      'types': 'A AAAA',
      'timestamp': 1524595169951597,
      'priority': 1,
      'name': 'lbdn1',
      'lbdn_pools': [{
        'ratio': 1,
        'position': 0,
        'pool': '21212121212121212121212121212121'
      }]
    },
    {
      'use_ttl': False,
      'id': '********************************',
      'disabled': False,
      'inuse': True,
      'persistence': 0,
      'lb_method': 'TOPOLOGY',
      'types': 'A AAAA',
      'timestamp': 1524595169951597,
      'priority': 1,
      'topology': '41414141414141414141414141414141',
      'name': 'lbdn3'
    },
    {
      'use_ttl': False,
      'id': '********************************',
      'disabled': False,
      'inuse': True,
      'persistence': 0,
      'lb_method': 'GLOBAL_AVAILABILITY',
      'types': 'A AAAA',
      'timestamp': 1524595169951597,
      'priority': 1,
      'name': 'lbdn_cname',
      'lbdn_pools': [{
        'ratio': 1,
        'position': 0,
        'pool': '25252525252525252525252525252525'
      }]
    },
    {
      'use_ttl': False,
      'id': '********************************',
      'disabled': False,
      'inuse': True,
      'persistence': 0,
      'lb_method': 'GLOBAL_AVAILABILITY',
      'types': 'A AAAA',
      'timestamp': 1524595169951597,
      'priority': 1,
      'name': 'lbdn5',
      'lbdn_pools': [{
        'ratio': 6,
        'position': 0,
        'pool': '21212121212121212121212121212121'
      }, {
        'ratio': 3,
        'position': 1,
        'pool': '24242424242424242424242424242424'
      }]
    },
    {
      'use_ttl': False,
      'id': '********************************',
      'disabled': False,
      'inuse': True,
      'persistence': 0,
      'lb_method': 'GLOBAL_AVAILABILITY',
      'types': 'A AAAA',
      'timestamp': 1524595169951597,
      'priority': 1,
      'name': 'lbdn2',
      'lbdn_pools': [{
        'ratio': 1,
        'position': 0,
        'pool': '21212121212121212121212121212121'
      }, {
        'ratio': 1,
        'position': 1,
        'pool': '22222222222222222222222222222222'
      }]
    },
    {
      'use_ttl': False,
      'id': '10000000000000000000000000000101',
      'disabled': False,
      'inuse': True,
      'persistence': 0,
      'lb_method': 'GLOBAL_AVAILABILITY',
      'types': 'A AAAA',
      'timestamp': 1524595169951597,
      'priority': 1,
      'name': 'lbdn-101',
      'lbdn_pools': [{
        'ratio': 1,
        'position': 0,
        'pool': '10000000000000000000000000002101'
      }, {
        'ratio': 1,
        'position': 1,
        'pool': '10000000000000000000000000002102'
      }]
    },
    {
      'use_ttl': False,
      'id': '********************************',
      'disabled': False,
      'inuse': True,
      'persistence': 0,
      'lb_method': 'RATIO',
      'types': 'A AAAA',
      'timestamp': 1524595169951597,
      'priority': 1,
      'name': 'lbdn4',
      'lbdn_pools': [{
        'ratio': 1,
        'position': 0,
        'pool': '23232323232323232323232323232323'
      }]
    }
  ],
  'topologies': [{
    'id': '51515151515151515151515151515151',
    'name': 'pool_topo1',
    'rules': [{
      'dest_link': '********************************',
      'dest_type': 'SERVER',
      'return_type': 'REGULAR',
      'position': 0,
      'sources': [{
        'source_op': 'IS_NOT',
        'source_type': 'SUBNET',
        'source_value': '2.0.2.0/24'
      }]
    }]
  }, {
    'id': '41414141414141414141414141414141',
    'name': 'lbdn_topo1',
    'rules': [{
      'dest_link': '21212121212121212121212121212121',
      'dest_type': 'POOL',
      'return_type': 'REGULAR',
      'position': 0,
      'sources': [{
        'source_op': 'IS_NOT',
        'source_type': 'SUBNET',
        'source_value': '2.0.1.0/24'
      }]
    }]
  }, {
    'id': '52525252525252525252525252525252',
    'name': 'pool_topo2',
    'rules': [{
      'dest_link': '********************************',
      'dest_type': 'SERVER',
      'return_type': 'REGULAR',
      'position': 0,
      'sources': [{
        'source_op': 'IS_NOT',
        'source_type': 'SUBNET',
        'source_value': '2.0.3.0/24'
      }]
    }, {
      'dest_link': '********************************',
      'dest_type': 'SERVER',
      'return_type': 'REGULAR',
      'position': 1,
      'sources': [{
        'source_op': 'IS_NOT',
        'source_type': 'SUBNET',
        'source_value': '2.0.4.0/24'
      }]
    }]
  }]
}
