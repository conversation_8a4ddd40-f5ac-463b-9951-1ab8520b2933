options {
  directory "DIRECTORY";
  pid-file "secondary-named.pid";
  recursion no;
  listen-on port 53 { 127.0.0.1; };
  dnssec-enable DNSSEC-ENABLE;
  dnssec-validation DNSSEC-VALIDATION;
  dnssec-accept-expired DNSSEC-ACCEPT-EXPIRED;
};
key "rndc-key" {
  algorithm hmac-md5;
  secret "FgLJuuSCc5sD9ewBRJfOUw==";
};
controls {
  inet 127.0.0.1 port 953
    allow { 127.0.0.1; } keys { "rndc-key"; };
};
# This key has no effect, and we include it to prove that point.
trusted-keys {
  "secure.com." 257 3 5 "AwEAAct2d03ch9cHb69rpYUr1Pm/kjPgt9X0RSGHTotCVylBSBNqV4go GFuG9t3JngevmtBBYVijeIwtK0KXR2Qa5uc=";
};
zone "secure.com" {
  type slave;
  notify no;
  file "db-secondary.secure.com.signedAUTHSUFFIX";
  masters port 5353 { 127.0.0.1; };
};
zone "recurs.secure.com" {
  type master;
  notify no;
  file "db-secondary.recurs.secure.com.signedRECURSSUFFIX";
};
