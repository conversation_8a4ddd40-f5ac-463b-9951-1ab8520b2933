<MDXML>
  <NEW-TYPES>
    <TYPE NAME=".com.infoblox.discovery.discovery_scan_interface"/>
  </NEW-TYPES>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.network_view"
                       POST-STRUCT-CALLBACK="nivrf_save_network_view_cache_keys"/>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.discovery.member_discovery_properties"
                       POST-STRUCT-CALLBACK="nivrf_member_discovery_properties_post_struct_callback">
    <REMOVE-MEMBER MEMBER-NAME="network_view"/>
    <REMOVE-MEMBER MEMBER-NAME="interface"/>
  </STRUCTURE-TRANSFORM>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.discovery.seed_router"
                       POST-STRUCT-CALLBACK="nivrf_save_seed_router_cache_objects">
    <NEW-MEMBER MEMBER-NAME="network_view" VALUE="."/>
  </STRUCTURE-TRANSFORM>

  <POST-PROCESSING PROCESS-FUNCTION="nivrf_member_discovery_properties_post_processing_callback"/>
</MDXML>
