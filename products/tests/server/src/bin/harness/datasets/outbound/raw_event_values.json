[{"event_type": "DNS_RPZ", "timestamp": {"time": 100000001}, "rpz_policy_disabled": false, "rpz_cef_log_enabled": true, "rpz_policy": "PASSTHRU", "rpz_type": "QNAME", "rpz_severity": "CRITICAL", "destination_ip": {"addr": "********", "kind": "IPv4"}, "source_ip": {"addr": "1234::5678:1", "kind": "IPv6"}, "source_port": 8080, "query_type": 9999, "query_name": "query.test", "query_view_name": "dns.view.test", "rule_name": "rule.test"}, {"event_type": "DHCP_LEASE", "timestamp": {"time": 1000000002}, "binding_state": "ACTIVE", "new_binding": true, "protocol": "IPv4", "address": {"addr": "********", "kind": "IPv4"}, "client_hostname": "host.name.test", "network_view_id": "_default", "network_view": "network_view", "network": {"addr": "********", "kind": "IPv4", "cidr": 8}, "range_start_addr": {"addr": "10.0.0.0", "kind": "IPv4"}, "range_end_addr": {"addr": "**************", "kind": "IPv4"}, "hardware": "00:00:00:00:00:00", "ipv6_duid": null, "ipv4_uid": "11:11:11:11", "ipv6_prefix_bits": null, "starts": {"time": 123}, "ends": {"time": 321}, "fingerprint_os_number": "fingerprint_os_number", "fingerprint": "fingerprint"}, {"event_type": "ANALYTICS_DNS_TUNNEL", "timestamp": {"time": 1000000003}, "source_ip": {"addr": "2000::1", "kind": "IPv6"}, "domain_name": "domain_name", "rpz_policy": "NODATA", "comment": "DNS Tunnelling Analytics"}, {"event_type": "DNS_RPZ", "timestamp": {"time": 100000001}, "rpz_policy_disabled": false, "rpz_cef_log_enabled": true, "rpz_policy": "PASSTHRU", "rpz_type": "QNAME", "rpz_severity": "CRITICAL", "destination_ip": {"addr": "********", "kind": "IPv4"}, "source_ip": {"addr": "********", "kind": "IPv4"}, "source_port": 8080, "query_type": 9999, "query_name": "query.test", "query_view_name": "dns.view.test", "rule_name": "rule.test"}, {"event_type": "DHCP_LEASE", "timestamp": {"time": 1000000002}, "binding_state": "ACTIVE", "new_binding": true, "protocol": "IPv4", "address": {"addr": "********", "kind": "IPv4"}, "client_hostname": "host.name.test", "network_view_id": "0", "network_view": "network_view", "network": {"addr": "********", "kind": "IPv4", "cidr": 8}, "range_start_addr": {"addr": "10.0.0.0", "kind": "IPv4"}, "range_end_addr": {"addr": "**************", "kind": "IPv4"}, "hardware": "00:00:00:00:00:00", "ipv6_duid": null, "ipv4_uid": null, "ipv6_prefix_bits": null, "starts": {"time": 123}, "ends": {"time": 321}, "fingerprint_os_number": null, "fingerprint": null}, {"event_type": "SECURITY_ADP", "timestamp": {"time": 1000000005}, "rule_sid": "55", "rule_severity": "WARNING", "rule_action": "ALERT", "source_ip": {"addr": "2000::1", "kind": "IPv6"}, "source_port": 8080, "is_nat_client": false, "nat_first_port": 5555, "nat_last_port": 6666, "hits_count": 99999, "rule_name": "adp.rule.name", "rule_category": "adp.rule.category", "query_fqdn": "*.domain.com"}, {"event_type": "DB_CHANGE_DNS_DISCOVERY_DATA", "timestamp": {"time": 1000000009}, "object_type": "DiscoveryData", "operation_type": "INSERT", "values": {"unmanaged": true, "discoverer": "test", "ip_address": "********", "is_ipv4": true, "network": "10.0.0.0/24", "mac_address": "00:0a:95:9d:68:16", "duid": null}, "previous_values": {}}]