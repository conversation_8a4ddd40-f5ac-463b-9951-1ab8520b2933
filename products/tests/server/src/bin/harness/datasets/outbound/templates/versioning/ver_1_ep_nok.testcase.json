{"name": "ver_1_ep_nok", "success": false, "exc": "SCHEMA", "errormessage": ["Outbound template: Template name: ver_1_ep_nok", "Outbound template: Schema validation error", "Outbound template: u'vendor_identifier' is a required property", "Outbound template:", "Outbound template: Failed validating u'required' in schema:", "Outbound template:     {u'$schema': u'http://json-schema.org/draft-04/schema#',", "Outbound template:      u'additionalProperties': False,", "Outbound template:      u'definitions': {u'__main__.Parameter': {u'additionalProperties': False,", "Outbound template:                                               u'properties': {u'name': {u'type': u'string'},", "Outbound template:                                                               u'type': {u'enum': [u'STRING',", "Outbound template:                                                                                   u'INT',", "Outbound template:                                                                                   u'BOOL'],", "Outbound template:                                                                         u'type': u'string'},", "Outbound template:                                                               u'value': {u'type': u'string'}},", "Outbound template:                                               u'required': [u'name',", "Outbound template:                                                             u'type'],", "Outbound template:                                               u'type': u'object'}},", "Outbound template:      u'properties': {u'comment': {u'maxLength': 256, u'type': u'string'},", "Outbound template:                      u'endpoint_variables': {u'items': {u'$ref': u'#/definitions/__main__.Parameter'},", "Outbound template:                                              u'type': u'array'},", "Outbound template:                      u'keepalive': {u'default': False,", "Outbound template:                                     u'type': u'boolean'},", "Outbound template:                      u'keepalive_timeout': {u'exclusiveMinimum': True,", "Outbound template:                                             u'maximum': 300,", "Outbound template:                                             u'minimum': 0,", "Outbound template:                                             u'type': u'number'},", "Outbound template:                      u'name': {u'maxLength': 256, u'type': u'string'},", "Outbound template:                      u'override_path': {u'type': u'boolean'},", "Outbound template:                      u'path': {u'type': u'string'},", "Outbound template:                      u'rate_limit': {u'default': 0,", "Outbound template:                                      u'minimum': 0,", "Outbound template:                                      u'type': u'number'},", "Outbound template:                      u'retry': {u'default': 2,", "Outbound template:                                 u'minimum': 0,", "Outbound template:                                 u'type': u'number'},", "Outbound template:                      u'retry_template': {u'default': 0,", "Outbound template:                                          u'minimum': 0,", "Outbound template:                                          u'type': u'number'},", "Outbound template:                      u'timeout': {u'exclusiveMinimum': True,", "Outbound template:                                   u'maximum': 3600,", "Outbound template:                                   u'minimum': 0,", "Outbound template:                                   u'type': u'number'},", "Outbound template:                      u'type': {u'enum': [u'REST_ENDPOINT'],", "Outbound template:                                u'type': u'string'},", "Outbound template:                      u'vendor_identifier': {u'type': u'string'},", "Outbound template:                      u'version': {u'enum': [u'1.0'], u'type': u'string'}},", "Outbound template:      u'required': [u'version', u'type', u'name', u'vendor_identifier'],", "Outbound template:      u'type': u'object'}", "Outbound template:", "Outbound template: On instance:", "Outbound template:     {u'name': u'ver_1_ep_nok',", "Outbound template:      u'type': u'REST_ENDPOINT',", "Outbound template:      u'version': u'1.0'}"]}