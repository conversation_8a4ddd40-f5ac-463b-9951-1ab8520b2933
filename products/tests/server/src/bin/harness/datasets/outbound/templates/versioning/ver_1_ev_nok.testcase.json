{"name": "ver_1_ev_nok", "success": false, "exc": "SCHEMA", "errormessage": ["Outbound template: Template name: ver_1_ev_nok", "Outbound template: Schema validation error", "Outbound template: u'event_type' is a required property", "Outbound template:", "Outbound template: Failed validating u'required' in schema:", "Outbound template:     {u'$schema': u'http://json-schema.org/draft-04/schema#',", "Outbound template:      u'additionalProperties': False,", "Outbound template:      u'definitions': {u'__main__.Condition': {u'additionalProperties': False,", "Outbound template:                                               u'anyOf': [{u'required': [u'stop'],", "Outbound template:                                                           u'type': u'object'},", "Outbound template:                                                          {u'required': [u'next'],", "Outbound template:                                                           u'type': u'object'},", "Outbound template:                                                          {u'required': [u'error'],", "Outbound template:                                                           u'type': u'object'},", "Outbound template:                                                          {u'required': [u'else_eval'],", "Outbound template:                                                           u'type': u'object'},", "Outbound template:                                                          {u'required': [u'eval'],", "Outbound template:                                                           u'type': u'object'}],", "Outbound template:                                               u'not': {u'anyOf': [{u'required': [u'stop',", "Outbound template:                                                                                  u'next'],", "Outbound template:                                                                    u'type': u'object'},", "Outbound template:                                                                   {u'required': [u'error',", "Outbound template:                                                                                  u'next'],", "Outbound template:                                                                    u'type': u'object'},", "Outbound template:                                                                   {u'required': [u'error',", "Outbound template:                                                                                  u'stop'],", "Outbound template:                                                                    u'type': u'object'}]},", "Outbound template:                                               u'properties': {u'condition_type': {u'enum': [u'AND',", "Outbound template:                                                                                             u'OR',", "Outbound template:                                                                                             u'NAND',", "Outbound template:                                                                                             u'NOR'],", "Outbound template:                                                                                   u'type': u'string'},", "Outbound template:                                                               u'else_eval': {u'type': u'string'},", "Outbound template:                                                               u'error': {u'type': u'boolean'},", "Outbound template:                                                               u'eval': {u'type': u'string'},", "Outbound template:                                                               u'next': {u'type': u'string'},", "Outbound template:                                                               u'statements': {u'items': {u'$ref': u'#/definitions/__main__.Statement'},", "Outbound template:                                                                               u'type': u'array'},", "Outbound template:                                                               u'stop': {u'type': u'boolean'}},", "Outbound template:                                               u'required': [u'condition_type',", "Outbound template:                                                             u'statements'],", "Outbound template:                                               u'type': u'object'},", "Outbound template:                       u'__main__.Parameter': {u'additionalProperties': False,", "Outbound template:                                               u'properties': {u'name': {u'type': u'string'},", "Outbound template:                                                               u'value': {u'type': u'string'}},", "Outbound template:                                               u'required': [u'name',", "Outbound template:                                                             u'value'],", "Outbound template:                                               u'type': u'object'},", "Outbound template:                       u'__main__.Result': {u'additionalProperties': False,", "Outbound template:                                            u'anyOf': [{u'required': [u'stop'],", "Outbound template:                                                        u'type': u'object'},", "Outbound template:                                                       {u'required': [u'error'],", "Outbound template:                                                        u'type': u'object'},", "Outbound template:                                                       {u'required': [u'next'],", "Outbound template:                                                        u'type': u'object'}],", "Outbound template:                                            u'not': {u'anyOf': [{u'required': [u'stop',", "Outbound template:                                                                               u'next'],", "Outbound template:                                                                 u'type': u'object'},", "Outbound template:                                                                {u'required': [u'error',", "Outbound template:                                                                               u'next'],", "Outbound template:                                                                 u'type': u'object'},", "Outbound template:                                                                {u'required': [u'error',", "Outbound template:                                                                               u'stop'],", "Outbound template:                                                                 u'type': u'object'}]},", "Outbound template:                                            u'properties': {u'codes': {u'type': u'string'},", "Outbound template:                                                            u'error': {u'type': u'boolean'},", "Outbound template:                                                            u'next': {u'type': u'string'},", "Outbound template:                                                            u'regex': {u'type': u'string'},", "Outbound template:                                                            u'stop': {u'type': u'boolean'}},", "Outbound template:                                            u'type': u'object'},", "Outbound template:                       u'__main__.Statement': {u'additionalProperties': False,", "Outbound template:                                               u'properties': {u'left': {u'type': u'string'},", "Outbound template:                                                               u'op': {u'enum': [u'==',", "Outbound template:                                                                                 u'!=',", "Outbound template:                                                                                 u'>',", "Outbound template:                                                                                 u'<',", "Outbound template:                                                                                 u'>=',", "Outbound template:                                                                                 u'<=',", "Outbound template:                                                                                 u'=~',", "Outbound template:                                                                                 u'!~'],", "Outbound template:                                                                       u'type': u'string'},", "Outbound template:                                                               u'right': {u'type': u'string'}},", "Outbound template:                                               u'required': [u'left',", "Outbound template:                                                             u'op',", "Outbound template:                                                             u'right'],", "Outbound template:                                               u'type': u'object'},", "Outbound template:                       u'__main__.Step': {u'additionalProperties': False,", "Outbound template:                                          u'dependencies': {u'body': {u'properties': {u'operation': {u'enum': [u'POST',", "Outbound template:                                                                                                               u'PUT',", "Outbound template:                                                                                                               u'NOP'],", "Outbound template:                                                                                                     u'type': u'string'}}},", "Outbound template:                                                            u'body_list': {u'properties': {u'operation': {u'enum': [u'POST',", "Outbound template:                                                                                                                    u'PUT',", "Outbound template:                                                                                                                    u'NOP'],", "Outbound template:                                                                                                          u'type': u'string'}}},", "Outbound template:                                                            u'condition': {u'properties': {u'operation': {u'enum': [u'CONDITION'],", "Outbound template:                                                                                                          u'type': u'string'}}},", "Outbound template:                                                            u'condition_type': {u'properties': {u'operation': {u'enum': [u'CONDITION'],", "Outbound template:                                                                                                               u'type': u'string'}}},", "Outbound template:                                                            u'headers': {u'properties': {u'operation': {u'enum': [u'GET',", "Outbound template:                                                                                                                  u'POST',", "Outbound template:                                                                                                                  u'PUT',", "Outbound template:                                                                                                                  u'DELETE'],", "Outbound template:                                                                                                        u'type': u'string'}}},", "Outbound template:                                                            u'override_headers': {u'properties': {u'operation': {u'enum': [u'GET',", "Outbound template:                                                                                                                           u'POST',", "Outbound template:                                                                                                                           u'PUT',", "Outbound template:                                                                                                                           u'DELETE'],", "Outbound template:                                                                                                                 u'type': u'string'}},", "Outbound template:                                                                                  u'required': [u'headers']},", "Outbound template:                                                            u'parameters': {u'properties': {u'operation': {u'enum': [u'GET',", "Outbound template:                                                                                                                     u'POST',", "Outbound template:                                                                                                                     u'PUT',", "Outbound template:                                                                                                                     u'DELETE'],", "Outbound template:                                                                                                           u'type': u'string'}}},", "Outbound template:                                                            u'parse_regex': {u'properties': {u'parse': {u'enum': [u'REGEX',", "Outbound template:                                                                                                                  u'REGEXLINE',", "Outbound template:                                                                                                                  u'REGEXMULTILINE'],", "Outbound template:                                                                                                        u'type': u'string'}},", "Outbound template:                                                                             u'required': [u'parse']},", "Outbound template:                                                            u'result': {u'properties': {u'operation': {u'enum': [u'GET',", "Outbound template:                                                                                                                 u'POST',", "Outbound template:                                                                                                                 u'PUT',", "Outbound template:                                                                                                                 u'DELETE'],", "Outbound template:                                                                                                       u'type': u'string'}}},", "Outbound template:                                                            u'timeout': {u'properties': {u'operation': {u'enum': [u'GET',", "Outbound template:                                                                                                                  u'POST',", "Outbound template:                                                                                                                  u'PUT',", "Outbound template:                                                                                                                  u'DELETE',", "Outbound template:                                                                                                                  u'SLEEP'],", "Outbound template:                                                                                                        u'type': u'string'}}},", "Outbound template:                                                            u'transport': {u'properties': {u'operation': {u'enum': [u'GET',", "Outbound template:                                                                                                                    u'POST',", "Outbound template:                                                                                                                    u'PUT',", "Outbound template:                                                                                                                    u'DELETE'],", "Outbound template:                                                                                                          u'type': u'string'}}}},", "Outbound template:                                          u'not': {u'required': [u'body',", "Outbound template:                                                                 u'body_list'],", "Outbound template:                                                   u'type': u'object'},", "Outbound template:                                          u'oneOf': [{u'properties': {u'operation': {u'enum': [u'GET',", "Outbound template:                                                                                               u'POST',", "Outbound template:                                                                                               u'PUT',", "Outbound template:                                                                                               u'DELETE'],", "Outbound template:                                                                                     u'type': u'string'},", "Outbound template:                                                                      u'timeout': {u'type': u'string'}}},", "Outbound template:                                                     {u'properties': {u'operation': {u'enum': [u'NOP'],", "Outbound template:                                                                                     u'type': u'string'}}},", "Outbound template:                                                     {u'properties': {u'operation': {u'enum': [u'CONDITION'],", "Outbound template:                                                                                     u'type': u'string'}},", "Outbound template:                                                      u'required': [u'condition']},", "Outbound template:                                                     {u'properties': {u'operation': {u'enum': [u'SLEEP'],", "Outbound template:                                                                                     u'type': u'string'},", "Outbound template:                                                                      u'timeout': {u'type': u'string'}},", "Outbound template:                                                      u'required': [u'timeout']}],", "Outbound template:                                          u'properties': {u'body': {u'type': u'string'},", "Outbound template:                                                          u'body_list': {u'items': {u'type': u'string'},", "Outbound template:                                                                         u'type': u'array'},", "Outbound template:                                                          u'condition': {u'$ref': u'#/definitions/__main__.Condition'},", "Outbound template:                                                          u'headers': {u'additionalProperties': {u'type': u'string'},", "Outbound template:                                                                       u'type': u'object'},", "Outbound template:                                                          u'name': {u'type': u'string'},", "Outbound template:                                                          u'operation': {u'enum': [u'GET',", "Outbound template:                                                                                   u'POST',", "Outbound template:                                                                                   u'PUT',", "Outbound template:                                                                                   u'DELETE',", "Outbound template:                                                                                   u'SLEEP',", "Outbound template:                                                                                   u'NOP',", "Outbound template:                                                                                   u'CONDITION'],", "Outbound template:                                                                         u'type': u'string'},", "Outbound template:                                                          u'override_headers': {u'type': u'boolean'},", "Outbound template:                                                          u'parameters': {u'items': {u'$ref': u'#/definitions/__main__.Parameter'},", "Outbound template:                                                                          u'type': u'array'},", "Outbound template:                                                          u'parse': {u'enum': [u'JSON',", "Outbound template:                                                                               u'REGEX',", "Outbound template:                                                                               u'REGEXLINE',", "Outbound template:                                                                               u'REGEXMULTILINE',", "Outbound template:                                                                               u'XML'],", "Outbound template:                                                                     u'type': u'string'},", "Outbound template:                                                          u'parse_regex': {u'type': u'string'},", "Outbound template:                                                          u'result': {u'items': {u'$ref': u'#/definitions/__main__.Result'},", "Outbound template:                                                                      u'type': u'array'},", "Outbound template:                                                          u'timeout': {u'type': u'string'},", "Outbound template:                                                          u'transport': {u'$ref': u'#/definitions/__main__.Transport'}},", "Outbound template:                                          u'required': [u'name',", "Outbound template:                                                        u'operation'],", "Outbound template:                                          u'type': u'object'},", "Outbound template:                       u'__main__.Transport': {u'additionalProperties': False,", "Outbound template:                                               u'dependencies': {u'override_path': {u'required': [u'path']}},", "Outbound template:                                               u'properties': {u'content_type': {u'type': u'string'},", "Outbound template:                                                               u'override_path': {u'type': u'boolean'},", "Outbound template:                                                               u'path': {u'type': u'string'}},", "Outbound template:                                               u'type': u'object'},", "Outbound template:                       u'__main__.Variable': {u'additionalProperties': False,", "Outbound template:                                              u'properties': {u'name': {u'type': u'string'},", "Outbound template:                                                              u'type': {u'enum': [u'STRING',", "Outbound template:                                                                                  u'INT',", "Outbound template:                                                                                  u'BOOL'],", "Outbound template:                                                                        u'type': u'string'},", "Outbound template:                                                              u'value': {u'type': u'string'}},", "Outbound template:                                              u'required': [u'name',", "Outbound template:                                                            u'type'],", "Outbound template:                                              u'type': u'object'}},", "Outbound template:      u'properties': {u'action_type': {u'type': u'string'},", "Outbound template:                      u'comment': {u'maxLength': 256, u'type': u'string'},", "Outbound template:                      u'content_type': {u'default': u'application/json',", "Outbound template:                                        u'type': u'string'},", "Outbound template:                      u'event_type': {u'items': {u'enum': [u'RPZ',", "Outbound template:                                                           u'LEASE',", "Outbound template:                                                           u'TUNNEL',", "Outbound template:                                                           u'NETWORK_IPV4',", "Outbound template:                                                           u'NETWORK_IPV6',", "Outbound template:                                                           u'RANGE_IPV4',", "Outbound template:                                                           u'RANGE_IPV6',", "Outbound template:                                                           u'FIXED_ADDRESS_IPV4',", "Outbound template:                                                           u'FIXED_ADDRESS_IPV6',", "Outbound template:                                                           u'HOST_ADDRESS_IPV4',", "Outbound template:                                                           u'HOST_ADDRESS_IPV6'],", "Outbound template:                                                 u'type': u'string'},", "Outbound template:                                      u'type': u'array'},", "Outbound template:                      u'headers': {u'additionalProperties': {u'type': u'string'},", "Outbound template:                                   u'type': u'object'},", "Outbound template:                      u'instance_variables': {u'items': {u'$ref': u'#/definitions/__main__.Variable'},", "Outbound template:                                              u'type': u'array'},", "Outbound template:                      u'name': {u'maxLength': 256, u'type': u'string'},", "Outbound template:                      u'quoting': {u'enum': [u'XML',", "Outbound template:                                             u'JSO<PERSON>',", "Outbound template:                                             u'URL',", "Outbound template:                                             u'ASIS'],", "Outbound template:                                   u'type': u'string'},", "Outbound template:                      u'steps': {u'items': {u'$ref': u'#/definitions/__main__.Step'},", "Outbound template:                                 u'type': u'array'},", "Outbound template:                      u'template_variables': {u'items': {u'$ref': u'#/definitions/__main__.Variable'},", "Outbound template:                                              u'type': u'array'},", "Outbound template:                      u'timeout': {u'minimum': 0, u'type': u'number'},", "Outbound template:                      u'transport': {u'$ref': u'#/definitions/__main__.Transport'},", "Outbound template:                      u'type': {u'enum': [u'REST_EVENT'],", "Outbound template:                                u'type': u'string'},", "Outbound template:                      u'vendor_identifier': {u'type': u'string'},", "Outbound template:                      u'version': {u'enum': [u'1.0'], u'type': u'string'}},", "Outbound template:      u'required': [u'event_type',", "Outbound template:                    u'version',", "Outbound template:                    u'name',", "Outbound template:                    u'type',", "Outbound template:                    u'vendor_identifier',", "Outbound template:                    u'steps'],", "Outbound template:      u'type': u'object'}", "Outbound template:", "Outbound template: On instance:", "Outbound template:     {u'name': u'ver_1_ev_nok', u'type': u'REST_EVENT', u'version': u'1.0'}"]}