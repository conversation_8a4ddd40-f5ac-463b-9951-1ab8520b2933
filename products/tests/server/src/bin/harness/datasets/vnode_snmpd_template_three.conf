
# Automatically generated config file, do not edit!
com2sec ConfigUser  default       snmp test community string2
com2sec6 ConfigUser  default       snmp test community string2
group   ConfigGroup v1            ConfigUser
group   ConfigGroup v2c           ConfigUser
view    everything     included      .1
access  ConfigGroup ""	any	noauth	exact	everything	none	none
agentxsocket   tcp:localhost:705,tcp6:[::1]:705,udp:localhost:705,udp6:[::1]:705
master agentx
agentXRetries 0
agentXTimeout 300
agentuser snmp
agentAddress udp:161,udp6:161
agentgroup snmp
pass_persist .*******.2.1.15.900.1 /usr/local/sbin/quagga-snmp-bgpd
sysservices	72
dontLogTCPWrappersConnects true
