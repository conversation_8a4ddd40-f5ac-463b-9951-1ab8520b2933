#
# This dhcpd.conf goes with dhcpd-nac-filter.py in
# the same directory
#
local-address 127.0.0.1;
server-identifier 127.0.0.1;
ddns-update-style interim;
not authoritative;
default-lease-time 120;
min-lease-time 120;
max-lease-time 120;
ping-number 1;
ping-timeout 1;
log-facility daemon;
ping-check false;

ddns-updates off;
ignore client-updates;

subnet TEST_NW netmask TEST_NW_MASK {
        pool {
                infoblox-range TEST_NW_START_ADDR_1 TEST_NW_END_ADDR_1;
                range TEST_NW_START_ADDR_1 TEST_NW_END_ADDR_1;
        }
        pool {
                infoblox-range TEST_NW_START_ADDR_2 TEST_NW_END_ADDR_2;
                range TEST_NW_START_ADDR_2 TEST_NW_END_ADDR_2;
        }
}

subnet ********** netmask ************* {
        not authoritative;
}

