Header-AuthZ<PERSON>,fqdn*,,,grid_primaries,view,external_secondaries,allow_transfer,allow_query,zone_type,allow_active_dir,allow_update,zone_format,notify_delay,disabled,soa_negative_ttl,soa_mnames,soa_default_ttl,soa_retry,,create_underscore_zones,soa_serial_number,soa_email,comment,soa_expire,soa_refresh
AuthZone,test.com,,,infoblox.localdomain,default,test.infoblox.com/*******/TRUE,"*********/Deny,1234::/64/Allow","*********/Deny,1234::/64/Allow",Authoritative,*******,"1234::/64/Allow",FORWARD,100,FALSE,400,infoblox.localdomain/mname1,900,800,FALSE,FALSE,1,<EMAIL>,"Update zone",100,200
AuthZone,notest.com,,,infoblox.localdomain,default,test.infoblox.com/*******/TRUE,"*********/Deny,1234::/64/Allow","*********/Deny,1234::/64/Allow",Authoritative,*******,"1234::/64/Allow",FORWARD,100,FALSE,400,infoblox.localdomain/mname1,900,800,FALSE,FALSE,1,<EMAIL>,"Update zone",100,200
AuthZone,10.0.0.0/24,,,infoblox.localdomain,default,,,,Authoritative,,,IPV4,,FALSE,,,,,FALSE,FALSE,,,,,
Header-ARecord,fqdn,_new_fqdn,view,address*,_new_address,comment,disabled,ttl
ARecord,a1.test.com,,default,***********,************,"Update A record",FALSE,7200
ARecord,a2.test.com,aa2.test.com,default,***********,,"Update A record",FALSE,7200
ARecord,a3.test.com,,default,***********,,,FALSE,
Header-PtrRecord,fqdn,_new_fqdn,view,address,,dname*,_new_dname,comment,disabled,ttl,
PtrRecord,*********.in-addr.arpa,,default,*********,,ss.dd.ff,new_dname.test.com,,FALSE,28800,
