source s_external {
    tcp ( port(666) max_connections(100) );
    tcp6 ( port(666) max_connections(100) );
    udp ( port(555) );
    udp6 ( port(555) );  };

destination d_external_1 {  udp6 ( "1234::4444" port(514) localip("::") ); };
destination d_internal_2 {  tcp6 ( "1234::5555" port(1022) localip("[IPV6_ADDRESS]") template("<$PRI>$DATE [IPV6_ADDRESS] $MSGHDR$MSG\n") template_escape(no) persist-name("dinternal2")); };
destination d_external_2 {  tcp6 ( "1234::5555" port(1022) localip("[IPV6_ADDRESS]") ); };

log { source(s_syslogng); filter(f_warning); destination(d_internal_2); };

log { source(s_external); filter(f_info); destination(d_external_1); };
log { source(s_external); filter(f_warning); destination(d_external_2); };
