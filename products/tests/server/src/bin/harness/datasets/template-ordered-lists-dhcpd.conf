ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
ddns-local-address4 ***********;
server-identifier ***********;
authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
option domain-name "infoblox.com";
option broadcast-address ***********;
default-lease-time 3600;
min-lease-time 3600;
max-lease-time 3600;
one-lease-per-client false;
server-name "a.b.com";
next-server *******;
filename "filename";
if substring (option vendor-class-identifier, 0, 9) = "PXEClient" {
	default-lease-time 60;
	min-lease-time 60;
	max-lease-time 60;
}
ping-number 1;
ping-timeout-ms 1000;
option time-offset 200;
option subnet-mask *************;
option domain-name-servers *******, *******, *******;
option routers **********, **********, **********;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/infoblox/var/dhcpd_conf/dhcp_updater.key";

subnet *********** netmask ************* {
	not authoritative;
}

shared-network "infoblox" {
	option domain-name-servers ***********, ***********, ***********;
	option routers **********, **********, **********;

subnet ********* netmask ********* {
	authoritative;
	option domain-name "infoblox.com";
	option broadcast-address ***********;
	option domain-name-servers ***********, ***********, ***********;
	option routers **********, **********, **********;
	option time-offset 200;
	option subnet-mask *************;
	default-lease-time 3600;
	min-lease-time 3600;
	max-lease-time 3600;
	server-name "a.b.com";
	next-server *******;
	filename "filename";
	if substring (option vendor-class-identifier, 0, 9) = "PXEClient" {
		default-lease-time 60;
		min-lease-time 60;
		max-lease-time 60;
	}
	host ********* {
		dynamic;
		hardware ethernet aa:aa:aa:aa:aa:bb;
		fixed-address *********;
		option domain-name "infoblox.com";
		option broadcast-address ***********;
		option domain-name-servers ***********, ***********, ***********;
		option routers *********9, *********8, **********;
		default-lease-time 3600;
		min-lease-time 3600;
		max-lease-time 3600;
		server-name "a.b.com";
		next-server *******;
		filename "filename";
		if substring (option vendor-class-identifier, 0, 9) = "PXEClient" {
			default-lease-time 60;
			min-lease-time 60;
			max-lease-time 60;
		}
	}
	}
	pool {
		infoblox-range ********** **********;
		range ********** **********;
		option domain-name-servers ***********, ***********, ***********;
		option routers **********, **********, **********;
	}
}


#End of dhcpd.conf file
