<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
  <PROPERTY NAME="record_type_num" VALUE="65"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1742012804"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="enable_host_name_policy" VALUE="false"/>
  <PROPERTY NAME="__timestamp" VALUE="1742012804409128"/>
  <PROPERTY NAME="zone" VALUE="._default.com.test"/>
  <PROPERTY NAME="name" VALUE="unk"/>
  <PROPERTY NAME="record_type" VALUE="TYPE65"/>
  <PROPERTY NAME="record_rdata" VALUE="\# 0"/>
  <PROPERTY NAME="record_rdata_hash" VALUE="0e0647fd021e04e2336145d800fb9b92baad0a7934848d8f8b61b23ff4fecb5e8adc250f3e66b5688e07c324741c8d4e94a5d4f0abcfc0a541d56e447e201ddd"/>
  <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
  <PROPERTY NAME="display_name" VALUE="unk"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
  <PROPERTY NAME="record_type_num" VALUE="65"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1742012804"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="enable_host_name_policy" VALUE="false"/>
  <PROPERTY NAME="__timestamp" VALUE="1742012804409128"/>
  <PROPERTY NAME="zone" VALUE="._default.com.test"/>
  <PROPERTY NAME="name" VALUE="unk1"/>
  <PROPERTY NAME="record_type" VALUE="TYPE65"/>
  <PROPERTY NAME="record_rdata" VALUE="\# 25 6D7974657374696E666F626C6F782E7A6F6E65312E636F6D0A"/>
  <PROPERTY NAME="record_rdata_hash" VALUE="0e0647fd021e04e2336145d800fb9b92baad0a7934848d8f8b61b23ff4fecb5e8adc250f3e66b5688e07c324741c8d4e94a5d4f0abcfc0a541d56e447e201ddd"/>
  <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
  <PROPERTY NAME="display_name" VALUE="unk1"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
</DATABASE>

