ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address *********;
ddns-local-address4 *********;
server-identifier *********;
authoritative;
infoblox-ignore-uid true;
infoblox-ignore-macaddr false;
option domain-name "member.infoblox.com";
option broadcast-address **********;
default-lease-time 30000;
min-lease-time 30000;
max-lease-time 30000;
one-lease-per-client true;
server-name "member-bootserver.infoblox.com";
next-server *********;
filename "member.bootfile";
if substring (option vendor-class-identifier, 0, 9) = "PXEClient" {
	default-lease-time 3000;
	min-lease-time 3000;
	max-lease-time 3000;
}
ping-number 1;
ping-timeout-ms 1000;
option domain-name-servers *********, *********;
option routers ********, ********;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/infoblox/var/dhcpd_conf/dhcp_updater.key";

subnet ********* netmask ********* {
	authoritative;
	option domain-name "network.infoblox.com";
	option broadcast-address **********;
	infoblox-ignore-uid true;
	infoblox-ignore-macaddr false;
	option domain-name-servers ***********, ***********;
	option routers **********, **********;
	default-lease-time 40000;
	min-lease-time 40000;
	max-lease-time 40000;
	server-name "network-bootserver.infoblox.com";
	next-server *********;
	filename "network.bootfile";
	if substring (option vendor-class-identifier, 0, 9) = "PXEClient" {
		default-lease-time 4000;
		min-lease-time 4000;
		max-lease-time 4000;
	}
	host ********** {
		dynamic;
		hardware ethernet 11:22:33:44:55:66;
		fixed-address **********;
		option domain-name "fixedaddr.infoblox.com";
		option broadcast-address **********;
		option domain-name-servers ***********, ***********;
		option routers **********, **********;
		default-lease-time 60000;
		min-lease-time 60000;
		max-lease-time 60000;
		server-name "fixedaddr-bootserver.infoblox.com";
		next-server *********;
		filename "fixedaddr.bootfile";
		if substring (option vendor-class-identifier, 0, 9) = "PXEClient" {
			default-lease-time 6000;
			min-lease-time 6000;
			max-lease-time 6000;
		}
	}
}

subnet 30.0.0.0 netmask ********* {
	not authoritative;
}

#End of dhcpd.conf file
