<DATABASE NAME="onedb" VERSION="MDXMLTEST">
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_properties"/>
    <PROPERTY NAME="virtual_node" VALUE="0"/>
    <PROPERTY NAME="atc_fwd_enable" VALUE="true"/>
  </OBJECT>

  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="virtual_oid" VALUE="0"/>
    <PROPERTY NAME="ha_enabled" VALUE="0"/>
    <PROPERTY NAME="host_name" VALUE="host1"/>
    <PROPERTY NAME="use_csp_dns_resolver" VALUE="false"/>
    <PROPERTY NAME="csp_dns_resolver" VALUE="*************"/>
    <PROPERTY NAME="use_csp_join_token" VALUE="false"/>
    <PROPERTY NAME="csp_join_token" VALUE=""/>
    <PROPERTY NAME="use_csp_https_proxy" VALUE="false"/>
    <PROPERTY NAME="csp_https_proxy" VALUE=""/>
  </OBJECT>

  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
    <PROPERTY NAME="dfp_running" VALUE="false"/>
    <PROPERTY NAME="physical_oid" VALUE="0"/>
    <PROPERTY NAME="virtual_node" VALUE="0"/>
    <PROPERTY NAME="csp_access_key" VALUE="aabbccdd"/>
  </OBJECT>

  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_dfp_properties"/>
    <PROPERTY NAME="virtual_node" VALUE="0"/>
    <PROPERTY NAME="dfp_override" VALUE="false"/>
    <PROPERTY NAME="dfp_enable" VALUE="false"/>
    <PROPERTY NAME="dfp_forward_first" VALUE="true"/>
    <PROPERTY NAME="is_dfp_override" VALUE="true"/>
  </OBJECT>
</DATABASE>
