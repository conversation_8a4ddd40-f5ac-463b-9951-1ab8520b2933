// Inspired by examples in chapter 3 of the BIND ARM

key "key1" {
  algorithm hmac-md5;
  secret "CdhtOnXxkZeuHP5saggSnA==";
};

key "key2" {
  algorithm hmac-md5;
  secret "MxuDAibo+rXNf9Cp9I4V6g==";
};

// This key is not used in any match-clients list
key "key-not-in-use" {
  algorithm hmac-md5;
  secret "xo9Qz0Tq435DBzZvXYVRsA==";
};

// Include a generated key-file (slightly closer to the "real thing")
include "/tmp/dhcp_updater.key";

acl "loopbacknet" { *********/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";
	recursion no;			// Do not provide recursive service
	listen-on {127.0.0.1; ***********; };
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};


view "VIEW_NAME" {

// Root server hints
zone "." { type hint; file "root.hint"; };

// Reverse mapping for loopback address
zone "127.in-addr.arpa" {
	type master;
	file "127.in-addr.arpa";
	notify no;
	allow-update { key DHCP_UPDATER; };
};

zone "test.infoblox.com" {
	type master;
	notify no;
	file "test.infoblox.com.db";
	allow-update { key DHCP_UPDATER; };
};

};
