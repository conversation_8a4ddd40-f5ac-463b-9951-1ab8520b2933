<DATABASE NAME="onedb" VERSION="MDXMLTEST">
 <OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.discovery.advisor_settings"/>
  <PROPERTY NAME="enable_proxy" VALUE="false"/>
  <PROPERTY NAME="execution_interval" VALUE="86400"/>
  <PROPERTY NAME="execution_hour" VALUE="3"/>
  <PROPERTY NAME="auth_type" VALUE="TOKEN"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="use_proxy_username_passwd" VALUE="false"/>
  <PROPERTY NAME="network_interface_virtual_ip" VALUE="."/>
  <PROPERTY NAME="min_severity" VALUE="8.0"/>
 </OBJECT>
</DATABASE>
