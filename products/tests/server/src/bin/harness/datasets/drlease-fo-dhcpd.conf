local-address MY-ADDRESS;
server-identifier MY-ADDRESS;
ddns-update-style interim;
authoritative;
max-lease-time LEASE-TIME;
default-lease-time LEASE-TIME;
ping-check false;
ddns-updates off;
ignore client-updates;

failover peer "f1"
{
  MY-ROLE;
  address MY-ADDRESS;
  port 519;
  peer address PEER-ADDRESS;
  peer port 519;
  max-response-delay 60;
  max-unacked-updates 10;
  MCLT
  SPLIT
}

# Allocate leases from the loopback network
subnet ********* netmask ********* {
	pool {
		failover peer "f1";
		deny dynamic bootp clients;
		infoblox-range ********* *********;
		range ********* *********;
	}
}

