# All times in this file are in UTC (GMT), not your local timezone.   This is
# not a bug, so please don't ask about it.   There is no portable way to
# store leases in the local timezone, so please don't request this as a
# feature.   If this is inconvenient or confusing to you, we sincerely
# apologize.   Seriously, though - don't ask.
# The format of this file is documented in the dhcpd.leases(5) manual page.
# This lease file was written by isc-dhcp-V3.0.1rc14-ib-p1

lease ********** {
  starts 3 2004/09/29 20:23:49;
  ends 3 2004/09/29 20:23:49;
  binding state abandoned;
  client-hostname "0-ABCDEFGH-0-ABCDEFGH-0-ABCDEFGH-0-ABCDEFGH-0-ABCDEFGH-0";
}
lease ********** {
  starts 3 2004/09/29 20:23:51;
  ends 4 2004/09/30 08:23:51;
  binding state active;
  hardware ethernet 01:01:01:01:01:01;
  uid "\001\001\001\001\001\001\001";
  client-hostname "1-ABCDEFGH-1-ABCDEFGH-1-ABCDEFGH-1-ABCDEFGH-1-ABCDEFGH-1";
}
lease ********** {
  starts 3 2004/09/29 20:23:52;
  ends 4 2004/09/30 08:23:52;
  binding state active;
  hardware ethernet 02:02:02:02:02:02;
  uid "\001\002\002\002\002\002\002";
  client-hostname "2-ABCDEFGH-2-ABCDEFGH-2-ABCDEFGH-2-ABCDEFGH-2-ABCDEFGH-2";
}
lease ********** {
  starts 3 2004/09/29 20:23:53;
  ends 4 2004/09/30 08:23:53;
  binding state active;
  hardware ethernet 03:03:03:03:03:03;
  uid "\001\003\003\003\003\003\003";
  client-hostname "3-ABCDEFGH-3-ABCDEFGH-3-ABCDEFGH-3-ABCDEFGH-3-ABCDEFGH-3";
}
lease ********** {
  starts 3 2004/09/29 20:23:54;
  ends 4 2004/09/30 08:23:54;
  binding state active;
  hardware ethernet 04:04:04:04:04:04;
  uid "\001\004\004\004\004\004\004";
  client-hostname "4-ABCDEFGH-4-ABCDEFGH-4-ABCDEFGH-4-ABCDEFGH-4-ABCDEFGH-4";
}
lease ********** {
  starts 3 2004/09/29 20:23:55;
  ends 4 2004/09/30 08:23:55;
  binding state active;
  hardware ethernet 05:05:05:05:05:05;
  uid "\001\005\005\005\005\005\005";
  client-hostname "5-ABCDEFGH-5-ABCDEFGH-5-ABCDEFGH-5-ABCDEFGH-5-ABCDEFGH-5";
}
lease ********** {
  starts 3 2004/09/29 20:23:56;
  ends 4 2004/09/30 08:23:56;
  binding state active;
  hardware ethernet 06:06:06:06:06:06;
  uid "\001\006\006\006\006\006\006";
  client-hostname "6-ABCDEFGH-6-ABCDEFGH-6-ABCDEFGH-6-ABCDEFGH-6-ABCDEFGH-6";
}
lease ********** {
  starts 3 2004/09/29 20:23:57;
  ends 4 2004/09/30 08:23:57;
  binding state active;
  hardware ethernet 07:07:07:07:07:07;
  uid "\001\007\007\007\007\007\007";
  client-hostname "7-ABCDEFGH-7-ABCDEFGH-7-ABCDEFGH-7-ABCDEFGH-7-ABCDEFGH-7";
}
lease ********** {
  starts 3 2004/09/29 20:23:58;
  ends 4 2004/09/30 08:23:58;
  binding state active;
  hardware ethernet 08:08:08:08:08:08;
  uid "\001\010\010\010\010\010\010";
  client-hostname "8-ABCDEFGH-8-ABCDEFGH-8-ABCDEFGH-8-ABCDEFGH-8-ABCDEFGH-8";
}
lease ********** {
  starts 3 2004/09/29 20:23:59;
  ends 4 2004/09/30 08:23:59;
  binding state active;
  hardware ethernet 09:09:09:09:09:09;
  uid "\001\011\011\011\011\011\011";
  client-hostname "9-ABCDEFGH-9-ABCDEFGH-9-ABCDEFGH-9-ABCDEFGH-9-ABCDEFGH-9";
}
lease ********** {
  starts 3 2004/09/29 20:24:00;
  ends 4 2004/09/30 08:24:00;
  binding state active;
  hardware ethernet 0a:0a:0a:0a:0a:0a;
  uid "\001\012\012\012\012\012\012";
  client-hostname "10-ABCDEFGH-10-ABCDEFGH-10-ABCDEFGH-10-ABCDEFGH-10-ABCDEFGH-10";
}
lease ********** {
  starts 3 2004/09/29 20:24:01;
  ends 4 2004/09/30 08:24:01;
  binding state active;
  hardware ethernet 0b:0b:0b:0b:0b:0b;
  uid "\001\013\013\013\013\013\013";
  client-hostname "11-ABCDEFGH-11-ABCDEFGH-11-ABCDEFGH-11-ABCDEFGH-11-ABCDEFGH-11";
}
lease ********** {
  starts 3 2004/09/29 20:24:02;
  ends 4 2004/09/30 08:24:02;
  binding state active;
  hardware ethernet 0c:0c:0c:0c:0c:0c;
  uid "\001\014\014\014\014\014\014";
  client-hostname "12-ABCDEFGH-12-ABCDEFGH-12-ABCDEFGH-12-ABCDEFGH-12-ABCDEFGH-12";
}
lease ********** {
  starts 3 2004/09/29 20:24:03;
  ends 4 2004/09/30 08:24:03;
  binding state active;
  hardware ethernet 0d:0d:0d:0d:0d:0d;
  uid "\001\015\015\015\015\015\015";
  client-hostname "13-ABCDEFGH-13-ABCDEFGH-13-ABCDEFGH-13-ABCDEFGH-13-ABCDEFGH-13";
}
lease ********** {
  starts 3 2004/09/29 20:24:04;
  ends 4 2004/09/30 08:24:04;
  binding state active;
  hardware ethernet 0e:0e:0e:0e:0e:0e;
  uid "\001\016\016\016\016\016\016";
  client-hostname "14-ABCDEFGH-14-ABCDEFGH-14-ABCDEFGH-14-ABCDEFGH-14-ABCDEFGH-14";
}
lease ********** {
  starts 3 2004/09/29 20:24:05;
  ends 4 2004/09/30 08:24:05;
  binding state active;
  hardware ethernet 0f:0f:0f:0f:0f:0f;
  uid "\001\017\017\017\017\017\017";
  client-hostname "15-ABCDEFGH-15-ABCDEFGH-15-ABCDEFGH-15-ABCDEFGH-15-ABCDEFGH-15";
}
lease ********** {
  starts 3 2004/09/29 20:24:06;
  ends 4 2004/09/30 08:24:06;
  binding state active;
  hardware ethernet 10:10:10:10:10:10;
  uid "\001\020\020\020\020\020\020";
  client-hostname "16-ABCDEFGH-16-ABCDEFGH-16-ABCDEFGH-16-ABCDEFGH-16-ABCDEFGH-16";
}
lease ********** {
  starts 3 2004/09/29 20:24:07;
  ends 4 2004/09/30 08:24:07;
  binding state active;
  hardware ethernet 11:11:11:11:11:11;
  uid "\001\021\021\021\021\021\021";
  client-hostname "17-ABCDEFGH-17-ABCDEFGH-17-ABCDEFGH-17-ABCDEFGH-17-ABCDEFGH-17";
}
lease ********** {
  starts 3 2004/09/29 20:24:08;
  ends 4 2004/09/30 08:24:08;
  binding state active;
  hardware ethernet 12:12:12:12:12:12;
  uid "\001\022\022\022\022\022\022";
  client-hostname "18-ABCDEFGH-18-ABCDEFGH-18-ABCDEFGH-18-ABCDEFGH-18-ABCDEFGH-18";
}
lease ********** {
  starts 3 2004/09/29 20:24:09;
  ends 4 2004/09/30 08:24:09;
  binding state active;
  hardware ethernet 13:13:13:13:13:13;
  uid "\001\023\023\023\023\023\023";
  client-hostname "19-ABCDEFGH-19-ABCDEFGH-19-ABCDEFGH-19-ABCDEFGH-19-ABCDEFGH-19";
}
lease ********** {
  starts 3 2004/09/29 20:24:09;
  ends 3 2004/09/29 20:24:09;
  binding state abandoned;
  client-hostname "20-ABCDEFGH-20-ABCDEFGH-20-ABCDEFGH-20-ABCDEFGH-20-ABCDEFGH-20";
}
lease ********** {
  starts 3 2004/09/29 20:24:10;
  ends 3 2004/09/29 20:24:10;
  binding state abandoned;
  client-hostname "21-ABCDEFGH-21-ABCDEFGH-21-ABCDEFGH-21-ABCDEFGH-21-ABCDEFGH-21";
}
lease ********** {
  starts 3 2004/09/29 20:24:12;
  ends 4 2004/09/30 08:24:12;
  binding state active;
  hardware ethernet 16:16:16:16:16:16;
  uid "\001\026\026\026\026\026\026";
  client-hostname "22-ABCDEFGH-22-ABCDEFGH-22-ABCDEFGH-22-ABCDEFGH-22-ABCDEFGH-22";
}
lease ********** {
  starts 3 2004/09/29 20:24:13;
  ends 4 2004/09/30 08:24:13;
  binding state active;
  hardware ethernet 17:17:17:17:17:17;
  uid "\001\027\027\027\027\027\027";
  client-hostname "23-ABCDEFGH-23-ABCDEFGH-23-ABCDEFGH-23-ABCDEFGH-23-ABCDEFGH-23";
}
lease ********** {
  starts 3 2004/09/29 20:24:14;
  ends 4 2004/09/30 08:24:14;
  binding state active;
  hardware ethernet 18:18:18:18:18:18;
  uid "\001\030\030\030\030\030\030";
  client-hostname "24-ABCDEFGH-24-ABCDEFGH-24-ABCDEFGH-24-ABCDEFGH-24-ABCDEFGH-24";
}
lease ********** {
  starts 3 2004/09/29 20:24:15;
  ends 4 2004/09/30 08:24:15;
  binding state active;
  hardware ethernet 19:19:19:19:19:19;
  uid "\001\031\031\031\031\031\031";
  client-hostname "25-ABCDEFGH-25-ABCDEFGH-25-ABCDEFGH-25-ABCDEFGH-25-ABCDEFGH-25";
}
lease ********** {
  starts 3 2004/09/29 20:24:16;
  ends 4 2004/09/30 08:24:16;
  binding state active;
  hardware ethernet 1a:1a:1a:1a:1a:1a;
  uid "\001\032\032\032\032\032\032";
  client-hostname "26-ABCDEFGH-26-ABCDEFGH-26-ABCDEFGH-26-ABCDEFGH-26-ABCDEFGH-26";
}
lease ********** {
  starts 3 2004/09/29 20:24:17;
  ends 4 2004/09/30 08:24:17;
  binding state active;
  hardware ethernet 1b:1b:1b:1b:1b:1b;
  uid "\001\033\033\033\033\033\033";
  client-hostname "27-ABCDEFGH-27-ABCDEFGH-27-ABCDEFGH-27-ABCDEFGH-27-ABCDEFGH-27";
}
lease ********** {
  starts 3 2004/09/29 20:24:18;
  ends 4 2004/09/30 08:24:18;
  binding state active;
  hardware ethernet 1c:1c:1c:1c:1c:1c;
  uid "\001\034\034\034\034\034\034";
  client-hostname "28-ABCDEFGH-28-ABCDEFGH-28-ABCDEFGH-28-ABCDEFGH-28-ABCDEFGH-28";
}
lease ********** {
  starts 3 2004/09/29 20:24:19;
  ends 4 2004/09/30 08:24:19;
  binding state active;
  hardware ethernet 1d:1d:1d:1d:1d:1d;
  uid "\001\035\035\035\035\035\035";
  client-hostname "29-ABCDEFGH-29-ABCDEFGH-29-ABCDEFGH-29-ABCDEFGH-29-ABCDEFGH-29";
}
lease ********** {
  starts 3 2004/09/29 20:24:20;
  ends 4 2004/09/30 08:24:20;
  binding state active;
  hardware ethernet 1e:1e:1e:1e:1e:1e;
  uid "\001\036\036\036\036\036\036";
  client-hostname "30-ABCDEFGH-30-ABCDEFGH-30-ABCDEFGH-30-ABCDEFGH-30-ABCDEFGH-30";
}
lease ********** {
  starts 3 2004/09/29 20:24:21;
  ends 4 2004/09/30 08:24:21;
  binding state active;
  hardware ethernet 1f:1f:1f:1f:1f:1f;
  uid "\001\037\037\037\037\037\037";
  client-hostname "31-ABCDEFGH-31-ABCDEFGH-31-ABCDEFGH-31-ABCDEFGH-31-ABCDEFGH-31";
}
lease ********** {
  starts 3 2004/09/29 20:24:22;
  ends 4 2004/09/30 08:24:22;
  binding state active;
  hardware ethernet 20:20:20:20:20:20;
  uid "\001      ";
  client-hostname "32-ABCDEFGH-32-ABCDEFGH-32-ABCDEFGH-32-ABCDEFGH-32-ABCDEFGH-32";
}
lease ********** {
  starts 3 2004/09/29 20:24:22;
  ends 3 2004/09/29 20:24:22;
  binding state abandoned;
  client-hostname "33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33";
}
lease ********** {
  starts 3 2004/09/29 20:24:24;
  ends 4 2004/09/30 08:24:24;
  binding state active;
  hardware ethernet 22:22:22:22:22:22;
  uid "\001\"\"\"\"\"\"";
  client-hostname "34-ABCDEFGH-34-ABCDEFGH-34-ABCDEFGH-34-ABCDEFGH-34-ABCDEFGH-34";
}
lease ********** {
  starts 3 2004/09/29 20:24:25;
  ends 4 2004/09/30 08:24:25;
  binding state active;
  hardware ethernet 23:23:23:23:23:23;
  uid "\001######";
  client-hostname "35-ABCDEFGH-35-ABCDEFGH-35-ABCDEFGH-35-ABCDEFGH-35-ABCDEFGH-35";
}
lease ********** {
  starts 3 2004/09/29 20:24:25;
  ends 3 2004/09/29 20:24:25;
  binding state abandoned;
  client-hostname "36-ABCDEFGH-36-ABCDEFGH-36-ABCDEFGH-36-ABCDEFGH-36-ABCDEFGH-36";
}
lease ********** {
  starts 3 2004/09/29 20:24:27;
  ends 4 2004/09/30 08:24:27;
  binding state active;
  hardware ethernet 25:25:25:25:25:25;
  uid "\001%%%%%%";
  client-hostname "37-ABCDEFGH-37-ABCDEFGH-37-ABCDEFGH-37-ABCDEFGH-37-ABCDEFGH-37";
}
lease ********** {
  starts 3 2004/09/29 20:24:28;
  ends 4 2004/09/30 08:24:28;
  binding state active;
  hardware ethernet 26:26:26:26:26:26;
  uid "\001&&&&&&";
  client-hostname "38-ABCDEFGH-38-ABCDEFGH-38-ABCDEFGH-38-ABCDEFGH-38-ABCDEFGH-38";
}
lease ********** {
  starts 3 2004/09/29 20:24:29;
  ends 4 2004/09/30 08:24:29;
  binding state active;
  hardware ethernet 27:27:27:27:27:27;
  uid "\001''''''";
  client-hostname "39-ABCDEFGH-39-ABCDEFGH-39-ABCDEFGH-39-ABCDEFGH-39-ABCDEFGH-39";
}
lease ********** {
  starts 3 2004/09/29 20:24:30;
  ends 4 2004/09/30 08:24:30;
  binding state active;
  hardware ethernet 28:28:28:28:28:28;
  uid "\001((((((";
  client-hostname "40-ABCDEFGH-40-ABCDEFGH-40-ABCDEFGH-40-ABCDEFGH-40-ABCDEFGH-40";
}
lease ********** {
  starts 3 2004/09/29 20:24:30;
  ends 3 2004/09/29 20:24:30;
  binding state abandoned;
  client-hostname "41-ABCDEFGH-41-ABCDEFGH-41-ABCDEFGH-41-ABCDEFGH-41-ABCDEFGH-41";
}
lease ********** {
  starts 3 2004/09/29 20:24:31;
  ends 3 2004/09/29 20:24:31;
  binding state abandoned;
  client-hostname "42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42";
}
lease ********** {
  starts 3 2004/09/29 20:24:33;
  ends 4 2004/09/30 08:24:33;
  binding state active;
  hardware ethernet 2b:2b:2b:2b:2b:2b;
  uid "\001++++++";
  client-hostname "43-ABCDEFGH-43-ABCDEFGH-43-ABCDEFGH-43-ABCDEFGH-43-ABCDEFGH-43";
}
lease ********** {
  starts 3 2004/09/29 20:24:34;
  ends 4 2004/09/30 08:24:34;
  binding state active;
  hardware ethernet 2c:2c:2c:2c:2c:2c;
  uid "\001,,,,,,";
  client-hostname "44-ABCDEFGH-44-ABCDEFGH-44-ABCDEFGH-44-ABCDEFGH-44-ABCDEFGH-44";
}
lease ********** {
  starts 3 2004/09/29 20:24:34;
  ends 3 2004/09/29 20:24:34;
  binding state abandoned;
  client-hostname "45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45";
}
lease ********** {
  starts 3 2004/09/29 20:24:36;
  ends 4 2004/09/30 08:24:36;
  binding state active;
  hardware ethernet 2e:2e:2e:2e:2e:2e;
  uid "\001......";
  client-hostname "46-ABCDEFGH-46-ABCDEFGH-46-ABCDEFGH-46-ABCDEFGH-46-ABCDEFGH-46";
}
lease ********** {
  starts 3 2004/09/29 20:24:37;
  ends 4 2004/09/30 08:24:37;
  binding state active;
  hardware ethernet 2f:2f:2f:2f:2f:2f;
  uid "\001//////";
  client-hostname "47-ABCDEFGH-47-ABCDEFGH-47-ABCDEFGH-47-ABCDEFGH-47-ABCDEFGH-47";
}
lease ********** {
  starts 3 2004/09/29 20:24:38;
  ends 4 2004/09/30 08:24:38;
  binding state active;
  hardware ethernet 30:30:30:30:30:30;
  uid "\001000000";
  client-hostname "48-ABCDEFGH-48-ABCDEFGH-48-ABCDEFGH-48-ABCDEFGH-48-ABCDEFGH-48";
}
lease ********** {
  starts 3 2004/09/29 20:24:38;
  ends 3 2004/09/29 20:24:38;
  binding state abandoned;
  client-hostname "49-ABCDEFGH-49-ABCDEFGH-49-ABCDEFGH-49-ABCDEFGH-49-ABCDEFGH-49";
}
lease ********** {
  starts 3 2004/09/29 20:24:40;
  ends 4 2004/09/30 08:24:40;
  binding state active;
  hardware ethernet 32:32:32:32:32:32;
  uid "\001222222";
  client-hostname "50-ABCDEFGH-50-ABCDEFGH-50-ABCDEFGH-50-ABCDEFGH-50-ABCDEFGH-50";
}
lease ********** {
  starts 3 2004/09/29 20:24:41;
  ends 4 2004/09/30 08:24:41;
  binding state active;
  hardware ethernet 33:33:33:33:33:33;
  uid "\001333333";
  client-hostname "51-ABCDEFGH-51-ABCDEFGH-51-ABCDEFGH-51-ABCDEFGH-51-ABCDEFGH-51";
}
lease ********** {
  starts 3 2004/09/29 20:24:42;
  ends 4 2004/09/30 08:24:42;
  binding state active;
  hardware ethernet 34:34:34:34:34:34;
  uid "\001444444";
  client-hostname "52-ABCDEFGH-52-ABCDEFGH-52-ABCDEFGH-52-ABCDEFGH-52-ABCDEFGH-52";
}
lease ********** {
  starts 3 2004/09/29 20:24:43;
  ends 4 2004/09/30 08:24:43;
  binding state active;
  hardware ethernet 35:35:35:35:35:35;
  uid "\001555555";
  client-hostname "53-ABCDEFGH-53-ABCDEFGH-53-ABCDEFGH-53-ABCDEFGH-53-ABCDEFGH-53";
}
lease ********** {
  starts 3 2004/09/29 20:24:44;
  ends 4 2004/09/30 08:24:44;
  binding state active;
  hardware ethernet 36:36:36:36:36:36;
  uid "\001666666";
  client-hostname "54-ABCDEFGH-54-ABCDEFGH-54-ABCDEFGH-54-ABCDEFGH-54-ABCDEFGH-54";
}
lease ********** {
  starts 3 2004/09/29 20:24:45;
  ends 4 2004/09/30 08:24:45;
  binding state active;
  hardware ethernet 37:37:37:37:37:37;
  uid "\001777777";
  client-hostname "55-ABCDEFGH-55-ABCDEFGH-55-ABCDEFGH-55-ABCDEFGH-55-ABCDEFGH-55";
}
lease ********** {
  starts 3 2004/09/29 20:24:46;
  ends 4 2004/09/30 08:24:46;
  binding state active;
  hardware ethernet 38:38:38:38:38:38;
  uid "\001888888";
  client-hostname "56-ABCDEFGH-56-ABCDEFGH-56-ABCDEFGH-56-ABCDEFGH-56-ABCDEFGH-56";
}
lease ********** {
  starts 3 2004/09/29 20:24:47;
  ends 4 2004/09/30 08:24:47;
  binding state active;
  hardware ethernet 39:39:39:39:39:39;
  uid "\001999999";
  client-hostname "57-ABCDEFGH-57-ABCDEFGH-57-ABCDEFGH-57-ABCDEFGH-57-ABCDEFGH-57";
}
lease ********** {
  starts 3 2004/09/29 20:24:48;
  ends 4 2004/09/30 08:24:48;
  binding state active;
  hardware ethernet 3a:3a:3a:3a:3a:3a;
  uid "\001::::::";
  client-hostname "58-ABCDEFGH-58-ABCDEFGH-58-ABCDEFGH-58-ABCDEFGH-58-ABCDEFGH-58";
}
lease ********** {
  starts 3 2004/09/29 20:24:49;
  ends 4 2004/09/30 08:24:49;
  binding state active;
  hardware ethernet 3b:3b:3b:3b:3b:3b;
  uid "\001;;;;;;";
  client-hostname "59-ABCDEFGH-59-ABCDEFGH-59-ABCDEFGH-59-ABCDEFGH-59-ABCDEFGH-59";
}
lease ********** {
  starts 3 2004/09/29 20:24:50;
  ends 4 2004/09/30 08:24:50;
  binding state active;
  hardware ethernet 3c:3c:3c:3c:3c:3c;
  uid "\001<<<<<<";
  client-hostname "60-ABCDEFGH-60-ABCDEFGH-60-ABCDEFGH-60-ABCDEFGH-60-ABCDEFGH-60";
}
lease ********** {
  starts 3 2004/09/29 20:24:50;
  ends 3 2004/09/29 20:24:50;
  binding state abandoned;
  client-hostname "61-ABCDEFGH-61-ABCDEFGH-61-ABCDEFGH-61-ABCDEFGH-61-ABCDEFGH-61";
}
lease ********** {
  starts 3 2004/09/29 20:24:52;
  ends 4 2004/09/30 08:24:52;
  binding state active;
  hardware ethernet 3e:3e:3e:3e:3e:3e;
  uid "\001>>>>>>";
  client-hostname "62-ABCDEFGH-62-ABCDEFGH-62-ABCDEFGH-62-ABCDEFGH-62-ABCDEFGH-62";
}
lease ********** {
  starts 3 2004/09/29 20:24:52;
  ends 3 2004/09/29 20:24:52;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:24:53;
  ends 3 2004/09/29 20:24:53;
  binding state abandoned;
  client-hostname "64-ABCDEFGH-64-ABCDEFGH-64-ABCDEFGH-64-ABCDEFGH-64-ABCDEFGH-64";
}
lease ********** {
  starts 3 2004/09/29 20:24:54;
  ends 3 2004/09/29 20:24:54;
  binding state abandoned;
  client-hostname "65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65";
}
lease ********** {
  starts 3 2004/09/29 20:24:55;
  ends 3 2004/09/29 20:24:55;
  binding state abandoned;
  client-hostname "66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66";
}
lease ********** {
  starts 3 2004/09/29 20:24:56;
  ends 3 2004/09/29 20:24:56;
  binding state abandoned;
  client-hostname "67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67";
}
lease ********** {
  starts 3 2004/09/29 20:24:58;
  ends 3 2004/09/29 20:24:58;
  binding state abandoned;
  client-hostname "68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68";
}
lease ********** {
  starts 3 2004/09/29 20:24:59;
  ends 3 2004/09/29 20:24:59;
  binding state abandoned;
  client-hostname "69-ABCDEFGH-69-ABCDEFGH-69-ABCDEFGH-69-ABCDEFGH-69-ABCDEFGH-69";
}
lease ********** {
  starts 3 2004/09/29 20:25:01;
  ends 4 2004/09/30 08:25:01;
  binding state active;
  hardware ethernet 24:24:24:24:24:24;
  uid "\001$$$$$$";
  client-hostname "36-ABCDEFGH-36-ABCDEFGH-36-ABCDEFGH-36-ABCDEFGH-36-ABCDEFGH-36";
}
lease ********** {
  starts 3 2004/09/29 20:25:01;
  ends 3 2004/09/29 20:25:01;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:02;
  ends 3 2004/09/29 20:25:02;
  binding state abandoned;
  client-hostname "33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33";
}
lease ********** {
  starts 3 2004/09/29 20:25:03;
  ends 3 2004/09/29 20:25:03;
  binding state abandoned;
  client-hostname "21-ABCDEFGH-21-ABCDEFGH-21-ABCDEFGH-21-ABCDEFGH-21-ABCDEFGH-21";
}
lease ********** {
  starts 3 2004/09/29 20:25:04;
  ends 3 2004/09/29 20:25:04;
  binding state abandoned;
  client-hostname "67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67";
}
lease ********** {
  starts 3 2004/09/29 20:25:06;
  ends 3 2004/09/29 20:25:06;
  binding state abandoned;
  client-hostname "45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45";
}
lease ********** {
  starts 3 2004/09/29 20:25:07;
  ends 3 2004/09/29 20:25:07;
  binding state abandoned;
  client-hostname "65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65";
}
lease ********** {
  starts 3 2004/09/29 20:25:08;
  ends 3 2004/09/29 20:25:08;
  binding state abandoned;
  client-hostname "42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42";
}
lease ********** {
  starts 3 2004/09/29 20:25:09;
  ends 3 2004/09/29 20:25:09;
  binding state abandoned;
  client-hostname "66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66";
}
lease ********** {
  starts 3 2004/09/29 20:25:11;
  ends 4 2004/09/30 08:25:11;
  binding state active;
  hardware ethernet 40:40:40:40:40:40;
  uid "\001@@@@@@";
  client-hostname "64-ABCDEFGH-64-ABCDEFGH-64-ABCDEFGH-64-ABCDEFGH-64-ABCDEFGH-64";
}
lease ********** {
  starts 3 2004/09/29 20:25:13;
  ends 4 2004/09/30 08:25:13;
  binding state active;
  hardware ethernet 31:31:31:31:31:31;
  uid "\001111111";
  client-hostname "49-ABCDEFGH-49-ABCDEFGH-49-ABCDEFGH-49-ABCDEFGH-49-ABCDEFGH-49";
}
lease ********** {
  starts 3 2004/09/29 20:25:13;
  ends 3 2004/09/29 20:25:13;
  binding state abandoned;
  client-hostname "68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68";
}
lease ********** {
  starts 3 2004/09/29 20:25:15;
  ends 4 2004/09/30 08:25:15;
  binding state active;
  hardware ethernet 45:45:45:45:45:45;
  uid "\001EEEEEE";
  client-hostname "69-ABCDEFGH-69-ABCDEFGH-69-ABCDEFGH-69-ABCDEFGH-69-ABCDEFGH-69";
}
lease ********** {
  starts 3 2004/09/29 20:25:16;
  ends 4 2004/09/30 08:25:16;
  binding state active;
  hardware ethernet 3d:3d:3d:3d:3d:3d;
  uid "\001======";
  client-hostname "61-ABCDEFGH-61-ABCDEFGH-61-ABCDEFGH-61-ABCDEFGH-61-ABCDEFGH-61";
}
lease ********** {
  starts 3 2004/09/29 20:25:17;
  ends 4 2004/09/30 08:25:17;
  binding state active;
  hardware ethernet 29:29:29:29:29:29;
  uid "\001))))))";
  client-hostname "41-ABCDEFGH-41-ABCDEFGH-41-ABCDEFGH-41-ABCDEFGH-41-ABCDEFGH-41";
}
lease ********** {
  starts 3 2004/09/29 20:25:17;
  ends 3 2004/09/29 20:25:17;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:19;
  ends 4 2004/09/30 08:25:19;
  binding state active;
  hardware ethernet 21:21:21:21:21:21;
  uid "\001!!!!!!";
  client-hostname "33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33";
}
lease ********** {
  starts 3 2004/09/29 20:25:20;
  ends 3 2004/09/29 20:25:20;
  binding state abandoned;
  client-hostname "67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67";
}
lease ********** {
  starts 3 2004/09/29 20:25:21;
  ends 3 2004/09/29 20:25:21;
  binding state abandoned;
  client-hostname "45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45";
}
lease ********** {
  starts 3 2004/09/29 20:25:23;
  ends 4 2004/09/30 08:25:23;
  binding state active;
  hardware ethernet 41:41:41:41:41:41;
  uid "\001AAAAAA";
  client-hostname "65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65";
}
lease ********** {
  starts 3 2004/09/29 20:25:24;
  ends 4 2004/09/30 08:25:24;
  binding state active;
  hardware ethernet 2a:2a:2a:2a:2a:2a;
  uid "\001******";
  client-hostname "42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42";
}
lease ********** {
  starts 3 2004/09/29 20:25:25;
  ends 4 2004/09/30 08:25:25;
  binding state active;
  hardware ethernet 42:42:42:42:42:42;
  uid "\001BBBBBB";
  client-hostname "66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66";
}
lease ********** {
  starts 3 2004/09/29 20:25:25;
  ends 3 2004/09/29 20:25:25;
  binding state abandoned;
  client-hostname "68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68";
}
lease ********** {
  starts 3 2004/09/29 20:25:26;
  ends 3 2004/09/29 20:25:26;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:28;
  ends 4 2004/09/30 08:25:28;
  binding state active;
  hardware ethernet 43:43:43:43:43:43;
  uid "\001CCCCCC";
  client-hostname "67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67";
}
lease ********** {
  starts 3 2004/09/29 20:25:29;
  ends 4 2004/09/30 08:25:29;
  binding state active;
  hardware ethernet 2d:2d:2d:2d:2d:2d;
  uid "\001------";
  client-hostname "45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45";
}
lease ********** {
  starts 3 2004/09/29 20:25:29;
  ends 3 2004/09/29 20:25:29;
  binding state abandoned;
  client-hostname "68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68";
}
lease ********** {
  starts 3 2004/09/29 20:25:30;
  ends 3 2004/09/29 20:25:30;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:32;
  ends 4 2004/09/30 08:25:32;
  binding state active;
  hardware ethernet 44:44:44:44:44:44;
  uid "\001DDDDDD";
  client-hostname "68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68";
}
lease ********** {
  starts 3 2004/09/29 20:25:32;
  ends 3 2004/09/29 20:25:32;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:33;
  ends 3 2004/09/29 20:25:33;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:34;
  ends 3 2004/09/29 20:25:34;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:35;
  ends 3 2004/09/29 20:25:35;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:36;
  ends 3 2004/09/29 20:25:36;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:37;
  ends 3 2004/09/29 20:25:37;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:38;
  ends 3 2004/09/29 20:25:38;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:39;
  ends 3 2004/09/29 20:25:39;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:40;
  ends 3 2004/09/29 20:25:40;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:41;
  ends 3 2004/09/29 20:25:41;
  binding state abandoned;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:42;
  ends 3 2004/09/29 20:25:42;
  binding state free;
  hardware ethernet 3f:3f:3f:3f:3f:3f;
  uid "\001??????";
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:42;
  ends 4 2004/09/30 08:25:42;
  binding state active;
  hardware ethernet 3f:3f:3f:3f:3f:3f;
  uid "\001??????";
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:24:52;
  ends 3 2004/09/29 20:24:52;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:24:53;
  ends 3 2004/09/29 20:24:53;
  binding state free;
  client-hostname "64-ABCDEFGH-64-ABCDEFGH-64-ABCDEFGH-64-ABCDEFGH-64-ABCDEFGH-64";
}
lease ********** {
  starts 3 2004/09/29 20:24:54;
  ends 3 2004/09/29 20:24:54;
  binding state free;
  client-hostname "65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65";
}
lease ********** {
  starts 3 2004/09/29 20:24:55;
  ends 3 2004/09/29 20:24:55;
  binding state free;
  client-hostname "66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66";
}
lease ********** {
  starts 3 2004/09/29 20:24:56;
  ends 3 2004/09/29 20:24:56;
  binding state free;
  client-hostname "67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67";
}
lease ********** {
  starts 3 2004/09/29 20:24:58;
  ends 3 2004/09/29 20:24:58;
  binding state free;
  client-hostname "68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68";
}
lease ********** {
  starts 3 2004/09/29 20:24:59;
  ends 3 2004/09/29 20:24:59;
  binding state free;
  client-hostname "69-ABCDEFGH-69-ABCDEFGH-69-ABCDEFGH-69-ABCDEFGH-69-ABCDEFGH-69";
}
lease ********** {
  starts 3 2004/09/29 20:25:01;
  ends 3 2004/09/29 20:25:01;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:02;
  ends 3 2004/09/29 20:25:02;
  binding state free;
  client-hostname "33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33-ABCDEFGH-33";
}
lease ********** {
  starts 3 2004/09/29 20:25:03;
  ends 3 2004/09/29 20:25:03;
  binding state free;
  client-hostname "21-ABCDEFGH-21-ABCDEFGH-21-ABCDEFGH-21-ABCDEFGH-21-ABCDEFGH-21";
}
lease ********** {
  starts 3 2004/09/29 20:25:04;
  ends 3 2004/09/29 20:25:04;
  binding state free;
  client-hostname "67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67";
}
lease ********** {
  starts 3 2004/09/29 20:25:06;
  ends 3 2004/09/29 20:25:06;
  binding state free;
  client-hostname "45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45";
}
lease ********** {
  starts 3 2004/09/29 20:25:07;
  ends 3 2004/09/29 20:25:07;
  binding state free;
  client-hostname "65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65-ABCDEFGH-65";
}
lease ********** {
  starts 3 2004/09/29 20:25:08;
  ends 3 2004/09/29 20:25:08;
  binding state free;
  client-hostname "42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42-ABCDEFGH-42";
}
lease ********** {
  starts 3 2004/09/29 20:25:09;
  ends 3 2004/09/29 20:25:09;
  binding state free;
  client-hostname "66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66-ABCDEFGH-66";
}
lease ********** {
  starts 3 2004/09/29 20:25:13;
  ends 3 2004/09/29 20:25:13;
  binding state free;
  client-hostname "68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68";
}
lease ********** {
  starts 3 2004/09/29 20:25:17;
  ends 3 2004/09/29 20:25:17;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:20;
  ends 3 2004/09/29 20:25:20;
  binding state free;
  client-hostname "67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67-ABCDEFGH-67";
}
lease ********** {
  starts 3 2004/09/29 20:25:21;
  ends 3 2004/09/29 20:25:21;
  binding state free;
  client-hostname "45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45-ABCDEFGH-45";
}
lease ********** {
  starts 3 2004/09/29 20:25:25;
  ends 3 2004/09/29 20:25:25;
  binding state free;
  client-hostname "68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68";
}
lease ********** {
  starts 3 2004/09/29 20:25:26;
  ends 3 2004/09/29 20:25:26;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:29;
  ends 3 2004/09/29 20:25:29;
  binding state free;
  client-hostname "68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68-ABCDEFGH-68";
}
lease ********** {
  starts 3 2004/09/29 20:25:30;
  ends 3 2004/09/29 20:25:30;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:32;
  ends 3 2004/09/29 20:25:32;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:33;
  ends 3 2004/09/29 20:25:33;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:34;
  ends 3 2004/09/29 20:25:34;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:35;
  ends 3 2004/09/29 20:25:35;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:36;
  ends 3 2004/09/29 20:25:36;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:37;
  ends 3 2004/09/29 20:25:37;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:38;
  ends 3 2004/09/29 20:25:38;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:39;
  ends 3 2004/09/29 20:25:39;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:40;
  ends 3 2004/09/29 20:25:40;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
lease ********** {
  starts 3 2004/09/29 20:25:41;
  ends 3 2004/09/29 20:25:41;
  binding state free;
  client-hostname "63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63-ABCDEFGH-63";
}
