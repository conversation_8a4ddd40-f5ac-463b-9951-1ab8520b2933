header-hostrecord,fqdn*,addresses,aliases,boot_file,boot_server,broadcast_address,comment,configure_for_dhcp,configure_for_dns,deny_bootp,disabled,domain_name,domain_name_servers,ignore_dhcp_param_request_list,ipv6_addresses,lease_time,mac_address,match_option,next_server,pxe_lease_time,pxe_lease_time_enabled,routers,ttl,view
hostrecord,hostrec1.test.com,*******,,,,,,Invalid,TRUE,,FALSE,,,,,,11:11:11:11:11:20,,,,,,,
hostrecord,hostrec2.test.com,*******,,,,,,TRUE,TRUE,Invalid,FALSE,,,,,,11:11:11:11:11:23,,,,,,,
hostrecord,hostrec3.test.com,*******,,,,,,TRUE,TRUE,,FALSE,,,,,,11:11:11:11:11:27,,,,,"********",,
