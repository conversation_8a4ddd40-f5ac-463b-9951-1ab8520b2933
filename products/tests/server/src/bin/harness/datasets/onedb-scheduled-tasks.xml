<DATABASE NAME="onedb" VERSION="4.x-aklein-2009-01-06-15-39">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task"/>
  <PROPERTY NAME="task_id" VALUE="4294967290"/>
  <PROPERTY NAME="type" VALUE="SCHEDULED"/>
  <PROPERTY NAME="scheduled_time" VALUE="2103154800"/>
  <PROPERTY NAME="submit_time" VALUE="1231358638"/>
  <PROPERTY NAME="execution_status" VALUE="COMPLETED"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="submitter" VALUE="admin"/>
  <PROPERTY NAME="admin_ref" VALUE=".com.infoblox.one.admin$admin"/>
  <PROPERTY NAME="admin_group_ref" VALUE=".admin-group"/>
  <PROPERTY NAME="is_network_insight_task" VALUE="false"/>
  <PROPERTY NAME="member" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task"/>
  <PROPERTY NAME="task_id" VALUE="4294967291"/>
  <PROPERTY NAME="type" VALUE="SCHEDULED"/>
  <PROPERTY NAME="scheduled_time" VALUE="2134690800"/>
  <PROPERTY NAME="submit_time" VALUE="1231358679"/>
  <PROPERTY NAME="execution_status" VALUE="PENDING"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="submitter" VALUE="admin"/>
  <PROPERTY NAME="admin_ref" VALUE=".com.infoblox.one.admin$admin"/>
  <PROPERTY NAME="admin_group_ref" VALUE=".admin-group"/>
  <PROPERTY NAME="is_network_insight_task" VALUE="false"/>
  <PROPERTY NAME="member" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_chunk"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="piece" VALUE="0"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.one.queued_task_link$4294967290.0.dns.bind_a$default.test01.testzone.net.********"/>
  <PROPERTY NAME="data" VALUE="2009-01-07 20:03:58.791Z [admin]: Created ARecord test01.testzone.net DnsView=default address=********: Set address=&quot;********&quot;,comment=&quot;A Record&quot;,disabled=False,discovered_data=[last_discovered=NULL,netbios_name=&quot;&quot;,os=&quot;&quot;],fqdn=&quot;test01.testzone.net&quot;,mac_address=&quot;&quot;,use_ttl=False,view=DnsView:default"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_chunk"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="piece" VALUE="0"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.one.queued_task$4294967290.0"/>
  <PROPERTY NAME="data" VALUE="&lt;?xml version='1.0' encoding='UTF-8'?&gt;&#xd;&#xa;&lt;SOAP-ENV:Envelope xmlns:SOAP-ENV=&quot;http://schemas.xmlsoap.org/soap/envelope/&quot; xmlns:xsi=&quot;http://www.w3.org/2001/XMLSchema-instance&quot; xmlns:xsd=&quot;http://www.w3.org/2001/XMLSchema&quot;&gt;&#xa;&lt;SOAP-ENV:Header&gt;&#xa;&lt;IBAPHeader xmlns:nsibap=&quot;urn:ibap.infoblox.com&quot; xsi:type=&quot;nsibap:IBAPHeader&quot;&gt;&lt;request_settings xmlns:nsibap=&quot;urn:ibap.infoblox.com&quot; xsi:type=&quot;nsibap:request_settings&quot;&gt;&lt;scheduling_info xmlns:nsibap=&quot;urn:ibap.infoblox.com&quot; xsi:type=&quot;nsibap:scheduling_info&quot;&gt;&lt;warnlevel xsi:type=&quot;xsd:string&quot;&gt;WARN&lt;/warnlevel&gt;&lt;scheduled_time xsi:type=&quot;xsd:dateTime&quot;&gt;2036-08-24T01:40:00.040Z&lt;/scheduled_time&gt;&lt;/scheduling_info&gt;&lt;mode xsi:type=&quot;xsd:string&quot;&gt;SCHEDULE&lt;/mode&gt;&lt;warnlevel xsi:type=&quot;xsd:string&quot;&gt;WARN&lt;/warnlevel&gt;&lt;/request_settings&gt;&lt;/IBAPHeader&gt;&#xa;&lt;/SOAP-ENV:Header&gt;&#xa;&lt;SOAP-ENV:Body&gt;&#xa;&lt;ns1:ObjectWrite xmlns:ns1=&quot;urn:ibap.infoblox.com&quot; SOAP-ENV:encodingStyle=&quot;http://schemas.xmlsoap.org/soap/encoding/&quot;&gt;&#xa;&lt;op xsi:type=&quot;xsd:string&quot;&gt;INSERT&lt;/op&gt;&#xa;&lt;object_type xsi:type=&quot;xsd:string&quot;&gt;ARecord&lt;/object_type&gt;&#xa;&lt;write_fields xmlns:ns2=&quot;http://schemas.xmlsoap.org/soap/encoding/&quot; xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[8]&quot;&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;address&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;********&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;mac_address&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;discovered_data&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns1:discovered_data&quot;&gt;&#xa;&lt;netbios_name xsi:type=&quot;xsd:string&quot;&gt;&lt;/netbios_name&gt;&#xa;&lt;os xsi:type=&quot;xsd:string&quot;&gt;&lt;/os&gt;&#xa;&lt;last_discovered xsi:type=&quot;xsd:dateTime&quot; xsi:nil=&quot;true&quot;/&gt;&#xa;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;disabled&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_ttl&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;comment&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;A Record&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;fqdn&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;test01.testzone.net&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;view&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns1:object_id&quot;&gt;&#xa;&lt;id xsi:type=&quot;xsd:string&quot;&gt;dns.view$._default&lt;/id&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;/write_fields&gt;&#xa;&lt;/ns1:ObjectWrite&gt;&#xa;&lt;/SOAP-ENV:Body&gt;&#xa;&lt;/SOAP-ENV:Envelope&gt;&#xa;"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_chunk"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="piece" VALUE="0"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.one.queued_task_link$4294967291.0.dns.host$default.net.testzone.test02"/>
  <PROPERTY NAME="data" VALUE="2009-01-07 20:04:39.212Z [admin]: Created HostAddress ._default.net.testzone.test02.********: Set address=&quot;********&quot;,boot_file=&quot;&quot;,boot_server=&quot;&quot;,broadcast_address=&quot;&quot;,configure_for_dhcp=True,custom_options=[],deny_bootp=False,discovered_data=[last_discovered=NULL,netbios_name=&quot;NetBIOS&quot;,os=&quot;OS&quot;],domain_name=&quot;&quot;,domain_name_servers=[],ignore_dhcp_param_request_list=False,mac_address=&quot;00-11-22-33-44-55&quot;,match_option=&quot;MAC_ADDRESS&quot;,next_server=&quot;&quot;,parent=HostRecord:._default.net.testzone.test02,pxe_lease_time_enabled=False,routers=[],use_bootp_options=False,use_broadcast_address=False,use_custom_options=False,use_deny_bootp=False,use_domain_name=False,use_domain_name_servers=False,use_ignore_dhcp_param_request_list=False,use_lease_time=False,use_pxe_lease_time=False,use_routers=False"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_chunk"/>
  <PROPERTY NAME="position" VALUE="1"/>
  <PROPERTY NAME="piece" VALUE="1"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.one.queued_task_link$4294967291.0.dns.host$default.net.testzone.test02"/>
  <PROPERTY NAME="data" VALUE="2009-01-07 20:04:39.212Z [admin]: Created HostAlias ._default.net.testzone.htest01.._default.net.testzone.test02: Set alias=&quot;htest01&quot;,parent=HostRecord:._default.net.testzone.test02"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_chunk"/>
  <PROPERTY NAME="position" VALUE="2"/>
  <PROPERTY NAME="piece" VALUE="2"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.one.queued_task_link$4294967291.0.dns.host$default.net.testzone.test02"/>
  <PROPERTY NAME="data" VALUE="2009-01-07 20:04:39.212Z [admin]: Created HostAlias ._default.net.testzone.htest02.._default.net.testzone.test02: Set alias=&quot;htest02&quot;,parent=HostRecord:._default.net.testzone.test02"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_chunk"/>
  <PROPERTY NAME="position" VALUE="3"/>
  <PROPERTY NAME="piece" VALUE="3"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.one.queued_task_link$4294967291.0.dns.host$default.net.testzone.test02"/>
  <PROPERTY NAME="data" VALUE="2009-01-07 20:04:39.212Z [admin]: Created HostRecord test02.testzone.net DnsView=default address=********: Set addresses=[HostAddress:._default.net.testzone.test02.********],aliases=[HostAlias:._default.net.testzone.htest01.._default.net.testzone.test02,HostAlias:._default.net.testzone.htest02.._default.net.testzone.test02],comment=&quot;Host Record&quot;,configure_for_dns=True,disabled=False,extensible_attributes=[],fqdn=&quot;test02.testzone.net&quot;,use_ttl=False,view=DnsView:default"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_chunk"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="piece" VALUE="0"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.one.queued_task$4294967291.0"/>
  <PROPERTY NAME="data" VALUE="&lt;?xml version='1.0' encoding='UTF-8'?&gt;&#xd;&#xa;&lt;SOAP-ENV:Envelope xmlns:SOAP-ENV=&quot;http://schemas.xmlsoap.org/soap/envelope/&quot; xmlns:xsi=&quot;http://www.w3.org/2001/XMLSchema-instance&quot; xmlns:xsd=&quot;http://www.w3.org/2001/XMLSchema&quot;&gt;&#xa;&lt;SOAP-ENV:Header&gt;&#xa;&lt;IBAPHeader xmlns:nsibap=&quot;urn:ibap.infoblox.com&quot; xsi:type=&quot;nsibap:IBAPHeader&quot;&gt;&lt;request_settings xmlns:nsibap=&quot;urn:ibap.infoblox.com&quot; xsi:type=&quot;nsibap:request_settings&quot;&gt;&lt;scheduling_info xmlns:nsibap=&quot;urn:ibap.infoblox.com&quot; xsi:type=&quot;nsibap:scheduling_info&quot;&gt;&lt;warnlevel xsi:type=&quot;xsd:string&quot;&gt;WARN&lt;/warnlevel&gt;&lt;scheduled_time xsi:type=&quot;xsd:dateTime&quot;&gt;2037-08-24T01:40:00.040Z&lt;/scheduled_time&gt;&lt;/scheduling_info&gt;&lt;mode xsi:type=&quot;xsd:string&quot;&gt;SCHEDULE&lt;/mode&gt;&lt;warnlevel xsi:type=&quot;xsd:string&quot;&gt;WARN&lt;/warnlevel&gt;&lt;/request_settings&gt;&lt;/IBAPHeader&gt;&#xa;&lt;/SOAP-ENV:Header&gt;&#xa;&lt;SOAP-ENV:Body&gt;&#xa;&lt;ns1:ObjectWrite xmlns:ns1=&quot;urn:ibap.infoblox.com&quot; SOAP-ENV:encodingStyle=&quot;http://schemas.xmlsoap.org/soap/encoding/&quot;&gt;&#xa;&lt;op xsi:type=&quot;xsd:string&quot;&gt;INSERT&lt;/op&gt;&#xa;&lt;object_type xsi:type=&quot;xsd:string&quot;&gt;HostRecord&lt;/object_type&gt;&#xa;&lt;write_fields xmlns:ns2=&quot;http://schemas.xmlsoap.org/soap/encoding/&quot; xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[9]&quot;&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;aliases&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[2]&quot;&gt;&#xa;&lt;item&gt;&#xa;&lt;object_type xsi:type=&quot;xsd:string&quot;&gt;HostAlias&lt;/object_type&gt;&#xa;&lt;write_fields xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[2]&quot;&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;parent&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns1:object_id&quot;&gt;&#xa;&lt;id xsi:type=&quot;xsd:string&quot;&gt;..&lt;/id&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;alias&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;htest01&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;/write_fields&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;object_type xsi:type=&quot;xsd:string&quot;&gt;HostAlias&lt;/object_type&gt;&#xa;&lt;write_fields xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[2]&quot;&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;parent&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns1:object_id&quot;&gt;&#xa;&lt;id xsi:type=&quot;xsd:string&quot;&gt;..&lt;/id&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;alias&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;htest02&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;/write_fields&gt;&#xa;&lt;/item&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;addresses&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[1]&quot;&gt;&#xa;&lt;item&gt;&#xa;&lt;object_type xsi:type=&quot;xsd:string&quot;&gt;HostAddress&lt;/object_type&gt;&#xa;&lt;write_fields xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[27]&quot;&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;address&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;********&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;parent&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns1:object_id&quot;&gt;&#xa;&lt;id xsi:type=&quot;xsd:string&quot;&gt;..&lt;/id&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;boot_file&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;boot_server&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;broadcast_address&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;deny_bootp&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;domain_name&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;ignore_dhcp_param_request_list&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;next_server&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;pxe_lease_time_enabled&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;mac_address&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;00-11-22-33-44-55&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;discovered_data&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns1:discovered_data&quot;&gt;&#xa;&lt;netbios_name xsi:type=&quot;xsd:string&quot;&gt;NetBIOS&lt;/netbios_name&gt;&#xa;&lt;os xsi:type=&quot;xsd:string&quot;&gt;OS&lt;/os&gt;&#xa;&lt;last_discovered xsi:type=&quot;xsd:dateTime&quot; xsi:nil=&quot;true&quot;/&gt;&#xa;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;configure_for_dhcp&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;true&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;custom_options&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[0]&quot;&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:t"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_chunk"/>
  <PROPERTY NAME="position" VALUE="1"/>
  <PROPERTY NAME="piece" VALUE="0"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.one.queued_task$4294967291.0"/>
  <PROPERTY NAME="data" VALUE="ype=&quot;xsd:string&quot;&gt;domain_name_servers&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[0]&quot;&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;match_option&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;MAC_ADDRESS&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;routers&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[0]&quot;&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_bootp_options&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_broadcast_address&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_custom_options&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_deny_bootp&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_domain_name&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_domain_name_servers&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_ignore_dhcp_param_request_list&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_lease_time&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_pxe_lease_time&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_routers&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;/write_fields&gt;&#xa;&lt;/item&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;configure_for_dns&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;true&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;disabled&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;use_ttl&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:boolean&quot;&gt;false&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;comment&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;Host Record&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;fqdn&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;xsd:string&quot;&gt;test02.testzone.net&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;view&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns1:object_id&quot;&gt;&#xa;&lt;id xsi:type=&quot;xsd:string&quot;&gt;dns.view$._default&lt;/id&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;item&gt;&#xa;&lt;field xsi:type=&quot;xsd:string&quot;&gt;extensible_attributes&lt;/field&gt;&#xa;&lt;value xsi:type=&quot;ns2:Array&quot; ns2:arrayType=&quot;ns1:SOAPStruct[0]&quot;&gt;&#xa;&lt;/value&gt;&#xa;&lt;/item&gt;&#xa;&lt;/write_fields&gt;&#xa;&lt;/ns1:ObjectWrite&gt;&#xa;&lt;/SOAP-ENV:Body&gt;&#xa;&lt;/SOAP-ENV:Envelope&gt;&#xa;"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_link"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="action" VALUE="INSERT"/>
  <PROPERTY NAME="queued_task" VALUE="4294967290.0"/>
  <PROPERTY NAME="changed_object_id" VALUE="dns.bind_a$._default.net.testzone,test01,********"/>
  <PROPERTY NAME="locking_cookie" VALUE="dns.bind_a$default.test01.testzone.net.********"/>
  <PROPERTY NAME="object_type" VALUE="ARecord"/>
  <PROPERTY NAME="object_name" VALUE="test01.testzone.net"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_link"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="action" VALUE="INSERT"/>
  <PROPERTY NAME="queued_task" VALUE="4294967291.0"/>
  <PROPERTY NAME="changed_object_id" VALUE="dns.host$._default.net.testzone.test02"/>
  <PROPERTY NAME="locking_cookie" VALUE="dns.host$default.net.testzone.test02"/>
  <PROPERTY NAME="object_type" VALUE="HostRecord"/>
  <PROPERTY NAME="object_name" VALUE="test02.testzone.net"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.csv_import_task"/>
  <PROPERTY NAME="import_id" VALUE="1"/>
  <PROPERTY NAME="status" VALUE="RUNNING"/>
  <PROPERTY NAME="lines_processed" VALUE="0"/>
  <PROPERTY NAME="lines_failed" VALUE="0"/>
  <PROPERTY NAME="lines_warning" VALUE="0"/>
  <PROPERTY NAME="lines_total" VALUE="0"/>
  <PROPERTY NAME="start_ts" VALUE="1335624922"/>
  <PROPERTY NAME="operation" VALUE="UPDATE"/>
  <PROPERTY NAME="update_method" VALUE="OVERRIDE"/>
  <PROPERTY NAME="separator" VALUE="COMMA"/>
  <PROPERTY NAME="on_error" VALUE="STOP"/>
  <PROPERTY NAME="parent" VALUE="."/>
  <PROPERTY NAME="admin" VALUE=".com.infoblox.one.admin$admin"/>
  <PROPERTY NAME="file_name" VALUE="Allzones.csv"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.csv_import_task"/>
  <PROPERTY NAME="import_id" VALUE="2"/>
  <PROPERTY NAME="status" VALUE="PENDING"/>
  <PROPERTY NAME="lines_processed" VALUE="0"/>
  <PROPERTY NAME="lines_failed" VALUE="0"/>
  <PROPERTY NAME="lines_warning" VALUE="0"/>
  <PROPERTY NAME="lines_total" VALUE="0"/>
  <PROPERTY NAME="start_ts" VALUE="1335624922"/>
  <PROPERTY NAME="operation" VALUE="UPDATE"/>
  <PROPERTY NAME="update_method" VALUE="OVERRIDE"/>
  <PROPERTY NAME="separator" VALUE="COMMA"/>
  <PROPERTY NAME="on_error" VALUE="STOP"/>
  <PROPERTY NAME="parent" VALUE="."/>
  <PROPERTY NAME="admin" VALUE=".com.infoblox.one.admin$admin"/>
  <PROPERTY NAME="file_name" VALUE="Allzones.csv"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.csv_import_task"/>
  <PROPERTY NAME="import_id" VALUE="3"/>
  <PROPERTY NAME="status" VALUE="COMPLETED"/>
  <PROPERTY NAME="lines_processed" VALUE="0"/>
  <PROPERTY NAME="lines_failed" VALUE="0"/>
  <PROPERTY NAME="lines_warning" VALUE="0"/>
  <PROPERTY NAME="lines_total" VALUE="0"/>
  <PROPERTY NAME="start_ts" VALUE="1335624922"/>
  <PROPERTY NAME="operation" VALUE="UPDATE"/>
  <PROPERTY NAME="update_method" VALUE="OVERRIDE"/>
  <PROPERTY NAME="separator" VALUE="COMMA"/>
  <PROPERTY NAME="on_error" VALUE="STOP"/>
  <PROPERTY NAME="parent" VALUE="."/>
  <PROPERTY NAME="admin" VALUE=".com.infoblox.one.admin$admin"/>
  <PROPERTY NAME="file_name" VALUE="Allzones.csv"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.csv_import_task"/>
  <PROPERTY NAME="import_id" VALUE="4"/>
  <PROPERTY NAME="status" VALUE="UPLOADED"/>
  <PROPERTY NAME="lines_processed" VALUE="0"/>
  <PROPERTY NAME="lines_failed" VALUE="0"/>
  <PROPERTY NAME="lines_warning" VALUE="0"/>
  <PROPERTY NAME="lines_total" VALUE="0"/>
  <PROPERTY NAME="start_ts" VALUE="1335624922"/>
  <PROPERTY NAME="operation" VALUE="UPDATE"/>
  <PROPERTY NAME="update_method" VALUE="OVERRIDE"/>
  <PROPERTY NAME="separator" VALUE="COMMA"/>
  <PROPERTY NAME="on_error" VALUE="STOP"/>
  <PROPERTY NAME="parent" VALUE="."/>
  <PROPERTY NAME="admin" VALUE=".com.infoblox.one.admin$admin"/>
  <PROPERTY NAME="file_name" VALUE="Allzones.csv"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task"/>
  <PROPERTY NAME="task_id" VALUE="1"/>
  <PROPERTY NAME="type" VALUE="SCHEDULED"/>
  <PROPERTY NAME="scheduled_time" VALUE="1491997222"/>
  <PROPERTY NAME="submit_time" VALUE="1491996943"/>
  <PROPERTY NAME="execution_status" VALUE="PENDING"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="submitter" VALUE="admin"/>
  <PROPERTY NAME="admin_ref" VALUE=".com.infoblox.one.admin$admin"/>
  <PROPERTY NAME="admin_group_ref" VALUE=".admin-group"/>
  <PROPERTY NAME="is_network_insight_task" VALUE="true"/>
  <PROPERTY NAME="predecessor_task" VALUE=""/>
  <PROPERTY NAME="member" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task"/>
  <PROPERTY NAME="task_id" VALUE="2"/>
  <PROPERTY NAME="type" VALUE="SCHEDULED"/>
  <PROPERTY NAME="scheduled_time" VALUE="1491997308"/>
  <PROPERTY NAME="submit_time" VALUE="1491996965"/>
  <PROPERTY NAME="execution_status" VALUE="COMPLETED"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="submitter" VALUE="admin"/>
  <PROPERTY NAME="admin_ref" VALUE=".com.infoblox.one.admin$admin"/>
  <PROPERTY NAME="admin_group_ref" VALUE=".admin-group"/>
  <PROPERTY NAME="is_network_insight_task" VALUE="true"/>
  <PROPERTY NAME="predecessor_task" VALUE=""/>
  <PROPERTY NAME="member" VALUE="0"/>
</OBJECT>
</DATABASE>
