[source__ib_discovery_ipaddr_activity_extractions]
FIELDS = "TIMESTAMP_USER_HOST_PROCESS_PID_INFO_PREFIX","NETWORK_VIEW","IPADDR","IPAD<PERSON>_MASK","IN_USE_FLAG","MAC_DUID","DISCOVERED_MAC_DUID","DISCOVERED_NAME","DEVICE_TYPE","DEVICE_VENDOR","DEVICE_MODEL","COMPONENT_TYPE","COMPONENT_NAME","COMPONENT_PORT"
DELIMS = ","

[source__ib_ddos_ip_rule_stats_extractions]
FIELDS = "TIMESTAMP","SOURCE_IP","RULE_SID","ACTIVE_COUNT","SOURCE_PORT","NAT_STATUS","BLOCK_START","BLOCK_END"
DELIMS = ","

[dns_view_report_csv_extractions]
FIELDS = "timestamp","view","members","zones_forward","zones_ipv4","zones_ipv6","zones_signed","rr_a","rr_aaaa","rr_cname","rr_dname","rr_dnskey","rr_ds","rr_mx","rr_naptr","rr_nsec","rr_nsec3param","rr_nsec3","rr_ns","rr_ptr","rr_rrsig","rr_soa","rr_srv","rr_txt","rr_other","rr_total","hosts","rr_lbdn"
DELIMS = ","

[fingerprint_device_class_lookup]
filename = fingerprint_device_class_map.csv

[ipaddr_mask_lookup]
filename = ipaddr_mask_lookup.csv

[os_number_fingerprint_lookup]
filename = os_number_fingerprint_map.csv

[dhcp_ranges_report_csv_extractions]
FIELDS = "timestamp","view","start_address","end_address","protocol","members","address_total","dynamic_hosts","static_hosts","dhcp_hosts","dhcp_utilization","dhcp_utilization_status","ms_servers"
DELIMS = ","

[ipam_networks_report_csv_extractions]
FIELDS = "timestamp","view","address","cidr","protocol","members","address_total","address_assignable","address_reserved","address_alloc","address_unalloc","address_assigned","address_conflicts","address_unmanaged","allocation","utilization"
DELIMS = ","

[dns_viewkey_displayname_lookup]
filename = dns_viewkey_displayname.csv

[dhcp_perf_csv_extractions]
FIELDS = "timestamp","protocol","dhcpv4discover","dhcpv4offer","dhcpv4request","dhcpv4decline","dhcpv4ack","dhcpv4nak","dhcpv4inform","dhcpv4release","dhcpv4leasequery","dhcpv4leaseunassigned","dhcpv4leaseunknown","dhcpv4leaseactive"
DELIMS = ","

[dhcpv6_perf_csv_extractions]
FIELDS = "timestamp","protocol","dhcpv6solicit","dhcpv6advertise","dhcpv6request","dhcpv6renew","dhcpv6rebind","dhcpv6reply","dhcpv6release","dhcpv6decline","dhcpv6information_request","dhcpv6relay_forward","dhcpv6relay_reply","dhcpv6confirm","dhcpv6reconfigure","dhcpv6leasequery","dhcpv6leasequery_reply"
DELIMS = ","

[analytics_rpz_lookup]
filename = analytics_rpz_lookup.csv

[nios_member_ip_lookup]
filename = nios_member_ip.csv

[atp_rule_sid_lookup]
filename = atp_rule_sid_lookup.csv

[network_ea_lookup_csv]
filename = network.csv

[source__ib_discovery_port_capacity_extractions]
FIELDS = "TIMESTAMP","NETWORK_VIEW","DEVICE_NAME","DEVICE_MGMT_IP","DEVICE_TYPE","TOTAL_AVAIL_COUNT","ADM_DN_OP_DN_COUNT","ADM_UP_OP_UP_COUNT","ADM_UP_OP_DN_COUNT"
DELIMS = ","

[idns_res_avail_csv_extractions]
FIELDS = "timestamp","monitor","resource","available","unavailable"
DELIMS = ","

[idns_res_pool_avail_csv_extractions]
FIELDS = "timestamp","pool","available","unavailable"
DELIMS = ","

[idns_res_resp_csv_extractions]
FIELDS = "timestamp","resource","response_count"
DELIMS = ","

[dns_zone_report_csv_extractions]
FIELDS = "timestamp","view","zone_name","zone_format","signed","primary","ms_primary","rr_a","rr_aaaa","rr_cname","rr_dname","rr_dnskey","rr_ds","rr_mx","rr_naptr","rr_nsec","rr_nsec3param","rr_nsec3","rr_ns","rr_ptr","rr_rrsig","rr_soa","rr_srv","rr_txt","rr_other","rr_total","hosts","rr_lbdn"
DELIMS = ","

[dhcp_networks_report_csv_extractions]
FIELDS = "timestamp","view","address","cidr","protocol","members","ranges","address_total","dynamic_hosts","static_hosts","dhcp_hosts","dhcp_utilization","dhcp_utilization_status","ms_servers"
DELIMS = ","

[resource_pool_ea_lookup_csv]
filename = idns_resources.csv

[pool_ea_lookup_csv]
filename = idns_pools.csv

[source__ib_cloud_vm_address_history_extractions]
FIELDS = "timestamp","address","action","address_type","mac_address","port_id","cnames","fqdn","vm_name","network","cidr","network_view","tenant_id","location","vlan_id","application_type","private_hostname","public_hostname","private_address","public_address","elastic_address","interface_name","mgmt_platform","vm_vpc_id","vm_vpc_name","vm_hostname","vm_vpc_address","vm_vpc_cidr","is_primary_ifc","interface_name"
DELIMS = ","

[source__ib_dns_query_top_rpz_hit_extractions]
FIELDS = "TIMESTAMP","VIEW","POSTNAT_IP","POSTNAT_PORT","PRENAT_IP","RPZ_SEVERITY","DOMAIN_NAME","RPZ_QNAME","MITIGATION_ACTION","REDIRECTION_RECORD","IMSI","IMEI","MSISDN"
DELIMS = ","

[source__ib_user_user_login_extractions]
FIELDS = "timestamp","user_name","domain","login_time","logout_time","ip_address","status","last_active","last_updated"
DELIMS = ","

[users_timeout_value_lookup]
filename = users_timeout_value_map.csv

[source__ib_dns_reclamation_extractions]
FIELDS = "timestamp","view","zone_name","rr_reclaimed"
DELIMS = ","

[source__ib_ecosystem_subscription_subscription_data_extractions]
FIELDS = "timestamp","username","domainname","cisco_ise_ssid","port_vlan_name","port_vlan_number","cisco_ise_session_state","cisco_ise_endpoint_profile","cisco_ise_security_group","last_discovered_timestamp","ea_eps_status","ip_address","guid"
DELIMS = ","

[analytics_rpz_lookup]
filename = analytics_rpz_lookup.csv

[source__ib_license_license_pool_utilizations_extractions]
FIELDS = "timestamp","license_pool","license_count","utilization"
DELIMS = ","

[source__ib_ecosystem_publish_publish_data_extractions]
FIELDS = "timestamp","ip_address","notification_target","notification_action","contents"
DELIMS = ","

[license_type_lookup]
filename = license_type_lookup.csv
