[indexAndForward]
index = false

[tcpout]
defaultGroup = ib_group
forwardedindex.filter.disable = true
indexAndForward = false
sslRootCAPath = /opt/splunksearchhead/etc/certs/cacert.pem
clientCert = /opt/splunksearchhead/etc/certs/ibx-searchhead.pem
sslPassword = password
sslVerifyServerCert = true
sslCommonNameToCheck = infoblox-indexer
sslAltNameToCheck = infoblox-indexer
compressed = true

[tcpout:ib_group]
server = 10.0.0.2:9997,10.0.0.3:9997,10.0.0.4:9997
