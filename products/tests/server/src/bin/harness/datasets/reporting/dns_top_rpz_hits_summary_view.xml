<dashboard script="top_rpz_hits_drilldown.js" stylesheet="inpage_drilldown.css">
  <label/>
  <row>
    <table id="master">
      <option name="drilldown">row</option>
      <option name="link.visible">false</option>
      <searchName>search-A-.-DNS_Top_RPZ_Hits</searchName>
    </table>
  </row>
  <row>
    <table id="user_details">
      <title>User History for IP Address = </title>
      <option name="drilldown">off</option>
      <option name="link.visible">false</option>
      <searchTemplate>sourcetype=ib:reserved1 source=ib:user:user_login index=ib_security earliest=-1d | eval TIMEOUT_VALUE=if(isnull(TIMEOUT_VAL),18000,TIMEOUT_VAL*60) | where ip_address="%(condition)s" | eval last_activeEpoch=if(isnum(last_active), last_active, strptime(last_active, "%Y-%m-%d %H:%M:%S")) | eventstats latest(last_activeEpoch) as l_last_active by user_name, ip_address, login_time | eval status=if((last_activeEpoch=l_last_active) AND (status=="ACTIVE") AND ((last_activeEpoch+TIMEOUT_VALUE)&lt;now()),"TIMEOUT",status) | sort -_time | eval last_active=if(isnum(last_active), strftime(last_active, "%Y-%m-%d %H:%M:%S"), last_active) | eval last_updated=if(isnum(last_updated), strftime(last_updated, "%Y-%m-%d %H:%M:%S"), last_updated) | eval logout_time=if(isnum(logout_time), strftime(logout_time, "%Y-%m-%d %H:%M:%S"), logout_time) | eval login_time=if(isnum(login_time), strftime(login_time, "%Y-%m-%d %H:%M:%S"), login_time) | rename timestamp as Time, user_name as "User Name", login_time as "First Seen", logout_time as "Logout Time", last_active as "Last Seen", last_updated as "Last Updated", ip_address as "IP Address", domain as "Domain", status as "User Status",  | table "Last Updated" "User Name" "Domain" "IP Address" "First Seen" "Logout Time" "Last Seen" "User Status" </searchTemplate>
    </table>
  </row>
  <row>
    <html id="auxiliary">
      <input id="auxiliarycolumnname" type="hidden" value="row.Client ID"/>
    </html>
  </row>
  <row>
    <table id="threat_details">
      <title>RPZ Threat Details for </title>
      <option name="drilldown">off</option>
      <option name="link.visible">false</option>
      <searchTemplate>| fetchthreatstopdetails %(param_value)s %(condition)s | eval rpz_rule="%(condition)s | " + name | convert ctime(first_identified) as "First Identified" | rename rpz_rule as "RPZ Rule", short_description as "Short Description", public_description as "Description" | table "RPZ Rule", "First Identified", "Short Description", "Description"</searchTemplate>
    </table>
  </row>
  <row>
    <html id="auxiliary">
      <input id="auxiliarycolumnname" type="hidden" value="row.RPZ Entry"/>
    </html>
  </row>
  <row>
    <table id="lease_history_details">
      <title>DHCP Lease History for Client ID = </title>
      <option name="drilldown">off</option>
      <option name="link.visible">false</option>
      <searchTemplate>sourcetype=ib:dhcp:lease_history index=ib_dhcp_lease_history dhcpd OR dhcpdv6 r-l-e earliest=-1d | eval Protocol=if(PROTO=="dhcpdv6","IPV6","IPV4") | eval LEASE_START=strftime(START_EPOCH,"%Y-%m-%d %H:%M:%S") | eval LEASE_END=strftime(END_EPOCH,"%Y-%m-%d %H:%M:%S") | eval FINGER_PRINT=if(isnull(OS_NUMBER),FP,SFP) | where LEASE_IP="%(condition)s" | rename host as "Member", ACTION as "Action", LEASE_IP as "Lease IP", MAC_DUID as "MAC/DUID", MEMBER_IP as "Member IP", OPTION12HOST as "Host Name", LEASE_START as "Lease Start", LEASE_END as "Lease End", FINGER_PRINT as "Fingerprint" | convert ctime(_time) as Time | table Time, Member, "Member IP", Protocol, Action, "Lease IP", "MAC/DUID", "Host Name", "Lease Start", "Lease End", "Fingerprint"</searchTemplate>
    </table>
  </row>
  <row>
    <html id="auxiliary">
      <input id="auxiliarycolumnname" type="hidden" value="row.Client ID"/>
    </html>
  </row>
</dashboard>
