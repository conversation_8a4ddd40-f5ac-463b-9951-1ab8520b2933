[indexAndForward]
index = false

[tcpout]
forwardedindex.filter.disable = true
maxQueueSize = 1000
useACK = true
sslRootCAPath = /opt/splunk/etc/certs/cacert.pem
clientCert = /opt/splunk/etc/certs/ibx-master.pem
sslPassword = password
sslVerifyServerCert = true
sslCommonNameToCheck = infoblox-indexer
sslAltNameToCheck = infoblox-indexer
compressed = true

[tcpout:ib_group]
server = 10.0.0.2:9997,10.0.0.3:9997,10.0.0.4:9997
