# Version 6.3.1

# Modify the following line to suit the location of your Splunk install.
# If unset, <PERSON><PERSON><PERSON><PERSON> will use the parent of the directory this configuration
# file was found in
#
SPLUNK_HOME=/opt/splunksearchhead

# By default, Splunk stores its indexes under SPLUNK_HOME in the
# var/lib/splunk subdirectory. This can be overridden
# here:
#
# SPLUNK_DB=/home/<USER>/build-home/ember/var/lib/splunk

# Splunkd daemon name
SPLUNK_SERVER_NAME=splunkd

# Splunkweb daemon name
SPLUNK_WEB_NAME=splunkweb

# If SPLUNK_OS_USER is set, then Splunk service will only start
# if the 'splunk [re]start [splunkd]' command is invoked by a user who
# is, or can effectively become via setuid(2), $SPLUNK_OS_USER.
# (This setting can be specified as username or as UID.)
#
# SPLUNK_OS_USER
