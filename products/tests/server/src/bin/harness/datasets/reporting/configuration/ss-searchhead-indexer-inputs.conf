

[default]

[splunktcp-ssl:9997]


[script://$SPLUNK_HOME/bin/scripts/reporting_log_monitor_data]
interval = 60
source = ib:system
sourcetype = ib:system
host = vm-11-84.inca.inoblox.com
index = ib_system
_TCP_ROUTING = ib_group


[monitor:///infoblox/var/audit.log*]
sourcetype = ib:audit
host = vm-11-84.inca.inoblox.com
index = ib_audit
_TCP_ROUTING = ib_group


[monitor:///infoblox/var/reporting/syslog_filtered.log*]
sourcetype = ib:syslog
host = vm-11-84.inca.inoblox.com
index = ib_syslog
_TCP_ROUTING = ib_group


[script://$SPLUNK_HOME/bin/scripts/reporting_system_capacity_objects_wrapper.sh]
interval = 60
source = ib:system_capacity:objects
sourcetype = ib:system_capacity:objects
host = vm-11-84.inca.inoblox.com
index = ib_system_capacity
_TCP_ROUTING = ib_group

[SSL]
cipherSuite = AES256-SHA:+AES128-SHA:+DHE-RSA-AES256-SHA:+DHE-RSA-AES128-SHA
password = $1$rspMoVk6sKFt
requireClientCert = true
rootCA = /opt/splunk/etc/certs/cacert.pem
serverCert = /opt/splunk/etc/certs/ibx-indexer.pem
supportSSLV3Only = true




