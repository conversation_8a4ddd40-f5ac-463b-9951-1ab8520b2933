<dashboard>
  <label>Global Pie Chart Report</label>
  <row>
    <panel>
      <title>Global Pie Chart Panel</title>
      <chart>
        <option name="charting.chart">pie</option>
        <option name="charting.data.preview">true</option>
        <option name="charting.legend.labelStyle.overflowMode">ellipsisNone</option>
        <search>
          <query>source=ib:ddos:ip_rule_stats index=ib_security earliest=-1y | lookup atp_rule_sid_lookup RULE_SID output RULE_DESCRIPTION, DNST_CATEGORY | append [ search source=* index=ib_dns earliest=-1y | eval JOIN_FIELD=1 | join JOIN_FIELD [ | inputlookup analytics_rpz_lookup | eval JOIN_FIELD=1 ] | where like(RPZ_QNAME, "%" + ANALYTICS_RPZ) | table TOTAL_COUNT, CLIENT, MITIGATION_ACTION, ANALYTICS_RPZ, _time | where MIT<PERSON>ATION_ACTION != "ER" | eval DNST_CATEGORY="Detected by Analytics Engine" | eval RULE_DESCRIPTION=ANALYTICS_RPZ | stats sum(TOTAL_COUNT) as ACTIVE_COUNT by CLIENT, DNST_CATEGORY, RULE_DESCRIPTION, ANALYTICS_RPZ, _time | eval SOURCE_IP=CLIENT ] | stats sum(ACTIVE_COUNT) as ACTIVE_COUNT_SUM by RULE_DESCRIPTION, DNST_CATEGORY | sort -ACTIVE_COUNT_SUM | head 0&lt;1 | eventstats sum(ACTIVE_COUNT_SUM) as EVENTS_TOTAL | eval Percentage=round(ACTIVE_COUNT_SUM*100/EVENTS_TOTAL, 2) | rename DNST_CATEGORY as Category, Percentage as "Category%", RULE_DESCRIPTION as Description, ACTIVE_COUNT_SUM as Events | stats sum(Events) as Events by Category | table Category, Events</query>
        </search>
      </chart>
    </panel>
  </row>
</dashboard>
