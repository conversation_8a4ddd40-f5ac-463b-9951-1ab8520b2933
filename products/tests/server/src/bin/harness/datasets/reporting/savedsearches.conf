[test_reporting_alert]
description = test reporting alert
search = index=ib_system_summary report=si_cpu_usage | timechart bins=1000 avg(CPU_PERCENT) by orig_host where max in top5 useother=f | interpolate 1200

[test_reporting_alert (Alert 1)]
enableSched = 1
cron_schedule = 3-59/30 * * * *
action.email.reportServerEnabled = 0
alert.track = 1
alert.suppress = 0
alert.severity = 3
action.email = 1
action.email.to = 
action.email.format = csv
action.email.sendcsv = 1
action.email.sendresults = 1
action.send_syslog = 1
action.send_snmp = 1
search = index=ib_system_summary report=si_cpu_usage earliest=-64m@m latest=-34m@m | stats avg(CPU_PERCENT) as CPU_PERCENT by _time, orig_host | eval marker="Current" | sort -_time | eval Time=strftime(_time, "%%Y-%%m-%%d %%H:%%M:%%S %%Z") | rename orig_host as Member, CPU_PERCENT as Utilization | table Time, Member, Utilization
counttype = number of events
quantity = 0
relation = greater than

[test_reporting_alert (Alert)]
description = test reporting alert
search = index=ib_system_summary report=si_cpu_usage | timechart bins=1000 avg(CPU_PERCENT) by orig_host where max in top5 useother=f | interpolate 1200

[test_reporting_alert (Alert) (Alert)]
enableSched = 1
cron_schedule = 3-59/30 * * * *
action.email.reportServerEnabled = 0
alert.track = 1
alert.suppress = 0
alert.severity = 3
action.email = 1
action.email.to = 
action.email.format = csv
action.email.sendcsv = 1
action.email.sendresults = 1
action.send_syslog = 1
action.send_snmp = 1
search = index=ib_system_summary report=si_cpu_usage earliest=-64m@m latest=-34m@m | stats avg(CPU_PERCENT) as CPU_PERCENT by _time, orig_host | eval marker="Current" | sort -_time | eval Time=strftime(_time, "%%Y-%%m-%%d %%H:%%M:%%S %%Z") | rename orig_host as Member, CPU_PERCENT as Utilization | table Time, Member, Utilization
counttype = number of events
quantity = 0
relation = greater than
