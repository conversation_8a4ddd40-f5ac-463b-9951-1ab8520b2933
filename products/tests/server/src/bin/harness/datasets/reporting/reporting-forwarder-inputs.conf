host = infoblox.localdomain

[monitor://$SPLUNK_HOME/var/log/splunk/web_access.log]
disabled = true

[monitor://$SPLUNK_HOME/var/log/splunk/web_service.log]
disabled = true

[monitor://$SPLUNK_HOME/var/log/splunk/searchhistory.log]
disabled = true

[monitor://$SPLUNK_HOME/var/log/splunk/splunklogger.log]
disabled = true

[fschange:$SPLUNK_HOME/etc]
disabled = true

[monitor:///infoblox/var/audit.log*]
sourcetype = ib:audit
host = infoblox.localdomain
index = ib_audit
blacklist = \.(?:passive|idx)$
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/cloud_vm_address.csv*]
source = ib:cloud:vm_address_history
sourcetype = ib:reserved2
host = infoblox.localdomain
index = ib_cloud
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/reporting_ddns.log*]
sourcetype = ib:ddns
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms_reporting_ddns.log*]
sourcetype = ib:ddns
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/reporting_dhcp_lease_history.log*]
sourcetype = ib:dhcp:lease_history
host = infoblox.localdomain
index = ib_dhcp_lease_history
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/reporting_ms_dhcp_lease_history.log*]
sourcetype = ib:dhcp:lease_history
host = infoblox.localdomain
index = ib_dhcp_lease_history
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dhcp_perf.csv]
sourcetype = ib:dhcp:message
host = infoblox.localdomain
index = ib_dhcp
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dhcpv6_perf.csv]
sourcetype = ib:dhcp:message
host = infoblox.localdomain
index = ib_dhcp
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms_dhcp_perf.csv]
sourcetype = ib:dhcp:message
host = infoblox.localdomain
index = ib_dhcp
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dhcp_networks_report.csv]
sourcetype = ib:dhcp:network
host = infoblox.localdomain
index = ib_dhcp
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dhcp_ranges_report.csv]
sourcetype = ib:dhcp:range
host = infoblox.localdomain
index = ib_dhcp
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/discovery_ipaddr_activity.log*]
source = ib:discovery:ipaddr_activity
sourcetype = ib:reserved2
host = infoblox.localdomain
index = ib_discovery
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/discovery_port_capacity.csv]
source = ib:discovery:port_capacity
sourcetype = ib:reserved2
host = infoblox.localdomain
index = ib_discovery
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/discovery_end_host_activity.log*]
source = ib:discovery:end_host_activity
sourcetype = ib:reserved2
host = infoblox.localdomain
index = ib_discovery
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/discovery_network_device_inventory.csv]
source = ib:discovery:device_inventory
sourcetype = ib:reserved2
host = infoblox.localdomain
index = ib_discovery
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/discovery_device_components.csv]
source = ib:discovery:device_components
sourcetype = ib:reserved2
host = infoblox.localdomain
index = ib_discovery
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/discovery_switch_port_capacity.csv]
source = ib:discovery:switch_port_capacity
sourcetype = ib:reserved2
host = infoblox.localdomain
index = ib_discovery
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/device_advisor_reporter.csv]
source = ib:discovery:device_advisor
sourcetype = ib:reserved2
host = infoblox.localdomain
index = ib_discovery
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_dns_resp_latency_wrapper.sh]
interval = 60
source = ib:dns:perf
sourcetype = ib:dns:perf
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-top-name.txt]
sourcetype = ib:dns:query:top_requested_domain_names
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-top-name.txt]
sourcetype = ib:dns:query:top_requested_domain_names
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/cache-hits-misses.txt]
sourcetype = ib:dns:query:cache_hit_rate
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-cache-hits-misses.txt]
sourcetype = ib:dns:query:cache_hit_rate
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-names-and-queries.txt]
sourcetype = ib:dns:query:by_member
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-names-and-queries.txt]
sourcetype = ib:dns:query:by_member
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-top-rr-type.txt]
sourcetype = ib:dns:query:qps
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-top-rr-type.txt]
sourcetype = ib:dns:query:qps
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-top-client.txt]
sourcetype = ib:dns:query:top_clients
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-top-client.txt]
sourcetype = ib:dns:query:top_clients
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-top-client-per-zone.txt]
source = ib:dns:query:top_clients_per_zone
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-top-client-per-fqdn.txt]
source = ib:dns:query:top_clients_per_domain
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-top-client-per-fqdn.txt]
source = ib:dns:query:top_clients_per_domain
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-top-client-per-zone.txt]
source = ib:dns:query:top_clients_per_zone
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-nxdomain-top-name.txt]
source = ib:dns:query:top_nxdomain_query
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-nxdomain-top-name.txt]
source = ib:dns:query:top_nxdomain_query
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-servfail-sent-top-name.txt]
source = ib:dns:query:top_failed
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-servfail-sent-top-name.txt]
source = ib:dns:query:top_failed
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-servfail-received-top-name.txt]
source = ib:dns:query:top_received
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-servfail-received-top-name.txt]
source = ib:dns:query:top_received
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-recursion-timeout-top-name.txt]
source = ib:dns:query:top_timeout
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-recursion-timeout-top-name.txt]
source = ib:dns:query:top_timeout
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_dns_stats_wrapper.sh]
interval = 60
source = ib:dns:stats
sourcetype = ib:dns:stats
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-ip-block-group.txt]
source = ib:dns:query:ip_block_group
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms-query-ip-block-group.txt]
source = ib:dns:query:ip_block_group
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dot_doh_stats.csv]
source = ib:dns:dot_doh_stats
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dns_reclamation.csv]
source = ib:dns:reclamation
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/publish_data.csv*]
source = ib:ecosystem_publish:publish_data
sourcetype = ib:reserved1
host = infoblox.localdomain
index = ib_ecosystem_publish
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/subscription_data.csv*]
source = ib:ecosystem_subscription:subscription_data
sourcetype = ib:reserved1
host = infoblox.localdomain
index = ib_ecosystem_subscription
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/idns_res_avail.csv]
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dtc
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/idns_res_pool_avail.csv]
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dtc
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/idns_res_resp.csv]
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dtc
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/idns_res_snmp.csv]
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dtc
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ipam_networks_report.csv]
sourcetype = ib:ipam:network
host = infoblox.localdomain
index = ib_ipam
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dns_view_report.csv]
sourcetype = ib:dns:view
host = infoblox.localdomain
index = ib_ipam
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dns_zone_report.csv]
sourcetype = ib:dns:zone
host = infoblox.localdomain
index = ib_ipam
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ibflex_zone_counts_report.csv]
sourcetype = ib:dns:ibflex_zone_counts
host = infoblox.localdomain
index = ib_ipam
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/dns_reporter_wrapper.sh]
interval = 00 00 * * *
host = infoblox.localdomain
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ip_address_inventory.csv]
source = ib:ipam:ip_address_inventory
sourcetype = ib:reserved1
host = infoblox.localdomain
index = ib_ipam
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/vlan_inventory_report.csv]
sourcetype = ib:ipam:vlan_inventory
host = infoblox.localdomain
index = ib_ipam
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/vlan_conflict_report.csv]
sourcetype = ib:ipam:vlan_conflict
host = infoblox.localdomain
index = ib_ipam
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/license_pool_utilizations.csv*]
source = ib:license:license_pool_utilizations
sourcetype = ib:reserved2
host = infoblox.localdomain
index = ib_license
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query_rpz_hits.csv]
source = ib:dns:query:top_rpz_hit
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dca_query_count.csv]
source = ib:dns:query:dca_query_count
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/subscriber_info_and_abl_query_stats.csv]
source = ib:dns:stats:subscriber_and_abl_queries
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[batch:///infoblox/var/reporting/threat_db_A.csv]
sourcetype = ib:ib_threatdb_reserved1
host = infoblox.localdomain
index = ib_threatdb_a
_TCP_ROUTING = ib_group
move_policy = sinkhole

[batch:///infoblox/var/reporting/threat_db_descriptions_A.csv]
sourcetype = ib:ib_threatdb_reserved2
host = infoblox.localdomain
crcSalt = <SOURCE>
index = ib_threatdb_descriptions_a
_TCP_ROUTING = ib_group
move_policy = sinkhole

[batch:///infoblox/var/reporting/threat_db_B.csv]
sourcetype = ib:ib_threatdb_reserved1
host = infoblox.localdomain
index = ib_threatdb_b
_TCP_ROUTING = ib_group
move_policy = sinkhole

[batch:///infoblox/var/reporting/threat_db_descriptions_B.csv]
sourcetype = ib:ib_threatdb_reserved2
host = infoblox.localdomain
crcSalt = <SOURCE>
index = ib_threatdb_descriptions_b
_TCP_ROUTING = ib_group
move_policy = sinkhole

[monitor:///infoblox/var/reporting/reporting_fireeye_alerts.log*]
source = ib:dns:fireeye
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/ddos_reporting_producer_wrapper.sh]
interval = 300
source = ib:ddos:events
sourcetype = ib:reserved1
host = infoblox.localdomain
index = ib_security
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ddos_ip_rule_stats.csv*]
source = ib:ddos:ip_rule_stats
sourcetype = ib:reserved1
host = infoblox.localdomain
index = ib_security
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/syslog_filtered.log*]
sourcetype = ib:syslog
host = infoblox.localdomain
index = ib_syslog
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_log_monitor_data]
interval = 60
source = ib:system
sourcetype = ib:system
host = infoblox.localdomain
index = ib_system
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ibflex_status.csv]
source = ib:system:ibflex:feature_status
sourcetype = ib:system
host = infoblox.localdomain
index = ib_system
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ibflex_fireeye_status.csv]
source = ib:system:ibflex:feature_status
sourcetype = ib:system
host = infoblox.localdomain
index = ib_system
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_system_capacity_objects_wrapper.sh]
interval = 3600
source = ib:system_capacity:objects
sourcetype = ib:system_capacity:objects
host = infoblox.localdomain
index = ib_system_capacity
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_discovery_capacity_objects_wrapper.sh]
interval = 3600
source = ib:system_capacity:discovery_stats
sourcetype = ib:system_capacity:discovery_stats
host = infoblox.localdomain
index = ib_system_capacity
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/user_login.csv*]
source = ib:user:user_login
sourcetype = ib:reserved1
host = infoblox.localdomain
index = ib_security
_TCP_ROUTING = ib_group

[SSL]
sslVersions = tls1.1, tls1.2
