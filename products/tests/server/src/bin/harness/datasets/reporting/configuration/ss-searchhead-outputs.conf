

# Turn off indexing on the search head
[indexAndForward]
index = false


[tcpout]
defaultGroup = ib_group
forwardedindex.filter.disable = true
indexAndForward = false


sslRootCAPath = /opt/splunksearchhead/etc/certs/cacert.pem
sslCertPath = /opt/splunksearchhead/etc/certs/ibx-searchhead.pem
sslPassword = $1$UXLaeMfJ42tY
sslVerifyServerCert = true
sslCommonNameToCheck = infoblox-indexer


compressed = true
channelReapInterval = 60000
channelReapLowater = 10
channelTTL = 300000
dnsResolutionInterval = 300
negotiateNewProtocol = true
useClientSSLCompression = true


[tcpout:ib_group]
server = 10.35.205.11:9997,10.35.205.12:9997,10.35.205.14:9997,10.35.205.13:9997
autoLB = true
