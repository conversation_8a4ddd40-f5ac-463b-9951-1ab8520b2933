

[filterdhcprangesreport]
filename = filterdhcprangesreport
type = python
streaming = true
enableheader = true
retainsevents = true
generating = false


[interpolate]
filename = interpolate
type = python
streaming = true
enableheader = true
retainsevents = true
generating = false


[alertlogging]
filename = alertlogging
type = python
streaming = true
enableheader = true
retainsevents = true
generating = false


[msservers]
filename = resolve_ms_servers
type = python
streaming = true
enableheader = true
retainsevents = true
generating = false


[fetchthreatstopdetails]
generating = false
overrides_timeorder = true
filename = fetch_threatstop_details
streaming = true
retainsevents = true
type = python
enableheader = true


[addthreatstopdetails]
filename = add_threatstop_details
type = python
streaming = true
enableheader = true
retainsevents = true
generating = false


[addthreatdbinfo]
filename = add_threatdb_info
type = python
streaming = true
enableheader = true
retainsevents = true
generating = false
