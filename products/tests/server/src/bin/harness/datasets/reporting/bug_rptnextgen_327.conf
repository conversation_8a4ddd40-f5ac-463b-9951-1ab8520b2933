[test_bug_rptnextgen_327]
description = test bug rptnextgen 327
search = sourcetype=ib:dhcp:lease_history index=ib_dhcp_lease_history dhcpd OR dhcpdv6 r-l-e | eval Protocol=if(PROTO=="dhcpdv6","IPV6","IPV4") | eval LEASE_START=strftime(START_EPOCH, "%%Y-%%m-%%d %%H:%%M:%%S") | eval LEASE_END=strftime(END_EPOCH, "%%Y-%%m-%%d %%H:%%M:%%S") | lookup os_number_fingerprint_lookup OS_NUMBER output SFP | eval FINGER_PRINT=if(isnull(OS_NUMBER) OR OS_NUMBER==0,FP,SFP) | lookup nios_member_ip_lookup host output MEMBER_IP | lookup fingerprint_device_class_lookup FINGER_PRINT output DEVICE_CLASS | eval DEVICE_CLASS=if(isnull(DEVICE_CLASS), "Modified or Deleted", DEVICE_CLASS) | rename host as "Member", ACTION as "Action", LEASE_IP as "Lease IP", MAC_DUID as "MAC/DUID", MEMBER_IP as "Member IP", OPTION12HOST as "Host Name", LEASE_START as "Lease Start", LEASE_END as "Lease End", FINGER_PRINT as "Fingerprint" | convert ctime(_time) as Time | table Time, Member, "Member IP", Protocol, Action, "Lease IP", "MAC/DUID", "Host Name", "Lease Start", "Lease End", "Fingerprint"
dispatch.earliest_time = 1444137422
dispatch.latest_time = 1446815822

[test_bug_rptnextgen_327 (Alert)]
dispatch.earliest_time = 1444137422
dispatch.latest_time = 1446815822
enableSched = 1
cron_schedule = 10 * * * *
action.export_search_results = 1
search = sourcetype=ib:dhcp:lease_history index=ib_dhcp_lease_history dhcpd OR dhcpdv6 r-l-e | eval Protocol=if(PROTO=="dhcpdv6","IPV6","IPV4") | eval LEASE_START=strftime(START_EPOCH, "%%Y-%%m-%%d %%H:%%M:%%S") | eval LEASE_END=strftime(END_EPOCH, "%%Y-%%m-%%d %%H:%%M:%%S") | lookup os_number_fingerprint_lookup OS_NUMBER output SFP | eval FINGER_PRINT=if(isnull(OS_NUMBER) OR OS_NUMBER==0,FP,SFP) | lookup nios_member_ip_lookup host output MEMBER_IP | lookup fingerprint_device_class_lookup FINGER_PRINT output DEVICE_CLASS | eval DEVICE_CLASS=if(isnull(DEVICE_CLASS), "Modified or Deleted", DEVICE_CLASS) | rename host as "Member", ACTION as "Action", LEASE_IP as "Lease IP", MAC_DUID as "MAC/DUID", MEMBER_IP as "Member IP", OPTION12HOST as "Host Name", LEASE_START as "Lease Start", LEASE_END as "Lease End", FINGER_PRINT as "Fingerprint" | convert ctime(_time) as Time | table Time, Member, "Member IP", Protocol, Action, "Lease IP", "MAC/DUID", "Host Name", "Lease Start", "Lease End", "Fingerprint"
counttype = number of events
relation = greater than
quantity = 0
