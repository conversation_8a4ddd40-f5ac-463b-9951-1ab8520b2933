[test_weekly_search_migration]
description = test weekly scheduled search
search = index=ib_system_summary report=si_cpu_usage | timechart bins=1000 avg(CPU_PERCENT) by orig_host where max in top5 useother=f | interpolate 1200
dispatch.earliest_time = 1444137422
dispatch.latest_time = 1446815822

[test_weekly_search_migration (Alert)]
dispatch.earliest_time = 1444137422
dispatch.latest_time = 1446815822
enableSched = 1
cron_schedule = 10 5 * * 0,3,6
action.export_search_results = 1
search = index=ib_system_summary report=si_cpu_usage | timechart bins=1000 avg(CPU_PERCENT) by orig_host where max in top5 useother=f | interpolate 1200
counttype = number of events
relation = greater than
quantity = 0

