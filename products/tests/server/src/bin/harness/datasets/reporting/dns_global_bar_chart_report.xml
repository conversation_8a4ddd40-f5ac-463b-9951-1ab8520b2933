<dashboard>
  <label>Global Bar Chart Report</label>
  <row>
    <panel>
      <title>Global Bar Chart Panel</title>
      <chart>
        <option name="charting.chart">bar</option>
        <option name="charting.data.preview">true</option>
        <option name="charting.legend.labelStyle.overflowMode">ellipsisNone</option>
        <option name="charting.axisTitleX.text">Client</option>
        <option name="charting.axisTitleY.text">Queries</option>
        <search>
          <query>index=ib_dns_summary report=si_dns_top_clients earliest=-1y | lookup dns_viewkey_displayname_lookup VIEW output display_name | stats sum(COUNT) as CLIENT_QUERIES by CLIENT | sort -CLIENT_QUERIES | head 35 | eventstats sum(CLIENT_QUERIES) as TOTAL | eval PERCENT=round(CLIENT_QUERIES*100/TOTAL,1) | eval PCLIENT=CLIENT+" ("+PERCENT+"%)" | rename PCLIENT as Client, CLIENT_QUERIES as Queries | fields Client, Queries</query>
        </search>
      </chart>
    </panel>
  </row>
</dashboard>
