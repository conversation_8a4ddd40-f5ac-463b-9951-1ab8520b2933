host = infoblox.localdomain

[monitor://$SPLUNK_HOME/var/log/splunk/web_access.log]
disabled = true

[monitor://$SPLUNK_HOME/var/log/splunk/web_service.log]
disabled = true

[monitor://$SPLUNK_HOME/var/log/splunk/searchhistory.log]
disabled = true

[monitor://$SPLUNK_HOME/var/log/splunk/splunklogger.log]
disabled = true

[fschange:$SPLUNK_HOME/etc]
disabled = true

[monitor:///infoblox/var/reporting/cloud_vm_address.csv*]
source = ib:cloud:vm_address_history
sourcetype = ib:reserved2
host = infoblox.localdomain
index = ib_cloud
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/reporting_ddns.log*]
sourcetype = ib:ddns
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ms_reporting_ddns.log*]
sourcetype = ib:ddns
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_dns_resp_latency_wrapper.sh]
interval = 60
source = ib:dns:perf
sourcetype = ib:dns:perf
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query_rpz_hits.csv]
source = ib:dns:query:top_rpz_hit
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dca_query_count.csv]
source = ib:dns:query:dca_query_count
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/subscriber_info_and_abl_query_stats.csv]
source = ib:dns:stats:subscriber_and_abl_queries
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[batch:///infoblox/var/reporting/threat_db_A.csv]
sourcetype = ib:ib_threatdb_reserved1
host = infoblox.localdomain
index = ib_threatdb_a
_TCP_ROUTING = ib_group
move_policy = sinkhole

[batch:///infoblox/var/reporting/threat_db_descriptions_A.csv]
sourcetype = ib:ib_threatdb_reserved2
host = infoblox.localdomain
crcSalt = <SOURCE>
index = ib_threatdb_descriptions_a
_TCP_ROUTING = ib_group
move_policy = sinkhole

[batch:///infoblox/var/reporting/threat_db_B.csv]
sourcetype = ib:ib_threatdb_reserved1
host = infoblox.localdomain
index = ib_threatdb_b
_TCP_ROUTING = ib_group
move_policy = sinkhole

[batch:///infoblox/var/reporting/threat_db_descriptions_B.csv]
sourcetype = ib:ib_threatdb_reserved2
host = infoblox.localdomain
crcSalt = <SOURCE>
index = ib_threatdb_descriptions_b
_TCP_ROUTING = ib_group
move_policy = sinkhole

[monitor:///infoblox/var/reporting/reporting_fireeye_alerts.log*]
source = ib:dns:fireeye
sourcetype = ib:dns:reserved
host = infoblox.localdomain
index = ib_dns
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/ddos_reporting_producer_wrapper.sh]
interval = 300
source = ib:ddos:events
sourcetype = ib:reserved1
host = infoblox.localdomain
index = ib_security
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ddos_ip_rule_stats.csv*]
source = ib:ddos:ip_rule_stats
sourcetype = ib:reserved1
host = infoblox.localdomain
index = ib_security
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/syslog_filtered.log*]
sourcetype = ib:syslog
host = infoblox.localdomain
index = ib_syslog
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_log_monitor_data]
interval = 60
source = ib:system
sourcetype = ib:system
host = infoblox.localdomain
index = ib_system
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ibflex_status.csv]
source = ib:system:ibflex:feature_status
sourcetype = ib:system
host = infoblox.localdomain
index = ib_system
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ibflex_fireeye_status.csv]
source = ib:system:ibflex:feature_status
sourcetype = ib:system
host = infoblox.localdomain
index = ib_system
_TCP_ROUTING = ib_group

[SSL]
sslVersions = tls1.1, tls1.2
