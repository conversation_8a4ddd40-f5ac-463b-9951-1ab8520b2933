<dashboard script="top_rpz_hits_drilldown.js" stylesheet="inpage_drilldown.css">
  <label/>
  <row>
    <table id="master">
      <option name="drilldown">row</option>
      <option name="link.visible">false</option>
      <searchString>index=ib_dns_summary report=si_dns_rpz_hits earliest=-1d | lookup dns_viewkey_displayname_lookup VIEW output display_name | eval DNS_VIEW =if(isnull(display_name), "NULL",display_name) | eval RECORD_DATA=if(isnull(RECORD_DATA),"",RECORD_DATA) | eval RPZ_QNAME=if(isnull(RPZ_QNAME),"",RPZ_QNAME) | eval RPZ_SEVERITY=if(isnull(RPZ_SEVERITY),"",RPZ_SEVERITY) | where MITIGATION_ACTION != "ER" | stats sum(COUNT) as QCOUNT by _time, CLIENT, DOMAIN_NAME, DNS_VIEW, orig_host, TOTAL_COUNT, MITIGATION_ACTION, RPZ_SEVERITY, RECORD_DATA RPZ_QNAME | stats sum(TOTAL_COUNT) as TOTAL_COUNT, sum(QCOUNT) as QCOUNT by _time, CLIENT, DOMAIN_NAME, DNS_VIEW, MITIGATION_ACTION, RPZ_SEVERITY, RECORD_DATA RPZ_QNAME | convert ctime(_time) as Time | sort -QCOUNT | head 10 | eval MITIGATION_ACTION=case(MITIGATION_ACTION == "PT", "Passthru", MITIGATION_ACTION == "NX", "Block (No Such Domain)", MITIGATION_ACTION == "ND", "Block (No Data)", MITIGATION_ACTION == "SB", "Substitute", MITIGATION_ACTION == "A1", "Substitute (A)", MITIGATION_ACTION == "A4", "Substitute (AAAA)", MITIGATION_ACTION == "AA", "Substitute (A/AAAA)", MITIGATION_ACTION == "DN", "Substitute (Domain Name)", MITIGATION_ACTION == "ER", "Error") | eval RPZ_SEVERITY=case(RPZ_SEVERITY == "4", "INFORMATIONAL", RPZ_SEVERITY == "6", "WARNING", RPZ_SEVERITY == "7", "MAJOR", RPZ_SEVERITY == "8", "CRITICAL", RPZ_SEVERITY == "", "") | rename CLIENT as "Client ID", QCOUNT as "Total Client Hits", DOMAIN_NAME as "Domain Name", TOTAL_COUNT as "Total Rule Hits", RPZ_QNAME as "RPZ Entry", RPZ_SEVERITY as "RPZ Severity", MITIGATION_ACTION as "Mitigation Action", RECORD_DATA as "Substitute Addresses" | table "Client ID", "Total Client Hits", "Domain Name", "RPZ Entry", "RPZ Severity", "Total Rule Hits", "Mitigation Action", "Substitute Addresses", Time</searchString>
    </table>
  </row>
  <row>
    <table id="lease_history_details">
      <title>DHCP Lease History for Client ID = $conditionalvalue$</title>
      <option name="drilldown">off</option>
      <option name="link.visible">false</option>
      <searchTemplate>sourcetype=ib:dhcp:lease_history index=ib_dhcp_lease_history dhcpd OR dhcpdv6 r-l-e earliest=-1d | eval Protocol=if(PROTO=="dhcpdv6","IPV6","IPV4") | eval LEASE_START=strftime(START_EPOCH,"%Y-%m-%d %H:%M:%S") | eval LEASE_END=strftime(END_EPOCH,"%Y-%m-%d %H:%M:%S") | lookup os_number_fingerprint_lookup OS_NUMBER output SFP | eval FINGER_PRINT=if(isnull(OS_NUMBER),FP,SFP) | where LEASE_IP="$conditionalvalue$" | lookup nios_member_ip_lookup host output MEMBER_IP | rename host as "Member", ACTION as "Action", LEASE_IP as "Lease IP", MAC_DUID as "MAC/DUID", MEMBER_IP as "Member IP", OPTION12HOST as "Host Name", LEASE_START as "Lease Start", LEASE_END as "Lease End", FINGER_PRINT as "Fingerprint" | convert ctime(_time) as Time | table Time, Member, "Member IP", Protocol, Action, "Lease IP", "MAC/DUID", "Host Name", "Lease Start", "Lease End", "Fingerprint"</searchTemplate>
    </table>
  </row>
  <row>
    <table id="user_details">
      <title>User History for IP Address = $conditionalvalue$</title>
      <option name="drilldown">off</option>
      <option name="link.visible">false</option>
      <searchTemplate>sourcetype=ib:reserved1 source=ib:user:user_login index=ib_security earliest=-1d | eval TIMEOUT_KEY="ad_user_default_timeout" | lookup users_timeout_value_lookup TIMEOUT_KEY output TIMEOUT_VAL | eval TIMEOUT_VALUE=if(isnull(TIMEOUT_VAL),18000,TIMEOUT_VAL*60) | where ip_address="$conditionalvalue$" | eval last_activeEpoch=if(isnum(last_active), last_active, strptime(last_active, "%Y-%m-%d %H:%M:%S")) | eventstats latest(last_activeEpoch) as l_last_active by user_name, ip_address, login_time | eval status=if((last_activeEpoch=l_last_active) AND (status=="ACTIVE") AND ((last_activeEpoch+TIMEOUT_VALUE)&lt;now()),"TIMEOUT",status) | sort -_time | eval last_active=if(isnum(last_active), strftime(last_active, "%Y-%m-%d %H:%M:%S"), last_active) | eval last_updated=if(isnum(last_updated), strftime(last_updated, "%Y-%m-%d %H:%M:%S"), last_updated) | eval logout_time=if(isnum(logout_time), strftime(logout_time, "%Y-%m-%d %H:%M:%S"), logout_time) | eval login_time=if(isnum(login_time), strftime(login_time, "%Y-%m-%d %H:%M:%S"), login_time) | rename timestamp as Time, user_name as "User Name", login_time as "First Seen", logout_time as "Logout Time", last_active as "Last Seen", last_updated as "Last Updated", ip_address as "IP Address", domain as "Domain", status as "User Status",  | table "Last Updated" "User Name" "Domain" "IP Address" "First Seen" "Logout Time" "Last Seen" "User Status" </searchTemplate>
    </table>
  </row>
  <row>
    <table id="threat_details">
      <title>RPZ Threat Details</title>
      <option name="drilldown">off</option>
      <option name="link.visible">false</option>
      <searchTemplate>| fetchthreatstopdetails $threatType$ $conditionalvalue$ | eval rpz_rule="$conditionalvalue$ | " + name | convert ctime(first_identified) as "First Identified" | rename rpz_rule as "RPZ Rule", short_description as "Short Description", public_description as "Description" | table "RPZ Rule", "First Identified", "Short Description", "Description"</searchTemplate>
    </table>
  </row>
</dashboard>
