



host = vm-10-84.inca.infoblox.com

[monitor://$SPLUNK_HOME/var/log/splunk/web_access.log]
disabled = true
[monitor://$SPLUNK_HOME/var/log/splunk/web_service.log]
disabled = true
[monitor://$SPLUNK_HOME/var/log/splunk/searchhistory.log]
disabled = true
[monitor://$SPLUNK_HOME/var/log/splunk/splunklogger.log]
disabled = true
[fschange:$SPLUNK_HOME/etc]
disabled = true

[monitor:///infoblox/var/reporting/publish_data.csv*]
source = ib:ecosystem_publish:publish_data
sourcetype = ib:reserved1
host = vm-10-84.inca.infoblox.com
index = ib_ecosystem_publish
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dhcp_perf.csv]
sourcetype = ib:dhcp:message
host = vm-10-84.inca.infoblox.com
index = ib_dhcp
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dhcpv6_perf.csv]
sourcetype = ib:dhcp:message
host = vm-10-84.inca.infoblox.com
index = ib_dhcp
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dhcp_networks_report.csv]
sourcetype = ib:dhcp:network
host = vm-10-84.inca.infoblox.com
index = ib_dhcp
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dhcp_ranges_report.csv]
sourcetype = ib:dhcp:range
host = vm-10-84.inca.infoblox.com
index = ib_dhcp
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-top-name.txt]
sourcetype = ib:dns:query:top_requested_domain_names
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/cache-hits-misses.txt]
sourcetype = ib:dns:query:cache_hit_rate
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-names-and-queries.txt]
sourcetype = ib:dns:query:by_member
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-top-rr-type.txt]
sourcetype = ib:dns:query:qps
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-top-client.txt]
sourcetype = ib:dns:query:top_clients
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-top-client-per-zone.txt]
source = ib:dns:query:top_clients_per_zone
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-top-client-per-fqdn.txt]
source = ib:dns:query:top_clients_per_domain
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-nxdomain-top-name.txt]
source = ib:dns:query:top_nxdomain_query
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-servfail-sent-top-name.txt]
source = ib:dns:query:top_failed
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-servfail-received-top-name.txt]
source = ib:dns:query:top_received
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-recursion-timeout-top-name.txt]
source = ib:dns:query:top_timeout
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_dns_stats_wrapper.sh]
interval = 60
source = ib:dns:stats
sourcetype = ib:dns:stats
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-ip-block-group.txt]
source = ib:dns:query:ip_block_group
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/idns_res_avail.csv]
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dtc
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/idns_res_pool_avail.csv]
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dtc
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/idns_res_resp.csv]
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dtc
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/reporting_ddns.log*]
sourcetype = ib:ddns
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/subscription_data.csv*]
source = ib:ecosystem_subscription:subscription_data
sourcetype = ib:reserved1
host = vm-10-84.inca.infoblox.com
index = ib_ecosystem_subscription
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_system_capacity_objects_wrapper.sh]
interval = 60
source = ib:system_capacity:objects
sourcetype = ib:system_capacity:objects
host = vm-10-84.inca.infoblox.com
index = ib_system_capacity
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/reporting_dhcp_lease_history.log*]
sourcetype = ib:dhcp:lease_history
host = vm-10-84.inca.infoblox.com
index = ib_dhcp_lease_history
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/discovery_ipaddr_activity.log*]
source = ib:discovery:ipaddr_activity
sourcetype = ib:reserved2
host = vm-10-84.inca.infoblox.com
index = ib_discovery
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/discovery_port_capacity.csv]
source = ib:discovery:port_capacity
sourcetype = ib:reserved2
host = vm-10-84.inca.infoblox.com
index = ib_discovery
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/discovery_end_host_activity.log*]
source = ib:discovery:end_host_activity
sourcetype = ib:reserved2
host = vm-10-84.inca.infoblox.com
index = ib_discovery
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/cloud_vm_address.csv*]
source = ib:cloud:vm_address_history
sourcetype = ib:reserved2
host = vm-10-84.inca.infoblox.com
index = ib_cloud
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dns_reclamation.csv]
source = ib:dns:reclamation
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/user_login.csv*]
source = ib:user:user_login
sourcetype = ib:reserved1
host = vm-10-84.inca.infoblox.com
index = ib_security
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/license_pool_utilizations.csv*]
source = ib:license:license_pool_utilizations
sourcetype = ib:reserved2
host = vm-10-84.inca.infoblox.com
index = ib_license
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_dns_resp_latency_wrapper.sh]
interval = 60
source = ib:dns:perf
sourcetype = ib:dns:perf
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/query-rpz-client.txt]
source = ib:dns:query:top_rpz_hit
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/reporting_fireeye_alerts.log*]
source = ib:dns:fireeye
sourcetype = ib:dns:reserved
host = vm-10-84.inca.infoblox.com
index = ib_dns
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/ddos_reporting_producer_wrapper.sh]
interval = 300
source = ib:ddos:events
sourcetype = ib:reserved1
host = vm-10-84.inca.infoblox.com
index = ib_security
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ddos_ip_rule_stats.csv*]
source = ib:ddos:ip_rule_stats
sourcetype = ib:reserved1
host = vm-10-84.inca.infoblox.com
index = ib_security
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_log_monitor_data]
interval = 60
source = ib:system
sourcetype = ib:system
host = vm-10-84.inca.infoblox.com
index = ib_system
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/syslog_filtered.log*]
sourcetype = ib:syslog
host = vm-10-84.inca.infoblox.com
index = ib_syslog
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ipam_networks_report.csv]
sourcetype = ib:ipam:network
host = vm-10-84.inca.infoblox.com
index = ib_ipam
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dns_view_report.csv]
sourcetype = ib:dns:view
host = vm-10-84.inca.infoblox.com
index = ib_ipam
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/dns_zone_report.csv]
sourcetype = ib:dns:zone
host = vm-10-84.inca.infoblox.com
index = ib_ipam
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/audit.log*]
sourcetype = ib:audit
host = vm-10-84.inca.infoblox.com
index = ib_audit
_TCP_ROUTING = ib_group


