[SSL]
sslPassword = password
requireClientCert = true
serverCert = /opt/splunk/etc/certs/ibx-indexer.pem
sslVersions = tls1.1, tls1.2
cipherSuite = ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA256:AES256-SHA:AES128-SHA:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA

[splunktcp-ssl:9997]

[monitor:///infoblox/var/audit.log*]
sourcetype = ib:audit
host = infoblox.localdomain
index = ib_audit
blacklist = \.(?:passive|idx)$
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/syslog_filtered.log*]
sourcetype = ib:syslog
host = infoblox.localdomain
index = ib_syslog
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_system_capacity_objects_wrapper.sh]
interval = 3600
source = ib:system_capacity:objects
sourcetype = ib:system_capacity:objects
host = infoblox.localdomain
index = ib_system_capacity
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_discovery_capacity_objects_wrapper.sh]
interval = 3600
source = ib:system_capacity:discovery_stats
sourcetype = ib:system_capacity:discovery_stats
host = infoblox.localdomain
index = ib_system_capacity
_TCP_ROUTING = ib_group

[script://$SPLUNK_HOME/bin/scripts/reporting_log_monitor_data]
interval = 60
source = ib:system
sourcetype = ib:system
host = infoblox.localdomain
index = ib_system
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ibflex_status.csv]
source = ib:system:ibflex:feature_status
sourcetype = ib:system
host = infoblox.localdomain
index = ib_system
_TCP_ROUTING = ib_group

[monitor:///infoblox/var/reporting/ibflex_fireeye_status.csv]
source = ib:system:ibflex:feature_status
sourcetype = ib:system
host = infoblox.localdomain
index = ib_system
_TCP_ROUTING = ib_group
