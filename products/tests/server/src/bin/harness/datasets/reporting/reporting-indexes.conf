

maxDataSize = 1000
maxHotBuckets = 10
maxWarmDBCount = 200

[volume:ib_volume]
path = $SPLUNK_HOME/var/lib/splunk
maxVolumeDataSizeMB = 250000

[ib_dhcp_summary]
thawedPath = $SPLUNK_DB/ib_dhcp_summary/thaweddb
maxTotalDataSizeMB = 25000
maxHotBuckets = 10
homePath = volume:ib_volume/ib_dhcp_summary/db
coldPath = volume:ib_volume/ib_dhcp_summary/colddb
frozenTimePeriodInSecs = 63244800

[ib_dns]
thawedPath = $SPLUNK_DB/ib_dns/thaweddb
maxTotalDataSizeMB = 25000
maxHotBuckets = 10
homePath = volume:ib_volume/ib_dns/db
coldPath = volume:ib_volume/ib_dns/colddb
frozenTimePeriodInSecs = 5270400

[ib_dtc]
thawedPath = $SPLUNK_DB/ib_dtc/thaweddb
maxTotalDataSizeMB = 0
maxHotBuckets = 10
homePath = volume:ib_volume/ib_dtc/db
coldPath = volume:ib_volume/ib_dtc/colddb
frozenTimePeriodInSecs = 604800

[ib_dhcp_lease_history]
thawedPath = $SPLUNK_DB/ib_dhcp_lease_history/thaweddb
maxTotalDataSizeMB = 97500
maxHotBuckets = 10
homePath = volume:ib_volume/ib_dhcp_lease_history/db
coldPath = volume:ib_volume/ib_dhcp_lease_history/colddb
frozenTimePeriodInSecs = 63244800

[ib_ipam]
thawedPath = $SPLUNK_DB/ib_ipam/thaweddb
maxTotalDataSizeMB = 12500
maxHotBuckets = 10
homePath = volume:ib_volume/ib_ipam/db
coldPath = volume:ib_volume/ib_ipam/colddb
frozenTimePeriodInSecs = 5270400

[ib_system_summary]
thawedPath = $SPLUNK_DB/ib_system_summary/thaweddb
maxTotalDataSizeMB = 25125
maxHotBuckets = 10
homePath = volume:ib_volume/ib_system_summary/db
coldPath = volume:ib_volume/ib_system_summary/colddb
frozenTimePeriodInSecs = 63244800

[ib_security]
thawedPath = $SPLUNK_DB/ib_security/thaweddb
maxTotalDataSizeMB = 1750
maxHotBuckets = 10
homePath = volume:ib_volume/ib_security/db
coldPath = volume:ib_volume/ib_security/colddb
frozenTimePeriodInSecs = 63244800

[ib_security_summary]
thawedPath = $SPLUNK_DB/ib_security_summary/thaweddb
maxTotalDataSizeMB = 750
maxHotBuckets = 10
homePath = volume:ib_volume/ib_security_summary/db
coldPath = volume:ib_volume/ib_security_summary/colddb
frozenTimePeriodInSecs = 63244800

[ib_dtc_summary]
thawedPath = $SPLUNK_DB/ib_dtc_summary/thaweddb
maxTotalDataSizeMB = 0
maxHotBuckets = 10
homePath = volume:ib_volume/ib_dtc_summary/db
coldPath = volume:ib_volume/ib_dtc_summary/colddb
frozenTimePeriodInSecs = 2678400

[ib_system]
thawedPath = $SPLUNK_DB/ib_system/thaweddb
maxTotalDataSizeMB = 12750
maxHotBuckets = 10
homePath = volume:ib_volume/ib_system/db
coldPath = volume:ib_volume/ib_system/colddb
frozenTimePeriodInSecs = 5270400

[ib_dhcp]
thawedPath = $SPLUNK_DB/ib_dhcp/thaweddb
maxTotalDataSizeMB = 25000
maxHotBuckets = 10
homePath = volume:ib_volume/ib_dhcp/db
coldPath = volume:ib_volume/ib_dhcp/colddb
frozenTimePeriodInSecs = 5270400

[ib_dns_summary]
thawedPath = $SPLUNK_DB/ib_dns_summary/thaweddb
maxTotalDataSizeMB = 25000
maxHotBuckets = 10
homePath = volume:ib_volume/ib_dns_summary/db
coldPath = volume:ib_volume/ib_dns_summary/colddb
frozenTimePeriodInSecs = 63244800

[ib_discovery]
thawedPath = $SPLUNK_DB/ib_discovery/thaweddb
maxTotalDataSizeMB = 0
maxHotBuckets = 10
homePath = volume:ib_volume/ib_discovery/db
coldPath = volume:ib_volume/ib_discovery/colddb
frozenTimePeriodInSecs = 31622400

[ib_cloud]
thawedPath = $SPLUNK_DB/ib_cloud/thaweddb
maxTotalDataSizeMB = 0
maxHotBuckets = 10
homePath = volume:ib_volume/ib_cloud/db
coldPath = volume:ib_volume/ib_cloud/colddb
frozenTimePeriodInSecs = 63244800

[ib_ecosystem_subscription]
thawedPath = $SPLUNK_DB/ib_ecosystem_subscription/thaweddb
maxTotalDataSizeMB = 0
maxHotBuckets = 10
homePath = volume:ib_volume/ib_ecosystem_subscription/db
coldPath = volume:ib_volume/ib_ecosystem_subscription/colddb
frozenTimePeriodInSecs = 63244800

[ib_audit]
thawedPath = $SPLUNK_DB/ib_audit/thaweddb
maxTotalDataSizeMB = 0
maxHotBuckets = 10
homePath = volume:ib_volume/ib_audit/db
coldPath = volume:ib_volume/ib_audit/colddb
frozenTimePeriodInSecs = 63244800

[ib_license]
thawedPath = $SPLUNK_DB/ib_license/thaweddb
maxTotalDataSizeMB = 0
maxHotBuckets = 10
homePath = volume:ib_volume/ib_license/db
coldPath = volume:ib_volume/ib_license/colddb
frozenTimePeriodInSecs = 63244800

[ib_ecosystem_publish]
thawedPath = $SPLUNK_DB/ib_ecosystem_publish/thaweddb
maxTotalDataSizeMB = 0
maxHotBuckets = 10
homePath = volume:ib_volume/ib_ecosystem_publish/db
coldPath = volume:ib_volume/ib_ecosystem_publish/colddb
frozenTimePeriodInSecs = 63244800

[ib_dns_capture]
thawedPath = $SPLUNK_DB/ib_dns_capture/thaweddb
maxTotalDataSizeMB = 0
maxHotBuckets = 10
homePath = volume:ib_volume/ib_dns_capture/db
coldPath = volume:ib_volume/ib_dns_capture/colddb
frozenTimePeriodInSecs = 2678400
