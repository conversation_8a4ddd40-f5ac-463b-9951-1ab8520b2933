<dashboard>
  <label>Customized Drilldown Report</label>
  <row>
    <panel>
      <title>Customized Drilldown Panel</title>
      <table>
        <option name="drilldown">cell</option>
        <drilldown>
          <condition field="Client ID">
            <set token="conditionvalue0_0">$row.Client ID$</set>
          </condition>
          <condition field="*">
            <set token="conditionvalue0_1">$row.Client ID$</set>
          </condition>
        </drilldown>
        <search>
          <query>index=ib_dns_summary report=si_dns_rpz_hits earliest=-1y | lookup dns_viewkey_displayname_lookup VIEW output display_name | stats avg(COUNT) as QCOUNT by _time, VIEW, CLIENT, orig_host | stats sum(QCOUNT) as QCOUNT by _time, CLIENT | eval QCOUNT=round(QCOUNT) | convert ctime(_time) as Time | sort -QCOUNT | head 35 | rename CLIENT as "Client ID", QCOUNT as "Total Client Hits" | table "Client ID", "Total Client Hits", Time</query>
        </search>
      </table>
    </panel>
  </row>
  <row>
    <panel depends="$conditionvalue0_0$">
      <table>
        <title>User History for IP Address = $conditionvalue0_0$</title>
        <option name="drilldown">off</option>
        <search>
          <query>sourcetype=ib:reserved1 source=ib:user:user_login index=ib_security earliest=-1y | eval TIMEOUT_VALUE=if(isnull(TIMEOUT_VAL),18000,TIMEOUT_VAL*60) | where ip_address="$conditionvalue0_0$" | eval last_activeEpoch=if(isnum(last_active), last_active, strptime(last_active, "%Y-%m-%d %H:%M:%S")) | eventstats latest(last_activeEpoch) as l_last_active by user_name, ip_address, login_time | eval status=if((last_activeEpoch=l_last_active) AND (status=="ACTIVE") AND ((last_activeEpoch+TIMEOUT_VALUE)&lt;now()),"TIMEOUT",status) | sort -_time | eval last_active=if(isnum(last_active), strftime(last_active, "%Y-%m-%d %H:%M:%S"), last_active) | eval last_updated=if(isnum(last_updated), strftime(last_updated, "%Y-%m-%d %H:%M:%S"), last_updated) | eval logout_time=if(isnum(logout_time), strftime(logout_time, "%Y-%m-%d %H:%M:%S"), logout_time) | eval login_time=if(isnum(login_time), strftime(login_time, "%Y-%m-%d %H:%M:%S"), login_time) | rename timestamp as Time, user_name as "User Name", login_time as "First Seen", logout_time as "Logout Time", last_active as "Last Seen", last_updated as "Last Updated", ip_address as "IP Address", domain as "Domain", status as "User Status",  | table "Last Updated" "User Name" "Domain" "IP Address" "First Seen" "Logout Time" "Last Seen" "User Status" </query>
        </search>
      </table>
    </panel>
  </row>
  <row>
    <panel depends="$conditionvalue0_1$">
      <table>
        <title>DHCP Lease History for Client ID = $conditionvalue0_1$</title>
        <option name="drilldown">off</option>
        <search>
          <query>sourcetype=ib:dhcp:lease_history index=ib_dhcp_lease_history dhcpd OR dhcpdv6 r-l-e earliest=-1y | eval Protocol=if(PROTO=="dhcpdv6","IPV6","IPV4") | eval LEASE_START=strftime(START_EPOCH,"%Y-%m-%d %H:%M:%S") | eval LEASE_END=strftime(END_EPOCH,"%Y-%m-%d %H:%M:%S") | eval FINGER_PRINT=if(isnull(OS_NUMBER),FP,SFP) | where LEASE_IP="$conditionvalue0_1$" | rename host as "Member", ACTION as "Action", LEASE_IP as "Lease IP", MAC_DUID as "MAC/DUID", MEMBER_IP as "Member IP", OPTION12HOST as "Host Name", LEASE_START as "Lease Start", LEASE_END as "Lease End", FINGER_PRINT as "Fingerprint" | convert ctime(_time) as Time | table Time, Member, "Member IP", Protocol, Action, "Lease IP", "MAC/DUID", "Host Name", "Lease Start", "Lease End", "Fingerprint"</query>
        </search>
      </table>
    </panel>
  </row>
</dashboard>
