db_import;db;full;2011:50;2011:1000
<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:ib="urn:ibap.infoblox.com" xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <SOAP-ENV:Body>
  <what/>
  <ever/>
  <you/>
  <want/>
  </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
<!-- some blank line following up ... ignored until next marker -->

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope >
  <SOAP-ENV:Body>
  <!-- minimal marker for parser synchronization is <SOAP-ENV:Envelope > -->
  </SOAP-ENV:Body>
</SOAP-ENV:Envelope>

(some noise to ignore)
(chatter)
(chatter)
(chatter)

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope >
  <SOAP-ENV:Body>
  some data inline
  </SOAP-ENV:Body>
</SOAP-ENV:Envelope>

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope >
  <SOAP-ENV:Body>
<!-- marker within data ...  -->
<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope />
  </SOAP-ENV:Body>
</SOAP-ENV:Envelope>

some more noise to be ignored ...
shhhhhhh
shhhhhhh
shhhhhhh
shhhhhhh
shhhhhhh
shhhhhhh
(argh)
