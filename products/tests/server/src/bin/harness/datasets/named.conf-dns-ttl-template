acl "loopbacknet" { *********/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";
	recursion no;			// Do not provide recursive service
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};


view "VIEW_NAME" {

  // Reverse mapping for loopback address
  zone "0.0.127.in-addr.arpa" {
	type master;
	file "localhost.rev";
	notify no;
  };

  zone "main.com" {
	type master;
	notify no;
	file "dns_ttl_main.db";
  };

  // The test zones all start out empty, so we can use one and the
  // same generic zone file. Note that things will get a bit weird
  // if the 'named' starts dumping zone data to file (since the zones
  // share the same zone file), but since it waits 900 seconds following
  // a DDNS update to do so, and the test completes in much less time,
  // we should be OK. The zones also share the same journal file, so
  // the named log will show lots of journal gaps and resets of the
  // journal. Inefficient, but of no consequence for this test.
  zone "all-def-ttl.com" {
	type master;
	notify no;
	file "dns_ttl_test.db";
	allow-update { "loopbacknet"; };
  };

  zone "all-individual-ttl.com" {
	type master;
	notify no;
	file "dns_ttl_test.db";
	allow-update { "loopbacknet"; };
  };

  zone "mix-ttl.com" {
	type master;
	notify no;
	file "dns_ttl_test.db";
	allow-update { "loopbacknet"; };
  };

};
