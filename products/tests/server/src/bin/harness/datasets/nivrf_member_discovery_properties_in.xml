<DATABASE NAME="onedb" VERSION="test">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.discovery.member_discovery_properties"/>
  <PROPERTY NAME="virtual_node" VALUE="2"/>
  <PROPERTY NAME="network_view" VALUE="2"/>
  <PROPERTY NAME="interface" VALUE="LAN2"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.discovery.seed_router"/>
  <PROPERTY NAME="member" VALUE="2"/>
  <PROPERTY NAME="address" VALUE="********"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.discovery.member_discovery_properties"/>
  <PROPERTY NAME="virtual_node" VALUE="1"/>
  <PROPERTY NAME="network_view" VALUE="1"/>
  <PROPERTY NAME="interface" VALUE="LAN1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.discovery.seed_router"/>
  <PROPERTY NAME="member" VALUE="1"/>
  <PROPERTY NAME="address" VALUE="********"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.discovery.seed_router"/>
  <PROPERTY NAME="member" VALUE="1"/>
  <PROPERTY NAME="address" VALUE="********"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.discovery.seed_router"/>
  <PROPERTY NAME="member" VALUE="1"/>
  <PROPERTY NAME="address" VALUE="********"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
  <PROPERTY NAME="netmri_id" VALUE="1"/>
  <PROPERTY NAME="parent" VALUE="/"/>
  <PROPERTY NAME="name" VALUE="test1"/>
  <PROPERTY NAME="id" VALUE="1"/>
</OBJECT>
</DATABASE>
