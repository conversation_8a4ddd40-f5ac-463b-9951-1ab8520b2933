<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="action" VALUE="access_cli"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-superuser"/>
  <PROPERTY NAME="action" VALUE="access_cli"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.test_group"/>
  <PROPERTY NAME="action" VALUE="access_cli"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="755f81c30c63427e9fa480ebe6059352"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="admin-group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="93da7ad557764c50a475df7ed3e07944"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="cloud-api-only"/>
  <PROPERTY NAME="comment" VALUE="Admins allowed to perform API request on Cloud API"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="736014a726224796ad38181513d0fd1c"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="cloud-api-superuser"/>
  <PROPERTY NAME="comment" VALUE="Admins allowed to perform API request on Cloud API with superuser permissions"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="a13624ae2aa64dab93ebe1949f6f5ae5"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="saml-group"/>
  <PROPERTY NAME="comment" VALUE="Admins allowed to perform authentication request through SAML"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="ac8e935c011440eba8f496f699ad01ae"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="splunk-reporting-group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="755f81c30c63427e9fa480ebe6059353"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="test_group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="755f81c30c63427e9fa480ebe6059354"/>
  <PROPERTY NAME="revision_id" VALUE="10"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="super_test_group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="acc525a87c4748f7a60ff5c359f81d21"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="APP_GE003000000_DNSSUPER_GE_72"/>
  <PROPERTY NAME="comment" VALUE="Super User Group for GE Employees"/>
  </OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="81adffd5bb6d49b9a45e0fd276b4dd66"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="6"/>
  <PROPERTY NAME="name" VALUE="admin-group"/>
  <PROPERTY NAME="comment" VALUE="default group"/>
</OBJECT> 
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="527376cf2ce14b0b92bbb353234dfff0"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="6"/>
  <PROPERTY NAME="name" VALUE="APP_GE003000000_DNSSUPER_GE_72"/>
  <PROPERTY NAME="comment" VALUE="Super User Group for GE Employees"/>
</OBJECT> 
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="faa70e1507ad44ba848e01394e331892"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="6"/>
  <PROPERTY NAME="name" VALUE="dhcp-dns"/>
  <PROPERTY NAME="comment" VALUE="Standard DHCP and DNS administration group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="a1750770ac194ab9abd9f57859f78050"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="6"/>
  <PROPERTY NAME="name" VALUE="cloud-api-superuser"/>
  <PROPERTY NAME="comment" VALUE="Admins allowed to perform API request on Cloud API with superuser permissions"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="7669701c7af54cea80c67e0e1097f7b1"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="8"/>
  <PROPERTY NAME="name" VALUE="admin-group"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="8f244d869c1740069b1f0000a3db5dc1"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <ROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="8"/>
  <PROPERTY NAME="name" VALUE="APP_GE003000000_DNSSUPER_GE_72"/>
  <PROPERTY NAME="comment" VALUE="Super User Group for GE Employees"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="a1750770ac194ab9abd9f57859f78050"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="true"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="8"/>
  <PROPERTY NAME="name" VALUE="cloud-api-superuser"/>
  <PROPERTY NAME="comment" VALUE="Admins allowed to perform API request on Cloud API with superuser permissions"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="uuid" VALUE="8c78996da41b4f88b915b2fea2e1b534"/>
  <PROPERTY NAME="revision_id" VALUE="11"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="ui_model" VALUE="FULL_UI"/>
  <PROPERTY NAME="enable_restricted_user_access" VALUE="false"/>
  <PROPERTY NAME="override_grid_password_setting" VALUE="false"/>
  <PROPERTY NAME="override_grid_concurrent_login" VALUE="false"/>
  <PROPERTY NAME="use_enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="enable_account_inactivity_lockout" VALUE="false"/>
  <PROPERTY NAME="inactive_days" VALUE="30"/>
  <PROPERTY NAME="inactivity_lockout_reminder_days" VALUE="15"/>
  <PROPERTY NAME="enable_reactivation_via_serial_console" VALUE="true"/>
  <PROPERTY NAME="enable_reactivation_via_remote_console" VALUE="true"/>
  <PROPERTY NAME="override_grid_sequential_lockout_setting" VALUE="false"/>
  <PROPERTY NAME="enable_sequential_failed_login_attempts_lockout" VALUE="false"/>
  <PROPERTY NAME="sequential_attempts" VALUE="5"/>
  <PROPERTY NAME="failed_lockout_duration" VALUE="5"/>
  <PROPERTY NAME="never_unlock_user" VALUE="false"/>
  <PROPERTY NAME="sub_grid" VALUE="8"/>
  <PROPERTY NAME="name" VALUE="APP_GE003003000_COMPUTE_GECLOUDHUB_COM"/>
  <PROPERTY NAME="comment" VALUE="DNS Admin account for mgmt.cloud.ds.ge.com domain."/>
</OBJECT>

</DATABASE>
