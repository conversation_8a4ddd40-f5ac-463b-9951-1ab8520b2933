
include "/infoblox/var/named_conf/tsig.key";

options {
	zone-statistics yes;
	directory "/infoblox/var/named_conf";
	version none;
	hostname none;
	recursion no;
	listen-on { 127.0.0.1; ***********; };
	query-source address  ***********;
	transfer-source ***********;
	notify-source ***********;
	minimal-responses yes;
	# for service restart: allow_bulkhost_ddns = Refusal
	max-cache-size 400568320;
	allow-transfer { !any; };
	transfer-format many-answers;
};

# Bulk IP Format: "-$-$-$-$"

include "/infoblox/var/named_conf/dhcp_updater.key";

include "/infoblox/var/named_conf/rndc.key";

controls {
	inet 127.0.0.1 port 953
	allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	 channel ib_syslog { 
		 syslog daemon; 
		 severity info; 
	};
	 category default { ib_syslog; };
};
# default
view "_default" {  # default
    match-clients { *******; *******; *******; *******; *******; *******; any; };
    zone "." in {
	type master;
	allow-transfer { *******; *******; };
	notify yes;
    };
    zone "0.0.127.in-addr.arpa" in {
	type master;
    };
    zone "test.com" in {
	type master;
	notify yes;
    };
    zone "auth-sec.test.com" in {
	type slave;
	masters { *******;*******; };
	allow-update-forwarding { none; };
	allow-transfer { *******; *******; };
	file "db.auth-sec.test.com._default";
	also-notify { *******; *******; };
	notify explicit;
    };
    zone "test1.com" in {
	type slave;
	masters { *******;*******; };
	allow-update-forwarding { none; };
	allow-transfer { *******; *******; key "nstwo"; key "nsthree"; };
	file "db.test1.com._default";
	also-notify { *******; *******; *******; 5.1.1.1; 6.1.1.1; };
	notify explicit;
    };
    zone "test2.com" in {
	type master;
	also-notify { 10.0.0.1; *******; };
	allow-transfer { *******; *******; };
	notify yes;
    };
    zone "xyz_test.com" in {
	type slave;
	masters { 10.0.0.1; };
	also-notify { 5.2.2.5; 4.4.5.5; };
	allow-update-forwarding { none; };
	allow-transfer { 5.2.2.5; 4.4.5.5; };
	notify explicit;
    };
    zone "192.in-addr.arpa" in {
	type slave;
	masters { 10.0.0.1; };
	also-notify { 15.2.2.5; 14.4.5.5; };
	allow-update-forwarding { none; };
	allow-transfer { 15.2.2.5; 14.4.5.5; };
	notify explicit;
    };
    zone "8.168.192.in-addr.arpa" in {
	type slave;
	masters { *******;*******; };
	allow-update-forwarding { none; };
	allow-transfer { 15.2.2.5; 14.4.5.5; };
	file "db.8.168.192.in-addr.arpa._default";
	also-notify { 15.2.2.5; 14.4.5.5; };
	notify explicit;
    };
    zone "193.in-addr.arpa" {
	type stub;
	masters { *******; *******; };
	file "db.193.in-addr.arpa._default";
    };
    zone "test_zone_transfer.com" in {
	type master;
	also-notify { 10.0.0.1; *******; };
	allow-transfer { 10.0.0.1; *******; *******; };
	notify yes;
    };
    zone "test_ext_sec_server.com" in {
	type master;
	also-notify { *******; };
	allow-transfer { *******; *******; };
	notify yes;
    };
};
