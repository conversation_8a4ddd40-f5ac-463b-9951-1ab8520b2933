ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/tmp/dhcp_updater.key";

subnet ******** netmask *********** {
	pool {
		infoblox-range ********* *********;
		range ********* *********;
		ddns-updates on;
		option domain-name "test.com";
		ddns-domainname = config-option domain-name;
	}
	pool {
		infoblox-range ********* *********0;
		range ********* *********0;
		ddns-updates on;
		ddns-domainname = config-option domain-name;
	}
	pool {
		infoblox-range *********1 **********;
		range *********1 **********;
		ddns-updates off;
	}
}

subnet *********** netmask ************* {
	not authoritative;
}
zone "test.com." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}

#End of dhcpd.conf file
