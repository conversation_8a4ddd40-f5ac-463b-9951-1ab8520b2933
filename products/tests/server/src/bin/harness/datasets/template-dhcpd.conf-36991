ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;


subnet 20.1.0.0 netmask 255.255.0.0 {
	pool {
		infoblox-range 20.1.0.10 20.1.0.20;
		range 20.1.0.10 20.1.0.20;
		ddns-updates on;
		ddns-domainname = config-option domain-name;
	}
	pool {
		infoblox-range 20.1.0.21 20.1.0.200;
		range 20.1.0.21 20.1.0.200;
		ddns-updates on;
		ddns-domainname = config-option domain-name;
	}
	pool {
		infoblox-range 20.1.0.201 20.1.0.220;
		range 20.1.0.201 20.1.0.220;
		ddns-updates off;
	}
}

}

#End of dhcpd.conf file
