empty.naptr.test.	3600	IN	NAPTR	0 0 "" "" "" .
flags_a1.naptr.test.	3600	IN	NAPTR	100 10 "a" "" "" .
flags_a2.naptr.test.	3600	IN	NAPTR	100 10 "A" "" "" .
flags_p1.naptr.test.	3600	IN	NAPTR	100 10 "p" "" "" .
flags_p2.naptr.test.	3600	IN	NAPTR	100 10 "P" "" "" .
flags_s1.naptr.test.	3600	IN	NAPTR	100 10 "s" "" "" .
flags_s2.naptr.test.	3600	IN	NAPTR	100 10 "S" "" "" .
flags_u1.naptr.test.	3600	IN	NAPTR	100 10 "u" "" "" .
flags_u2.naptr.test.	3600	IN	NAPTR	100 10 "U" "" "" .
naptr.test.		3600	IN	NS	dummy.naptr.test.
naptr.test.		3600	IN	SOA	naptr.test. root.test.com. 1 3600 7200 2592000 43200
naptr.test.		3600	IN	SOA	naptr.test. root.test.com. 1 3600 7200 2592000 43200
protocol_only.naptr.test. 3600	IN	NAPTR	100 10 "" "http" "" .
regexp_special.naptr.test. 3600	IN	NAPTR	50 25 "" "" "!@#$%^&*()-_=+[{]}\\|\;:'\",<.>/?" .
rfc1.naptr.test.	3600	IN	NAPTR	100 10 "" "" "/urn:cid:.+@([^\\.]+\\.)(.*)$/\\2/i" .
rfc2.naptr.test.	3600	IN	NAPTR	100 50 "s" "z3950+I2L+I2C" "" _z3950._tcp.gatech.edu.
rfc3.naptr.test.	3600	IN	NAPTR	100 50 "s" "rcds+I2C" "" _rcds._udp.gatech.edu.
rfc4.naptr.test.	3600	IN	NAPTR	100 50 "s" "http+I2L+I2C+I2R" "" _http._tcp.gatech.edu.
rfc5.naptr.test.	3600	IN	NAPTR	100 90 "" "" "!http://([^/:]+)!\\1!i" .
rfc6.naptr.test.	3600	IN	NAPTR	100 100 "s" "http+I2R" "" _http._tcp.foo.com.
rfc7.naptr.test.	3600	IN	NAPTR	100 100 "s" "ftp+I2R" "" _ftp._tcp.foo.com.
rfc8.naptr.test.	3600	IN	NAPTR	100 10 "u" "sip+E2U" "!^.*$!sip:<EMAIL>!" .
rfc9.naptr.test.	3600	IN	NAPTR	102 10 "u" "mailto+E2U" "!^.*$!mailto:<EMAIL>!" .
