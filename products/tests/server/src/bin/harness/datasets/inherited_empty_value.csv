header-ipv6network,address*,cidr*,always_update_dns,disabled,network_view,EA-TestEA,EAInherited-TestEA
ipv6network,3144:1::,60,TRUE,FALSE,TestNetworkView,TestValue,INHERIT
ipv6network,3144:2::,60,TRUE,FALSE,TestNetworkView,TestValue,OVERRIDE
ipv6network,3144:3::,60,TRUE,FALSE,TestNetworkView,TestValueOverridden,OVERRIDE
ipv6network,3144:4::,60,TRUE,FALSE,TestNetworkView,,DELETE
ipv6network,3144:5::,60,TRUE,FALSE,TestNetworkView,,
ipv6network,3144:1::,64,TRUE,FALSE,TestNetworkView,,
ipv6network,3144:2::,64,TRUE,FALSE,TestNetworkView,,
ipv6network,3144:3::,64,TRUE,FALSE,TestNetworkView,,
ipv6network,3144:4::,64,TRUE,FALSE,TestNetworkView,,
ipv6network,3144:5::,64,TRUE,FALSE,TestNetworkView,,
ipv6network,3144:1::,80,TRUE,FALSE,TestNetworkView,,
ipv6network,3144:2::,80,TRUE,FALSE,TestNetworkView,,
ipv6network,3144:3::,80,TRUE,FALSE,TestNetworkView,,
ipv6network,3144:4::,80,TRUE,FALSE,TestNetworkView,,
ipv6network,3144:5::,80,TRUE,FALSE,TestNetworkView,,
