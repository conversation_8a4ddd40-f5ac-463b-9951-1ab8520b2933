##############################################################################
# Infoblox httpd-ibdelay.conf for test mod_ibdelay purpose only
##############################################################################

ServerRoot "/infoblox/common/htdocs"
CoreDumpDirectory /storage/cores

LockFile /usr/logs/httpd-ibdelay.lock
PidFile /usr/logs/httpd-ibdelay.pid
ScoreBoardFile /usr/logs/httpd-ibdelay.scoreboard

Timeout 300
KeepAlive On
MaxKeepAliveRequests 0
KeepAliveTimeout 150

MinSpareServers 5
MaxSpareServers 10
StartServers 5
MaxClients 150
MaxRequestsPerChild 100

User nobody
Group nobody

ServerAdmin <EMAIL>
ServerName localhost

DocumentRoot "/infoblox/common/htdocs"

LoadModule ssl_module /usr/modules/mod_ssl.so

ErrorLog /infoblox/var/authdhcp-ibdelay.log
LoadModule ibdelay_module /infoblox/one/lib/mod_ibdelay.so
Listen 127.0.0.1:1080
Listen 127.0.0.1:10443

<VirtualHost 127.0.0.1:1080>
    IBDelay 1
</VirtualHost>

<VirtualHost 127.0.0.1:10443>
    IBDelay 2
    SSLEngine on
    SSLProtocol -ALL +TLSv1
    SSLCipherSuite ALL:!aNULL:!ADH:!eNULL:!LOW:!EXP:RC4+RSA:+HIGH:+MEDIUM
    SSLCertificateFile /infoblox/security/certs/one-httpd.crt
    SSLCertificateKeyFile /infoblox/security/keys/one-httpd.key
</VirtualHost>



