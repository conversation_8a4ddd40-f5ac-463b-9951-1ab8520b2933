
db_import;db;incr;{gen_id{}}:N;{gen_id{}}:N

<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
	<SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
		<ib:ObjectReadResponse>
			<total_size>1</total_size>
			<objects>
				<item xsi:type="ib:ExtensibleAttributeDefinition">
					<allowed_object_types></allowed_object_types>
					<attribute_type>INTEGER</attribute_type>
					<comment>Sub grid int ext_ettr_def updated</comment>
					<gui_default_value></gui_default_value>
					<flags>V</flags>
					<value_syntax></value_syntax>
					<name>int_ead</name>
					<uuid>{rec_id{.com.infoblox.one.extensible_attributes_def$.int_ead}}</uuid>
				</item>
			</objects>
		</ib:ObjectReadResponse>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

