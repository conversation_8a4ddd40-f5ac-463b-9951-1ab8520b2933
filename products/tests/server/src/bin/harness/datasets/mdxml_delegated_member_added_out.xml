<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_template"/>
<PROPERTY NAME="cidr" VALUE="24"/>
<PROPERTY NAME="allow_any_netmask" VALUE="false"/>
<PROPERTY NAME="auto_create_reversezone" VALUE="false"/>
<PROPERTY NAME="authoritative" VALUE="0"/>
<PROPERTY NAME="lease_time" VALUE="43200"/>
<PROPERTY NAME="pxe_lease_time_enabled" VALUE="false"/>
<PROPERTY NAME="override_boot_file" VALUE="false"/>
<PROPERTY NAME="override_boot_server" VALUE="false"/>
<PROPERTY NAME="override_next_server" VALUE="false"/>
<PROPERTY NAME="override_broadcast_address" VALUE="false"/>
<PROPERTY NAME="override_routers" VALUE="false"/>
<PROPERTY NAME="override_custom_options" VALUE="false"/>
<PROPERTY NAME="override_domain_name" VALUE="false"/>
<PROPERTY NAME="override_authoritative" VALUE="false"/>
<PROPERTY NAME="override_domain_name_servers" VALUE="false"/>
<PROPERTY NAME="override_lease_time" VALUE="false"/>
<PROPERTY NAME="override_pxe_lease_time" VALUE="false"/>
<PROPERTY NAME="override_ddns_updates" VALUE="false"/>
<PROPERTY NAME="ddns_updates_enabled" VALUE="false"/>
<PROPERTY NAME="override_ddns_use_client_fqdn" VALUE="false"/>
<PROPERTY NAME="ddns_use_client_fqdn" VALUE="false"/>
<PROPERTY NAME="override_ddns_no_client_fqdn" VALUE="false"/>
<PROPERTY NAME="ddns_no_client_fqdn" VALUE="false"/>
<PROPERTY NAME="override_ddns_server_use_fqdn" VALUE="false"/>
<PROPERTY NAME="ddns_server_use_fqdn" VALUE="false"/>
<PROPERTY NAME="override_update_static_leases" VALUE="false"/>
<PROPERTY NAME="update_static_leases" VALUE="false"/>
<PROPERTY NAME="override_dhcp_thresholds" VALUE="false"/>
<PROPERTY NAME="enable_dhcp_thresholds" VALUE="false"/>
<PROPERTY NAME="range_high_water_mark" VALUE="95"/>
<PROPERTY NAME="range_high_water_mark_reset" VALUE="85"/>
<PROPERTY NAME="range_low_water_mark" VALUE="0"/>
<PROPERTY NAME="range_low_water_mark_reset" VALUE="10"/>
<PROPERTY NAME="enable_threshold_email_warnings" VALUE="false"/>
<PROPERTY NAME="enable_threshold_snmp_warnings" VALUE="false"/>
<PROPERTY NAME="override_threshold_email_notification" VALUE="false"/>
<PROPERTY NAME="override_recycle_leases" VALUE="false"/>
<PROPERTY NAME="recycle_leases" VALUE="true"/>
<PROPERTY NAME="ddns_ttl" VALUE="0"/>
<PROPERTY NAME="override_ignore_dhcp_param_request_list" VALUE="false"/>
<PROPERTY NAME="ignore_dhcp_param_request_list" VALUE="false"/>
<PROPERTY NAME="override_deny_bootp_request" VALUE="false"/>
<PROPERTY NAME="deny_bootp" VALUE="false"/>
<PROPERTY NAME="override_update_dns_on_renew" VALUE="false"/>
<PROPERTY NAME="update_dns_on_renew" VALUE="false"/>
<PROPERTY NAME="override_client_association_grace_period" VALUE="false"/>
<PROPERTY NAME="client_association_grace_period" VALUE="-1"/>
<PROPERTY NAME="use_preferred_lifetime" VALUE="false"/>
<PROPERTY NAME="rir_registration_action" VALUE="NONE"/>
<PROPERTY NAME="send_rir_request" VALUE="false"/>
<PROPERTY NAME="rir_registration_status" VALUE="NOT_REGISTERED"/>
<PROPERTY NAME="use_ipam_trap_settings" VALUE="false"/>
<PROPERTY NAME="use_ipam_threshold_settings" VALUE="false"/>
<PROPERTY NAME="use_ipam_email_addresses" VALUE="false"/>
<PROPERTY NAME="cloud_api_compatible" VALUE="false"/>
<PROPERTY NAME="parent" VALUE="."/>
<PROPERTY NAME="name" VALUE="my_nw_template"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="override_ddns_domainname" VALUE="false"/>
<PROPERTY NAME="override_ddns_ttl" VALUE="false"/>
<PROPERTY NAME="preferred_lifetime" VALUE="27000"/>
<PROPERTY NAME="delegated_member" VALUE="."/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.dhcp_range_template"/>
<PROPERTY NAME="offset" VALUE="10"/>
<PROPERTY NAME="number_of_addresses" VALUE="10"/>
<PROPERTY NAME="server_association_type" VALUE="None"/>
<PROPERTY NAME="lease_time" VALUE="43200"/>
<PROPERTY NAME="override_boot_file" VALUE="false"/>
<PROPERTY NAME="override_boot_server" VALUE="false"/>
<PROPERTY NAME="override_next_server" VALUE="false"/>
<PROPERTY NAME="override_broadcast_address" VALUE="false"/>
<PROPERTY NAME="override_routers" VALUE="false"/>
<PROPERTY NAME="override_custom_options" VALUE="false"/>
<PROPERTY NAME="override_domain_name" VALUE="false"/>
<PROPERTY NAME="override_domain_name_servers" VALUE="false"/>
<PROPERTY NAME="override_lease_time" VALUE="false"/>
<PROPERTY NAME="override_pxe_lease_time" VALUE="false"/>
<PROPERTY NAME="override_dhcp_thresholds" VALUE="false"/>
<PROPERTY NAME="enable_dhcp_thresholds" VALUE="false"/>
<PROPERTY NAME="range_high_water_mark" VALUE="95"/>
<PROPERTY NAME="range_high_water_mark_reset" VALUE="85"/>
<PROPERTY NAME="range_low_water_mark" VALUE="0"/>
<PROPERTY NAME="range_low_water_mark_reset" VALUE="10"/>
<PROPERTY NAME="enable_threshold_email_warnings" VALUE="false"/>
<PROPERTY NAME="enable_threshold_snmp_warnings" VALUE="false"/>
<PROPERTY NAME="override_threshold_email_notification" VALUE="false"/>
<PROPERTY NAME="override_ddns_updates" VALUE="false"/>
<PROPERTY NAME="ddns_updates_enabled" VALUE="false"/>
<PROPERTY NAME="override_ddns_no_client_fqdn" VALUE="false"/>
<PROPERTY NAME="ddns_no_client_fqdn" VALUE="false"/>
<PROPERTY NAME="override_recycle_leases" VALUE="false"/>
<PROPERTY NAME="recycle_leases" VALUE="true"/>
<PROPERTY NAME="override_deny_bootp_request" VALUE="false"/>
<PROPERTY NAME="deny_bootp" VALUE="false"/>
<PROPERTY NAME="update_serial_primary" VALUE="0"/>
<PROPERTY NAME="update_serial_secondary" VALUE="0"/>
<PROPERTY NAME="deny_all_clients" VALUE="false"/>
<PROPERTY NAME="override_ignore_dhcp_param_request_list" VALUE="false"/>
<PROPERTY NAME="ignore_dhcp_param_request_list" VALUE="false"/>
<PROPERTY NAME="use_known_clients" VALUE="false"/>
<PROPERTY NAME="use_unknown_clients" VALUE="false"/>
<PROPERTY NAME="override_update_dns_on_renew" VALUE="false"/>
<PROPERTY NAME="update_dns_on_renew" VALUE="false"/>
<PROPERTY NAME="override_client_association_grace_period" VALUE="false"/>
<PROPERTY NAME="client_association_grace_period" VALUE="-1"/>
<PROPERTY NAME="override_ms_custom_options" VALUE="false"/>
<PROPERTY NAME="cloud_api_compatible" VALUE="false"/>
<PROPERTY NAME="parent" VALUE="."/>
<PROPERTY NAME="name" VALUE="my_range_template"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="pxe_lease_time_enabled" VALUE="false"/>
<PROPERTY NAME="override_ddns_domainname" VALUE="false"/>
<PROPERTY NAME="delegated_member" VALUE="."/>
</OBJECT>

</DATABASE>
