version 1.0

Header-ARecord,fqdn,view,address*,comment,disabled,ttl
ARecord,a1.test_csv.com,default,*********,A record,FALSE,7200

Header-AaaaRecord,fqdn*,view,address,,comment,disabled,ttl
AaaaRecord,a4.test_csv.com,default,2001::123,nul column test,AAAA record,FALSE,7200

Header-ARecord,fqdn,view,address,comment,disabled,ttl,create_ptr
ARecord,a11.test_csv.com,default,*********,A11 record,FALSE,,TRUE

Header-ARecord,fqdn,view,address,comment,disabled,ttl
ARecord,a12.test_csv.com,default,*********,A12 record,FALSE,4200

Header-CnameRecord,fqdn,view,canonical_name,comment,disabled,ttl
CnameRecord,c1.test_csv.com,default,a1.test_csv.com,C1 record,FALSE,3600

Header-DnameRecord,fqdn,view,target,comment,disabled,ttl
DnameRecord,d1.test_csv.com,default,target.test_csv.com,D1 record,TRUE,100

Header-MxRecord,fqdn,view,mx,priority,comment,disabled,ttl
MxRecord,d1.test_csv.com,default,webmaster.infoblox.com,10,D1 record,TRUE,500

Header-NaptrRecord,fqdn,view,preference,services,regexp,flags,order,replacement,comment,disabled,ttl
NaptrRecord,n1.test_csv.com,default,20,SIP+D2U,,U,10,test.com,NAPTR1 record,FALSE,1000

Header-NsRecord,fqdn,view,dname,zone_nameservers
NsRecord,test_csv.com,default,dname.test.com,"*******/FALSE,*******/TRUE"
NsRecord,test_csv.com,default,dname2.test.com,"""*******/FALSE,*******/TRUE"""

Header-SrvRecord,fqdn,view,target,port,weight,priority,comment,disabled,ttl
SrvRecord,s1.test_csv.com,default,target.test.com,6666,5,10,SRV1 record,FALSE,500

Header-TxtRecord,fqdn,view,text,comment,disabled,ttl
TxtRecord,t1.test_csv.com,default,hello world,TXT1 record,FALSE,6667

Header-HostRecord,fqdn,view,addresses,ipv6_addresses,aliases,configure_for_dns,comment,disabled,ttl
HostRecord,h1.test_csv.com,default,"*******,*******","2001:0db8:85a3:0000:0000:8a2e:0370:7334,fc00::","alias1.test.com,alias2.test.com",TRUE,TXT1 record,False,6667

Header-HostAddress,parent,address,pxe_lease_time_enabled,lease_time,broadcast_address,pxe_lease_time,ignore_dhcp_param_request_list,match_option,domain_name_servers,domain_name,mac_address,routers,boot_server,configure_for_dhcp,deny_bootp,next_server
HostAddress,h1.test_csv.com,*******,TRUE,40,*******,50,FALSE,FALSE,"*******,*******",hostaddr1,00:01:02:03:04:05,"*******,*******",*******,FALSE,FALSE,*******

Header-IPv6HostAddress,parent,address
IPv6HostAddress,h1.test_csv.com,2000:0db8:85a3:0000:0000:8a2e:0370:7334

header-ptrrecord,dname*,_new_dname,address,_new_address,comment,disabled,fqdn,_new_fqdn,ttl,view
ptrrecord,xyz.foo.com,,,,,False,p1.test_csv.com,,,default
