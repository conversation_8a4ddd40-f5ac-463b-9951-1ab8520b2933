<?xml version="1.0" ?>

<datatype>
  <enum name='ib_monitor_type_t'>
    <?sgu_enforce ANNOTATE_SEUT_GROUP("OPCODES_MONITOR")?>
    <namevalue name='e_monitor_type_node_status' value='1'/>
    <namevalue name='e_monitor_type_clusterd' value='2'/>
    <namevalue name='e_monitor_type_fan' value='23'/>
    <namevalue name='e_monitor_type_network_capacity' value='57'/>
    <namevalue name='e_monitor_type_sgm' value='58'/>
    <?sgu_enforce ANNOTATE_SEUT_END?>
    <namevalue name='e_monitor_type_last' value='59'/>
  </enum>

  <!-- Version for monitor type -->
  <?sgu_enforce ANNOTATE_SEUT_GROUP("VERSIONS_MONITOR")?>
  <constant type='integer' name='MONITOR_TYPE_NODE_STATUS_VERSION' value='10' />
  <constant type='integer' name='MON<PERSON>OR_TYPE_CLUSTERD_VERSION' value='10' />
  <constant type='integer' name='MONITOR_TYPE_FAN_VERSION' value='11' />
  <constant type='integer' name='MONITOR_TYPE_NETWORK_CAPACITY_VERSION' value='10' />
  <constant type='integer' name='MONITOR_TYPE_SGM_VERSION' value='10' />
  <?sgu_enforce ANNOTATE_SEUT_END?>

</datatype>
