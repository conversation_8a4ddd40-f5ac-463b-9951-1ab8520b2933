SOF

ANNOTATE_SEUT_GROUP("OPCODES_CONTROLD_DNS")
#define DM_VIEW_DNS_STATS_REQUEST         0x10000001
ANNOTATE_SEUT_END

ANNOTATE_SEUT_GROUP("VERSIONS_CONTROLD")
#define DM_VIEW_DNS_STATS_REQUEST_VERSION      10
ANNOTATE_SEUT_END

#define IOBJECT_MAX_STRING_LENGTH 1024

ANNOTATE_SEUT_MSG_OUT("DM_VIEW_DNS_STATS_REQUEST")
typedef struct
{
    char m_ms_dhcp_server_oid[IOBJECT_MAX_STRING_LENGTH];
} a_new_item_v10_t;

typedef a_new_item_v10_t a_new_item_t;

ANNOTATE_SEUT_END
