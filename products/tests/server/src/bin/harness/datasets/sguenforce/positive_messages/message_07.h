SOF // it's just random text actually

#define IB_CRA_MAX_SIZE 100

ANNOTATE_SEUT_MSG_IN("CLUSTERD_PROTOCOL_VERSION_CURRENT","XXX")
// Sent by replica to respond to challenge, request tunnel

typedef   struct   {
  clusterd_msg_header_t m_head;
  ib_uint32_t m_unused;
  char m_r2[IB_CRA_MAX_SIZE];
} clusterd_msg_handshake_resp_req_v20_t;

typedef struct
{
  clusterd_msg_header_t m_head;
  ib_uint32_t m_unused;
  int a_new_member;
  char m_r2[IB_CRA_MAX_SIZE];
} clusterd_msg_handshake_resp_req_v40_t;

typedef clusterd_msg_handshake_resp_req_v20_t clusterd_msg_handshake_resp_req_v30_t;
typedef clusterd_msg_handshake_resp_req_v40_t clusterd_msg_handshake_resp_req_t;

ANNOTATE_SEUT_END

EOF
