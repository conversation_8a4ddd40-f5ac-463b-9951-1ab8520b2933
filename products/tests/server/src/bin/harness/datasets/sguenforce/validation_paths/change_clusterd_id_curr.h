ANNOTATE_SEUT_GROUP("VERSIONS_PROTOCOL_CLUSTERD")
#define CLUSTERD_PROTOCOL_VERSION_NIOS_63  25
#define CLUSTERD_PROTOCOL_VERSION_VPN_MTU               5
#define CLUSTERD_PROTOCOL_VERSION_DOWNGRADE_SUPPORT     4
ANNOTATE_SEUT_PROTOCOL_CURRENT
#define CLUSTERD_PROTOCOL_VERSION_CURRENT CLUSTERD_PROTOCOL_VERSION_NIOS_63
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("CLUSTERD_PROTOCOL_VERSION_CURRENT")
typedef struct
{
  clusterd_msg_header_t m_head;
  char m_pnode_oid[16];
} clusterd_msg_node_op_v4_t;

typedef clusterd_msg_node_op_v4_t clusterd_msg_node_op_t;
ANNOTATE_SEUT_END


ANNOTATE_SEUT_MSG_IN("CLUSTERD_PROTOCOL_VERSION_CURRENT")
typedef struct
{
  clusterd_msg_header_t m_head;
  ib_uint32_t m_status;
} clusterd_msg_iipc_response_v4_t;

typedef clusterd_msg_iipc_response_v4_t clusterd_msg_iipc_response_t;
ANNOTATE_SEUT_END
