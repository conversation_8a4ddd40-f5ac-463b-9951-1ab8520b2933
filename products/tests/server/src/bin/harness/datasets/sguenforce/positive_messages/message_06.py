id_refs = ['CLUSTERD_PROTOCOL_VERSION_CURRENT', 'XXX'  ]
md5sum = '78bee3b720707f8373066131787be90d'
direction = 'IN'

messages = [
    {'identifier_name' : 'clusterd_msg_handshake_resp_req_v20_t',
     'versioned' : True,
     'direction' : direction,
     'id_refs' : id_refs,
     'md5sum' : md5sum
     },
    {'identifier_name' : 'clusterd_msg_handshake_resp_req_v40_t',
     'versioned' : True,
     'direction' : direction,
     'id_refs' : id_refs,
     'md5sum' : 'bf622ec93c31404896d3047aef1d1a26'
     },
    {'identifier_name' : 'clusterd_msg_handshake_resp_req_v30_t',
     'versioned' : True,
     'direction' : direction,
     'id_refs' : id_refs,
     'md5sum' : md5sum
     },
    {'identifier_name' : 'clusterd_msg_handshake_resp_req_t',
     'versioned' : False,
     'direction' : direction,
     'id_refs' : id_refs,
     'md5sum' : 'bf622ec93c31404896d3047aef1d1a26'
     },
]

expected = {'messages' : messages,
            'groups' : []}
