#define IB_CRA_MAX_SIZE 100

ANNOTATE_SEUT_MSG_IN("CLUSTERD_PROTOCOL_VERSION_CURRENT","XXX")
// it must generate the same output as message_02.h - just different order.
typedef struct
{
  clusterd_msg_header_t m_head;
  ib_uint32_t m_unused;
  unsigned volatile char m_r2[IB_CRA_MAX_SIZE];
  unsigned int m_r;
} clusterd_msg_handshake_resp_req_v20_t;

typedef clusterd_msg_handshake_resp_req_v20_t clusterd_msg_handshake_resp_req_t;

ANNOTATE_SEUT_END
