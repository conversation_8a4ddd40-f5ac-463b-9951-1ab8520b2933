SOF

#define IOBJECT_MAX_STRING_LENGTH 1024
#define MAX_IPV4_ADDR_SZ 16

ANNOTATE_SEUT_GROUP("OPCODES_CONTROLD_DHCP")
#define DM_DHCP_FO_STATE_CHANGE_REQUEST         0x2000000a
#define DM_DHCP_LEASE_STATE_CHANGE_REQUEST      0x2000000b
#define DM_DHCP_CLEAR_MS_LEASE_REQUEST         0xc        // (*)
#define DM_DHCP_STATS_REQUEST               0x2000000d

ANNOTATE_SEUT_END


// Start of DHCP operation version
ANNOTATE_SEUT_GROUP("VERSIONS_CONTROLD")
#define DM_DHCP_LEASE_STATE_CHANGE_REQUEST_VERSION      11
#define DM_DHCP_CLEAR_MS_LEASE_REQUEST_VERSION          10 // (*)
#define DM_DHCP_V6_STATS_REQUEST_VERSION                10
ANNOTATE_SEUT_END




ANNOTATE_SEUT_MSG_OUT("DM_DHCP_CLEAR_MS_LEASE_REQUEST")
typedef struct
{
    char m_ms_dhcp_server_oid[IOBJECT_MAX_STRING_LENGTH];
    char m_lease_ip[MAX_IPV4_ADDR_SZ];
} dhcp_clear_ms_lease_req_v10_t;

typedef dhcp_clear_ms_lease_req_v10_t dhcp_clear_ms_lease_req_v20_t;
typedef dhcp_clear_ms_lease_req_v20_t dhcp_clear_ms_lease_req_t;

ANNOTATE_SEUT_END

