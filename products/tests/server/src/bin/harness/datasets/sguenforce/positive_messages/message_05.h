SOF

ANNOTATE_SEUT_MSG_IN("CLUSTERD_PROTOCOL_VERSION_CURRENT","XXX")

// gcc packed attribute is here

typedef struct
{
  ib_uint8_t m_monitor_type;
  ib_uint8_t m_ui_state;
  ib_uint32_t m_id;
  ib_uint64_t m_status;
  ib_uint32_t m_monitor_event;
  ib_uint32_t m_monitor_timestamp;
  ib_uint32_t m_version;
} __attribute__((__packed__)) ib_monitor_data_v3_t;

typedef ib_monitor_data_v3_t ib_monitor_data_t;

ANNOTATE_SEUT_END



EOF
