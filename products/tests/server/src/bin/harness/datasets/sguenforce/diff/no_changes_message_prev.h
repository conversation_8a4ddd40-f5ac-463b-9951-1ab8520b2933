SOF

#define IOBJECT_MAX_STRING_LENGTH 1024
#define MAX_IPV4_ADDR_SZ 16

ANNOTATE_SEUT_GROUP("OPCODES_CONTROLD_DHCP")
#define DM_DHCP_LEASE_HISTORY_TRANSFER          0x20000001
#define DM_DHCP_LEASE_HISTORY_DOWNLOAD          0x20000002
#define DM_DHCP_LEASE_HISTORY_UNPACK            0x20000003
#define DM_DHCP_LEASE_HISTORY_IMPORT            0x20000004
#define DM_DHCP_EXPERT_MODE_CONFIG_UPLOAD       0x20000007
#define DM_DHCP_EXPERT_MODE_CONFIG_DOWNLOAD     0x20000008
#define DM_DHCP_EXPERT_MODE_CONFIG_REMOVE       0x20000009
#define DM_DHCP_FO_STATE_CHANGE_REQUEST         0x2000000a
#define DM_DHCP_LEASE_STATE_CHANGE_REQUEST      0x2000000b
#define DM_DHCP_CLEAR_MS_LEASE_REQUEST         0xc
#define DM_DHCP_STATS_REQUEST               0x2000000d
#define DM_DHCP_CLEAR_NAC_AUTH_CACHE_REQUEST    0x2000000e
#define DM_DHCP_V6_STATS_REQUEST                0x2000000f
#define DM_DHCP_SET_NAC_FILTERS_SWITCH          0x20000010        
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_CLEAR_MS_LEASE_REQUEST")
typedef struct
{
    char m_ms_dhcp_server_oid[IOBJECT_MAX_STRING_LENGTH];
    char m_lease_ip[MAX_IPV4_ADDR_SZ];
} dhcp_clear_ms_lease_req_v10_t;

typedef dhcp_clear_ms_lease_req_v10_t dhcp_clear_ms_lease_req_v20_t;
typedef dhcp_clear_ms_lease_req_v20_t dhcp_clear_ms_lease_req_t;

ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_IN("DM_DHCP_CLEAR_MS_LEASE_REQUEST")
typedef struct
{
    ib_bool_t m_ret_flag;
    char m_err_str[IOBJECT_MAX_STRING_LENGTH];
} dhcp_clear_ms_lease_resp_v10_t;

typedef dhcp_clear_ms_lease_resp_v10_t dhcp_clear_ms_lease_resp_t;
ANNOTATE_SEUT_END
