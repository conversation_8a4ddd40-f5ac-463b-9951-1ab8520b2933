group_type = 'VERSIONS'

values = [
    {'protocolLevel': True,
     'protocolCurrent': False,
     'id': 'CLUSTERD_PROTOCOL_VERSION_CROSS_VERSION_6X',
     'value': '21'},

    {'protocolLevel': True,
     'protocolCurrent': False,
     'id': 'CLUSTERD_PROTOCOL_VERSION_CROSS_VERSION',
     'value': '20'},

    {'protocolLevel': True,
     'protocolCurrent': False,
     'id': 'CLUSTERD_PROTOCOL_VERSION_DOWNGRADE_SUPPORT',
     'value': '4'},

    {'protocolLevel': True,
     'protocolCurrent': True,
     'id': 'CLUSTERD_PROTOCOL_VERSION_CURRENT',
     'value': '21'},
    ]


groups = [
    { 'group_label': 'VERSIONS_PROTOCOL_CLUSTERD',
      'type' : group_type,
      'values' : values
      },
    ]

expected = {'groups' : groups,
            'messages' : []
            }
