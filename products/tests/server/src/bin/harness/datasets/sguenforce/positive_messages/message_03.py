id_refs = ['DM_DISCOVERY_CONTROL_REQUEST', ]
md5sum = 'b82c2e2b5567e31ec31b7e25c4c61f1e'
direction = 'OUT'

messages = [
    {'identifier_name' : 'dns_discovery_control_v10_t',
     'versioned' : True,
     'direction' : direction,
     'id_refs' : id_refs,
     'md5sum' : md5sum
     },
    {'identifier_name' : 'dns_discovery_control_t',
     'versioned' : False,
     'direction' : direction,
     'id_refs' : id_refs,
     'md5sum' : md5sum
     },
]

expected = {'messages' : messages,
            'groups' : []}
