group_type = 'VERSIONS'

dict1 = {'values': [{'protocolLevel': True,
                     'protocolCurrent': False,
                     'id': 'CLUSTERD_PROTOCOL_VERSION_CROSS_VERSION',
                     'value': '20'},
                    {'protocolLevel': True,
                     'protocolCurrent': False,
                     'id': 'CLUSTERD_PROTOCOL_VERSION_MGM',
                     'value': '9'},
                    {'protocolLevel': True,
                     'protocolCurrent': False,
                     'id': 'CLUSTERD_PROTOCOL_VERSION_DOWNGRADE_SUPPORT',
                     'value': '4'}
                    ],
         'type': group_type,
         'group_label': 'VERSIONS_PROTOCOL_CLUSTERD'
         }

dict2 = {'values': [{'protocolLevel': True,
                     'protocolCurrent': False,
                     'id': 'DM_PROTOCOL_VERSION_LEGACY',
                     'value': '2'},
                    {'protocolLevel': True,
                     'protocolCurrent': False,
                     'id': 'DM_PROTOCOL_VERSION_CROSS_VERSION',
                     'value': '291'},
                    {'protocolLevel': True,
                     'protocolCurrent': True,
                     'id': 'DM_PROTOCOL_VERSION',
                     'value': '291'}
                    ],
         'type': group_type,
         'group_label': 'VERSIONS_PROTOCOL_CONTROLD'
         }

expected = {'groups' : [dict1, dict2],
            'messages' : []
            }
