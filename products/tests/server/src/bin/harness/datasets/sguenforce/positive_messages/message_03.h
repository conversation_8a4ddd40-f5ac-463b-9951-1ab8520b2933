SOF

// testing enum as a type inside a structure

#define IOBJECT_MAX_STRING_LENGTH 1024

ANNOTATE_SEUT_MSG_OUT("DM_DISCOVERY_CONTROL_REQUEST")
typedef struct
{
  enum e_discovery_control_type m_control_type; // control type (see below)
  char m_discovery_task_oid[IOBJECT_MAX_STRING_LENGTH]; // task oid
  ib_uint32_t m_search_options;
} dns_discovery_control_v10_t;

typedef dns_discovery_control_v10_t dns_discovery_control_t;
ANNOTATE_SEUT_END


EOF
