group_type = 'OPCODES'

values = [{'id': 'e_monitor_type_node_status',
           'versioned': True,
           'value': '1'},

          {'id': 'e_monitor_type_clusterd',
           'versioned': True,
           'value': '2'},

          {'id': 'e_monitor_type_controld',
           'versioned': False,
           'value': '3'},

          {'id': 'e_monitor_type_dhcp',
           'versioned': True,
           'value': '4'}
          ]


groups = [
    {'group_label': 'OPCODES_MONITOR',
     'type' : group_type,
     'values' : values
     },
    ]

expected = {'groups' : groups,
            'messages' : []
            }
