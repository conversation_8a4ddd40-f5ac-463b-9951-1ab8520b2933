SOF

ANNOTATE_SEUT_GROUP("OPCODES_CONTROLD_DNS")
#define DM_ONE_FORCED_RESTORE_REQUEST       0x00000004
#define DM_ONE_SET_MEMBER_TIME          0x00000005
#define DM_ONE_MERGE_LOG_DOWNLOAD          0x000000045
ANNOTATE_SEUT_END

ANNOTATE_SEUT_GROUP("OPCODES_CONTROLD_DHCP")
#define DM_ONE_FORCED_RESTORE_REQUEST       0x00000004
#define DM_ONE_SET_MEMBER_TIME          0x00000005
#define DM_ONE_MERGE_LOG_DOWNLOAD2          0x000000045
ANNOTATE_SEUT_END

ANNOTATE_SEUT_GROUP("VERSIONS_CONTROLD")
#define DM_DHCP_LEASE_STATE_CHANGE_REQUEST_VERSION      11
#define DM_DHCP_V6_STATS_REQUEST_VERSION                10
#define DM_ONE_MERGE_LOG_DOWNLOAD_VERSION  1
ANNOTATE_SEUT_END

ANNOTATE_SEUT_GROUP("VERSIONS_CONTROLD")
#define DM_ONE_MERGE_LOG_DOWNLOAD2_VERSION  1
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_ONE_MERGE_LOG_DOWNLOAD")
typedef struct
{
  int                 m_start_line;
} one_file_view_v10_t;

typedef one_file_view_v10_t one_file_view_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_ONE_MERGE_LOG_DOWNLOAD2")
typedef struct
{
  int m_start_line;
} z_view_v10_t;

typedef z_view_v10_t z_view_t;
ANNOTATE_SEUT_END

EOF
