
#define IOBJECT_MAX_STRING_LENGTH 1024
#define MAX_IPV4_ADDR_SZ 16
#define MAX_IPV6_ADDR_SZ 46

ANNOTATE_SEUT_GROUP("OPCODES_CONTROLD_DHCP")
#define DM_DHCP_LEASE_STATE_CHANGE_REQUEST      0x2000000b
ANNOTATE_SEUT_END

ANNOTATE_SEUT_GROUP("VERSIONS_CONTROLD")
#define DM_DHCP_LEASE_STATE_CHANGE_REQUEST_VERSION      11
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_OUT("DM_DHCP_LEASE_STATE_CHANGE_REQUEST")
typedef struct
{
  char  m_lease_state[IOBJECT_MAX_STRING_LENGTH];
  char  m_dhcp_srvr_ip_addr[MAX_IPV4_ADDR_SZ];
  char  m_lease_ip[MAX_IPV4_ADDR_SZ];
  char  m_dhcp_updater_key[IOBJECT_MAX_STRING_LENGTH];
} dhcp_lease_state_change_req_v10_t;

typedef struct
{
  char  m_lease_state[IOBJECT_MAX_STRING_LENGTH];
  char  m_dhcp_srvr_ip_addr[MAX_IPV4_ADDR_SZ];
  char  m_lease_ip[MAX_IPV6_ADDR_SZ];
  char  m_dhcp_updater_key[IOBJECT_MAX_STRING_LENGTH];
  char m_duid[IOBJECT_MAX_STRING_LENGTH];
} dhcp_lease_state_change_req_v11_t;

typedef dhcp_lease_state_change_req_v11_t dhcp_lease_state_change_req_t;
ANNOTATE_SEUT_END

ANNOTATE_SEUT_MSG_IN("DM_DHCP_LEASE_STATE_CHANGE_REQUEST")
typedef struct
{
  char m_duid[IOBJECT_MAX_STRING_LENGTH];
} change_req_v11_t;

typedef change_req_v11_t change_req_t;
ANNOTATE_SEUT_END

EOF
