SOF

#define MAX_FILE_NAME_SZ          128
#define IOBJECT_MAX_STRING_LENGTH 1024

ANNOTATE_SEUT_MSG_IN("CLUSTERD_PROTOCOL_VERSION_CURRENT","XXX")
// #define inside struct

typedef struct
{
  int                 m_start_line; // index of first line to be in response (may be <0 to indicate that it is in reverse mode, starting from the end of the
  int                 m_requested_lines_count; // number of lines requested in response (actual number can be smaller if for instance we reach the end of fil
  char                m_destination_file_name[MAX_FILE_NAME_SZ]; // file path of resulting file on grid master
  char                m_param[MAX_FILE_NAME_SZ]; // optional parameter used for some file selections
  char                m_file_selection; // one of values defined below
  char                m_search_string[IOBJECT_MAX_STRING_LENGTH];
  ib_bool_t           m_is_search_mode;

  // Do NOT modify these constants without also updating
  // the corresponding constants in ONEFunction.java.
#define               DHCP_CFG_FILE       1
#define               DNS_CFG_FILE        2
#define               DNS_CACHE_FILE      3
// #define               DNS_SS_FILE         4   OBSOLETE
#define               SYS_AUDIT_LOG       5
#define               SYS_LOG             6
#define               SYS_AUDIT_LOG_RESET 7
#define               UNUSED8         8
#define               SYS_AUDIT_LOG_COMBINED 9
#define               SYSLOG_RESET        10
#define               RADIUS_CFG_FILE     11
#define               MSMGMT_LOG 12
} one_file_view_v10_t;

typedef one_file_view_v10_t one_file_view_t;

ANNOTATE_SEUT_END



EOF
