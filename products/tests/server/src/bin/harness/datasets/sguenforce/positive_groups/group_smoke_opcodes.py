group_type = 'OPCODES'

values = [{'id': 'DM_ONE_FORCED_RESTORE_REQUEST',
           'versioned': True,
           'value': '4'},

          {'id': 'DM_ONE_SET_MEMBER_TIME',
           'versioned': True,
           'value': '5'},

          {'id': 'DM_ONE_DB_MERGE_STATS_REQUEST',
           'versioned': False,
           'value': '6'},

          {'id': 'DM_ONE_MERGE_LOG_DOWNLOAD',
           'versioned': True,
           'value': '7'},

          {'id': 'DM_ONE_DB_MERGE_REQUEST',
           'versioned': False,
           'value': '8'},

          {'id': 'DM_ONE_RET_UPGRADE_FILE_REQUEST',
           'versioned': True,
           'value': '9'},

          {'id': 'DM_ONE_RET_UPG_PROGRESS_STATUS_REQUEST',
           'versioned': False,
           'value': '10'}
          ]


groups = [
    { 'group_label': 'OPCODES_CONTROLD_ONE',
      'type' : group_type,
      'values' : values
      },
    ]

expected = {'groups' : groups,
            'messages' : []
            }
