id_refs = ['CLUSTERD_PROTOCOL_VERSION_CURRENT', 'XXX'  ]
md5sum = '041eb26f8ce693189c27a18bbe7aea56'
direction = 'IN'

messages = [
    {'identifier_name' : 'ib_monitor_data_v3_t',
     'versioned' : True,
     'direction' : direction,
     'id_refs' : id_refs,
     'md5sum' : md5sum
     },
    {'identifier_name' : 'ib_monitor_data_t',
     'versioned' : False,
     'direction' : direction,
     'id_refs' : id_refs,
     'md5sum' : md5sum
     },
]

expected = {'messages' : messages,
            'groups' : []}
