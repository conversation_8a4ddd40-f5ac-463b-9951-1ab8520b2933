SOF


ANNOTATE_SEUT_GROUP("VERSIONS_PROTOCOL_CLUSTERD")
#define CLUSTERD_PROTOCOL_VERSION_CROSS_VERSION         20
#define CLUSTERD_PROTOCOL_VERSION_MGM                   9
// bla-bla
#define CLUSTERD_PROTOCOL_VERSION_DOWNGRADE_SUPPORT     4
ANNOTATE_SEUT_END

ANNOTATE_SEUT_GROUP("VERSIONS_PROTOCOL_CONTROLD")
#define DM_PROTOCOL_VERSION_LEGACY           0x2
#define DM_PROTOCOL_VERSION_CROSS_VERSION 0x123
ANNOTATE_SEUT_PROTOCOL_CURRENT
#define DM_PROTOCOL_VERSION DM_PROTOCOL_VERSION_CROSS_VERSION
ANNOTATE_SEUT_END

EOF
