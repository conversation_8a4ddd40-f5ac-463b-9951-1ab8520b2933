id_refs = ['CLUSTERD_PROTOCOL_VERSION_CURRENT', 'XXX'  ]
md5sum = '2569b26d6d8bc29c0fc41b9be84df746'
direction = 'IN'

messages = [
    {'identifier_name' : 'clusterd_msg_handshake_resp_req_v20_t',
     'versioned' : True,
     'direction' : direction,
     'id_refs' : id_refs,
     'md5sum' : md5sum
     },
    {'identifier_name' : 'clusterd_msg_handshake_resp_req_t',
     'versioned' : False,
     'direction' : direction,
     'id_refs' : id_refs,
     'md5sum' : md5sum
     },
]

expected = {'messages' : messages,
            'groups' : []}
