include "/tmp/tsig.key";

options {
        masterfile-format text;
        zone-statistics yes;
        directory "/tmp";
        version none;
        recursion no;
        max-recursion-depth 7;
        max-recursion-queries 150;
        infoblox-dns-update-quota 1024;
        infoblox-dns-update-forwarding-quota 1024;
        hostname none;
        listen-on { 127.0.0.1; x.x.x.x; *******; *******; *******; };
        query-source address x.x.x.x port *;
        notify-source x.x.x.x port *;
        transfer-source x.x.x.x;
        use-alt-transfer-source no;
        listen-on-v6 { ::1; 2002::12; 2:2:2:2:2:2:2:2; 2:2:2:2:2:2:2:3; 2:2:2:2:2:2:2:4; };
        query-source-v6 address 2002::12 port *;
        notify-source-v6 2002::12 port *;
        transfer-source-v6 2002::12;
        minimal-responses yes;
        max-cache-size 1430677504;
        lame-ttl 600;
        tcp-clients 1000;
        transfers-in 10;
        transfers-out 10;
        transfers-per-ns 2;
        serial-query-rate 20;
        max-cache-ttl 604800;
        max-ncache-ttl 10800;
        edns-udp-size 1220;
        max-udp-size 1220;
        # for service restart: allow_bulkhost_ddns = Refusal
        allow-transfer { !any; };
        avoid-v4-udp-ports { 2114; 2113; 2115; 3000; 8000; 8089; 9997; 2222; 4040; 5575; 7077; 7911; 7912; 8000; 8089; 9090; 9091; 9997; 8070; 8787; 9999; 9004; 2022; 3374; 3115; 1194; 8080; 9000; 9183; 9185; };
        avoid-v6-udp-ports { 2114; 2113; 2115; 3000; 8000; 8089; 9997; 2222; 4040; 5575; 7077; 7911; 7912; 8000; 8089; 9090; 9091; 9997; 8070; 8787; 9999; 9004; 2022; 3374; 3115; 1194; 8080; 9000; 9183; 9185; };
        transfer-format many-answers;
        max-journal-size 100000K;
};

# Worker threads: default

# Bulk Host Name Templates:
#       Four Octets:            "-$1-$2-$3-$4" (Default)
#       One Octet:              "-$4"
#       Three Octets:           "-$2-$3-$4"
#       Two Octets:             "-$3-$4"

# multi-master master selection: 30-5-120-20

include "/tmp/dhcp_updater.key";

include "/tmp/rndc.key";

controls {
        inet 127.0.0.1 port 953
        allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	 channel ib_syslog { 
		 syslog daemon; 
		 severity info; 
	};
	 category default { ib_syslog; };
};
