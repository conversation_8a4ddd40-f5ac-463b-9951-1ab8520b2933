<DATABASE NAME="onedb" VERSION="MDXMLTEST">

  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.imc_accounting_avp"/>
      <PROPERTY NAME="name" VALUE="APN"/>
      <PROPERTY NAME="user_defined" VALUE="false"/>
      <PROPERTY NAME="value_type" VALUE="STRING"/>
      <PROPERTY NAME="domains" VALUE="SUBS_ID ALT_SUBS_ID ANCILLARY NAS_CONTEXT"/>
  </OBJECT>

  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.imc_accounting_avp"/>
      <PROPERTY NAME="name" VALUE="NO-VALUE"/>
      <PROPERTY NAME="user_defined" VALUE="false"/>
      <PROPERTY NAME="value_type" VALUE="STRING"/>
      <PROPERTY NAME="domains" VALUE=""/>
  </OBJECT>

  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.imc_accounting_avp"/>
      <PROPERTY NAME="name" VALUE="ONLY-IP_SPACE_DIS"/>
      <PROPERTY NAME="user_defined" VALUE="false"/>
      <PROPERTY NAME="value_type" VALUE="STRING"/>
      <PROPERTY NAME="domains" VALUE=""/>
  </OBJECT>

  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.imc_accounting_avp"/>
      <PROPERTY NAME="name" VALUE="Proxy-All"/>
      <PROPERTY NAME="user_defined" VALUE="false"/>
      <PROPERTY NAME="value_type" VALUE="BYTE"/>
      <PROPERTY NAME="domains" VALUE="ANCILLARY"/>
  </OBJECT>

  <OBJECT>
      <PROPERTY NAME="__type" VALUE=".com.infoblox.one.imc_accounting_avp"/>
      <PROPERTY NAME="name" VALUE="Deterministic-NAT-Port"/>
      <PROPERTY NAME="user_defined" VALUE="false"/>
      <PROPERTY NAME="value_type" VALUE="INTEGER"/>
      <PROPERTY NAME="domains" VALUE="ANCILLARY IP_SPACE_DIS"/>
  </OBJECT>

</DATABASE>