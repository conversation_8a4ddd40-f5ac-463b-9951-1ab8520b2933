ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates on;
# DNS update retry interval: 5
deny client-updates;
ddns-domainname =  config-option domain-name;
ddns-hostname = pick ( option fqdn.hostname, option host-name );
option host-name = config-option server.ddns-hostname;
update-static-leases false;

include "/storage/tmp/dhcp_updater.key";

subnet ******** netmask *********** {
	deny client-updates;
	ddns-domainname =  config-option domain-name;
	ddns-hostname = pick ( option fqdn.hostname, option host-name );
	option host-name = config-option server.ddns-hostname;
}

subnet ******** netmask *********** {
	allow client-updates;
	ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
	ddns-hostname = pick ( option fqdn.hostname, option host-name );
	option host-name = config-option server.ddns-hostname;
}

subnet *********** netmask ************* {
	not authoritative;
}

#End of dhcpd.conf file
