"header-memberdhcp","name*","domain_name","broadcast_address","routers","domain_name_servers","ignore_client_requested_options","lease_time","enable_pxe_lease_time","pxe_lease_time","boot_file","boot_server","next_server","deny_bootp","enable_thresholds","range_high_water_mark","range_high_water_mark_reset","range_low_water_mark","range_low_water_mark_reset","enable_threshold_email_warnings","enable_threshold_snmp_warnings","threshold_email_addresses","enable_ddns","enable_option81","always_update_dns","generate_hostname","update_static_leases","ddns_ttl","update_dns_on_lease_renewal","preferred_lifetime","valid_lifetime","is_authoritative","recycle_leases","ping_count","ping_timeout","enable_leasequery","retry_ddns_updates","ddns_retry_interval","lease_scavenge_time","enable_fingerprint","ipv6_enable_ddns","ipv6_ddns_enable_option_fqdn","ipv6_generate_hostname","ipv6_ddns_domainname","ipv6_ddns_ttl","ipv6_domain_name_servers","ipv6_domain_name","ipv6_recycle_leases","ipv6_server_duid","ipv6_enable_retry_updates","ipv6_retry_updates_interval","ipv6_update_dns_on_lease_renewal","ddns_domainname","lease_per_client_settings","option60_match_rules","v6_leases_scavenging_enabled","v6_leases_scavenging_grace_period","v6_remember_expired_client_association"
"memberdhcp","infoblox.localdomain","infoblox.com","********",,"********,********","FALSE",,"FALSE",,"bootfile1","abc.domain.com","xyz.domain.com","TRUE","FALSE",,,,,"TRUE","TRUE","<EMAIL>,<EMAIL>","TRUE","TRUE","FALSE","TRUE","TRUE",0,"TRUE",27000,43200,"TRUE","TRUE",1,1000,"TRUE","TRUE",5,,"TRUE","TRUE","TRUE","TRUE","test_domain.com",0,"2001::1,2001::2","10.0.0.0","TRUE",,"TRUE",5,"TRUE","test_domain2.com","ONE_LEASE_PER_CLIENT","DHCP/abc/True/2/4","FALSE","21600","TRUE"
