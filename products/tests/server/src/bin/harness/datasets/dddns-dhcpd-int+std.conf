#ignore unknown-clients;
omapi-key DHCP_UPDATER;
omapi-port 7911;
ignore bootp;
ddns-updates DDNS-UPDATES;
ddns-update-style DDNS_UPDATE_STYLE;
update-static-leases true;
update-optimization UPDATE-OPTIMIZATION;
option domain-name "test.infoblox.com";
option domain-name-servers 127.0.0.1;
#always-broadcast true;
local-address 127.0.0.1;
server-identifier 127.0.0.1;
authoritative;
max-lease-time LEASE-TIME;
default-lease-time LEASE-TIME;
ping-check false;

# Obtain the key file from named
include "/infoblox/var/named_conf/dhcp_updater.key";

# Allocate leases from the loopback network
subnet ********* netmask ********* {
	pool {
		infoblox-range ********* *************;
	range ********* *************;
	}

	# Fixed-address hosts
	host fixed-host-1 {
		dynamic;
		hardware ethernet 00:00:00:00:00:01;
		fixed-address *********;
	}
	host fixed-host-4 {
		dynamic;
		hardware ethernet 00:00:00:00:00:04;
		fixed-address *********;
	}
	host fixed-host-7 {
		dynamic;
		hardware ethernet 00:00:00:00:00:07;
		fixed-address *********;
	}
	host fixed-host-9 {
		dynamic;
		hardware ethernet 00:00:00:00:00:09;
		fixed-address *********;
		option netbios-name-servers *******,*******;
	}
	host fixed-host-23 {
		dynamic;
		hardware ethernet 00:00:00:00:00:23;
		fixed-address **********;
		option netbios-name-servers *******,*******;
	}
	host fixed-host-42 {
		dynamic;
		hardware ethernet 00:00:00:00:00:42;
		fixed-address *********2;
	}
        host fixed-host-43 {
		dynamic;
		fixed-address *********3;
                option dhcp-client-identifier "foo";
	}
	host fixed-host-77 {
		dynamic;
		hardware ethernet 77:77:77:77:77:77;
		fixed-address ***********;
		option host-name "可.test.infoblox.com";
	}
}

# Where to send the DNS updates
zone "test.infoblox.com." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}

zone "127.in-addr.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
