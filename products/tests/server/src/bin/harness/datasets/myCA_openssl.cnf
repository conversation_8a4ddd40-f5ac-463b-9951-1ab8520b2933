# myCA_openssl.cnf
###############################################

extensions = common_x509_extensions

###############################################

[ ca ]
default_ca = intermediate_ca

###############################################

[ root_ca ]

dir = /storage/tmp/myCA/root_ca
database = $dir/index.txt
serial = $dir/serial.txt
certs = $dir/certs
new_certs_dir = $dir/newcerts
crl_dir = $dir/crl
crl = $dir/crl.pem
crlnumber = $dir/crlnumber
certificate = $dir/certs/root_ca_cert.pem
private_key = $dir/private/root_ca_key.pem
RANDFILE = $dir/private/.rand
default_days = 365
default_crl_days = 10
default_md = sha1
preserve = no
policy = common_CA_policy
x509_extensions = common_x509_extensions

###############################################

[ intermediate_ca ]

dir = /storage/tmp/myCA/intermediate_ca
database = $dir/index.txt
serial = $dir/serial.txt
certs = $dir/certs
new_certs_dir = $dir/newcerts
crl_dir = $dir/crl
crl = $dir/crl.pem
crlnumber = $dir/crlnumber
certificate = $dir/certs/intermediate_ca_cert.pem
private_key = $dir/private/intermediate_ca_key.pem
RANDFILE = $dir/private/.rand
default_days = 365
default_crl_days = 10
default_md = sha1
preserve = no
policy = common_CA_policy
x509_extensions = common_x509_extensions

###############################################

[ fake_ca ]

dir = /storage/tmp/myCA/fake_ca
database = $dir/index.txt
serial = $dir/serial.txt
certs = $dir/certs
new_certs_dir = $dir/newcerts
crl_dir = $dir/crl
crl = $dir/crl.pem
crlnumber = $dir/crlnumber
certificate = $dir/certs/fake_ca_cert.pem
private_key = $dir/private/fake_ca_key.pem
RANDFILE = $dir/private/.rand
default_days = 365
default_crl_days = 10
default_md = sha1
preserve = no
policy = common_CA_policy
x509_extensions = common_x509_extensions

###############################################

[ req ]

default_bits = 2048
default_md = sha1
req_extensions = common_v3_req_extensions
distinguished_name = common_req_distinguished_name

###############################################

[ common_CA_policy ]
countryName             = optional
stateOrProvinceName     = optional
localityName            = optional
organizationName        = optional
organizationalUnitName  = optional
commonName              = supplied
emailAddress            = optional
UID                     = optional

###############################################

[ common_CA_extensions ]

basicConstraints = CA:TRUE
subjectKeyIdentifier = hash

###############################################

[ common_x509_extensions ]

basicConstraints = CA:FALSE
subjectKeyIdentifier = hash

###############################################

[ OCSPSigning_x509_extensions ]

basicConstraints = CA:FALSE
subjectKeyIdentifier = hash
extendedKeyUsage=OCSPSigning

###############################################

[ astroboy_good_SAN_dirName ]
C=CA
O=Infoblox
OU=Engineering
CN=astroboy_good_dirname

###############################################

[ astroboy_good_SAN_x509_extensions ]

basicConstraints = CA:FALSE
subjectKeyIdentifier = hash
subjectAltName = email:<EMAIL>,dirName:astroboy_good_SAN_dirName,otherName:*******.4.1.311.20.2.3;UTF8:astroboy_good_upn

###############################################

[ bender_revoked_SAN_dirName ]
C=CA
O=Infoblox
OU=Engineering
CN=bender_revoked_dirname

###############################################

[ bender_revoked_SAN_x509_extensions ]

basicConstraints = CA:FALSE
subjectKeyIdentifier = hash
subjectAltName = email:<EMAIL>,dirName:bender_revoked_SAN_dirName,otherName:*******.4.1.311.20.2.3;UTF8:bender_revoked_upn

###############################################

[ c3po_expired_SAN_dirName ]
C=CA
O=Infoblox
OU=Engineering
CN=c3po_expired_dirname

###############################################

[ c3po_expired_SAN_x509_extensions ]

basicConstraints = CA:FALSE
subjectKeyIdentifier = hash
subjectAltName = email:<EMAIL>,dirName:c3po_expired_SAN_dirName,otherName:*******.4.1.311.20.2.3;UTF8:c3po_expired_upn

###############################################

[ common_v3_req_extensions ]

basicConstraints = CA:FALSE
subjectKeyIdentifier = hash

###############################################

[ common_req_distinguished_name ]
countryName                     = Country Name (2 letter code)
countryName_default             = XX
countryName_min                 = 2
countryName_max                 = 2

stateOrProvinceName             = State or Province Name (full name)
stateOrProvinceName_default     = CA

localityName                    = Locality Name (eg, city)
localityName_default            = Santa Clara

0.organizationName              = Organization Name (eg, company)
0.organizationName_default      = Infoblox

organizationalUnitName          = Organizational Unit Name (eg, section)
organizationalUnitName_default  = EngDev

commonName                      = Common Name (eg, your name or your server\'s hostname)
commonName_max                  = 64

emailAddress                    = Email Address
emailAddress_max                = 64

###############################################
# END


