<MDXML>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.srg">
    <NEW-MEMBER MEMBER-NAME="ns_group" value=""/>
  </STRUCTURE-TRANSFORM>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.srg_zone_linking">
    <NEW-MEMBER MEMBER-NAME="delegated_name" value=""/>
    <NEW-MEMBER MEMBER-NAME="delegated_display_name" value=""/>
    <NEW-MEMBER MEMBER-NAME="delegated_ttl" value=""/>
  </STRUCTURE-TRANSFORM>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.ns_group"
        POST-STRUCT-CALLBACK="gsnsgrp_nsgroup_cache_objects">
    <NEW-MEMBER MEMBER-NAME="group_type" value="AUTH"/>
  </STRUCTURE-TRANSFORM>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.cluster_dns_properties"
        POST-STRUCT-CALLBACK="gsnsgrp_cluster_dnsprops_cache">
  </STRUCTURE-TRANSFORM>

  <POST-PROCESSING PROCESS-FUNCTION="gsnsgrp_zone_nsgroup_links"/>

</MDXML>
