# dhcpd conf file for ifmap_dhcpd.sh
local-address 127.0.0.1;
server-identifier 127.0.0.1;
ddns-update-style interim;
not authoritative;
default-lease-time 10;
min-lease-time 10;
max-lease-time 10;
ping-check false;
log-facility daemon;

ddns-updates off;
ignore client-updates;

# Allocate leases from the loopback network
subnet ********* netmask ********* {
        pool {
                infoblox-range ********* ***********;
                range ********* ***********;
        }
}

