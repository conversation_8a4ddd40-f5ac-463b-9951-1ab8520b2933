<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="hmac-sha1"/>
    <PROPERTY NAME="enabled" VALUE="false"/>
    <PROPERTY NAME="position" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="hmac-sha1-96"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="1"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="hmac-sha2-256"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="2"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="hmac-sha2-512"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="3"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="hmac-md5"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="4"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="hmac-md5-96"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="5"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="<EMAIL>"/>
    <PROPERTY NAME="enabled" VALUE="false"/>
    <PROPERTY NAME="position" VALUE="6"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="<EMAIL>"/>
    <PROPERTY NAME="enabled" VALUE="false"/>
    <PROPERTY NAME="position" VALUE="7"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="<EMAIL>"/>
    <PROPERTY NAME="enabled" VALUE="false"/>
    <PROPERTY NAME="position" VALUE="8"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="<EMAIL>"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="9"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="<EMAIL>"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="10"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="<EMAIL>"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="11"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="<EMAIL>"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="12"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="<EMAIL>"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="13"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="<EMAIL>"/>
    <PROPERTY NAME="enabled" VALUE="false"/>
    <PROPERTY NAME="position" VALUE="14"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ssh_macs"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="macs" VALUE="<EMAIL>"/>
    <PROPERTY NAME="enabled" VALUE="false"/>
    <PROPERTY NAME="position" VALUE="15"/>
  </OBJECT>
</DATABASE>
