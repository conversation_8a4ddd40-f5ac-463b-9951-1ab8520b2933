local-address ***********;
server-identifier ***********;
ddns-update-style interim;
not authoritative;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
log-facility daemon;
omapi-key DHCP_UPDATER;
omapi-port 7911;

failover peer "assoc1"
{
  primary;
  address ***********;
  port 519;

  peer address ************;
  peer port 519;

  max-response-delay 60;
  max-unacked-updates 10;
  mclt 3600;
  split 128;
 load balance max seconds 3;
}
ddns-updates off;
update-static-leases false;

include "/infoblox/var/dhcpd_conf/dhcp_updater.key";

subnet ******** netmask ********* {
}

subnet 10.0.0.0 netmask ********* {
	pool {
		infoblox-range ********* *********;
		failover peer "assoc1";
		deny dynamic bootp clients;
		range ********* *********;
	}
}

subnet *********** netmask ************* {
}

subnet ********** netmask *********** {
}

subnet *********** netmask ************* {
	pool {
		infoblox-range ************ ************;
		range ************ ************;
	}
}

#End of dhcpd.conf file
