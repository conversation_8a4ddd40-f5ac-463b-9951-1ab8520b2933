#
# Test dictionary file 1
#

VENDOR		Test1				65533	format=1,2

BEGIN-VENDOR  Test1

ATTRIBUTE Test1-Attribute1    1 integer
ATTRIBUTE Test1-Attribute2    2 string
ATTRIBUTE Test1-Attribute3    3 string  encrypt=1
ATTRIBUTE Test1-Attribute4    4 string  encrypt=1,has_tag
ATTRIBUTE Test2-Attribute5    5 string  Test2   # Test2's attribute
ATTRIBUTE Test1-Attribute6    6 string  has_tag

VALUE Test1-Attribute1  Attribute1-Value1   1
VALUE Test1-Attribute1  Attribute1-Value2   2

END-VENDOR  Test1
