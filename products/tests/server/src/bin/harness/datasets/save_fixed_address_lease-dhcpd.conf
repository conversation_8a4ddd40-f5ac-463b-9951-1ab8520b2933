# dhcpd conf file for save_fixed_address_lease.sh
local-address 127.0.0.1;
server-identifier 127.0.0.1;
ddns-update-style interim;
not authoritative;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
log-facility daemon;
ping-check false;

ddns-updates off;
ignore client-updates;

# Allocate leases from the loopback network
subnet ********* netmask ********* {
        pool {
                infoblox-range ********** **********;
                range ********** **********;
        }
        # Fixed-address hosts
        host fixed-host-1 {
                hardware ethernet 11:22:33:44:55:08;
                fixed-address *********;
        }
        host fixed-host-2 {
                hardware ethernet 11:22:33:44:55:16;
                fixed-address **********;
        }
}
