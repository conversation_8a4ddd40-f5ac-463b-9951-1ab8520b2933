<MDXML>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.lease"
        POST-STRUCT-CALLBACK="fp_upg_7_0_0_to_8_0_0">
    </STRUCTURE-TRANSFORM>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.lease_event"
        POST-STRUCT-CALLBACK="fp_upg_7_0_0_to_8_0_0">
    </STRUCTURE-TRANSFORM>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.dhcp_host"
        POST-STRUCT-CALLBACK="fp_upg_7_0_0_to_8_0_0">
    </STRUCTURE-TRANSFORM>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.cluster_mac_filterset_item"
        POST-STRUCT-CALLBACK="fp_upg_7_0_0_to_8_0_0">
    </STRUCTURE-TRANSFORM>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.smart_folder_query_item"
        POST-STRUCT-CALLBACK="smart_folder_query_value_fp_upg_7_0_0_to_8_0_0">
    </STRUCTURE-TRANSFORM>
</MDXML>
