<RTXML-RESPONSE>
	<FUNCTION name=".com.infoblox.tests.get_parent_container1_by_parent">
	<VECTOR __collection_name="children" __size="5" __start="0" __total_size="5">
		<OBJECT>
			<PROPERTY NAME="parent" VALUE="."/>
			<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.qe_parent_container1"/>
			<PROPERTY NAME="type" VALUE="Authoritative"/>
			<PROPERTY NAME="name" VALUE="parent-container1-0"/>
			<PROPERTY NAME="__key" VALUE=".parent-container1-0"/>
		</OBJECT>
		<OBJECT>
			<PROPERTY NAME="parent" VALUE="."/>
			<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.qe_parent_container1"/>
			<PROPERTY NAME="type" VALUE="Delegated"/>
			<PROPERTY NAME="name" VALUE="parent-container1-1"/>
			<PROPERTY NAME="__key" VALUE=".parent-container1-1"/>
		</OBJECT>
		<OBJECT>
			<PROPERTY NAME="parent" VALUE="."/>
			<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.qe_parent_container1"/>
			<PROPERTY NAME="type" VALUE="Forward"/>
			<PROPERTY NAME="name" VALUE="parent-container1-2"/>
			<PROPERTY NAME="__key" VALUE=".parent-container1-2"/>
		</OBJECT>
		<OBJECT>
			<PROPERTY NAME="parent" VALUE="."/>
			<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.qe_parent_container1"/>
			<PROPERTY NAME="type" VALUE="Stub"/>
			<PROPERTY NAME="name" VALUE="parent-container1-3"/>
			<PROPERTY NAME="__key" VALUE=".parent-container1-3"/>
		</OBJECT>
		<OBJECT>
			<PROPERTY NAME="parent" VALUE="."/>
			<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.qe_parent_container1"/>
			<PROPERTY NAME="type" VALUE="Dummy"/>
			<PROPERTY NAME="name" VALUE="parent-container1-4"/>
			<PROPERTY NAME="__key" VALUE=".parent-container1-4"/>
		</OBJECT>
	</VECTOR>
	</FUNCTION>
</RTXML-RESPONSE>