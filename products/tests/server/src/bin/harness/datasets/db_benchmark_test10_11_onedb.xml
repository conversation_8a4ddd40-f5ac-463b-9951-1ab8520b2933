<DATABASE NAME="onedb" VERSION="3.0.0-0-052604-EA-B1">
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.record_name_policy"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="policy_name" VALUE="Allow Underscore"/>
    <PROPERTY NAME="policy_regex" VALUE="^[-a-zA-Z0-9_]+$"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bulk_host_name_template"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="template_name" VALUE="Four Octets"/>
    <PROPERTY NAME="template_format" VALUE="-$1-$2-$3-$4"/>
  </OBJECT>

<OBJECT>
  <PROPERTY NAME="cluster_parent" VALUE="."/>
  <PROPERTY NAME="virtual_node_count" VALUE="0"/>
  <PROPERTY NAME="shared_secret" VALUE="test"/>
  <PROPERTY NAME="cluster_oid" VALUE="0"/>
  <PROPERTY NAME="physical_node_count" VALUE="0"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster"/>
  <PROPERTY NAME="name" VALUE="Infoblox"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="hint" VALUE="infoblox"/>
  <PROPERTY NAME="password" VALUE="infoblox"/>
  <PROPERTY NAME="tree_size" VALUE="100"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin"/>
  <PROPERTY NAME="name" VALUE="admin"/>
  <PROPERTY NAME="response" VALUE="infoblox"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.shared_network_parent"/>
  <PROPERTY NAME="name" VALUE="/"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="custom_root_server" VALUE="false"/>
  <PROPERTY NAME="refresh" VALUE="10800"/>
  <PROPERTY NAME="recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="default_ttl" VALUE="3600"/>
  <PROPERTY NAME="retry" VALUE="3600"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dns_properties"/>
  <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="negative_ttl" VALUE="3600"/>
  <PROPERTY NAME="expire" VALUE="604800"/>
 <PROPERTY NAME="record_name_policy" VALUE="Allow Underscore"/>
 <PROPERTY NAME="bulk_host_name_template" VALUE="Four Octets"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="limit_recursive_clients" VALUE="false"/>
  <PROPERTY NAME="recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="override_cluster_transfer_list" VALUE="false"/>
  <PROPERTY NAME="override_cluster_qacl" VALUE="false"/>
  <PROPERTY NAME="override_cluster_ddns_updaters" VALUE="false"/>
  <PROPERTY NAME="override_cluster_sortlist" VALUE="false"/>
  <PROPERTY NAME="numberof_recursive_clients" VALUE="1000"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_properties"/>
  <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
  <PROPERTY NAME="override_cluster_rqacl" VALUE="false"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="pxe_lease_time_enabled" VALUE="false"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dhcp_properties"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_ipam_properties"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="pxe_lease_time_enabled" VALUE="false"/>
  <PROPERTY NAME="override_lease_time" VALUE="false"/>
  <PROPERTY NAME="override_domain_name_servers" VALUE="false"/>
  <PROPERTY NAME="override_custom_options" VALUE="false"/>
  <PROPERTY NAME="override_domain_name" VALUE="false"/>
  <PROPERTY NAME="update_static_leases" VALUE="false"/>
  <PROPERTY NAME="override_local_dns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
  <PROPERTY NAME="override_boot_file" VALUE="false"/>
  <PROPERTY NAME="override_boot_server" VALUE="false"/>
  <PROPERTY NAME="override_next_server" VALUE="false"/>
  <PROPERTY NAME="override_authoritative" VALUE="false"/>
  <PROPERTY NAME="override_routers" VALUE="false"/>
  <PROPERTY NAME="remote_ddns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="override_update_static_leases" VALUE="false"/>
  <PROPERTY NAME="override_pxe_lease_time" VALUE="false"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="override_broadcast_address" VALUE="false"/>
  <PROPERTY NAME="local_ddns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="override_remote_dns_updates_enabled" VALUE="false"/>
</OBJECT>
<OBJECT><PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node_parent"/><PROPERTY NAME="cluster" VALUE="0"/></OBJECT>
<OBJECT>
  <PROPERTY NAME="ha_enabled" VALUE="0"/>
  <PROPERTY NAME="override_support_access" VALUE="false"/>
  <PROPERTY NAME="support_access_enabled" VALUE="false"/>
  <PROPERTY NAME="virtual_ip" VALUE="***********"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="subnet_mask" VALUE="*************"/>
  <PROPERTY NAME="is_master" VALUE="true"/>
  <PROPERTY NAME="host_name" VALUE="host.infoblox.com"/>
  <PROPERTY NAME="gateway" VALUE="***********"/>
  <PROPERTY NAME="virtual_oid" VALUE="0"/>
  <PROPERTY NAME="member_type" VALUE="INFOBLOX"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="ddns_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="name" VALUE="com.infoblox"/>
  <PROPERTY NAME="revzone_netmask" VALUE="***************"/>
  <PROPERTY NAME="member_primary" VALUE="0"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.host"/>
  <PROPERTY NAME="creator_member" VALUE=""/>
  <PROPERTY NAME="created_timestamp" VALUE=""/>
  <PROPERTY NAME="name" VALUE="host1"/>
  <PROPERTY NAME="record_type" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="comment" VALUE="Generated"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.host"/>
  <PROPERTY NAME="creator_member" VALUE=""/>
  <PROPERTY NAME="created_timestamp" VALUE=""/>
  <PROPERTY NAME="name" VALUE="test_host"/>
  <PROPERTY NAME="record_type" VALUE="0"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="address" VALUE="**********2"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.host_address"/>
  <PROPERTY NAME="host" VALUE="._default.com.infoblox.host1"/>
  <PROPERTY NAME="mac_address" VALUE=""/>
  <PROPERTY NAME="v6_prefix" VALUE=""/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="address" VALUE="**********"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.host_address"/>
  <PROPERTY NAME="host" VALUE="._default.com.infoblox.host1"/>
  <PROPERTY NAME="mac_address" VALUE=""/>
  <PROPERTY NAME="v6_prefix" VALUE=""/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="address" VALUE="***********"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.host_address"/>
  <PROPERTY NAME="host" VALUE="._default.com.infoblox.host1"/>
  <PROPERTY NAME="mac_address" VALUE=""/>
  <PROPERTY NAME="v6_prefix" VALUE=""/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.host_alias"/>
  <PROPERTY NAME="alias" VALUE="hello"/>
  <PROPERTY NAME="host" VALUE="._default.com.infoblox.host1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.host_alias"/>
  <PROPERTY NAME="alias" VALUE="hello2"/>
  <PROPERTY NAME="host" VALUE="._default.com.infoblox.host1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.host_alias"/>
  <PROPERTY NAME="alias" VALUE="hello3"/>
  <PROPERTY NAME="host" VALUE="._default.com.infoblox.host1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.host_alias"/>
  <PROPERTY NAME="alias" VALUE="alias2"/>
  <PROPERTY NAME="host" VALUE="._default.com.infoblox.host1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.host_alias"/>
  <PROPERTY NAME="alias" VALUE="alias1"/>
  <PROPERTY NAME="host" VALUE="._default.com.infoblox.host1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="address" VALUE="**********"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="name" VALUE="host1"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_mx"/>
  <PROPERTY NAME="name" VALUE="host1"/>
  <PROPERTY NAME="priority" VALUE="10"/>
  <PROPERTY NAME="mx" VALUE="mx1.infoblox.com"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_mx"/>
  <PROPERTY NAME="name" VALUE="mx2"/>
  <PROPERTY NAME="priority" VALUE="10"/>
  <PROPERTY NAME="mx" VALUE="blah.blah.blah"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_mx"/>
  <PROPERTY NAME="name" VALUE="host1"/>
  <PROPERTY NAME="priority" VALUE="10"/>
  <PROPERTY NAME="mx" VALUE="blah.blah.blah.blah"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_mx"/>
  <PROPERTY NAME="name" VALUE="host1"/>
  <PROPERTY NAME="priority" VALUE="10"/>
  <PROPERTY NAME="mx" VALUE="gkgkgk.g.g.g.f"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="port" VALUE="10"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="weight" VALUE="10"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_srv"/>
  <PROPERTY NAME="name" VALUE="host1"/>
  <PROPERTY NAME="priority" VALUE="10"/>
  <PROPERTY NAME="target" VALUE="blah.blah"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_txt"/>
  <PROPERTY NAME="name" VALUE="host1"/>
  <PROPERTY NAME="txt_string" VALUE="hello_world this is mars"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_txt"/>
  <PROPERTY NAME="name" VALUE="host1"/>
  <PROPERTY NAME="txt_string" VALUE="slkfaflkjfslkjdf"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="enable_auto_lan" VALUE="true"/>
  <PROPERTY NAME="physical_oid" VALUE="0"/>
  <PROPERTY NAME="hwid" VALUE="a6f7422bf0a22e27cb474a8a4a59f33"/>
  <PROPERTY NAME="node_status" VALUE="Running"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
  <PROPERTY NAME="public_ip_address" VALUE="***********"/>
  <PROPERTY NAME="mgmt_port_enabled" VALUE="false"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="enable_auto_ha" VALUE="true"/>
  <PROPERTY NAME="position" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="session_timeout" VALUE="60000"/>
  <PROPERTY NAME="support_access_enabled" VALUE="false"/>
  <PROPERTY NAME="security_enabled" VALUE="false"/>
  <PROPERTY NAME="enable_http_redirect" VALUE="false"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_security"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="notify_sys_admin" VALUE="false"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_monitor"/>
  <PROPERTY NAME="enable_snmp" VALUE="false"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="enable_syslog_server" VALUE="false"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_resolver"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="resolver_enabled" VALUE="false"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="time_zone" VALUE="(UTC) London"/>
  <PROPERTY NAME="ntp_enabled" VALUE="false"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_time"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="dns_enabled" VALUE="true"/>
  <PROPERTY NAME="publish_changes" VALUE="false"/>
  <PROPERTY NAME="dhcp_enabled" VALUE="false"/>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.system_state"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
</OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.view"/>
    <PROPERTY NAME="zone" VALUE="._default"/>
    <PROPERTY NAME="displayname" VALUE="default"/>
    <PROPERTY NAME="recursion_enabled" VALUE="false"/>
    <PROPERTY NAME="network_view" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
    <PROPERTY NAME="zone" VALUE=".."/>
    <PROPERTY NAME="name" VALUE="_default"/>
    <PROPERTY NAME="is_external_primary" VALUE="false"/>
    <PROPERTY NAME="zone_type" VALUE="View"/>
    <PROPERTY NAME="disabled" VALUE="false"/>
    <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  </OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
  <PROPERTY NAME="parent" VALUE="/"/>
  <PROPERTY NAME="name" VALUE="default"/>
  <PROPERTY NAME="id" VALUE="0"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view_parent"/>
  <PROPERTY NAME="name" VALUE="/"/>
</OBJECT>


</DATABASE>
