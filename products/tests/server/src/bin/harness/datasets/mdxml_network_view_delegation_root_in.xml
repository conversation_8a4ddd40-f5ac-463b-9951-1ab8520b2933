<DATABASE NAME="onedb" VERSION="MDXMLTEST">

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.vconnector_cluster"/>
<PROPERTY NAME="feature_init" VALUE="2"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
<PROPERTY NAME="name" VALUE="default"/>
<PROPERTY NAME="id" VALUE="0"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
<PROPERTY NAME="name" VALUE="netview1"/>
<PROPERTY NAME="id" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
<PROPERTY NAME="name" VALUE="netview2"/>
<PROPERTY NAME="id" VALUE="2"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
<PROPERTY NAME="name" VALUE="netview3"/>
<PROPERTY NAME="id" VALUE="3"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
<PROPERTY NAME="name" VALUE="netview4"/>
<PROPERTY NAME="id" VALUE="4"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.vconnector_directory"/>
<PROPERTY NAME="type" VALUE="NETWORK_VIEW"/>
<PROPERTY NAME="vco_member" VALUE="1"/>
<PROPERTY NAME="vco_delegated_entity" VALUE=".com.infoblox.dns.network_view$1"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="network_view" VALUE="1"/>
<PROPERTY NAME="address" VALUE=""/>
<PROPERTY NAME="end_address" VALUE=""/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.vconnector_directory"/>
<PROPERTY NAME="type" VALUE="NETWORK_VIEW"/>
<PROPERTY NAME="vco_member" VALUE="1"/>
<PROPERTY NAME="vco_delegated_entity" VALUE=".com.infoblox.dns.network_view$3"/>
<PROPERTY NAME="flags" VALUE="RA"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="network_view" VALUE="3"/>
<PROPERTY NAME="address" VALUE=""/>
<PROPERTY NAME="end_address" VALUE=""/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.vconnector_directory"/>
<PROPERTY NAME="type" VALUE="NETWORK_VIEW"/>
<PROPERTY NAME="vco_member" VALUE="2"/>
<PROPERTY NAME="vco_delegated_entity" VALUE=".com.infoblox.dns.network_view$4"/>
<PROPERTY NAME="flags" VALUE="R"/>
<PROPERTY NAME="is_ipv4" VALUE="true"/>
<PROPERTY NAME="network_view" VALUE="4"/>
<PROPERTY NAME="address" VALUE=""/>
<PROPERTY NAME="end_address" VALUE=""/>
</OBJECT>

</DATABASE>