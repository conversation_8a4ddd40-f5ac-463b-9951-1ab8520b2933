
# Automatically generated config file, do not edit!
# HA status (Required for failover): Active
tinker panic 0
tinker step 0
server -6 2a01::56  iburst minpoll 6 maxpoll 8
server 127.127.1.1
fudge 127.127.1.1 stratum 12
#tos orphan 13
restrict default ignore
restrict 127.0.0.1
restrict ::1
restrict 169.254.0.0 mask 255.255.0.0
restrict 2a01::56 noquery
restrict 2101::34
restrict 2b02::45 mask 8000::
restrict 2b03::45 mask c000::
restrict 2b06::45 mask f800::
restrict 2b07::45 mask ff00::
restrict 2b09::45 mask ff80::
restrict 2b10::45 mask fff0::
restrict 2b12::45 mask ffff:ff00::
restrict 2b13::45 mask ffff:ffff:ffff:ffff:ffff:f800::
restrict 2b14::45 mask ffff:ffff:ffff:ffff::
restrict 2b02::45 mask ffff:ffff:ffff:ffff:8000::
keys /storage/etc/ntp.keys

driftfile /storage/etc/ntp.drift

# Listern on all interfaces
nic listen all
# Ignore link-local addresses
nic ignore fe80::/64

# Enable writing of statistics records.
statistics cryptostats loopstats peerstats
statsdir /infoblox/var/ntp/
filegen peerstats file peers type day link enable
filegen loopstats file loops type day link enable
filegen cryptostats file cryptos type day link enable
