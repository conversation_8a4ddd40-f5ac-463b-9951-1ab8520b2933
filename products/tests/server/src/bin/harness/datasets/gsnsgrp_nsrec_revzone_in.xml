<DATABASE NAME="onedb" VERSION="7.0.0-pk-2015-05-06-14-30" MD5="f8fdd25fde89bbcc8821ed10e2f73ef1" SCHEMA-MD5="c00d84773a5ac1c2118503082ca18f46" INT-VERSION="7.0.6005-999999">
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dns_properties"/>
        <PROPERTY NAME="custom_root_server" VALUE="false"/>
        <PROPERTY NAME="default_ttl" VALUE="28800"/>
        <PROPERTY NAME="expire" VALUE="2419200"/>
        <PROPERTY NAME="negative_ttl" VALUE="900"/>
        <PROPERTY NAME="recursion_enabled" VALUE="false"/>
        <PROPERTY NAME="lame_ttl" VALUE="600"/>
        <PROPERTY NAME="refresh" VALUE="10800"/>
        <PROPERTY NAME="retry" VALUE="3600"/>
        <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_bulkhost_ddns" VALUE="0"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="facility" VALUE="daemon"/>
        <PROPERTY NAME="log_general" VALUE="true"/>
        <PROPERTY NAME="log_client" VALUE="true"/>
        <PROPERTY NAME="log_config" VALUE="true"/>
        <PROPERTY NAME="log_database" VALUE="true"/>
        <PROPERTY NAME="log_dnssec" VALUE="true"/>
        <PROPERTY NAME="log_lame_servers" VALUE="true"/>
        <PROPERTY NAME="log_network" VALUE="true"/>
        <PROPERTY NAME="log_notify" VALUE="true"/>
        <PROPERTY NAME="log_queries" VALUE="false"/>
        <PROPERTY NAME="log_resolver" VALUE="true"/>
        <PROPERTY NAME="log_security" VALUE="true"/>
        <PROPERTY NAME="log_update" VALUE="true"/>
        <PROPERTY NAME="log_xfer_in" VALUE="true"/>
        <PROPERTY NAME="log_xfer_out" VALUE="true"/>
        <PROPERTY NAME="log_update_security" VALUE="true"/>
        <PROPERTY NAME="log_rpz" VALUE="false"/>
        <PROPERTY NAME="log_responses" VALUE="false"/>
        <PROPERTY NAME="log_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="double_confirm_zone_deletion" VALUE="true"/>
        <PROPERTY NAME="enable_secondary_notify" VALUE="true"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="views_count" VALUE="0"/>
        <PROPERTY NAME="srgs_count" VALUE="0"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
        <PROPERTY NAME="lower_case_ptr_dname" VALUE="true"/>
        <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
        <PROPERTY NAME="transfers_out" VALUE="10"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="nxdomain_log_query" VALUE="false"/>
        <PROPERTY NAME="blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="blacklist_action" VALUE="REDIRECT"/>
        <PROPERTY NAME="blacklist_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="blacklist_log_query" VALUE="false"/>
        <PROPERTY NAME="enable_dns64" VALUE="false"/>
        <PROPERTY NAME="host_rrset_order" VALUE="false"/>
        <PROPERTY NAME="preserve_host_rrset_order_on_secondaries" VALUE="false"/>
        <PROPERTY NAME="enable_hsm_signing" VALUE="false"/>
        <PROPERTY NAME="dns_cache_ttl" VALUE="1"/>
        <PROPERTY NAME="filter_aaaa" VALUE="NO"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="transfers_in" VALUE="10"/>
        <PROPERTY NAME="transfers_per_ns" VALUE="2"/>
        <PROPERTY NAME="serial_query_rate" VALUE="20"/>
        <PROPERTY NAME="max_cached_lifetime" VALUE="86400"/>
        <PROPERTY NAME="max_cache_ttl" VALUE="604800"/>
        <PROPERTY NAME="max_ncache_ttl" VALUE="10800"/>
        <PROPERTY NAME="enable_ms_sticky_ip" VALUE="false"/>
        <PROPERTY NAME="disable_edns" VALUE="false"/>
        <PROPERTY NAME="query_rewrite_enabled" VALUE="false"/>
        <PROPERTY NAME="zrq_high_water_mark" VALUE="10"/>
        <PROPERTY NAME="latency_poll_interval" VALUE="30"/>
        <PROPERTY NAME="latency_response_timeout" VALUE="5"/>
        <PROPERTY NAME="master_sticky_time" VALUE="120"/>
        <PROPERTY NAME="master_sticky_delta" VALUE="20"/>
        <PROPERTY NAME="bind_tombstone_low_water_mark" VALUE="100"/>
        <PROPERTY NAME="bind_tombstone_high_water_mark" VALUE="10"/>
        <PROPERTY NAME="bind_tombstone_high_water_mark_additional_cleanup" VALUE="7"/>
        <PROPERTY NAME="bind_max_xfr_clients_tracked" VALUE="10000"/>
        <PROPERTY NAME="dnssec_blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nxdomain_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_rpz_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="log_idns_gslb" VALUE="false"/>
        <PROPERTY NAME="log_idns_health" VALUE="false"/>
        <PROPERTY NAME="cluster" VALUE="0"/>
        <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="record_name_policy" VALUE="Allow Underscore"/>
        <PROPERTY NAME="bulk_host_name_template" VALUE="Four Octets"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
        <PROPERTY NAME="uuid" VALUE="fbdd3e42a32a49f998596ab49e656c01"/>
	<PROPERTY NAME="revision_id" VALUE="12"/>
        <PROPERTY NAME="member_type" VALUE="INFOBLOX"/>
        <PROPERTY NAME="virtual_ip" VALUE="***********"/>
        <PROPERTY NAME="subnet_mask" VALUE="*************"/>
        <PROPERTY NAME="gateway" VALUE="**********"/>
        <PROPERTY NAME="is_master" VALUE="true"/>
        <PROPERTY NAME="ha_enabled" VALUE="0"/>
        <PROPERTY NAME="lan2_enabled" VALUE="false"/>
        <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
        <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
        <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
        <PROPERTY NAME="override_support_access" VALUE="false"/>
        <PROPERTY NAME="support_access_enabled" VALUE="false"/>
        <PROPERTY NAME="is_potential_master" VALUE="true"/>
        <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
        <PROPERTY NAME="nat_enabled" VALUE="false"/>
        <PROPERTY NAME="upgrade_position" VALUE="0"/>
        <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
        <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
        <PROPERTY NAME="override_member_redirect" VALUE="false"/>
        <PROPERTY NAME="enable_member_redirect" VALUE="false"/>
        <PROPERTY NAME="revert_window_start" VALUE="0"/>
        <PROPERTY NAME="revert_window_end" VALUE="0"/>
        <PROPERTY NAME="default_route" VALUE="LAN1"/>
        <PROPERTY NAME="use_dscp" VALUE="false"/>
        <PROPERTY NAME="dscp" VALUE="0"/>
        <PROPERTY NAME="use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="is_pre_provisioned" VALUE="false"/>
        <PROPERTY NAME="passive_ha_arp_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_router_discovery_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="v6_lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="use_v4_vrrp" VALUE="true"/>
        <PROPERTY NAME="config_addr_type" VALUE="IPV4"/>
        <PROPERTY NAME="service_type_configuration" VALUE="ALL_V4"/>
        <PROPERTY NAME="is_vconnector" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="0"/>
        <PROPERTY NAME="virtual_oid" VALUE="0"/>
        <PROPERTY NAME="host_name" VALUE="infoblox.localdomain"/>
        <PROPERTY NAME="active_position" VALUE="0"/>
        <PROPERTY NAME="upgrade_group" VALUE="Grid Master"/>
        <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
        <PROPERTY NAME="use_lom_users" VALUE="false"/>
        <PROPERTY NAME="lom_enabled" VALUE="true"/>
        <PROPERTY NAME="use_lom_enabled" VALUE="false"/>
        <PROPERTY NAME="_update_id" VALUE="0:0:f60e025d:0:800030e9"/>
        <PROPERTY NAME="proxy_auth_key" VALUE="{0}_{aes}_MAAAAOUeBq2J+Hqq8EJi5RunI1zkEdVq/kDiiGFiRrrBzNOKkZ+c6i/6DuoGT7HM7MpSjA=="/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="1"/>
        <PROPERTY NAME="primary_type" VALUE="Grid"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default"/>
        <PROPERTY NAME="name" VALUE="arpa.in-addr.10.6"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="revzone_address" VALUE="********"/>
        <PROPERTY NAME="revzone_netmask" VALUE="***********"/>
        <PROPERTY NAME="assigned_ns_group" VALUE="Grid-Prim-Nsg"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="6.10.in-addr.arpa"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="arpa.in-addr.10.6"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="2"/>
        <PROPERTY NAME="primary_type" VALUE="Grid"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default"/>
        <PROPERTY NAME="name" VALUE="arpa.ip6.d.e.a.d"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="revzone_address" VALUE="dead::"/>
        <PROPERTY NAME="revzone_netmask" VALUE="16"/>
        <PROPERTY NAME="assigned_ns_group" VALUE="Grid-Prim-Nsg"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="d.a.e.d.ip6.arpa"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="arpa.ip6.d.e.a.d"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ns_group"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="cluster_dns_properties" VALUE="0"/>
        <PROPERTY NAME="group_name" VALUE="Grid-Prim-Nsg"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ns_group_ext_secondary_server"/>
        <PROPERTY NAME="tsig_option" VALUE="false"/>
        <PROPERTY NAME="x_tsig_option" VALUE="false"/>
        <PROPERTY NAME="ext_sec_stealth" VALUE="false"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="ns_group" VALUE="Grid-Prim-Nsg"/>
        <PROPERTY NAME="ext_sec_address" VALUE="dead::beef"/>
        <PROPERTY NAME="ext_sec_name" VALUE="ns1.outer.space"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ns_group_grid_primary"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="ns_group" VALUE="Grid-Prim-Nsg"/>
        <PROPERTY NAME="grid_member" VALUE="0"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.10.6"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="ns1.outer.space"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.space.outer.ns1"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.arpa.ip6.d.e.a.d"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="ns1.outer.space"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.space.outer.ns1"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.10.6"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="infoblox.localdomain"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.localdomain.infoblox"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.arpa.ip6.d.e.a.d"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="infoblox.localdomain"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.localdomain.infoblox"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
    </OBJECT>
</DATABASE>
