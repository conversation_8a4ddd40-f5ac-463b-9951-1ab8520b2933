! -*- ospf -*-
!
hostname Infoblox-x.x.x.x

password infoblox
enable password infoblox

! loglevel: emergencies, alerts, critical, errors, warnings,
!           notifications, informational, or debugging
!           (default is informational.)
log syslog critical

! Create an access list that allows access from localhost and nowhere else
access-list access permit 127.0.0.1/32
access-list access deny any

ipv6 access-list access permit ::1/128
ipv6 access-list access deny any

! Enable access control on the command-line interface
line vty
    access-class access4
    ipv6 access-class access6


! Enable SNMP traps
agentx

interface lo
  ipv6 ospf6 priority 0
  ipv6 ospf6 hello-interval 10
  ipv6 ospf6 dead-interval 40
  ipv6 ospf6 retransmit-interval 5
  ipv6 ospf6 transmit-delay 1

interface eth1
  ipv6 ospf6 priority 0
  ipv6 ospf6 hello-interval 10
  ipv6 ospf6 dead-interval 40
  ipv6 ospf6 retransmit-interval 5
  ipv6 ospf6 transmit-delay 1

router ospf6
  router-id x.x.x.x
  interface eth1 area *******
  interface lo area *******

