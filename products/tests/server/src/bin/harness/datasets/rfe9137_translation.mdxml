<MDXML>
    <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.member_dns_properties"
        POST-STRUCT-CALLBACK="save_member_dns_properties_for_post_proc"/>

   <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.physical_node"
        POST-STRUCT-CALLBACK="save_physical_node_objects_for_post_proc">
        <NEW-MEMBER MEMBER-NAME="csp_access_key" VALUE=""/>
        <NEW-MEMBER MEMBER-NAME="dfp_running" VALUE="false"/>
    </STRUCTURE-TRANSFORM>

   <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.virtual_node"
        POST-STRUCT-CALLBACK="save_virtual_node_objects_for_post_proc">
        <NEW-MEMBER MEMBER-NAME="use_csp_join_token" VALUE="false"/>
        <NEW-MEMBER MEMBER-NAME="csp_join_token" VALUE="" />
        <NEW-MEMBER MEMBER-NAME="use_csp_dns_resolver" VALUE="false"/>
        <NEW-MEMBER MEMBER-NAME="csp_dns_resolver" VALUE=""/>
        <NEW-MEMBER MEMBER-NAME="use_csp_https_proxy" VALUE="false"/>
        <NEW-MEMBER MEMBER-NAME="csp_https_proxy" VALUE=""/>
    </STRUCTURE-TRANSFORM>

    <POST-PROCESSING PROCESS-FUNCTION="member_dns_properties_post_processing"/>

    <NEW-TYPES>
       <TYPE name=".com.infoblox.one.member_dfp_properties" />
    </NEW-TYPES>
</MDXML>
