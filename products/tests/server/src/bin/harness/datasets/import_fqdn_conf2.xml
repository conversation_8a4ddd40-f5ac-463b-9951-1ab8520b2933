<!-- Copyright (c) 2015 Infoblox Inc. All Rights Reserved. -->
<DATABASE NAME="onedb" VERSION="5.x-asehgal-2010-05-24-10-09" MD5="ef5089054c00609637c8ac1d6dd2d010" SCHEMA-MD5="1d242c1b992e44922d4b6b532c167e4b">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="true"/>
  <PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.127.0.0"/>
  <PROPERTY NAME="name" VALUE=""/>
  <PROPERTY NAME="dname" VALUE="cluster"/>
  <PROPERTY NAME="reversed_dname" VALUE="._default.cluster"/>
  <PROPERTY NAME="__key" VALUE="._default.arpa.in-addr.127.0.0..cluster"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ptr"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.127.0.0"/>
  <PROPERTY NAME="name" VALUE="1"/>
  <PROPERTY NAME="dname" VALUE="localhost"/>
  <PROPERTY NAME="__key" VALUE="._default.arpa.in-addr.127.0.0.1.localhost"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_soa"/>
  <PROPERTY NAME="default_ttl" VALUE="3600"/>
  <PROPERTY NAME="expire" VALUE="604800"/>
  <PROPERTY NAME="negative_ttl" VALUE="3600"/>
  <PROPERTY NAME="refresh" VALUE="10800"/>
  <PROPERTY NAME="retry" VALUE="3600"/>
  <PROPERTY NAME="serial_number" VALUE="1"/>
  <PROPERTY NAME="use_global_email" VALUE="true"/>
  <PROPERTY NAME="ttl" VALUE="3600"/>
  <PROPERTY NAME="ttl_option" VALUE="1"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="override_mname" VALUE="false"/>
  <PROPERTY NAME="override_serial_number" VALUE="false"/>
  <PROPERTY NAME="app_serial_number" VALUE="1"/>
  <PROPERTY NAME="signing_state" VALUE="unsigned"/>
  <PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.127.0.0"/>
  <PROPERTY NAME="mname" VALUE="cluster"/>
  <PROPERTY NAME="soa_email" VALUE="<EMAIL>"/>
  <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
  <PROPERTY NAME="__key" VALUE="._default.arpa.in-addr.127.0.0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bulk_host_name_template"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="template_name" VALUE="Four Octets"/>
  <PROPERTY NAME="template_format" VALUE="-$1-$2-$3-$4"/>
  <PROPERTY NAME="__key" VALUE="Four Octets"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bulk_host_name_template"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="template_name" VALUE="Three Octets"/>
  <PROPERTY NAME="template_format" VALUE="-$2-$3-$4"/>
  <PROPERTY NAME="__key" VALUE="Three Octets"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bulk_host_name_template"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="template_name" VALUE="Two Octets"/>
  <PROPERTY NAME="template_format" VALUE="-$3-$4"/>
  <PROPERTY NAME="__key" VALUE="Two Octets"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bulk_host_name_template"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="template_name" VALUE="One Octet"/>
  <PROPERTY NAME="template_format" VALUE="-$4"/>
  <PROPERTY NAME="__key" VALUE="One Octet"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dhcp_properties"/>
  <PROPERTY NAME="lease_time" VALUE="43200"/>
  <PROPERTY NAME="pxe_lease_time" VALUE="43200"/>
  <PROPERTY NAME="pxe_lease_time_enabled" VALUE="false"/>
  <PROPERTY NAME="facility" VALUE="daemon"/>
  <PROPERTY NAME="ddns_use_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_no_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_server_use_fqdn" VALUE="false"/>
  <PROPERTY NAME="ping_number" VALUE="1"/>
  <PROPERTY NAME="ping_timeout" VALUE="1"/>
  <PROPERTY NAME="double_confirm_network_deletion" VALUE="true"/>
  <PROPERTY NAME="recycle_leases" VALUE="true"/>
  <PROPERTY NAME="log_lease_events" VALUE="false"/>
  <PROPERTY NAME="enable_expert_mode" VALUE="false"/>
  <PROPERTY NAME="enable_dhcp_thresholds" VALUE="false"/>
  <PROPERTY NAME="range_high_water_mark" VALUE="95"/>
  <PROPERTY NAME="range_low_water_mark" VALUE="0"/>
  <PROPERTY NAME="enable_threshold_email_warnings" VALUE="false"/>
  <PROPERTY NAME="enable_threshold_snmp_warnings" VALUE="false"/>
  <PROPERTY NAME="override_threshold_email_notification" VALUE="false"/>
  <PROPERTY NAME="retry_dns_updates" VALUE="true"/>
  <PROPERTY NAME="retry_dns_updates_interval" VALUE="5"/>
  <PROPERTY NAME="rate_limit_dhcp_expirations" VALUE="true"/>
  <PROPERTY NAME="dhcpd_startup_expiration_time_limit" VALUE="5"/>
  <PROPERTY NAME="ddns_txt_record_handling" VALUE="isc"/>
  <PROPERTY NAME="ddns_ttl" VALUE="0"/>
  <PROPERTY NAME="ignore_dhcp_param_request_list" VALUE="false"/>
  <PROPERTY NAME="update_dns_on_renew" VALUE="false"/>
  <PROPERTY NAME="fixed_address_obeys_mac_filter" VALUE="false"/>
  <PROPERTY NAME="enable_leasequery" VALUE="false"/>
  <PROPERTY NAME="deny_bootp" VALUE="false"/>
  <PROPERTY NAME="network_views_count" VALUE="0"/>
  <PROPERTY NAME="hostname_translation" VALUE="None"/>
  <PROPERTY NAME="enable_roaming_hosts" VALUE="false"/>
  <PROPERTY NAME="enable_ifmap" VALUE="false"/>
  <PROPERTY NAME="always_defer_dns_updates" VALUE="false"/>
  <PROPERTY NAME="failover_port" VALUE="647"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="view_for_internal_updates" VALUE="._default"/>
  <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dns_properties"/>
  <PROPERTY NAME="custom_root_server" VALUE="false"/>
  <PROPERTY NAME="default_ttl" VALUE="28800"/>
  <PROPERTY NAME="expire" VALUE="2419200"/>
  <PROPERTY NAME="negative_ttl" VALUE="900"/>
  <PROPERTY NAME="recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="refresh" VALUE="10800"/>
  <PROPERTY NAME="retry" VALUE="3600"/>
  <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_bulkhost_ddns" VALUE="0"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="facility" VALUE="daemon"/>
  <PROPERTY NAME="log_general" VALUE="true"/>
  <PROPERTY NAME="log_client" VALUE="true"/>
  <PROPERTY NAME="log_config" VALUE="true"/>
  <PROPERTY NAME="log_database" VALUE="true"/>
  <PROPERTY NAME="log_dnssec" VALUE="true"/>
  <PROPERTY NAME="log_lame_servers" VALUE="true"/>
  <PROPERTY NAME="log_network" VALUE="true"/>
  <PROPERTY NAME="log_notify" VALUE="true"/>
  <PROPERTY NAME="log_queries" VALUE="false"/>
  <PROPERTY NAME="log_resolver" VALUE="true"/>
  <PROPERTY NAME="log_security" VALUE="true"/>
  <PROPERTY NAME="log_update" VALUE="true"/>
  <PROPERTY NAME="log_xfer_in" VALUE="true"/>
  <PROPERTY NAME="log_xfer_out" VALUE="true"/>
  <PROPERTY NAME="log_update_security" VALUE="true"/>
  <PROPERTY NAME="double_confirm_zone_deletion" VALUE="true"/>
  <PROPERTY NAME="enable_secondary_notify" VALUE="true"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
  <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
  <PROPERTY NAME="max_replication_send_queue_size_for_gui_ixfr" VALUE="1000"/>
  <PROPERTY NAME="views_count" VALUE="0"/>
  <PROPERTY NAME="srgs_count" VALUE="0"/>
  <PROPERTY NAME="check_names_policy" VALUE="1"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
  <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
  <PROPERTY NAME="lower_case_ptr_dname" VALUE="true"/>
  <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
  <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
  <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
  <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="record_name_policy" VALUE="Allow Underscore"/>
  <PROPERTY NAME="bulk_host_name_template" VALUE="Four Octets"/>
  <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
  <PROPERTY NAME="dnssec_ksk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_ksk_size" VALUE="2048"/>
  <PROPERTY NAME="dnssec_zsk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_zsk_size" VALUE="1024"/>
  <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_ipam_properties"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.dhcp_mac_filter_parent"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_config"/>
  <PROPERTY NAME="mode" VALUE="FULL"/>
  <PROPERTY NAME="tcp_scan_technique" VALUE="SYN"/>
  <PROPERTY NAME="ping_timeout" VALUE="1000"/>
  <PROPERTY NAME="ping_retries" VALUE="2"/>
  <PROPERTY NAME="update_metadata" VALUE="true"/>
  <PROPERTY NAME="merge_data" VALUE="true"/>
  <PROPERTY NAME="ip_discovery_disabled" VALUE="false"/>
  <PROPERTY NAME="v_discovery_disabled" VALUE="false"/>
  <PROPERTY NAME="task" VALUE="current"/>
  <PROPERTY NAME="v_discovery_network_view" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="current"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_discovering_state"/>
  <PROPERTY NAME="state" VALUE="COMPLETE"/>
  <PROPERTY NAME="raw_created" VALUE="0"/>
  <PROPERTY NAME="producer_v_total" VALUE="0"/>
  <PROPERTY NAME="v_raw_created" VALUE="0"/>
  <PROPERTY NAME="task" VALUE=".com.infoblox.dns.discovery_task$current"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.discovery_task$current"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_discovering_state"/>
  <PROPERTY NAME="state" VALUE="COMPLETE"/>
  <PROPERTY NAME="raw_created" VALUE="0"/>
  <PROPERTY NAME="producer_v_total" VALUE="0"/>
  <PROPERTY NAME="v_raw_created" VALUE="0"/>
  <PROPERTY NAME="task" VALUE=".com.infoblox.dns.discovery_task$scheduled"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.discovery_task$scheduled"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="21"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="ftp"/>
  <PROPERTY NAME="__key" VALUE="current.21"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="22"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="ssh"/>
  <PROPERTY NAME="__key" VALUE="current.22"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="23"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="telnet"/>
  <PROPERTY NAME="__key" VALUE="current.23"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="25"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="smtp"/>
  <PROPERTY NAME="__key" VALUE="current.25"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="79"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="finger"/>
  <PROPERTY NAME="__key" VALUE="current.79"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="80"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="http"/>
  <PROPERTY NAME="__key" VALUE="current.80"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="88"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="kerberos-sec"/>
  <PROPERTY NAME="__key" VALUE="current.88"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="110"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="pop3"/>
  <PROPERTY NAME="__key" VALUE="current.110"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="111"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="rpcbind"/>
  <PROPERTY NAME="__key" VALUE="current.111"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="113"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="auth"/>
  <PROPERTY NAME="__key" VALUE="current.113"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="135"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="msrpc"/>
  <PROPERTY NAME="__key" VALUE="current.135"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="137"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="netbios-ns"/>
  <PROPERTY NAME="__key" VALUE="current.137"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="138"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="netbios-dgm"/>
  <PROPERTY NAME="__key" VALUE="current.138"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="139"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="netbios-ssn"/>
  <PROPERTY NAME="__key" VALUE="current.139"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="143"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="imap"/>
  <PROPERTY NAME="__key" VALUE="current.143"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="443"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="https"/>
  <PROPERTY NAME="__key" VALUE="current.443"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="445"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="microsoft-ds"/>
  <PROPERTY NAME="__key" VALUE="current.445"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="513"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="login"/>
  <PROPERTY NAME="__key" VALUE="current.513"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="514"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="shell"/>
  <PROPERTY NAME="__key" VALUE="current.514"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="515"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="printer"/>
  <PROPERTY NAME="__key" VALUE="current.515"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="543"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="klogin"/>
  <PROPERTY NAME="__key" VALUE="current.543"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="750"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="kerberos"/>
  <PROPERTY NAME="__key" VALUE="current.750"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="1024"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="kdm"/>
  <PROPERTY NAME="__key" VALUE="current.1024"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_port"/>
  <PROPERTY NAME="tcp_port" VALUE="1025"/>
  <PROPERTY NAME="config" VALUE="current"/>
  <PROPERTY NAME="comment" VALUE="NFS-or-IIS"/>
  <PROPERTY NAME="__key" VALUE="current.1025"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_processing_state"/>
  <PROPERTY NAME="state" VALUE="COMPLETE"/>
  <PROPERTY NAME="processed_objects" VALUE="0"/>
  <PROPERTY NAME="unmanaged_created" VALUE="0"/>
  <PROPERTY NAME="conflicts_found" VALUE="0"/>
  <PROPERTY NAME="managed_seen" VALUE="0"/>
  <PROPERTY NAME="metadata_updated" VALUE="0"/>
  <PROPERTY NAME="latest_action" VALUE="INIT"/>
  <PROPERTY NAME="latest_action_error" VALUE="NO_ERROR"/>
  <PROPERTY NAME="task" VALUE=".com.infoblox.dns.discovery_task$current"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.discovery_task$current"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_processing_state"/>
  <PROPERTY NAME="state" VALUE="COMPLETE"/>
  <PROPERTY NAME="processed_objects" VALUE="0"/>
  <PROPERTY NAME="unmanaged_created" VALUE="0"/>
  <PROPERTY NAME="conflicts_found" VALUE="0"/>
  <PROPERTY NAME="managed_seen" VALUE="0"/>
  <PROPERTY NAME="metadata_updated" VALUE="0"/>
  <PROPERTY NAME="latest_action" VALUE="INIT"/>
  <PROPERTY NAME="latest_action_error" VALUE="NO_ERROR"/>
  <PROPERTY NAME="task" VALUE=".com.infoblox.dns.discovery_task$scheduled"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.discovery_task$scheduled"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_task"/>
  <PROPERTY NAME="discovery_task_oid" VALUE="current"/>
  <PROPERTY NAME="parent" VALUE="nd"/>
  <PROPERTY NAME="member" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="current"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.discovery_task"/>
  <PROPERTY NAME="discovery_task_oid" VALUE="scheduled"/>
  <PROPERTY NAME="parent" VALUE="nd"/>
  <PROPERTY NAME="member" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="scheduled"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
  <PROPERTY NAME="authoritative" VALUE="0"/>
  <PROPERTY NAME="override_authoritative" VALUE="false"/>
  <PROPERTY NAME="override_boot_file" VALUE="false"/>
  <PROPERTY NAME="override_boot_server" VALUE="false"/>
  <PROPERTY NAME="override_next_server" VALUE="false"/>
  <PROPERTY NAME="override_broadcast_address" VALUE="false"/>
  <PROPERTY NAME="override_custom_options" VALUE="false"/>
  <PROPERTY NAME="override_option60_match_rules" VALUE="false"/>
  <PROPERTY NAME="override_domain_name" VALUE="false"/>
  <PROPERTY NAME="override_domain_name_servers" VALUE="false"/>
  <PROPERTY NAME="override_lease_time" VALUE="false"/>
  <PROPERTY NAME="override_pxe_lease_time" VALUE="false"/>
  <PROPERTY NAME="override_routers" VALUE="false"/>
  <PROPERTY NAME="pxe_lease_time_enabled" VALUE="false"/>
  <PROPERTY NAME="override_local_dns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="local_ddns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="override_remote_dns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="remote_ddns_updates_enabled" VALUE="false"/>
  <PROPERTY NAME="override_update_static_leases" VALUE="false"/>
  <PROPERTY NAME="update_static_leases" VALUE="false"/>
  <PROPERTY NAME="service_enabled" VALUE="false"/>
  <PROPERTY NAME="service_enabled_on_lan2" VALUE="false"/>
  <PROPERTY NAME="force_restart_service" VALUE="false"/>
  <PROPERTY NAME="test_restart_service" VALUE="false"/>
  <PROPERTY NAME="restart_service_status" VALUE="No-request"/>
  <PROPERTY NAME="restart_in_0" VALUE="-1"/>
  <PROPERTY NAME="restart_in_1" VALUE="-1"/>
  <PROPERTY NAME="automatic_restart_needed" VALUE="false"/>
  <PROPERTY NAME="override_cluster_facility" VALUE="false"/>
  <PROPERTY NAME="facility" VALUE="daemon"/>
  <PROPERTY NAME="override_ddns_use_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_use_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="override_ddns_no_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_no_client_fqdn" VALUE="false"/>
  <PROPERTY NAME="override_ddns_server_use_fqdn" VALUE="false"/>
  <PROPERTY NAME="ddns_server_use_fqdn" VALUE="false"/>
  <PROPERTY NAME="override_ping_number" VALUE="false"/>
  <PROPERTY NAME="ping_number" VALUE="1"/>
  <PROPERTY NAME="override_ping_timeout" VALUE="false"/>
  <PROPERTY NAME="ping_timeout" VALUE="1"/>
  <PROPERTY NAME="override_recycle_leases" VALUE="false"/>
  <PROPERTY NAME="recycle_leases" VALUE="true"/>
  <PROPERTY NAME="override_log_lease_events" VALUE="false"/>
  <PROPERTY NAME="log_lease_events" VALUE="false"/>
  <PROPERTY NAME="override_dhcp_thresholds" VALUE="false"/>
  <PROPERTY NAME="enable_dhcp_thresholds" VALUE="false"/>
  <PROPERTY NAME="range_high_water_mark" VALUE="95"/>
  <PROPERTY NAME="range_low_water_mark" VALUE="0"/>
  <PROPERTY NAME="enable_threshold_email_warnings" VALUE="false"/>
  <PROPERTY NAME="enable_threshold_snmp_warnings" VALUE="false"/>
  <PROPERTY NAME="override_threshold_email_notification" VALUE="false"/>
  <PROPERTY NAME="retry_dns_updates" VALUE="true"/>
  <PROPERTY NAME="retry_dns_updates_interval" VALUE="5"/>
  <PROPERTY NAME="retry_dns_updates_override" VALUE="false"/>
  <PROPERTY NAME="ddns_ttl" VALUE="0"/>
  <PROPERTY NAME="override_ignore_dhcp_param_request_list" VALUE="false"/>
  <PROPERTY NAME="ignore_dhcp_param_request_list" VALUE="false"/>
  <PROPERTY NAME="is_scheduled" VALUE="false"/>
  <PROPERTY NAME="override_deny_bootp_request" VALUE="false"/>
  <PROPERTY NAME="deny_bootp" VALUE="false"/>
  <PROPERTY NAME="override_fixed_address_obeys_mac_filter" VALUE="false"/>
  <PROPERTY NAME="fixed_address_obeys_mac_filter" VALUE="false"/>
  <PROPERTY NAME="override_enable_leasequery" VALUE="false"/>
  <PROPERTY NAME="enable_leasequery" VALUE="false"/>
  <PROPERTY NAME="override_hostname_translation" VALUE="false"/>
  <PROPERTY NAME="hostname_translation" VALUE="None"/>
  <PROPERTY NAME="enable_ifmap" VALUE="false"/>
  <PROPERTY NAME="override_always_defer_dns_updates" VALUE="false"/>
  <PROPERTY NAME="always_defer_dns_updates" VALUE="false"/>
  <PROPERTY NAME="static_hosts" VALUE="0"/>
  <PROPERTY NAME="dynamic_hosts" VALUE="0"/>
  <PROPERTY NAME="total_hosts" VALUE="0"/>
  <PROPERTY NAME="dhcp_utilization" VALUE="0"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="override_ddns_ttl" VALUE="false"/>
  <PROPERTY NAME="override_gss_tsig" VALUE="false"/>
  <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_properties"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="limit_recursive_clients" VALUE="false"/>
  <PROPERTY NAME="numberof_recursive_clients" VALUE="1000"/>
  <PROPERTY NAME="override_cluster_sortlist" VALUE="false"/>
  <PROPERTY NAME="override_cluster_transfer_list" VALUE="false"/>
  <PROPERTY NAME="override_cluster_transfer_format" VALUE="false"/>
  <PROPERTY NAME="override_cluster_qacl" VALUE="false"/>
  <PROPERTY NAME="override_cluster_rqacl" VALUE="false"/>
  <PROPERTY NAME="override_cluster_ddns_updaters" VALUE="false"/>
  <PROPERTY NAME="override_cluster_forwarders" VALUE="false"/>
  <PROPERTY NAME="recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
  <PROPERTY NAME="service_enabled" VALUE="true"/>
  <PROPERTY NAME="force_restart_service" VALUE="false"/>
  <PROPERTY NAME="restart_service_status" VALUE="No-request"/>
  <PROPERTY NAME="restart_in_0" VALUE="-1"/>
  <PROPERTY NAME="restart_in_1" VALUE="-1"/>
  <PROPERTY NAME="automatic_restart_needed" VALUE="false"/>
  <PROPERTY NAME="minimal_response" VALUE="true"/>
  <PROPERTY NAME="override_cluster_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="override_cluster_facility" VALUE="false"/>
  <PROPERTY NAME="facility" VALUE="daemon"/>
  <PROPERTY NAME="override_cluster_logging" VALUE="false"/>
  <PROPERTY NAME="log_general" VALUE="true"/>
  <PROPERTY NAME="log_client" VALUE="true"/>
  <PROPERTY NAME="log_config" VALUE="true"/>
  <PROPERTY NAME="log_database" VALUE="true"/>
  <PROPERTY NAME="log_dnssec" VALUE="true"/>
  <PROPERTY NAME="log_lame_servers" VALUE="true"/>
  <PROPERTY NAME="log_network" VALUE="true"/>
  <PROPERTY NAME="log_notify" VALUE="true"/>
  <PROPERTY NAME="log_queries" VALUE="false"/>
  <PROPERTY NAME="log_resolver" VALUE="true"/>
  <PROPERTY NAME="log_security" VALUE="true"/>
  <PROPERTY NAME="log_update" VALUE="true"/>
  <PROPERTY NAME="log_xfer_in" VALUE="true"/>
  <PROPERTY NAME="log_xfer_out" VALUE="true"/>
  <PROPERTY NAME="log_update_security" VALUE="true"/>
  <PROPERTY NAME="auto_sort_views" VALUE="false"/>
  <PROPERTY NAME="use_nat_address" VALUE="0"/>
  <PROPERTY NAME="dns_over_mgmt" VALUE="false"/>
  <PROPERTY NAME="notify_xfr_source" VALUE="VIP"/>
  <PROPERTY NAME="query_source" VALUE="VIP"/>
  <PROPERTY NAME="dns_over_lan2" VALUE="false"/>
  <PROPERTY NAME="auto_create_a_and_ptr_for_lan2" VALUE="true"/>
  <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_cluster_notify_query_sport" VALUE="false"/>
  <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
  <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="is_scheduled" VALUE="false"/>
  <PROPERTY NAME="named_worker_threads" VALUE="0"/>
  <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
  <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
  <PROPERTY NAME="check_names_policy" VALUE="1"/>
  <PROPERTY NAME="override_dnssec" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
  <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
  <PROPERTY NAME="override_blackhole" VALUE="false"/>
  <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
  <PROPERTY NAME="gsstsig_key_expiration_time" VALUE="3600"/>
  <PROPERTY NAME="override_transfers_out" VALUE="false"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="nxdomain_redirect_override" VALUE="false"/>
  <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="gss_tsig_principal_name" VALUE="None"/>
  <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_view_nat_item"/>
  <PROPERTY NAME="glue_record_address_choice" VALUE="0"/>
  <PROPERTY NAME="attach_empty_recursive_view" VALUE="true"/>
  <PROPERTY NAME="member_dns_properties" VALUE="0"/>
  <PROPERTY NAME="view" VALUE="._default"/>
  <PROPERTY NAME="is_ipv4" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="0@._default@true"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_discovery_parent"/>
  <PROPERTY NAME="name" VALUE="nd"/>
  <PROPERTY NAME="__key" VALUE="nd"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view"/>
  <PROPERTY NAME="enable_view_display" VALUE="false"/>
  <PROPERTY NAME="parent" VALUE="/"/>
  <PROPERTY NAME="name" VALUE="default"/>
  <PROPERTY NAME="id" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.network_view_parent"/>
  <PROPERTY NAME="name" VALUE="/"/>
  <PROPERTY NAME="__key" VALUE="/"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="1"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="subnet-mask"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.subnet-mask"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="2"/>
  <PROPERTY NAME="type" VALUE="32-bit signed integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="time-offset"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.time-offset"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="3"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="routers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.routers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="4"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="time-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.time-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="5"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="ien116-name-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.ien116-name-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="6"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="domain-name-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.domain-name-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="7"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="log-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.log-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="8"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="cookie-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.cookie-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="9"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="lpr-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.lpr-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="10"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="impress-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.impress-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="11"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="resource-location-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.resource-location-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="12"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="host-name"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.host-name"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="13"/>
  <PROPERTY NAME="type" VALUE="16-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="boot-size"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.boot-size"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="14"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="merit-dump"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.merit-dump"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="15"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="domain-name"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.domain-name"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="16"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="swap-server"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.swap-server"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="17"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="root-path"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.root-path"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="18"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="extensions-path"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.extensions-path"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="19"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="ip-forwarding"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.ip-forwarding"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="20"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="non-local-source-routing"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.non-local-source-routing"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="21"/>
  <PROPERTY NAME="type" VALUE="array of ip-address pair"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="policy-filter"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.policy-filter"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="22"/>
  <PROPERTY NAME="type" VALUE="16-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="max-dgram-reassembly"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.max-dgram-reassembly"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="23"/>
  <PROPERTY NAME="type" VALUE="8-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="default-ip-ttl"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.default-ip-ttl"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="24"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="path-mtu-aging-timeout"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.path-mtu-aging-timeout"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="25"/>
  <PROPERTY NAME="type" VALUE="array of 16-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="path-mtu-plateau-table"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.path-mtu-plateau-table"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="26"/>
  <PROPERTY NAME="type" VALUE="16-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="interface-mtu"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.interface-mtu"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="27"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="all-subnets-local"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.all-subnets-local"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="28"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="broadcast-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.broadcast-address"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="29"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="perform-mask-discovery"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.perform-mask-discovery"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="30"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="mask-supplier"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.mask-supplier"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="31"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="router-discovery"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.router-discovery"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="32"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="router-solicitation-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.router-solicitation-address"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="33"/>
  <PROPERTY NAME="type" VALUE="array of ip-address pair"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="static-routes"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.static-routes"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="34"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="trailer-encapsulation"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.trailer-encapsulation"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="35"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="arp-cache-timeout"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.arp-cache-timeout"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="36"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="ieee802-3-encapsulation"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.ieee802-3-encapsulation"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="37"/>
  <PROPERTY NAME="type" VALUE="8-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="default-tcp-ttl"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.default-tcp-ttl"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="38"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="tcp-keepalive-interval"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.tcp-keepalive-interval"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="39"/>
  <PROPERTY NAME="type" VALUE="boolean"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="tcp-keepalive-garbage"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.tcp-keepalive-garbage"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="40"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="nis-domain"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.nis-domain"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="41"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="nis-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.nis-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="42"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="ntp-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.ntp-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="43"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="vendor-encapsulated-options"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.vendor-encapsulated-options"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="44"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="netbios-name-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.netbios-name-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="45"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="netbios-dd-server"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.netbios-dd-server"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="46"/>
  <PROPERTY NAME="type" VALUE="8-bit unsigned integer (1,2,4,8)"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="netbios-node-type"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.netbios-node-type"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="47"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="netbios-scope"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.netbios-scope"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="48"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="font-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.font-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="49"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="x-display-manager"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.x-display-manager"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="50"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-requested-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-requested-address"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="51"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-lease-time"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-lease-time"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="52"/>
  <PROPERTY NAME="type" VALUE="8-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-option-overload"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-option-overload"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="53"/>
  <PROPERTY NAME="type" VALUE="8-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-message-type"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-message-type"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="54"/>
  <PROPERTY NAME="type" VALUE="ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-server-identifier"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-server-identifier"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="55"/>
  <PROPERTY NAME="type" VALUE="array of 8-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-parameter-request-list"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-parameter-request-list"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="56"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-message"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-message"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="57"/>
  <PROPERTY NAME="type" VALUE="16-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-max-message-size"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-max-message-size"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="58"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-renewal-time"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-renewal-time"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="59"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-rebinding-time"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-rebinding-time"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="60"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="vendor-class-identifier"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.vendor-class-identifier"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="61"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="dhcp-client-identifier"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.dhcp-client-identifier"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="62"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="nwip-domain"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.nwip-domain"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="63"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="nwip-suboptions"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.nwip-suboptions"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="64"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="nisplus-domain"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.nisplus-domain"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="65"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="nisplus-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.nisplus-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="66"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="tftp-server-name"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.tftp-server-name"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="67"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="bootfile-name"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.bootfile-name"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="68"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="mobile-ip-home-agent"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.mobile-ip-home-agent"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="69"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="smtp-server"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.smtp-server"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="70"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="pop-server"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.pop-server"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="71"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="nntp-server"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.nntp-server"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="72"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="www-server"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.www-server"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="73"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="finger-server"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.finger-server"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="74"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="irc-server"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.irc-server"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="75"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="streettalk-server"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.streettalk-server"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="76"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="streettalk-directory-assistance-server"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.streettalk-directory-assistance-server"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="77"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="user-class"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.user-class"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="78"/>
  <PROPERTY NAME="type" VALUE="boolean array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="slp-directory-agent"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.slp-directory-agent"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="79"/>
  <PROPERTY NAME="type" VALUE="boolean-text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="slp-service-scope"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.slp-service-scope"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="81"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="fqdn"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.fqdn"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="82"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="relay-agent-information"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.relay-agent-information"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="85"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="nds-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.nds-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="86"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="nds-tree-name"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.nds-tree-name"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="87"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="nds-context"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.nds-context"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="89"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="bcms-controller-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.bcms-controller-address"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="91"/>
  <PROPERTY NAME="type" VALUE="32-bit unsigned integer"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="client-last-transaction-time"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.client-last-transaction-time"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="92"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="associated-ip"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.associated-ip"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="98"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="uap-servers"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.uap-servers"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="112"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="netinfo-server-address"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.netinfo-server-address"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="113"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="netinfo-server-tag"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.netinfo-server-tag"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="114"/>
  <PROPERTY NAME="type" VALUE="text"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="default-url"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.default-url"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="118"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="subnet-selection"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.subnet-selection"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="119"/>
  <PROPERTY NAME="type" VALUE="domain-list"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="domain-search"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.domain-search"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="124"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="vivco"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.vivco"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
  <PROPERTY NAME="code" VALUE="125"/>
  <PROPERTY NAME="type" VALUE="string"/>
  <PROPERTY NAME="space" VALUE="DHCP"/>
  <PROPERTY NAME="name" VALUE="vivso"/>
  <PROPERTY NAME="globally_enabled" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE="DHCP.vivso"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_space"/>
  <PROPERTY NAME="ms_is_user_class" VALUE="false"/>
  <PROPERTY NAME="is_ipv4" VALUE="true"/>
  <PROPERTY NAME="parent" VALUE="."/>
  <PROPERTY NAME="name" VALUE="DHCP"/>
  <PROPERTY NAME="ms_server" VALUE="."/>
  <PROPERTY NAME="__key" VALUE="DHCP..false"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_space"/>
  <PROPERTY NAME="ms_is_user_class" VALUE="false"/>
  <PROPERTY NAME="is_ipv4" VALUE="true"/>
  <PROPERTY NAME="parent" VALUE="."/>
  <PROPERTY NAME="name" VALUE="DHCPv6"/>
  <PROPERTY NAME="ms_server" VALUE="."/>
  <PROPERTY NAME="__key" VALUE="DHCPv6..false"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.record_name_policy"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="policy_name" VALUE="Strict Hostname Checking"/>
  <PROPERTY NAME="policy_regex" VALUE="^[a-zA-Z0-9]$|^[a-zA-Z0-9][-a-zA-Z0-9]*[a-zA-Z0-9]$"/>
  <PROPERTY NAME="__key" VALUE="Strict Hostname Checking"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.record_name_policy"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="policy_name" VALUE="Allow Underscore"/>
  <PROPERTY NAME="policy_regex" VALUE="^[-a-zA-Z0-9_]+$"/>
  <PROPERTY NAME="__key" VALUE="Allow Underscore"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.record_name_policy"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="policy_name" VALUE="Allow Any"/>
  <PROPERTY NAME="policy_regex" VALUE=".+"/>
  <PROPERTY NAME="__key" VALUE="Allow Any"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.shared_network_parent"/>
  <PROPERTY NAME="name" VALUE="/"/>
  <PROPERTY NAME="__key" VALUE="/"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.template_parent"/>
  <PROPERTY NAME="name" VALUE="."/>
  <PROPERTY NAME="__key" VALUE="."/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.view"/>
  <PROPERTY NAME="override_grid_and_member_recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="match_all_members" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
  <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
  <PROPERTY NAME="is_ddns_view" VALUE="true"/>
  <PROPERTY NAME="override_dnssec" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
  <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
  <PROPERTY NAME="override_lower_case_ptr_dname" VALUE="false"/>
  <PROPERTY NAME="lower_case_ptr_dname" VALUE="true"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="nxdomain_redirect_override" VALUE="false"/>
  <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default"/>
  <PROPERTY NAME="displayname" VALUE="default"/>
  <PROPERTY NAME="ddns_update_key" VALUE="/xdZoyJUn94rxcJS95H1Mg=="/>
  <PROPERTY NAME="network_view" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="._default"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.view"/>
  <PROPERTY NAME="override_grid_and_member_recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="recursion_enabled" VALUE="false"/>
  <PROPERTY NAME="match_all_members" VALUE="false"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
  <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
  <PROPERTY NAME="is_ddns_view" VALUE="false"/>
  <PROPERTY NAME="override_dnssec" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
  <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
  <PROPERTY NAME="override_lower_case_ptr_dname" VALUE="false"/>
  <PROPERTY NAME="lower_case_ptr_dname" VALUE="true"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="nxdomain_redirect_override" VALUE="false"/>
  <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE=".non_DNS_host_root"/>
  <PROPERTY NAME="displayname" VALUE=" "/>
  <PROPERTY NAME="network_view" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE=".non_DNS_host_root"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="View"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="primary_type" VALUE="None"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="check_names_policy" VALUE="1"/>
  <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
  <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
  <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
  <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
  <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
  <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
  <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
  <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
  <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
  <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
  <PROPERTY NAME="zone" VALUE=".."/>
  <PROPERTY NAME="name" VALUE="_default"/>
  <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
  <PROPERTY NAME="fqdn" VALUE="_default"/>
  <PROPERTY NAME="dnssec_ksk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_ksk_size" VALUE="2048"/>
  <PROPERTY NAME="dnssec_zsk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_zsk_size" VALUE="1024"/>
  <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
  <PROPERTY NAME="__key" VALUE="._default"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="SharedRecordGroup"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="primary_type" VALUE="None"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="check_names_policy" VALUE="1"/>
  <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
  <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
  <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
  <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
  <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
  <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
  <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
  <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
  <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
  <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
  <PROPERTY NAME="zone" VALUE=".."/>
  <PROPERTY NAME="name" VALUE="srg_root"/>
  <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
  <PROPERTY NAME="fqdn" VALUE="srg_root"/>
  <PROPERTY NAME="dnssec_ksk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_ksk_size" VALUE="2048"/>
  <PROPERTY NAME="dnssec_zsk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_zsk_size" VALUE="1024"/>
  <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
  <PROPERTY NAME="__key" VALUE=".srg_root"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="Dummy"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="1"/>
  <PROPERTY NAME="primary_type" VALUE="None"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="check_names_policy" VALUE="1"/>
  <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
  <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
  <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
  <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
  <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
  <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
  <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
  <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
  <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
  <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
  <PROPERTY NAME="zone" VALUE="._default"/>
  <PROPERTY NAME="name" VALUE="arpa.in-addr.127.0.0"/>
  <PROPERTY NAME="revzone_address" VALUE="*********"/>
  <PROPERTY NAME="revzone_netmask" VALUE="*************"/>
  <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
  <PROPERTY NAME="fqdn" VALUE="0.0.127.in-addr.arpa"/>
  <PROPERTY NAME="dnssec_ksk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_ksk_size" VALUE="2048"/>
  <PROPERTY NAME="dnssec_zsk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_zsk_size" VALUE="1024"/>
  <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
  <PROPERTY NAME="__key" VALUE="._default.arpa.in-addr.127.0.0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="NonDNSHost"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="primary_type" VALUE="None"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="check_names_policy" VALUE="1"/>
  <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
  <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
  <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
  <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
  <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
  <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
  <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
  <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
  <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
  <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
  <PROPERTY NAME="zone" VALUE=".."/>
  <PROPERTY NAME="name" VALUE="non_DNS_host_root"/>
  <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
  <PROPERTY NAME="fqdn" VALUE="non_DNS_host_root"/>
  <PROPERTY NAME="dnssec_ksk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_ksk_size" VALUE="2048"/>
  <PROPERTY NAME="dnssec_zsk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_zsk_size" VALUE="1024"/>
  <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
  <PROPERTY NAME="__key" VALUE=".non_DNS_host_root"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="action" VALUE="access_gui"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.admin_group$.admin-group..com.infoblox.one.admin_group$.admin-group.access_gui"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="action" VALUE="access_papi"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.admin_group$.admin-group..com.infoblox.one.admin_group$.admin-group.access_papi"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="auth_type" VALUE="LOCAL"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="override_auto_detect_time_zone" VALUE="false"/>
  <PROPERTY NAME="time_zone" VALUE="(UTC) Coordinated Universal Time"/>
  <PROPERTY NAME="table_size" VALUE="20"/>
  <PROPERTY NAME="name" VALUE="admin"/>
  <PROPERTY NAME="password" VALUE="{ssha}_HAAAAMeRoDvPMDNe8VDS4uXsZrPHSeBXldF/C7FTT9Y="/>
  <PROPERTY NAME="__key" VALUE="admin"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_auth_method"/>
  <PROPERTY NAME="auth_method_type" VALUE="Local"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="remote_admin_policy" VALUE="0"/>
  <PROPERTY NAME="auth_method_name" VALUE="Local Admin"/>
  <PROPERTY NAME="__key" VALUE="0.0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group"/>
  <PROPERTY NAME="tree_size" VALUE="50"/>
  <PROPERTY NAME="superuser" VALUE="true"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="name" VALUE="admin-group"/>
  <PROPERTY NAME="sub_grid" VALUE=""/>
  <PROPERTY NAME="__key" VALUE=".admin-group"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.admin_group_link"/>
  <PROPERTY NAME="admin" VALUE="admin"/>
  <PROPERTY NAME="admin_group" VALUE=".admin-group"/>
  <PROPERTY NAME="__key" VALUE="admin..admin-group"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.apache_certificate"/>
  <PROPERTY NAME="last_update" VALUE="0"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="app_certificate" VALUE="0.e7cadd70b990f77a4d8aba7ac2ac71886f4044b35a4953ff80db91162e27500542e2a9bb18208b4e20bb59ba4d1b42bf3a209ba32b6bd34009375fb38dcbad7e"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.app_certificate"/>
  <PROPERTY NAME="status" VALUE="V"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="obj_hash" VALUE="e7cadd70b990f77a4d8aba7ac2ac71886f4044b35a4953ff80db91162e27500542e2a9bb18208b4e20bb59ba4d1b42bf3a209ba32b6bd34009375fb38dcbad7e"/>
  <PROPERTY NAME="issuer" VALUE="egAAADB4MQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTESMBAGA1UEBxMJU3Vubnl2YWxlMREwDwYDVQQKEwhJbmZvYmxveDEUMBIGA1UECxMLRW5naW5lZXJpbmcxFzAVBgNVBAMTDkVQQllNSU5XMDQzM1Qy"/>
  <PROPERTY NAME="serial" VALUE="28882640d554c5a9e5a3f594b7d096d4"/>
  <PROPERTY NAME="common_name" VALUE="EPBYMINW0433T2"/>
  <PROPERTY NAME="__key" VALUE="0.e7cadd70b990f77a4d8aba7ac2ac71886f4044b35a4953ff80db91162e27500542e2a9bb18208b4e20bb59ba4d1b42bf3a209ba32b6bd34009375fb38dcbad7e"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.app_certificate_chunk"/>
  <PROPERTY NAME="seq_no" VALUE="0"/>
  <PROPERTY NAME="app_certificate" VALUE="0.e7cadd70b990f77a4d8aba7ac2ac71886f4044b35a4953ff80db91162e27500542e2a9bb18208b4e20bb59ba4d1b42bf3a209ba32b6bd34009375fb38dcbad7e"/>
  <PROPERTY NAME="certificate_chunk" VALUE="oQMAADCCA50wggKFoAMCAQICECiIJkDVVMWp5aP1lLfQltQwDQYJKoZIhvcNAQELBQAweDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCkNhbGlmb3JuaWExEjAQBgNVBAcTCVN1bm55dmFsZTERMA8GA1UEChMISW5mb2Jsb3gxFDASBgNVBAsTC0VuZ2luZWVyaW5nMRcwFQYDVQQDEw5FUEJZTUlOVzA0MzNUMjAeFw0yMDEyMTYxNjA1MzlaFw0yMTEyMTYxNjA1MzlaMHgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRIwEAYDVQQHEwlTdW5ueXZhbGUxETAPBgNVBAoTCEluZm9ibG94MRQwEgYDVQQLEwtFbmdpbmVlcmluZzEXMBUGA1UEAxMORVBCWU1JTlcwNDMzVDIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDw5GqPDY8LhzIZQwYX70Nz36fm3BYuJBTqjN0iipcv33Cj3F1xOxQ6rtZpMFTaRK1XzOQdqQvEeD/hzex7j+04Dmv9Y4DdxqQK23ymPLnEPVVVvqzzX2ZXd77eMNJUwa9uX3vqMPdrZRQkEXpW80hPVKmpI7FTe7MT6IvwzQvnXBMfi5sKMiAEPMD52t8BDzlBBk+UPPmnhaGdzaSjUgf+ur1dIi/3fDW/bkILu1bjyvQFvFeSWpK0ehpqt6a4pz91AFB/lGSY6qzQxsP2uaYtzNsaaAkSHhjHrMrksE/3Mcs5YRR0pTUcZBmuZUodxrK2kbQdmRv+ni8CpJiXBBA7AgMBAAGjIzAhMB8GA1UdEQQYMBaHBMCoAQKCDkVQQllNSU5XMDQzM1QyMA0GCSqGSIb3DQEBCwUAA4IBAQDac0DiYuY9yP4PMkavMO+buMkqj6dqjnAykEJ29YI0EqpQd9rPXhRtpYF+EU+09qMcqB4G2u3UzYKQ5Px0FWMO4MG5fChFwPN1DDbH7w+fvwU8/PBaK/HC"/>
  <PROPERTY NAME="__key" VALUE="0.e7cadd70b990f77a4d8aba7ac2ac71886f4044b35a4953ff80db91162e27500542e2a9bb18208b4e20bb59ba4d1b42bf3a209ba32b6bd34009375fb38dcbad7e.0"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.app_certificate_chunk"/>
  <PROPERTY NAME="seq_no" VALUE="1"/>
  <PROPERTY NAME="app_certificate" VALUE="0.e7cadd70b990f77a4d8aba7ac2ac71886f4044b35a4953ff80db91162e27500542e2a9bb18208b4e20bb59ba4d1b42bf3a209ba32b6bd34009375fb38dcbad7e"/>
  <PROPERTY NAME="certificate_chunk" VALUE="PlvnvjIPcFVB5KugDwxXyZ9FIJ9zsyEhRzwKJ7YBQC/HpZkP2ykHDXKCE9PeN2OFnhFh4WA82wN2krR8g5LSSQgFrYoc0OMXEbmNuhSXHYyX0XGXru4AqUG8vEiS7HRpo6J4dtfE/fP64DB0MvLzv8TfTOo8/WTVIfRCtP5zvq6yuS+2kp5W4Hf57YO/MSu3BS/Fd/6ikUDf17+GWkvm4WSX0p2uYGPb"/>
  <PROPERTY NAME="__key" VALUE="0.e7cadd70b990f77a4d8aba7ac2ac71886f4044b35a4953ff80db91162e27500542e2a9bb18208b4e20bb59ba4d1b42bf3a209ba32b6bd34009375fb38dcbad7e.1"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster"/>
  <PROPERTY NAME="virtual_node_count" VALUE="0"/>
  <PROPERTY NAME="ms_server_count" VALUE="0"/>
  <PROPERTY NAME="ms_rpc_timeout" VALUE="10000"/>
  <PROPERTY NAME="physical_node_count" VALUE="0"/>
  <PROPERTY NAME="vpn_port_number" VALUE="1194"/>
  <PROPERTY NAME="enable_recycle_bin" VALUE="true"/>
  <PROPERTY NAME="enable_scheduling" VALUE="false"/>
  <PROPERTY NAME="audit_log_format" VALUE="0"/>
  <PROPERTY NAME="restart_option" VALUE="1"/>
  <PROPERTY NAME="member_restart_sequential_delay" VALUE="10"/>
  <PROPERTY NAME="internal_apache_http_port" VALUE="9000"/>
  <PROPERTY NAME="internal_jetty_http_port" VALUE="8080"/>
  <PROPERTY NAME="enable_extended_discovery" VALUE="false"/>
  <PROPERTY NAME="cluster_parent" VALUE="."/>
  <PROPERTY NAME="cluster_oid" VALUE="0"/>
  <PROPERTY NAME="name" VALUE="Infoblox"/>
  <PROPERTY NAME="shared_secret" VALUE="test"/>
  <PROPERTY NAME="prompt_type" VALUE="Infoblox"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_email"/>
  <PROPERTY NAME="email_notify" VALUE="false"/>
  <PROPERTY NAME="specify_email_relay" VALUE="false"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_monitor"/>
  <PROPERTY NAME="syslog_size" VALUE="300"/>
  <PROPERTY NAME="enable_syslog_server" VALUE="false"/>
  <PROPERTY NAME="enable_snmp" VALUE="false"/>
  <PROPERTY NAME="enable_snmp_get" VALUE="false"/>
  <PROPERTY NAME="audit_to_syslog" VALUE="false"/>
  <PROPERTY NAME="syslog_facility" VALUE="daemon"/>
  <PROPERTY NAME="device_status_log_interval" VALUE="10800"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_resolver"/>
  <PROPERTY NAME="resolver_enabled" VALUE="false"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_security"/>
  <PROPERTY NAME="enable_http_redirect" VALUE="false"/>
  <PROPERTY NAME="security_enabled" VALUE="false"/>
  <PROPERTY NAME="session_timeout" VALUE="600"/>
  <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
  <PROPERTY NAME="support_access_enabled" VALUE="false"/>
  <PROPERTY NAME="login_banner_enabled" VALUE="true"/>
  <PROPERTY NAME="audit_log_rolling_enabled" VALUE="true"/>
  <PROPERTY NAME="ssh_perm_disabled" VALUE="false"/>
  <PROPERTY NAME="password_min_length" VALUE="4"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="login_banner_text" VALUE="Disconnect NOW if you have not been expressly authorized to use this system."/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster_time"/>
  <PROPERTY NAME="ntp_enabled" VALUE="false"/>
  <PROPERTY NAME="time_zone" VALUE="(UTC) Coordinated Universal Time"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.directory"/>
  <PROPERTY NAME="root_type" VALUE="tftp"/>
  <PROPERTY NAME="parent_dir" VALUE="/"/>
  <PROPERTY NAME="name" VALUE=""/>
  <PROPERTY NAME="__key" VALUE="/"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="name" VALUE="Building"/>
  <PROPERTY NAME="allowed_object_types" VALUE="Network,IPv6Network"/>
  <PROPERTY NAME="__key" VALUE="Building"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="name" VALUE="Country"/>
  <PROPERTY NAME="allowed_object_types" VALUE="Network,IPv6Network"/>
  <PROPERTY NAME="__key" VALUE="Country"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="name" VALUE="Region"/>
  <PROPERTY NAME="allowed_object_types" VALUE="Network,IPv6Network"/>
  <PROPERTY NAME="__key" VALUE="Region"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="name" VALUE="Site"/>
  <PROPERTY NAME="__key" VALUE="Site"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="name" VALUE="State"/>
  <PROPERTY NAME="allowed_object_types" VALUE="Network,IPv6Network"/>
  <PROPERTY NAME="__key" VALUE="State"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="name" VALUE="VLAN"/>
  <PROPERTY NAME="allowed_object_types" VALUE="Network,IPv6Network"/>
  <PROPERTY NAME="__key" VALUE="VLAN"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.grid_upgrade"/>
  <PROPERTY NAME="grid_distribute_type" VALUE="NONE"/>
  <PROPERTY NAME="grid_distribution_active" VALUE="false"/>
  <PROPERTY NAME="grid_distribution_state" VALUE="NONE"/>
  <PROPERTY NAME="grid_upgrade_type" VALUE="NONE"/>
  <PROPERTY NAME="grid_upgrade_active" VALUE="false"/>
  <PROPERTY NAME="is_upgrade_lite" VALUE="false"/>
  <PROPERTY NAME="member_upgrade_timeout" VALUE="10"/>
  <PROPERTY NAME="member_revert_window" VALUE="172800"/>
  <PROPERTY NAME="grid_upgrade_oid" VALUE="0"/>
  <PROPERTY NAME="release" VALUE="NO_RELEASE"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.cluster$0...com.infoblox.one.admin_group$.admin-group."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.saml-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.cluster$0...com.infoblox.one.admin_group$.saml-group."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.option_space_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.option_space_parent$....com.infoblox.one.admin_group$.admin-group."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.restorable_operation_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.restorable_operation_parent$....com.infoblox.one.admin_group$.admin-group."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.csv_import_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.csv_import_parent$....com.infoblox.one.admin_group$.admin-group."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-only"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.cluster$0...com.infoblox.one.admin_group$.cloud-api-only."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.option_space_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-only"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.option_space_parent$....com.infoblox.one.admin_group$.cloud-api-only."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.restorable_operation_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-only"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.restorable_operation_parent$....com.infoblox.one.admin_group$.cloud-api-only."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.non_DNS_host_root"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-only"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.non_DNS_host_root...com.infoblox.one.admin_group$.cloud-api-only."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.idns_root"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-only"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.idns_root...com.infoblox.one.admin_group$.cloud-api-only."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.idns_root."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-only"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.idns_root....com.infoblox.one.admin_group$.cloud-api-only."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-superuser"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.cluster$0...com.infoblox.one.admin_group$.cloud-api-superuser."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.option_space_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-superuser"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.option_space_parent$....com.infoblox.one.admin_group$.cloud-api-superuser."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.restorable_operation_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-superuser"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.restorable_operation_parent$....com.infoblox.one.admin_group$.cloud-api-superuser."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.non_DNS_host_root"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-superuser"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.non_DNS_host_root...com.infoblox.one.admin_group$.cloud-api-superuser."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.idns_root"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-superuser"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.idns_root...com.infoblox.one.admin_group$.cloud-api-superuser."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.idns_root."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-superuser"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.idns_root....com.infoblox.one.admin_group$.cloud-api-superuser."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DNS Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.....com.infoblox.one.role$DNS Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.srg_root"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DNS Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.srg_root...com.infoblox.one.role$DNS Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.virtual_node_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DNS Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.virtual_node_parent$0...com.infoblox.one.role$DNS Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.ms_server_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DNS Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.ms_server_parent$0...com.infoblox.one.role$DNS Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.kerberos_key_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DNS Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.kerberos_key_parent$0...com.infoblox.one.role$DNS Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.network_view_parent$/"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.network_view_parent$/...com.infoblox.one.role$DHCP Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.dhcp_mac_filter_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.dhcp_mac_filter_parent$0...com.infoblox.one.role$DHCP Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.template_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.template;is_ipv4=true"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.template_parent$....com.infoblox.one.role$DHCP Admin.dns.template;is_ipv4=true"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.template_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.template;is_ipv4=false"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.template_parent$....com.infoblox.one.role$DHCP Admin.dns.template;is_ipv4=false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.lease_history_access$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.lease_history_access;is_ipv4=true"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.lease_history_access$....com.infoblox.one.role$DHCP Admin.dns.lease_history_access;is_ipv4=true"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.lease_history_access$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.lease_history_access;is_ipv4=false"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.lease_history_access$....com.infoblox.one.role$DHCP Admin.dns.lease_history_access;is_ipv4=false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.virtual_node_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.virtual_node_parent$0...com.infoblox.one.role$DHCP Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.ms_server_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.ms_server_parent$0...com.infoblox.one.role$DHCP Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.dhcp_fingerprint_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.dhcp_fingerprint_parent$0...com.infoblox.one.role$DHCP Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.kerberos_key_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.kerberos_key_parent$0...com.infoblox.one.role$DHCP Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.tftp_storage$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$File Distribution Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.tftp_storage$0...com.infoblox.one.role$File Distribution Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.superhost_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Super Host Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.superhost_parent$0...com.infoblox.one.role$Super Host Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.vlan_view_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$VLAN Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.vlan_view"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.vlan_view_parent$0...com.infoblox.one.role$VLAN Admin.dns.vlan_view"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.vlan_view_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$VLAN Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.vlan_range"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.vlan_view_parent$0...com.infoblox.one.role$VLAN Admin.dns.vlan_range"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.vlan_view_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$VLAN Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.vlan"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.vlan_view_parent$0...com.infoblox.one.role$VLAN Admin.dns.vlan"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.network_view_parent$/"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$VLAN Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.network_view_parent$/...com.infoblox.one.role$VLAN Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.network_view_parent$/"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$VLAN Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.network;is_ipv4=true"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.network_view_parent$/...com.infoblox.one.role$VLAN Admin.dns.network;is_ipv4=true"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.network_view_parent$/"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$VLAN Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.network;is_ipv4=false"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.network_view_parent$/...com.infoblox.one.role$VLAN Admin.dns.network;is_ipv4=false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.....com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.srg_root"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.srg_root...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.virtual_node_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.virtual_node_parent$0...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.ms_server_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.ms_server_parent$0...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.network_view_parent$/"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.network_view_parent$/...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.dhcp_mac_filter_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.dhcp_mac_filter_parent$0...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.template_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.template;is_ipv4=true"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.template_parent$....com.infoblox.one.role$Grid Admin.dns.template;is_ipv4=true"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.template_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.template;is_ipv4=false"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.template_parent$....com.infoblox.one.role$Grid Admin.dns.template;is_ipv4=false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.lease_history_access$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.lease_history_access;is_ipv4=true"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.lease_history_access$....com.infoblox.one.role$Grid Admin.dns.lease_history_access;is_ipv4=true"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.lease_history_access$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.lease_history_access;is_ipv4=false"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.lease_history_access$....com.infoblox.one.role$Grid Admin.dns.lease_history_access;is_ipv4=false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.tftp_storage$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.tftp_storage$0...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.network_discovery_parent$nd"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.network_discovery_parent$nd...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.kerberos_key_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.kerberos_key_parent$0...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.queued_task_queue$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.queued_task_queue$0...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.non_DNS_host_root"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.non_DNS_host_root...com.infoblox.one.admin_group$.admin-group."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.idns_root"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.idns_root...com.infoblox.one.admin_group$.admin-group."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.idns_root."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="true"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.idns_root....com.infoblox.one.admin_group$.admin-group."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.cluster_dhcp_properties$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DHCP Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.cluster_dhcp_properties$0...com.infoblox.one.role$DHCP Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.cluster_dns_properties$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DNS Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.cluster_dns_properties$0...com.infoblox.one.role$DNS Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.cluster_dhcp_properties$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.cluster_dhcp_properties$0...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.cluster_dns_properties$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.cluster_dns_properties$0...com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$PKI Admin"/>
  <PROPERTY NAME="sub_type" VALUE="one.eap_ca_cert"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.cluster$0...com.infoblox.one.role$PKI Admin.one.eap_ca_cert"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$PKI Admin"/>
  <PROPERTY NAME="sub_type" VALUE="one.ocsp_auth_service"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.cluster$0...com.infoblox.one.role$PKI Admin.one.ocsp_auth_service"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$PKI Admin"/>
  <PROPERTY NAME="sub_type" VALUE="one.hsm_group"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.cluster$0...com.infoblox.one.role$PKI Admin.one.hsm_group"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.network_view_parent$/"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$IPAM Discovery Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.network_view_parent$/...com.infoblox.one.role$IPAM Discovery Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.network_discovery_parent$nd"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$IPAM Discovery Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.network_discovery_parent$nd...com.infoblox.one.role$IPAM Discovery Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.virtual_node_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$IPAM Discovery Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.virtual_node_parent$0...com.infoblox.one.role$IPAM Discovery Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$IPAM Discovery Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.host"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.....com.infoblox.one.role$IPAM Discovery Admin.dns.host"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.idns_auth_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DTC Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.idns_lbdn"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.idns_auth_parent$0...com.infoblox.one.role$DTC Admin.dns.idns_lbdn"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DTC Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.idns_lbdn_record"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.zone$.....com.infoblox.one.role$DTC Admin.dns.idns_lbdn_record"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.idns_auth_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DTC Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.idns_pool"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.idns_auth_parent$0...com.infoblox.one.role$DTC Admin.dns.idns_pool"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.idns_monitor_auth_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DTC Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.idns_monitor_auth_parent$0...com.infoblox.one.role$DTC Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.idns_auth_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DTC Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.idns_server"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.idns_auth_parent$0...com.infoblox.one.role$DTC Admin.dns.idns_server"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.idns_auth_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DTC Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.idns_topology"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.idns_auth_parent$0...com.infoblox.one.role$DTC Admin.dns.idns_topology"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.idns_auth_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DTC Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.idns_certificate"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.idns_auth_parent$0...com.infoblox.one.role$DTC Admin.dns.idns_certificate"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.idns_auth_parent$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$DTC Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.maxmind_db_info"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.idns_auth_parent$0...com.infoblox.one.role$DTC Admin.dns.maxmind_db_info"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.csv_import_parent$."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.csv_import_parent$....com.infoblox.one.role$Grid Admin."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.network_view_parent$/"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$Grid Admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.ms_ad_sites_domain"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.dns.network_view_parent$/...com.infoblox.one.role$Grid Admin.dns.ms_ad_sites_domain"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.one.cluster$0"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.role$SAML Admin"/>
  <PROPERTY NAME="sub_type" VALUE="one.saml_auth_service"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="true"/>
  <PROPERTY NAME="allow_delete" VALUE="true"/>
  <PROPERTY NAME="allow_create" VALUE="true"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE=".com.infoblox.one.cluster$0...com.infoblox.one.role$SAML Admin.one.saml_auth_service"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_tftp_properties"/>
  <PROPERTY NAME="tftp_enabled" VALUE="false"/>
  <PROPERTY NAME="tftp_port" VALUE="69"/>
  <PROPERTY NAME="http_enabled" VALUE="false"/>
  <PROPERTY NAME="http_acl_enabled" VALUE="false"/>
  <PROPERTY NAME="ftp_enabled" VALUE="false"/>
  <PROPERTY NAME="ftp_port" VALUE="21"/>
  <PROPERTY NAME="ftp_passive_enabled" VALUE="true"/>
  <PROPERTY NAME="ftp_filelist_enabled" VALUE="false"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="ftp_banner" VALUE="Restricted Access Only"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ms_server_parent"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
  <PROPERTY NAME="hwtype" VALUE="IB-0000youareinchroot"/>
  <PROPERTY NAME="enable_auto_lan" VALUE="true"/>
  <PROPERTY NAME="enable_auto_ha" VALUE="true"/>
  <PROPERTY NAME="ha_port_speed" VALUE="10"/>
  <PROPERTY NAME="enable_auto_mgmt" VALUE="true"/>
  <PROPERTY NAME="mgmt_port_enabled" VALUE="false"/>
  <PROPERTY NAME="enable_auto_lan2" VALUE="true"/>
  <PROPERTY NAME="restrict_sec_access_to_mgmt_port" VALUE="false"/>
  <PROPERTY NAME="mgmt_port_vpn_enabled" VALUE="false"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="master_notification_required" VALUE="false"/>
  <PROPERTY NAME="override_device_status_log_interval" VALUE="false"/>
  <PROPERTY NAME="device_status_log_interval" VALUE="10800"/>
  <PROPERTY NAME="physical_oid" VALUE="0"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="hwid" VALUE="a6f7422bf0a22e27cb474a8a4a59f33"/>
  <PROPERTY NAME="hwmodel" VALUE=""/>
  <PROPERTY NAME="public_ip_address" VALUE="***********"/>
  <PROPERTY NAME="serial_number" VALUE="0001youareinchroot"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.private_key"/>
  <PROPERTY NAME="app_certificate" VALUE="0.e7cadd70b990f77a4d8aba7ac2ac71886f4044b35a4953ff80db91162e27500542e2a9bb18208b4e20bb59ba4d1b42bf3a209ba32b6bd34009375fb38dcbad7e"/>
  <PROPERTY NAME="private_key1" VALUE="{0}_{aes}_AAQAAM5bxUYFNU1nC0b9IlY5OvuCyg4P8bnGsvELIOQzPEBX44kVkLTXZ/LVus5jyowOtcnQZau5Icwd4iIse4qvTD/YbM70WJB5WbRieUQucDSHmhZ+U1jY1lf18Yshd3YMNlBdPH/pNLxdVmDynvI6NGME3QSREN4Ryik0ggk4lIa3EvJMSU3jTLBA5m+0oy2F7AMoWBIhSBnapU1Xl9XUt/Jh1XdObfCfU2I7xEx8BubkOUJ5GcBOVfolLaUpnr6/ONLfJTGyJnZPUSlQB3/EyoOJbRc8VoHaYjbTYFLH5fEbe/H/IthQ9V6i+fY7oCKZzAEt85x6ZTI7PXQ9Wmreysm5AQimZmro+TqIp+ZKEKdt7ihOAjKq8+sd9kKYVc3FPlYy2PuPYJCJN7SDlyK1cyIxGJp0RExWyU+8RL6uDg2KZU/+4Ia9QF7LOGedD7Seb+9/dTxoBQOdfWsT6n6o8EgVEsyCPD1/2tix8Odrgg545e6c6HSK/bCMo1qc3o//EE3TCYEwRKTZ37esdCBYYTOOTlaMfvTI1hJWEfLAcU/2Kh6vPD9UE3avxJgFtm7+GNpiTAs2uzuKm6XG3ia92YeZot+YLebQQ1GEqan2T/VVr3n0J8ccp+U5L+FIHDNn/IoixHnpNVw7rx5R5pEreY7pg+NVi87OUedyW6G9gSpC8eEIt8L9bbhnbLlH9LHrGM+l02/7291oBwqW8R0rp1nU8cyp0UR4IGlcRXvesPnPS42z3MkWzuhi7B+Me1PTsLxjrLHMrhKfGXXbfOSFFSWNgGRwADuA4VIt9fdOPzH6pBW/8B9SwrNklc7xFc1M6h9gCTqV8+4iUgouiOl4Hh7iscFXvHLd9P03gBmVnCuinlV6oLpmdMOwWaVZ54sYnQxtcFi9qYj2kN03RrP1BsbyEcBjsS/TSjF2D6C/qpsThfGM1wjiJLSJqYX4eb/eFTp3y3eVfkuCaWs6uwcghlgTIIFV/k5bU3tH70EbTbs2+cnSlLrVkMuY0BIQXIpfQtZ/1JhlSzbCgXyb60ARhwBdk5Zbb2LRFmZxSYMXY/OTDD5MqAeg3/KPGmsIGd1cgFqilumbO08wCn95v7ORXu/HoGhcOgqbskkaPFL9mgeoy5R2pA7ZsQ8prbp5UV2jVfh6yS1VpdDhliGwWVr71cC4S7ACkzb6+PrGNkCQ+0UYxjiWBxALlQxHguKLgBe9ANQvkDwwZpZOdK8z//gSUys9hPapwVCleXiUy9CBHdWV6KzPqagFZlf5xCtwgw3uRNCTjuli19giccuy8izpM2f7qLwpPszjoL9vgHLFdH1ZqdIR4jR2I1M9y+g/zqMPnPSK6UiskuWMfvE0Nix4xGQ="/>
  <PROPERTY NAME="private_key2" VALUE="{0}_{aes}_UAIAAPkni7UBo4TcuWqgLo1F/mVKT/HrS9Ng38D0DkYeXhmCT/Hw3ABSRJvjKSnwje2QysoCk79SffmN5KU+1QzgbTth6H5VP0ACmCk4FrbmNKNHCeX1wGgWQWRMQsvbrFYPfcbniyoybP+sariHHDWezAYG7uruQsgq/7xVS6+xGFXOi/alnJ4QUVoCZaE6YdJM/bbDlLc8t/HY4w34ARHCHwqBw+X4nk2Tg7oGiIO+4K0GeVm8ORU/pauxiCuqii3Hd3cxaCMsyxIHTi+KD1PlcyYA/5RfHJIrQOYBWvoWvkbVeeYV4c+dtSkSCswque8RKnnwzjyv0o9I8WJ9wWvqLQmpDYpiOnuWfQGNmL6FXaB0d05Fv/OysnbbVX758t201dRzLA9+WPHrlieL50riwW+2G0gb1HIW+PPbHUNb6QcAyuVv94LoKfWCiHj6fL3/ACB0K7n0P5wMEh9xW+sOciGeyd+BnEuRbLcz0/OzspyOJO0ra5isQiYdx9fBgpKN43C5EZaTi72N8of50PaVp+Zf8AyP0LjhRul+QBSfy0P0PUCC9QI5cw/mxFNt1Uii3CRT6Y9coQ/WLwwV4L/9JPo4PRTK0avfN4DLfCJ9KQqf/Xjkq3egw0c3Pw9EA/EMFD4EV99AnY60u+dzDB/tVL+qG+P4hMCRxv52deGKIrtIKFma1Y65nduUSGDsoYyZ03HFKCYCaSR3MOhD8ZGhNEjLl7qxGaolaVNeuG69DOXGxKUvKuMtHYi5TQ9bE25KPpkStPIE080sD9QHqJ3ntAU="/>
  <PROPERTY NAME="__key" VALUE="0.e7cadd70b990f77a4d8aba7ac2ac71886f4044b35a4953ff80db91162e27500542e2a9bb18208b4e20bb59ba4d1b42bf3a209ba32b6bd34009375fb38dcbad7e"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.product_license"/>
  <PROPERTY NAME="pnode" VALUE="0"/>
  <PROPERTY NAME="license_type" VALUE="dhcp"/>
  <PROPERTY NAME="license_string" VALUE="EgAAAKsgMthNDSXUc+p9n7L6DLduDw=="/>
  <PROPERTY NAME="expiry_date" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0,dhcp,0"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.product_license"/>
  <PROPERTY NAME="pnode" VALUE="0"/>
  <PROPERTY NAME="license_type" VALUE="dns"/>
  <PROPERTY NAME="license_string" VALUE="EQAAAKsmItQcEWiFb6csg/+2VeQ7"/>
  <PROPERTY NAME="expiry_date" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0,dns,0"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.product_license"/>
  <PROPERTY NAME="pnode" VALUE="0"/>
  <PROPERTY NAME="license_type" VALUE="enterprise"/>
  <PROPERTY NAME="license_string" VALUE="GAAAAKomJc1DUGbBLb59n7L6QLRzRJF1wY9loQ=="/>
  <PROPERTY NAME="expiry_date" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0,enterprise,0"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.queued_task_queue"/>
  <PROPERTY NAME="queue_id" VALUE="0"/>
  <PROPERTY NAME="next_task_id" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.remote_admin_policy"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.restorable_operation_parent"/>
  <PROPERTY NAME="dummy" VALUE="."/>
  <PROPERTY NAME="__key" VALUE="."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.role"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="pre_defined" VALUE="true"/>
  <PROPERTY NAME="name" VALUE="DNS Admin"/>
  <PROPERTY NAME="comment" VALUE="DNS Administrator"/>
  <PROPERTY NAME="__key" VALUE="DNS Admin"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.role"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="pre_defined" VALUE="true"/>
  <PROPERTY NAME="name" VALUE="DHCP Admin"/>
  <PROPERTY NAME="comment" VALUE="DHCP Administrator"/>
  <PROPERTY NAME="__key" VALUE="DHCP Admin"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.role"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="pre_defined" VALUE="true"/>
  <PROPERTY NAME="name" VALUE="File Distribution Admin"/>
  <PROPERTY NAME="comment" VALUE="File Distribution Administrator"/>
  <PROPERTY NAME="__key" VALUE="File Distribution Admin"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.role"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="pre_defined" VALUE="true"/>
  <PROPERTY NAME="name" VALUE="Grid Admin"/>
  <PROPERTY NAME="comment" VALUE="Grid Administrator"/>
  <PROPERTY NAME="__key" VALUE="Grid Admin"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.scheduled_backups"/>
  <PROPERTY NAME="source" VALUE="DB"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="backup_type" VALUE="LOCAL"/>
  <PROPERTY NAME="backup_frequency" VALUE="Weekly"/>
  <PROPERTY NAME="minutes_past_hour" VALUE="0"/>
  <PROPERTY NAME="hours_past_day" VALUE="3"/>
  <PROPERTY NAME="weekday" VALUE="Saturday"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.scheduled_task_properties"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="restarts_per_hour" VALUE="60"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.system_state"/>
  <PROPERTY NAME="publish_changes" VALUE="false"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.tftp_storage"/>
  <PROPERTY NAME="storage_limit" VALUE="500"/>
  <PROPERTY NAME="backup_storage" VALUE="true"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.upgrade_group"/>
  <PROPERTY NAME="mem_dist_policy" VALUE="SIMULTANEOUSLY"/>
  <PROPERTY NAME="dist_time" VALUE="0"/>
  <PROPERTY NAME="mem_upgd_policy" VALUE="SEQUENTIALLY"/>
  <PROPERTY NAME="upgd_time" VALUE="0"/>
  <PROPERTY NAME="name" VALUE="Grid Master"/>
  <PROPERTY NAME="comment" VALUE="Grid Master Group"/>
  <PROPERTY NAME="__key" VALUE="Grid Master"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.upgrade_group"/>
  <PROPERTY NAME="mem_dist_policy" VALUE="SIMULTANEOUSLY"/>
  <PROPERTY NAME="dist_time" VALUE="0"/>
  <PROPERTY NAME="mem_upgd_policy" VALUE="SIMULTANEOUSLY"/>
  <PROPERTY NAME="upgd_time" VALUE="0"/>
  <PROPERTY NAME="name" VALUE="Default"/>
  <PROPERTY NAME="comment" VALUE="Default Upgrade Group "/>
  <PROPERTY NAME="__key" VALUE="Default"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
  <PROPERTY NAME="member_type" VALUE="INFOBLOX"/>
  <PROPERTY NAME="subnet_mask" VALUE="*************"/>
  <PROPERTY NAME="gateway" VALUE="192.168.1.1"/>
  <PROPERTY NAME="is_master" VALUE="true"/>
  <PROPERTY NAME="ha_enabled" VALUE="0"/>
  <PROPERTY NAME="lan2_enabled" VALUE="false"/>
  <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
  <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
  <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
  <PROPERTY NAME="override_support_access" VALUE="false"/>
  <PROPERTY NAME="support_access_enabled" VALUE="false"/>
  <PROPERTY NAME="is_potential_master" VALUE="true"/>
  <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
  <PROPERTY NAME="nat_enabled" VALUE="false"/>
  <PROPERTY NAME="upgrade_position" VALUE="0"/>
  <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
  <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
  <PROPERTY NAME="parent" VALUE="0"/>
  <PROPERTY NAME="virtual_oid" VALUE="0"/>
  <PROPERTY NAME="virtual_ip" VALUE="***********"/>
  <PROPERTY NAME="host_name" VALUE="infoblox.localdomain"/>
  <PROPERTY NAME="active_position" VALUE="0"/>
  <PROPERTY NAME="upgrade_group" VALUE="Grid Master"/>
  <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node_parent"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.vnode_time"/>
  <PROPERTY NAME="ntp_service_enabled" VALUE="false"/>
  <PROPERTY NAME="enable_authentication" VALUE="false"/>
  <PROPERTY NAME="override_cluster_ntp_key" VALUE="false"/>
  <PROPERTY NAME="override_cluster_ntp_acl" VALUE="false"/>
  <PROPERTY NAME="override_timezone" VALUE="false"/>
  <PROPERTY NAME="time_zone" VALUE="(UTC) Coordinated Universal Time"/>
  <PROPERTY NAME="external_ntp_servers_enabled" VALUE="false"/>
  <PROPERTY NAME="grid_master_ntp_server_excluded" VALUE="false"/>
  <PROPERTY NAME="override_cluster_ntp_server" VALUE="false"/>
  <PROPERTY NAME="virtual_node" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.webui_host"/>
  <PROPERTY NAME="webui_state" VALUE="UNINITALIZED"/>
  <PROPERTY NAME="ui_memory" VALUE="256"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PROPERTY NAME="member" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.webui_host_grid"/>
  <PROPERTY NAME="http_enabled" VALUE="true"/>
  <PROPERTY NAME="http_port" VALUE="444"/>
  <PROPERTY NAME="ftp_port" VALUE="26"/>
  <PROPERTY NAME="sshd_port" VALUE="28"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="ftp_enabled" VALUE="false"/>
  <PROPERTY NAME="sftp_enabled" VALUE="false"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.wizard_data_record"/>
  <PROPERTY NAME="value" VALUE="1"/>
  <PROPERTY NAME="name" VALUE="appliance_flag"/>
  <PROPERTY NAME="eula_version" VALUE="NONE"/>
  <PROPERTY NAME="__key" VALUE="appliance_flag"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.aaa_parent"/>
  <PROPERTY NAME="cluster" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.external_service_parent"/>
  <PROPERTY NAME="aaa_parent" VALUE="0"/>
  <PROPERTY NAME="__key" VALUE="0"/>
  <PARTITION-MAP VALUE="f+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="SharedRecordGroup"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="primary_type" VALUE="None"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="check_names_policy" VALUE="1"/>
  <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
  <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
  <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
  <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
  <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
  <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
  <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
  <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
  <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
  <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
  <PROPERTY NAME="zone" VALUE=".."/>
  <PROPERTY NAME="name" VALUE="srg_root_test_conflict"/>
  <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
  <PROPERTY NAME="fqdn" VALUE="srg_root_test_conflict"/>
  <PROPERTY NAME="dnssec_ksk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_ksk_size" VALUE="2048"/>
  <PROPERTY NAME="dnssec_zsk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_zsk_size" VALUE="1024"/>
  <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
  <PROPERTY NAME="__key" VALUE=".srg_root_test_conflict"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="SharedRecordGroup"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="primary_type" VALUE="None"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="check_names_policy" VALUE="1"/>
  <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
  <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
  <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
  <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
  <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
  <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
  <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
  <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
  <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
  <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
  <PROPERTY NAME="zone" VALUE=".."/>
  <PROPERTY NAME="name" VALUE="srg_root_test_conflict_second"/>
  <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
  <PROPERTY NAME="fqdn" VALUE="srg_root_test_conflict_second"/>
  <PROPERTY NAME="dnssec_ksk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_ksk_size" VALUE="2048"/>
  <PROPERTY NAME="dnssec_zsk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_zsk_size" VALUE="1024"/>
  <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
  <PROPERTY NAME="__key" VALUE=".srg_root_test_conflict_second"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="primary_type" VALUE="None"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="check_names_policy" VALUE="1"/>
  <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
  <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
  <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
  <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
  <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
  <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
  <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
  <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
  <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
  <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
  <PROPERTY NAME="zone" VALUE="._default"/>
  <PROPERTY NAME="name" VALUE="realzone_conflict"/>
  <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
  <PROPERTY NAME="fqdn" VALUE="realzone_conflict"/>
  <PROPERTY NAME="dnssec_ksk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_ksk_size" VALUE="2048"/>
  <PROPERTY NAME="dnssec_zsk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_zsk_size" VALUE="1024"/>
  <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
  <PROPERTY NAME="__key" VALUE="._default.realzone_conflict"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
  <PROPERTY NAME="is_external_primary" VALUE="false"/>
  <PROPERTY NAME="active_directory_option" VALUE="0"/>
  <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
  <PROPERTY NAME="underscore_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
  <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
  <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
  <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
  <PROPERTY NAME="primary_type" VALUE="None"/>
  <PROPERTY NAME="member_stealth" VALUE="false"/>
  <PROPERTY NAME="zone_qal_option" VALUE="0"/>
  <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
  <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
  <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="locked" VALUE="false"/>
  <PROPERTY NAME="check_names_policy" VALUE="1"/>
  <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
  <PROPERTY NAME="override_notify_delay" VALUE="false"/>
  <PROPERTY NAME="notify_delay" VALUE="5"/>
  <PROPERTY NAME="forwarders_only" VALUE="false"/>
  <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
  <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
  <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
  <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
  <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
  <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
  <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
  <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
  <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
  <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
  <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
  <PROPERTY NAME="zone" VALUE="._default.realzone_conflict"/>
  <PROPERTY NAME="name" VALUE="subz"/>
  <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
  <PROPERTY NAME="fqdn" VALUE="realzone_conflict"/>
  <PROPERTY NAME="dnssec_ksk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_ksk_size" VALUE="2048"/>
  <PROPERTY NAME="dnssec_zsk_algorithm" VALUE="5"/>
  <PROPERTY NAME="dnssec_zsk_size" VALUE="1024"/>
  <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
  <PROPERTY NAME="__key" VALUE="._default.realzone_conflict.subz"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.srg"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE=".srg_root_test_conflict"/>
  <PROPERTY NAME="name" VALUE="srg"/>
  <PROPERTY NAME="__key" VALUE=".srg_root_test_conflict"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.srg"/>
  <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE=".srg_root_test_conflict_second"/>
  <PROPERTY NAME="name" VALUE="srg"/>
  <PROPERTY NAME="__key" VALUE=".srg_root_test_conflict_second"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.srg_zone_linking"/>
  <PROPERTY NAME="srg" VALUE=".srg_root_test_conflict"/>
  <PROPERTY NAME="zone" VALUE="._default.realzone_conflict"/>
  <PROPERTY NAME="delegated_name" VALUE=""/>
  <PROPERTY NAME="__key" VALUE=".srg_root_test_conflict.._default.realzone_conflict."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.srg_zone_linking"/>
  <PROPERTY NAME="srg" VALUE=".srg_root_test_conflict_second"/>
  <PROPERTY NAME="zone" VALUE="._default.realzone_conflict"/>
  <PROPERTY NAME="delegated_name" VALUE=""/>
  <PROPERTY NAME="__key" VALUE=".srg_root_test_conflict_second.._default.realzone_conflict."/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_a"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="auto_created" VALUE="false"/>
  <PROPERTY NAME="shared_record" VALUE="true"/>
  <PROPERTY NAME="zone" VALUE=".srg_root_test_conflict"/>
  <PROPERTY NAME="name" VALUE="subz"/>
  <PROPERTY NAME="address" VALUE="*******"/>
  <PROPERTY NAME="__key" VALUE=".srg_root_test_conflict,subz,*******"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
</DATABASE>
