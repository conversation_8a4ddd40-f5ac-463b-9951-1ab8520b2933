#!/bin/bash       

############################################################################
#automatically generated iptables for syslog proxy client access control
#
#Do not edit!!!! 
############################################################################

. /infoblox/one/bin/one_common

PROXY_CHAIN=proxyacl  
TCP_PORT=666
UDP_PORT=555
TCP_RULE="INPUT --protocol tcp --dport ${TCP_PORT} -j ${PROXY_CHAIN}"
UDP_RULE="INPUT --protocol udp --dport ${UDP_PORT} -j ${PROXY_CHAIN}"

case "$1" in 

    start) 

     $IPTABLES -N $PROXY_CHAIN 
     if [ $? != "0" ]; then
        echo "Error adding new chain" | $IB_LOG 
        exit 1
     fi;

     $IPTABLES -A $TCP_RULE 
     if [ $? != "0" ]; then
        echo "Error adding tcp rule to input chain" | $IB_LOG 
        exit 1
     fi;

     $IPTABLES -A $UDP_RULE 
     if [ $? != "0" ]; then
        echo "Error adding udp rule to input chain" | $IB_LOG 
        exit 1
     fi;

     $IPTABLES -A $PROXY_CHAIN -s ********/*********** -j ACCEPT
     if [ $? != "0" ]; then
        echo "Error adding access control rule to proxyacl chain" | $IB_LOG 
        exit 1
     fi;

     $IPTABLES -A $PROXY_CHAIN -s ******** -j ACCEPT
     if [ $? != "0" ]; then
        echo "Error adding access control rule to proxyacl chain" | $IB_LOG 
        exit 1
     fi;

     $IPTABLES -A $PROXY_CHAIN -j DROP
     if [ $? != "0" ]; then
        echo "Error adding access control rule to proxyacl chain" | $IB_LOG 
        exit 1
     fi;


     ;;
    stop)

     $IPTABLES -F $PROXY_CHAIN 
     $IPTABLES -D $TCP_RULE 
     $IPTABLES -D $UDP_RULE 
     $IPTABLES -X $PROXY_CHAIN 

     ;;

esac

exit 0 
