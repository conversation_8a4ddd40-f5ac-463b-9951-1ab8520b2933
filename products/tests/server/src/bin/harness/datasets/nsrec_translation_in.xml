<DATABASE NAME="onedb" VERSION="7.1.0-267126" MD5="3edf22ffe8c2ce7b16bf75481c5b1821" SCHEMA-MD5="bc5ec45c2dee369961dd0957cee38ab5" INT-VERSION="7.1.6000-267126">
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="View"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="None"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE=".."/>
        <PROPERTY NAME="name" VALUE="_default"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="_default"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="_default"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="SharedRecordGroup"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="None"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE=".."/>
        <PROPERTY NAME="name" VALUE="srg_root"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="srg_root"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="srg_root"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="ResponsePolicy"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="None"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE=".."/>
        <PROPERTY NAME="name" VALUE="rpz_root"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="rpz_root"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="rpz_root"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Dummy"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="1"/>
        <PROPERTY NAME="primary_type" VALUE="None"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default"/>
        <PROPERTY NAME="name" VALUE="arpa.in-addr.127.0.0"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="revzone_address" VALUE="*********"/>
        <PROPERTY NAME="revzone_netmask" VALUE="*************"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="0.0.127.in-addr.arpa"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="arpa.in-addr.127.0.0"/>
        <PARTITION-MAP VALUE="f+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="NonDNSHost"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="None"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE=".."/>
        <PROPERTY NAME="name" VALUE="non_DNS_host_root"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="non_DNS_host_root"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="non_DNS_host_root"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="Grid"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default"/>
        <PROPERTY NAME="name" VALUE="zn.top"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="top.zn"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="zn.top"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="Grid"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="nsg"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="comment" VALUE="Zone assigned to NS Group"/>
        <PROPERTY NAME="assigned_ns_group" VALUE="authgroup01"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="nsg.top.zn"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="nsg"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="Grid"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="direct"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="comment" VALUE="Zone assigned to a list of servers directly"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="direct.top.zn"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="direct"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="true"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="External"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="sec"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="comment" VALUE="Secondary Zone assigned to a list of servers directly"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="sec.top.zn"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="sec"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Forward"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="None"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="fwd"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="comment" VALUE="Forward zone"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="fwd.top.zn"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="fwd"/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="Microsoft"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="NS"/>
        <PROPERTY NAME="ms_last_sync_time" VALUE="1426777841"/>
        <PROPERTY NAME="ms_last_sync_status" VALUE="OK"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="ms_sync_retry_offset" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="ms"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="comment" VALUE="MS Primary zone with Direct server assignment"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="ms.top.zn"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="ms"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Forward"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="None"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.ms"/>
        <PROPERTY NAME="name" VALUE="fwd"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="comment" VALUE="Forward zone under MS primary zone"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="fwd.ms.top.zn"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="fwd"/>
        <PARTITION-MAP VALUE="20+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Delegated"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="None"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="deleg"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="comment" VALUE="Delegation zone under TopZone"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="deleg.top.zn"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="deleg"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="active_directory_option" VALUE="0"/>
        <PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
        <PROPERTY NAME="underscore_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_for_underscore_zone" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
        <PROPERTY NAME="zone_type" VALUE="Authoritative"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="forwarding_disabled" VALUE="false"/>
        <PROPERTY NAME="is_reverse_zone" VALUE="0"/>
        <PROPERTY NAME="primary_type" VALUE="Grid"/>
        <PROPERTY NAME="zone_qal_option" VALUE="0"/>
        <PROPERTY NAME="import_zone_option" VALUE="false"/>
        <PROPERTY NAME="override_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="disable_all_subzones" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="locked" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="use_delegated_ttl" VALUE="false"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_override_key_parameters" VALUE="false"/>
        <PROPERTY NAME="dnssec_signature_expiration" VALUE="345600"/>
        <PROPERTY NAME="dnssec_ksk_rollover_interval" VALUE="31536000"/>
        <PROPERTY NAME="dnssec_zsk_rollover_interval" VALUE="2592000"/>
        <PROPERTY NAME="dnssec_previous_gm_role" VALUE="none"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="ms_ddns_mode" VALUE="None"/>
        <PROPERTY NAME="ms_allow_transfer_mode" VALUE="None"/>
        <PROPERTY NAME="ms_last_sync_failure_count" VALUE="0"/>
        <PROPERTY NAME="ms_synchronization_id" VALUE="0"/>
        <PROPERTY NAME="enable_rfc2317_exclusion" VALUE="false"/>
        <PROPERTY NAME="enable_lb_link" VALUE="false"/>
        <PROPERTY NAME="rpz_severity" VALUE="MAJOR"/>
        <PROPERTY NAME="rpz_policy" VALUE="Given"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_enabled" VALUE="false"/>
        <PROPERTY NAME="dns_integrity_check_frequency" VALUE="3600"/>
        <PROPERTY NAME="dns_integrity_check_verbose_logging" VALUE="false"/>
        <PROPERTY NAME="dnssec_nsec3_salt_min_length" VALUE="1"/>
        <PROPERTY NAME="dnssec_nsec3_salt_max_length" VALUE="15"/>
        <PROPERTY NAME="dnssec_nsec3_iterations" VALUE="10"/>
        <PROPERTY NAME="dnssec_zsk_rollover_mechanism" VALUE="PRE_PUBLISH"/>
        <PROPERTY NAME="dnssec_enable_ksk_auto_rollover" VALUE="false"/>
        <PROPERTY NAME="dnssec_ksk_rollover_notification_config" VALUE="REQUIRE_MANUAL_INTERVENTION"/>
        <PROPERTY NAME="dnssec_ksk_snmp_notification_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_ksk_email_notification_enabled" VALUE="false"/>
        <PROPERTY NAME="ms_sync_disabled" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="name" VALUE="mid.btm"/>
        <PROPERTY NAME="override_global_system_name" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PROPERTY NAME="fqdn" VALUE="btm.mid.direct.top.zn"/>
        <PROPERTY NAME="next_secure_type" VALUE="NSEC"/>
        <PROPERTY NAME="display_name" VALUE="mid.btm"/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_cluster_secondary_server"/>
        <PROPERTY NAME="cluster_sec_stealth" VALUE="false"/>
        <PROPERTY NAME="cluster_sec_lead" VALUE="false"/>
        <PROPERTY NAME="cluster_sec_zone_transfer" VALUE="0"/>
        <PROPERTY NAME="enable_preferred_primaries" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="member_address" VALUE="4"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_cluster_secondary_server"/>
        <PROPERTY NAME="cluster_sec_stealth" VALUE="false"/>
        <PROPERTY NAME="cluster_sec_lead" VALUE="false"/>
        <PROPERTY NAME="cluster_sec_zone_transfer" VALUE="1"/>
        <PROPERTY NAME="enable_preferred_primaries" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.sec"/>
        <PROPERTY NAME="member_address" VALUE="5"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_cluster_secondary_server"/>
        <PROPERTY NAME="cluster_sec_stealth" VALUE="false"/>
        <PROPERTY NAME="cluster_sec_lead" VALUE="false"/>
        <PROPERTY NAME="cluster_sec_zone_transfer" VALUE="1"/>
        <PROPERTY NAME="enable_preferred_primaries" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.ms"/>
        <PROPERTY NAME="member_address" VALUE="4"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_delegated_server"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.deleg"/>
        <PROPERTY NAME="address" VALUE="*********"/>
        <PROPERTY NAME="ds_name" VALUE="a.top.com"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_delegated_server"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.deleg"/>
        <PROPERTY NAME="address" VALUE="********"/>
        <PROPERTY NAME="ds_name" VALUE="ns5.deleg.top.zn"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_delegated_server"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.deleg"/>
        <PROPERTY NAME="address" VALUE="********"/>
        <PROPERTY NAME="ds_name" VALUE="c.top.com"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ext_primary"/>
        <PROPERTY NAME="tsig_option" VALUE="false"/>
        <PROPERTY NAME="x_tsig_option" VALUE="false"/>
        <PROPERTY NAME="ext_prim_stealth" VALUE="false"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.sec"/>
        <PROPERTY NAME="ext_address" VALUE="***************"/>
        <PROPERTY NAME="ext_name" VALUE="ext200.outer.space"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ext_secondary_server"/>
        <PROPERTY NAME="tsig_option" VALUE="false"/>
        <PROPERTY NAME="x_tsig_option" VALUE="false"/>
        <PROPERTY NAME="ext_sec_stealth" VALUE="false"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="ext_sec_address" VALUE="**********"/>
        <PROPERTY NAME="ext_sec_name" VALUE="ext555.outer.space"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ext_secondary_server"/>
        <PROPERTY NAME="tsig_option" VALUE="false"/>
        <PROPERTY NAME="x_tsig_option" VALUE="false"/>
        <PROPERTY NAME="ext_sec_stealth" VALUE="false"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct.mid.btm"/>
        <PROPERTY NAME="ext_sec_address" VALUE="**********"/>
        <PROPERTY NAME="ext_sec_name" VALUE="c.top.com"/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_forwarder"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.fwd"/>
        <PROPERTY NAME="address" VALUE="***********"/>
        <PROPERTY NAME="ds_name" VALUE="ext300.outer.space"/>
        <PROPERTY NAME="forward_address" VALUE="."/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_forwarder"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.ms.fwd"/>
        <PROPERTY NAME="address" VALUE="*********"/>
        <PROPERTY NAME="ds_name" VALUE="ext33.outer.space"/>
        <PROPERTY NAME="forward_address" VALUE="."/>
        <PARTITION-MAP VALUE="20+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_forwarding_server"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="use_override_forwarders" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.fwd"/>
        <PROPERTY NAME="forward_address" VALUE="4"/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_forwarding_server"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="use_override_forwarders" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.ms.fwd"/>
        <PROPERTY NAME="forward_address" VALUE="5"/>
        <PARTITION-MAP VALUE="20+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_grid_primary"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="grid_member" VALUE="0"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_grid_primary"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="grid_member" VALUE="5"/>
        <PARTITION-MAP VALUE="20+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_grid_primary"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct.mid.btm"/>
        <PROPERTY NAME="grid_member" VALUE="4"/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ms_primary_server"/>
        <PROPERTY NAME="is_master" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.ms"/>
        <PROPERTY NAME="ms_server" VALUE="0"/>
        <PROPERTY NAME="ns_ip" VALUE="************"/>
        <PROPERTY NAME="ns_name" VALUE="w8r264-12.inca.infoblox.com"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_nameserver"/>
        <PROPERTY NAME="auto_create_ptr" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="dname" VALUE="manual.top.zn"/>
        <PROPERTY NAME="address" VALUE="********"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_nameserver"/>
        <PROPERTY NAME="auto_create_ptr" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="dname" VALUE="b.top.com"/>
        <PROPERTY NAME="address" VALUE="*********"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_nameserver"/>
        <PROPERTY NAME="auto_create_ptr" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.nsg"/>
        <PROPERTY NAME="dname" VALUE="d.top.com"/>
        <PROPERTY NAME="address" VALUE="*********"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_nameserver"/>
        <PROPERTY NAME="auto_create_ptr" VALUE="true"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.nsg"/>
        <PROPERTY NAME="dname" VALUE="b.top.com"/>
        <PROPERTY NAME="address" VALUE="*********"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
        <PROPERTY NAME="uuid" VALUE="ac80b85e97124c38bd24747decdb4012"/>
        <PROPERTY NAME="revision_id" VALUE="12"/>
        <PROPERTY NAME="member_type" VALUE="INFOBLOX"/>
        <PROPERTY NAME="virtual_ip" VALUE="***********"/>
        <PROPERTY NAME="subnet_mask" VALUE="***********"/>
        <PROPERTY NAME="gateway" VALUE="*********"/>
        <PROPERTY NAME="is_master" VALUE="true"/>
        <PROPERTY NAME="ha_enabled" VALUE="0"/>
        <PROPERTY NAME="lan2_enabled" VALUE="false"/>
        <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
        <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
        <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
        <PROPERTY NAME="override_support_access" VALUE="false"/>
        <PROPERTY NAME="support_access_enabled" VALUE="false"/>
        <PROPERTY NAME="is_potential_master" VALUE="true"/>
        <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
        <PROPERTY NAME="nat_enabled" VALUE="false"/>
        <PROPERTY NAME="upgrade_position" VALUE="0"/>
        <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
        <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
        <PROPERTY NAME="override_member_redirect" VALUE="false"/>
        <PROPERTY NAME="enable_member_redirect" VALUE="false"/>
        <PROPERTY NAME="revert_window_start" VALUE="0"/>
        <PROPERTY NAME="revert_window_end" VALUE="0"/>
        <PROPERTY NAME="default_route" VALUE="LAN1"/>
        <PROPERTY NAME="use_dscp" VALUE="false"/>
        <PROPERTY NAME="dscp" VALUE="0"/>
        <PROPERTY NAME="use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="is_pre_provisioned" VALUE="false"/>
        <PROPERTY NAME="passive_ha_arp_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_router_discovery_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="v6_lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="use_v4_vrrp" VALUE="true"/>
        <PROPERTY NAME="config_addr_type" VALUE="IPV4"/>
        <PROPERTY NAME="service_type_configuration" VALUE="ALL_V4"/>
        <PROPERTY NAME="is_vconnector" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="0"/>
        <PROPERTY NAME="virtual_oid" VALUE="0"/>
        <PROPERTY NAME="host_name" VALUE="infoblox.localdomain"/>
        <PROPERTY NAME="active_position" VALUE="0"/>
        <PROPERTY NAME="upgrade_group" VALUE="Grid Master"/>
        <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
        <PROPERTY NAME="use_lom_users" VALUE="false"/>
        <PROPERTY NAME="lom_enabled" VALUE="true"/>
        <PROPERTY NAME="use_lom_enabled" VALUE="false"/>
        <PROPERTY NAME="_update_id" VALUE="0:0:fe38459:0:8082aa30"/>
        <PROPERTY NAME="proxy_auth_key" VALUE="{0}_{aes}_MAAAAFasCL8WqP8Bx3X2n91l8z57hCHgMI63307NifDOBdOAA82FAFcPyiRN1jCHsBcwqw=="/>
        <PARTITION-MAP VALUE="f+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
        <PROPERTY NAME="uuid" VALUE="5ad7dedd1c19451bba1e2ca565636a20"/>
        <PROPERTY NAME="revision_id" VALUE="23"/>
        <PROPERTY NAME="member_type" VALUE="VNIOS"/>
        <PROPERTY NAME="virtual_ip" VALUE="************"/>
        <PROPERTY NAME="subnet_mask" VALUE="***********"/>
        <PROPERTY NAME="gateway" VALUE="*********"/>
        <PROPERTY NAME="is_master" VALUE="false"/>
        <PROPERTY NAME="ha_enabled" VALUE="0"/>
        <PROPERTY NAME="lan2_enabled" VALUE="false"/>
        <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
        <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
        <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
        <PROPERTY NAME="override_support_access" VALUE="false"/>
        <PROPERTY NAME="support_access_enabled" VALUE="false"/>
        <PROPERTY NAME="is_potential_master" VALUE="false"/>
        <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
        <PROPERTY NAME="nat_enabled" VALUE="false"/>
        <PROPERTY NAME="upgrade_position" VALUE="0"/>
        <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
        <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
        <PROPERTY NAME="override_member_redirect" VALUE="false"/>
        <PROPERTY NAME="enable_member_redirect" VALUE="false"/>
        <PROPERTY NAME="revert_window_start" VALUE="0"/>
        <PROPERTY NAME="revert_window_end" VALUE="0"/>
        <PROPERTY NAME="default_route" VALUE="LAN1"/>
        <PROPERTY NAME="use_dscp" VALUE="false"/>
        <PROPERTY NAME="dscp" VALUE="0"/>
        <PROPERTY NAME="use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="is_pre_provisioned" VALUE="false"/>
        <PROPERTY NAME="passive_ha_arp_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_router_discovery_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="v6_lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="use_v4_vrrp" VALUE="true"/>
        <PROPERTY NAME="config_addr_type" VALUE="IPV4"/>
        <PROPERTY NAME="service_type_configuration" VALUE="ALL_V4"/>
        <PROPERTY NAME="is_vconnector" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="0"/>
        <PROPERTY NAME="virtual_oid" VALUE="1"/>
        <PROPERTY NAME="host_name" VALUE="a.top.com"/>
        <PROPERTY NAME="active_position" VALUE="0"/>
        <PROPERTY NAME="upgrade_group" VALUE="Default"/>
        <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
        <PROPERTY NAME="use_lom_users" VALUE="false"/>
        <PROPERTY NAME="lom_enabled" VALUE="true"/>
        <PROPERTY NAME="use_lom_enabled" VALUE="false"/>
        <PROPERTY NAME="_update_id" VALUE="0:0:ee5b631a:0:8003fee6"/>
        <PROPERTY NAME="proxy_auth_key" VALUE="{0}_{aes}_MAAAAHp69bGebYMIXQ5HQOSqSMU3XH94E8BNnCgJH3wF97cF2aGuxWm/89TEMbaMj8er1A=="/>
        <PARTITION-MAP VALUE="f+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
        <PROPERTY NAME="uuid" VALUE="3e01d74f590645389e7addc6ce931b82"/>
        <PROPERTY NAME="revision_id" VALUE="23"/>
        <PROPERTY NAME="member_type" VALUE="VNIOS"/>
        <PROPERTY NAME="virtual_ip" VALUE="***********"/>
        <PROPERTY NAME="subnet_mask" VALUE="***********"/>
        <PROPERTY NAME="gateway" VALUE="*********"/>
        <PROPERTY NAME="is_master" VALUE="false"/>
        <PROPERTY NAME="ha_enabled" VALUE="0"/>
        <PROPERTY NAME="lan2_enabled" VALUE="false"/>
        <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
        <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
        <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
        <PROPERTY NAME="override_support_access" VALUE="false"/>
        <PROPERTY NAME="support_access_enabled" VALUE="false"/>
        <PROPERTY NAME="is_potential_master" VALUE="false"/>
        <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
        <PROPERTY NAME="nat_enabled" VALUE="false"/>
        <PROPERTY NAME="upgrade_position" VALUE="1"/>
        <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
        <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
        <PROPERTY NAME="override_member_redirect" VALUE="false"/>
        <PROPERTY NAME="enable_member_redirect" VALUE="false"/>
        <PROPERTY NAME="revert_window_start" VALUE="0"/>
        <PROPERTY NAME="revert_window_end" VALUE="0"/>
        <PROPERTY NAME="default_route" VALUE="LAN1"/>
        <PROPERTY NAME="use_dscp" VALUE="false"/>
        <PROPERTY NAME="dscp" VALUE="0"/>
        <PROPERTY NAME="use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="is_pre_provisioned" VALUE="false"/>
        <PROPERTY NAME="passive_ha_arp_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_router_discovery_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="v6_lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="use_v4_vrrp" VALUE="true"/>
        <PROPERTY NAME="config_addr_type" VALUE="IPV4"/>
        <PROPERTY NAME="service_type_configuration" VALUE="ALL_V4"/>
        <PROPERTY NAME="is_vconnector" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="0"/>
        <PROPERTY NAME="virtual_oid" VALUE="2"/>
        <PROPERTY NAME="host_name" VALUE="b.top.com"/>
        <PROPERTY NAME="active_position" VALUE="0"/>
        <PROPERTY NAME="upgrade_group" VALUE="Default"/>
        <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
        <PROPERTY NAME="use_lom_users" VALUE="false"/>
        <PROPERTY NAME="lom_enabled" VALUE="true"/>
        <PROPERTY NAME="use_lom_enabled" VALUE="false"/>
        <PROPERTY NAME="_update_id" VALUE="0:0:ee5b631a:0:8003fee6"/>
        <PROPERTY NAME="proxy_auth_key" VALUE="{0}_{aes}_MAAAAAsjAGtF42twpwh2XAKvwMMxSKrv8BLlUEbZScmXJbk+eKFqSoMe5KDNEaglnFwrcg=="/>
        <PARTITION-MAP VALUE="f+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
        <PROPERTY NAME="uuid" VALUE="db4d4681b698421582e11d4309bf1dc0"/>
        <PROPERTY NAME="revision_id" VALUE="23"/>
        <PROPERTY NAME="member_type" VALUE="VNIOS"/>
        <PROPERTY NAME="virtual_ip" VALUE="***********"/>
        <PROPERTY NAME="subnet_mask" VALUE="***********"/>
        <PROPERTY NAME="gateway" VALUE="*********"/>
        <PROPERTY NAME="is_master" VALUE="false"/>
        <PROPERTY NAME="ha_enabled" VALUE="0"/>
        <PROPERTY NAME="lan2_enabled" VALUE="false"/>
        <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
        <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
        <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
        <PROPERTY NAME="override_support_access" VALUE="false"/>
        <PROPERTY NAME="support_access_enabled" VALUE="false"/>
        <PROPERTY NAME="is_potential_master" VALUE="false"/>
        <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
        <PROPERTY NAME="nat_enabled" VALUE="false"/>
        <PROPERTY NAME="upgrade_position" VALUE="2"/>
        <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
        <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
        <PROPERTY NAME="override_member_redirect" VALUE="false"/>
        <PROPERTY NAME="enable_member_redirect" VALUE="false"/>
        <PROPERTY NAME="revert_window_start" VALUE="0"/>
        <PROPERTY NAME="revert_window_end" VALUE="0"/>
        <PROPERTY NAME="default_route" VALUE="LAN1"/>
        <PROPERTY NAME="use_dscp" VALUE="false"/>
        <PROPERTY NAME="dscp" VALUE="0"/>
        <PROPERTY NAME="use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="is_pre_provisioned" VALUE="false"/>
        <PROPERTY NAME="passive_ha_arp_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_router_discovery_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="v6_lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="use_v4_vrrp" VALUE="true"/>
        <PROPERTY NAME="config_addr_type" VALUE="IPV4"/>
        <PROPERTY NAME="service_type_configuration" VALUE="ALL_V4"/>
        <PROPERTY NAME="is_vconnector" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="0"/>
        <PROPERTY NAME="virtual_oid" VALUE="4"/>
        <PROPERTY NAME="host_name" VALUE="d.top.com"/>
        <PROPERTY NAME="active_position" VALUE="0"/>
        <PROPERTY NAME="upgrade_group" VALUE="Default"/>
        <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
        <PROPERTY NAME="use_lom_users" VALUE="false"/>
        <PROPERTY NAME="lom_enabled" VALUE="true"/>
        <PROPERTY NAME="use_lom_enabled" VALUE="false"/>
        <PROPERTY NAME="_update_id" VALUE="0:0:ee5b631a:0:8003fee6"/>
        <PROPERTY NAME="proxy_auth_key" VALUE="{0}_{aes}_MAAAAJe8PmQUxYNyD13dSNft+2JL47clEHSV/MrXC+lMzKroFNwXf+FHoOXMgdyJA3vIOA=="/>
        <PARTITION-MAP VALUE="f+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
        <PROPERTY NAME="uuid" VALUE="83d06d40179144a2b12dbe3026e6b9e1"/>
        <PROPERTY NAME="revision_id" VALUE="25"/>
        <PROPERTY NAME="member_type" VALUE="VNIOS"/>
        <PROPERTY NAME="virtual_ip" VALUE="************"/>
        <PROPERTY NAME="subnet_mask" VALUE="***********"/>
        <PROPERTY NAME="gateway" VALUE="*********"/>
        <PROPERTY NAME="is_master" VALUE="false"/>
        <PROPERTY NAME="ha_enabled" VALUE="0"/>
        <PROPERTY NAME="lan2_enabled" VALUE="false"/>
        <PROPERTY NAME="nic_failover_enabled" VALUE="false"/>
        <PROPERTY NAME="override_remote_console_access" VALUE="false"/>
        <PROPERTY NAME="remote_console_access_enabled" VALUE="false"/>
        <PROPERTY NAME="override_support_access" VALUE="false"/>
        <PROPERTY NAME="support_access_enabled" VALUE="false"/>
        <PROPERTY NAME="is_potential_master" VALUE="false"/>
        <PROPERTY NAME="newly_promoted_master" VALUE="false"/>
        <PROPERTY NAME="nat_enabled" VALUE="false"/>
        <PROPERTY NAME="upgrade_position" VALUE="3"/>
        <PROPERTY NAME="vpn_mtu" VALUE="1450"/>
        <PROPERTY NAME="ipv6_enabled" VALUE="false"/>
        <PROPERTY NAME="override_member_redirect" VALUE="false"/>
        <PROPERTY NAME="enable_member_redirect" VALUE="false"/>
        <PROPERTY NAME="revert_window_start" VALUE="0"/>
        <PROPERTY NAME="revert_window_end" VALUE="0"/>
        <PROPERTY NAME="default_route" VALUE="LAN1"/>
        <PROPERTY NAME="use_dscp" VALUE="false"/>
        <PROPERTY NAME="dscp" VALUE="0"/>
        <PROPERTY NAME="use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="is_pre_provisioned" VALUE="false"/>
        <PROPERTY NAME="passive_ha_arp_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_router_discovery_enabled" VALUE="false"/>
        <PROPERTY NAME="v6_use_lan1_dscp" VALUE="false"/>
        <PROPERTY NAME="v6_lan1_dscp" VALUE="0"/>
        <PROPERTY NAME="use_v4_vrrp" VALUE="true"/>
        <PROPERTY NAME="config_addr_type" VALUE="IPV4"/>
        <PROPERTY NAME="service_type_configuration" VALUE="ALL_V4"/>
        <PROPERTY NAME="is_vconnector" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="0"/>
        <PROPERTY NAME="virtual_oid" VALUE="5"/>
        <PROPERTY NAME="host_name" VALUE="c.top.com"/>
        <PROPERTY NAME="active_position" VALUE="0"/>
        <PROPERTY NAME="upgrade_group" VALUE="Default"/>
        <PROPERTY NAME="force_mld_version_1" VALUE="false"/>
        <PROPERTY NAME="use_lom_users" VALUE="false"/>
        <PROPERTY NAME="lom_enabled" VALUE="true"/>
        <PROPERTY NAME="use_lom_enabled" VALUE="false"/>
        <PROPERTY NAME="_update_id" VALUE="0:0:ee5b631a:0:800405cc"/>
        <PROPERTY NAME="proxy_auth_key" VALUE="{0}_{aes}_MAAAAJ+c3Q5CQZQBsbDb7bV47DK2MU7Y2vvyj2lgZPKikoo9+NCDB0kZW/ssmrnddLHoZw=="/>
        <PARTITION-MAP VALUE="f+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node_parent"/>
        <PROPERTY NAME="cluster" VALUE="0"/>
        <PARTITION-MAP VALUE="f+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_properties"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="limit_recursive_clients" VALUE="false"/>
        <PROPERTY NAME="numberof_recursive_clients" VALUE="1000"/>
        <PROPERTY NAME="override_cluster_sortlist" VALUE="false"/>
        <PROPERTY NAME="override_cluster_transfer_list" VALUE="false"/>
        <PROPERTY NAME="override_cluster_transfer_format" VALUE="false"/>
        <PROPERTY NAME="override_cluster_qacl" VALUE="false"/>
        <PROPERTY NAME="override_cluster_rqacl" VALUE="false"/>
        <PROPERTY NAME="override_cluster_ddns_updaters" VALUE="false"/>
        <PROPERTY NAME="override_cluster_forwarders" VALUE="false"/>
        <PROPERTY NAME="recursion_enabled" VALUE="false"/>
        <PROPERTY NAME="lame_ttl" VALUE="600"/>
        <PROPERTY NAME="use_lame_ttl" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
        <PROPERTY NAME="minimal_response" VALUE="true"/>
        <PROPERTY NAME="override_cluster_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="override_cluster_facility" VALUE="false"/>
        <PROPERTY NAME="facility" VALUE="daemon"/>
        <PROPERTY NAME="override_cluster_logging" VALUE="false"/>
        <PROPERTY NAME="log_general" VALUE="true"/>
        <PROPERTY NAME="log_client" VALUE="true"/>
        <PROPERTY NAME="log_config" VALUE="true"/>
        <PROPERTY NAME="log_database" VALUE="true"/>
        <PROPERTY NAME="log_dnssec" VALUE="true"/>
        <PROPERTY NAME="log_lame_servers" VALUE="true"/>
        <PROPERTY NAME="log_network" VALUE="true"/>
        <PROPERTY NAME="log_notify" VALUE="true"/>
        <PROPERTY NAME="log_queries" VALUE="false"/>
        <PROPERTY NAME="log_resolver" VALUE="true"/>
        <PROPERTY NAME="log_security" VALUE="true"/>
        <PROPERTY NAME="log_update" VALUE="true"/>
        <PROPERTY NAME="log_xfer_in" VALUE="true"/>
        <PROPERTY NAME="log_xfer_out" VALUE="true"/>
        <PROPERTY NAME="log_update_security" VALUE="true"/>
        <PROPERTY NAME="log_rpz" VALUE="false"/>
        <PROPERTY NAME="log_responses" VALUE="false"/>
        <PROPERTY NAME="log_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="auto_sort_views" VALUE="false"/>
        <PROPERTY NAME="use_nat_address" VALUE="0"/>
        <PROPERTY NAME="dns_over_mgmt" VALUE="false"/>
        <PROPERTY NAME="notify_xfr_source" VALUE="VIP"/>
        <PROPERTY NAME="query_source" VALUE="VIP"/>
        <PROPERTY NAME="dns_over_lan2" VALUE="false"/>
        <PROPERTY NAME="auto_create_a_and_ptr_for_lan2" VALUE="true"/>
        <PROPERTY NAME="use_enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="override_cluster_notify_query_sport" VALUE="false"/>
        <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="named_worker_threads" VALUE="0"/>
        <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
        <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="override_dnssec" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
        <PROPERTY NAME="override_blackhole" VALUE="false"/>
        <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
        <PROPERTY NAME="gsstsig_key_expiration_time" VALUE="3600"/>
        <PROPERTY NAME="override_transfers_out" VALUE="false"/>
        <PROPERTY NAME="transfers_out" VALUE="10"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="nxdomain_redirect_override" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="nxdomain_log_query" VALUE="false"/>
        <PROPERTY NAME="blacklist_override" VALUE="false"/>
        <PROPERTY NAME="blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="blacklist_action" VALUE="REDIRECT"/>
        <PROPERTY NAME="blacklist_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="blacklist_log_query" VALUE="false"/>
        <PROPERTY NAME="always_ret_nxdomain_for_fmz_ptr" VALUE="false"/>
        <PROPERTY NAME="enable_dns64" VALUE="false"/>
        <PROPERTY NAME="use_dns64" VALUE="false"/>
        <PROPERTY NAME="override_dns_cache_ttl" VALUE="false"/>
        <PROPERTY NAME="dns_cache_ttl" VALUE="1"/>
        <PROPERTY NAME="enable_dns_cache_acceleration" VALUE="false"/>
        <PROPERTY NAME="dns_over_v6_lan2" VALUE="false"/>
        <PROPERTY NAME="dns_over_v6_mgmt" VALUE="false"/>
        <PROPERTY NAME="v6_auto_create_lan2_glue" VALUE="false"/>
        <PROPERTY NAME="override_filter_aaaa" VALUE="false"/>
        <PROPERTY NAME="filter_aaaa" VALUE="NO"/>
        <PROPERTY NAME="dns_over_v6_lan" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="transfers_in" VALUE="10"/>
        <PROPERTY NAME="use_transfers_in" VALUE="false"/>
        <PROPERTY NAME="transfers_per_ns" VALUE="2"/>
        <PROPERTY NAME="use_transfers_per_ns" VALUE="false"/>
        <PROPERTY NAME="serial_query_rate" VALUE="20"/>
        <PROPERTY NAME="use_serial_query_rate" VALUE="false"/>
        <PROPERTY NAME="override_max_cached_lifetime" VALUE="false"/>
        <PROPERTY NAME="max_cached_lifetime" VALUE="86400"/>
        <PROPERTY NAME="use_max_cache_ttl" VALUE="false"/>
        <PROPERTY NAME="max_cache_ttl" VALUE="604800"/>
        <PROPERTY NAME="use_max_ncache_ttl" VALUE="false"/>
        <PROPERTY NAME="max_ncache_ttl" VALUE="10800"/>
        <PROPERTY NAME="use_disable_edns" VALUE="false"/>
        <PROPERTY NAME="disable_edns" VALUE="false"/>
        <PROPERTY NAME="use_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="query_rewrite_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nxdomain_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_rpz_enabled" VALUE="false"/>
        <PROPERTY NAME="resolver_query_timeout" VALUE="0"/>
        <PROPERTY NAME="use_auto_blackhole" VALUE="false"/>
        <PROPERTY NAME="use_rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="dns_over_lan" VALUE="true"/>
        <PROPERTY NAME="log_idns_gslb" VALUE="false"/>
        <PROPERTY NAME="log_idns_health" VALUE="false"/>
        <PROPERTY NAME="idns_health_source" VALUE="VIP"/>
        <PROPERTY NAME="max_recursion_depth" VALUE="7"/>
        <PROPERTY NAME="max_recursion_queries" VALUE="150"/>
        <PROPERTY NAME="virtual_node" VALUE="0"/>
        <PROPERTY NAME="use_gss_tsig_keys" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_properties"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="limit_recursive_clients" VALUE="false"/>
        <PROPERTY NAME="numberof_recursive_clients" VALUE="1000"/>
        <PROPERTY NAME="override_cluster_sortlist" VALUE="false"/>
        <PROPERTY NAME="override_cluster_transfer_list" VALUE="false"/>
        <PROPERTY NAME="override_cluster_transfer_format" VALUE="false"/>
        <PROPERTY NAME="override_cluster_qacl" VALUE="false"/>
        <PROPERTY NAME="override_cluster_rqacl" VALUE="false"/>
        <PROPERTY NAME="override_cluster_ddns_updaters" VALUE="false"/>
        <PROPERTY NAME="override_cluster_forwarders" VALUE="false"/>
        <PROPERTY NAME="recursion_enabled" VALUE="false"/>
        <PROPERTY NAME="lame_ttl" VALUE="600"/>
        <PROPERTY NAME="use_lame_ttl" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
        <PROPERTY NAME="minimal_response" VALUE="true"/>
        <PROPERTY NAME="override_cluster_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="override_cluster_facility" VALUE="false"/>
        <PROPERTY NAME="facility" VALUE="daemon"/>
        <PROPERTY NAME="override_cluster_logging" VALUE="false"/>
        <PROPERTY NAME="log_general" VALUE="true"/>
        <PROPERTY NAME="log_client" VALUE="true"/>
        <PROPERTY NAME="log_config" VALUE="true"/>
        <PROPERTY NAME="log_database" VALUE="true"/>
        <PROPERTY NAME="log_dnssec" VALUE="true"/>
        <PROPERTY NAME="log_lame_servers" VALUE="true"/>
        <PROPERTY NAME="log_network" VALUE="true"/>
        <PROPERTY NAME="log_notify" VALUE="true"/>
        <PROPERTY NAME="log_queries" VALUE="false"/>
        <PROPERTY NAME="log_resolver" VALUE="true"/>
        <PROPERTY NAME="log_security" VALUE="true"/>
        <PROPERTY NAME="log_update" VALUE="true"/>
        <PROPERTY NAME="log_xfer_in" VALUE="true"/>
        <PROPERTY NAME="log_xfer_out" VALUE="true"/>
        <PROPERTY NAME="log_update_security" VALUE="true"/>
        <PROPERTY NAME="log_rpz" VALUE="false"/>
        <PROPERTY NAME="log_responses" VALUE="false"/>
        <PROPERTY NAME="log_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="auto_sort_views" VALUE="false"/>
        <PROPERTY NAME="use_nat_address" VALUE="0"/>
        <PROPERTY NAME="dns_over_mgmt" VALUE="false"/>
        <PROPERTY NAME="notify_xfr_source" VALUE="VIP"/>
        <PROPERTY NAME="query_source" VALUE="VIP"/>
        <PROPERTY NAME="dns_over_lan2" VALUE="false"/>
        <PROPERTY NAME="auto_create_a_and_ptr_for_lan2" VALUE="true"/>
        <PROPERTY NAME="use_enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="override_cluster_notify_query_sport" VALUE="false"/>
        <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="named_worker_threads" VALUE="0"/>
        <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
        <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="override_dnssec" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
        <PROPERTY NAME="override_blackhole" VALUE="false"/>
        <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
        <PROPERTY NAME="gsstsig_key_expiration_time" VALUE="3600"/>
        <PROPERTY NAME="override_transfers_out" VALUE="false"/>
        <PROPERTY NAME="transfers_out" VALUE="10"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="nxdomain_redirect_override" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="nxdomain_log_query" VALUE="false"/>
        <PROPERTY NAME="blacklist_override" VALUE="false"/>
        <PROPERTY NAME="blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="blacklist_action" VALUE="REDIRECT"/>
        <PROPERTY NAME="blacklist_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="blacklist_log_query" VALUE="false"/>
        <PROPERTY NAME="always_ret_nxdomain_for_fmz_ptr" VALUE="false"/>
        <PROPERTY NAME="enable_dns64" VALUE="false"/>
        <PROPERTY NAME="use_dns64" VALUE="false"/>
        <PROPERTY NAME="override_dns_cache_ttl" VALUE="false"/>
        <PROPERTY NAME="dns_cache_ttl" VALUE="1"/>
        <PROPERTY NAME="enable_dns_cache_acceleration" VALUE="false"/>
        <PROPERTY NAME="dns_over_v6_lan2" VALUE="false"/>
        <PROPERTY NAME="dns_over_v6_mgmt" VALUE="false"/>
        <PROPERTY NAME="v6_auto_create_lan2_glue" VALUE="false"/>
        <PROPERTY NAME="override_filter_aaaa" VALUE="false"/>
        <PROPERTY NAME="filter_aaaa" VALUE="NO"/>
        <PROPERTY NAME="dns_over_v6_lan" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="transfers_in" VALUE="10"/>
        <PROPERTY NAME="use_transfers_in" VALUE="false"/>
        <PROPERTY NAME="transfers_per_ns" VALUE="2"/>
        <PROPERTY NAME="use_transfers_per_ns" VALUE="false"/>
        <PROPERTY NAME="serial_query_rate" VALUE="20"/>
        <PROPERTY NAME="use_serial_query_rate" VALUE="false"/>
        <PROPERTY NAME="override_max_cached_lifetime" VALUE="false"/>
        <PROPERTY NAME="max_cached_lifetime" VALUE="86400"/>
        <PROPERTY NAME="use_max_cache_ttl" VALUE="false"/>
        <PROPERTY NAME="max_cache_ttl" VALUE="604800"/>
        <PROPERTY NAME="use_max_ncache_ttl" VALUE="false"/>
        <PROPERTY NAME="max_ncache_ttl" VALUE="10800"/>
        <PROPERTY NAME="use_disable_edns" VALUE="false"/>
        <PROPERTY NAME="disable_edns" VALUE="false"/>
        <PROPERTY NAME="use_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="query_rewrite_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nxdomain_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_rpz_enabled" VALUE="false"/>
        <PROPERTY NAME="resolver_query_timeout" VALUE="0"/>
        <PROPERTY NAME="use_auto_blackhole" VALUE="false"/>
        <PROPERTY NAME="use_rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="dns_over_lan" VALUE="true"/>
        <PROPERTY NAME="log_idns_gslb" VALUE="false"/>
        <PROPERTY NAME="log_idns_health" VALUE="false"/>
        <PROPERTY NAME="idns_health_source" VALUE="VIP"/>
        <PROPERTY NAME="max_recursion_depth" VALUE="7"/>
        <PROPERTY NAME="max_recursion_queries" VALUE="150"/>
        <PROPERTY NAME="virtual_node" VALUE="1"/>
        <PROPERTY NAME="use_gss_tsig_keys" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PARTITION-MAP VALUE="020+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_properties"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="limit_recursive_clients" VALUE="false"/>
        <PROPERTY NAME="numberof_recursive_clients" VALUE="1000"/>
        <PROPERTY NAME="override_cluster_sortlist" VALUE="false"/>
        <PROPERTY NAME="override_cluster_transfer_list" VALUE="false"/>
        <PROPERTY NAME="override_cluster_transfer_format" VALUE="false"/>
        <PROPERTY NAME="override_cluster_qacl" VALUE="false"/>
        <PROPERTY NAME="override_cluster_rqacl" VALUE="false"/>
        <PROPERTY NAME="override_cluster_ddns_updaters" VALUE="false"/>
        <PROPERTY NAME="override_cluster_forwarders" VALUE="false"/>
        <PROPERTY NAME="recursion_enabled" VALUE="false"/>
        <PROPERTY NAME="lame_ttl" VALUE="600"/>
        <PROPERTY NAME="use_lame_ttl" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
        <PROPERTY NAME="minimal_response" VALUE="true"/>
        <PROPERTY NAME="override_cluster_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="override_cluster_facility" VALUE="false"/>
        <PROPERTY NAME="facility" VALUE="daemon"/>
        <PROPERTY NAME="override_cluster_logging" VALUE="false"/>
        <PROPERTY NAME="log_general" VALUE="true"/>
        <PROPERTY NAME="log_client" VALUE="true"/>
        <PROPERTY NAME="log_config" VALUE="true"/>
        <PROPERTY NAME="log_database" VALUE="true"/>
        <PROPERTY NAME="log_dnssec" VALUE="true"/>
        <PROPERTY NAME="log_lame_servers" VALUE="true"/>
        <PROPERTY NAME="log_network" VALUE="true"/>
        <PROPERTY NAME="log_notify" VALUE="true"/>
        <PROPERTY NAME="log_queries" VALUE="false"/>
        <PROPERTY NAME="log_resolver" VALUE="true"/>
        <PROPERTY NAME="log_security" VALUE="true"/>
        <PROPERTY NAME="log_update" VALUE="true"/>
        <PROPERTY NAME="log_xfer_in" VALUE="true"/>
        <PROPERTY NAME="log_xfer_out" VALUE="true"/>
        <PROPERTY NAME="log_update_security" VALUE="true"/>
        <PROPERTY NAME="log_rpz" VALUE="false"/>
        <PROPERTY NAME="log_responses" VALUE="false"/>
        <PROPERTY NAME="log_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="auto_sort_views" VALUE="false"/>
        <PROPERTY NAME="use_nat_address" VALUE="0"/>
        <PROPERTY NAME="dns_over_mgmt" VALUE="false"/>
        <PROPERTY NAME="notify_xfr_source" VALUE="VIP"/>
        <PROPERTY NAME="query_source" VALUE="VIP"/>
        <PROPERTY NAME="dns_over_lan2" VALUE="false"/>
        <PROPERTY NAME="auto_create_a_and_ptr_for_lan2" VALUE="true"/>
        <PROPERTY NAME="use_enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="override_cluster_notify_query_sport" VALUE="false"/>
        <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="named_worker_threads" VALUE="0"/>
        <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
        <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="override_dnssec" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
        <PROPERTY NAME="override_blackhole" VALUE="false"/>
        <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
        <PROPERTY NAME="gsstsig_key_expiration_time" VALUE="3600"/>
        <PROPERTY NAME="override_transfers_out" VALUE="false"/>
        <PROPERTY NAME="transfers_out" VALUE="10"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="nxdomain_redirect_override" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="nxdomain_log_query" VALUE="false"/>
        <PROPERTY NAME="blacklist_override" VALUE="false"/>
        <PROPERTY NAME="blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="blacklist_action" VALUE="REDIRECT"/>
        <PROPERTY NAME="blacklist_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="blacklist_log_query" VALUE="false"/>
        <PROPERTY NAME="always_ret_nxdomain_for_fmz_ptr" VALUE="false"/>
        <PROPERTY NAME="enable_dns64" VALUE="false"/>
        <PROPERTY NAME="use_dns64" VALUE="false"/>
        <PROPERTY NAME="override_dns_cache_ttl" VALUE="false"/>
        <PROPERTY NAME="dns_cache_ttl" VALUE="1"/>
        <PROPERTY NAME="enable_dns_cache_acceleration" VALUE="false"/>
        <PROPERTY NAME="dns_over_v6_lan2" VALUE="false"/>
        <PROPERTY NAME="dns_over_v6_mgmt" VALUE="false"/>
        <PROPERTY NAME="v6_auto_create_lan2_glue" VALUE="false"/>
        <PROPERTY NAME="override_filter_aaaa" VALUE="false"/>
        <PROPERTY NAME="filter_aaaa" VALUE="NO"/>
        <PROPERTY NAME="dns_over_v6_lan" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="transfers_in" VALUE="10"/>
        <PROPERTY NAME="use_transfers_in" VALUE="false"/>
        <PROPERTY NAME="transfers_per_ns" VALUE="2"/>
        <PROPERTY NAME="use_transfers_per_ns" VALUE="false"/>
        <PROPERTY NAME="serial_query_rate" VALUE="20"/>
        <PROPERTY NAME="use_serial_query_rate" VALUE="false"/>
        <PROPERTY NAME="override_max_cached_lifetime" VALUE="false"/>
        <PROPERTY NAME="max_cached_lifetime" VALUE="86400"/>
        <PROPERTY NAME="use_max_cache_ttl" VALUE="false"/>
        <PROPERTY NAME="max_cache_ttl" VALUE="604800"/>
        <PROPERTY NAME="use_max_ncache_ttl" VALUE="false"/>
        <PROPERTY NAME="max_ncache_ttl" VALUE="10800"/>
        <PROPERTY NAME="use_disable_edns" VALUE="false"/>
        <PROPERTY NAME="disable_edns" VALUE="false"/>
        <PROPERTY NAME="use_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="query_rewrite_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nxdomain_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_rpz_enabled" VALUE="false"/>
        <PROPERTY NAME="resolver_query_timeout" VALUE="0"/>
        <PROPERTY NAME="use_auto_blackhole" VALUE="false"/>
        <PROPERTY NAME="use_rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="dns_over_lan" VALUE="true"/>
        <PROPERTY NAME="log_idns_gslb" VALUE="false"/>
        <PROPERTY NAME="log_idns_health" VALUE="false"/>
        <PROPERTY NAME="idns_health_source" VALUE="VIP"/>
        <PROPERTY NAME="max_recursion_depth" VALUE="7"/>
        <PROPERTY NAME="max_recursion_queries" VALUE="150"/>
        <PROPERTY NAME="virtual_node" VALUE="2"/>
        <PROPERTY NAME="use_gss_tsig_keys" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PARTITION-MAP VALUE="040+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_properties"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="limit_recursive_clients" VALUE="false"/>
        <PROPERTY NAME="numberof_recursive_clients" VALUE="1000"/>
        <PROPERTY NAME="override_cluster_sortlist" VALUE="false"/>
        <PROPERTY NAME="override_cluster_transfer_list" VALUE="false"/>
        <PROPERTY NAME="override_cluster_transfer_format" VALUE="false"/>
        <PROPERTY NAME="override_cluster_qacl" VALUE="false"/>
        <PROPERTY NAME="override_cluster_rqacl" VALUE="false"/>
        <PROPERTY NAME="override_cluster_ddns_updaters" VALUE="false"/>
        <PROPERTY NAME="override_cluster_forwarders" VALUE="false"/>
        <PROPERTY NAME="recursion_enabled" VALUE="false"/>
        <PROPERTY NAME="lame_ttl" VALUE="600"/>
        <PROPERTY NAME="use_lame_ttl" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
        <PROPERTY NAME="minimal_response" VALUE="true"/>
        <PROPERTY NAME="override_cluster_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="override_cluster_facility" VALUE="false"/>
        <PROPERTY NAME="facility" VALUE="daemon"/>
        <PROPERTY NAME="override_cluster_logging" VALUE="false"/>
        <PROPERTY NAME="log_general" VALUE="true"/>
        <PROPERTY NAME="log_client" VALUE="true"/>
        <PROPERTY NAME="log_config" VALUE="true"/>
        <PROPERTY NAME="log_database" VALUE="true"/>
        <PROPERTY NAME="log_dnssec" VALUE="true"/>
        <PROPERTY NAME="log_lame_servers" VALUE="true"/>
        <PROPERTY NAME="log_network" VALUE="true"/>
        <PROPERTY NAME="log_notify" VALUE="true"/>
        <PROPERTY NAME="log_queries" VALUE="false"/>
        <PROPERTY NAME="log_resolver" VALUE="true"/>
        <PROPERTY NAME="log_security" VALUE="true"/>
        <PROPERTY NAME="log_update" VALUE="true"/>
        <PROPERTY NAME="log_xfer_in" VALUE="true"/>
        <PROPERTY NAME="log_xfer_out" VALUE="true"/>
        <PROPERTY NAME="log_update_security" VALUE="true"/>
        <PROPERTY NAME="log_rpz" VALUE="false"/>
        <PROPERTY NAME="log_responses" VALUE="false"/>
        <PROPERTY NAME="log_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="auto_sort_views" VALUE="false"/>
        <PROPERTY NAME="use_nat_address" VALUE="0"/>
        <PROPERTY NAME="dns_over_mgmt" VALUE="false"/>
        <PROPERTY NAME="notify_xfr_source" VALUE="VIP"/>
        <PROPERTY NAME="query_source" VALUE="VIP"/>
        <PROPERTY NAME="dns_over_lan2" VALUE="false"/>
        <PROPERTY NAME="auto_create_a_and_ptr_for_lan2" VALUE="true"/>
        <PROPERTY NAME="use_enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="override_cluster_notify_query_sport" VALUE="false"/>
        <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="named_worker_threads" VALUE="0"/>
        <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
        <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="override_dnssec" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
        <PROPERTY NAME="override_blackhole" VALUE="false"/>
        <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
        <PROPERTY NAME="gsstsig_key_expiration_time" VALUE="3600"/>
        <PROPERTY NAME="override_transfers_out" VALUE="false"/>
        <PROPERTY NAME="transfers_out" VALUE="10"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="nxdomain_redirect_override" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="nxdomain_log_query" VALUE="false"/>
        <PROPERTY NAME="blacklist_override" VALUE="false"/>
        <PROPERTY NAME="blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="blacklist_action" VALUE="REDIRECT"/>
        <PROPERTY NAME="blacklist_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="blacklist_log_query" VALUE="false"/>
        <PROPERTY NAME="always_ret_nxdomain_for_fmz_ptr" VALUE="false"/>
        <PROPERTY NAME="enable_dns64" VALUE="false"/>
        <PROPERTY NAME="use_dns64" VALUE="false"/>
        <PROPERTY NAME="override_dns_cache_ttl" VALUE="false"/>
        <PROPERTY NAME="dns_cache_ttl" VALUE="1"/>
        <PROPERTY NAME="enable_dns_cache_acceleration" VALUE="false"/>
        <PROPERTY NAME="dns_over_v6_lan2" VALUE="false"/>
        <PROPERTY NAME="dns_over_v6_mgmt" VALUE="false"/>
        <PROPERTY NAME="v6_auto_create_lan2_glue" VALUE="false"/>
        <PROPERTY NAME="override_filter_aaaa" VALUE="false"/>
        <PROPERTY NAME="filter_aaaa" VALUE="NO"/>
        <PROPERTY NAME="dns_over_v6_lan" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="transfers_in" VALUE="10"/>
        <PROPERTY NAME="use_transfers_in" VALUE="false"/>
        <PROPERTY NAME="transfers_per_ns" VALUE="2"/>
        <PROPERTY NAME="use_transfers_per_ns" VALUE="false"/>
        <PROPERTY NAME="serial_query_rate" VALUE="20"/>
        <PROPERTY NAME="use_serial_query_rate" VALUE="false"/>
        <PROPERTY NAME="override_max_cached_lifetime" VALUE="false"/>
        <PROPERTY NAME="max_cached_lifetime" VALUE="86400"/>
        <PROPERTY NAME="use_max_cache_ttl" VALUE="false"/>
        <PROPERTY NAME="max_cache_ttl" VALUE="604800"/>
        <PROPERTY NAME="use_max_ncache_ttl" VALUE="false"/>
        <PROPERTY NAME="max_ncache_ttl" VALUE="10800"/>
        <PROPERTY NAME="use_disable_edns" VALUE="false"/>
        <PROPERTY NAME="disable_edns" VALUE="false"/>
        <PROPERTY NAME="use_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="query_rewrite_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nxdomain_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_rpz_enabled" VALUE="false"/>
        <PROPERTY NAME="resolver_query_timeout" VALUE="0"/>
        <PROPERTY NAME="use_auto_blackhole" VALUE="false"/>
        <PROPERTY NAME="use_rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="dns_over_lan" VALUE="true"/>
        <PROPERTY NAME="log_idns_gslb" VALUE="false"/>
        <PROPERTY NAME="log_idns_health" VALUE="false"/>
        <PROPERTY NAME="idns_health_source" VALUE="VIP"/>
        <PROPERTY NAME="max_recursion_depth" VALUE="7"/>
        <PROPERTY NAME="max_recursion_queries" VALUE="150"/>
        <PROPERTY NAME="virtual_node" VALUE="4"/>
        <PROPERTY NAME="use_gss_tsig_keys" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_properties"/>
        <PROPERTY NAME="forwarders_only" VALUE="false"/>
        <PROPERTY NAME="limit_recursive_clients" VALUE="false"/>
        <PROPERTY NAME="numberof_recursive_clients" VALUE="1000"/>
        <PROPERTY NAME="override_cluster_sortlist" VALUE="false"/>
        <PROPERTY NAME="override_cluster_transfer_list" VALUE="false"/>
        <PROPERTY NAME="override_cluster_transfer_format" VALUE="false"/>
        <PROPERTY NAME="override_cluster_qacl" VALUE="false"/>
        <PROPERTY NAME="override_cluster_rqacl" VALUE="false"/>
        <PROPERTY NAME="override_cluster_ddns_updaters" VALUE="false"/>
        <PROPERTY NAME="override_cluster_forwarders" VALUE="false"/>
        <PROPERTY NAME="recursion_enabled" VALUE="false"/>
        <PROPERTY NAME="lame_ttl" VALUE="600"/>
        <PROPERTY NAME="use_lame_ttl" VALUE="false"/>
        <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
        <PROPERTY NAME="minimal_response" VALUE="true"/>
        <PROPERTY NAME="override_cluster_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
        <PROPERTY NAME="override_cluster_facility" VALUE="false"/>
        <PROPERTY NAME="facility" VALUE="daemon"/>
        <PROPERTY NAME="override_cluster_logging" VALUE="false"/>
        <PROPERTY NAME="log_general" VALUE="true"/>
        <PROPERTY NAME="log_client" VALUE="true"/>
        <PROPERTY NAME="log_config" VALUE="true"/>
        <PROPERTY NAME="log_database" VALUE="true"/>
        <PROPERTY NAME="log_dnssec" VALUE="true"/>
        <PROPERTY NAME="log_lame_servers" VALUE="true"/>
        <PROPERTY NAME="log_network" VALUE="true"/>
        <PROPERTY NAME="log_notify" VALUE="true"/>
        <PROPERTY NAME="log_queries" VALUE="false"/>
        <PROPERTY NAME="log_resolver" VALUE="true"/>
        <PROPERTY NAME="log_security" VALUE="true"/>
        <PROPERTY NAME="log_update" VALUE="true"/>
        <PROPERTY NAME="log_xfer_in" VALUE="true"/>
        <PROPERTY NAME="log_xfer_out" VALUE="true"/>
        <PROPERTY NAME="log_update_security" VALUE="true"/>
        <PROPERTY NAME="log_rpz" VALUE="false"/>
        <PROPERTY NAME="log_responses" VALUE="false"/>
        <PROPERTY NAME="log_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="auto_sort_views" VALUE="false"/>
        <PROPERTY NAME="use_nat_address" VALUE="0"/>
        <PROPERTY NAME="dns_over_mgmt" VALUE="false"/>
        <PROPERTY NAME="notify_xfr_source" VALUE="VIP"/>
        <PROPERTY NAME="query_source" VALUE="VIP"/>
        <PROPERTY NAME="dns_over_lan2" VALUE="false"/>
        <PROPERTY NAME="auto_create_a_and_ptr_for_lan2" VALUE="true"/>
        <PROPERTY NAME="use_enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
        <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
        <PROPERTY NAME="override_cluster_notify_query_sport" VALUE="false"/>
        <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
        <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
        <PROPERTY NAME="named_worker_threads" VALUE="0"/>
        <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
        <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
        <PROPERTY NAME="check_names_policy" VALUE="1"/>
        <PROPERTY NAME="override_dnssec" VALUE="false"/>
        <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
        <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
        <PROPERTY NAME="override_blackhole" VALUE="false"/>
        <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
        <PROPERTY NAME="gsstsig_key_expiration_time" VALUE="3600"/>
        <PROPERTY NAME="override_transfers_out" VALUE="false"/>
        <PROPERTY NAME="transfers_out" VALUE="10"/>
        <PROPERTY NAME="override_notify_delay" VALUE="false"/>
        <PROPERTY NAME="notify_delay" VALUE="5"/>
        <PROPERTY NAME="nxdomain_redirect_override" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
        <PROPERTY NAME="nxdomain_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="nxdomain_log_query" VALUE="false"/>
        <PROPERTY NAME="blacklist_override" VALUE="false"/>
        <PROPERTY NAME="blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="blacklist_action" VALUE="REDIRECT"/>
        <PROPERTY NAME="blacklist_redirect_ttl" VALUE="60"/>
        <PROPERTY NAME="blacklist_log_query" VALUE="false"/>
        <PROPERTY NAME="always_ret_nxdomain_for_fmz_ptr" VALUE="false"/>
        <PROPERTY NAME="enable_dns64" VALUE="false"/>
        <PROPERTY NAME="use_dns64" VALUE="false"/>
        <PROPERTY NAME="override_dns_cache_ttl" VALUE="false"/>
        <PROPERTY NAME="dns_cache_ttl" VALUE="1"/>
        <PROPERTY NAME="enable_dns_cache_acceleration" VALUE="false"/>
        <PROPERTY NAME="dns_over_v6_lan2" VALUE="false"/>
        <PROPERTY NAME="dns_over_v6_mgmt" VALUE="false"/>
        <PROPERTY NAME="v6_auto_create_lan2_glue" VALUE="false"/>
        <PROPERTY NAME="override_filter_aaaa" VALUE="false"/>
        <PROPERTY NAME="filter_aaaa" VALUE="NO"/>
        <PROPERTY NAME="dns_over_v6_lan" VALUE="false"/>
        <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
        <PROPERTY NAME="transfers_in" VALUE="10"/>
        <PROPERTY NAME="use_transfers_in" VALUE="false"/>
        <PROPERTY NAME="transfers_per_ns" VALUE="2"/>
        <PROPERTY NAME="use_transfers_per_ns" VALUE="false"/>
        <PROPERTY NAME="serial_query_rate" VALUE="20"/>
        <PROPERTY NAME="use_serial_query_rate" VALUE="false"/>
        <PROPERTY NAME="override_max_cached_lifetime" VALUE="false"/>
        <PROPERTY NAME="max_cached_lifetime" VALUE="86400"/>
        <PROPERTY NAME="use_max_cache_ttl" VALUE="false"/>
        <PROPERTY NAME="max_cache_ttl" VALUE="604800"/>
        <PROPERTY NAME="use_max_ncache_ttl" VALUE="false"/>
        <PROPERTY NAME="max_ncache_ttl" VALUE="10800"/>
        <PROPERTY NAME="use_disable_edns" VALUE="false"/>
        <PROPERTY NAME="disable_edns" VALUE="false"/>
        <PROPERTY NAME="use_query_rewrite" VALUE="false"/>
        <PROPERTY NAME="query_rewrite_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_blacklist_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_nxdomain_enabled" VALUE="false"/>
        <PROPERTY NAME="dnssec_rpz_enabled" VALUE="false"/>
        <PROPERTY NAME="resolver_query_timeout" VALUE="0"/>
        <PROPERTY NAME="use_auto_blackhole" VALUE="false"/>
        <PROPERTY NAME="use_rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="rpz_disable_nsdname_nsip" VALUE="false"/>
        <PROPERTY NAME="dns_over_lan" VALUE="true"/>
        <PROPERTY NAME="log_idns_gslb" VALUE="false"/>
        <PROPERTY NAME="log_idns_health" VALUE="false"/>
        <PROPERTY NAME="idns_health_source" VALUE="VIP"/>
        <PROPERTY NAME="max_recursion_depth" VALUE="7"/>
        <PROPERTY NAME="max_recursion_queries" VALUE="150"/>
        <PROPERTY NAME="virtual_node" VALUE="5"/>
        <PROPERTY NAME="use_gss_tsig_keys" VALUE="false"/>
        <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
        <PARTITION-MAP VALUE="20+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_restart"/>
        <PROPERTY NAME="service_enabled" VALUE="false"/>
        <PROPERTY NAME="force_restart_service" VALUE="false"/>
        <PROPERTY NAME="restart_service_status" VALUE="No-request"/>
        <PROPERTY NAME="restart_in_0" VALUE="-1"/>
        <PROPERTY NAME="restart_in_1" VALUE="-1"/>
        <PROPERTY NAME="automatic_restart_needed" VALUE="false"/>
        <PROPERTY NAME="is_scheduled" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="0"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_restart"/>
        <PROPERTY NAME="service_enabled" VALUE="false"/>
        <PROPERTY NAME="force_restart_service" VALUE="false"/>
        <PROPERTY NAME="restart_service_status" VALUE="No-request"/>
        <PROPERTY NAME="restart_in_0" VALUE="-1"/>
        <PROPERTY NAME="restart_in_1" VALUE="-1"/>
        <PROPERTY NAME="automatic_restart_needed" VALUE="false"/>
        <PROPERTY NAME="is_scheduled" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="1"/>
        <PARTITION-MAP VALUE="020+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_restart"/>
        <PROPERTY NAME="service_enabled" VALUE="false"/>
        <PROPERTY NAME="force_restart_service" VALUE="false"/>
        <PROPERTY NAME="restart_service_status" VALUE="No-request"/>
        <PROPERTY NAME="restart_in_0" VALUE="-1"/>
        <PROPERTY NAME="restart_in_1" VALUE="-1"/>
        <PROPERTY NAME="automatic_restart_needed" VALUE="false"/>
        <PROPERTY NAME="is_scheduled" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="2"/>
        <PARTITION-MAP VALUE="040+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_restart"/>
        <PROPERTY NAME="service_enabled" VALUE="true"/>
        <PROPERTY NAME="force_restart_service" VALUE="false"/>
        <PROPERTY NAME="restart_service_status" VALUE="No-request"/>
        <PROPERTY NAME="restart_in_0" VALUE="-1"/>
        <PROPERTY NAME="restart_in_1" VALUE="0"/>
        <PROPERTY NAME="automatic_restart_needed" VALUE="false"/>
        <PROPERTY NAME="is_scheduled" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="4"/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_restart"/>
        <PROPERTY NAME="service_enabled" VALUE="true"/>
        <PROPERTY NAME="force_restart_service" VALUE="false"/>
        <PROPERTY NAME="restart_service_status" VALUE="No-request"/>
        <PROPERTY NAME="restart_in_0" VALUE="-1"/>
        <PROPERTY NAME="restart_in_1" VALUE="0"/>
        <PROPERTY NAME="automatic_restart_needed" VALUE="false"/>
        <PROPERTY NAME="is_scheduled" VALUE="false"/>
        <PROPERTY NAME="parent" VALUE="5"/>
        <PARTITION-MAP VALUE="20+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl" VALUE="3600"/>
        <PROPERTY NAME="ttl_option" VALUE="1"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="true"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.127.0.0"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="cluster"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.cluster"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="f+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.nsg"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="b.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.b"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="nsg"/>
        <PROPERTY NAME="dname" VALUE="b.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.b"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="nsg"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.nsg"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="d.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.d"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="nsg"/>
        <PROPERTY NAME="dname" VALUE="d.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.d"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="nsg"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.nsg"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="ext555.outer.space"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.space.outer.ext555"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="nsg"/>
        <PROPERTY NAME="dname" VALUE="ext555.outer.space"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.space.outer.ext555"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="nsg"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.nsg"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.c"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="nsg"/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.c"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="nsg"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.c"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="direct"/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.c"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="direct"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="ext555.outer.space"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.space.outer.ext555"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="direct"/>
        <PROPERTY NAME="dname" VALUE="ext555.outer.space"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.space.outer.ext555"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="direct"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="d.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.d"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="direct"/>
        <PROPERTY NAME="dname" VALUE="d.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.d"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="direct"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="sec"/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.c"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="sec"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="sec"/>
        <PROPERTY NAME="dname" VALUE="ext200.outer.space"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.space.outer.ext200"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="sec"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="fwd"/>
        <PROPERTY NAME="dname" VALUE="ext300.outer.space"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.space.outer.ext300"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="fwd"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.ms"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="d.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.d"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="ms"/>
        <PROPERTY NAME="dname" VALUE="d.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.d"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="ms"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.ms"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="w8r264-12.inca.infoblox.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.infoblox.inca.w8r264-12"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="ms"/>
        <PROPERTY NAME="dname" VALUE="w8r264-12.inca.infoblox.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.infoblox.inca.w8r264-12"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="ms"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.ms"/>
        <PROPERTY NAME="name" VALUE="fwd"/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.c"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add NS"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="fwd"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="ms"/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.c"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add NS"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="ms"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="manual.top.zn"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.zn.top.manual"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add NS"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="b.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.b"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add NS"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="direct"/>
        <PROPERTY NAME="dname" VALUE="b.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.b"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add NS"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="direct"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="deleg"/>
        <PROPERTY NAME="dname" VALUE="a.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.a"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="deleg"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="deleg"/>
        <PROPERTY NAME="dname" VALUE="ns5.deleg.top.zn"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.zn.top.deleg.ns5"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="deleg"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top"/>
        <PROPERTY NAME="name" VALUE="deleg"/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.c"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="deleg"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct.mid.btm"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="d.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.d"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="name" VALUE="mid.btm"/>
        <PROPERTY NAME="dname" VALUE="d.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.d"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="mid.btm"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct.mid.btm"/>
        <PROPERTY NAME="name" VALUE=""/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.c"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE=""/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_ns"/>
        <PROPERTY NAME="ttl_option" VALUE="0"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="created_by_bind" VALUE="false"/>
        <PROPERTY NAME="auto_created" VALUE="true"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.direct"/>
        <PROPERTY NAME="name" VALUE="mid.btm"/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PROPERTY NAME="reversed_dname" VALUE="._default.com.top.c"/>
        <PROPERTY NAME="comment" VALUE="Auto-created by Add Zone"/>
        <PROPERTY NAME="refcount" VALUE="1"/>
        <PROPERTY NAME="display_name" VALUE="mid.btm"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ns_group"/>
        <PROPERTY NAME="is_external_primary" VALUE="false"/>
        <PROPERTY NAME="ms_ad_integrated" VALUE="false"/>
        <PROPERTY NAME="is_multimaster" VALUE="false"/>
        <PROPERTY NAME="cluster_dns_properties" VALUE="0"/>
        <PROPERTY NAME="group_name" VALUE="authgroup01"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ns_group_ext_secondary_server"/>
        <PROPERTY NAME="tsig_option" VALUE="false"/>
        <PROPERTY NAME="x_tsig_option" VALUE="false"/>
        <PROPERTY NAME="ext_sec_stealth" VALUE="false"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="ns_group" VALUE="authgroup01"/>
        <PROPERTY NAME="ext_sec_address" VALUE="***********"/>
        <PROPERTY NAME="ext_sec_name" VALUE="ext555.outer.space"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ns_group_grid_primary"/>
        <PROPERTY NAME="stealth" VALUE="false"/>
        <PROPERTY NAME="ns_group" VALUE="authgroup01"/>
        <PROPERTY NAME="grid_member" VALUE="4"/>
        <PARTITION-MAP VALUE="10+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ns_group_secondary_server"/>
        <PROPERTY NAME="cluster_sec_stealth" VALUE="false"/>
        <PROPERTY NAME="cluster_sec_lead" VALUE="false"/>
        <PROPERTY NAME="cluster_sec_zone_transfer" VALUE="0"/>
        <PROPERTY NAME="enable_preferred_primaries" VALUE="false"/>
        <PROPERTY NAME="ns_group" VALUE="authgroup01"/>
        <PROPERTY NAME="member_address" VALUE="5"/>
        <PARTITION-MAP VALUE="30+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ms_server"/>
        <PROPERTY NAME="disabled" VALUE="false"/>
        <PROPERTY NAME="read_only" VALUE="false"/>
        <PROPERTY NAME="synchronization_min_delay" VALUE="2"/>
        <PROPERTY NAME="log_level" VALUE="NORMAL"/>
        <PROPERTY NAME="override_cluster_ms_rpc_timeout" VALUE="false"/>
        <PROPERTY NAME="ms_rpc_timeout" VALUE="10000"/>
        <PROPERTY NAME="override_ms_max_connection" VALUE="false"/>
        <PROPERTY NAME="ms_max_connection" VALUE="5"/>
        <PROPERTY NAME="override_log_destination" VALUE="false"/>
        <PROPERTY NAME="log_destination" VALUE="MSLOG"/>
        <PROPERTY NAME="parent" VALUE="0"/>
        <PROPERTY NAME="ms_oid" VALUE="0"/>
        <PROPERTY NAME="address" VALUE="w8r264-12.inca.infoblox.com"/>
        <PROPERTY NAME="login_name" VALUE="frtest"/>
        <PROPERTY NAME="login_password_db" VALUE="{0}_{aes}_EAAAALis0De8nmz/DfrHxwU2jwc="/>
        <PROPERTY NAME="grid_member" VALUE="0"/>
        <PROPERTY NAME="network_view" VALUE="0"/>
        <PROPERTY NAME="dns_view" VALUE="._default"/>
        <PROPERTY NAME="resolved_name" VALUE="w8r264-12.inca.infoblox.com"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.ms_server_parent"/>
        <PROPERTY NAME="cluster" VALUE="0"/>
        <PARTITION-MAP VALUE="0+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ms_server_dns_properties"/>
        <PROPERTY NAME="managed" VALUE="true"/>
        <PROPERTY NAME="last_sync_time" VALUE="1426777844"/>
        <PROPERTY NAME="last_sync_status" VALUE="ERROR"/>
        <PROPERTY NAME="sync_retry_offset" VALUE="0"/>
        <PROPERTY NAME="synchronization_id" VALUE="0"/>
        <PROPERTY NAME="connection_type" VALUE="undefined"/>
        <PROPERTY NAME="override_enable_dns_monitoring" VALUE="false"/>
        <PROPERTY NAME="enable_monitoring" VALUE="true"/>
        <PROPERTY NAME="parent" VALUE="0"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.ms_zone_delegation"/>
        <PROPERTY NAME="zone" VALUE="._default.zn.top.ms"/>
        <PROPERTY NAME="name" VALUE="fwd"/>
        <PROPERTY NAME="address" VALUE="************"/>
        <PROPERTY NAME="dname" VALUE="c.top.com"/>
        <PARTITION-MAP VALUE="010+"/>
    </OBJECT>
</DATABASE>
