<test_data>

    <!-- ACTIVE:ACTIVE -->

    <!-- Same ends, primary will win. -->
    <lease ip="127.0.0.1">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:00:00" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
    </lease>
    <!-- Same ends, primary will win, shouldn't be updated, check ack_state for primary didn't changed. -->
    <lease ip="*********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:00:00" ack_state="renew"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
    </lease>
    <!-- Primary has latest ends, and thus should win. -->
    <lease ip="*********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00"  ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00"  ack_state="from_peer"/>
    </lease>
    <!-- Secondary has latest ends, and thus should win. -->
    <lease ip="*********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:20:00" ack_state="bind"/>
    </lease>
    <!-- Both leases are infinite, primary should win. -->
    <lease ip="*********">
        <primary binding_state="ACTIVE" _ends="never;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="never;" ack_state="from_peer"/>
    </lease>
    <!-- Primary lease is infinite, and thus should win. -->
    <lease ip="*********">
        <primary binding_state="ACTIVE" _ends="never;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="never;" ack_state="from_peer"/>
    </lease>
    <!-- Secondary lease is infinite, and thus should win. -->
    <lease ip="*********">
        <primary binding_state="ACTIVE" _ends="never;" ack_state="from_peer"/>
        <secondary binding_state="ACTIVE" _ends="never;" ack_state="bind"/>
    </lease>
    <!-- Both leases have applied DNS data, primary has latest ends and thus should win. Remove DNS data from secondary. -->
    <lease ip="*********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" variable="lt=&quot;4300&quot;" ack_state="from_peer"/>
    </lease>
    <!-- Both leases have applied DNS data, secondary has latest ends and thus should win. Remove DNS data from primary. -->
    <lease ip="*********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:20:00" variable="lt=&quot;4200&quot;" ack_state="from_peer"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:20:00" variable="ddns-client-fqdn=&quot;some1.name&quot; ddns-rev-name=&quot;rev1.data&quot; lt=&quot;4300&quot;" ack_state="bind"/>
    </lease>
    <!-- Primary leases has applied DNS data and latest ends and thus should win. -->
    <lease ip="127.0.0.10">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" variable="lt=&quot;4300&quot;" ack_state="from_peer"/>
    </lease>
    <!-- Secondary lease has applied DNS data and latest ends and thus should win. -->
    <lease ip="127.0.0.11">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:20:00" variable="lt=&quot;4300&quot;" ack_state="from_peer"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:20:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;" ack_state="bind"/>
    </lease>
    <!-- Secondary lease has applied DNS data, primary lease has latest ends and thus should win (retain DNS data for secondary). -->
    <lease ip="127.0.0.12">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" variable="lt=&quot;4300&quot;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;" ack_state="from_peer"/>
    </lease>
    <!-- Primary lease has applied DNS data, secondary lease has latest ends and thus should win (retain DNS data for primary). -->
    <lease ip="127.0.0.13">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:20:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;" ack_state="from_peer"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:20:00" variable="lt=&quot;4300&quot;" ack_state="bind"/>
    </lease>

    <!-- In next 9 cases primary has latest ends and thus should win.
         Will test different combinations of deferred data in leases (ADD mean deferred adding update, REMOVE means deferred deletion update):
            - primary ADD, secondary ADD;
            - primary ADD + REMOVE, secondary ADD;
            - primary REMOVE, secondary ADD;
            - primary ADD, secondary REMOVE;
            - primary ADD, secondary ADD + REMOVE;
            - primary ADD + REMOVE, secondary ADD + REMOVE;
            - primary REMOVE, secondary REMOVE;
            - primary ADD + REMOVE, secondary REMOVE;
            - primary REMOVE, secondary ADD + REMOVE;
        In all above cases updates should retain for one server (prefer winner).
        Please note that 'deferred_fwd_name' and 'deferred_txt' are processed in pair.
        Will remove 'deferred_ttl' for lease if no updates left for it.
    -->
    <lease ip="127.0.0.14">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="127.0.0.15">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00"  deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="127.0.0.16">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_ttl="102" deferred_rev_name="ADD name1.some" ack_state="from_peer"/>
    </lease>
    <lease ip="127.0.0.17">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="127.0.0.18">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="127.0.0.19">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_rev_name="ADD name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="*********0">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="*********1">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="*********2">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_rev_name="ADD name1.some" deferred_ttl="102" ack_state="from_peer"/>
    </lease>

    <!-- ACTIVE:ABANDONED -->

    <!-- Same ends, primary will win. -->
    <lease ip="*********3">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:00:00" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
    </lease>
    <!-- Same ends, primary will win, shouldn't be updated, check ack_state for primary didn't changed. -->
    <lease ip="*********4">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:00:00" ack_state="renew"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
    </lease>
    <!-- Primary has latest ends, and thus should win. -->
    <lease ip="*********5">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00"  ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00"  ack_state="from_peer"/>
    </lease>
    <!-- Secondary has latest ends, and thus should win. -->
    <lease ip="*********6">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="bind"/>
    </lease>
    <!-- Both leases are infinite, primary should win. -->
    <lease ip="*********7">
        <primary binding_state="ACTIVE" _ends="never;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="never;" ack_state="from_peer"/>
    </lease>
    <!-- Primary lease is infinite, and thus should win. -->
    <lease ip="*********8">
        <primary binding_state="ACTIVE" _ends="never;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="never;" ack_state="from_peer"/>
    </lease>
    <!-- Secondary lease is infinite, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="never;" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="never;" ack_state="bind"/>
    </lease>

    <!-- In next cases active lease will win in all cases when it has applied DNS data. -->

    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" variable="lt=&quot;4300&quot;" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" variable="lt=&quot;4300&quot;" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 3015/12/07 10:00:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 3015/12/07 10:00:00" variable="lt=&quot;4300&quot;" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 3015/12/07 10:00:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 3015/12/07 10:00:00" variable="lt=&quot;4300&quot;" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 3015/12/07 10:00:00" variable="lt=&quot;4200&quot;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 3015/12/07 10:00:00" variable="ddns-client-fqdn=&quot;some1.name&quot; ddns-rev-name=&quot;rev1.data&quot; lt=&quot;4300&quot;" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 3015/12/07 10:00:00" variable="lt=&quot;4200&quot;" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 3015/12/07 10:00:00" variable="lt=&quot;4300&quot;" ack_state="from_peer"/>
    </lease>

    <!-- In next 9 cases active lease has deferred DNS data and thus should win.
         Will test different combinations of deferred data in leases (ADD mean deferred adding update, REMOVE means deferred deletion update):
            - primary ADD, secondary ADD;
            - primary ADD + REMOVE, secondary ADD;
            - primary REMOVE, secondary ADD;
            - primary ADD, secondary REMOVE;
            - primary ADD, secondary ADD + REMOVE;
            - primary ADD + REMOVE, secondary ADD + REMOVE;
            - primary REMOVE, secondary REMOVE;
            - primary ADD + REMOVE, secondary REMOVE;
            - primary REMOVE, secondary ADD + REMOVE;
        In all above cases updates should retain for one server (prefer winner).
        Please note that 'deferred_fwd_name' and 'deferred_txt' are processed in pair.
        Will remove 'deferred_ttl' for lease if no updates left for it.
    -->
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00"  deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_ttl="102" deferred_rev_name="ADD name1.some" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_rev_name="ADD name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="ACTIVE" _ends="1 2015/12/07 10:10:00" deferred_rev_name="ADD name1.some" deferred_ttl="102" ack_state="from_peer"/>
    </lease>

    <!-- In next cases inactive lease will win because of latest 'ends' time. -->

    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="bind"/>
    </lease>

    <!-- EXPIRED:RELEASED -->

    <!-- Same ends, primary will win. -->
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
    </lease>
    <!-- Same ends, primary will win, shouldn't be updated, check ack_state for primary didn't changed. -->
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00" ack_state="renew"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
    </lease>
    <!-- Primary has latest ends, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00"  ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00"  ack_state="from_peer"/>
    </lease>
    <!-- Secondary has latest ends, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="RELEASED" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="RELEASED" _ends="1 2015/12/07 10:20:00" ack_state="bind"/>
    </lease>
    <!-- Both leases are infinite, primary should win. -->
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="never;" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="never;" ack_state="from_peer"/>
    </lease>
    <!-- Primary lease is infinite, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="never;" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="never;" ack_state="from_peer"/>
    </lease>
    <!-- Secondary lease is infinite, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="RELEASED" _ends="never;" ack_state="from_peer"/>
        <secondary binding_state="RELEASED" _ends="never;" ack_state="bind"/>
    </lease>

    <!-- In next 9 cases active lease has latest ends and thus should win.
         Will test different combinations of deferred data in leases (ADD mean deferred adding update, REMOVE means deferred deletion update):
            - primary ADD, secondary ADD;
            - primary ADD + REMOVE, secondary ADD;
            - primary REMOVE, secondary ADD;
            - primary ADD, secondary REMOVE;
            - primary ADD, secondary ADD + REMOVE;
            - primary ADD + REMOVE, secondary ADD + REMOVE;
            - primary REMOVE, secondary REMOVE;
            - primary ADD + REMOVE, secondary REMOVE;
            - primary REMOVE, secondary ADD + REMOVE;
        In all above cases updates should retain for one server (prefer winner).
        Please note that 'deferred_fwd_name' and 'deferred_txt' are processed in pair.
        Will remove 'deferred_ttl' for lease if no updates left for it.
    -->
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>

    <!-- EXPIRED:EXPIRED -->
    <!-- Primary lease will always win. -->

    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00" ack_state="renew"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00"  ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00"  ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="never;" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="never;" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="never;" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="never;" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
    </lease>

    <!-- Will test different combinations of deferred data in leases (ADD mean deferred adding update, REMOVE means deferred deletion update):
            - primary ADD, secondary ADD;
            - primary ADD + REMOVE, secondary ADD;
            - primary REMOVE, secondary ADD;
            - primary ADD, secondary REMOVE;
            - primary ADD, secondary ADD + REMOVE;
            - primary ADD + REMOVE, secondary ADD + REMOVE;
            - primary REMOVE, secondary REMOVE;
            - primary ADD + REMOVE, secondary REMOVE;
            - primary REMOVE, secondary ADD + REMOVE;
        In all above cases updates should retain for one server (prefer winner).
        Please note that 'deferred_fwd_name' and 'deferred_txt' are processed in pair.
        Will remove 'deferred_ttl' for lease if no updates left for it.
    -->
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="bind"/>
        <secondary binding_state="EXPIRED" _ends="1 2015/12/07 10:10:00" ack_state="from_peer"/>
    </lease>

    <!-- EXPIRED:ABANDONED -->
    <!-- Secondary lease will always win. -->

    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="renew"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00"  ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00"  ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:20:00" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="never;" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="never;" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="never;" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="never;" ack_state="bind"/>
    </lease>

    <!-- Will test different combinations of deferred data in leases (ADD mean deferred adding update, REMOVE means deferred deletion update):
            - primary ADD, secondary ADD;
            - primary ADD + REMOVE, secondary ADD;
            - primary REMOVE, secondary ADD;
            - primary ADD, secondary REMOVE;
            - primary ADD, secondary ADD + REMOVE;
            - primary ADD + REMOVE, secondary ADD + REMOVE;
            - primary REMOVE, secondary REMOVE;
            - primary ADD + REMOVE, secondary REMOVE;
            - primary REMOVE, secondary ADD + REMOVE;
        In all above cases updates should retain for one server (prefer winner).
        Please note that 'deferred_fwd_name' and 'deferred_txt' are processed in pair.
        Will remove 'deferred_ttl' for lease if no updates left for it.
    -->
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_ttl="102" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102" ack_state="bind"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" ack_state="from_peer"/>
        <secondary binding_state="ABANDONED" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_ttl="102" ack_state="bind"/>
    </lease>

</test_data>
