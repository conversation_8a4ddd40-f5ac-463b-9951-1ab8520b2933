<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="name" VALUE="ea1"/>
  <PROPERTY NAME="allowed_object_types" VALUE="Network"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="uuid" VALUE="28490d9efb754b0fa2eca791bf2ff892"/>
  <PROPERTY NAME="group_flag" VALUE="false"/>
  <PROPERTY NAME="gog_revision" VALUE="12"/>
  <PROPERTY NAME="ea_namespace" VALUE="CLOUD"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="ea2"/>
  <PROPERTY NAME="flags" VALUE="CR"/>
  <PROPERTY NAME="allowed_object_types" VALUE="NetworkView,Network,DhcpRange,FixedAddress,IPv6Network,IPv6DhcpRange,IPv6FixedAddress,View,BaseZone,HostRecord,ResourceRecord"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="uuid" VALUE="28490d9efb754b0fa2eca791bf2ff893"/>
  <PROPERTY NAME="group_flag" VALUE="false"/>
  <PROPERTY NAME="gog_revision" VALUE="12"/>
  <PROPERTY NAME="ea_namespace" VALUE="CLOUD"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="ea3"/>
  <PROPERTY NAME="flags" VALUE="CR"/>
  <PROPERTY NAME="allowed_object_types" VALUE="DelegationNsGroup,NetworkView,Network,DhcpRange,FixedAddress,IPv6Network,IPv6DhcpRange,IPv6FixedAddress,View,BaseZone,HostRecord,ResourceRecord,NsGroup"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="uuid" VALUE="28490d9efb754b0fa2eca791bf2ff894"/>
  <PROPERTY NAME="group_flag" VALUE="false"/>
  <PROPERTY NAME="gog_revision" VALUE="12"/>
  <PROPERTY NAME="ea_namespace" VALUE="CLOUD"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="Tenant ID3"/>
  <PROPERTY NAME="flags" VALUE="CR"/>
  <PROPERTY NAME="allowed_object_types" VALUE="DelegationNsGroup"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="uuid" VALUE="45bfc1a5633f4ec1aaf523bcbf09fb3a"/>
  <PROPERTY NAME="gog_revision" VALUE="12"/>
  <PROPERTY NAME="group_flag" VALUE="false"/>
  <PROPERTY NAME="ea_namespace" VALUE="default"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="City"/>
  <PROPERTY NAME="allowed_object_types" VALUE="Network,IPv6Network,DelegationNsGroup"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_def"/>
  <PROPERTY NAME="attribute_type" VALUE="STRING"/>
  <PROPERTY NAME="uuid" VALUE="a08f07ea54e34ec5865384dc1bc04f0f"/>
  <PROPERTY NAME="gog_revision" VALUE="13"/>
  <PROPERTY NAME="group_flag" VALUE="false"/>
  <PROPERTY NAME="ea_namespace" VALUE="default"/>
  <PROPERTY NAME="sub_grid" VALUE="."/>
  <PROPERTY NAME="name" VALUE="Region"/>
  <PROPERTY NAME="allowed_object_types" VALUE="Network,IPv6Network,NsGroup"/>
</OBJECT>
</DATABASE>

