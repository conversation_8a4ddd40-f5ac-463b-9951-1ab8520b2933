# dhcpd conf file for ipam_ipv4_index.sh
local-address 127.0.0.1;
server-identifier 127.0.0.1;
ddns-update-style interim;
not authoritative;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
log-facility daemon;
ping-check false;

ddns-updates off;
ignore client-updates;

# Allocate leases from the loopback network
subnet ********* netmask ********* {
        pool {
                infoblox-range ********** **********;
                range ********** **********;
        }
}
