<DATABASE NAME="onedb" VERSION="MDXMLTEST">
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
    <PROPERTY NAME="ttl" VALUE="28000"/>
    <PROPERTY NAME="ttl_option" VALUE="1"/>
    <PROPERTY NAME="disabled" VALUE="false"/>
    <PROPERTY NAME="creation_timestamp" VALUE="1532598779"/>
    <PROPERTY NAME="creator" VALUE="DYNAMIC"/>
    <PROPERTY NAME="ddns_protected" VALUE="false"/>
    <PROPERTY NAME="__timestamp" VALUE="1532598779638649"/>
    <PROPERTY NAME="zone" VALUE="._default.com.infoblox"/>
    <PROPERTY NAME="name" VALUE="rp"/>
    <PROPERTY NAME="record_type" VALUE="RP"/>
    <PROPERTY NAME="record_rdata" VALUE="foo.example. bar.example."/>
    <PROPERTY NAME="record_rdata_hash" VALUE="0f5161dde88c77f82f975552c6a97dddae907987279e56908c450596fefccae467fe2e924e4b85732bb05eb273fa98469ec89d47b3fa3f24505d5575cbba59bd"/>
    <PROPERTY NAME="record_type_num" VALUE="17"/>
    <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
    <PROPERTY NAME="display_name" VALUE="rp"/>
  </OBJECT>
</DATABASE>
