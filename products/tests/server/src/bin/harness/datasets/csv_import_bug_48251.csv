header-hostrecord,fqdn*,_new_fqdn,addresses,aliases,boot_file,boot_server,broadcast_address,comment,configure_for_dhcp,configure_for_dns,deny_bootp,disabled,domain_name,domain_name_servers,ignore_dhcp_param_request_list,ipv6_addresses,lease_time,mac_address,match_option,next_server,pxe_lease_time,pxe_lease_time_enabled,routers,ttl,view
hostrecord,hostrec275.test.com,hostrec276.test.com,*********,rr.test.com,bootfile,bootserver.com,*********,Comment,TRUE,TRUE,TRUE,FALSE,a.com,"*******,*******",TRUE,1234::0aaa,1234,dd:aa:11:11:11:11,MAC_ADDRESS,*******,1200,TRUE,********,50,default
