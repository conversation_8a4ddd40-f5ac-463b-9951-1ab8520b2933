# ************************************************************
# SQL dump
#
# MySQL 5.5.24
# Database: netmri, report
# ************************************************************


USE `netmri`;

SET FOREIGN_KEY_CHECKS = 0;
LOCK TABLES `Device` WRITE;
/*!40000 ALTER TABLE `Device` DISABLE KEYS */;

TRUNCATE TABLE `Device`;

/*!40000 ALTER TABLE `Device` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifConfig` WRITE;
/*!40000 ALTER TABLE `ifConfig` DISABLE KEYS */;

TRUNCATE TABLE `ifConfig`;

/*!40000 ALTER TABLE `ifConfig` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifAddr` WRITE;
/*!40000 ALTER TABLE `ifAddr` DISABLE KEYS */;

TRUNCATE TABLE `ifAddr`;

/*!40000 ALTER TABLE `ifAddr` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `vrfAssignment` WRITE;
/*!40000 ALTER TABLE `vrfAssignment` DISABLE KEYS */;

TRUNCATE TABLE `vrfAssignment`;

/*!40000 ALTER TABLE `vrfAssignment` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DataCollectionStatus` WRITE;
/*!40000 ALTER TABLE `DataCollectionStatus` DISABLE KEYS */;
TRUNCATE TABLE `DataCollectionStatus`;
/*!40000 ALTER TABLE `DataCollectionStatus` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DeviceProperty` WRITE;
/*!40000 ALTER TABLE `DeviceProperty` DISABLE KEYS */;
TRUNCATE TABLE `DeviceProperty`;
/*!40000 ALTER TABLE `DeviceProperty` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `SNMPTableStatus` WRITE;
/*!40000 ALTER TABLE `SNMPTableStatus` DISABLE KEYS */;
TRUNCATE TABLE `SNMPTableStatus`;
/*!40000 ALTER TABLE `SNMPTableStatus` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `SdnNetwork` WRITE;
/*!40000 ALTER TABLE `SdnNetwork` DISABLE KEYS */;
TRUNCATE TABLE `SdnNetwork`;
/*!40000 ALTER TABLE `SdnNetwork` ENABLE KEYS */;
UNLOCK TABLES;


USE `report`;

LOCK TABLES `Device` WRITE;
/*!40000 ALTER TABLE `Device` DISABLE KEYS */;

TRUNCATE TABLE `Device`;

/*!40000 ALTER TABLE `Device` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `InfraDevice` WRITE;
/*!40000 ALTER TABLE `InfraDevice` DISABLE KEYS */;

TRUNCATE TABLE `InfraDevice`;

/*!40000 ALTER TABLE `InfraDevice` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `VirtualNetworkMember` WRITE;
/*!40000 ALTER TABLE `VirtualNetworkMember` DISABLE KEYS */;

TRUNCATE TABLE `VirtualNetworkMember`;

/*!40000 ALTER TABLE `VirtualNetworkMember` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifConfig` WRITE;
/*!40000 ALTER TABLE `ifConfig` DISABLE KEYS */;

TRUNCATE TABLE `ifConfig`;

/*!40000 ALTER TABLE `ifConfig` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifAddr` WRITE;
/*!40000 ALTER TABLE `ifAddr` DISABLE KEYS */;

TRUNCATE TABLE `ifAddr`;

/*!40000 ALTER TABLE `ifAddr` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifNeighbor` WRITE;
/*!40000 ALTER TABLE `ifNeighbor` DISABLE KEYS */;

TRUNCATE TABLE `ifNeighbor`;

/*!40000 ALTER TABLE `ifNeighbor` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifSwitchFwdNeighbor` WRITE;
/*!40000 ALTER TABLE `ifSwitchFwdNeighbor` DISABLE KEYS */;

TRUNCATE TABLE `ifSwitchFwdNeighbor`;

/*!40000 ALTER TABLE `ifSwitchFwdNeighbor` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `VlanMember` WRITE;
/*!40000 ALTER TABLE `VlanMember` DISABLE KEYS */;
TRUNCATE TABLE `VlanMember`;
/*!40000 ALTER TABLE `VlanMember` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifVlan` WRITE;
/*!40000 ALTER TABLE `ifVlan` DISABLE KEYS */;
TRUNCATE TABLE `ifVlan`;
/*!40000 ALTER TABLE `ifVlan` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `Vlan` WRITE;
/*!40000 ALTER TABLE `Vlan` DISABLE KEYS */;
TRUNCATE TABLE `Vlan`;
/*!40000 ALTER TABLE `Vlan` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DevicePhysical` WRITE;
/*!40000 ALTER TABLE `DevicePhysical` DISABLE KEYS */;
TRUNCATE TABLE `DevicePhysical`;
/*!40000 ALTER TABLE `DevicePhysical` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DeviceSetting` WRITE;
/*!40000 ALTER TABLE `DeviceSetting` DISABLE KEYS */;
TRUNCATE TABLE `DeviceSetting`;
/*!40000 ALTER TABLE `DeviceSetting` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `VirtualNetwork` WRITE;
/*!40000 ALTER TABLE `VirtualNetwork` DISABLE KEYS */;
TRUNCATE TABLE `VirtualNetwork`;
/*!40000 ALTER TABLE `VirtualNetwork` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DeviceGroup` WRITE;
/*!40000 ALTER TABLE `DeviceGroup` DISABLE KEYS */;
TRUNCATE TABLE `DeviceGroup`;
/*!40000 ALTER TABLE `DeviceGroup` ENABLE KEYS */;
UNLOCK TABLES;


USE `config`;

LOCK TABLES `scan_interfaces` WRITE;
/*!40000 ALTER TABLE `scan_interfaces` DISABLE KEYS */;
TRUNCATE TABLE `scan_interfaces`;
/*!40000 ALTER TABLE `scan_interfaces` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `device_metas` WRITE;
/*!40000 ALTER TABLE `device_metas` DISABLE KEYS */;
TRUNCATE TABLE `device_metas`;
/*!40000 ALTER TABLE `device_metas` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `interface_metas` WRITE;
/*!40000 ALTER TABLE `interface_metas` DISABLE KEYS */;
TRUNCATE TABLE `interface_metas`;
/*!40000 ALTER TABLE `interface_metas` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `sdn_controller_settings` WRITE;
/*!40000 ALTER TABLE `sdn_controller_settings` DISABLE KEYS */;
TRUNCATE TABLE `sdn_controller_settings`;
/*!40000 ALTER TABLE `sdn_controller_settings` ENABLE KEYS */;
UNLOCK TABLES;
SET FOREIGN_KEY_CHECKS = 1;
