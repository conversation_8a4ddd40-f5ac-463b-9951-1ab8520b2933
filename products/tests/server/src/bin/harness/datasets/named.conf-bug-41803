
include "/tmp/tsig.key";

key __infoblox_gss_tsig_key {
  algorithm gss-tsig;
};

acl all_dns_views_updater_keys {  key DHCP_UPDATER_default; };

options {
	masterfile-format text;
	zone-statistics yes;
	directory "/tmp";
	version none;
	recursion no;
	additional-from-cache no;
	max-recursion-depth 7;
	max-recursion-queries 150;
        infoblox-dns-update-quota 1024;
        infoblox-dns-update-forwarding-quota 1024;
	hostname none;
	listen-on { 127.0.0.1; ***********; };
	query-source address *********** port *; 
	notify-source *********** port *; 
	transfer-source ***********; 
	use-alt-transfer-source no; 
	minimal-responses yes;
	max-cache-size 2404829184;
        lame-ttl 600;
	tcp-clients 1000;
	transfers-in 10;
	transfers-out 10;
	transfers-per-ns 2;
	serial-query-rate 20;
        max-cache-ttl 604800;
        max-ncache-ttl 10800;
        edns-udp-size 1220;
        max-udp-size 1220;
	# for service restart: allow_bulkhost_ddns = Refusal
	allow-transfer { !any; };
	avoid-v4-udp-ports { 2114; 2113; 2115; 3000; 8000; 8089; 9997; 2222; 7911; 7912; 8000; 8089; 9997; 8080; 9000; 9999; 9004; 2022; 3374; 3115; 1194; };
	avoid-v6-udp-ports { 2114; 2113; 2115; 3000; 8000; 8089; 9997; 2222; 7911; 7912; 8000; 8089; 9997; 8080; 9000; 9999; 9004; 2022; 3374; 3115; 1194; };
	transfer-format many-answers;
        max-journal-size 100000K;
#
#  GSS-TSIG Keys
#  Vno	Type	Principal
#  3	des-cbc-crc	DNS/<EMAIL>
};

# Worker threads: default

# Bulk Host Name Templates:
#	Four Octets: 		"-$1-$2-$3-$4" (Default)
#	One Octet: 		"-$4"
#	Three Octets: 		"-$2-$3-$4"
#	Two Octets: 		"-$3-$4"

# multi-master master selection: 30-5-120-20

include "/tmp/dhcp_updater.key";

include "/tmp/rndc.key";

controls {
        inet 127.0.0.1 port 953
        allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	 channel ib_syslog { 
		 syslog daemon; 
		 severity info; 
	};
	 category default { ib_syslog; };
};

# default
view "_default" {  # default
    match-clients { key DHCP_UPDATER_default; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
	database infoblox_zdb;
	masterfile-format raw;
	file "azd/db.0.0.127.in-addr.arpa._default";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa._default";
    };
    zone "bug_41803.com" in { # bug_41803.com
	type master;
	database infoblox_zdb;
	masterfile-format raw;
	file "azd/db.bug_41803.com._default";
	notify yes;
    };
    zone "_msdcs.bug_41803.com" in { # _msdcs.bug_41803.com
	type master;
	database infoblox_zdb;
	masterfile-format raw;
	file "azd/db._msdcs.bug_41803.com._default";
	allow-update { key __infoblox_gss_tsig_key;  };
	notify yes;
    };
    zone "_sites.bug_41803.com" in { # _sites.bug_41803.com
	type master;
	database infoblox_zdb;
	masterfile-format raw;
	file "azd/db._sites.bug_41803.com._default";
	allow-update { key __infoblox_gss_tsig_key;  };
	notify yes;
    };
    zone "_tcp.bug_41803.com" in { # _tcp.bug_41803.com
	type master;
	database infoblox_zdb;
	masterfile-format raw;
	file "azd/db._tcp.bug_41803.com._default";
	allow-update { key __infoblox_gss_tsig_key;  };
	notify yes;
    };
    zone "_udp.bug_41803.com" in { # _udp.bug_41803.com
	type master;
	database infoblox_zdb;
	masterfile-format raw;
	file "azd/db._udp.bug_41803.com._default";
	allow-update { key __infoblox_gss_tsig_key;  };
	notify yes;
    };
    zone "domaindnszones.bug_41803.com" in { # domaindnszones.bug_41803.com
	type master;
	database infoblox_zdb;
	masterfile-format raw;
	file "azd/db.domaindnszones.bug_41803.com._default";
	allow-update { key __infoblox_gss_tsig_key;  };
	notify yes;
    };
    zone "forestdnszones.bug_41803.com" in { # forestdnszones.bug_41803.com
	type master;
	database infoblox_zdb;
	masterfile-format raw;
	file "azd/db.forestdnszones.bug_41803.com._default";
	allow-update { key __infoblox_gss_tsig_key;  };
	notify yes;
    };
};

# Zone OID composite: 15785
