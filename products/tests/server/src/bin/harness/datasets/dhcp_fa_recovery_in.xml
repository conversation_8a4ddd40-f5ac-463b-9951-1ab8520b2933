<test_data>

<?ignore
Conflict resolution table:
---------------------------------------------------------------------------
s |                                  Primary
e |-------------------------------------------------------------------------
c |              ACTIVE | EXPIRED | RELEASED | FREE/BACKUP | RESET/ABANDONED
o | ACTIVE       case1  | case2   | case2    | case2       | case2
n | EXPIRED      case2  | prm     | prm      | prm         | prm
d | RELEASED     case2  | r/ends  | prm      | prm         | prm
a | FREE/BACKUP  case2  | prm     | prm      | prm         | prm
r | RESET        case2  | prm     | prm      | prm         | prm
y | ABANDONED    case2  | sec     | sec      | sec         | prm

 prm - primary lease incarnation wins
 sec - secondary lease incarnation wins
 r/ends - lease with recent end wins

 case1:
  1.1 need to check if there is dns data for lease (check "variable"
      member in lease, per dhcpd.leases(5), also check for
      "deferred_*" members in lease);
  1.2 in case both peers have dns data check for "ends" date and lease
      with latest date will win;
  1.4 in case one lease has dns data it will win (please also look at 1.6);
  1.5 in case there is no dns data compare "ends" and lease with
      latest date will win;
  1.6 in case the ends time for the primary is later, and that the
      secondary lease has DNS data. Then, the merged lease should have the
      ends time (and items other than the DNS data) from the primary, but
      the DNS data from the secondary.

 case2:
  2.1 need to check if there is dns data for lease (check
      "variable" member in lease, per dhcpd.leases(5), also
      check for "deferred_*" members in lease);
  2.2 in case both peers have dns data, active lease win (remove
      dns data from inactive lease);
  2.3 in case there is no dns data for leases active lease will
      win.

 If the recovery converts an active lease to an inactive one, and that lease has
 deferred update information for adding DNS data, then that add is obsolete
 (since the lease no longer is active), and should be dropped.

 If the recovery code transitions a lease from inactive to active, and the
 inactive lease had a deferred DNS removal, then that removal is
 obsolete and should be deleted from the lease.

 DNS removals also need to reside in the inactive lease structure (until the
 lease is re-used and becomes active again). Such deferred removals should also
 be preserved in the merged/recovered lease.
 ?>
    <!-- ACTIVE(primary):ACTIVE(secondary) -->

    <!-- Same ends, primary will win. -->
    <lease ip="127.0.0.1">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Same ends, primary will win, shouldn't be updated, check ack_state for primary didn't changed. -->
    <lease ip="*********">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00" ack_state="renew"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Primary has latest ends, and thus should win. -->
    <lease ip="*********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Secondary has latest ends, and thus should win. -->
    <lease ip="*********">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:20:00"/>
    </lease>
    <!-- Both leases are infinite, primary should win. -->
    <lease ip="*********">
        <primary binding_state="active" _ends="never;"/>
        <secondary binding_state="active" _ends="never;"/>
    </lease>
    <!-- Primary lease is infinite, and thus should win. -->
    <lease ip="*********">
        <primary binding_state="active" _ends="never;"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Secondary lease is infinite, and thus should win. -->
    <lease ip="*********">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="active" _ends="never;"/>
    </lease>
    <!-- Both leases have applied DNS data, primary has latest ends and thus should win. Remove DNS data from secondary. -->
    <lease ip="*********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" variable="ddns-client-fqdn=&quot;some1.name&quot; ddns-rev-name=&quot;rev1.data&quot; lt=&quot;4300&quot;"/>
    </lease>
    <!-- Both leases have applied DNS data, secondary has latest ends and thus should win. Remove DNS data from primary. -->
    <lease ip="*********">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:20:00" variable="ddns-client-fqdn=&quot;some1.name&quot; ddns-rev-name=&quot;rev1.data&quot; lt=&quot;4300&quot;"/>
    </lease>
    <!-- Primary lease have applied DNS data and latest ends and thus should win. -->
    <lease ip="127.0.0.10">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" variable="lt=&quot;4300&quot;"/>
    </lease>
    <!-- Secondary lease have applied DNS data and latest ends and thus should win. -->
    <lease ip="127.0.0.11">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00" variable="lt=&quot;4300&quot;"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:20:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;"/>
    </lease>
    <!-- Secondary lease has applied DNS data, primary lease has latest ends and thus should win (retain DNS data for secondary). -->
    <lease ip="127.0.0.12">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" variable="lt=&quot;4300&quot;"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;"/>
    </lease>
    <!-- Primary lease have applied DNS data, secondary lease has latest ends and thus should win (retain DNS data for primary). -->
    <lease ip="127.0.0.13">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:20:00" variable="lt=&quot;4300&quot;"/>
    </lease>

    <!-- In next 9 cases primary has latest ends and thus should win.
         Will test different combinations of deferred data in leases (ADD mean deferred adding update, REMOVE means deferred deletion update):
            - primary ADD, secondary ADD;
            - primary ADD + REMOVE, secondary ADD;
            - primary REMOVE, secondary ADD;
            - primary ADD, secondary REMOVE;
            - primary ADD, secondary ADD + REMOVE;
            - primary ADD + REMOVE, secondary ADD + REMOVE;
            - primary REMOVE, secondary REMOVE;
            - primary ADD + REMOVE, secondary REMOVE;
            - primary REMOVE, secondary ADD + REMOVE;
        In all above cases updates should retain for one server (prefer winner).
        Please note that 'deferred_fwd_name' and 'deferred_txt' are processed in pair.
        Will remove 'deferred_ttl' for lease if no updates left for it.
    -->
    <lease ip="127.0.0.14">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="127.0.0.15">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="127.0.0.16">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="127.0.0.17">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="127.0.0.18">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="127.0.0.19">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="active" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>

    <!-- ACTIVE(primary):ABANDONED(secondary) -->

    <!-- Same ends, primary will win. -->
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Same ends, primary will win, shouldn't be updated, check ack_state for primary didn't changed. -->
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00" ack_state="renew"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Primary has latest ends, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Secondary has latest ends, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00"/>
    </lease>
    <!-- Both leases are infinite, primary should win. -->
    <lease ip="**********">
        <primary binding_state="active" _ends="never;"/>
        <secondary binding_state="abandoned" _ends="never;"/>
    </lease>
    <!-- Primary lease is infinite, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="active" _ends="never;"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Secondary lease is infinite, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="abandoned" _ends="never;"/>
    </lease>

    <!-- In next cases active lease will win in all cases when it has applied DNS data or if it's 'ends' time is in the future.
         DNS data will retain with one peer. -->

    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" variable="ddns-client-fqdn=&quot;some1.name&quot; ddns-rev-name=&quot;rev1.data&quot; lt=&quot;4300&quot;"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" variable="lt=&quot;4300&quot;"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 3015/12/07 10:00:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" variable="ddns-client-fqdn=&quot;some1.name&quot; ddns-rev-name=&quot;rev1.data&quot; lt=&quot;4300&quot;"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 3015/12/07 10:00:00" variable="ddns-client-fqdn=&quot;some.name&quot; ddns-rev-name=&quot;rev.data&quot; lt=&quot;4200&quot;"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" variable="lt=&quot;4300&quot;"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 3015/12/07 10:00:00" variable="lt=&quot;4200&quot;"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" variable="ddns-client-fqdn=&quot;some1.name&quot; ddns-rev-name=&quot;rev1.data&quot; lt=&quot;4300&quot;"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 3015/12/07 10:00:00" variable="lt=&quot;4200&quot;"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" variable="lt=&quot;4300&quot;"/>
    </lease>

    <!-- In next 9 cases primary has latest ends and thus should win.
         Will test different combinations of deferred data in leases (ADD mean deferred adding update, REMOVE means deferred deletion update):
            - primary ADD, secondary ADD;
            - primary ADD + REMOVE, secondary ADD;
            - primary REMOVE, secondary ADD;
            - primary ADD, secondary REMOVE;
            - primary ADD, secondary ADD + REMOVE;
            - primary ADD + REMOVE, secondary ADD + REMOVE;
            - primary REMOVE, secondary REMOVE;
            - primary ADD + REMOVE, secondary REMOVE;
            - primary REMOVE, secondary ADD + REMOVE;
        In all above cases updates should retain for one server (prefer winner).
        Please note that 'deferred_fwd_name' and 'deferred_txt' are processed in pair.
        Will remove 'deferred_ttl' for lease if no updates left for it.
    -->
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>

    <!-- In next cases inactive lease will win because of latest 'ends' time and no adding updates for active lease. -->

    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="active" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>

    <!-- EXPIRED(primary):RELEASED(secondary) -->

    <!-- Same ends, primary will win. -->
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Same ends, primary will win, shouldn't be updated, check ack_state for primary didn't changed. -->
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00" ack_state="renew"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Primary has latest ends, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Secondary has latest ends, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:20:00"/>
    </lease>
    <!-- Both leases are infinite, primary should win. -->
    <lease ip="**********">
        <primary binding_state="expired" _ends="never;"/>
        <secondary binding_state="released" _ends="never;"/>
    </lease>
    <!-- Primary lease is infinite, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="expired" _ends="never;"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <!-- Secondary lease is infinite, and thus should win. -->
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="released" _ends="never;"/>
    </lease>

    <!-- In next 9 cases primary has latest ends and thus should win.
         Will test different combinations of deferred data in leases (ADD mean deferred adding update, REMOVE means deferred deletion update):
            - primary ADD, secondary ADD;
            - primary ADD + REMOVE, secondary ADD;
            - primary REMOVE, secondary ADD;
            - primary ADD, secondary REMOVE;
            - primary ADD, secondary ADD + REMOVE;
            - primary ADD + REMOVE, secondary ADD + REMOVE;
            - primary REMOVE, secondary REMOVE;
            - primary ADD + REMOVE, secondary REMOVE;
            - primary REMOVE, secondary ADD + REMOVE;
        In all above cases updates should retain for one server (prefer winner).
        Please note that 'deferred_fwd_name' and 'deferred_txt' are processed in pair.
        Will remove 'deferred_ttl' for lease if no updates left for it.
    -->
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="released" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>

    <!-- EXPIRED(primary):EXPIRED(secondary) -->
    <!-- Primary lease will always win. -->

    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00" ack_state="renew"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:10:00"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:20:00"  ack_state="renew"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="never;"/>
        <secondary binding_state="expired" _ends="never;"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="never;"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="expired" _ends="never;"/>
    </lease>

    <!-- Will test different combinations of deferred data in leases (ADD mean deferred adding update, REMOVE means deferred deletion update):
            - primary ADD, secondary ADD;
            - primary ADD + REMOVE, secondary ADD;
            - primary REMOVE, secondary ADD;
            - primary ADD, secondary REMOVE;
            - primary ADD, secondary ADD + REMOVE;
            - primary ADD + REMOVE, secondary ADD + REMOVE;
            - primary REMOVE, secondary REMOVE;
            - primary ADD + REMOVE, secondary REMOVE;
            - primary REMOVE, secondary ADD + REMOVE;
        In all above cases updates should retain for one server (prefer winner).
        Please note that 'deferred_fwd_name' and 'deferred_txt' are processed in pair.
        Will remove 'deferred_ttl' for lease if no updates left for it.
    -->
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="expired" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>

    <!-- EXPIRED(primary):ABANDONED(secondary) -->
    <!-- Secondary lease will always win. -->

    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00" ack_state="renew"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00" ack_state="renew"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:20:00"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="never;"/>
        <secondary binding_state="abandoned" _ends="never;"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="never;"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:00:00"/>
        <secondary binding_state="abandoned" _ends="never;"/>
    </lease>

    <!-- Will test different combinations of deferred data in leases (ADD mean deferred adding update, REMOVE means deferred deletion update):
            - primary ADD, secondary ADD;
            - primary ADD + REMOVE, secondary ADD;
            - primary REMOVE, secondary ADD;
            - primary ADD, secondary REMOVE;
            - primary ADD, secondary ADD + REMOVE;
            - primary ADD + REMOVE, secondary ADD + REMOVE;
            - primary REMOVE, secondary REMOVE;
            - primary ADD + REMOVE, secondary REMOVE;
            - primary REMOVE, secondary ADD + REMOVE;
        In all above cases updates should retain for one server (prefer winner).
        Please note that 'deferred_fwd_name' and 'deferred_txt' are processed in pair.
        Will remove 'deferred_ttl' for lease if no updates left for it.
    -->
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="ADD some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="ADD name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="ADD some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="REMOVE name1.some" deferred_ttl="102"/>
    </lease>
    <lease ip="**********">
        <primary binding_state="expired" _ends="1 2015/12/07 10:10:00" deferred_fwd_name="REMOVE some.name" deferred_txt="some.name" deferred_rev_name="REMOVE name.some" deferred_ttl="101"/>
        <secondary binding_state="abandoned" _ends="1 2015/12/07 10:00:00" deferred_fwd_name="REMOVE some1.name" deferred_txt="some1.name" deferred_rev_name="ADD name1.some" deferred_ttl="102"/>
    </lease>

</test_data>
