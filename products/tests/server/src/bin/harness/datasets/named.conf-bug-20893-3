include "/infoblox/var/named_conf/tsig.key";

acl all_dns_views_updater_keys { key DHCP_UPDATER_default; };

options {
        masterfile-format text;
        zone-statistics yes;
        directory "/infoblox/var/named_conf";
        version none;
        recursion no;
        max-recursion-depth 7;
        max-recursion-queries 150;
        infoblox-dns-update-quota 1024;
        infoblox-dns-update-forwarding-quota 1024;
        hostname none;
        listen-on { 127.0.0.1; ***********; };
        query-source address *********** port *;
        notify-source *********** port *;
        transfer-source ***********;
        minimal-responses yes;
        max-cache-size 467550208;
        lame-ttl 600;
        tcp-clients 1000;
        transfers-in 10;
        transfers-out 10;
        transfers-per-ns 2;
        serial-query-rate 20;
        max-cache-ttl 604800;
        max-ncache-ttl 10800;
        edns-udp-size 1220;
        max-udp-size 1220;
        # for service restart: allow_bulkhost_ddns = Refusal
        allow-transfer { !any; };
        transfer-format many-answers;
        max-journal-size 100000K;
};

# Worker threads: default

# Bulk Host Name Templates:
#       Four Octets:            "-$1-$2-$3-$4" (Default)
#       One Octet:              "-$4"
#       Three Octets:           "-$2-$3-$4"
#       Two Octets:             "-$3-$4"

# multi-master master selection: 30-5-120-20

include "/infoblox/var/named_conf/dhcp_updater.key";

include "/infoblox/var/named_conf/rndc.key";

controls {
        inet 127.0.0.1 port 953
        allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
         channel ib_syslog {
                 syslog daemon;
                 severity info;
        };
         category default { ib_syslog; };
         category rpz { null; };
};

# default
view "_default" {  # default
    match-clients { key DHCP_UPDATER_default; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "10.in-addr.arpa" { # 10.in-addr.arpa
        type stub;
        masters { *******; *******; *******; *******; *******; };
        masterfile-format raw;
        file "db.10.in-addr.arpa._default";
    };
    zone "0.0.127.in-addr.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.0.0.127.in-addr.arpa._default";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa._default";
    };
    zone "stub.com" { # stub.com
        type stub;
        masters { *******; *******; *******; *******; *******; };
        masterfile-format raw;
        file "db.stub.com._default";
    };
};
