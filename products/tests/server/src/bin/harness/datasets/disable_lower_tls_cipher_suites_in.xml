<MDXML>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.cluster"/>
    <PROPERTY NAME="uuid" VALUE="c51dd2c9eea54f018248e4f85bbbebb1"/>
    <PROPERTY NAME="revision_id" VALUE="31"/>
    <PROPERTY NAME="virtual_node_count" VALUE="2"/>
    <PROPERTY NAME="ms_server_count" VALUE="0"/>
    <PROPERTY NAME="physical_node_count" VALUE="2"/>
    <PROPERTY NAME="end_point_count" VALUE="0"/>
    <PROPERTY NAME="vpn_port_number" VALUE="1194"/>
    <PROPERTY NAME="enable_member_redirect" VALUE="true"/>
    <PROPERTY NAME="enable_recycle_bin" VALUE="true"/>
    <PROPERTY NAME="enable_scheduling" VALUE="false"/>
    <PROPERTY NAME="audit_log_format" VALUE="0"/>
    <PROPERTY NAME="restart_option" VALUE="1"/>
    <PROPERTY NAME="member_restart_sequential_delay" VALUE="10"/>
    <PROPERTY NAME="internal_apache_http_port" VALUE="9000"/>
    <PROPERTY NAME="internal_taxii_http_port" VALUE="9183"/>
    <PROPERTY NAME="internal_jetty_http_port" VALUE="8080"/>
    <PROPERTY NAME="internal_reporting_http_port" VALUE="9185"/>
    <PROPERTY NAME="enable_extended_discovery" VALUE="false"/>
    <PROPERTY NAME="enable_automation_task_pack" VALUE="false"/>
    <PROPERTY NAME="lb_sync_source" VALUE="ANY"/>
    <PROPERTY NAME="lines_per_page" VALUE="22"/>
    <PROPERTY NAME="fw_http_limit" VALUE="20"/>
    <PROPERTY NAME="fw_https_limit" VALUE="20"/>
    <PROPERTY NAME="enable_rir_swip" VALUE="false"/>
    <PROPERTY NAME="allow_recursive_deletion" VALUE="ALL"/>
    <PROPERTY NAME="dscp" VALUE="0"/>
    <PROPERTY NAME="enable_dns_perm_for_nw_range" VALUE="false"/>
    <PROPERTY NAME="admin_httpd_max_clients" VALUE="0"/>
    <PROPERTY NAME="is_grid_visualization_visible" VALUE="false"/>
    <PROPERTY NAME="enable_gui_api_for_lan_vip" VALUE="false"/>
    <PROPERTY NAME="token_usage_delay" VALUE="10"/>
    <PROPERTY NAME="rpz_hit_rate_interval" VALUE="10"/>
    <PROPERTY NAME="rpz_hit_rate_min_query" VALUE="1000"/>
    <PROPERTY NAME="rpz_hit_rate_max_query" VALUE="100000"/>
    <PROPERTY NAME="override_ssl_tls_settings" VALUE="false"/>
    <PROPERTY NAME="enable_tlsv1" VALUE="false"/>
    <PROPERTY NAME="enable_tlsv1_1" VALUE="false"/>
    <PROPERTY NAME="enable_tlsv1_2" VALUE="true"/>
    <PROPERTY NAME="enable_tlsv1_3" VALUE="true"/>
    <PROPERTY NAME="ssl_security_level" VALUE="0"/>
    <PROPERTY NAME="override_sshd_settings" VALUE="false"/>
    <PROPERTY NAME="override_ssh_settings" VALUE="false"/>
    <PROPERTY NAME="deny_mgm_snapshots" VALUE="false"/>
    <PROPERTY NAME="enable_wins_forwarding" VALUE="false"/>
    <PROPERTY NAME="output_interface_for_wins_packets" VALUE="LAN"/>
    <PROPERTY NAME="heka_opt_in" VALUE="false"/>
    <PROPERTY NAME="heka_update_enabled" VALUE="false"/>
    <PROPERTY NAME="heka_action_ts" VALUE="0"/>
    <PROPERTY NAME="support_timeout" VALUE="1200"/>
    <PROPERTY NAME="core_files_quota" VALUE="30"/>
    <PROPERTY NAME="enable_sanitize_csv" VALUE="true"/>
    <PROPERTY NAME="federation_enabled" VALUE="false"/>
    <PROPERTY NAME="federation_enforcement_enabled" VALUE="false"/>
    <PROPERTY NAME="force_sync_join_token_to_gmc" VALUE="false"/>
    <PROPERTY NAME="blast_radius_mitigation" VALUE="false"/>
    <PROPERTY NAME="openvpn_over_ssh_tunnel" VALUE="1"/>
    <PROPERTY NAME="ftp_mode" VALUE="passive"/>
    <PROPERTY NAME="radius_calling_station_id" VALUE="true"/>
    <PROPERTY NAME="cluster_parent" VALUE="."/>
    <PROPERTY NAME="cluster_oid" VALUE="0"/>
    <PROPERTY NAME="name" VALUE="Infoblox"/>
    <PROPERTY NAME="shared_secret" VALUE="{0}_{aes}_IAAAANRiBYKzxj2fh3zazQSjYWJTcqww1KxwOm3/9RcBmS92"/>
    <PROPERTY NAME="prompt_type" VALUE="Infoblox"/>
    <PROPERTY NAME="_update_id" VALUE="0:0:53710b7e:0:80009a9f"/>
    <PROPERTY NAME="is_gog_strict_delegate_mode" VALUE="false"/>
    <PROPERTY NAME="lom_enabled" VALUE="false"/>
    <PROPERTY NAME="license_uid" VALUE="7757d5f9820d4e4382a056c1d790fc64"/>
    <PROPERTY NAME="csp_dns_resolver" VALUE="*************"/>
    <PROPERTY NAME="heka_collection_member" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.httpd_ssl_tls_cipher_suite"/>
    <PROPERTY NAME="cipher_suite" VALUE="TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="4"/>
    <PROPERTY NAME="parent" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.httpd_ssl_tls_cipher_suite"/>
    <PROPERTY NAME="cipher_suite" VALUE="TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="5"/>
    <PROPERTY NAME="parent" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.httpd_ssl_tls_cipher_suite"/>
    <PROPERTY NAME="cipher_suite" VALUE="TLS_RSA_WITH_AES_128_CBC_SHA"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="14"/>
    <PROPERTY NAME="parent" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.httpd_ssl_tls_cipher_suite"/>
    <PROPERTY NAME="cipher_suite" VALUE="TLS_DHE_RSA_WITH_AES_128_CBC_SHA"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="9"/>
    <PROPERTY NAME="parent" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.httpd_ssl_tls_cipher_suite"/>
    <PROPERTY NAME="cipher_suite" VALUE="TLS_DHE_RSA_WITH_AES_256_CBC_SHA"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="10"/>
    <PROPERTY NAME="parent" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.httpd_ssl_tls_cipher_suite"/>
    <PROPERTY NAME="cipher_suite" VALUE="TLS_RSA_WITH_3DES_EDE_CBC_SHA"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="16"/>
    <PROPERTY NAME="parent" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.httpd_ssl_tls_cipher_suite"/>
    <PROPERTY NAME="cipher_suite" VALUE="TLS_RSA_WITH_AES_256_CBC_SHA"/>
    <PROPERTY NAME="enabled" VALUE="true"/>
    <PROPERTY NAME="position" VALUE="18"/>
    <PROPERTY NAME="parent" VALUE="0"/>
  </OBJECT>
</MDXML>