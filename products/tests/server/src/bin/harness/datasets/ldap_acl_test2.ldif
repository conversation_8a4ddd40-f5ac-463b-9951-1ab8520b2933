# Copyright (c) 1999-2010 Infoblox Inc. All Rights Reserved.

dn: ou=data, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: data

dn: ou=subtree, ou=data, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: subtree

dn: ou=one, ou=data, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: data

dn: ou=children, ou=data, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: children

dn: ou=who_test, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: who_test

dn: ou=people, ou=who_test, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: people

dn: ou=subtree, ou=people, ou=who_test, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: subtree

dn: ou=one, ou=people, ou=who_test, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: one

dn: ou=priv_test, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: priv_test

dn: ou=add, ou=priv_test, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: add

dn: ou=people, ou=priv_test, dc=acme, dc=com
objectclass: top
objectclass: organizationalUnit
ou: people

dn: cn=subtree_data, ou=subtree, ou=data, dc=acme, dc=com
uid: subtree_data
cn: subtree_data
sn: SUBTREE_DATA
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1002
gidNumber: 1002
homeDirectory: /home/<USER>
gecos: subtree_data
userpassword: subtree_data_pwd

dn: cn=everything_data, ou=data, dc=acme, dc=com
uid: everything_data
cn: everything_data
sn: EVERYTHING_DATA
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1003
gidNumber: 1003
homeDirectory: /home/<USER>
gecos: everything_data
userpassword: everything_data_pwd

dn: cn=base_data, ou=data, dc=acme, dc=com
uid: base_data
cn: base_data
sn: BASE_DATA
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1003
gidNumber: 1003
homeDirectory: /home/<USER>
gecos: base_data
userpassword: base_data_pwd

dn: cn=one_data, ou=one, ou=data, dc=acme, dc=com
uid: one_data
cn: one_data
sn: ONE_DATA
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1004
gidNumber: 1004
homeDirectory: /home/<USER>
gecos: one_data
userpassword: one_data_pwd

dn: cn=children_data, ou=children, ou=data, dc=acme, dc=com
uid: children_data
cn: children_data
sn: CHILDREN_DATA
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1005
gidNumber: 1005
homeDirectory: /home/<USER>
gecos: children_data
userpassword: children_data_pwd

dn: cn=what_dn_type_test_user, ou=other, dc=acme, dc=com
uid: what_dn_type_test_user
cn: what_dn_type_test_user
sn: WHAT_DN_TYPE_TEST_USER
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1006
gidNumber: 1006
homeDirectory: /home/<USER>
gecos: what_dn_type_test_user
userpassword: what_dn_type_test_user_pwd

dn: cn=what_everything_filter_test_user, ou=other, dc=acme, dc=com
uid: what_everything_filter_test_user
cn: what_everything_filter_test_user
sn: WHAT_EVERYTHING_FILTER_TEST_USER
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1007
gidNumber: 1007
homeDirectory: /home/<USER>
gecos: what_everything_filter_test_user
userpassword: what_everything_filter_test_user_pwd

dn: cn=what_filter_test_user, ou=other, dc=acme, dc=com
uid: what_filter_test_user
cn: what_filter_test_user
sn: WHAT_FILTER_TEST_USER
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1007
gidNumber: 1007
homeDirectory: /home/<USER>
gecos: what_filter_test_user
userpassword: what_filter_test_user_pwd

dn: cn=what_dn_type_attr_test_user, ou=other, dc=acme, dc=com
uid: what_dn_type_attr_test_user
cn: what_dn_type_attr_test_user
sn: WHAT_DN_TYPE_ATTR_TEST_USER
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1008
gidNumber: 1008
homeDirectory: /home/<USER>
gecos: what_dn_type_attr_test_user
userpassword: what_dn_type_attr_test_user_pwd

dn: cn=what_filter_attr_test_user, ou=other, dc=acme, dc=com
uid: what_filter_attr_test_user
cn: what_filter_attr_test_user
sn: WHAT_FILTER_ATTR_TEST_USER
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1009
gidNumber: 1009
homeDirectory: /home/<USER>
gecos: what_filter_attr_test_user
userpassword: what_filter_attr_test_user_pwd

dn: cn=what_dn_type_not_attr_test_user, ou=other, dc=acme, dc=com
uid: what_dn_type_not_attr_test_user
cn: what_dn_type_not_attr_test_user
sn: WhAT_DN_TYPE_NOT_ATTR_TEST_USER
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1010
gidNumber: 1010
homeDirectory: /home/<USER>
gecos: what_dn_type_not_attr_test_user
userpassword: what_dn_type_not_attr_test_user_pwd

dn: cn=what_filter_not_attr_test_user, ou=other, dc=acme, dc=com
uid: what_filter_not_attr_test_user
cn: what_filter_not_attr_test_user
sn: WHAT_FILTER_NOT_ATTR_TEST_USER
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1011
gidNumber: 1011
homeDirectory: /home/<USER>
gecos: what_filter_not_attr_test_user
userpassword: what_filter_not_attr_test_user_pwd

dn: cn=everyone, ou=who_test, dc=acme, dc=com
uid: everyone
cn: everyone
sn: EVERYONE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1012
gidNumber: 1012
homeDirectory: /home/<USER>
gecos: everyone
userpassword: everyone_pwd

dn: cn=anonymous, ou=who_test, dc=acme, dc=com
uid: anonymous
cn: anonymous
sn: ANONYMOUS
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1013
gidNumber: 1013
homeDirectory: /home/<USER>
gecos: anonymous
userpassword: anonymous_pwd

dn: cn=users, ou=who_test, dc=acme, dc=com
uid: users
cn: users
sn: USERS
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1014
gidNumber: 1014
homeDirectory: /home/<USER>
gecos: users
userpassword: users_pwd

dn: cn=self, ou=who_test, dc=acme, dc=com
uid: self
cn: self
sn: SELF
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1015
gidNumber: 1015
homeDirectory: /home/<USER>
gecos: self
userpassword: self_pwd

dn: cn=subtree, ou=who_test, dc=acme, dc=com
uid: subtree
cn: subtree
sn: SUBTREE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1016
gidNumber: 1016
homeDirectory: /home/<USER>
gecos: subtree
userpassword: subtree_pwd

dn: cn=subtree_people, ou=subtree, ou=people, ou=who_test, dc=acme, dc=com
uid: subtree_people
cn: subtree_people
sn: SUBTREE_PEOPLE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1017
gidNumber: 1017
homeDirectory: /home/<USER>
gecos: subtree_people
userpassword: subtree_people_pwd

dn: cn=base_who_test, ou=who_test, dc=acme, dc=com
uid: base_who_test
cn: base_who_test
sn: BASE_WHO_TEST
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1018
gidNumber: 1018
homeDirectory: /home/<USER>
gecos: base_who_test
userpassword: base_who_test_pwd

dn: cn=base_people_who_test, ou=people, ou=who_test, dc=acme, dc=com
uid: base_people_who_test
cn: base_people_who_test
sn: BASE_PEOPLE_WHO_TEST
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1019
gidNumber: 1019
homeDirectory: /home/<USER>
gecos: base_people_who_test
userpassword: base_people_who_test_pwd

dn: cn=one, ou=who_test, dc=acme, dc=com
uid: one
cn: one
sn: ONE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1020
gidNumber: 1020
homeDirectory: /home/<USER>
gecos: one
userpassword: one_pwd

dn: cn=one_people, ou=one, ou=people, ou=who_test, dc=acme, dc=com
uid: one_people
cn: one_people
sn: ONE_PEOPLE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1021
gidNumber: 1021
homeDirectory: /home/<USER>
gecos: one_people
userpassword: one_people_pwd

dn: cn=group_who_test, ou=who_test, dc=acme, dc=com
uid: group_who_test
cn: group_who_test
sn: GROUP_WHO_TEST
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1022
gidNumber: 1022
homeDirectory: /home/<USER>
gecos: group_who_test
userpassword: group_who_test_pwd

dn: cn=group, ou=people, ou=who_test, dc=acme, dc=com
cn: group
objectClass: groupOfNames
objectClass: top
member: cn=one_people,ou=one,ou=people,ou=who_test,dc=acme,dc=com

dn: cn=ipaddr, ou=who_test, dc=acme, dc=com
uid: ipaddr
cn: ipaddr
sn: IPADDR
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1024
gidNumber: 1024
homeDirectory: /home/<USER>
gecos: ipaddr
userpassword: ipaddr_pwd

dn: cn=ipaddr_anonymous, ou=who_test, dc=acme, dc=com
uid: ipaddr_anonymous
cn: ipaddr_anonymous
sn: IPADDR_ANONYMOUS
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1025
gidNumber: 1025
homeDirectory: /home/<USER>
gecos: ipaddr_anonymous
userpassword: ipaddr_anonymous_pwd

dn: cn=ipaddr_users, ou=who_test, dc=acme, dc=com
uid: ipaddr_users
cn: ipaddr_users
sn: IPADDR_USERS
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1026
gidNumber: 1026
homeDirectory: /home/<USER>
gecos: ipaddr_users
userpassword: ipaddr_users_pwd

dn: cn=ipaddr_self, ou=who_test, dc=acme, dc=com
uid: ipaddr_self
cn: ipaddr_self
sn: IPADDR_SELF
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1027
gidNumber: 1027
homeDirectory: /home/<USER>
gecos: ipaddr_self
userpassword: ipaddr_self_pwd

dn: cn=ipaddr_subtree, ou=who_test, dc=acme, dc=com
uid: ipaddr_subtree
cn: ipaddr_subtree
sn: IPADDR_SUBTREE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1028
gidNumber: 1028
homeDirectory: /home/<USER>
gecos: ipaddr_subtree
userpassword: ipaddr_subtree_pwd

dn: cn=ipaddr_base, ou=who_test, dc=acme, dc=com
uid: ipaddr_base
cn: ipaddr_base
sn: IPADDR_BASE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1029
gidNumber: 1029
homeDirectory: /home/<USER>
gecos: ipaddr_base
userpassword: ipaddr_base_pwd

dn: cn=ipaddr_one, ou=who_test, dc=acme, dc=com
uid: ipaddr_one
cn: ipaddr_one
sn: IPADDR_ONE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1030
gidNumber: 1030
homeDirectory: /home/<USER>
gecos: ipaddr_one
userpassword: ipaddr_one_pwd

dn: cn=ipaddr_group, ou=who_test, dc=acme, dc=com
uid: ipaddr_group
cn: ipaddr_group
sn: IPADDR_GROUP
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1031
gidNumber: 1031
homeDirectory: /home/<USER>
gecos: ipaddr_group
userpassword: ipaddr_group_pwd

dn: cn=network_everyone, ou=who_test, dc=acme, dc=com
uid: network_everyone
cn: network_everyone
sn: NETWORK_EVERYONE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1032
gidNumber: 1032
homeDirectory: /home/<USER>
gecos: network_everyone
userpassword: network_everyone_pwd

dn: cn=network_anonymous, ou=who_test, dc=acme, dc=com
uid: network_anonymous
cn: network_anonymous
sn: NETWORK_ANONYMOUS
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1033
gidNumber: 1033
homeDirectory: /home/<USER>
gecos: network_anonymous
userpassword: network_anonymous_pwd

dn: cn=network_users, ou=who_test, dc=acme, dc=com
uid: network_users
cn: network_users
sn: NETWORK_USERS
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1034
gidNumber: 1034
homeDirectory: /home/<USER>
gecos: network_users
userpassword: network_users_pwd

dn: cn=network_self, ou=who_test, dc=acme, dc=com
uid: network_self
cn: network_self
sn: NETWORK_SELF
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1035
gidNumber: 1035
homeDirectory: /home/<USER>
gecos: network_self
userpassword: network_self_pwd

dn: cn=network_subtree, ou=who_test, dc=acme, dc=com
uid: network_subtree
cn: network_subtree
sn: NETWORK_SUBTREE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1036
gidNumber: 1036
homeDirectory: /home/<USER>
gecos: network_subtree
userpassword: network_subtree_pwd

dn: cn=network_base, ou=who_test, dc=acme, dc=com
uid: network_base
cn: network_base
sn: NETWORK_BASE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1037
gidNumber: 1037
homeDirectory: /home/<USER>
gecos: network_base
userpassword: network_base_pwd

dn: cn=network_one, ou=who_test, dc=acme, dc=com
uid: network_one
cn: network_one
sn: NETWORK_ONE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1038
gidNumber: 1038
homeDirectory: /home/<USER>
gecos: network_one
userpassword: network_one_pwd

dn: cn=network_group, ou=who_test, dc=acme, dc=com
uid: network_group
cn: network_group
sn: NETWORK_GROUP
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1039
gidNumber: 1039
homeDirectory: /home/<USER>
gecos: network_group
userpassword: network_group_pwd

dn: cn=encrypt_everyone, ou=who_test, dc=acme, dc=com
uid: encrypt_everyone
cn: encrypt_everyone
sn: ENCRYPT_EVERYONE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1040
gidNumber: 1040
homeDirectory: /home/<USER>
gecos: encrypt_everyone
userpassword: encrypt_everyone_pwd

dn: cn=encrypt_anonymous, ou=who_test, dc=acme, dc=com
uid: encrypt_anonymous
cn: encrypt_anonymous
sn: ENCRTYP_ANONYMOUS
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1041
gidNumber: 1041
homeDirectory: /home/<USER>
gecos: encrypt_anonymous
userpassword: encrypt_anonymous_pwd

dn: cn=encrypt_users, ou=who_test, dc=acme, dc=com
uid: encrypt_users
cn: encrypt_users
sn: ENCRYPT_USERS
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1042
gidNumber: 1042
homeDirectory: /home/<USER>
gecos: encrypt_users
userpassword: encrypt_users_pwd

dn: cn=encrypt_self, ou=who_test, dc=acme, dc=com
uid: encrypt_self
cn: encrypt_self
sn: ENCRYPT_SELF
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1043
gidNumber: 1043
homeDirectory: /home/<USER>
gecos: encrypt_self
userpassword: encrypt_self_pwd

dn: cn=encrypt_subtree, ou=who_test, dc=acme, dc=com
uid: encrypt_subtree
cn: encrypt_subtree
sn: ENCRYPT_SUBTREE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1044
gidNumber: 1044
homeDirectory: /home/<USER>
gecos: encrypt_subtree
userpassword: encrypt_subtree_pwd

dn: cn=encrypt_base, ou=who_test, dc=acme, dc=com
uid: encrypt_base
cn: encrypt_base
sn: ENCRYPT_BASE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1045
gidNumber: 1045
homeDirectory: /home/<USER>
gecos: encrypt_base
userpassword: encrypt_base_pwd

dn: cn=encrypt_one, ou=who_test, dc=acme, dc=com
uid: encrypt_one
cn: encrypt_one
sn: ENCRYPT_ONE
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1046
gidNumber: 1046
homeDirectory: /home/<USER>
gecos: encrypt_one
userpassword: encrypt_one_pwd

dn: cn=encrypt_group, ou=who_test, dc=acme, dc=com
uid: encrypt_group
cn: encrypt_group
sn: ENCRYPT_GROUP
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1047
gidNumber: 1047
homeDirectory: /home/<USER>
gecos: encrypt_group
userpassword: encrypt_group_pwd

dn: cn=add_priv_test, ou=add, ou=priv_test, dc=acme, dc=com
uid: add_priv_test
cn: add_priv_test
sn: ADD_PRIV_TEST
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1048
gidNumber: 1048
homeDirectory: /home/<USER>
gecos: add_priv_test
userpassword: add_priv_test_pwd

dn: cn=add, ou=people, ou=priv_test, dc=acme, dc=com
uid: add
cn: add
sn: ADD
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1049
gidNumber: 1049
homeDirectory: /home/<USER>
gecos: add
userpassword: add_pwd

dn: cn=compare_priv_test, ou=priv_test, dc=acme, dc=com
uid: compare_priv_test
cn: compare_priv_test
sn: COMPARE_PRIV_TEST
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1050
gidNumber: 1050
homeDirectory: /home/<USER>
gecos: compare_priv_test
userpassword: compare_priv_test

dn: cn=compare, ou=people, ou=priv_test, dc=acme, dc=com
uid: compare
cn: compare
sn: ADMIN
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1051
gidNumber: 1051
homeDirectory: /home/<USER>
gecos: compare
userpassword: compare_pwd

dn: cn=modify_priv_test, ou=priv_test, dc=acme, dc=com
uid: modify_priv_test
cn: modify_priv_test
sn: MODIFY_PRIV_TEST
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1052
gidNumber: 1052
homeDirectory: /home/<USER>
gecos: modify_priv_test
userpassword: modify_priv_test_pwd

dn: cn=modify, ou=people, ou=priv_test, dc=acme, dc=com
uid: modify
cn: modify
sn: MODIFY
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1053
gidNumber: 1053
homeDirectory: /home/<USER>
gecos: modify
userpassword: modify_pwd

dn: cn=old_modrdn, ou=priv_test, dc=acme, dc=com
uid: old_modrdn
cn: old_modrdn
sn: OLD_MODRDN
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1054
gidNumber: 1054
homeDirectory: /home/<USER>
gecos: old_modrdn
userpassword: old_modrdn_pwd

dn: cn=modrdn, ou=people, ou=priv_test, dc=acme, dc=com
uid: modrdn
cn: modrdn
sn: MODRDN
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1055
gidNumber: 1055
homeDirectory: /home/<USER>
gecos: modrdn
userpassword: modrdn_pwd

dn: cn=old_modrdn_del, ou=priv_test, dc=acme, dc=com
uid: old_modrdn_del
cn: old_modrdn_del
sn: OLD_MODRDN_DEL
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1057
gidNumber: 1057
homeDirectory: /home/<USER>
gecos: old_modrdn_del
userpassword: old_modrdn_del_pwd

dn: cn=modrdn_del, ou=people, ou=priv_test, dc=acme, dc=com
uid: modrdn_del
cn: modrdn_del
sn: MODRDN_DEL
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1058
gidNumber: 1058
homeDirectory: /home/<USER>
gecos: modrdn_del
userpassword: modrdn_del_pwd

dn: cn=everything_attr_test_user, ou=other, dc=acme, dc=com
uid: everything_attr_test_user
cn: everything_attr_test_user
sn: EVERYTHING_ATTR_TEST_USER
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1060
gidNumber: 1060
homeDirectory: /home/<USER>
gecos: everything_attr_test_user
userpassword: everything_attr_test_user_pwd

dn: cn=everything_not_attr_test_user, ou=other, dc=acme, dc=com
uid: everything_not_attr_test_user
cn: everything_not_attr_test_user
sn: EVERYTHING_NOT_ATTR_TEST_USER
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: top
objectClass: shadowAccount
loginShell: /bin/bash
uidNumber: 1061
gidNumber: 1061
homeDirectory: /home/<USER>
gecos: everything_not_attr_test_user
userpassword: everything_not_attr_test_user_pwd

