
include "/tmp/tsig.key";

options {
	zone-statistics yes;
	directory "/tmp";
	version none;
	hostname none;
	recursion yes;
	listen-on { 127.0.0.1; ***********; };
	query-source address *********** port *; 
	notify-source *********** port *; 
	transfer-source ***********; 
	minimal-responses yes;
	max-cache-size 104857600;
	# for service restart: allow_bulkhost_ddns = Refusal
	allow-transfer { !any; };
        recursive-clients 800;
	transfer-format many-answers;
};

# Worker threads: default

# Bulk Host Name Templates:
#	Four Octets: 		"-$1-$2-$3-$4" (Default)
#	One Octet: 		"-$4"
#	Three Octets: 		"-$2-$3-$4"
#	Two Octets: 		"-$3-$4"

include "/tmp/dhcp_updater.key";

include "/tmp/rndc.key";

controls {
	inet 127.0.0.1 port 953
	allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	 channel ib_syslog { 
		 syslog daemon; 
		 severity info; 
	};
	 category default { ib_syslog; };
};
# default
view "_default" {  # default
    match-clients { *******; any; };
    zone "." in {
	type slave;
	masters { *******; };
	allow-update-forwarding { key DHCP_UPDATER;  none; };
	file "db..._default";
	notify explicit;
    };
    zone "0.0.127.in-addr.arpa" in {
	type master;
    };
};
