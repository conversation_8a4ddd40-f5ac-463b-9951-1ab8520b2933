$ORIGIN naptr.test.
$TTL 3600

@ IN SOA naptr.test. root.test.com. 1 3600 7200 2592000 43200
  IN NS dummy.naptr.test.

empty IN NAPTR 0 0 "" "" "" .

flags_a1 IN NAPTR 100 10 "a" "" "" .
flags_a2 IN NAPTR 100 10 "A" "" "" .
flags_p1 IN NAPTR 100 10 "p" "" "" .
flags_p2 IN NAPTR 100 10 "P" "" "" .
flags_s1 IN NAPTR 100 10 "s" "" "" .
flags_s2 IN NAPTR 100 10 "S" "" "" .
flags_u1 IN NAPTR 100 10 "u" "" "" .
flags_u2 IN NAPTR 100 10 "U" "" "" .

protocol_only IN NAPTR 100 10 "" http "" .

;; Examples from RFC29215 (with backslashes escaped)
rfc1 IN NAPTR 100 10  ""  ""  "/urn:cid:.+@([^\\.]+\\.)(.*)$/\\2/i"  .
rfc2 IN NAPTR 100 50  "s"  "z3950+I2L+I2C"  ""  _z3950._tcp.gatech.edu.
rfc3 IN NAPTR 100 50  "s"  "rcds+I2C"  ""  _rcds._udp.gatech.edu.
rfc4 IN NAPTR 100 50  "s" "http+I2L+I2C+I2R"  ""  _http._tcp.gatech.edu.
rfc5 IN NAPTR 100 90  ""   "" "!http://([^/:]+)!\\1!i"  .
rfc6 IN NAPTR 100 100  "s"  "http+I2R"  ""  _http._tcp.foo.com.
rfc7 IN NAPTR 100 100  "s"  "ftp+I2R"  ""  _ftp._tcp.foo.com.
rfc8 IN NAPTR 100 10  "u"  "sip+E2U"  "!^.*$!sip:<EMAIL>!"  .
rfc9 IN NAPTR 102 10  "u"  "mailto+E2U"  "!^.*$!mailto:<EMAIL>!"  .

regexp_special IN NAPTR 50 25 "" "" "!@#$%^&*()-_=+[{]}\\|;:'\",<.>/?" .
