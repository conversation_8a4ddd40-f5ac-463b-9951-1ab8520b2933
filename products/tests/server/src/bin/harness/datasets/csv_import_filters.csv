"Header-DhcpMacFilter","name","comment","never_expires","expiration_interval","enforce_expiration_time",,
"DhcpMacFilter","macfilter1","comment for macfilter1",FALSE,10000,TRUE,,
Header-NacFilter,name,comment,,,,,
NacFilter,nacfilter1,comment for nacfilter,,,,,
Header-OptionFilter,name,comment,lease_time,boot_server,option_space,next_server,pxe_lease_time,expression
OptionFilter,optionfilter1,comment1,40,1.1.1.1,DHCP,*******,100,"(option domain-name=""test.com"")"
Header-RelayAgentFilter,name,comment,circuit_id,circuit_id_rule,remote_id,remote_id_rule,
RelayAgentFilter,82filter,comment1,,ANY,10,MATCHES_VALUE,
<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,comment,parent,is_registered_user,guest_custom_field4,guest_custom_field2,guest_custom_field3,guest_phone,guest_custom_field1,expire_time,guest_middle_name,never_expires,guest_email,registered_user,guest_first_name,guest_last_name,mac_address
MacFilterAddress,comment,macfilter1,FALSE,field4,field2,field3,4085553333,field1,2010-09-16T18:40:00Z,middle,FALSE,<EMAIL>,user1,first,last,00:01:02:03:04:05
header-ipv6optionfilter,name*,_new_name,comment,expression,lease_time,option_space,OPTION-DHCPv6-16,OPTION-DHCPv6-27
ipv6optionfilter,ipv6optionfilter1,,this is a ipv6_filter,"(option dhcp6.fqdn=""test.com"")",,DHCPv6,,ee90::2
