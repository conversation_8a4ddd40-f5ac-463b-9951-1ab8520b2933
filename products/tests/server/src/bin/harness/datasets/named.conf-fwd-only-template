// Inspired by examples in chapter 3 of the BIND ARM

key "key1" {
  algorithm hmac-md5;
  secret "CdhtOnXxkZeuHP5saggSnA==";
};

key "key2" {
  algorithm hmac-md5;
  secret "MxuDAibo+rXNf9Cp9I4V6g==";
};

acl "loopbacknet" { 127.0.0.0/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";
	recursion no;			// Do not provide recursive service
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

view "another_view" {
    match-clients { key "key1"; key "key2"; };
    zone "bulk.infoblox.com" {
        type master;
        notify no;
        file "bulk.infoblox.com.db-another_view";
    };
    zone "2.168.192.in-addr.arpa" {
	type master;
	notify no;
	file "2.168.192.in-addr.arpa-another_view";
	allow-update { "loopbacknet"; };
    };
};

view "VIEW_NAME" {

// Root server hints
zone "." { type hint; file "root.hint"; };

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	file "localhost.rev";
	notify no;
};

zone "test.infoblox.com" {
	type master;
	notify no;
	file "test.infoblox.com.db";
	allow-update { "loopbacknet"; };
};

zone " test escape .infoblox.com" {
	type master;
	notify no;
	file "test-escape.infoblox.com.db";
	allow-update { "loopbacknet"; };
};

zone "b0003.grainger.com" {
	type master;
// Don't notify; the NS records are probably bogus for this test zone
	notify no;
	file "b0003.grainger.com";
};

zone "bulk.infoblox.com" {
        type master;
        notify no;
        file "bulk.infoblox.com.db";
};

zone "apo.infoblox.com" {
	type master;
	notify no;
	file "apo.infoblox.com.db";
};

};
