# dhcpd conf file for dhcp_option82.sh
local-address 127.0.0.1;
server-identifier 127.0.0.1;
ddns-update-style interim;
not authoritative;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
log-facility daemon;
ping-check false;

ddns-updates off;
ignore client-updates;

class "ra1" {
   match if ( option agent.circuit-id = "************ atm 3/3:8.36") and
            ( option agent.remote-id =
              30:30);
            }

class "circuit-id-class" {
	match option agent.circuit-id;
	}
subclass "circuit-id-class" "ra2" 30:31:2e:31:31:32:2e:30:2e:32:32:37:20:61:74:6d:20:33:2f:33:3a:38:2e:33:36;
#   option agent.circuit-id = "************ atm 3/3:8.36"

class "SubstringTestClass" {
    match if (substring (option agent.circuit-id, 1, 2) = ee:ee) and (substring (option agent.remote-id, 0, 2) = ff:ff);
}

class "has_relay_info" {
	match if (exists relay-agent-information);
	option domain-name-servers 127.0.0.127;
}

subnet ********* netmask ********* {
	if (exists relay-agent-information) {
		# Option filter "has_relay_info"
		option domain-name-servers 127.0.0.127;
	}
        pool {
                infoblox-range 12******** **********;
                range 12******** **********;
                range ********** **********;
                allow members of "ra1";
        }
        pool {
                infoblox-range ********** **********;
                range ********** **********;
                allow members of "ra2";
        }
        pool {
                infoblox-range ********** **********;
                range ********** **********;
                allow members of "SubstringTestClass";
        }
        host fixed-host-1 {
                dynamic;
                fixed-address **********;
                hardware ethernet 18:18:18:18:18:18;
                option routers *********;
                option log-servers *******;
        }
        host fixed-host-2 {
                dynamic;
                fixed-address **********;
                hardware ethernet 99:99:99:99:99:99;
        }
        host fixed-host-3 {
                dynamic;
                fixed-address **********;
                hardware ethernet 20:20:20:20:20:20;
        }
}
