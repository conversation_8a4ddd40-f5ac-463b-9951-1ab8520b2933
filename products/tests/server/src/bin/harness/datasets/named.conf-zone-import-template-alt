// Inspired by examples in chapter 3 of the BIND ARM

acl "loopbacknet" { *********/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";
	recursion no;			// Do not provide recursive service
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

view "VIEW_NAME" {

// Root server hints
zone "." { type hint; file "root.hint"; };

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	file "localhost.rev";
	notify no;
};

zone "test.infoblox.com" {
	type master;
	notify no;
	file "test.infoblox.com.db-zone-import";
	allow-update { "loopbacknet"; };
};

zone "2.168.192.in-addr.arpa" {
	type master;
	notify no;
	file "db-alt.192.168.2";
	allow-update { "loopbacknet"; };
};

zone "bulk.infoblox.com" {
        type master;
        notify no;
        file "bulk.infoblox.com.db-alt";
};

};
