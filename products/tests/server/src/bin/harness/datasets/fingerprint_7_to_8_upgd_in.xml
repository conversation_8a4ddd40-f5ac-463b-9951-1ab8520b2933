<DATABASE NAME="onedb" VERSION="MDXMLTEST">
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.lease"/>
    <PROPERTY NAME="fingerprint" VALUE="Microsoft Windows XP"/>
    <PROPERTY NAME="node_id" VALUE="0"/>
    <PROPERTY NAME="ip_address" VALUE="********"/>
    <PROPERTY NAME="network_view" VALUE="0"/>
    <PROPERTY NAME="ms_server_id" VALUE="."/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.lease_event"/>
    <PROPERTY NAME="fingerprint" VALUE="Apple Mac OS X, TV (HD)"/>
    <PROPERTY NAME="timestamp" VALUE=""/>
    <PROPERTY NAME="event" VALUE="Issued"/>
    <PROPERTY NAME="ip_address" VALUE="********"/>
    <PROPERTY NAME="member_ip" VALUE="********"/>
    <PROPERTY NAME="network_view" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.dhcp_host"/>
    <PROPERTY NAME="fingerprint" VALUE="Apple Airport"/>
    <PROPERTY NAME="node_id" VALUE="0"/>
    <PROPERTY NAME="name" VALUE="finger.print.test.host"/>
    <PROPERTY NAME="network_view" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_mac_filterset_item"/>
    <PROPERTY NAME="fingerprint" VALUE="Cisco Catalyst 35xx/ASA"/>
    <PROPERTY NAME="dhcp_mac_filter" VALUE=".com.infoblox.dns.dhcp_mac_filter$name.filter"/>
    <PROPERTY NAME="mac_address" VALUE="11:22:33:44:55:66"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.smart_folder_query_item"/>
    <PROPERTY NAME="value" VALUE="Palm OS"/>
    <PROPERTY NAME="parent" VALUE=".com.infoblox.one.personal_smart_folder$.com.infoblox.one.admin$admin.ARTO"/>
    <PROPERTY NAME="position" VALUE="0"/>
    <PROPERTY NAME="name" VALUE="fingerprint"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.smart_folder_query_item"/>
    <PROPERTY NAME="value" VALUE="Apple Mac OS 9"/>
    <PROPERTY NAME="parent" VALUE=".com.infoblox.one.personal_smart_folder$.com.infoblox.one.admin$admin.ARTO"/>
    <PROPERTY NAME="position" VALUE="1"/>
    <PROPERTY NAME="name" VALUE="fingerprint_class"/>
  </OBJECT>
</DATABASE>
