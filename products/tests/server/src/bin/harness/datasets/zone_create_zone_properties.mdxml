<MDXML>

<STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone"
    POST-STRUCT-CALLBACK="create_zone_properties">
    <REMOVE-MEMBER member-name="import_zone_address"/>
    <REMOVE-MEMBER member-name="import_zone_option"/>
    <REMOVE-MEMBER member-name="maximum_ttl"/>
    <REMOVE-MEMBER member-name="override_global_system_name"/>
    <REMOVE-MEMBER member-name="disable_all_subzones"/>
    <REMOVE-MEMBER member-name="active_directory_option"/>
    <REMOVE-MEMBER member-name="override_cluster_ddns_updates"/>
    <REMOVE-MEMBER member-name="allow_ddns_updates"/>
    <REMOVE-MEMBER member-name="notify"/>
    <REMOVE-MEMBER member-name="underscore_zone"/>
    <REMOVE-MEMBER member-name="allow_gss_tsig_for_underscore_zone"/>
    <REMOVE-MEMBER member-name="zone_transfer_list_option"/>
    <REMOVE-MEMBER member-name="forwarding_disabled"/>
    <REMOVE-MEMBER member-name="zone_qal_option"/>
    <REMOVE-MEMBER member-name="override_update_forwarding"/>
    <REMOVE-MEMBER member-name="allow_update_forwarding"/>
    <REMOVE-MEMBER member-name="auto_created_AD_zone"/>
    <REMOVE-MEMBER member-name="allow_gss_tsig_zone_updates"/>
    <REMOVE-MEMBER member-name="record_name_policy"/>
    <REMOVE-MEMBER member-name="override_record_name_policy"/>
    <REMOVE-MEMBER member-name="check_names_for_ddns_and_zone_transfer"/>
    <REMOVE-MEMBER member-name="check_names_policy"/>
    <REMOVE-MEMBER member-name="use_delegated_ttl"/>
    <REMOVE-MEMBER member-name="delegated_ttl"/>
    <REMOVE-MEMBER member-name="override_notify_delay"/>
    <REMOVE-MEMBER member-name="notify_delay"/>
    <REMOVE-MEMBER member-name="forwarders_only"/>
    <REMOVE-MEMBER member-name="dnssec_override_key_parameters"/>
    <REMOVE-MEMBER member-name="dnssec_signature_expiration"/>
    <REMOVE-MEMBER member-name="dnssec_ksk_rollover_interval"/>
    <REMOVE-MEMBER member-name="dnssec_zsk_rollover_interval"/>
    <REMOVE-MEMBER member-name="dnssec_ksk_current_rollover_interval"/>
    <REMOVE-MEMBER member-name="dnssec_zsk_current_rollover_interval"/>
    <REMOVE-MEMBER member-name="dnssec_previous_gm_role"/>
    <REMOVE-MEMBER member-name="ms_ad_integrated"/>
    <REMOVE-MEMBER member-name="ms_ddns_mode"/>
    <REMOVE-MEMBER member-name="ms_allow_transfer_mode"/>
    <REMOVE-MEMBER member-name="ms_last_sync_time"/>
    <REMOVE-MEMBER member-name="ms_last_sync_status"/>
    <REMOVE-MEMBER member-name="ms_last_sync_failure_count"/>
    <REMOVE-MEMBER member-name="ms_synchronization_id"/>
    <REMOVE-MEMBER member-name="ms_sync_retry_offset"/>
    <REMOVE-MEMBER member-name="ms_last_sync_detail"/>
    <REMOVE-MEMBER member-name="ms_ad_zone_dn"/>
    <REMOVE-MEMBER member-name="next_secure_type"/>
    <REMOVE-MEMBER member-name="effective_next_secure_type"/>
    <REMOVE-MEMBER member-name="enable_rfc2317_exclusion"/>
    <REMOVE-MEMBER member-name="zone_not_queried_enabled_time"/>
    <REMOVE-MEMBER member-name="rr_not_queried_enabled_time"/>
    <REMOVE-MEMBER member-name="rpz_severity"/>
    <REMOVE-MEMBER member-name="rpz_policy"/>
    <REMOVE-MEMBER member-name="substitute_name"/>
    <REMOVE-MEMBER member-name="use_copy_xfer_to_notify"/>
    <REMOVE-MEMBER member-name="copy_xfer_to_notify"/>
    <REMOVE-MEMBER member-name="dns_integrity_check_member"/>
    <REMOVE-MEMBER member-name="dns_integrity_check_frequency"/>
    <REMOVE-MEMBER member-name="dns_integrity_check_verbose_logging"/>
    <REMOVE-MEMBER member-name="dnssec_nsec3_salt_min_length"/>
    <REMOVE-MEMBER member-name="dnssec_nsec3_salt_max_length"/>
    <REMOVE-MEMBER member-name="dnssec_nsec3_iterations"/>
    <REMOVE-MEMBER member-name="dnssec_zsk_rollover_mechanism"/>
    <REMOVE-MEMBER member-name="dnssec_enable_ksk_auto_rollover"/>
    <REMOVE-MEMBER member-name="dnssec_ksk_rollover_notification_config"/>
    <REMOVE-MEMBER member-name="dnssec_ksk_snmp_notification_enabled"/>
    <REMOVE-MEMBER member-name="dnssec_ksk_email_notification_enabled"/>
    <REMOVE-MEMBER member-name="dnssec_rollover_switching_way"/>
</STRUCTURE-TRANSFORM>
   <NEW-TYPES>
      <TYPE NAME=".com.infoblox.dns.zone_properties"/>
   </NEW-TYPES>

</MDXML>

