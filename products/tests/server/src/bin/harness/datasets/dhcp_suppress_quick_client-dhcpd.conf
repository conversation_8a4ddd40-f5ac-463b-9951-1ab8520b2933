# dhcpd conf file for dhcp_suppress_quick_client.sh
local-address 127.0.0.1;
server-identifier 127.0.0.1;
ddns-update-style interim;
not authoritative;
default-lease-time 80;
min-lease-time 80;
max-lease-time 80;
log-facility daemon;
ping-check false;

ddns-updates off;
ignore client-updates;

# Allocate leases from the loopback network
subnet ********* netmask ********* {
        pool {
                infoblox-range ********** **********;
                range ********** **********;
        }
}
