<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
   <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dns_properties"/>
   <PROPERTY NAME="forwarders_only" VALUE="false"/>
   <PROPERTY NAME="limit_recursive_clients" VALUE="false"/>
   <PROPERTY NAME="numberof_recursive_clients" VALUE="1000"/>
   <PROPERTY NAME="override_cluster_sortlist" VALUE="false"/>
   <PROPERTY NAME="override_cluster_transfer_list" VALUE="false"/>
   <PROPERTY NAME="override_cluster_transfer_format" VALUE="false"/>
   <PROPERTY NAME="override_cluster_qacl" VALUE="false"/>
   <PROPERTY NAME="override_cluster_rqacl" VALUE="false"/>
   <PROPERTY NAME="override_cluster_ddns_updaters" VALUE="false"/>
   <PROPERTY NAME="override_cluster_forwarders" VALUE="false"/>
   <PROPERTY NAME="recursion_enabled" VALUE="false"/>
   <PROPERTY NAME="lame_ttl" VALUE="600"/>
   <PROPERTY NAME="use_lame_ttl" VALUE="false"/>
   <PROPERTY NAME="zone_transfer_format_option" VALUE="MANY_ANSWERS"/>
   <PROPERTY NAME="minimal_response" VALUE="true"/>
   <PROPERTY NAME="override_cluster_update_forwarding" VALUE="false"/>
   <PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
   <PROPERTY NAME="override_cluster_facility" VALUE="false"/>
   <PROPERTY NAME="facility" VALUE="daemon"/>
   <PROPERTY NAME="override_cluster_logging" VALUE="false"/>
   <PROPERTY NAME="log_general" VALUE="true"/>
   <PROPERTY NAME="log_client" VALUE="true"/>
   <PROPERTY NAME="log_config" VALUE="true"/>
   <PROPERTY NAME="log_database" VALUE="true"/>
   <PROPERTY NAME="log_dnssec" VALUE="true"/>
   <PROPERTY NAME="log_lame_servers" VALUE="true"/>
   <PROPERTY NAME="log_network" VALUE="true"/>
   <PROPERTY NAME="log_notify" VALUE="true"/>
   <PROPERTY NAME="log_queries" VALUE="false"/>
   <PROPERTY NAME="log_resolver" VALUE="true"/>
   <PROPERTY NAME="log_security" VALUE="true"/>
   <PROPERTY NAME="log_update" VALUE="true"/>
   <PROPERTY NAME="log_xfer_in" VALUE="true"/>
   <PROPERTY NAME="log_xfer_out" VALUE="true"/>
   <PROPERTY NAME="log_update_security" VALUE="true"/>
   <PROPERTY NAME="log_rpz" VALUE="false"/>
   <PROPERTY NAME="log_responses" VALUE="false"/>
   <PROPERTY NAME="log_query_rewrite" VALUE="false"/>
   <PROPERTY NAME="auto_sort_views" VALUE="false"/>
   <PROPERTY NAME="use_nat_address" VALUE="0"/>
   <PROPERTY NAME="dns_over_mgmt" VALUE="false"/>
   <PROPERTY NAME="notify_xfr_source" VALUE="VIP"/>
   <PROPERTY NAME="query_source" VALUE="VIP"/>
   <PROPERTY NAME="dns_over_lan2" VALUE="false"/>
   <PROPERTY NAME="auto_create_a_and_ptr_for_lan2" VALUE="true"/>
   <PROPERTY NAME="use_enable_gss_tsig" VALUE="false"/>
   <PROPERTY NAME="enable_gss_tsig" VALUE="false"/>
   <PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
   <PROPERTY NAME="override_cluster_notify_query_sport" VALUE="false"/>
   <PROPERTY NAME="notify_source_port_enabled" VALUE="false"/>
   <PROPERTY NAME="query_source_port_enabled" VALUE="false"/>
   <PROPERTY NAME="override_record_name_policy" VALUE="false"/>
   <PROPERTY NAME="named_worker_threads" VALUE="0"/>
   <PROPERTY NAME="override_cluster_root_server" VALUE="false"/>
   <PROPERTY NAME="use_root_name_servers" VALUE="false"/>
   <PROPERTY NAME="check_names_policy" VALUE="1"/>
   <PROPERTY NAME="override_dnssec" VALUE="false"/>
   <PROPERTY NAME="dnssec_enabled" VALUE="false"/>
   <PROPERTY NAME="dnssec_validation_enabled" VALUE="true"/>
   <PROPERTY NAME="dnssec_expired_signatures_enabled" VALUE="false"/>
   <PROPERTY NAME="override_blackhole" VALUE="false"/>
   <PROPERTY NAME="blackhole_enabled" VALUE="false"/>
   <PROPERTY NAME="gsstsig_key_expiration_time" VALUE="3600"/>
   <PROPERTY NAME="override_transfers_out" VALUE="false"/>
   <PROPERTY NAME="transfers_out" VALUE="10"/>
   <PROPERTY NAME="override_notify_delay" VALUE="false"/>
   <PROPERTY NAME="notify_delay" VALUE="5"/>
   <PROPERTY NAME="nxdomain_redirect_override" VALUE="false"/>
   <PROPERTY NAME="nxdomain_redirect_enabled" VALUE="false"/>
   <PROPERTY NAME="nxdomain_redirect_ttl" VALUE="60"/>
   <PROPERTY NAME="nxdomain_log_query" VALUE="false"/>
   <PROPERTY NAME="blacklist_override" VALUE="false"/>
   <PROPERTY NAME="blacklist_enabled" VALUE="false"/>
   <PROPERTY NAME="blacklist_action" VALUE="REDIRECT"/>
   <PROPERTY NAME="blacklist_redirect_ttl" VALUE="60"/>
   <PROPERTY NAME="blacklist_log_query" VALUE="false"/>
   <PROPERTY NAME="always_ret_nxdomain_for_fmz_ptr" VALUE="false"/>
   <PROPERTY NAME="enable_dns64" VALUE="false"/>
   <PROPERTY NAME="use_dns64" VALUE="false"/>
   <PROPERTY NAME="override_dns_cache_ttl" VALUE="false"/>
   <PROPERTY NAME="dns_cache_ttl" VALUE="1"/>
   <PROPERTY NAME="enable_dns_cache_acceleration" VALUE="false"/>
   <PROPERTY NAME="dns_over_v6_lan2" VALUE="false"/>
   <PROPERTY NAME="dns_over_v6_mgmt" VALUE="false"/>
   <PROPERTY NAME="v6_auto_create_lan2_glue" VALUE="false"/>
   <PROPERTY NAME="override_filter_aaaa" VALUE="false"/>
   <PROPERTY NAME="filter_aaaa" VALUE="NO"/>
   <PROPERTY NAME="dns_over_v6_lan" VALUE="false"/>
   <PROPERTY NAME="use_copy_xfer_to_notify" VALUE="false"/>
   <PROPERTY NAME="copy_xfer_to_notify" VALUE="false"/>
   <PROPERTY NAME="transfers_in" VALUE="10"/>
   <PROPERTY NAME="use_transfers_in" VALUE="false"/>
   <PROPERTY NAME="transfers_per_ns" VALUE="2"/>
   <PROPERTY NAME="use_transfers_per_ns" VALUE="false"/>
   <PROPERTY NAME="serial_query_rate" VALUE="20"/>
   <PROPERTY NAME="use_serial_query_rate" VALUE="false"/>
   <PROPERTY NAME="override_max_cached_lifetime" VALUE="false"/>
   <PROPERTY NAME="max_cached_lifetime" VALUE="86400"/>
   <PROPERTY NAME="use_max_cache_ttl" VALUE="false"/>
   <PROPERTY NAME="max_cache_ttl" VALUE="604800"/>
   <PROPERTY NAME="use_max_ncache_ttl" VALUE="false"/>
   <PROPERTY NAME="max_ncache_ttl" VALUE="10800"/>
   <PROPERTY NAME="use_disable_edns" VALUE="false"/>
   <PROPERTY NAME="disable_edns" VALUE="false"/>
   <PROPERTY NAME="use_query_rewrite" VALUE="false"/>
   <PROPERTY NAME="query_rewrite_enabled" VALUE="false"/>
   <PROPERTY NAME="dnssec_blacklist_enabled" VALUE="false"/>
   <PROPERTY NAME="dnssec_nxdomain_enabled" VALUE="false"/>
   <PROPERTY NAME="dnssec_rpz_enabled" VALUE="false"/>
   <PROPERTY NAME="resolver_query_timeout" VALUE="0"/>
   <PROPERTY NAME="dns_resolver_type" VALUE="BIND"/>
   <PROPERTY NAME="use_auto_blackhole" VALUE="false"/>
   <PROPERTY NAME="use_rpz_disable_nsdname_nsip" VALUE="false"/>
   <PROPERTY NAME="rpz_disable_nsdname_nsip" VALUE="false"/>
   <PROPERTY NAME="dns_over_lan" VALUE="true"/>
   <PROPERTY NAME="log_idns_gslb" VALUE="false"/>
   <PROPERTY NAME="log_idns_health" VALUE="false"/>
   <PROPERTY NAME="idns_health_source" VALUE="VIP"/>
   <PROPERTY NAME="max_recursion_depth" VALUE="7"/>
   <PROPERTY NAME="max_recursion_queries" VALUE="150"/>
   <PROPERTY NAME="virtual_node" VALUE="0"/>
   <PROPERTY NAME="use_gss_tsig_keys" VALUE="false"/>
   <PROPERTY NAME="check_names_for_ddns_and_zone_transfer" VALUE="false"/>
   <PARTITION-MAP VALUE="010+"/>
</OBJECT>
</DATABASE>
