ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/storage/tmp/dhcp_updater.key";

subnet 20.0.0.0 netmask ********* {
	host ******** {
                dynamic;
                hardware ethernet aa:aa:aa:aa:aa:aa;
                fixed-address ********;
        }
	pool {
		infoblox-range ******** ********;
		allow known-clients;
		allow unknown-clients;
		range ******** ********;
	}
	pool {
		infoblox-range ********* ********0;
		allow known-clients;
		range ********* ********0;
	}
	pool {
		infoblox-range ********1 ********5;
		deny unknown-clients;
		range ********1 ********5;
	}
	pool {
		infoblox-range ********* *********;
		deny all clients;
		allow known-clients;
		allow unknown-clients;
		range ********* *********;
	}
}

subnet *********** netmask ************* {
	not authoritative;
}

#End of dhcpd.conf file
