
include "/infoblox/var/named_conf/tsig.key";

options {
	zone-statistics yes;
	directory "/infoblox/var/named_conf";
	version none;
	hostname none;
	recursion no;
	listen-on { 127.0.0.1; ***********; };
	query-source address  ***********;
	transfer-source ***********;
	notify-source ***********;
	minimal-responses yes;
	# for service restart: allow_bulkhost_ddns = Refusal
	allow-transfer { *******; *******; key "k1"; key "k2"; };
	transfer-format many-answers;
};

# Worker threads: default

# Bulk Host Name Templates:
#	Four Octets: 		"-$1-$2-$3-$4" (Default)
#	One Octet: 		"-$4"
#	Three Octets: 		"-$2-$3-$4"
#	Two Octets: 		"-$3-$4"

include "/infoblox/var/named_conf/dhcp_updater.key";

include "/infoblox/var/named_conf/rndc.key";

controls {
	inet 127.0.0.1 port 953
	allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	 channel ib_syslog { 
		 syslog daemon; 
		 severity info; 
	};
	 category default { ib_syslog; };
};

acl all_dns_views_updater_keys {  key DHCP_UPDATER_default; };

# default
view "_default" {  # default
    match-clients { key DHCP_UPDATER_default; !all_dns_views_updater_keys; any; };
    zone "0.0.127.in-addr.arpa" in {
	type master;
    };
    zone "test.com" in {
	type master;
	allow-update { key "k3";  };
	notify yes;
    };
};
