<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
	<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
	<PROPERTY NAME="is_ipv4" VALUE="false"/>
	<PROPERTY NAME="space" VALUE="DHCPv6..false"/>
	<PROPERTY NAME="globally_enabled" VALUE="true"/>
	<PROPERTY NAME="code" VALUE="18"/>
	<PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
	<PROPERTY NAME="type" VALUE="string"/>
	<PROPERTY NAME="name" VALUE="dhcp6.interface-id"/>
</OBJECT>
<OBJECT>
	<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
	<PROPERTY NAME="is_ipv4" VALUE="false"/>
	<PROPERTY NAME="space" VALUE="DHCPv6..false"/>
	<PROPERTY NAME="globally_enabled" VALUE="true"/>
	<PROPERTY NAME="code" VALUE="37"/>
	<PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
	<PROPERTY NAME="type" VALUE="string"/>
	<PROPERTY NAME="name" VALUE="dhcp6.remote-id"/>
</OBJECT>
<OBJECT>
	<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
	<PROPERTY NAME="is_ipv4" VALUE="false"/>
	<PROPERTY NAME="space" VALUE="DHCPv6..false"/>
	<PROPERTY NAME="globally_enabled" VALUE="true"/>
	<PROPERTY NAME="code" VALUE="38"/>
	<PROPERTY NAME="option_space_type" VALUE="predefined-dhcp"/>
	<PROPERTY NAME="type" VALUE="string"/>
	<PROPERTY NAME="name" VALUE="dhcp6.subscriber-id"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.option_definition"/>
  <PROPERTY NAME="type" VALUE="array of ip-address"/>
  <PROPERTY NAME="is_ipv4" VALUE="true"/>
  <PROPERTY NAME="space" VALUE="DHCP Standard Options.0.false"/>
  <PROPERTY NAME="name" VALUE="LPR Servers"/>
  <PROPERTY NAME="code" VALUE="9"/>
  <PROPERTY NAME="globally_enabled" VALUE="false"/>
  <PROPERTY NAME="comment" VALUE="Array of RFC 1179 servers, by preference"/>
</OBJECT>
</DATABASE>
