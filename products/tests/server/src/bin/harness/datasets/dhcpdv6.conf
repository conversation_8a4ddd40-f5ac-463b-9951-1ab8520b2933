ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
ddns-local-address6 2010:12:29::2011;
local-address ***********;
ddns-local-address4 ***********;
prefix-length-mode exact;
# ddns txt record handling - isc_transitional
option dhcp6.preference 100;
option dhcp6.rapid-commit;
option dhcp6.info-refresh-time 111;
option dhcp6.nisp-servers 2011::2011;
option dhcp6.server-id 00:55;
option domain-name "test.com";
default-lease-time 43200;
preferred-lifetime 27000;
option dhcp6.name-servers ff:aa:bb::1, ff:aa:bb::2;
omapi-key DHCP_UPDATER;
omapi-port 7912;
ddns-updates on;
# DNS update retry interval: 5
allow client-updates;
ddns-ttl 9000;
ddns-domainname = pick ( option fqdn.domainname, "ddns.test.com" );
ddns-hostname = pick ( option fqdn.hostname,option host-name,
	concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
option host-name = config-option server.ddns-hostname;

include "/storage/tmp/dhcp_updater.key";

option space vendor6 code width 2 length width 2;
option vsio.vendor6 code 1045 = encapsulate vendor6;
option vendor6.name61 code 1 = string;
option vendor6.name62 code 2 = string;

group { # Start of Roaming Hosts
	host zroaming1 {
		host-identifier option
			dhcp6.client-id 00:01:cc:dd:ee:ff;
		option domain-name "tt.test.com";
		ddns-updates on;
		ddns-hostname "aaaa";
		ddns-domainname = pick ( option fqdn.domainname, "ff.aa.com" );
		option dhcp6.name-servers ff:aa:bb::3, ff:aa:bb::4;
		option dhcp6.fqdn "SharedNet_shared_net1";
	}
	host zroaming2 {
		host-identifier option
			dhcp6.client-id 00:01:ee:dd:ff;
		option domain-name "tt.test.com";
		ddns-updates on;
		ddns-hostname "bbbb";
		ddns-domainname = pick ( option fqdn.domainname, "ff.aa.com" );
		option dhcp6.name-servers ff:aa:bb::3, ff:aa:bb::4;
		option dhcp6.fqdn "SharedNet_shared_net1";
	}
} # End of Roaming Hosts

subnet6 10:10::/64 {
	ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
	ddns-hostname = pick ( option fqdn.hostname,option host-name,
		concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
	option host-name = config-option server.ddns-hostname;
}

subnet6 11:11::/64 {
	ddns-updates on;
	ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
	ddns-hostname = pick ( option fqdn.hostname,option host-name,
		concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
	option host-name = config-option server.ddns-hostname;
}

subnet6 12:12::/64 {
	option domain-name "localoverride.com";
	ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
	ddns-hostname = pick ( option fqdn.hostname,option host-name,
		concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
	option host-name = config-option server.ddns-hostname;
}

subnet6 13:13::/64 {
	ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
	ddns-hostname = pick ( option fqdn.hostname,option host-name,
		concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
	option host-name = config-option server.ddns-hostname;
	host 13_13__2 {
		host-identifier option
			dhcp6.client-id 00:01:aa:aa;
		fixed-address6 13:13::2;
		option domain-name "fixedaddress.com";
	}
	host 13_13__4 {
		ib-revision 1732056222;
		hardware ethernet 00:00:00:00:00:07;
		fixed-address6 13:13::4;
		option domain-name "fixedaddress.com";
	}
	host 13_13__3 {
                ib-revision 1666159667;
		host-identifier option
			dhcp6.client-id 00:02:bb:bb;
		fixed-address6 13:13::3;
	}
	host 13_13__5 {
		ib-revision 1732056222;
		hardware ethernet 00:00:00:00:00:08;
		fixed-address6 13:13::5;
	}
	pool6 {
		range6 13:13::4 13:13::6;
	}
	pool6 {
		range6 13:13::7 13:13::9;
	}
}

subnet6 16:16::/64 {
	option domain-name "ddnszone.com";
	ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
	ddns-hostname = pick ( option fqdn.hostname,option host-name,
		concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
	option host-name = config-option server.ddns-hostname;
}

subnet6 2010:12:29::/48 {
	ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
	ddns-hostname = pick ( option fqdn.hostname,option host-name,
		concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
	option host-name = config-option server.ddns-hostname;
	pool6 {
		prefix6 2010:12:29:: 2010:12:29:1:: /64;
	}
}

subnet6 2010:12:30::/48 {
	option dhcp6.name-servers ff:aa:bb::1, ff:aa:bb::2;
	option dhcp6.fqdn "Network_2010:12:30::";
	ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
	ddns-hostname = pick ( option fqdn.hostname,option host-name,
		concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
	option host-name = config-option server.ddns-hostname;
	host 2010_12_30__101 {
		host-identifier option
			dhcp6.client-id 00:01;
		fixed-address6 2010:12:30::101;
		default-lease-time 3600;
		option dhcp6.name-servers ff:aa:bb::1, ff:aa:bb::2;
		option dhcp6.fqdn "FixedAddr_2010:12:30::101";
		preferred-lifetime 3600;
	}
	host 2010_12_30__102 {
		ib-revision 1732056876;
		hardware ethernet 00:00:00:00:00:02;
		fixed-address6 2010:12:30::102;
		default-lease-time 3600;
		option dhcp6.name-servers ff:aa:bb::1, ff:aa:bb::2;
		option dhcp6.fqdn "FixedAddr_2010:12:30::102";
		preferred-lifetime 3600;
	}
	host 2010_12_30_0_100__ {
		host-identifier option
			dhcp6.client-id 00:02;
		fixed-prefix6 2010:12:30:0:100::/80;
	}
	host 2010_12_30_0_103__ {
		ib-revision 1732058596;
		hardware ethernet 00:00:00:00:00:03;
		fixed-prefix6 2010:12:30:0:103::/80;
	}
	host 2010_12_30__106 {
		ib-revision 1666159665;
		host-identifier option
			dhcp6.client-id 00:03;
		fixed-address6 2010:12:30::106;
		fixed-prefix6 2010:12:30:0:101::/80;
		option dhcp6.fqdn "I am enabled";
	}
	host 2010_12_30__107 {
		ib-revision 1732056876;
		hardware ethernet 00:00:00:00:00:04;
		fixed-address6 2010:12:30::107;
		fixed-prefix6 2010:12:30:0:104::/80;
		option dhcp6.fqdn "I am enabled";
	}
	pool6 {
		range6 2010:12:30::4 2010:12:30::14;
		range6 2010:12:30::16 2010:12:30::94;
		prefix6 2010:12:30:: 2010:12:30:1:: /64;
	}
}

subnet6 2010:12:31::/48 {
	ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
	ddns-hostname = pick ( option fqdn.hostname,option host-name,
		concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
	option host-name = config-option server.ddns-hostname;
	pool6 {
		range6 2010:12:31::1 2010:12:31::100;
	}
}

subnet6 ::10.0.0.0/104 {
	ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
	ddns-hostname = pick ( option fqdn.hostname,option host-name,
		concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
	option host-name = config-option server.ddns-hostname;
	host 0__******** {
		host-identifier option
			dhcp6.client-id 00:04;
		fixed-address6 ::********;
		option dhcp6.fqdn "I am disabled";
	}
	host 0__******** {
		ib-revision 1732053519;
		hardware ethernet 00:00:00:00:00:06;
		fixed-address6 ::********;
		option dhcp6.fqdn "I am disabled";
	}
	pool6 {
		range6 ::******** ::********;
	}
}

subnet6 2010:12:29::/64 {

}

shared-network "shared_net1" {
	option domain-name "ll.test.com";
	option dhcp6.name-servers ff:aa:bb::3, ff:aa:bb::4;
	option dhcp6.fqdn "SharedNet_shared_net1";
	default-lease-time 7200;
	preferred-lifetime 3600;
	ddns-updates off;

	subnet6 2010:12:28::/48 {
		option domain-name "bb.test.com";
		ddns-updates on;
		ddns-domainname = pick ( option fqdn.domainname, config-option domain-name );
		ddns-hostname = pick ( option fqdn.hostname,option host-name,
			concat ("dhcp-",binary-to-ascii(16,16,"-", leased-address)));
		option host-name = config-option server.ddns-hostname;
		pool6 {
			prefix6 2010:12:28:: 2010:12:28:1:: /64;
		}
	}
}

shared-network "shared" {
	option domain-name "shared.com";

	subnet6 14:14::/64 {
	}
}

shared-network "shared2" {
	ddns-updates off;

	subnet6 15:15::/64 {
	}
}
zone "ddnszone.com." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
zone "0.0.0.0.0.0.0.0.*******.*******.ip6.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
zone "0.0.0.0.0.0.0.0.*******.*******.ip6.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
zone "0.0.0.*******.0.*******.*******.ip6.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
zone "0.0.0.0.*******.*******.*******.ip6.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
zone "0.0.0.0.*******.*******.*******.ip6.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
zone "0.0.0.0.0.0.0.0.*******.*******.ip6.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
zone "0.0.0.0.0.0.0.0.*******.*******.ip6.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
zone "0.0.0.0.0.0.0.0.*******.*******.ip6.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}
zone "0.0.0.0.0.0.0.0.*******.*******.ip6.arpa." {
	primary 127.0.0.1;
	key DHCP_UPDATER_default;
}

#End of dhcpdv6.conf file
#Data set version 2
