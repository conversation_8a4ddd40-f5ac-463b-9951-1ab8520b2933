db_import;db;full;2011:50;2011:1000
<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
    <SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
      <ib:ObjectReadResponse>
        <objects>
          <item xsi:type="ib:SubGridMember">
            <virtual_oid>8</virtual_oid>
            <host_name>foo-08.com</host_name>
            <comment>Sub grid member object 08</comment>
            <member_type>INFOBLOX</member_type>
            <is_master>false</is_master>
            <ha_enabled>false</ha_enabled>
            <virtual_ip>********</virtual_ip>
          </item>
        </objects>
      </ib:ObjectReadResponse>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
    <SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
      <ib:ObjectReadResponse>
        <objects>
          <item xsi:type="ib:SubGridNetworkView">
            <id>1</id>
            <name>netview1</name>
            <comment>Sub grid network view object 01</comment>
          </item>
        </objects>
      </ib:ObjectReadResponse>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ib="urn:ibap.infoblox.com">
    <SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
      <ib:ObjectReadResponse>
        <objects>
          <item xsi:type="ib:SubGridNetwork">
            <id>2</id>
            <address>10.0.0.0</address>
            <cidr>24</cidr>
            <is_ipv4>true</is_ipv4>
            <is_container>false</is_container>
            <comment>Sub grid network object 02</comment>
          </item>
        </objects>
      </ib:ObjectReadResponse>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>

