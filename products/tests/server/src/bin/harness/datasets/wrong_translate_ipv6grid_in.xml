<DATABASE NAME="onedb" VERSION="MDXMLTEST">

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
<PROPERTY NAME="__key" VALUE="1"/>
<PROPERTY NAME="virtual_ip" VALUE="***********"/>
<PROPERTY NAME="subnet_mask" VALUE="*************"/>
<PROPERTY NAME="gateway" VALUE="***********"/>
<PROPERTY NAME="is_master" VALUE="true"/>
<PROPERTY NAME="ha_enabled" VALUE="1"/>
<PROPERTY NAME="lan2_enabled" VALUE="true"/>
<PROPERTY NAME="ipv6_enabled" VALUE="true"/>
<PROPERTY NAME="parent" VALUE="0"/>
<PROPERTY NAME="virtual_oid" VALUE="1"/>
<PROPERTY NAME="host_name" VALUE="infoblox1.localdomain"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
<PROPERTY NAME="__key" VALUE="1"/>
<PROPERTY NAME="virtual_node" VALUE="1"/>
<PROPERTY NAME="position" VALUE="0"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
<PROPERTY NAME="__key" VALUE="2"/>
<PROPERTY NAME="virtual_node" VALUE="1"/>
<PROPERTY NAME="position" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_virtual_ip"/>
<PROPERTY NAME="__key" VALUE="1.2003::2"/>
<PROPERTY NAME="virtual_node" VALUE="1"/>
<PROPERTY NAME="v_ip" VALUE="2003::2"/>
<PROPERTY NAME="cidr" VALUE="48"/>
<PROPERTY NAME="gateway" VALUE="2003::1"/>
<PROPERTY NAME="router_discovery_enabled" VALUE="true"/>
<PROPERTY NAME="interface_type" VALUE="LAN_HA"/>
<PROPERTY NAME="vlan_id" VALUE="33"/>
<PROPERTY NAME="primary" VALUE="true"/>
<PROPERTY NAME="use_dscp" VALUE="true"/>
<PROPERTY NAME="dscp" VALUE="7"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
<PROPERTY NAME="__key" VALUE="2"/>
<PROPERTY NAME="virtual_ip" VALUE="***********0"/>
<PROPERTY NAME="subnet_mask" VALUE="*************"/>
<PROPERTY NAME="gateway" VALUE="***********"/>
<PROPERTY NAME="is_master" VALUE="false"/>
<PROPERTY NAME="ha_enabled" VALUE="0"/>
<PROPERTY NAME="lan2_enabled" VALUE="true"/>
<PROPERTY NAME="ipv6_enabled" VALUE="true"/>
<PROPERTY NAME="parent" VALUE="0"/>
<PROPERTY NAME="is_potential_master" VALUE="true"/>
<PROPERTY NAME="virtual_oid" VALUE="2"/>
<PROPERTY NAME="host_name" VALUE="infoblox2.localdomain"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
<PROPERTY NAME="__key" VALUE="3"/>
<PROPERTY NAME="virtual_ip" VALUE="************"/>
<PROPERTY NAME="subnet_mask" VALUE="*************"/>
<PROPERTY NAME="gateway" VALUE="***********"/>
<PROPERTY NAME="is_master" VALUE="false"/>
<PROPERTY NAME="ha_enabled" VALUE="1"/>
<PROPERTY NAME="lan2_enabled" VALUE="true"/>
<PROPERTY NAME="ipv6_enabled" VALUE="true"/>
<PROPERTY NAME="parent" VALUE="0"/>
<PROPERTY NAME="virtual_oid" VALUE="3"/>
<PROPERTY NAME="host_name" VALUE="infoblox3.localdomain"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
<PROPERTY NAME="__key" VALUE="3"/>
<PROPERTY NAME="virtual_node" VALUE="3"/>
<PROPERTY NAME="position" VALUE="0"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
<PROPERTY NAME="__key" VALUE="4"/>
<PROPERTY NAME="virtual_node" VALUE="3"/>
<PROPERTY NAME="position" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_virtual_ip"/>
<PROPERTY NAME="__key" VALUE="3.2004::2"/>
<PROPERTY NAME="virtual_node" VALUE="3"/>
<PROPERTY NAME="v_ip" VALUE="2004::2"/>
<PROPERTY NAME="cidr" VALUE="48"/>
<PROPERTY NAME="gateway" VALUE="2004::1"/>
<PROPERTY NAME="router_discovery_enabled" VALUE="true"/>
<PROPERTY NAME="interface_type" VALUE="LAN_HA"/>
<PROPERTY NAME="vlan_id" VALUE="35"/>
<PROPERTY NAME="primary" VALUE="true"/>
<PROPERTY NAME="use_dscp" VALUE="true"/>
<PROPERTY NAME="dscp" VALUE="8"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
<PROPERTY NAME="__key" VALUE="4"/>
<PROPERTY NAME="virtual_ip" VALUE="************"/>
<PROPERTY NAME="subnet_mask" VALUE="*************"/>
<PROPERTY NAME="gateway" VALUE="***********"/>
<PROPERTY NAME="is_master" VALUE="false"/>
<PROPERTY NAME="ha_enabled" VALUE="1"/>
<PROPERTY NAME="lan2_enabled" VALUE="true"/>
<PROPERTY NAME="ipv6_enabled" VALUE="true"/>
<PROPERTY NAME="parent" VALUE="0"/>
<PROPERTY NAME="virtual_oid" VALUE="4"/>
<PROPERTY NAME="host_name" VALUE="infoblox4.localdomain"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
<PROPERTY NAME="__key" VALUE="5"/>
<PROPERTY NAME="virtual_node" VALUE="4"/>
<PROPERTY NAME="position" VALUE="0"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
<PROPERTY NAME="__key" VALUE="6"/>
<PROPERTY NAME="virtual_node" VALUE="4"/>
<PROPERTY NAME="position" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.virtual_node"/>
<PROPERTY NAME="__key" VALUE="5"/>
<PROPERTY NAME="virtual_ip" VALUE="************"/>
<PROPERTY NAME="subnet_mask" VALUE="*************"/>
<PROPERTY NAME="gateway" VALUE="***********"/>
<PROPERTY NAME="is_master" VALUE="false"/>
<PROPERTY NAME="ha_enabled" VALUE="1"/>
<PROPERTY NAME="lan2_enabled" VALUE="true"/>
<PROPERTY NAME="ipv6_enabled" VALUE="true"/>
<PROPERTY NAME="parent" VALUE="0"/>
<PROPERTY NAME="virtual_oid" VALUE="5"/>
<PROPERTY NAME="host_name" VALUE="infoblox5.localdomain"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
<PROPERTY NAME="__key" VALUE="7"/>
<PROPERTY NAME="virtual_node" VALUE="5"/>
<PROPERTY NAME="position" VALUE="0"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.physical_node"/>
<PROPERTY NAME="__key" VALUE="8"/>
<PROPERTY NAME="virtual_node" VALUE="5"/>
<PROPERTY NAME="v6_public_ip_address" VALUE="2005::5"/>
<PROPERTY NAME="position" VALUE="1"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.one.member_virtual_ip"/>
<PROPERTY NAME="__key" VALUE="5.2005::2"/>
<PROPERTY NAME="virtual_node" VALUE="5"/>
<PROPERTY NAME="v_ip" VALUE="2005::2"/>
<PROPERTY NAME="cidr" VALUE="48"/>
<PROPERTY NAME="gateway" VALUE="2005::1"/>
<PROPERTY NAME="router_discovery_enabled" VALUE="true"/>
<PROPERTY NAME="interface_type" VALUE="LAN_HA"/>
<PROPERTY NAME="vlan_id" VALUE="35"/>
<PROPERTY NAME="primary" VALUE="true"/>
<PROPERTY NAME="use_dscp" VALUE="true"/>
<PROPERTY NAME="dscp" VALUE="5"/>
</OBJECT>


</DATABASE>

