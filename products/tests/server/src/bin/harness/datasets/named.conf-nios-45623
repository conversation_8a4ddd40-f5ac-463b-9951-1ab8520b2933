
include "/tmp/tsig.key";

acl all_dns_views_updater_keys {  key DHCP_UPDATER1; key DHCP_UPDATER2; key DHCP_UPDATER3; key DHCP_UPDATER4; key DHCP_UPDATER5; key DHCP_UPDATER6; key DHCP_UPDATER7; key DHCP_UPDATER8; key DHCP_UPDATER9; key DHCP_UPDATER10; key DHCP_UPDATER11; key DHCP_UPDATER12; key DHCP_UPDATER13; key DHCP_UPDATER14; key DHCP_UPDATER15; key DHCP_UPDATER16; key DHCP_UPDATER17; key DHCP_UPDATER18; key DHCP_UPDATER19; };

options {
	masterfile-format text;
	zone-statistics yes;
	directory "/tmp";
	version none;
	recursion no;
	max-recursion-depth 7;
	max-recursion-queries 150;
        infoblox-dns-update-quota 1024;
        infoblox-dns-update-forwarding-quota 1024;
	hostname none;
	listen-on { 127.0.0.1; x.x.x.x; };
	query-source address x.x.x.x port *; 
	notify-source x.x.x.x port *; 
	transfer-source x.x.x.x; 
	use-alt-transfer-source no; 
	minimal-responses yes;
	max-cache-size 4294967295;
        lame-ttl 600;
	tcp-clients 1000;
	transfers-in 10;
	transfers-out 10;
	transfers-per-ns 2;
	serial-query-rate 20;
        max-cache-ttl 604800;
        max-ncache-ttl 10800;
        edns-udp-size 1220;
        max-udp-size 1220;
	# for service restart: allow_bulkhost_ddns = Refusal
        max-records-per-type 500;
        max-types-per-name 100;
	allow-transfer { !any; };
	infoblox-deny-rpz { localhost; x.x.x.x; };
	avoid-v4-udp-ports { 2114; 2113; 2115; 3000; 8000; 8089; 9997; 2222; 4040; 5575; 7077; 7911; 7912; 8000; 8089; 9090; 9091; 9997; 8070; 8787; 9999; 9004; 2022; 3374; 3115; 8909; 1194; 8080; 9000; 9183; 9185; };
	avoid-v6-udp-ports { 2114; 2113; 2115; 3000; 8000; 8089; 9997; 2222; 4040; 5575; 7077; 7911; 7912; 8000; 8089; 9090; 9091; 9997; 8070; 8787; 9999; 9004; 2022; 3374; 3115; 8909; 1194; 8080; 9000; 9183; 9185; };
	transfer-format many-answers;
        max-journal-size 100000K;
};

# Worker threads: default

# Bulk Host Name Templates:
#	Four Octets: 		"-$1-$2-$3-$4" (Default)
#	One Octet: 		"-$4"
#	Three Octets: 		"-$2-$3-$4"
#	Two Octets: 		"-$3-$4"

include "/tmp/dhcp_updater.key";

include "/tmp/rndc.key";

controls {
        inet 127.0.0.1 port 953
        allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	 channel ib_syslog { 
		 syslog daemon; 
		 severity info; 
	};
	 category default { ib_syslog; };
	 category rpz { null; };
};

# view_nios_45623_1
view "1" {  # view_nios_45623_1
    match-clients { key DHCP_UPDATER1; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.1";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.1";
    };
    zone "zone_nios_45623_1" in { # zone_nios_45623_1
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
    file "azd/db.zone_nios_45623_1.1";
    notify yes;
    };
};
# view_nios_45623_2
view "2" {  # view_nios_45623_2
    match-clients { key DHCP_UPDATER2; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.2";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.2";
    };
    zone "zone_nios_45623_2" in { # zone_nios_45623_2
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_2.2";
	notify yes;
    };
};
# view_nios_45623_3
view "3" {  # view_nios_45623_3
    match-clients { key DHCP_UPDATER3; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.3";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.3";
    };
    zone "zone_nios_45623_3" in { # zone_nios_45623_3
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_3.3";
	notify yes;
    };
};
# view_nios_45623_4
view "4" {  # view_nios_45623_4
    match-clients { key DHCP_UPDATER4; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.4";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.4";
    };
    zone "zone_nios_45623_4" in { # zone_nios_45623_4
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_4.4";
	notify yes;
    };
};
# view_nios_45623_5
view "5" {  # view_nios_45623_5
    match-clients { key DHCP_UPDATER5; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.5"; 
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.5";
    };
    zone "zone_nios_45623_5" in { # zone_nios_45623_5
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_5.5";
	notify yes;
    };
};
# view_nios_45623_6
view "6" {  # view_nios_45623_6
    match-clients { key DHCP_UPDATER6; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.6";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.6";
    };
    zone "zone_nios_45623_6" in { # zone_nios_45623_6
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_6.6";
	notify yes;
    };
};
# view_nios_45623_7
view "7" {  # view_nios_45623_7
    match-clients { key DHCP_UPDATER7; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.7";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.7";
    };
    zone "zone_nios_45623_7" in { # zone_nios_45623_7
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_7.7";
	notify yes;
    };
};
# view_nios_45623_8
view "8" {  # view_nios_45623_8
    match-clients { key DHCP_UPDATER8; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.8";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.8";
    };
    zone "zone_nios_45623_8" in { # zone_nios_45623_8
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_8.8";
	notify yes;
    };
};
# view_nios_45623_9
view "9" {  # view_nios_45623_9
    match-clients { key DHCP_UPDATER9; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.9";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.9";
    };
    zone "zone_nios_45623_9" in { # zone_nios_45623_9
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_9.9";
	notify yes;
    };
};
# view_nios_45623_10
view "10" {  # view_nios_45623_10
    match-clients { key DHCP_UPDATER10; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    response-policy {
	zone "rpz_nios_45623_10" policy Given;# priority 0
    } qname-wait-recurse no add-soa no;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.10"; 
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.10";
    };
    zone "rpz_nios_45623_10" in { # rpz_nios_45623_10
	# default TTL = 28800;
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
 	file "azd/db.rpz_nios_45623_10.10";
	allow-query { 127.0.0.1; };
    notify yes;
    };
    zone "zone_nios_45623_10" in { # zone_nios_45623_10
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_10.10";
	notify yes;
    };
};
# view_nios_45623_11
view "11" {  # view_nios_45623_11
    match-clients { key DHCP_UPDATER11; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    response-policy {
	zone "rpz_nios_45623_11" policy Given;# priority 0
    } qname-wait-recurse no add-soa no;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.11";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.11";
    };
    zone "rpz_nios_45623_11" in { # rpz_nios_45623_11
	# default TTL = 28800;
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
 	file "azd/db.rpz_nios_45623_11.11";
	allow-query { 127.0.0.1; };
	notify yes;
    };
    zone "zone_nios_45623_11" in { # zone_nios_45623_11
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_11.11";
	notify yes;
    };
};
# view_nios_45623_12
view "12" {  # view_nios_45623_12
    match-clients { key DHCP_UPDATER12; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    response-policy {
	zone "rpz_nios_45623_12" policy Given;# priority 0
    } qname-wait-recurse no add-soa no;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.12";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.12";
    };
    zone "rpz_nios_45623_12" in { # rpz_nios_45623_12
	# default TTL = 28800;
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
 	file "azd/db.rpz_nios_45623_12.12";
	allow-query { 127.0.0.1; };
	notify yes;
    };
    zone "zone_nios_45623_12" in { # zone_nios_45623_12
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_12.12";
	notify yes;
    };
};
# view_nios_45623_13
view "13" {  # view_nios_45623_13
    match-clients { key DHCP_UPDATER13; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    response-policy {
	zone "rpz_nios_45623_13" policy Given;# priority 0
    } qname-wait-recurse no add-soa no;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.13";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.13";
    };
    zone "rpz_nios_45623_13" in { # rpz_nios_45623_13
	# default TTL = 28800;
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
 	file "azd/db.rpz_nios_45623_13.13";
	allow-query { 127.0.0.1; };
	notify yes;
    };
    zone "zone_nios_45623_13" in { # zone_nios_45623_13
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_13.13";
	notify yes;
    };
};
# view_nios_45623_14
view "14" {  # view_nios_45623_14
    match-clients { key DHCP_UPDATER14; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    response-policy {
	zone "rpz_nios_45623_14" policy Given;# priority 0
    } qname-wait-recurse no add-soa no;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.14";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.14";
    };
    zone "rpz_nios_45623_14" in { # rpz_nios_45623_14
	# default TTL = 28800;
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
 	file "azd/db.rpz_nios_45623_14.14";
	allow-query { 127.0.0.1; };
	notify yes;
    };
    zone "zone_nios_45623_14" in { # zone_nios_45623_14
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_14.14";
	notify yes;
    };
};
# view_nios_45623_15
view "15" {  # view_nios_45623_15
    match-clients { key DHCP_UPDATER15; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    response-policy {
	zone "rpz_nios_45623_15" policy Given;# priority 0
    } qname-wait-recurse no add-soa no;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.15";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.15";
    };
    zone "rpz_nios_45623_15" in { # rpz_nios_45623_15
	# default TTL = 28800;
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
 	file "azd/db.rpz_nios_45623_15.15";
	allow-query { 127.0.0.1; };
	notify yes;
    };
    zone "zone_nios_45623_15" in { # zone_nios_45623_15
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_15.15";
	notify yes;
    };
};
# view_nios_45623_16
view "16" {  # view_nios_45623_16
    match-clients { key DHCP_UPDATER16; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    response-policy {
	zone "rpz_nios_45623_16" policy Given;# priority 0
    } qname-wait-recurse no add-soa no;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.16";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.16";
    };
    zone "rpz_nios_45623_16" in { # rpz_nios_45623_16
	# default TTL = 28800;
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
 	file "azd/db.rpz_nios_45623_16.16";
	allow-query { 127.0.0.1; };
	notify yes;
    };
    zone "zone_nios_45623_16" in { # zone_nios_45623_16
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_16.16";
	notify yes;
    };
};
# view_nios_45623_17
view "17" {  # view_nios_45623_17
    match-clients { key DHCP_UPDATER17; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    response-policy {
	zone "rpz_nios_45623_17" policy Given;# priority 0
    } qname-wait-recurse no add-soa no;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.17"; 
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.17";
    };
    zone "rpz_nios_45623_17" in { # rpz_nios_45623_17
	# default TTL = 28800;
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
 	file "azd/db.rpz_nios_45623_17.17";
	allow-query { 127.0.0.1; };
	notify yes;
    };
    zone "zone_nios_45623_17" in { # zone_nios_45623_17
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_17.17";
	notify yes;
    };
};
# view_nios_45623_18
view "18" {  # view_nios_45623_18
    match-clients { key DHCP_UPDATER18; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    response-policy {
	zone "rpz_nios_45623_18" policy Given;# priority 0
    } qname-wait-recurse no add-soa no;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.18"; 
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.18";
    };
    zone "rpz_nios_45623_18" in { # rpz_nios_45623_18
	# default TTL = 28800;
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
 	file "azd/db.rpz_nios_45623_18.18";
	allow-query { 127.0.0.1; };
	notify yes;
    };
    zone "zone_nios_45623_18" in { # zone_nios_45623_18
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_18.18";
	notify yes;
    };
};
# view_nios_45623_19
view "19" {  # view_nios_45623_19
    match-clients { key DHCP_UPDATER19; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220;
    edns-udp-size 1220;
    response-policy {
	zone "rpz_nios_45623_19" policy Given;# priority 0
    } qname-wait-recurse no add-soa no;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.0.0.127.in-addr.arpa.19"; 
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.19";
    };
    zone "rpz_nios_45623_19" in { # rpz_nios_45623_19
	# default TTL = 28800;
	type master;
    database infoblox_zdb;
 	masterfile-format raw;
 	file "azd/db.rpz_nios_45623_19.19";
	allow-query { 127.0.0.1; };
	notify yes;
    };
    zone "zone_nios_45623_19" in { # zone_nios_45623_19
	type master;
    database infoblox_zdb;
    masterfile-format raw;
    file "azd/db.zone_nios_45623_19.19";
	notify yes;
    };
};
