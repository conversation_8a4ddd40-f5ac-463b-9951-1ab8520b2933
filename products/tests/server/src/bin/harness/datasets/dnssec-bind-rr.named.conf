options {
    pid-file "named.pid";
    directory ".";
    recursion no;
    listen-on { 127.0.0.1; 192.168.1.2; };
    allow-query { any; };
    allow-transfer { any; };
};

key "rndc-key" {
    algorithm hmac-md5;       
    secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
    inet 127.0.0.1 port 953
    allow { 127.0.0.1; } keys { "rndc-key"; };
};

zone "foo" in {
    type master;
    sig-validity-interval 1800 1350; # mandatory option for correct work of named ref to CL224135
    database infoblox_zdb;
    masterfile-format raw;
    file "dnssec-bind-rr.foo.db.raw";
};
