<DATABASE NAME="onedb" VERSION="MDXMLTEST">
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.shared_record_objects_changes_tracking"/>
        <PROPERTY NAME="srg" VALUE=".srg_root.1"/>
        <PROPERTY NAME="zone" VALUE="._default.com.foo"/>
        <PROPERTY NAME="uuid" VALUE="772cc4c0d48d4eadad848f01534186a7"/>
        <PROPERTY NAME="record_type" VALUE="dns.bind_txt"/>
        <PROPERTY NAME="revision_id" VALUE="322"/>
        <PROPERTY NAME="srg_zone_linking" VALUE=".srg_root.1,._default.com.foo,"/>
        <PROPERTY NAME="resource_record" VALUE=".com.infoblox.dns.bind_txt$._default.com.test._domainkey.h7.&quot;text1 with \; semicolon&quot;"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="object" VALUE=".com.infoblox.dns.bind_txt$._default.com.test._domainkey.h7.&quot;text1 with \; semicolon&quot;"/>
        <PROPERTY NAME="tag" VALUE=".owner"/>
        <PROPERTY NAME="value" VALUE="value1"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.shared_record_objects_changes_tracking"/>
        <PROPERTY NAME="srg" VALUE=".srg_root.1"/>
        <PROPERTY NAME="zone" VALUE="._default.com.foo"/>
        <PROPERTY NAME="uuid" VALUE="772cc4c0d48d4eadad848f01534186a7"/>
        <PROPERTY NAME="record_type" VALUE="dns.bind_naptr"/>
        <PROPERTY NAME="revision_id" VALUE="322"/>
        <PROPERTY NAME="srg_zone_linking" VALUE=".srg_root.1,._default.com.foo,"/>
        <PROPERTY NAME="resource_record" VALUE=".com.infoblox.dns.bind_naptr$._default.com.test,test-naptr,100,10,,,regexp1 with \; semicolon,."/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="object" VALUE=".com.infoblox.dns.bind_naptr$._default.com.test,test-naptr,100,10,,,regexp1 with \; semicolon,."/>
        <PROPERTY NAME="tag" VALUE=".owner"/>
        <PROPERTY NAME="value" VALUE="value1"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.shared_record_objects_changes_tracking"/>
        <PROPERTY NAME="srg" VALUE=".srg_root.1"/>
        <PROPERTY NAME="zone" VALUE="._default.com.foo"/>
        <PROPERTY NAME="uuid" VALUE="772cc4c0d48d4eadad848f01534186a7"/>
        <PROPERTY NAME="record_type" VALUE="dns.bind_txt"/>
        <PROPERTY NAME="revision_id" VALUE="322"/>
        <PROPERTY NAME="srg_zone_linking" VALUE=".srg_root.1,._default.com.foo,"/>
        <PROPERTY NAME="resource_record" VALUE=".com.infoblox.dns.bind_txt$._default.com.test._domainkey.h7.&quot;text2 with ; semicolon&quot;"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="object" VALUE=".com.infoblox.dns.bind_naptr$._default.com.test,test-naptr,100,10,,,regexp2 with ; semicolon,."/>
        <PROPERTY NAME="tag" VALUE=".owner"/>
        <PROPERTY NAME="value" VALUE="value1"/>
    </OBJECT>
    <OBJECT>
        <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
        <PROPERTY NAME="position" VALUE="0"/>
        <PROPERTY NAME="object" VALUE=".com.infoblox.dns.bind_a$._default.com.name with \; semicolon"/>
        <PROPERTY NAME="tag" VALUE=".owner"/>
        <PROPERTY NAME="value" VALUE="value1"/>
    </OBJECT>
</DATABASE>
