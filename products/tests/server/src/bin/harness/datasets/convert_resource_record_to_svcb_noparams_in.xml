<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
  <PROPERTY NAME="record_type_num" VALUE="64"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1741586128"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="enable_host_name_policy" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.test"/>
  <PROPERTY NAME="name" VALUE="unk11"/>
  <PROPERTY NAME="record_type" VALUE="TYPE64"/>
  <PROPERTY NAME="record_rdata" VALUE="0 test.com."/>
  <PROPERTY NAME="record_rdata_hash" VALUE="f4587e2afb7316ab2c2d3823e6ade729897ab93c801295f679f642ff1bf62a40537d01ede4f521db19ff1896546c96050643d9d938e1a75fe020a34a5455638b"/>
  <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
  <PROPERTY NAME="display_name" VALUE="unk11"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
  <PROPERTY NAME="record_type_num" VALUE="64"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1741586129"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="enable_host_name_policy" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.temp"/>
  <PROPERTY NAME="name" VALUE="unk"/>
  <PROPERTY NAME="record_type" VALUE="TYPE64"/>
  <PROPERTY NAME="record_rdata" VALUE="0 foo.example.org."/>
  <PROPERTY NAME="record_rdata_hash" VALUE="0faa1397e7383fd2554ea31bbec8d11a4d249d9238c885274f553c3e074aa7c478bea89e86697cc242c964b5222781b3dda3e1bf2edc2844a6578d292565d642"/>
  <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
  <PROPERTY NAME="display_name" VALUE="unk"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
  <PROPERTY NAME="record_type_num" VALUE="64"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1741586129"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="enable_host_name_policy" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.temp"/>
  <PROPERTY NAME="name" VALUE="unk2"/>
  <PROPERTY NAME="record_type" VALUE="TYPE64"/>
  <PROPERTY NAME="record_rdata" VALUE="\# 16 000003616263047465737403636F6D00"/>
  <PROPERTY NAME="record_rdata_hash" VALUE="f3929ccf0ffca2a1836a490b2c1af82cf9093574db107fed3210ead9d77499a1c8ae128de6367615d861e35fb04fd54994149230d34b46063777c520c0b2f00c"/>
  <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
  <PROPERTY NAME="display_name" VALUE="unk2"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="object" VALUE=".com.infoblox.dns.zone$._default.com.temp"/>
  <PROPERTY NAME="tag" VALUE=".Site"/>
  <PROPERTY NAME="value" VALUE="Bengaluru"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="object" VALUE=".com.infoblox.dns.bind_resource_record$._default.com.temp/unk2/64/f3929ccf0ffca2a1836a490b2c1af82cf9093574db107fed3210ead9d77499a1c8ae128de6367615d861e35fb04fd54994149230d34b46063777c520c0b2f00c"/>
  <PROPERTY NAME="tag" VALUE=".Site"/>
  <PROPERTY NAME="value" VALUE="Bangalore"/>
  <PROPERTY NAME="inheritance_source" VALUE=".com.infoblox.dns.zone$._default.com.temp"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
</DATABASE>
