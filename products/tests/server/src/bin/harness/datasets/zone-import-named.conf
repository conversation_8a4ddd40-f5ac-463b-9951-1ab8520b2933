options {
	directory "/storage/tmp/zone-import";	// Working directory
	pid-file "named.pid-zone-import";		// Put pid file in working dir
	recursion no;				// Do not provide recursive service
	allow-transfer { any; };
	listen-on port 53 { *********; };
    dnssec-enable yes;
    dnssec-validation yes;
    dnssec-accept-expired yes;
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 954
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	channel import_syslog {
		syslog daemon;
		severity debug;
	};
	category default { import_syslog; };
};

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	file "zone-import-localhost.db";
	notify no;
};

zone "." {
	type master;
	file "zone-import-root.db";
	notify no;
};

zone "com." {
	type master;
	file "zone-import.com.db";
	notify no;
};

zone "test-import1.com." {
	type master;
	file "zone-import.test-import1.com.db";
	notify no;
};

zone "nios-71359.com." {
    type master;
    file "zone-import.nios-71359.com.db";
    notify no;
};

zone "nios73966.com." {
    type master;
    file "zone-import.nios73966.com.db";
    notify no;
};

zone "nios90153.com." {
    type master;
    file "zone-import.nios90153.com.db";
    notify no;
};

zone "in-addr.arpa." {
	type master;
	file "zone-import.1.192.db";
	notify no;
};

zone "0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.A.A.A.A.ip6.arpa." {
       type master;
       file "zone-import.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.A.A.A.A.db";
       notify no;
 };

zone "1.192.in-addr.arpa." {
	type master;
	file "zone-import.1.192.db";
	notify no;
};

zone "168.1.192.in-addr.arpa." {
	type master;
	file "zone-import.168.1.192.db";
	notify no;
};


zone "0.0.0.0.b.0.a.*******.*******.0.ip6.arpa." {
	type master;
	file "zone-import.0000.b0a0.4030.2010.db";
	notify no;
};

zone "0.0.0.0.d.0.c.0.0.0.0.0.b.0.a.*******.*******.0.ip6.arpa." {
	type master;
	file "zone-import.0000.d0c0.0000.b0a0.4030.2010.db";
	notify no;
};

zone "ip6.arpa." {
	type master;
	file "zone-import.0000.d0c0.0000.b0a0.4030.2010.db";
	notify no;
};

zone "test-ad.com." {
	type master;
	file "zone-import.test-ad.com.db";
	notify no;
};

zone "test-serial-no-import.com." {
	type master;
	file "zone-import.test-serial-no-import.com.db";
	notify no;
};

zone "bug22854.com." {
	type master;
	file "zone-import.bug22854.com.db";
	notify no;
};

zone "bugnios52164.com." {
	type master;
	file "zone-import.bug-nios-52164.com.db";
	notify no;
};

zone "there." {
        type master;
        file "zone-import.there.db";
        notify no;
};

zone "secure.com." {
        type master;
        file "dnssec-db-primary.secure.com.signed-nsec3";
        notify no;
};

zone "test.com." {
        type master;
        file "dnssec.db.test.com";
        notify no;
};

zone "test-apl.com." {
        type master;
        file "zone-import.test-apl.com.db";
        notify no;
};

zone "tlsa.com." {
        type master;
        file "dnssec.db.tlsa.com";
        notify no;
};

zone "svcb.com." {
        type master;
        file "zone-import.svcb.com.db";
        notify no;
};

zone "https.com." {
        type master;
        file "zone-import.https.com.db";
        notify no;
};

zone "bulk.com." {
	type master;
	file "zone-import.bulk.com.db";
	notify no;
};

zone "test-zone.com." {
        type master;
        file "zone-import.test-zone.db";
        notify no;
};

zone "0.0.10.in-addr.arpa" in {
	type master;
	file "zone-import.10.0.0.db";
	notify no;
};

zone "junk.com." in {
	type master;
	file "zone-import.bug_NIOS_37367.db";
	notify no;
};

zone "rfe-3953.com." in {
	type master;
	file "zone-import.RFE-3953.db";
	notify no;
};

zone "rpz.net." in {
	type master;
	file "zone-import.rpz.net.db";
	notify no;
};

zone "rpz1.net." in {
	type master;
	file "zone-import.rpz1.net.db";
	notify no;
};

zone "rpz2.net." in {
	type master;
	file "zone-import.rpz2.net.db";
	notify no;
};

