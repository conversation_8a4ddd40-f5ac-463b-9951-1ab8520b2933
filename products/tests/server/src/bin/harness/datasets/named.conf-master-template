// Inspired by examples in chapter 3 of the BIND ARM

acl "loopbacknet" { 127.0.0.0/24; };

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid";			// Put pid file in working dir
	recursion no;				// Do not provide recursive service
	listen-on port 5353 { 127.0.0.1; };	// Put master on non-standard port
	also-notify port 53 { 127.0.0.1; };	// Notify slave on same system
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 953
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

view "VIEW_NAME" {

// Root server hints
zone "." { type hint; file "root.hint"; };

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	file "localhost.rev";
	notify no;
};

zone "test.infoblox.com" {
	type master;
	file "test.infoblox.com.db";
	allow-update { "loopbacknet"; };
	notify explicit; // Use also-notify only
};

zone " test escape .infoblox.com" {
	type master;
	file "test-escape.infoblox.com.db";
	allow-update { "loopbacknet"; };
	notify explicit; // Use also-notify only
};

zone "1.168.192.in-addr.arpa" {
	type master;
	file "1.168.192.in-addr.arpa";
	allow-update { "loopbacknet"; };
	notify explicit; // Use also-notify only
};

zone "b0003.grainger.com" {
	type master;
	file "b0003.grainger.com";
	notify explicit; // Use also-notify only
};

zone "bulk.infoblox.com" {
        type master;
        notify no;
        file "bulk.infoblox.com.db";
};

zone "2.168.192.in-addr.arpa" {
        type master;
        notify no;
        file "2.168.192.in-addr.arpa";
};

zone "3.in-addr.arpa" {
	type master;
	notify no;
	file "3.in-addr.arpa";
};

zone "10.in-addr.arpa" {
	type master;
	notify no;
	file "10.in-addr.arpa";
};

zone "d.c.b.a.ip6.arpa" {
	type master;
	file "d.c.b.a.ip6.arpa";
	allow-update { "loopbacknet"; };
	notify explicit; // Use also-notify only
};

};
