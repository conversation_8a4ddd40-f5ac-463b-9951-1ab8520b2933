options {
    zone-statistics yes;
    directory "TEST_WORKING_DIRECTORY";
    pid-file "/usr/var/run/named.pid";
    recursion yes;
    dump-file "/infoblox/var/named_conf/named_dump.db";
    statistics-file "/infoblox/var/named_conf/named.stats";
    listen-on port NAMED_PORT { 127.0.0.1; };
};

include "/infoblox/var/named_conf/rndc.key";

controls {
        inet 127.0.0.1 port RNDC_PORT
        allow { 127.0.0.1; } keys { "rndc-key"; };
};

view "VIEW_NAME" {
    match-clients { *********; };
    query-source address *********;
    zone "." { type hint; file "root.hint"; };
    zone "test.com" {
        type master;
        notify no;
        file "rndc-sc-2-test.com";
        database rbt;
    };
    zone "test.net" {
        type master;
        notify no;
        file "rndc-sc-2-test.net";
        database rbt;
    };
    zone "sec.net" {
        type slave;
        masters port MASTER_PORT { 127.0.0.1; };
        notify no;
        file "rndc-sc-2-slave-sec.net";
        database rbt;
        transfer-source *********;
    };
};

view "_default" {
    query-source address 127.0.0.1;
    zone "." { type hint; file "root.hint"; };
    zone "test.com" {
        type master;
        notify no;
        file "rndc-sc-def-test.com";
        database rbt;
    };
    zone "test.net" {
        type master;
        notify no;
        file "rndc-sc-def-test.net";
        database rbt;
    };
    zone "sec.net" {
        type slave;
        masters port MASTER_PORT { 127.0.0.1; };
        notify no;
        file "rndc-sc-def-slave-sec.net";
        database rbt;
        transfer-source 127.0.0.1;
    };
};
