<DATABASE NAME="onedb" VERSION="MDXMLTEST">
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dhcp_properties"/>
    <PROPERTY NAME="enable_one_lease_per_client" VALUE="false"/>
    <PROPERTY NAME="cluster" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.cluster_dhcp_properties"/>
    <PROPERTY NAME="enable_one_lease_per_client" VALUE="true"/>
    <PROPERTY NAME="cluster" VALUE="1"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
    <PROPERTY NAME="enable_one_lease_per_client" VALUE="false"/>
    <PROPERTY NAME="override_enable_one_lease_per_client" VALUE="false"/>
    <PROPERTY NAME="virtual_node" VALUE="1"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
    <PROPERTY NAME="enable_one_lease_per_client" VALUE="false"/>
    <PROPERTY NAME="override_enable_one_lease_per_client" VALUE="true"/>
    <PROPERTY NAME="virtual_node" VALUE="2"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
    <PROPERTY NAME="enable_one_lease_per_client" VALUE="true"/>
    <PROPERTY NAME="override_enable_one_lease_per_client" VALUE="false"/>
    <PROPERTY NAME="virtual_node" VALUE="3"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.member_dhcp_properties"/>
    <PROPERTY NAME="enable_one_lease_per_client" VALUE="true"/>
    <PROPERTY NAME="override_enable_one_lease_per_client" VALUE="true"/>
    <PROPERTY NAME="virtual_node" VALUE="4"/>
  </OBJECT>
</DATABASE>
