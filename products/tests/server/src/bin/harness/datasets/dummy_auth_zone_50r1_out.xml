<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
<PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
<PROPERTY NAME="underscore_zone" VALUE="false"/>
<PROPERTY NAME="revzone_netmask" VALUE="*************"/>
<PROPERTY NAME="is_external_primary" VALUE="false"/>
<PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
<PROPERTY NAME="zone_type" VALUE="Dummy"/>
<PROPERTY NAME="name" VALUE="arpa.in-addr.127.0.0"/>
<PROPERTY NAME="active_directory_option" VALUE="0"/>
<PROPERTY NAME="member_stealth" VALUE="true"/>
<PROPERTY NAME="is_reverse_zone" VALUE="1"/>
<PROPERTY NAME="zone_qal_option" VALUE="0"/>
<PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
<PROPERTY NAME="record_name_policy" VALUE="Allow Underscore"/>
<PROPERTY NAME="zone" VALUE="._default"/>
<PROPERTY NAME="disabled" VALUE="false"/>
<PROPERTY NAME="override_update_forwarding" VALUE="false"/>
<PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
<PROPERTY NAME="forwarding_disabled" VALUE="false"/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
<PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
<PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
<PROPERTY NAME="member_primary" VALUE="0"/>
<PROPERTY NAME="revzone_address" VALUE="*********"/>
<PROPERTY NAME="locked" VALUE="false"/>
<PROPERTY NAME="override_record_name_policy" VALUE="false"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="zone_transfer_list_option" VALUE="0"/>
<PROPERTY NAME="underscore_zone" VALUE="false"/>
<PROPERTY NAME="revzone_netmask" VALUE="*************"/>
<PROPERTY NAME="is_external_primary" VALUE="false"/>
<PROPERTY NAME="auto_created_AD_zone" VALUE="false"/>
<PROPERTY NAME="zone_type" VALUE="Authoritative"/>
<PROPERTY NAME="name" VALUE="arpa.in-addr.213.213.200"/>
<PROPERTY NAME="active_directory_option" VALUE="0"/>
<PROPERTY NAME="member_stealth" VALUE="false"/>
<PROPERTY NAME="is_reverse_zone" VALUE="1"/>
<PROPERTY NAME="zone_qal_option" VALUE="0"/>
<PROPERTY NAME="override_cluster_ddns_updates" VALUE="false"/>
<PROPERTY NAME="record_name_policy" VALUE="Allow Underscore"/>
<PROPERTY NAME="zone" VALUE="._default"/>
<PROPERTY NAME="disabled" VALUE="false"/>
<PROPERTY NAME="override_update_forwarding" VALUE="false"/>
<PROPERTY NAME="allow_update_forwarding" VALUE="false"/>
<PROPERTY NAME="forwarding_disabled" VALUE="false"/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone"/>
<PROPERTY NAME="allow_ddns_updates" VALUE="false"/>
<PROPERTY NAME="allow_gss_tsig_zone_updates" VALUE="false"/>
<PROPERTY NAME="member_primary" VALUE="1"/>
<PROPERTY NAME="revzone_address" VALUE="*************"/>
<PROPERTY NAME="locked" VALUE="false"/>
<PROPERTY NAME="override_record_name_policy" VALUE="false"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="cluster_sec_lead" VALUE="false"/>
<PROPERTY NAME="cluster_sec_stealth" VALUE="false"/>
<PROPERTY NAME="zone" VALUE="._default.arpa.in-addr.213.213.200"/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_cluster_secondary_server"/>
<PROPERTY NAME="cluster_sec_zone_transfer" VALUE="0"/>
<PROPERTY NAME="member_address" VALUE="5"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="cluster_sec_lead" VALUE="false"/>
<PROPERTY NAME="cluster_sec_stealth" VALUE="true"/>
<PROPERTY NAME="zone" VALUE="._default.be.brutele"/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_cluster_secondary_server"/>
<PROPERTY NAME="cluster_sec_zone_transfer" VALUE="0"/>
<PROPERTY NAME="member_address" VALUE="0"/>
</OBJECT>

<OBJECT>
<PROPERTY NAME="ext_sec_address" VALUE="***************"/>
<PROPERTY NAME="auto_created" VALUE="false"/>
<PROPERTY NAME="tsig_option" VALUE="false"/>
<PROPERTY NAME="__type" VALUE=".com.infoblox.dns.zone_ext_secondary_server"/>
<PROPERTY NAME="ext_sec_name" VALUE="dns01.brutele.com"/>
<PROPERTY NAME="__key" VALUE="._default.org.aqua-sambre@<EMAIL>"/>
<PROPERTY NAME="x_tsig_option" VALUE="false"/>
<PROPERTY NAME="ext_sec_stealth" VALUE="false"/>
<PROPERTY NAME="position" VALUE="0"/>
<PROPERTY NAME="zone" VALUE="._default.org.aqua-sambre"/>
</OBJECT>

</DATABASE>

