<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.tests.insert_range_select">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.range_select"/>
<PROPERTY NAME="name" VALUE="ab"/>
<PROPERTY NAME="timestamp" VALUE="00000000:00000010"/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.tests.insert_range_select">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.range_select"/>
<PROPERTY NAME="name" VALUE="ac"/>
<PROPERTY NAME="timestamp" VALUE="00000000:00000050"/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.tests.insert_range_select">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.range_select"/>
<PROPERTY NAME="name" VALUE="ef"/>
<PROPERTY NAME="timestamp" VALUE="00000020:00000050"/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.tests.insert_range_select">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.range_select"/>
<PROPERTY NAME="name" VALUE="gh"/>
<PROPERTY NAME="timestamp" VALUE="00000020:00000080"/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.tests.insert_range_select">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.range_select"/>
<PROPERTY NAME="name" VALUE="az"/>
<PROPERTY NAME="timestamp" VALUE="00000002:00000015"/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.tests.insert_range_select">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.range_select"/>
<PROPERTY NAME="name" VALUE="kl"/>
<PROPERTY NAME="timestamp" VALUE="00000001:00000057"/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.tests.insert_range_select">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.range_select"/>
<PROPERTY NAME="name" VALUE="mn"/>
<PROPERTY NAME="timestamp" VALUE="00000010:00000057"/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
<RTXML-REQUEST>
<FUNCTION name=".com.infoblox.tests.insert_range_select">
<OBJECT>
<PROPERTY NAME="__type" VALUE=".com.infoblox.tests.range_select"/>
<PROPERTY NAME="name" VALUE="op"/>
<PROPERTY NAME="timestamp" VALUE="00000010:00000007"/>
</OBJECT>
</FUNCTION>
</RTXML-REQUEST>
