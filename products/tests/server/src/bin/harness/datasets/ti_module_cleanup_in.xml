<DATABASE NAME="onedb" VERSION="MDXMLTEST">

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.analytics.analytics_module"/>
  <PROPERTY NAME="disabled" VALUE="true"/>
  <PROPERTY NAME="parent" VALUE="20191011"/>
  <PROPERTY NAME="name" VALUE="DTG:DNST"/>
  <PROPERTY NAME="description" VALUE="DNS Tunneling Detection"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.analytics.analytics_module"/>
  <PROPERTY NAME="disabled" VALUE="true"/>
  <PROPERTY NAME="parent" VALUE="20191011"/>
  <PROPERTY NAME="name" VALUE="DNSM"/>
  <PROPERTY NAME="description" VALUE="DNS Messager Detection"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.analytics.analytics_module_param_def"/>
  <PROPERTY NAME="position" VALUE="5"/>
  <PROPERTY NAME="is_member_editable" VALUE="false"/>
  <PROPERTY NAME="hidden" VALUE="false"/>
  <PROPERTY NAME="parent" VALUE="20210620.DTG:DNST"/>
  <PROPERTY NAME="name" VALUE="dnst.enable.legit.tunnel.detection"/>
  <PROPERTY NAME="description" VALUE="detect and ignore legitimate tunnels"/>
  <PROPERTY NAME="syntax" VALUE="BOOL"/>
  <PROPERTY NAME="default_value" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.analytics.analytics_module_param_def"/>
  <PROPERTY NAME="position" VALUE="6"/>
  <PROPERTY NAME="is_member_editable" VALUE="false"/>
  <PROPERTY NAME="hidden" VALUE="false"/>
  <PROPERTY NAME="parent" VALUE="20210620.DTG:DNST"/>
  <PROPERTY NAME="name" VALUE="dnst.use.legitimate.classification"/>
  <PROPERTY NAME="description" VALUE="turn on or off legitimate classification"/>
  <PROPERTY NAME="syntax" VALUE="BOOL"/>
  <PROPERTY NAME="default_value" VALUE="false"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.analytics.analytics_module_param_value"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.analytics.analytics_module$20210620.DTG:DNST"/>
  <PROPERTY NAME="param_def" VALUE="20210620.DTG:DNST.dnst.use.legitimate.classification"/>
  <PROPERTY NAME="value" VALUE="false"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.analytics.analytics_module_param_value"/>
  <PROPERTY NAME="parent" VALUE=".com.infoblox.analytics.analytics_module$20210620.DNSM"/>
  <PROPERTY NAME="param_def" VALUE="20210620.DNSM.dnsm.min.txt.payload"/>
  <PROPERTY NAME="value" VALUE="500"/>
</OBJECT>

</DATABASE>
