<DATABASE NAME="onedb" VERSION="MDXMLTEST">
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
  <PROPERTY NAME="record_type_num" VALUE="64"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="1741586128"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="enable_host_name_policy" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.test"/>
  <PROPERTY NAME="name" VALUE="unk11"/>
  <PROPERTY NAME="record_type" VALUE="TYPE64"/>
  <PROPERTY NAME="record_rdata" VALUE="16 foo.example.org. mandatory=alpn,ipv4hint ech=AEn+DQBFKwAgACABWIHUGj4u+PIggYXcR5JF0gYk3dCRioBW8uJq9H4mKAAIAAEAAQABAANAEnB1YmxpYy50bHMtZWNoLmRldgAA key65333=&quot;ex1&quot; port=&quot;53&quot; key65444=&quot;ex2&quot; alpn=&quot;h2,h3-19&quot; ipv4hint=&quot;**********,************&quot; no-default-alpn ipv6hint=&quot;2001:db8:122:344::1&quot;"/>
  <PROPERTY NAME="record_rdata_hash" VALUE="081d64ed02a7f98681740125d509d8571d8ae3d7cac7495f221bce1d5eae199933cc5711f9ea235bef165dcef8db9fa8ac4c70d9ae336eda4a39d2ce8dfdeb17"/>
  <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
  <PROPERTY NAME="display_name" VALUE="unk11"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
  <PROPERTY NAME="record_type_num" VALUE="64"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="17415861289"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="enable_host_name_policy" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.temp"/>
  <PROPERTY NAME="name" VALUE="unk"/>
  <PROPERTY NAME="record_type" VALUE="TYPE64"/>
  <PROPERTY NAME="record_rdata" VALUE="600 test.com. mandatory=&quot;alpn,ipv4hint&quot; ech=&quot;AEn+DQBFKwAgACABWIHUGj4u+PIggYXcR5JF0gYk3dCRioBW8uJq9H4mKAAIAAEAAQABAANAEnB1YmxpYy50bHMtZWNoLmRldgAA&quot; key65=&quot;ex1&quot; port=10443 key64=&quot;ex2&quot; alpn=&quot;h2,h3&quot; ipv4hint=********* no-default-alpn ipv6hint=2001:db8::1"/>
  <PROPERTY NAME="record_rdata_hash" VALUE="8f6cbe1258a6f6eb04550de76ff7e1d5b81dc47ecf193c25d08d0133cdf9616d77d33479cfa57b71b3fde9c78caef6eb782d1635db72f8d5e735cce6b4f1f5fd"/>
  <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
  <PROPERTY NAME="display_name" VALUE="unk"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.bind_resource_record"/>
  <PROPERTY NAME="record_type_num" VALUE="64"/>
  <PROPERTY NAME="ttl_option" VALUE="0"/>
  <PROPERTY NAME="disabled" VALUE="false"/>
  <PROPERTY NAME="creation_timestamp" VALUE="17415861289"/>
  <PROPERTY NAME="creator" VALUE="STATIC"/>
  <PROPERTY NAME="ddns_protected" VALUE="false"/>
  <PROPERTY NAME="reclaimable" VALUE="false"/>
  <PROPERTY NAME="forbid_reclamation" VALUE="false"/>
  <PROPERTY NAME="enable_host_name_policy" VALUE="false"/>
  <PROPERTY NAME="zone" VALUE="._default.com.temp"/>
  <PROPERTY NAME="name" VALUE="new_unk"/>
  <PROPERTY NAME="record_type" VALUE="TYPE64"/>
  <PROPERTY NAME="record_rdata" VALUE="\# 90 000106676F6F676C6503636F6D000000000400010005000100030268320002000000030002AA74000400080A0A0A0A141414140005000369B71D0006001020020000000000000000000000000001000700082F717B3F646E737D"/>
  <PROPERTY NAME="record_rdata_hash" VALUE="6f5f2cf22c0aa3f1f9b81812b897b65e0c7efdb7943cff1d8a91700f7754853bd959da06e88b2427c76082b129dabf27075cb14d1bc666bd498b64894c4623e6"/>
  <PROPERTY NAME="subfield_definition" VALUE="P 0 0"/>
  <PROPERTY NAME="display_name" VALUE="new_unk"/>
  <PARTITION-MAP VALUE="010+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="object" VALUE=".com.infoblox.dns.zone$._default.com.temp"/>
  <PROPERTY NAME="tag" VALUE=".Site"/>
  <PROPERTY NAME="value" VALUE="Bengaluru"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.extensible_attributes_value"/>
  <PROPERTY NAME="position" VALUE="0"/>
  <PROPERTY NAME="object" VALUE=".com.infoblox.dns.bind_resource_record$._default.com.temp/new_unk/64/6f5f2cf22c0aa3f1f9b81812b897b65e0c7efdb7943cff1d8a91700f7754853bd959da06e88b2427c76082b129dabf27075cb14d1bc666bd498b64894c4623e6"/>
  <PROPERTY NAME="tag" VALUE=".Site"/>
  <PROPERTY NAME="value" VALUE="Bangalore"/>
  <PROPERTY NAME="inheritance_source" VALUE=".com.infoblox.dns.zone$._default.com.temp"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.zone$.."/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.test-admin"/>
  <PROPERTY NAME="sub_type" VALUE="dns.bind_resource_record"/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.hier_rule"/>
  <PROPERTY NAME="data" VALUE=".com.infoblox.dns.bind_resource_record$._default.com.temp/new_unk/64/6f5f2cf22c0aa3f1f9b81812b897b65e0c7efdb7943cff1d8a91700f7754853bd959da06e88b2427c76082b129dabf27075cb14d1bc666bd498b64894c4623e6"/>
  <PROPERTY NAME="synthetic_data" VALUE=""/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.test-admin"/>
  <PROPERTY NAME="sub_type" VALUE=""/>
  <PROPERTY NAME="allow_read" VALUE="true"/>
  <PROPERTY NAME="allow_modify" VALUE="false"/>
  <PROPERTY NAME="allow_delete" VALUE="false"/>
  <PROPERTY NAME="allow_create" VALUE="false"/>
  <PROPERTY NAME="unmanaged_flag" VALUE="false"/>
  <PARTITION-MAP VALUE="0+"/>
</OBJECT>
</DATABASE>
