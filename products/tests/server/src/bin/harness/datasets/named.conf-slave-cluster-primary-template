// Inspired by examples in chapter 3 of the BIND ARM

options {
	directory "TEST_WORKING_DIRECTORY";	// Working directory
	pid-file "named.pid-slave";		// Put pid file in working dir
	recursion no;				// Do not provide recursive service
};

key "rndc-key" {
	algorithm hmac-md5;
	secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 954
		allow { 127.0.0.1; } keys { "rndc-key"; };
};

view "VIEW_NAME" {

// Root server hints
zone "." { type hint; file "root.hint"; };

// Reverse mapping for loopback address
zone "0.0.127.in-addr.arpa" {
	type master;
	file "localhost.rev-slave";
	notify no;
};

zone "test.infoblox.com" {
	type slave;
	masters port 5353 { 127.0.0.1; };
};

zone " test escape .infoblox.com" {
	type slave;
	masters port 5353 { 127.0.0.1; };
};

zone "1.168.192.in-addr.arpa" {
	type slave;
	masters port 5353 { 127.0.0.1; };
};

zone "b0003.grainger.com" {
	type slave;
	notify no;
	masters port 5353 { 127.0.0.1; };
};

zone "bulk.infoblox.com" {
        type slave;
	masters port 5353 { 127.0.0.1; };
};

zone "2.168.192.in-addr.arpa" {
        type slave;
	masters port 5353 { 127.0.0.1; };
};

zone "3.in-addr.arpa" {
	type slave;
	masters port 5353 { 127.0.0.1; };
};

zone "10.in-addr.arpa" {
	type slave;
	masters port 5353 { 127.0.0.1; };
};

zone "d.c.b.a.ip6.arpa" {
	type slave;
	masters port 5353 { 127.0.0.1; };
};

};
