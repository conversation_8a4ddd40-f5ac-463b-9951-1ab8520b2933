"header-griddhcp","authority","domain_name","recycle_leases","ignore_dhcp_option_list_request","enable_pxe_lease_time","pxe_lease_time","bootfile","bootserver","nextserver","deny_bootp","enable_ddns","ddns_use_option81","ddns_server_always_updates","ddns_generate_hostname","ddns_ttl","retry_ddns_updates","ddns_retry_interval","enable_dhcp_thresholds","high_water_mark","high_water_mark_reset","low_water_mark","low_water_mark_reset","enable_email_warnings","enable_snmp_warnings","email_list","ipv6_domain_name_servers","ping_count","ping_timeout","capture_hostname","enable_leasequery","update_dns_on_lease_renewal","ipv6_update_dns_on_lease_renewal","txt_record_handling","lease_scavenge_time","failover_port","enable_fingerprint","ipv6_enable_ddns","ipv6_ddns_enable_option_fqdn","ipv6_ddns_server_always_updates","ipv6_generate_hostname","ipv6_ddns_domainname","ipv6_ddns_ttl","preferred_lifetime","valid_lifetime","ipv6_domain_name","ipv6_txt_record_handling","ipv6_capture_hostname","ipv6_recycle_leases","ipv6_enable_retry_updates","ipv6_retry_updates_interval","ddns_domainname","lease_per_client_settings","disable_all_nac_filters","format_log_option_82","option60_match_rules","v6_leases_scavenging_enabled","v6_leases_scavenging_grace_period","v6_remember_expired_client_association","prefix_length_mode"
"griddhcp","TRUE","abc.infoblox.com","TRUE","FALSE","TRUE",3600,"bootfile1","abc.domain.com","xyz.domain.com","FALSE","TRUE","TRUE","TRUE","TRUE",,"FALSE",,"TRUE",96,86,0,11,"TRUE","TRUE","<EMAIL>,<EMAIL>","2001::1,2001::2",1,1000,"FALSE","TRUE","TRUE","TRUE","ISC",,648,"TRUE","FALSE","FALSE","FALSE","FALSE","test.domain.com",0,27000,43200,,"ISC","TRUE","TRUE","TRUE",,"test1.domain.com","ONE_LEASE_PER_CLIENT","TRUE","HEX","DHCP/abc/True/2/4","TRUE",21605,"TRUE","EXACT"
