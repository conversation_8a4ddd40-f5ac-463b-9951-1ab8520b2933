<DATABASE NAME="onedb" VERSION="MDXMLTEST">
 <OBJECT>
   <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
   <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.admin-group"/>
   <PROPERTY NAME="action" VALUE="access_cli"/>
   <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.cloud-api-superuser"/>
  <PROPERTY NAME="action" VALUE="access_cli"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
 </OBJECT>
 <OBJECT>
   <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
   <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.super_test_group"/>
   <PROPERTY NAME="action" VALUE="access_cli"/>
   <PROPERTY NAME="allow_flag" VALUE="true"/>
 </OBJECT>
 <OBJECT>
   <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
   <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.test_group"/>
   <PROPERTY NAME="action" VALUE="access_cli"/>
   <PROPERTY NAME="allow_flag" VALUE="true"/>
 </OBJECT>
 <OBJECT>
   <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
   <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$.APP_GE003000000_DNSSUPER_GE_72"/>
   <PROPERTY NAME="action" VALUE="access_cli"/>
   <PROPERTY NAME="allow_flag" VALUE="true"/>
 </OBJECT>
 <OBJECT>
   <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
   <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$6.admin-group"/>
   <PROPERTY NAME="action" VALUE="access_cli"/>
   <PROPERTY NAME="allow_flag" VALUE="true"/>
 </OBJECT>
 <OBJECT>
   <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
   <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$6.APP_GE003000000_DNSSUPER_GE_72"/>
   <PROPERTY NAME="action" VALUE="access_cli"/>
   <PROPERTY NAME="allow_flag" VALUE="true"/>
 </OBJECT>

<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$6.cloud-api-superuser"/>
  <PROPERTY NAME="action" VALUE="access_cli"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$8.admin-group"/>
  <PROPERTY NAME="action" VALUE="access_cli"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$8.APP_GE003000000_DNSSUPER_GE_72"/>
  <PROPERTY NAME="action" VALUE="access_cli"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
<OBJECT>
  <PROPERTY NAME="__type" VALUE=".com.infoblox.one.action_rule"/>
  <PROPERTY NAME="entity" VALUE=".com.infoblox.one.admin_group$8.cloud-api-superuser"/>
  <PROPERTY NAME="action" VALUE="access_cli"/>
  <PROPERTY NAME="allow_flag" VALUE="true"/>
</OBJECT>
</DATABASE>
