$ORIGIN signtest.com.
$TTL 3600
signtest.com.			SOA	master.signtest.com. root.signtest.com. 1 86400 7200 2592000 3600
				NS	master.signtest.com.
				MX	11 mx.signtest.com.
master.signtest.com.		A	***********
				TXT	"TXT for master.signtest.com."
				HINFO	"HINFO for master.signtest.com." "Uses bind_resource_record"
*.signtest.com.			MX	11 mx.signtest.com.
mx.signtest.com.		A	************
www.signtest.com.		SRV	0 1 80 webserver.signtest.com.
webserver.signtest.com.		CNAME	server.signtest.com.
server.signtest.com.		A	***********
; The following should become a "host" structure with three host_addresses and two host_aliases
host.signtest.com.		A	***********
				A	***********4
				A	*************
hostcname.signtest.com.		CNAME	host.signtest.com.
hostcname2.signtest.com.	CNAME	host.signtest.com.
sub.signtest.com.		NS	host.signtest.com.
aliaszone.signtest.com.		DNAME	signtest.com.
with\032a\032few\032spaces.signtest.com.	A	************
8ro4544m678fi6ijkjqcfcsre7nn6jpd.signtest.com.	TXT	"NSEC3 hash of server.signtest.com"
aaaa1.signtest.com.		AAAA	abcd:1234::1
aaaa1.signtest.com.		AAAA	abcd:1234::11
aaaa2.signtest.com.		AAAA	abcd:1234::2
dotted.name.in.signtest.com.	A	*************
yan.inside.signtest.com.	TXT	"A TXT on its own"
ttltest.signtest.com.	4001	TXT	"TTL = 4001"
ttltest.signtest.com.	4444	TXT	"TTL = 4444"
ttltest.signtest.com.	4009	TXT	"TTL = 4009"
ttltest.signtest.com.	4002	TXT	"TTL = 4002"
ttltest.signtest.com.	4011	TXT	"TTL = 4011"
naptr1.signtest.com.		NAPTR 100 10 "" "" "" .
naptr2.signtest.com.		NAPTR 100 10 "" "" "" bar.com.
naptr3.signtest.com.		NAPTR 100 10 "u" "mailto+E2U" "!^.*$!mailto:<EMAIL>!" mail.foo.com.
ptr-fwd1.signtest.com           PTR another.domain.
ptr-fwd2.signtest.com   1664    PTR yet.another.domain.
