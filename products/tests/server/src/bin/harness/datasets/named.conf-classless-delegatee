options {
	directory "TEST_WORKING_DIRECTORY";
	recursion yes;
	listen-on port 53 { 127.0.0.1; };
	allow-transfer { any; };
	transfer-format many-answers;
};

key "rndc-key" {
        algorithm hmac-md5;
        secret "FgLJuuSCc5sD9ewBRJfOUw==";
};

controls {
	inet 127.0.0.1 port 954
	allow { 127.0.0.1; } keys { "rndc-key"; };
};

view "VIEW_NAME" {

zone "0.0.127.in-addr.arpa" in { 
	type master;
	file "localhost.rev";

};

zone "delegation.test.com" in {
	type master;
	file "db.delegation.test.com";
	notify no;
};

zone "prefix.1.1.1.in-addr.arpa" in {
	type master;
	file "db.prefix.1.1.1.in-addr.arpa";
	notify no;
};

zone "localdomain" {
	type master;
	file "db.localdomain";
	notify no;
};

};
