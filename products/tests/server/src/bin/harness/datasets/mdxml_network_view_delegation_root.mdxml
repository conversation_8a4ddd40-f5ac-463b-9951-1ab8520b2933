<MDXML>
  <NEW-TYPES>
    <TYPE NAME=".com.infoblox.dns.network_view_delegation_root"/>
    <TYPE NAME=".com.infoblox.dns.network_container_delegation_root"/>
  </NEW-TYPES>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.network_view" POST-STRUCT-CALLBACK="network_view_common_post_callback_7_4"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.network_container" POST-STRUCT-CALLBACK="collect_network_containers_post_callback_7_4"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.vconnector_directory" POST-STRUCT-CALLBACK="collect_vconnector_directories_post_callback_7_4"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.vconnector_cluster" POST-STRUCT-CALLBACK="fix_and_get_feature_init_post_callback_7_4"/>

  <POST-PROCESSING PROCESS-FUNCTION="insert_delegation_roots"/>
</MDXML>