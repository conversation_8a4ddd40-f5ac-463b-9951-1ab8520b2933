<DATABASE NAME="onedb" VERSION="MDXMLTEST">
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.lease"/>
    <PROPERTY NAME="fingerprint" VALUE="Microsoft Windows XP (Version 5.1, 5.2)"/>
    <PROPERTY NAME="fingerprint_class" VALUE="Windows"/>
    <PROPERTY NAME="node_id" VALUE="0"/>
    <PROPERTY NAME="ip_address" VALUE="********"/>
    <PROPERTY NAME="network_view" VALUE="0"/>
    <PROPERTY NAME="ms_server_id" VALUE="."/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.lease"/>
    <PROPERTY NAME="fingerprint" VALUE="APC Uninterruptible Power Supply"/>
    <PROPERTY NAME="fingerprint_class" VALUE="Monitoring Devices"/>
    <PROPERTY NAME="node_id" VALUE="0"/>
    <PROPERTY NAME="ip_address" VALUE="********"/>
    <PROPERTY NAME="network_view" VALUE="0"/>
    <PROPERTY NAME="ms_server_id" VALUE="."/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.lease_event"/>
    <PROPERTY NAME="fingerprint" VALUE="Ubuntu 11.04"/>
    <PROPERTY NAME="fingerprint_class" VALUE="Linux"/>
    <PROPERTY NAME="timestamp" VALUE=""/>
    <PROPERTY NAME="event" VALUE="Issued"/>
    <PROPERTY NAME="ip_address" VALUE="********"/>
    <PROPERTY NAME="member_ip" VALUE="********"/>
    <PROPERTY NAME="network_view" VALUE="0"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.dhcp_host"/>
    <PROPERTY NAME="fingerprint" VALUE="Apple Airport/Mac OS X"/>
    <PROPERTY NAME="fingerprint_class" VALUE="Routers and APs"/>
    <PROPERTY NAME="node_id" VALUE="0"/>
    <PROPERTY NAME="name" VALUE="finger.print.test.host"/>
    <PROPERTY NAME="network_view" VALUE="0"/>
  </OBJECT> 
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.dns.dhcp_fingerprint"/>
    <PROPERTY NAME="type" VALUE="CUSTOM"/>
    <PROPERTY NAME="disabled" VALUE="false"/>
    <PROPERTY NAME="parent" VALUE="0"/>
    <PROPERTY NAME="name" VALUE="Custom Fingerprint"/>
    <PROPERTY NAME="device_class" VALUE="Linux"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.smart_folder_query_item"/>
    <PROPERTY NAME="value" VALUE="Wii"/>
    <PROPERTY NAME="parent" VALUE=".com.infoblox.one.personal_smart_folder$.com.infoblox.one.admin$admin.ARTO"/>
    <PROPERTY NAME="position" VALUE="0"/>
    <PROPERTY NAME="name" VALUE="fingerprint"/>
  </OBJECT>
  <OBJECT>
    <PROPERTY NAME="__type" VALUE=".com.infoblox.one.smart_folder_query_item"/>
    <PROPERTY NAME="value" VALUE="Macintosh"/>
    <PROPERTY NAME="parent" VALUE=".com.infoblox.one.personal_smart_folder$.com.infoblox.one.admin$admin.ARTO"/>
    <PROPERTY NAME="position" VALUE="1"/>
    <PROPERTY NAME="name" VALUE="fingerprint_class"/>
  </OBJECT>
</DATABASE>
