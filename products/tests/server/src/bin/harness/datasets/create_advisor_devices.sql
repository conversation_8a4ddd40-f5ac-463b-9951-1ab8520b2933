-- MySQL dump 10.13  Distrib 5.5.29, for Linux (x86_64)
--
-- Host: localhost    Database: report
-- ------------------------------------------------------
-- Server version	5.5.29-log

use report;

LOCK TABLES `DevicePhysical` WRITE;
/*!40000 ALTER TABLE `DevicePhysical` DISABLE KEYS */;
TRUNCATE TABLE `DevicePhysical`;

INSERT INTO `DevicePhysical` VALUES
(459548291848246801,0,728610777930471899,1,'2019-03-01 15:23:04',NULL,NULL,'2019-04-29 17:09:24','CISCO1941W-A/K9 chassis','cevChassisC1941W',0,'chassis',-1,'CISCO1941W-A/K9 chassis','V02 ','System Bootstrap, Version 15.0(1r)M9, RELEASE SOFT','15.2(3)T, RELEASE SOFTWARE (fc1)','FTX1526811N','Cisco','CISCO1941W-A/K9','','',NULL),
(6242009019150268375,0,1319435335904437035,7,'2019-03-14 15:58:16',NULL,NULL,'2019-04-29 17:10:49','Nexus C9396PX Chassis','Cisco Systems, Inc.',0,'chassis',0,'Nexus C9396PX Chassis','','','','SAL1911B5YU','','N9K-C9396PX','','','online');
/*!40000 ALTER TABLE `DevicePhysical` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `InfraDevice`
--

LOCK TABLES `InfraDevice` WRITE;
/*!40000 ALTER TABLE `InfraDevice` DISABLE KEYS */;

TRUNCATE TABLE `InfraDevice`;

INSERT INTO `InfraDevice`
(`DataSourceID`, `DeviceID`, `InfraDeviceStartTime`, `InfraDeviceEndTime`, `InfraDeviceChangedCols`, `DeviceIPDotted`, `DeviceIPNumeric`, `DeviceName`, `DeviceType`, `DeviceAssurance`, `DeviceVendor`, `DeviceModel`, `DeviceVersion`, `DeviceSysName`, `DeviceSysDescr`, `DeviceSysLocation`, `DeviceSysContact`, `DeviceDNSName`, `DeviceConfigTimestamp`, `DeviceFirstOccurrenceTime`, `InfraDeviceTimestamp`, `DeviceSAAVersion`, `DeviceRebootTime`, `DeviceRunningConfigLastChangedTime`, `DeviceSavedConfigLastChangedTime`, `DeviceConfigLastCheckedTime`, `DevicePolicyScheduleMode`, `DeviceAddlInfo`, `DeviceMAC`, `ParentDeviceID`, `DeviceContextName`, `DeviceNetBIOSName`, `DeviceOUI`, `MgmtServerDeviceID`, `NetworkDeviceInd`, `RoutingInd`, `SwitchingInd`, `VirtualInd`, `FilteringInd`, `FilterProvisionData`, `VirtualNetworkID`, `VirtualNetworkingInd`, `DeviceUniqueKey`)
VALUES

-- Cisco (3) --
(0,419543732906163030,'2019-03-01 18:48:00',NULL,'DeviceAssurance','*************',**********,'unknown','Switch',72,'Cisco',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2019-03-01 18:23:52','2019-03-01 18:48:00',NULL,NULL,NULL,NULL,NULL,'change',NULL,'',0,'',NULL,NULL,0,1,0,1,0,0,NULL,1,0,NULL),
(0,728610777930471899,'2019-04-24 14:57:55',NULL,'DeviceRunningConfigLastChangedTime,DeviceSavedConfigLastChangedTime','***********',**********,'CE2-3-C1941.qanet.com','Router',99,'Cisco','1941W','15.2(3)T','CE2-3-C1941.qanet.com','Cisco IOS Software, C1900 Software (C1900-UNIVERSALK9-M), Version 15.2(3)T, RELEASE SOFTWARE (fc1)  Technical Support: http://www.cisco.com/techsupport  Copyright (c) 1986-2012 by Cisco Systems, Inc.  Compiled Fri 23-Mar-12 15:35 by prod_rel_team','santaClara-ColoLab','\"<EMAIL>\"',NULL,NULL,'2019-03-01 15:14:51','2019-04-25 10:03:22',NULL,'2018-12-02 19:12:17','2018-12-02 19:12:40','2018-12-02 19:12:17','2019-04-25 09:41:41','change',NULL,'00:00:00:00:00:07',0,'',NULL,'XEROX CORPORATION',0,1,1,0,0,0,NULL,1,1,'f4:29:97:86:ec:2c:58:f8:a4:c2:93:1a:e8:fe:f0:88'),
(0,1319435335904437035,'2019-03-14 16:58:29',NULL,'DeviceName','***********',170398476,'LEAF1','SDN Element',99,'Cisco','N9K-C9396PX','n9000-12.2(1n)',NULL,NULL,NULL,NULL,NULL,NULL,'2019-03-14 15:57:59','2019-04-25 10:33:29',NULL,NULL,NULL,NULL,NULL,'change',NULL,'00:22:BD:F8:19:FF',0,'',NULL,'Cisco',0,1,1,1,0,0,NULL,1,0,NULL),

-- Juniper (3) --
(0,1406980955799672566,'2019-04-24 14:57:46',NULL,'DeviceRunningConfigLastChangedTime','***********',3458730501,'CE2-1_SRX220','Firewall',99,'Juniper','SRX220H2','12.1X44-D15.5','CE2-1_SRX220','NetScreen-25 version 5.4.0r25.0 (SN: 0096122005001294, Firewall+VPN)','','',NULL,NULL,'2019-03-01 15:14:51','2019-04-25 10:13:24',NULL,'2018-05-23 01:52:29','2018-05-23 01:52:30',NULL,'2019-04-25 09:41:59','change',NULL,'28:8A:1C:31:B4:00',0,'',NULL,'Juniper',0,1,1,0,0,0,NULL,1,0,'fc:49:08:ce:e1:87:c0:83:4e:96:70:94:93:0a:bd:f1'),
(0,3638029069453289476,'2019-04-25 08:41:39',NULL,'DeviceRebootTime','***********',3456769541,'PE1-ACX1100','Router',99,'Juniper','ACX1100','12.3X52-D10.4','PE1-ACX1100','Juniper Networks, Inc. ex3300-24t Ethernet Switch, kernel JUNOS 12.3R3.4, Build date: 2013-06-14 02:21:01 UTC Copyright (c) 1996-2013 Juniper Networks, Inc.','','',NULL,NULL,'2019-03-01 15:14:51','2019-04-25 10:03:23',NULL,'2018-05-09 02:53:23','2018-05-09 02:53:23',NULL,'2019-04-25 09:41:31','change',NULL,'02:00:00:00:00:04',0,'',NULL,NULL,0,1,1,0,0,0,NULL,1,1,'f2:84:02:f8:8c:1a:1f:7c:bd:88:09:50:0e:05:10:43'),
(0,4949737678375892384,'2019-04-24 14:57:36',NULL,'DeviceRunningConfigLastChangedTime','***********',3458733061,'CE2-1_SRX220','Firewall',99,'Netscreen','SRX220H2','11.4R7.5','CE2-1_SRX220','Juniper Networks, Inc. ex3300-24t Ethernet Switch, kernel JUNOS 12.3R3.4, Build date: 2013-06-14 02:21:01 UTC Copyright (c) 1996-2013 Juniper Networks, Inc.','','',NULL,NULL,'2019-03-01 15:12:51','2019-04-25 09:42:01',NULL,'2018-05-12 04:49:35','2018-05-12 04:49:38',NULL,'2019-04-25 09:42:01','change',NULL,'54:E0:32:A8:82:20',0,'',NULL,'Juniper',0,1,1,0,0,0,NULL,1,0,'42:69:26:48:39:91:98:ea:24:d2:2b:32:70:96:9c:c4'),

-- Infoblox (5) --
(0,4,'2019-04-24 00:19:38',NULL,'DeviceVersion,DeviceSysDescr,DeviceUniqueKey','**********',33897601,'NM129','vNIOS',99,'Infoblox','Campus','7.2.10','NM129','Infoblox NetMRI, Version 7.2.10, Model VMware, SerialNo VM-CF64-6787D','NM129','Infoblox <<EMAIL>>',NULL,NULL,'2019-04-19 09:51:01','2019-04-25 16:05:59',NULL,'2019-04-23 21:38:02',NULL,NULL,NULL,'change',NULL,'00:50:56:B9:31:FB',0,'',NULL,'VMware, Inc.',0,0,0,0,0,0,NULL,1,0,'52:7e:a5:38:b0:9f:09:10:37:fd:6b:c0:fe:8a:db:05'),
(0,8,'2019-04-25 15:31:00',NULL,'DeviceUniqueKey','*********',33897488,'akhalupau-DEV','NetMRI',99,'Infoblox','Campus',',','akhalupau-DEV','Infoblox NetMRI, Version , Model Campus, SerialNo akhalupau','akhalupau-DEV','Infoblox <<EMAIL>>',NULL,NULL,'2019-04-19 10:00:20','2019-04-25 16:01:42',NULL,'2019-04-21 14:19:44',NULL,NULL,NULL,'change',NULL,'00:50:56:B4:F8:B8',0,'',NULL,'VMware, Inc.',0,0,0,0,0,0,NULL,1,0,'d3:0e:7e:8d:af:35:bb:a3:f7:b1:e8:c0:02:01:af:bf'),
(0,9,'2019-04-21 17:15:37',NULL,'DeviceRebootTime','*********',33897489,'172-19-3-18','NetMRI',99,'Infoblox','Campus','7.3.1.90683','172-19-3-18','Infoblox NetMRI, Version 7.3.1.90683, Model VMware, SerialNo VM-674A-6FD45','172-19-3-18','Infoblox <<EMAIL>>',NULL,NULL,'2019-04-19 10:00:20','2019-04-25 16:06:56',NULL,'2019-04-21 17:11:11',NULL,NULL,NULL,'change',NULL,'00:50:56:B4:B9:D8',0,'',NULL,'VMware, Inc.',0,0,0,0,0,0,NULL,1,0,'5d:ef:55:00:05:a7:bd:e9:50:c0:a0:4c:ef:ee:59:d8'),
(0,10,'2019-04-21 19:14:48',NULL,'DeviceUniqueKey','2.5.60.19',33897491,'19-repartition-732u','NetMRI',99,'Infoblox','Enterprise','7.3.2.93007','19-repartition-732u','Infoblox NetMRI, Version 7.3.2.93007, Model Enterprise, SerialNo 4065201304200020','19-repartition-732u','Infoblox <<EMAIL>>',NULL,NULL,'2019-04-19 10:00:20','2019-04-25 16:02:38',NULL,'2019-04-21 17:07:33',NULL,NULL,NULL,'change',NULL,'AC:16:2D:84:FC:8D',0,'',NULL,'Hewlett Packard',0,0,0,0,0,0,NULL,1,0,'cb:98:25:9a:d0:e9:13:1e:80:63:08:61:73:5c:e8:89'),
(0,11,'2019-04-19 10:07:53',NULL,'DeviceAssurance','2.5.60.20',33897492,'oleg-DEV','NetMRI',99,'Infoblox','Campus',',','oleg-DEV','Infoblox NetMRI, Version , Model Campus, SerialNo oleg','oleg-DEV','Infoblox <<EMAIL>>',NULL,NULL,'2019-04-19 10:00:20','2019-04-25 16:09:46',NULL,'2019-02-24 15:05:16',NULL,NULL,NULL,'change',NULL,'00:50:56:B4:8D:9C',0,'',NULL,'VMware, Inc.',0,0,0,0,0,0,NULL,1,0,'a4:1c:42:9f:39:73:9d:36:32:7b:a4:e2:26:67:b4:9c'),

-- Arista (3) --
(0,8633796504881834078,'2019-04-25 10:03:46',NULL,'DeviceRebootTime','***********',1006749765,'JTCiLABLF7001','Switch-Router',99,'Arista','DCS7050SX128','4.14.3F','JTCiLABLF7001','Arista Networks EOS version v4.14.3F running on an Arista Networks DCS-7050SX-128','','',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:13:31',NULL,'2019-03-15 11:23:20',NULL,NULL,NULL,'change',NULL,'00:1C:73:87:4E:D3',0,'',NULL,'Arista',0,1,1,1,0,0,NULL,1,0,'59:28:88:a7:02:ec:83:aa:68:d6:57:95:3e:99:55:c5'),
(0,7980608693942302107,'2019-04-25 09:13:16',NULL,'DeviceIPNumeric,DeviceAssurance,DeviceMAC,DeviceOUI','***********',168430090,'JTCiLABLF7001','Switch-Router',99,'Arista','DCS7050SX128','4.14.6M','JTCiLABLF7001','Arista Networks EOS version v4.14.6M running on an Arista Networks DCS-7050SX-128','','',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 09:17:14',NULL,'2019-03-15 10:23:01',NULL,NULL,NULL,'change',NULL,'00:1C:73:87:4E:D3',0,'',NULL,'Arista',0,1,0,1,0,0,NULL,1,0,'96:8b:25:5e:a4:c2:2d:79:c9:93:39:8f:e3:7f:db:85'),
(0,4165484829560303059,'2019-04-25 10:03:47',NULL,'DeviceRebootTime','***********',**********,'JTCiLABLF7001','Switch-Router',99,'Arista','DCS7050SX128','4.15.5M','JTCiLABLF7001','Arista Networks EOS version v4.15.5M running on an Arista Networks DCS-7050SX-128','','',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:13:33',NULL,'2019-03-15 11:23:21',NULL,NULL,NULL,'change',NULL,'00:1C:73:87:4E:D3',0,'',NULL,'Arista',0,1,1,1,0,0,NULL,1,1,'cd:e1:71:9c:0f:14:01:5a:40:18:8e:2e:b5:29:81:fc'),

-- CheckPoint (5) --
(0,8959126990705692930,'2019-04-25 10:03:27',NULL,'RoutingInd','***********',**********,'gblothamb2esf11','Firewall',99,'CheckPoint','IP690','IPSO R77.30/6.2-GA100','gblothamb2esf11','IPSO gblothamb2esf11 6.2-GA100 releng 1 08.20.2015-220751 i386','London_Thames_Court','+UK-Network',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:03:27',NULL,'2019-02-01 07:48:16',NULL,NULL,NULL,'change',NULL,'00:A0:8E:BF:B9:00',0,'',NULL,'Check Point Software Technologies',0,1,1,0,0,0,NULL,1,0,'e2:bf:b6:b4:66:08:1d:a5:38:87:2f:a3:c7:b9:a7:41'),
(0,1505761591939457621,'2019-04-25 10:03:26',NULL,'RoutingInd','***********',**********,'FNORM5-DFI-A-VSX6','Firewall',99,'CheckPoint','12600','Gaia R77.20','FNORM5-DFI-A-VSX6','Linux FNORM5-DFI-A-VSX5 2.6.18-92cpx86_64 #1 SMP Thu Sep 24 23:30:47 IDT 2015 x86_64','MARNE_NORD/ZONE/PARIS_BP2I_MARNE_NORD','PARIS_BP2I_MARNE_NORD',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:03:26',NULL,'2018-10-21 12:01:10',NULL,NULL,NULL,'change',NULL,'00:1C:7F:38:DE:7C',0,'',NULL,'Check Point Software Technologies',0,1,1,0,0,0,NULL,1,0,'a3:32:40:0d:5d:b0:6f:04:0f:02:c9:36:8e:18:8a:fc'),
(0,1214530318824802170,'2019-04-25 10:03:26',NULL,'RoutingInd','***********',**********,'vm-tb-Checkpoint-sgw','Firewall',99,'CheckPoint','3100','Gaia R80.10','vm-tb-Checkpoint-sgw','Linux vm-tb-Checkpoint-sgw 2.6.18-92cpx86_64 \\#1 SMP Fri Apr 7 13:55:39 IDT 2017 x86_64','Unknown','root@localhost',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:03:26',NULL,'2019-04-16 21:22:41',NULL,NULL,NULL,'change',NULL,'00:50:56:89:12:46',0,'',NULL,'VMware, Inc.',0,1,1,0,0,0,NULL,1,0,'19:e7:a7:fc:9d:1d:72:a4:31:f2:8a:35:96:0a:4e:8c'),
(0,6380359921095699925,'2019-04-25 10:03:26',NULL,'RoutingInd','***********',**********,'FBAN11-CPL01','Firewall',99,'CheckPoint','Smart-1 3050','Gaia R77.30','FBAN11-CPL01','Linux FBAN11-CPL01 2.6.18-92cpx86_64 #1 SMP Sun Dec 18 14:56:16 IST 2016 x86_64','BASTOGNE_NORTH_BN12','<EMAIL>',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:03:26',NULL,'2018-11-22 17:26:32',NULL,NULL,NULL,'change',NULL,'00:1C:7F:43:0C:0A',0,'',NULL,'Check Point Software Technologies',0,1,1,0,0,0,NULL,1,0,'fd:58:fd:c6:6a:77:9d:e3:cb:74:af:b1:00:af:1c:71'),
(0,80,'2020-04-22 08:36:23',NULL,'DeviceModel,DeviceVersion','***********',**********,'FBAN11-CPL01','Firewall',99,'CheckPoint','Gaia','R77.30','FBAN11-CPL01','Linux FBAN11-CPL01 2.6.18-92cpx86_64 #1 SMP Sun Dec 18 14:56:16 IST 2016 x86_64','BASTOGNE_NORTH_BN12','<EMAIL>',NULL,'2020-04-23 08:00:37','2020-04-17 10:39:46','2020-04-23 09:13:50',NULL,'2019-11-15 13:24:25','2020-04-19 07:31:17','2020-04-19 07:31:17','2020-04-23 08:00:37','change',NULL,'00:1C:7F:43:0C:0A',0,'',NULL,'Check Point Software Technologies',0,1,1,0,0,0,NULL,1,0,'dp:ad:c7:1c:75:87:a7:78:d2:4e:cc:29:e1:61:e0:d4:23'),

-- PaloAltoNetworks (4) --
(0,8337601305972845602,'2019-04-25 09:23:18',NULL,'DeviceAssurance','60.1.200.25',**********,'SaukINTPA01','Firewall',99,'PaloAltoNetworks','PA-7050','7.1.13','SaukINTPA01','Palo Alto Networks PA-7000 series firewall','Unknown','Not Set',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:18:26',NULL,'2019-04-20 20:16:04',NULL,NULL,NULL,'change',NULL,'00:1B:17:EC:0A:3E',0,'',NULL,'Palo Alto Networks',0,1,0,0,0,0,NULL,1,0,'3f:a2:dd:82:ad:2d:1a:8c:37:83:4d:97:0e:c8:8a:ed'),
(0,2998023622066470196,'2019-04-25 09:23:18',NULL,'DeviceAssurance','60.1.200.23',1006749719,'gw.acuity.com','Firewall',99,'PaloAltoNetworks','PA-5020','8.0.2','gw.acuity.com','Palo Alto Networks PA-5000 series firewall','Unknown','Not Set',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:13:34',NULL,'2019-04-21 01:46:40',NULL,NULL,NULL,'change',NULL,'00:1B:17:00:04:10',0,'',NULL,'Palo Alto Networks',0,1,0,0,0,0,NULL,1,0,'3f:04:1c:d5:3f:ea:69:a2:3b:75:69:f8:c6:17:ee:78'),
(0,708545954138265951,'2019-04-25 09:23:18',NULL,'DeviceAssurance','60.1.200.33',1006749729,'EXTPAN1','Firewall',99,'PaloAltoNetworks','PA-5060','8.0.2','EXTPAN1','Palo Alto Networks PA-5000 series firewall','Unknown','Not Set',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:13:39',NULL,'2019-04-24 02:57:40',NULL,NULL,NULL,'change',NULL,'00:1B:17:00:02:10',0,'',NULL,'Palo Alto Networks',0,1,0,0,0,0,NULL,1,0,'d9:1a:19:86:a8:a7:3e:d5:bd:a9:cb:ec:75:4f:00:55'),
(0,3696336508317248877,'2019-04-25 08:53:12',NULL,'DeviceAssurance','60.1.200.18',**********,'LAB-Firewall','Firewall',99,'PaloAltoNetworks','PA-5050','6.1.3','LAB-Firewall','Palo Alto Networks PA-5000 series firewall','LAB','Security',NULL,NULL,'2019-04-25 08:41:05','2019-04-25 09:43:23',NULL,'2018-06-01 13:41:01',NULL,NULL,NULL,'change',NULL,'00:1B:17:46:9B:10',0,'',NULL,'Palo Alto Networks',0,1,1,0,0,0,NULL,1,0,'59:83:c2:9f:c9:2d:12:dc:b5:72:1a:93:b6:25:23:e6'),

-- Riverbed (5) --
(0,3198957118339253073,'2019-04-25 10:03:27',NULL,'RoutingInd','60.1.200.54',**********,'rb01-wsh-dc-legal','WOC',99,'Riverbed','Steelhead EX1150L','4.5.0','rb01-wsh-dc-legal','Linux rb01-wsh-dc-legal 2.6.32 #1 SMP Mon Aug 22 10:26:03 PDT 2016 x86_64','Unknown','<EMAIL>',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:13:30',NULL,'2019-02-02 14:12:55',NULL,NULL,NULL,'change',NULL,'00:0E:B6:51:D2:38',0,'',NULL,'Riverbed',0,1,1,0,0,0,NULL,1,0,'ea:12:b3:db:9d:ce:8f:e8:ff:91:09:da:7e:b8:11:fd'),
(0,1999154227286441309,'2019-04-25 10:08:28',NULL,'RoutingInd','60.1.200.59',**********,'w1-lnswklphx','WOC',99,'Riverbed','Steelhead EX560L','4.5.0','w1-lnswklphx','Linux w1-lnswklphx 2.6.32 #1 SMP Mon Aug 22 10:26:03 PDT 2016 x86_64','Unknown','<EMAIL>',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:18:26',NULL,'2019-03-10 12:27:02',NULL,NULL,NULL,'change',NULL,'00:0E:B6:53:D8:40',0,'',NULL,'Riverbed',0,1,1,0,0,0,NULL,1,0,'de:f7:81:ba:7d:81:a2:a4:d2:fe:78:ac:e8:38:0d:7e'),
(0,7734932100149995383,'2019-04-25 10:03:27',NULL,'RoutingInd','60.1.200.57',**********,'w1-lnswklnor','WOC',99,'Riverbed','Steelhead EX1160VH','4.5.0','w1-lnswklnor','Linux w1-lnswklnor 2.6.32 #1 SMP Mon Aug 22 10:26:03 PDT 2016 x86_64','Unknown','<EMAIL>',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:13:30',NULL,'2019-02-13 00:48:06',NULL,NULL,NULL,'change',NULL,'00:0E:B6:50:22:30',0,'',NULL,'Riverbed',0,1,1,0,0,0,NULL,1,0,'42:6d:9d:a4:e3:b2:3a:0a:e4:c7:b0:a0:b4:12:40:de'),
(0,5323514539682776775,'2019-04-25 10:03:27',NULL,'RoutingInd','60.1.200.53',**********,'w1-lnswklroc','WOC',99,'Riverbed','Steelhead SF3100W3','4.5.0','w1-lnswklroc','Linux w1-lnswklroc 2.6.32 #1 SMP Thu Aug 18 10:13:28 PDT 2016 x86_64','Unknown','<EMAIL>',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:13:40',NULL,'2019-02-06 06:04:09',NULL,NULL,NULL,'change',NULL,'00:0E:B6:53:09:30',0,'',NULL,'Riverbed',0,1,1,0,0,0,NULL,1,0,'a9:35:ca:6d:25:fd:ed:a6:ed:75:fc:64:4d:80:69:bb'),
(0,2237811075767295706,'2019-04-25 10:03:27',NULL,'RoutingInd','60.1.200.62',**********,'w1-lnswklnytele','WOC',99,'Riverbed','5050 (5050H)','rbt_sh 8.5.2c','w1-lnswklnytele','Linux riCNHKCHEUWAE1 2.6.32 #1 SMP Thu Apr 10 15:20:18 PDT 2014 x86_64','Unknown','<EMAIL>',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:13:39',NULL,'2019-03-28 21:59:16',NULL,NULL,NULL,'change',NULL,'00:0E:B6:51:D2:A0',0,'',NULL,'Riverbed',0,1,1,0,0,0,NULL,1,0,'a4:4b:73:73:3c:9c:e7:a7:7b:b5:cd:cb:ae:3c:d5:09'),

-- BlueCoat (3) --
(0,2142764268303543122,'2019-04-29 08:58:28',NULL,'DeviceRebootTime','60.1.200.66',**********,'ProxySG600**********','Proxy',99,'BlueCoat','sg600','6.4.4.1','ProxySG 600 **********','Blue Coat SG-S400 Series, Version: SGOS 6.6.5.4, Release id: 198852 Proxy Edition','somewhere over there','<EMAIL>',NULL,NULL,'2019-04-25 08:43:10','2019-04-30 12:46:32',NULL,'2019-04-16 05:49:48',NULL,NULL,NULL,'change',NULL,'00:D0:83:05:AF:B2',0,'',NULL,'INVERTEX, INC.',0,1,0,0,0,0,NULL,1,0,'4e:a4:4b:3b:f0:c0:7e:38:c1:6d:7f:55:d1:29:fe:64'),
(0,6915858144913503528,'2019-04-29 14:05:51',NULL,'DeviceRebootTime','60.1.200.65',1006749761,'GCCBCSGF','Proxy',99,'BlueCoat',NULL,'6.6.5.13','GCCBCSGF','Blue Coat SG-S500 Series, Version: SGOS 6.6.5.13, Release id: 206389 Proxy Edition','\"\"','<EMAIL> (416) 348-3354',NULL,NULL,'2019-04-25 08:43:10','2019-04-30 12:47:23',NULL,'2019-01-12 01:02:30',NULL,NULL,NULL,'change',NULL,'00:D0:83:0C:D7:56',0,'',NULL,'INVERTEX, INC.',0,1,0,0,0,0,NULL,1,0,'d1:cd:4a:2e:43:22:96:9a:65:10:4c:66:81:b7:f9:a2'),
(0,8595306006988772904,'2019-04-30 12:46:32',NULL,'DeviceRebootTime','60.1.200.63',1006749759,'ProxySG9002712240111','Proxy',99,'BlueCoat','sg900','6.5.2.3','ProxySG 900 2712240111','Blue Coat SG900 Series, Version: SGOS 6.5.2.3, Release id:  125625 MACH5 Edition','Somewhere Else','<EMAIL>',NULL,NULL,'2019-04-25 08:43:10','2019-04-30 12:46:32',NULL,'2019-04-22 04:36:19',NULL,NULL,NULL,'change',NULL,'00:D0:83:07:84:A3',0,'',NULL,'INVERTEX, INC.',0,1,0,0,0,0,NULL,1,0,'c5:9a:9b:5b:8a:13:4c:6a:2a:7f:5e:aa:a1:19:56:a5'),

-- Aruba (3) --
(0,81,'2020-04-21 10:34:55',NULL,'DeviceRebootTime','60.1.200.148',1006749844,'jfk-dev-wap02','Wireless AP',99,'Aruba','ap225','6.4.2.6-4.1.1.10_51844','jfk-dev-wap02','ArubaOS Version 6.4.2.6-4.1.1.10 ','','',NULL,NULL,'2020-04-17 10:39:46','2020-04-21 10:34:55',NULL,'2020-03-18 06:07:53',NULL,NULL,NULL,'change',NULL,'04:BD:88:41:D0:00',0,'',NULL,'Aruba',0,1,0,0,0,0,NULL,1,0,'6c:47:3d:b6:ba:c4:00:1d:56:74:7c:d2:2a:28:60:82'),
(0,76,'2020-04-17 18:03:52',NULL,'DeviceUniqueKey','60.1.200.17',1006749713,'AMHdc1aAC','Wireless Controller',99,'Aruba','A7240-US','5.0.4.5','AMHdc1aAC','ArubaOS (MODEL: Aruba7210-US), Version 6.5.4.7 (64552)',' ',' ',NULL,'2020-04-21 10:01:09','2020-04-17 10:28:00','2020-04-21 10:47:00',NULL,'2020-04-16 10:07:07','2020-04-17 12:35:18','2020-04-17 12:35:18','2020-04-21 10:01:09','change',NULL,'',0,'',NULL,NULL,0,1,0,0,0,0,NULL,1,0,'dp:be:52:7b:18:f0:6e:71:bf:b1:3e:61:70:ce:59:ca:7f'),
(0,5610836252935231563,'2019-04-25 09:23:18',NULL,'DeviceAssurance','60.1.200.17',1006749713,'AMHdc1aAC','Wireless Controller',99,'Aruba','A7240-US','5.0.4.5','AMHdc1aAC','ArubaOS (MODEL: Aruba7210-US), Version 6.5.4.7 (64552)',' ',' ',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:13:42',NULL,'2019-04-15 07:35:25',NULL,NULL,NULL,'change',NULL,'',0,'',NULL,NULL,0,1,0,0,0,0,NULL,1,0,'05:02:27:f9:0b:9e:3f:39:eb:42:3c:31:af:a3:ae:d2'),

-- Nortel/Avaya (2) --
(0,77,'2020-04-21 08:45:55',NULL,'DeviceRebootTime','60.1.200.18',**********,'FP-8560-E1901-10-LEAF-A','Router',20,'Avaya','VSP4850GTS','6.3.1.0','FP-8560-E1901-10-LEAF-A','VSP-8608 (6.3.1.0)','Frederick/ATRF/E1901/DC/10','NCI-Frederick <EMAIL>',NULL,NULL,'2020-04-17 10:28:00','2020-04-21 08:45:55',NULL,'2020-03-04 01:41:37',NULL,NULL,NULL,'change',NULL,'',0,'',NULL,NULL,0,1,0,0,0,0,NULL,1,0,'4c:53:f7:ad:52:b8:20:2c:70:5a:64:cd:c4:3d:13:20'),
(0,78,'2020-04-21 08:45:55',NULL,'DeviceRebootTime','60.1.200.18',**********,'FP-8560-E1901-10-LEAF-A','Router',20,'Nortel','VSP4850GTS','6.3.1.0','FP-8560-E1901-10-LEAF-A','VSP-8608 (6.3.1.0)','Frederick/ATRF/E1901/DC/10','NCI-Frederick <EMAIL>',NULL,NULL,'2020-04-17 10:28:00','2020-04-21 08:45:55',NULL,'2020-03-04 01:41:37',NULL,NULL,NULL,'change',NULL,'',0,'',NULL,NULL,0,1,0,0,0,0,NULL,1,0,'4c:53:f7:ad:52:b8:20:2c:70:5a:64:cd:c4:3d:13:20'),

-- Brocade (1) --
(0,79,'2020-04-19 12:39:20',NULL,'SwitchingInd','60.1.200.25',**********,'USG-06-MD1','Switch-Router',99,'Brocade','ICX6450-48','08.0.40aT213','USG-06-MD1','Brocade Communications Systems, Inc. Stacking System ICX6450-48-HPOE, IronWare Version 07.4.00bT311 Compiled on Oct 3 2012 at 08:42:29 labeled as ICX64S07400b','CANDC-West NET3','NHSS',NULL,'2020-04-21 09:31:31','2020-04-17 10:39:46','2020-04-21 10:48:17',NULL,'2019-09-29 13:05:38','2020-04-19 07:31:01','2020-04-19 07:31:01','2020-04-21 09:31:31','change',NULL,'60:9C:9F:27:89:A0',0,'',NULL,NULL,0,1,1,1,0,0,NULL,1,0,'a4:21:9d:27:7a:71:51:1a:c5:13:92:55:ea:d2:9f:eb'),

-- Others --
(0,11111,'2019-04-25 10:13:33',NULL,'DeviceRebootTime','***********',**********,'PJQ-DC-SFM-ALT-1','Load Balancer',99,'Alteon',NULL,NULL,'PJQ-DC-SFM-ALT-1','Alteon Application Switch 6024XL','','',NULL,NULL,'2019-04-25 08:43:10','2019-04-25 10:13:33',NULL,'2019-01-07 22:10:07',NULL,NULL,NULL,'change',NULL,'2C:B6:93:2F:3C:01',0,'',NULL,'Radware',0,1,1,0,0,0,NULL,1,0,'d7:b8:76:4f:33:9c:b7:c0:7f:ae:b0:f2:f3:42:1f:96');
/*!40000 ALTER TABLE `InfraDevice` ENABLE KEYS */;
UNLOCK TABLES;



--
-- Dumping data for table `Device`
--

LOCK TABLES `Device` WRITE;
/*!40000 ALTER TABLE `Device` DISABLE KEYS */;

TRUNCATE TABLE `Device`;

INSERT INTO `Device`
(`DataSourceID`, `DeviceID`, `DeviceStartTime`, `DeviceEndTime`, `DeviceChangedCols`, `DeviceIPDotted`, `DeviceIPNumeric`, `DeviceName`, `DeviceType`, `DeviceAssurance`, `DeviceVendor`, `DeviceModel`, `DeviceVersion`, `DeviceSysName`, `DeviceSysDescr`, `DeviceSysLocation`, `DeviceSysContact`, `DeviceDNSName`, `DeviceFirstOccurrenceTime`, `DeviceTimestamp`, `DeviceAddlInfo`, `DeviceMAC`, `ParentDeviceID`, `DeviceNetBIOSName`, `DeviceOUI`, `MgmtServerDeviceID`, `InfraDeviceInd`, `NetworkDeviceInd`, `VirtualInd`, `VirtualNetworkID`, `DeviceUniqueKey`)
VALUES 

-- Cisco --
(0,419543732906163030,'2019-03-01 18:48:00',NULL,'DeviceAssurance','*************',**********,'unknown','Switch',72,'Cisco',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2019-03-01 18:23:52','2019-03-01 18:48:00',NULL,'',0,NULL,NULL,0,1,1,0,1,NULL),
(0,728610777930471899,'2019-03-01 18:20:04',NULL,'DeviceUniqueKey','***********',**********,'CE2-3-C1941.qanet.com','Router',99,'Cisco','1941W','15.2(3)T','CE2-3-C1941.qanet.com','Cisco IOS Software, C1900 Software (C1900-UNIVERSALK9-M), Version 15.2(3)T, RELEASE SOFTWARE (fc1)  Technical Support: http://www.cisco.com/techsupport  Copyright (c) 1986-2012 by Cisco Systems, Inc.  Compiled Fri 23-Mar-12 15:35 by prod_rel_team','santaClara-ColoLab','\"<EMAIL>\"',NULL,'2019-03-01 15:14:51','2019-04-25 10:03:22',NULL,'00:00:00:00:00:07',0,NULL,'XEROX CORPORATION',0,1,1,0,1,'f4:29:97:86:ec:2c:58:f8:a4:c2:93:1a:e8:fe:f0:88'),
(0,1319435335904437035,'2019-03-14 16:58:29',NULL,'DeviceName','***********',170398476,'LEAF1','SDN Element',99,'Cisco','N9K-C9396PX','n9000-12.2(1n)',NULL,NULL,NULL,NULL,NULL,'2019-03-14 15:57:59','2019-04-25 10:33:29',NULL,'00:22:BD:F8:19:FF',0,NULL,'Cisco',0,1,1,0,1,NULL),

-- Juniper --
(0,1354046138317536233,'2019-04-25 10:23:27',NULL,'DeviceAssurance','***********',1006749724,'JR-ITE-SPN-A','Linux',69,'Juniper','QFX10008','20180223.045120_builder_junos_151_x53_d66','JR-ITE-SPN-A','QFX10008 Chassis','ITE H05-304','Network Operations 571-272-6700',NULL,'2019-04-25 08:43:10','2019-04-25 10:23:27',NULL,'',0,NULL,NULL,0,0,0,0,1,'04:54:88:e4:dd:c8:28:01:f0:c5:2f:c3:88:b9:11:d9'),
(0,1406980955799672566,'2019-03-01 15:23:11',NULL,'DeviceIPNumeric,DeviceAssurance,DeviceMAC,DeviceOUI','***********',3458730501,'CE2-1_SRX220','Firewall',99,'Juniper','SRX220H2','12.1X44-D15.5','CE2-1_SRX220','NetScreen-25 version 5.4.0r25.0 (SN: 0096122005001294, Firewall+VPN)','','',NULL,'2019-03-01 15:14:51','2019-04-25 10:13:24',NULL,'28:8A:1C:31:B4:00',0,NULL,'Juniper',0,1,1,0,1,'fc:49:08:ce:e1:87:c0:83:4e:96:70:94:93:0a:bd:f1'),
(0,3638029069453289476,'2019-03-01 18:21:18',NULL,'DeviceUniqueKey','***********',3456769541,'PE1-ACX1100','Router',99,'Juniper','ACX1100','12.3X52-D10.4','PE1-ACX1100','Juniper Networks, Inc. ex3300-24t Ethernet Switch, kernel JUNOS 12.3R3.4, Build date: 2013-06-14 02:21:01 UTC Copyright (c) 1996-2013 Juniper Networks, Inc.','','',NULL,'2019-03-01 15:14:51','2019-04-25 10:03:23',NULL,'02:00:00:00:00:04',0,NULL,NULL,0,1,1,0,1,'f2:84:02:f8:8c:1a:1f:7c:bd:88:09:50:0e:05:10:43'),
(0,4949737678375892384,'2019-03-01 15:21:10',NULL,'DeviceAssurance','***********',3458733061,'CE2-1_SRX220','Firewall',99,'Netscreen','SRX220H2','11.4R7.5','CE2-1_SRX220','Juniper Networks, Inc. ex3300-24t Ethernet Switch, kernel JUNOS 12.3R3.4, Build date: 2013-06-14 02:21:01 UTC Copyright (c) 1996-2013 Juniper Networks, Inc.','','',NULL,'2019-03-01 15:12:51','2019-04-25 09:42:01',NULL,'54:E0:32:A8:82:20',0,NULL,'Juniper',0,1,1,0,1,'42:69:26:48:39:91:98:ea:24:d2:2b:32:70:96:9c:c4'),

-- Infoblox --
(0,4,'2019-04-24 00:19:38',NULL,'DeviceVersion,DeviceSysDescr,DeviceUniqueKey','**********',33897601,'NM129','vNIOS',99,'Infoblox','Campus','7.2.10','NM129','Infoblox NetMRI, Version 7.2.10, Model VMware, SerialNo VM-CF64-6787D','NM129','Infoblox <<EMAIL>>',NULL,'2019-04-19 09:51:01','2019-04-25 16:05:59',NULL,'00:50:56:B9:31:FB',0,NULL,'VMware, Inc.',0,1,0,0,1,'52:7e:a5:38:b0:9f:09:10:37:fd:6b:c0:fe:8a:db:05'),
(0,8,'2019-04-25 15:31:00',NULL,'DeviceUniqueKey','*********',33897488,'akhalupau-DEV','NetMRI',99,'Infoblox','Campus',',','akhalupau-DEV','Infoblox NetMRI, Version , Model Campus, SerialNo akhalupau','akhalupau-DEV','Infoblox <<EMAIL>>',NULL,'2019-04-19 10:00:20','2019-04-25 16:01:42',NULL,'00:50:56:B4:F8:B8',0,NULL,'VMware, Inc.',0,1,0,0,1,'d3:0e:7e:8d:af:35:bb:a3:f7:b1:e8:c0:02:01:af:bf'),
(0,9,'2019-04-19 10:07:54',NULL,'DeviceAssurance','*********',33897489,'172-19-3-18','NetMRI',99,'Infoblox','Campus','7.3.1.90683','172-19-3-18','Infoblox NetMRI, Version 7.3.1.90683, Model VMware, SerialNo VM-674A-6FD45','172-19-3-18','Infoblox <<EMAIL>>',NULL,'2019-04-19 10:00:20','2019-04-25 16:06:56',NULL,'00:50:56:B4:B9:D8',0,NULL,'VMware, Inc.',0,1,0,0,1,'5d:ef:55:00:05:a7:bd:e9:50:c0:a0:4c:ef:ee:59:d8'),
(0,10,'2019-04-21 19:14:48',NULL,'DeviceUniqueKey','2.5.60.19',33897491,'19-repartition-732u','NetMRI',99,'Infoblox','Enterprise','7.3.2.93007','19-repartition-732u','Infoblox NetMRI, Version 7.3.2.93007, Model Enterprise, SerialNo 4065201304200020','19-repartition-732u','Infoblox <<EMAIL>>',NULL,'2019-04-19 10:00:20','2019-04-25 16:15:45',NULL,'AC:16:2D:84:FC:8D',0,NULL,'Hewlett Packard',0,1,0,0,1,'cb:98:25:9a:d0:e9:13:1e:80:63:08:61:73:5c:e8:89'),
(0,11,'2019-04-19 10:07:53',NULL,'DeviceAssurance','2.5.60.20',33897492,'oleg-DEV','NetMRI',99,'Infoblox','Campus',',','oleg-DEV','Infoblox NetMRI, Version , Model Campus, SerialNo oleg','oleg-DEV','Infoblox <<EMAIL>>',NULL,'2019-04-19 10:00:20','2019-04-25 16:09:46',NULL,'00:50:56:B4:8D:9C',0,NULL,'VMware, Inc.',0,1,0,0,1,'a4:1c:42:9f:39:73:9d:36:32:7b:a4:e2:26:67:b4:9c'),

-- Arista --
(0,8633796504881834078,'2019-04-25 09:13:38',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','***********',1006749765,'JTCiLABLF7001','Switch-Router',99,'Arista','DCS7050SX128','4.14.3F','JTCiLABLF7001','Arista Networks EOS version v4.14.3F running on an Arista Networks DCS-7050SX-128','','',NULL,'2019-04-25 08:43:10','2019-04-25 10:13:31',NULL,'00:1C:73:87:4E:D3',0,NULL,'Arista',0,1,1,0,1,'59:28:88:a7:02:ec:83:aa:68:d6:57:95:3e:99:55:c5'),
(0,7980608693942302107,'2019-04-25 09:13:16',NULL,'DeviceIPNumeric,DeviceAssurance,DeviceMAC,DeviceOUI','***********',168430090,'JTCiLABLF7001','Switch-Router',99,'Arista','DCS7050SX128','4.14.6M','JTCiLABLF7001','Arista Networks EOS version v4.14.6M running on an Arista Networks DCS-7050SX-128','','',NULL,'2019-04-25 08:43:10','2019-04-25 09:17:14',NULL,'00:1C:73:87:4E:D3',0,NULL,'Arista',0,1,1,0,1,'96:8b:25:5e:a4:c2:2d:79:c9:93:39:8f:e3:7f:db:85'),
(0,4165484829560303059,'2019-04-25 09:13:39',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','***********',**********,'JTCiLABLF7001','Switch-Router',99,'Arista','DCS7050SX128','4.15.5M','JTCiLABLF7001','Arista Networks EOS version v4.15.5M running on an Arista Networks DCS-7050SX-128','','',NULL,'2019-04-25 08:43:10','2019-04-25 10:13:33',NULL,'00:1C:73:87:4E:D3',0,NULL,'Arista',0,1,1,0,1,'cd:e1:71:9c:0f:14:01:5a:40:18:8e:2e:b5:29:81:fc'),

-- CheckPoint --
(0,8959126990705692930,'2019-04-25 09:13:39',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','***********',**********,'gblothamb2esf11','Firewall',99,'CheckPoint','IP690','IPSO R77.30/6.2-GA100','gblothamb2esf11','IPSO gblothamb2esf11 6.2-GA100 releng 1 08.20.2015-220751 i386','London_Thames_Court','+UK-Network',NULL,'2019-04-25 08:43:10','2019-04-25 10:03:27',NULL,'00:A0:8E:BF:B9:00',0,NULL,'Check Point Software Technologies',0,1,1,0,1,'e2:bf:b6:b4:66:08:1d:a5:38:87:2f:a3:c7:b9:a7:41'),
(0,1505761591939457621,'2019-04-25 09:13:31',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','***********',**********,'FNORM5-DFI-A-VSX6','Firewall',99,'CheckPoint','12600','Gaia R77.20','FNORM5-DFI-A-VSX6','Linux FNORM5-DFI-A-VSX5 2.6.18-92cpx86_64 #1 SMP Thu Sep 24 23:30:47 IDT 2015 x86_64','MARNE_NORD/ZONE/PARIS_BP2I_MARNE_NORD','PARIS_BP2I_MARNE_NORD',NULL,'2019-04-25 08:43:10','2019-04-25 10:03:26',NULL,'00:1C:7F:38:DE:7C',0,NULL,'Check Point Software Technologies',0,1,1,0,1,'a3:32:40:0d:5d:b0:6f:04:0f:02:c9:36:8e:18:8a:fc'),
(0,1214530318824802170,'2019-04-25 09:13:35',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','***********',**********,'vm-tb-Checkpoint-sgw','Firewall',99,'CheckPoint','3100','Gaia R80.10','vm-tb-Checkpoint-sgw','Linux vm-tb-Checkpoint-sgw 2.6.18-92cpx86_64 \\#1 SMP Fri Apr 7 13:55:39 IDT 2017 x86_64','Unknown','root@localhost',NULL,'2019-04-25 08:43:10','2019-04-25 10:03:26',NULL,'00:50:56:89:12:46',0,NULL,'VMware, Inc.',0,1,1,0,1,'19:e7:a7:fc:9d:1d:72:a4:31:f2:8a:35:96:0a:4e:8c'),
(0,6380359921095699925,'2019-04-25 09:13:31',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','***********',**********,'FBAN11-CPL01','Firewall',99,'CheckPoint','Smart-1 3050','Gaia R77.30','FBAN11-CPL01','Linux FBAN11-CPL01 2.6.18-92cpx86_64 #1 SMP Sun Dec 18 14:56:16 IST 2016 x86_64','BASTOGNE_NORTH_BN12','<EMAIL>',NULL,'2019-04-25 08:43:10','2019-04-25 10:03:26',NULL,'00:1C:7F:43:0C:0A',0,NULL,'Check Point Software Technologies',0,1,1,0,1,'fd:58:fd:c6:6a:77:9d:e3:cb:74:af:b1:00:af:1c:71'),
(0,80,'2020-04-22 08:36:23',NULL,'DeviceModel,DeviceVersion','***********',**********,'FBAN11-CPL01','Firewall',99,'CheckPoint','Gaia','R77.30','FBAN11-CPL01','Linux FBAN11-CPL01 2.6.18-92cpx86_64 #1 SMP Sun Dec 18 14:56:16 IST 2016 x86_64','BASTOGNE_NORTH_BN12','<EMAIL>',NULL,'2020-04-17 10:39:46','2020-04-23 09:13:50',NULL,'00:1C:7F:43:0C:0A',0,NULL,'Check Point Software Technologies',0,1,1,0,1,'dp:ad:c7:1c:75:87:a7:78:d2:4e:cc:29:e1:61:e0:d4:23'),

-- PaloAltoNetwork --
(0,8337601305972845602,'2019-04-25 09:23:18',NULL,'DeviceAssurance','60.1.200.25',**********,'SaukINTPA01','Firewall',99,'PaloAltoNetworks','PA-7050','7.1.13','SaukINTPA01','Palo Alto Networks PA-7000 series firewall','Unknown','Not Set',NULL,'2019-04-25 08:43:10','2019-04-25 10:18:26',NULL,'00:1B:17:EC:0A:3E',0,NULL,'Palo Alto Networks',0,1,1,0,1,'3f:a2:dd:82:ad:2d:1a:8c:37:83:4d:97:0e:c8:8a:ed'),
(0,2998023622066470196,'2019-04-25 09:23:18',NULL,'DeviceAssurance','60.1.200.23',1006749719,'gw.acuity.com','Firewall',99,'PaloAltoNetworks','PA-5020','8.0.2','gw.acuity.com','Palo Alto Networks PA-5000 series firewall','Unknown','Not Set',NULL,'2019-04-25 08:43:10','2019-04-25 10:13:34',NULL,'00:1B:17:00:04:10',0,NULL,'Palo Alto Networks',0,1,1,0,1,'3f:04:1c:d5:3f:ea:69:a2:3b:75:69:f8:c6:17:ee:78'),
(0,708545954138265951,'2019-04-25 09:23:18',NULL,'DeviceAssurance','60.1.200.33',1006749729,'EXTPAN1','Firewall',99,'PaloAltoNetworks','PA-5060','8.0.2','EXTPAN1','Palo Alto Networks PA-5000 series firewall','Unknown','Not Set',NULL,'2019-04-25 08:43:10','2019-04-25 10:13:39',NULL,'00:1B:17:00:02:10',0,NULL,'Palo Alto Networks',0,1,1,0,1,'d9:1a:19:86:a8:a7:3e:d5:bd:a9:cb:ec:75:4f:00:55'),
(0,3696336508317248877,'2019-04-25 08:53:12',NULL,'DeviceAssurance','60.1.200.18',**********,'LAB-Firewall','Firewall',99,'PaloAltoNetworks','PA-5050','6.1.3','LAB-Firewall','Palo Alto Networks PA-5000 series firewall','LAB','Security',NULL,'2019-04-25 08:41:05','2019-04-25 09:43:23',NULL,'00:1B:17:46:9B:10',0,NULL,'Palo Alto Networks',0,1,1,0,1,'59:83:c2:9f:c9:2d:12:dc:b5:72:1a:93:b6:25:23:e6'),

-- Riverbed --
(0,3198957118339253073,'2019-04-25 09:13:32',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','60.1.200.54',**********,'rb01-wsh-dc-legal','WOC',99,'Riverbed','Steelhead EX1150L','4.5.0','rb01-wsh-dc-legal','Linux rb01-wsh-dc-legal 2.6.32 #1 SMP Mon Aug 22 10:26:03 PDT 2016 x86_64','Unknown','<EMAIL>',NULL,'2019-04-25 08:43:10','2019-04-25 10:13:30',NULL,'00:0E:B6:51:D2:38',0,NULL,'Riverbed',0,1,1,0,1,'ea:12:b3:db:9d:ce:8f:e8:ff:91:09:da:7e:b8:11:fd'),
(0,1999154227286441309,'2019-04-25 09:13:43',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','60.1.200.59',**********,'w1-lnswklphx','WOC',99,'Riverbed','Steelhead EX560L','4.5.0','w1-lnswklphx','Linux w1-lnswklphx 2.6.32 #1 SMP Mon Aug 22 10:26:03 PDT 2016 x86_64','Unknown','<EMAIL>',NULL,'2019-04-25 08:43:10','2019-04-25 10:18:26',NULL,'00:0E:B6:53:D8:40',0,NULL,'Riverbed',0,1,1,0,1,'de:f7:81:ba:7d:81:a2:a4:d2:fe:78:ac:e8:38:0d:7e'),
(0,7734932100149995383,'2019-04-25 09:13:38',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','60.1.200.57',**********,'w1-lnswklnor','WOC',99,'Riverbed','Steelhead EX1160VH','4.5.0','w1-lnswklnor','Linux w1-lnswklnor 2.6.32 #1 SMP Mon Aug 22 10:26:03 PDT 2016 x86_64','Unknown','<EMAIL>',NULL,'2019-04-25 08:43:10','2019-04-25 10:13:30',NULL,'00:0E:B6:50:22:30',0,NULL,'Riverbed',0,1,1,0,1,'42:6d:9d:a4:e3:b2:3a:0a:e4:c7:b0:a0:b4:12:40:de'),
(0,5323514539682776775,'2019-04-25 09:13:45',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','60.1.200.53',**********,'w1-lnswklroc','WOC',99,'Riverbed','Steelhead SF3100W3','4.5.0','w1-lnswklroc','Linux w1-lnswklroc 2.6.32 #1 SMP Thu Aug 18 10:13:28 PDT 2016 x86_64','Unknown','<EMAIL>',NULL,'2019-04-25 08:43:10','2019-04-25 10:13:40',NULL,'00:0E:B6:53:09:30',0,NULL,'Riverbed',0,1,1,0,1,'a9:35:ca:6d:25:fd:ed:a6:ed:75:fc:64:4d:80:69:bb'),
(0,2237811075767295706,'2019-04-25 09:13:42',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','60.1.200.62',**********,'w1-lnswklnytele','WOC',99,'Riverbed','5050 (5050H)','rbt_sh 8.5.2c','w1-lnswklnytele','Linux riCNHKCHEUWAE1 2.6.32 #1 SMP Thu Apr 10 15:20:18 PDT 2014 x86_64','Unknown','<EMAIL>',NULL,'2019-04-25 08:43:10','2019-04-25 10:13:39',NULL,'00:0E:B6:51:D2:A0',0,NULL,'Riverbed',0,1,1,0,1,'a4:4b:73:73:3c:9c:e7:a7:7b:b5:cd:cb:ae:3c:d5:09'),

-- BlueCoat --
(0,2142764268303543122,'2019-04-25 10:52:56',NULL,'InfraDeviceInd,NetworkDeviceInd','60.1.200.66',**********,'ProxySG600**********','Proxy',99,'BlueCoat','sg600','6.4.4.1','ProxySG 600 **********','Blue Coat SG-S400 Series, Version: SGOS 6.6.5.4, Release id: 198852 Proxy Edition','somewhere over there','<EMAIL>',NULL,'2019-04-25 08:43:10','2019-04-30 12:46:32',NULL,'00:D0:83:05:AF:B2',0,NULL,'INVERTEX, INC.',0,1,1,0,1,'4e:a4:4b:3b:f0:c0:7e:38:c1:6d:7f:55:d1:29:fe:64'),
(0,6915858144913503528,'2019-04-25 11:13:30',NULL,'InfraDeviceInd,NetworkDeviceInd','60.1.200.65',1006749761,'GCCBCSGF','Proxy',99,'BlueCoat',NULL,'6.6.5.13','GCCBCSGF','Blue Coat SG-S500 Series, Version: SGOS 6.6.5.13, Release id: 206389 Proxy Edition','\"\"','<EMAIL> (416) 348-3354',NULL,'2019-04-25 08:43:10','2019-04-30 12:47:23',NULL,'00:D0:83:0C:D7:56',0,NULL,'INVERTEX, INC.',0,1,1,0,1,'d1:cd:4a:2e:43:22:96:9a:65:10:4c:66:81:b7:f9:a2'),
(0,8595306006988772904,'2019-04-25 11:13:31',NULL,'InfraDeviceInd,NetworkDeviceInd','60.1.200.63',1006749759,'ProxySG9002712240111','Proxy',99,'BlueCoat','sg900','6.5.2.3','ProxySG 900 2712240111','Blue Coat SG900 Series, Version: SGOS 6.5.2.3, Release id:  125625 MACH5 Edition','Somewhere Else','<EMAIL>',NULL,'2019-04-25 08:43:10','2019-04-30 12:46:32',NULL,'00:D0:83:07:84:A3',0,NULL,'INVERTEX, INC.',0,1,1,0,1,'c5:9a:9b:5b:8a:13:4c:6a:2a:7f:5e:aa:a1:19:56:a5'),

-- Aruba --
(0,81,'2020-04-19 07:20:15',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','60.1.200.148',1006749844,'jfk-dev-wap02','Wireless AP',99,'Aruba','ap225','6.4.2.6-4.1.1.10_51844','jfk-dev-wap02','ArubaOS Version 6.4.2.6-4.1.1.10 ','','',NULL,'2020-04-17 10:39:46','2020-04-20 10:19:02',NULL,'04:BD:88:41:D0:00',0,NULL,'Aruba',0,1,1,0,1,'6c:47:3d:b6:ba:c4:00:1d:56:74:7c:d2:2a:28:60:82'),
(0,76,'2020-04-17 18:03:52',NULL,'DeviceUniqueKey','60.1.200.17',1006749713,'AMHdc1aAC','Wireless Controller',99,'Aruba','A7240-US','5.0.4.5','AMHdc1aAC','ArubaOS (MODEL: Aruba7210-US), Version 6.5.4.7 (64552)',' ',' ',NULL,'2020-04-17 10:28:00','2020-04-20 10:13:23',NULL,'',0,NULL,NULL,0,1,1,0,1,'dp:be:52:7b:18:f0:6e:71:bf:b1:3e:61:70:ce:59:ca:7f'),
(0,5610836252935231563,'2019-04-25 09:23:18',NULL,'DeviceAssurance','60.1.200.17',1006749713,'AMHdc1aAC','Wireless Controller',99,'Aruba','A7240-US','5.0.4.5','AMHdc1aAC','ArubaOS (MODEL: Aruba7210-US), Version 6.5.4.7 (64552)',' ',' ',NULL,'2019-04-25 08:43:10','2019-04-25 10:13:42',NULL,'',0,NULL,NULL,0,1,1,0,1,'05:02:27:f9:0b:9e:3f:39:eb:42:3c:31:af:a3:ae:d2'),

-- Nortel (Avaya) --
(0,77,'2020-04-17 12:33:55',NULL,'DeviceName,DeviceVendor,DeviceModel,DeviceVersion,DeviceSysName,DeviceSysDescr,DeviceSysLocation,DeviceSysContact,DeviceUniqueKey','60.1.200.18',**********,'FP-8560-E1901-10-LEAF-A','Router',20,'Avaya','VSP4850GTS','6.3.1.0','FP-8560-E1901-10-LEAF-A','VSP-8608 (6.3.1.0)','Frederick/ATRF/E1901/DC/10','NCI-Frederick <EMAIL>',NULL,'2020-04-17 10:28:00','2020-04-20 05:10:15',NULL,'',0,NULL,NULL,0,1,1,0,1,'4c:53:f7:ad:52:b8:20:2c:70:5a:64:cd:c4:3d:13:20'),
(0,78,'2020-04-17 12:33:55',NULL,'DeviceName,DeviceVendor,DeviceModel,DeviceVersion,DeviceSysName,DeviceSysDescr,DeviceSysLocation,DeviceSysContact,DeviceUniqueKey','60.1.200.18',**********,'FP-8560-E1901-10-LEAF-A','Router',20,'Nortel','VSP4850GTS','6.3.1.0','FP-8560-E1901-10-LEAF-A','VSP-8608 (6.3.1.0)','Frederick/ATRF/E1901/DC/10','NCI-Frederick <EMAIL>',NULL,'2020-04-17 10:28:00','2020-04-20 05:10:15',NULL,'',0,NULL,NULL,0,1,1,0,1,'4c:53:f7:ad:52:b8:20:2c:70:5a:64:cd:c4:3d:13:20'),

-- Brocade --
(0,79,'2020-04-19 07:17:45',NULL,'DeviceAssurance,DeviceMAC','60.1.200.25',**********,'USG-06-MD1','Switch-Router',99,'Brocade','ICX6450-48','08.0.40aT213','USG-06-MD1','Brocade Communications Systems, Inc. Stacking System ICX6450-48-HPOE, IronWare Version 07.4.00bT311 Compiled on Oct 3 2012 at 08:42:29 labeled as ICX64S07400b','CANDC-West NET3','NHSS',NULL,'2020-04-17 10:39:46','2020-04-20 10:21:43',NULL,'60:9C:9F:27:89:A0',0,NULL,NULL,0,1,1,0,1,'a4:21:9d:27:7a:71:51:1a:c5:13:92:55:ea:d2:9f:eb'),

-- Others --
(0,11111,'2019-04-25 09:23:18',NULL,'DeviceAssurance,DeviceMAC,DeviceOUI','***********',**********,'PJQ-DC-SFM-ALT-1','Load Balancer',99,'Alteon',NULL,NULL,'PJQ-DC-SFM-ALT-1','Alteon Application Switch 6024XL','','',NULL,'2019-04-25 08:43:10','2019-04-25 10:13:33',NULL,'2C:B6:93:2F:3C:01',0,NULL,'Radware',0,1,1,0,1,'d7:b8:76:4f:33:9c:b7:c0:7f:ae:b0:f2:f3:42:1f:96'),
(0,1467043345832493818,'2019-04-25 10:23:27',NULL,'DeviceAssurance','***********',**********,'FALDM2-BNL-VOICEP','Linux',69,'Nokia',NULL,NULL,'FALDM2-BNL-VOICEP','IPSO FALDM2-BNL-VOICEP 6.2-GA055b06 releng 1 07.06.2011-204947 i386','','',NULL,'2019-04-25 08:43:10','2019-04-25 10:23:27',NULL,'',0,NULL,NULL,0,0,0,0,1,'4c:4d:72:8f:8e:ff:83:19:e4:dc:a2:27:20:bf:41:a1'),
(0,6471729958981813365,'2019-04-25 10:23:27',NULL,'DeviceAssurance','***********',**********,'CEST12-D3','Linux',69,'Avocent',NULL,NULL,'CEST12-D3','Avocent ACS 8000','/MARNE_EST/SI_BULLE_MGMT//PARIS_BP2I_MARNE_EST','PARIS_BP2I_MARNE_EST',NULL,'2019-04-25 08:43:10','2019-04-25 10:23:27',NULL,'',0,NULL,NULL,0,0,0,0,1,'d0:48:ed:d8:08:55:6b:43:75:cf:2a:cd:de:56:1e:99');

/*!40000 ALTER TABLE `Device` ENABLE KEYS */;
UNLOCK TABLES;

-- Dump completed on 2019-04-25 10:27:16
