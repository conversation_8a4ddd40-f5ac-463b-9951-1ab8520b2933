ddns-update-style interim;
log-facility daemon;
# fingerprinting enabled;
# DHCP Filter Behavior: new;
# DDNS Domainname Preference: standard;
# Option 82 Logging Format: HEX;
local-address ***********;
ddns-local-address4 ***********;
server-identifier ***********;
not authoritative;
infoblox-ignore-uid false;
infoblox-ignore-macaddr false;
default-lease-time 43200;
min-lease-time 43200;
max-lease-time 43200;
one-lease-per-client false;
ping-number 1;
ping-timeout-ms 1000;
omapi-key DHCP_UPDATER;
omapi-port 7911;

ddns-updates off;
ignore client-updates;

include "/infoblox/var/dhcpd_conf/dhcp_updater.key";

subnet ******** netmask ************* {
	option host-name "add1_option2";
	option merit-dump "add1_option1";
}

subnet ********* netmask ************* {
	option host-name "add1_option2";
	option default-url "add1_option4";
	option merit-dump "override3_option1";
}

subnet ********* netmask ************* {
	option host-name "override3_option2";
	option default-url "override1_option4";
	option merit-dump "override4_option1";
}

subnet ********* netmask ************* {
	option host-name "override4_option2";
	option nwip-domain "add1_option5";
	option default-url "override2_option4";
	option merit-dump "override5_option1";
}

subnet ******** netmask ************* {
	option host-name "override1_option2";
	option merit-dump "override1_option1";
}

subnet ******** netmask ************* {
	option host-name "add1_option2";
	option root-path "add1_option3";
	option merit-dump "add1_option1";
}

subnet ******** netmask ************* {
	option host-name "override2_option2";
	option root-path "add2_option3";
	option merit-dump "override2_option1";
}

subnet 20.0.0.0 netmask ********* {
	option merit-dump "add2_option1";
}

subnet *********** netmask ************* {
	not authoritative;
}

shared-network "test_shared_network" {
	option default-url "add2_option4";
	option host-name "add2_option2";
	option root-path "add3_option3";

	subnet ********* netmask ************* {
		option host-name "override5_option2";
		option nwip-domain "add2_option5";
		option merit-dump "override3_option1";
	}
}

#End of dhcpd.conf file
