options {
  directory "DIRECTORY";
  pid-file "primary-named.pid";
  recursion yes;
  listen-on port 5353 { 127.0.0.1; };
  dnssec-enable DNSSEC-ENABLE;
  dnssec-validation DNSSEC-VALIDATION;
  dnssec-accept-expired DNSSEC-ACCEPT-EXPIRED;
};

key "rndc-key" {
  algorithm hmac-md5;
  secret "FgLJuuSCc5sD9ewBRJfOUw==";
};
controls {
  inet 127.0.0.1 port 9953
    allow { 127.0.0.1; } keys { "rndc-key"; };
};
logging {
  channel dnssec_log {
    file "dnssec.log" size 20m;
    print-time yes;
    print-category yes;
    print-severity yes;
    severity debug 3;
  };
  category dnssec { dnssec_log; };
};
zone "secure.com" {
  type master;
  notify explicit;
  also-notify { 127.0.0.1; };
  allow-update { 127.0.0.1; };
  file "db-primary.secure.com.signedAUTHSUFFIX";
  forwarders {};
};
include "TRUSTEDKEYSFILE";
