# ************************************************************
# SQL dump
#
# MySQL 5.5.24
# Database: netmri, report
# ************************************************************


USE `netmri`;

LOCK TABLES `Device` WRITE;
/*!40000 ALTER TABLE `Device` DISABLE KEYS */;

TRUNCATE TABLE `Device`;

INSERT INTO `Device` (`DeviceID`, `IPAddress`, `Name`, `Type`, `TypeProbability`, `Vendor`, `Model`, `SWVersion`, `SNMPTimestamp`, `SNMPVersion`, `SNMPReadSource`, `SNMPEnabled`, `RespTime`, `UpTime`, `FirstOccurrence`, `LastTimeStamp`, `LastSource`, `ConfigLocked`, `SNMPAuthProto`, `SNMPPrivProto`, `PolicyScheduleMode`, `ParentDeviceID`, `TelnetUserSecure`, `TelnetPasswordSecure`, `SNMPReadSecure`, `SNMPWriteSecure`, `SNMPAuthPWSecure`, `SNMPPrivPWSecure`, `SecureVersion`, `DiscoveryStatus`)
VALUES
    (0x01000,'********','TestDevice01','Router',20,'TestVendor01','TestModel01',NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,NULL,2,NULL,NULL,'change',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL),
    (0x02000,'********','TestDevice02','Switch',20,'TestVendor02','TestModel02',NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,NULL,2,NULL,NULL,'change',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL),
    (0x03000,'********','TestDevice03','Hub',20,'TestVendor03','TestModel03',NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,NULL,2,NULL,NULL,'change',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL),
    (0x04000,'********','TestDevice04','Firewall',20,'TestVendor04','TestModel04',NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,NULL,2,NULL,NULL,'change',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL),
    (0x05000,'********','TestDevice05','Host',20,'TestVendor05','TestModel05',NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,NULL,2,NULL,NULL,'change',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL),
    (0x06000,'********','TestDevice06','SDN Element',20,'TestVendor06','TestModel06',NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,NULL,2,NULL,NULL,'change',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL),
    (0x01002,'********','TestDevice07','Router',20,'TestVendor07','TestModel07',NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,NULL,2,NULL,NULL,'change',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL),
    (0x02002,'********','TestDevice08','Switch',20,'TestVendor08','TestModel08',NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,NULL,2,NULL,NULL,'change',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL);

/*!40000 ALTER TABLE `Device` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifConfig` WRITE;
/*!40000 ALTER TABLE `ifConfig` DISABLE KEYS */;

TRUNCATE TABLE `ifConfig`;

INSERT INTO `ifConfig` (`DeviceID`, `ifIndex`, `Timestamp`, `Name`, `Descr`, `Type`, `Mtu`, `PhysAddress`, `LinkUpDownTrapEnable`, `ConnectorPresent`, `Duplex`, `LowerLayer`, `ifAlias`, `ifDescrRaw`, `ifAdminDuplex`)
VALUES
        (0x01000,1,'0000-00-00 00:00:00','TestIf01','TestDescr01','TestType01',NULL,'00:00:00:00:00:01',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x01000,2,'0000-00-00 00:00:00','TestIf02','TestDescr02','ethernet-csmacd',NULL,'00:00:00:00:00:02',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x01000,3,'0000-00-00 00:00:00','TestIf03','TestDescr03','TestType03',NULL,'00:00:00:00:00:03',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x02000,1,'0000-00-00 00:00:00','TestIf04','TestDescr04','TestType04',NULL,'00:00:00:00:00:04',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x02000,2,'0000-00-00 00:00:00','TestIf05','TestDescr05','TestType05',NULL,'00:00:00:00:00:05',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x02000,3,'0000-00-00 00:00:00','TestIf06','TestDescr06','TestType06',NULL,'00:00:00:00:00:06',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x03000,1,'0000-00-00 00:00:00','TestIf07','TestDescr07','TestType07',NULL,'00:00:00:00:00:07',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x03000,2,'0000-00-00 00:00:00','TestIf08','TestDescr08','TestType08',NULL,'00:00:00:00:00:08',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x03000,3,'0000-00-00 00:00:00','TestIf09','TestDescr09','TestType09',NULL,'00:00:00:00:00:09',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x04000,1,'0000-00-00 00:00:00','TestIf10','TestDescr10','TestType10',NULL,'00:00:00:00:00:0A',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x04000,2,'0000-00-00 00:00:00','TestIf11','TestDescr11','TestType11',NULL,'00:00:00:00:00:0B',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x04000,3,'0000-00-00 00:00:00','TestIf12','TestDescr12','TestType12',NULL,'00:00:00:00:00:0C',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x05000,1,'0000-00-00 00:00:00','TestIf13','TestDescr13','TestType13',NULL,'00:00:00:00:00:0D',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x05000,2,'0000-00-00 00:00:00','TestIf14','TestDescr14','TestType14',NULL,'00:00:00:00:00:0E',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        /* NIOS-55812: add one description with 0x0 character */
        (0x05000,3,'0000-00-00 00:00:00','TestIf15','\0TestDescr15','TestType15',NULL,'00:00:00:00:00:0F',NULL,NULL,NULL,NULL,NULL,NULL,NULL),
        (0x06000,1,'0000-00-00 00:00:00','TestIf16','TestDescr16','TestType16',NULL,'00:00:00:00:00:1A',NULL,NULL,NULL,NULL,NULL,NULL,NULL);

/*!40000 ALTER TABLE `ifConfig` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifAddr` WRITE;
/*!40000 ALTER TABLE `ifAddr` DISABLE KEYS */;

TRUNCATE TABLE `ifAddr`;

INSERT INTO `ifAddr` (`IPAddress`, `DeviceID`, `Timestamp`, `ifIndex`, `NetMask`, `IPAddressDotted`, `SubnetIPNumeric`)
VALUES
        (*********,0x01000,'0000-00-00 00:00:00',1,4278190080,'********',167772160),
        (167772417,0x01000,'0000-00-00 00:00:00',2,4278190080,'********',167772160),
        (167772418,0x01000,'0000-00-00 00:00:00',3,4278190080,'********',167772160),
        (*********,0x02000,'0000-00-00 00:00:00',1,4278190080,'********',167772160),
        (167772673,0x02000,'0000-00-00 00:00:00',2,4278190080,'********',167772160),
        (167772674,0x02000,'0000-00-00 00:00:00',3,4278190080,'********',167772160),
        (*********,0x03000,'0000-00-00 00:00:00',1,4278190080,'********',167772160),
        (167772929,0x03000,'0000-00-00 00:00:00',2,4278190080,'********',167772160),
        (167772930,0x03000,'0000-00-00 00:00:00',3,4278190080,'********',167772160),
        (*********,0x04000,'0000-00-00 00:00:00',1,4278190080,'********',335544320),
        (335545345,0x04000,'0000-00-00 00:00:00',2,4278190080,'********',335544320),
        (335545346,0x04000,'0000-00-00 00:00:00',3,4278190080,'20.0.4.2',335544320),
        (335544325,0x05000,'0000-00-00 00:00:00',1,4278190080,'********',335544320),
        (335545601,0x05000,'0000-00-00 00:00:00',2,4278190080,'20.0.5.1',335544320),
        (335545602,0x05000,'0000-00-00 00:00:00',3,4278190080,'20.0.5.2',335544320),
        (42540508761298096469278130746727333890,0x01000,'0000-00-00 00:00:00',1,340282366920938463444927863358058659840,'2001:104::2',42540508761298096469278130746727333888),
        (335544326,0x06000,'0000-00-00 00:00:00',1,4278190080,'********',335544320);

/*!40000 ALTER TABLE `ifAddr` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `vrfAssignment` WRITE;
/*!40000 ALTER TABLE `vrfAssignment` DISABLE KEYS */;

TRUNCATE TABLE `vrfAssignment`;

INSERT INTO `vrfAssignment` (`DeviceID`, `VirtualNetworkMemberID`, `VirtualNetworkID`, `Timestamp`)
VALUES
        (0x1000, 1, 1, '0000-00-00 00:00:00'),
        (0x1000, 2, 1, '0000-00-00 00:00:00'),
        (0x1000, 3, 1, '0000-00-00 00:00:00'),
        (0x2000, 4, 1, '0000-00-00 00:00:00'),
        (0x2000, 5, 1, '0000-00-00 00:00:00'),
        (0x2000, 6, 1, '0000-00-00 00:00:00'),
        (0x3000, 7, 1, '0000-00-00 00:00:00'),
        (0x3000, 8, 1, '0000-00-00 00:00:00'),
        (0x3000, 9, 1, '0000-00-00 00:00:00'),
        (0x4000, 10, 1, '0000-00-00 00:00:00'),
        (0x4000, 11, 1, '0000-00-00 00:00:00'),
        (0x4000, 12, 1, '0000-00-00 00:00:00'),
        (0x5000, 13, 1, '0000-00-00 00:00:00'),
        (0x5000, 14, 1, '0000-00-00 00:00:00');

/*!40000 ALTER TABLE `vrfAssignment` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DataCollectionStatus` WRITE;
/*!40000 ALTER TABLE `DataCollectionStatus` DISABLE KEYS */;

TRUNCATE TABLE `DataCollectionStatus`;

INSERT INTO `DataCollectionStatus` (`DeviceID`,`SystemInd`,`CPUInd`,`MemoryInd`,`VlansInd`,`ForwardingInd`,`EnvironmentalInd`,`InventoryInd`,`ARPInd`,`RouteInd`,`NeighborInd`,`ConfigInd`,`AccessInd`,`VrfInd`,`SystemTimestamp`,`CPUTimestamp`,`MemoryTimestamp`,`VlansTimestamp`,`ForwardingTimestamp`,`EnvironmentalTimestamp`,`InventoryTimestamp`,`ARPTimestamp`,`RouteTimestamp`,`NeighborTimestamp`,`ConfigTimestamp`,`AccessTimestamp`,`VrfTimestamp`)
VALUES
        (0x1000,'OK','OK','OK','N/A','N/A','N/A','Error','N/A','N/A','N/A','N/A','N/A','N/A','2017-03-01 01:23:15','2017-03-01 04:08:20','2017-03-01 04:08:20',NULL,NULL,'2017-03-01 03:23:19','2017-03-01 03:23:19','2017-03-01 03:27:50',NULL,NULL,NULL,NULL,NULL);

/*!40000 ALTER TABLE `DataCollectionStatus` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DeviceProperty` WRITE;
/*!40000 ALTER TABLE `DeviceProperty` DISABLE KEYS */;

TRUNCATE TABLE `DeviceProperty`;

INSERT INTO `DeviceProperty` (`DeviceID`,`PropertyName`,`PropertyIndex`,`Source`,`Value`,`Timestamp`,`SecureVersion`)
VALUES
        (0x1000,'pIPAddress','','NetMRI','************','2017-03-01 04:17:50',NULL),
        (0x1000,'pStartTime','','NetMRI','2017-02-26 07:19:39.109','2017-03-01 04:17:50',NULL),
        (0x1000,'pCollectionTime','','NetMRI','2017-02-26 07:19:39.126','2017-03-01 04:17:50',NULL),
        (0x1000,'ipForwarding','','SNMP','forwarding','2017-03-01 01:23:15',NULL),
        (0x1000,'sysServices','','SNMP','72','2017-03-01 01:23:15',NULL),
        (0x1000,'sysName','','SNMP','netmri','2017-03-01 01:23:15',NULL),
        (0x1000,'sysObjectID','','SNMP','*******.*******.576','2017-03-01 01:30:25',NULL);

/*!40000 ALTER TABLE `DeviceProperty` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `SNMPTableStatus` WRITE;
/*!40000 ALTER TABLE `SNMPTableStatus` DISABLE KEYS */;

TRUNCATE TABLE `SNMPTableStatus`;

INSERT INTO `SNMPTableStatus` (`DeviceID`,`TableName`,`EndTime`,`PollDuration`)
VALUES
        (0x1000,'atTable','2017-03-01 03:27:50',62),
        (0x1000,'DeviceCpuStats','2017-03-01 04:08:20',NULL),
        (0x1000,'DeviceMemStats','2017-03-01 04:08:20',NULL),
        (0x1000,'hrStorageTable','2017-03-01 04:08:20',NULL),
        (0x1000,'ifConfig','2017-03-01 04:08:20',62),
        (0x1000,'ifPerfHist','2017-03-01 04:08:20',0),
        (0x1000,'ifStatus','2017-03-01 04:08:20',4);

/*!40000 ALTER TABLE `SNMPTableStatus` ENABLE KEYS */;
UNLOCK TABLES;


LOCK TABLES `CIDR` WRITE;

delete from `CIDR`;
INSERT INTO `CIDR` ( `CIDR`
                   , `NumAddr`
                   , `DotAddr`
                   , `HexAddr`
                   , `NumMask`
                   , `DotMask`
                   , `HexMask`
                   , `BlockSize`
                   , `DiscoveryMode`
                   , `UnitID`
                   , `PingSweepInd`
                   , `SmartPingSweepInd`
                   , `StartBlackoutSchedule`
                   , `BlackoutDuration`
                   , `StartPortControlBlackoutSchedule`
                   , `PortControlBlackoutDuration`
                   , `VirtualNetworkID`
                   )

VALUES
      ('10.0.0.0/8',*********,'10.0.0.0',*********,8,'*********',4278190080,16777216,'INCLUDE',1,0,0,'',0,'',0,1),
      ('20.0.0.0/8',*********,'20.0.0.0',*********,8,'*********',4278190080,16777216,'INCLUDE',1,0,0,'',0,'',0,1),
      ('2001:104::/64',42540508761298096469278130746727333890,'2001:104::',42540508761298096469278130746727333890,64,'ffff:ffff:ffff:ffff::',340282366920938463444927863358058659840,18446744073709551616,'INCLUDE',1,0,0,'',0,'',0,1);
UNLOCK TABLES;

LOCK TABLES `SdnNetwork` WRITE;
/*!40000 ALTER TABLE `SdnNetwork` DISABLE KEYS */;
TRUNCATE TABLE `SdnNetwork`;

INSERT INTO `SdnNetwork`
(sdn_network_id, sdn_network_key, sdn_network_name, fabric_id, virtual_network_id, StartTime, EndTime)
VALUES
(1,'883652/L_662029145223466425','infoblox/TestSdnNet1',1,1,'2019-10-23 12:40:09','2019-10-23 14:37:41'),
(2,'883652/L_662029145223466427','infoblox/TestSdnNet2',1,0,'2019-10-23 12:40:09','2019-10-23 14:37:41');

/*!40000 ALTER TABLE `SdnNetwork` ENABLE KEYS */;
UNLOCK TABLES;

USE `report`;

LOCK TABLES `Device` WRITE;
/*!40000 ALTER TABLE `Device` DISABLE KEYS */;

TRUNCATE TABLE `Device`;

INSERT INTO `Device` (`DataSourceID`, `DeviceID`, `DeviceStartTime`, `DeviceEndTime`, `DeviceChangedCols`, `DeviceIPDotted`, `DeviceIPNumeric`, `DeviceName`, `DeviceType`, `DeviceAssurance`, `DeviceVendor`, `DeviceModel`, `DeviceVersion`, `DeviceSysName`, `DeviceSysDescr`, `DeviceSysLocation`, `DeviceSysContact`, `DeviceDNSName`, `DeviceFirstOccurrenceTime`, `DeviceTimestamp`, `DeviceAddlInfo`, `DeviceMAC`, `ParentDeviceID`, `DeviceNetBIOSName`, `DeviceOUI`, `MgmtServerDeviceID`, `InfraDeviceInd`, `NetworkDeviceInd`, `VirtualInd`, `VirtualNetworkID`, `DeviceUniqueKey`)
VALUES
        (1,0x01000,'0000-00-00 00:00:00',NULL,NULL,'********',*********,'TestDevice01','Router',NULL,'Cisco',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,1,0,0,1,NULL),
        (1,0x02000,'0000-00-00 00:00:00',NULL,NULL,'********',*********,'TestDevice02','Switch',NULL,'Cisco',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,1,0,0,1,NULL),
        (1,0x03000,'0000-00-00 00:00:00',NULL,NULL,'********',*********,'TestDevice03','Hub',NULL,'Microsoft',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,1,0,0,1,NULL),
        (2,0x04000,'0000-00-00 00:00:00',NULL,NULL,'********',*********,'TestDevice04','Firewall',NULL,'Juniper',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,1,0,0,1,NULL),
        (2,0x05000,'0000-00-00 00:00:00',NULL,NULL,'********',335544325,'TestDevice05','Host',NULL,'Nokia',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,1,0,0,1,NULL),
        (2,0x06000,'0000-00-00 00:00:00',NULL,NULL,'********',335544326,'TestDevice06','SDN Element',NULL,'Cisco',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,1,0,0,1,NULL),
        (1,0x07000,'0000-00-00 00:00:00',NULL,NULL,'********',671088647,'TestDevice07','test',NULL,'Juniper',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,1,0,0,1,NULL),
        (1,0x08000,'0000-00-00 00:00:00',NULL,NULL,'********',671088648,'TestDevice08','test',NULL,'Nokia',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,1,0,0,1,NULL),
        (1,0x09000,'0000-00-00 00:00:00',NULL,NULL,'********',671088649,'TestDevice09','test',NULL,'Cisco',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,0,1,0,0,1,NULL);

/*!40000 ALTER TABLE `Device` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DeviceGroup` WRITE;
/*!40000 ALTER TABLE `DeviceGroup` DISABLE KEYS */;

TRUNCATE TABLE `DeviceGroup`;

INSERT INTO `DeviceGroup` (
    `DeviceGroupID`,
    `ParentDeviceGroupID`,
    `DeviceGroupDefnID`,
    `DeviceGroupStartTime`,
    `DeviceGroupEndTime`,
    `DeviceGroupChangedCols`,
    `DeviceGroupTimestamp`,
    `DataSourceID`,
    `GroupID`,
    `GroupName`,
    `Criteria`,
    `Rank`,
    `SNMPPolling`,
    `CLIPolling`,
    `SNMPAnalysis`,
    `FingerPrint`,
    `CCSCollection`,
    `VendorDefaultCollection`,
    `ConfigPolling`,
    `PortScanning`,
    `StandardsCompliance`,
    `MemberCount`,
    `ConfigLocked`,
    `PolicyScheduleMode`,
    `PerfEnvPollingInd`,
    `SPMCollectionInd`,
    `NetBIOSScanningInd`,
    `ARPCacheRefreshInd`,
    `SAMLicensedInd`,
    `AdvancedGroupInd`,
    `IncludeEndHostsInd`)
VALUES
    (2845478125151626911,0,22,'2016-03-03 22:19:34',NULL,'Rank','2016-03-03 22:19:34',0,2845478125151626911,'grid device group','true',1146166105165791232,1,1,0,0,0,0,0,0,0,0,0,'change',1,1,0,1,0,1,1);

/*!40000 ALTER TABLE `DeviceGroup` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `InfraDevice` WRITE;
/*!40000 ALTER TABLE `InfraDevice` DISABLE KEYS */;

TRUNCATE TABLE `InfraDevice`;

INSERT INTO `InfraDevice` (`DataSourceID`, `DeviceID`, `InfraDeviceStartTime`, `InfraDeviceEndTime`, `InfraDeviceChangedCols`, `DeviceIPDotted`, `DeviceIPNumeric`, `DeviceName`, `DeviceType`, `DeviceAssurance`, `DeviceVendor`, `DeviceModel`, `DeviceVersion`, `DeviceSysName`, `DeviceSysDescr`, `DeviceSysLocation`, `DeviceSysContact`, `DeviceDNSName`, `DeviceConfigTimestamp`, `DeviceFirstOccurrenceTime`, `InfraDeviceTimestamp`, `DeviceSAAVersion`, `DeviceRebootTime`, `DeviceRunningConfigLastChangedTime`, `DeviceSavedConfigLastChangedTime`, `DeviceConfigLastCheckedTime`, `DevicePolicyScheduleMode`, `DeviceAddlInfo`, `DeviceMAC`, `ParentDeviceID`, `DeviceContextName`, `DeviceNetBIOSName`, `DeviceOUI`, `MgmtServerDeviceID`, `NetworkDeviceInd`, `RoutingInd`, `SwitchingInd`, `VirtualInd`, `FilteringInd`, `FilterProvisionData`, `VirtualNetworkID`, `VirtualNetworkingInd`, `DeviceUniqueKey`)
VALUES
        (1,0x01000,'0000-00-00 00:00:00',NULL,NULL,'********',*********,'TestDevice01','Router',NULL,'Cisco',NULL,NULL,NULL,'TestDescr1','TestLab01',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'',NULL,NULL,0,0,0,0,0,0,NULL,1,0,NULL),
        (1,0x02000,'0000-00-00 00:00:00',NULL,NULL,'********',*********,'TestDevice02','Switch',NULL,'Cisco',NULL,NULL,NULL,'TestDescr2','TestLab02',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'',NULL,NULL,0,0,0,0,0,0,NULL,1,0,NULL),
        (1,0x03000,'0000-00-00 00:00:00',NULL,NULL,'********',*********,'TestDevice03','Hub',NULL,'Microsoft',NULL,NULL,NULL,NULL,'TestLab03',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'',NULL,NULL,0,0,0,0,0,0,NULL,1,0,NULL),
        (1,0x04000,'0000-00-00 00:00:00',NULL,NULL,'********',*********,'TestDevice04','Firewall',NULL,'Juniper',NULL,NULL,NULL,NULL,'TestLab04',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'',NULL,NULL,0,0,0,0,0,0,NULL,1,0,NULL),
        (1,0x05000,'0000-00-00 00:00:00',NULL,NULL,'********',335544325,'TestDevice05','Host',NULL,'Nokia',NULL,NULL,NULL,NULL,'TestLab05',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'',NULL,NULL,0,0,0,0,0,0,NULL,1,0,NULL),
        (1,0x06000,'0000-00-00 00:00:00',NULL,NULL,'********',335544326,'TestDevice06','SDN Element',NULL,'Cisco',NULL,NULL,NULL,NULL,'TestLab06',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'',NULL,NULL,0,0,0,0,0,0,NULL,1,0,NULL),
        (1,0x07000,'0000-00-00 00:00:00',NULL,NULL,'********',671088647,'TestDevice07','test',NULL,'Juniper',NULL,NULL,NULL,NULL,'TestLab07',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'',NULL,NULL,0,0,0,0,0,0,NULL,1,0,NULL),
        (1,0x08000,'0000-00-00 00:00:00',NULL,NULL,'********',671088648,'TestDevice08','test',NULL,'Nokia',NULL,NULL,NULL,NULL,'TestLab08',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'',NULL,NULL,0,0,0,0,0,0,NULL,1,0,NULL),
        (1,0x09000,'0000-00-00 00:00:00',NULL,NULL,'********',671088649,'TestDevice09','test',NULL,'Cisco',NULL,NULL,NULL,NULL,'TestLab09',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,'',NULL,NULL,0,0,0,0,0,0,NULL,1,0,NULL);

/*!40000 ALTER TABLE `InfraDevice` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `VirtualNetworkMember` WRITE;
/*!40000 ALTER TABLE `VirtualNetworkMember` DISABLE KEYS */;

TRUNCATE TABLE `VirtualNetworkMember`;

INSERT INTO `VirtualNetworkMember` (
    `VirtualNetworkMemberID`,
    `VirtualNetworkMemberStartTime`,
    `VirtualNetworkMemberEndTime`,
    `VirtualNetworkMemberChangedCols`,
    `VirtualNetworkMemberTimestamp`,
    `VirtualNetworkMemberFirstTime`,
    `DataSourceID`,
    `DeviceID`,
    `VirtualNetworkID`,
    `VirtualNetworkMemberName`,
    `VirtualNetworkMemberDescription`,
    `VirtualNetworkMemberArtificialInd`,
    `VirtualNetworkMemberDefaultInd`,
    `DefaultRDType`,
    `DefaultRDLeft`,
    `DefaultRDRight`,
    `DefaultVPNID`,
    `RouteLimit`,
    `WarningLimit`,
    `CurrentCount`
)

VALUES
    (1,NULL,NULL,NULL,NULL,NULL,1,0x1000,1,'TestVirtualNetworkMember1','VRF 1',0,0,NULL,11,1,NULL,NULL,NULL,NULL),
    (2,NULL,NULL,NULL,NULL,NULL,1,0x1000,1,'TestVirtualNetworkMember2','VRF 2',0,0,NULL,22,2,NULL,NULL,NULL,NULL),
    (3,NULL,NULL,NULL,NULL,NULL,1,0x1000,1,'TestVirtualNetworkMember3','VRF 3',0,0,NULL,33,3,NULL,NULL,NULL,NULL),
    (4,NULL,NULL,NULL,NULL,NULL,1,0x2000,1,'TestVirtualNetworkMember4','VRF 4',0,0,NULL,44,4,NULL,NULL,NULL,NULL),
    (5,NULL,NULL,NULL,NULL,NULL,1,0x2000,1,'TestVirtualNetworkMember5','VRF 5',0,0,NULL,55,5,NULL,NULL,NULL,NULL),
    (6,NULL,NULL,NULL,NULL,NULL,1,0x2000,1,'TestVirtualNetworkMember6','VRF 6',0,0,NULL,66,6,NULL,NULL,NULL,NULL),
    (7,NULL,NULL,NULL,NULL,NULL,1,0x3000,1,'TestVirtualNetworkMember7','VRF 7',0,0,NULL,77,7,NULL,NULL,NULL,NULL),
    (8,NULL,NULL,NULL,NULL,NULL,1,0x3000,1,'TestVirtualNetworkMember8','VRF 8',0,0,NULL,88,8,NULL,NULL,NULL,NULL),
    (9,NULL,NULL,NULL,NULL,NULL,1,0x3000,1,'TestVirtualNetworkMember9','VRF 9',0,0,NULL,99,9,NULL,NULL,NULL,NULL),
    (10,NULL,NULL,NULL,NULL,NULL,2,0x4000,1,'TestVirtualNetworkMember10','VRF 10',0,0,NULL,1010,10,NULL,NULL,NULL,NULL),
    (11,NULL,NULL,NULL,NULL,NULL,2,0x4000,1,'TestVirtualNetworkMember11','VRF 11',0,0,NULL,1111,11,NULL,NULL,NULL,NULL),
    (12,NULL,NULL,NULL,NULL,NULL,2,0x4000,1,'TestVirtualNetworkMember12','VRF 12',0,0,NULL,1212,12,NULL,NULL,NULL,NULL),
    (13,NULL,NULL,NULL,NULL,NULL,2,0x5000,1,'TestVirtualNetworkMember13','VRF 13',0,0,NULL,1313,13,NULL,NULL,NULL,NULL),
    (14,NULL,NULL,NULL,NULL,NULL,2,0x5000,1,'TestVirtualNetworkMember14','VRF 14',0,0,NULL,1414,14,NULL,NULL,NULL,NULL),
    (15,NULL,NULL,NULL,NULL,NULL,2,0x5000,1,'TestVirtualNetworkMember15','VRF 15',1,0,NULL,1515,15,NULL,NULL,NULL,NULL);

/*!40000 ALTER TABLE `VirtualNetworkMember` ENABLE KEYS */;
UNLOCK TABLES;


LOCK TABLES `ifConfig` WRITE;
/*!40000 ALTER TABLE `ifConfig` DISABLE KEYS */;

TRUNCATE TABLE `ifConfig`;

INSERT INTO `ifConfig` (`DataSourceID`, `DeviceID`, `InterfaceID`, `ifIndex`, `ifTimestamp`, `ifFirstSeenTime`, `ifStartTime`, `ifEndTime`, `ifChangedCols`, `ifName`, `ifDescr`, `ifType`, `ifMtu`, `ifMAC`, `ifLinkTrap`, `ifConnector`, `ifDuplex`, `ifSpeed`, `ifLowerLayer`, `ifAdminStatus`, `ifOperStatus`, `ifTrunkStatus`, `ifPortFast`, `ifTunnelInd`, `ifVirtualInd`, `ifLinkAggrInd`, `ifAggrMemberInd`, `ifArtificialInd`, `ifLinkAggrIndex`, `AggrInterfaceID`, `ifLastChange`, `ifAlias`, `ifDescrRaw`, `ifAdminDuplex`, `Slot`, `Port`, `PoEPower`, `PoEStatus`, `SwitchPortNumber`, `VirtualNetworkMemberID`, `ifPortControlInd`, `ifSwitchPortMgmtInd`)
VALUES
        (1,0x01000,1,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf01','TestDescr01','TestType01',NULL,'00:00:00:00:00:01',NULL,NULL,NULL,1000000000,NULL,'up','up','off',NULL,0,0,1,0,0,NULL,1,NULL,NULL,NULL,NULL,1,23,NULL,'unsupported',NULL,1,0,0),
        (1,0x01000,2,2,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf02','TestDescr02','ethernet-csmacd',NULL,'00:00:00:00:00:02',NULL,NULL,NULL,1000000000,NULL,'up','down','off',NULL,0,0,0,1,0,NULL,1,NULL,NULL,NULL,NULL,0,55,NULL,'unsupported',NULL,2,0,0),
        (1,0x01000,3,3,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf03','TestDescr03','TestType03',NULL,'00:00:00:00:00:03',NULL,NULL,NULL,1000000,NULL,'down','up','off',NULL,0,0,0,1,0,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,3,0,0),
        (2,0x02000,4,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf04','TestDescr04','TestType04',NULL,'00:00:00:00:00:04',NULL,NULL,NULL,1000000,NULL,'down','up','off',NULL,0,0,1,0,0,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,4,0,0),
        (2,0x02000,5,2,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf05','TestDescr05','TestType05',NULL,'00:00:00:00:00:05',NULL,NULL,NULL,0,NULL,'down','down','off',NULL,0,0,0,1,0,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,5,0,0),
        (2,0x02000,6,3,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf06','TestDescr06','TestType06',NULL,'00:00:00:00:00:06',NULL,NULL,NULL,0,NULL,'down','down','off',NULL,0,0,0,1,0,NULL,4,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,6,0,0),
        (3,0x03000,7,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf07','TestDescr07','TestType07',NULL,'00:00:00:00:00:07',NULL,NULL,NULL,NULL,NULL,'up','up','off',NULL,0,0,1,0,0,NULL,7,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,7,0,0),
        (3,0x03000,8,2,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf08','TestDescr08','TestType08',NULL,'00:00:00:00:00:08',NULL,NULL,NULL,NULL,NULL,'up','down','off',NULL,0,0,0,1,0,NULL,7,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,8,0,0),
        (3,0x03000,9,3,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf09','TestDescr09','TestType09',NULL,'00:00:00:00:00:09',NULL,NULL,NULL,NULL,NULL,'up','up','off',NULL,0,0,0,1,0,NULL,7,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,9,0,0),
        (4,0x04000,10,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf10','TestDescr10','TestType10',NULL,'00:00:00:00:00:0A',NULL,NULL,NULL,NULL,NULL,'up','down','off',NULL,0,0,0,0,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,10,0,0),
        (4,0x04000,11,2,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf11','TestDescr11','TestType11',NULL,'00:00:00:00:00:0B',NULL,NULL,NULL,NULL,NULL,'up','up','off',NULL,0,0,0,0,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,11,0,0),
        (4,0x04000,12,3,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf12','TestDescr12','TestType12',NULL,'00:00:00:00:00:0C',NULL,NULL,NULL,NULL,NULL,'up','down','off',NULL,0,0,0,0,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,12,0,0),
        (5,0x05000,13,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf13','TestDescr13','TestType13',NULL,'00:00:00:00:00:0D',NULL,NULL,NULL,NULL,NULL,'down','up','off',NULL,0,0,0,0,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,13,0,0),
        (5,0x05000,14,2,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf14','TestDescr14','TestType14',NULL,'00:00:00:00:00:0E',NULL,NULL,NULL,NULL,NULL,'down','down','off',NULL,0,0,0,0,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,14,0,0),
        (5,0x05000,15,3,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf15','\0TestDescr15','TestType15',NULL,'00:00:00:00:00:0F',NULL,NULL,NULL,NULL,NULL,'down','up','off',NULL,0,0,0,0,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,15,0,0),
        (6,0x06000,16,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,NULL,'TestIf16','TestDescr16','TestType16',NULL,'00:00:00:00:00:1A',NULL,NULL,NULL,NULL,NULL,'down','up','off',NULL,0,0,0,0,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'unsupported',NULL,NULL,0,0);

/*!40000 ALTER TABLE `ifConfig` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `AggregatedLinkGroupMembership` WRITE;
/*!40000 ALTER TABLE `AggregatedLinkGroupMembership` DISABLE KEYS */;

TRUNCATE TABLE `AggregatedLinkGroupMembership`;

INSERT INTO `AggregatedLinkGroupMembership` (`DataSourceID`, `aggLinkGroupMemberID`, `aggLinkGroupMemberGroupName`, `aggLinkGroupMemberGroupIndex`, `DeviceID`, `aggLinkGroupMemberAggregateInterface`, `aggLinkGroupMemberPhysicalInterface`, `aggLinkGroupMemberStartTime`, `aggLinkGroupMemberEndTime`, `aggLinkGroupMemberTimestamp`, `aggLinkGroupMemberChangedCols`)
VALUES (1,1,0xBAD,0xF00D,0x1000,1,2,'2018-10-08 17:05:43',NULL,'2018-10-25 12:43:38',NULL),
       (1,2,0xBAD,0xF00D,0x2000,4,5,'2018-10-08 17:05:43',NULL,'2018-10-25 12:43:38',NULL),
       (1,3,0xBAD,0xF00D,0x2000,4,6,'2018-10-08 17:05:43',NULL,'2018-10-25 12:43:38',NULL),
       (1,4,0xBAD,0xF00D,0x1000,1,3,'2018-10-08 17:05:43',NULL,'2018-10-25 12:43:38',NULL);
/*!40000 ALTER TABLE `AggregatedLinkGroupMembership` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifAddr` WRITE;
/*!40000 ALTER TABLE `ifAddr` DISABLE KEYS */;

TRUNCATE TABLE `ifAddr`;

INSERT INTO `ifAddr` (`IfAddrID`, `InterfaceID`, `DeviceID`, `ifIndex`, `DataSourceID`, `AddrStartTime`, `AddrEndTime`, `AddrChangedCols`, `AddrTimestamp`, `ifIPDotted`, `ifIPNumeric`, `ifNetMaskDotted`, `ifNetMaskNumeric`, `SubnetIPNumeric`, `SubnetIPDotted`)
VALUES
        (1,1,0x01000,1,1,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',*********,'*********',4278190080,167772160,'10.0.0.0'),
        (2,2,0x01000,2,1,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',167772417,'*********',4278190080,167772160,'10.0.0.0'),
        (3,3,0x01000,3,1,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',167772418,'*********',4278190080,167772160,'10.0.0.0'),
        (4,4,0x02000,1,2,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',*********,'*********',4278190080,167772160,'10.0.0.0'),
        (5,5,0x02000,2,2,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',167772673,'*********',4278190080,167772160,'10.0.0.0'),
        (6,6,0x02000,3,2,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',167772674,'*********',4278190080,167772160,'10.0.0.0'),
        (7,7,0x03000,1,3,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',*********,'*********',4278190080,167772160,'10.0.0.0'),
        (8,8,0x03000,2,3,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',167772929,'*********',4278190080,167772160,'10.0.0.0'),
        (9,9,0x03000,3,3,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',167772930,'*********',4278190080,167772160,'10.0.0.0'),
        (10,10,0x04000,1,4,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',*********,'*********',4278190080,335544320,'20.0.0.0'),
        (11,11,0x04000,2,4,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',335545345,'*********',4278190080,335544320,'20.0.0.0'),
        (12,12,0x04000,3,4,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','20.0.4.2',335545346,'*********',4278190080,335544320,'20.0.0.0'),
        (13,13,0x05000,1,5,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',335544325,'*********',4278190080,335544320,'20.0.0.0'),
        (14,14,0x05000,2,5,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','20.0.5.1',335545601,'*********',4278190080,335544320,'20.0.0.0'),
        (15,15,0x05000,3,5,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','20.0.5.2',335545602,'*********',4278190080,335544320,'20.0.0.0'),
        (16,1,0x01000,1,1,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','2001:104::2',42540508761298096469278130746727333890,'ffff:ffff:ffff:ffff::',340282366920938463444927863358058659840,42540508761298096469278130746727333888,'2001:104::'),
        (17,16,0x06000,1,6,'0000-00-00 00:00:00',NULL,NULL,'0000-00-00 00:00:00','********',335544326,'*********',4278190080,335544320,'20.0.0.0');

/*!40000 ALTER TABLE `ifAddr` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `ifNeighbor` WRITE;
/*!40000 ALTER TABLE `ifNeighbor` DISABLE KEYS */;

TRUNCATE TABLE `ifNeighbor`;

INSERT INTO `ifNeighbor` (`DataSourceID`,`NeighborID`,`DeviceID`,`InterfaceID`,`ifIndex`,`NeighborDeviceID`,`NeighborInterfaceID`,`NeighborIfIndex`,`NeighborFirstSeenTime`,`NeighborStartTime`,`NeighborEndTime`,`NeighborChangedCols`,`NeighborTimestamp`,`CombinedInd`,`CDPInd`,`LLDPInd`,`SerialInd`,`SwitchFwdInd`,`RevSwitchFwdInd`,`DirectEthernetInd`,`IPRoutedInd`,`StaticRoutedInd`,`LocalRoutedInd`,`ProtoRoutedInd`,`BGPRoutedInd`,`OSPFRoutedInd`,`IGRPRoutedInd`,`NetworkDeviceInd`,`NeighborNetworkDeviceInd`,`CDPNeighborID`,`LLDPNeighborID`)
VALUES
        (1,1,0x01000,1,1,0x02000,4,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,NULL,NULL),
        (1,2,0x01000,2,1,0x03000,7,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,NULL,NULL),
        (2,3,0x02000,4,1,0x01000,1,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,NULL,NULL),
        (2,4,0x02000,5,1,0x03000,8,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,NULL,NULL),
        (3,5,0x03000,7,1,0x01000,2,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,NULL,NULL),
        (3,6,0x03000,8,1,0x02000,5,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,NULL,NULL),
        (4,7,0x04000,11,1,0x05000,13,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,NULL,NULL),
        (5,8,0x05000,13,1,0x04000,11,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,1,NULL,NULL),
        (1,9,0x01000,4,1, 0x03000, 1,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,NULL,NULL),
        (1,10,0x01000,1,1, 0x02000, 4,1,'0000-00-00 00:00:00','0000-00-00 00:00:00','0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,NULL,NULL);


/*!40000 ALTER TABLE `ifNeighbor` ENABLE KEYS */;
UNLOCK TABLES;



LOCK TABLES `ifSwitchFwdNeighbor` WRITE;
/*!40000 ALTER TABLE `ifSwitchFwdNeighbor` DISABLE KEYS */;

TRUNCATE TABLE `ifSwitchFwdNeighbor`;

INSERT INTO `ifSwitchFwdNeighbor` (`DataSourceID`,`NeighborID`,`SwitchFwdNeighborMAC`,`SwitchFwdNeighborIPDotted`,`SwitchFwdNeighborIPNumeric`, `SwitchFwdNeighborVlanID`, `SwitchFwdNeighborVlanIndex`)
VALUES
        (1,1,'00:00:00:00:00:02', '********',*********, 1, 1),
        (1,2,'00:00:00:00:00:03', '********0',*********, 2, 1),
        (2,3,'00:00:00:00:00:01', '********',*********, NULL, NULL),
        (2,4,'00:00:00:00:00:03', '********',*********, 0, 0),
        (3,5,'00:00:00:00:00:01', '********',*********, 0, 0),
        (3,6,'00:00:00:00:00:02', '********',*********, 0, 0),
        (4,7,'00:00:00:00:00:05', '********',167772165, 0, 0),
        (5,8,'00:00:00:00:00:04', '********',167772164, 0, 0),
        (1,9,'00:00:00:00:00:02', '2000::2',167772164, 1, 1),
        (1,10,'00:00:00:00:00:02', '********',167772164, 2, 1);


/*!40000 ALTER TABLE `ifSwitchFwdNeighbor` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `VlanMember` WRITE;
/*!40000 ALTER TABLE `VlanMember` DISABLE KEYS */;

TRUNCATE TABLE `VlanMember`;

INSERT INTO `VlanMember` (`DataSourceID`, `DeviceID`, `InterfaceID`, `VlanName`, `VlanMemberEndTime`, `VlanMemberStartTime`, `VlanMemberTimestamp`, `VlanID`, `VlanMemberID`)
VALUES
        (1, 0x01000, 1, 'vlan-1', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 1, 1),
        (1, 0x02000, 7, 'vlan-3', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 1, 2),
        (1, 0x01000, 1, 'vlan-4', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 1, 3),
        (1, 0x01000, 2, 'vlan-2', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 2, 4),
        (1, 0x02000, 10, 'vlan-3', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '0000-00-00 00:00:00', 3, 5);

/*!40000 ALTER TABLE `VlanMember` ENABLE KEYS */;
UNLOCK TABLES;


LOCK TABLES `ifVlan` WRITE;
/*!40000 ALTER TABLE `ifVlan` DISABLE KEYS */;

TRUNCATE TABLE `ifVlan`;

INSERT INTO `ifVlan` (`DataSourceID`, `ifVlanID`, `VlanID`, `DeviceID`, `InterfaceID`)
VALUES
        (1, 1, 1, 0x01000, 1),
        (1, 2, 1, 0x02000, 7),
        (1, 3, 4, 0x01000, 1),
        (1, 4, 2, 0x01000, 2),
        (1, 5, 3, 0x02000, 10);

/*!40000 ALTER TABLE `ifVlan` ENABLE KEYS */;
UNLOCK TABLES;


LOCK TABLES `Vlan` WRITE;
/*!40000 ALTER TABLE `Vlan` DISABLE KEYS */;

TRUNCATE TABLE `Vlan`;

INSERT INTO `Vlan` (`DataSourceID`, `VlanID`, `VlanIndex`, `VlanName`, `VlanEndTime`, `VlanStartTime`, `VlanTimestamp`)
VALUES
        (1, 1, 1, 'vlan-1', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '0000-00-00 00:00:00'),
        (1, 2, 2, 'vlan-2', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '0000-00-00 00:00:00'),
        (1, 3, 3, 'vlan-3', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '0000-00-00 00:00:00'),
        (1, 4, 4, 'vlan-4', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '0000-00-00 00:00:00');

/*!40000 ALTER TABLE `Vlan` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DevicePhysical` WRITE;
/*!40000 ALTER TABLE `DevicePhysical` DISABLE KEYS */;

TRUNCATE TABLE `DevicePhysical`;

INSERT INTO `DevicePhysical` (`DevicePhysicalID`, `DataSourceID`, `DeviceID`, `PhysicalIndex`, `PhysicalStartTime`, `PhysicalTimestamp`, `PhysicalDescr`, `PhysicalClass`, `PhysicalSerialNum`, `PhysicalModelName`, `PhysicalName`)
VALUES
        (1, 1, 0x1000, 1, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'TestItem1', 'Router', '1111', 'TestVendor01', 'TestDevComp1'),
        (2, 1, 0x1000, 1, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'TestItem2', 'Router', '2222', 'TestVendor01', 'TestDevComp2'),
        (3, 1, 0x1000, 1, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'TestItem3', 'Router', '3333', 'TestVendor01', 'TestDevComp3'),
        (4, 1, 0x1000, 1, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'TestItem4', 'chassis', 'SAD061903JE', 'TestVendor02', 'TestDevComp4'),
        (5, 1, 0x1000, 1, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'TestItem5', 'chassis', 'TBA03270652', 'TestVendor02', 'TestDevComp5'),
        (6, 1, 0x2000, 1, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'TestItem6', 'Router', '6666', 'TestVendor03', 'TestDevComp6'),
        (7, 1, 0x2000, 1, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'TestItem7', 'chassis', 'JAE053002JD', 'TestVendor03', 'TestDevComp7'),
        (8, 1, 0x2000, 1, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'TestItem8', 'chassis', 'JAE053101RQ', 'TestVendor03', 'TestDevComp8'),
        (9, 1, 0x2000, 1, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'TestItem9', 'chassis', 'FOX0627A001', 'TestVendor03', 'TestDevComp9'),
        (10, 1, 0x3000, 1, '0000-00-00 00:00:00', '0000-00-00 00:00:00', 'TestItem10', 'chassis', concat('ABC06260005', CHAR(0x08 using utf8), CHAR(0x00 using utf8), CHAR(0x01 using utf8)), 'TestVendor01', 'TestDevComp10');

/*!40000 ALTER TABLE `DevicePhysical` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DeviceSetting` WRITE;
/*!40000 ALTER TABLE `DeviceSetting` DISABLE KEYS */;

TRUNCATE TABLE `DeviceSetting`;

INSERT INTO `DeviceSetting` (`DataSourceID`, `DeviceID`, `DeviceLicensedInd`)
VALUES
        (1,0x01000,1),
        (1,0x02000,1),
        (1,0x03000,1),
        (2,0x04000,1),
        (2,0x05000,1),
        (2,0x06000,1);

/*!40000 ALTER TABLE `DeviceSetting` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `VirtualNetwork` WRITE;
/*!40000 ALTER TABLE `VirtualNetwork` DISABLE KEYS */;

TRUNCATE TABLE `VirtualNetwork`;

INSERT INTO `VirtualNetwork` (`VirtualNetworkID`, `VirtualNetworkName`, `VirtualNetworkDescription`, `VirtualNetworkDeleteInd`)
VALUES
        (1, 'default', 'test', 0),
        (2, 'another', 'development', 0);

/*!40000 ALTER TABLE `VirtualNetwork` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `DeviceRoute` WRITE;
/*!40000 ALTER TABLE `DeviceRoute` DISABLE KEYS */;

TRUNCATE TABLE `DeviceRoute`;

INSERT INTO `DeviceRoute` (`DeviceRouteID`, `RouteStartTime`, `RouteTimestamp`, `DataSourceID`, `DeviceID`, `RouteSubnetIPDotted`, `RouteSubnetIPNumeric`, `RouteCIDR`, `RouteNetMaskDotted`, `RouteNetMaskNumeric`, `RouteMetric1`, `RouteMetric2`, `RouteNextHopIPDotted`, `RouteNextHopIPNumeric`, `RouteProto`, `RouteType`, `RouteAdminDistance`, `VirtualNetworkMemberID`)
VALUES
        (1,'0000-00-00 00:00:00','0000-00-00 00:00:00',1,0x01000,'',0,'10.0.0.0/8','',0,0,0,'',0,'local','local',0,1),
        (2,'0000-00-00 00:00:00','0000-00-00 00:00:00',1,0x02000,'',0,'10.0.0.0/8','',0,0,0,'',0,'local','local',0,1),
        (3,'0000-00-00 00:00:00','0000-00-00 00:00:00',1,0x03000,'',0,'10.0.0.0/8','',0,0,0,'',0,'local','local',0,1),
        (4,'0000-00-00 00:00:00','0000-00-00 00:00:00',1,0x04000,'',0,'20.0.0.0/8','',0,0,0,'',0,'local','local',0,1),
        (5,'0000-00-00 00:00:00','0000-00-00 00:00:00',1,0x05000,'',0,'20.0.0.0/8','',0,0,0,'',0,'local','local',0,1),
        (6,'0000-00-00 00:00:00','0000-00-00 00:00:00',1,0x06000,'',0,'20.0.0.0/8','',0,0,0,'',0,'local','local',0,1),
        (7,'0000-00-00 00:00:00','0000-00-00 00:00:00',1,0x07000,'',0,'30.0.0.0/8','',0,0,0,'',0,'local','local',0,1),
        (8,'0000-00-00 00:00:00','0000-00-00 00:00:00',1,0x08000,'',0,'30.0.0.0/8','',0,0,0,'',0,'local','local',0,1),
        (9,'0000-00-00 00:00:00','0000-00-00 00:00:00',1,0x09000,'',0,'30.0.0.0/8','',0,0,0,'',0,'local','local',0,1);

/*!40000 ALTER TABLE `DeviceRoute` ENABLE KEYS */;
UNLOCK TABLES;


USE `config`;

LOCK TABLES `scan_interfaces` WRITE;
/*!40000 ALTER TABLE `scan_interfaces` DISABLE KEYS */;

TRUNCATE TABLE `scan_interfaces`;

INSERT INTO `scan_interfaces` (
  `id`,
  `unit_id`,
  `if_dev`,
  `name`,
  `physical_if_id`,
  `encap_tag`,
  `ipv4_address`,
  `ipv4_mask`,
  `ipv4_gateway`,
  `ipv6_address`,
  `ipv6_prefix`,
  `ipv6_gateway`,
  `virtual_network_id`,
  `primary_dns_server`,
  `secondary_dns_server`,
  `search_domains`
) VALUES
    (1,0,'eth1','LAN1',NULL,NULL,'',' ',NULL,NULL,' ',NULL,1,'127.0.0.1',NULL,NULL);

/*!40000 ALTER TABLE `scan_interfaces` ENABLE KEYS */;
UNLOCK TABLES;

LOCK TABLES `sdn_controller_settings` WRITE;

SET FOREIGN_KEY_CHECKS = 0;
delete from `sdn_controller_settings`;
SET FOREIGN_KEY_CHECKS = 1;

insert into sdn_controller_settings
(controller_address, sdn_type, id, handle)
values
('api.meraki.com', 'MERAKI', 1, 'Meraki 1'),
('api.meraki.com', 'MERAKI', 2, 'Meraki 2');

/*!40000 ALTER TABLE `sdn_controller_settings` ENABLE KEYS */;
UNLOCK TABLES;
