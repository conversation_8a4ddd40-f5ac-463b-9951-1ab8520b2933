<MDXML>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.bind_ns"
        POST-STRUCT-CALLBACK="dns_bind_ns_common_callback">
    <NEW-MEMBER MEMBER-NAME="shared_record" value="false"/>
    <NEW-MEMBER MEMBER-NAME="dns_service_status" value=""/>
    <NEW-MEMBER MEMBER-NAME="manually_created" value=""/>
    <REMOVE-MEMBER MEMBER-NAME="created_by_bind"/>
    <REMOVE-MEMBER MEMBER-NAME="refcount"/>
    <REMOVE-MEMBER MEMBER-NAME="stealth"/>
  </STRUCTURE-TRANSFORM>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.one.virtual_node"
        POST-STRUCT-CALLBACK="one_virtual_node_common_callback"/>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.zone"
    POST-STRUCT-CALLBACK="gsnsgrp_dns_zone_common_callback"/>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.ns_group"
        POST-STRUCT-CALLBACK="gsnsgrp_nsgroup_cache_objects">
    <NEW-MEMBER MEMBER-NAME="group_type" value="AUTH"/>
  </STRUCTURE-TRANSFORM>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.ns_group_grid_primary"
        POST-STRUCT-CALLBACK="gsnsgrp_cache_nsgroup_servers"/>
  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.ns_group_ext_secondary_server"
        POST-STRUCT-CALLBACK="gsnsgrp_cache_nsgroup_servers"/>

  <STRUCTURE-TRANSFORM STRUCT-NAME=".com.infoblox.dns.cluster_dns_properties"
        POST-STRUCT-CALLBACK="dns_cluster_dns_properties_common_callback"/>

  <POST-PROCESSING PROCESS-FUNCTION="gsnsgrp_common_post_processing"/>

</MDXML>

