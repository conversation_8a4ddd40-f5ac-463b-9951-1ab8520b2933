include "/tmp/tsig.key";

options {
        masterfile-format text;
        zone-statistics yes;
        directory "/tmp";
        version none;
        recursion yes;
        max-recursion-depth 7;
        max-recursion-queries 150;
        infoblox-dns-update-quota 1024;
        infoblox-dns-update-forwarding-quota 1024;
        hostname none;
        listen-on { 127.0.0.1; REPLACE_WITH_NODE_VIP; };
        query-source address REPLACE_WITH_NODE_VIP port *;
        notify-source REPLACE_WITH_NODE_VIP port *;
        transfer-source REPLACE_WITH_NODE_VIP;
        minimal-responses yes;
        max-cache-size 26214400;
        lame-ttl 600;
        tcp-clients 1000;
        transfers-in 10;
        transfers-out 10;
        transfers-per-ns 2;
        serial-query-rate 20;
        max-cache-ttl 604800;
        max-ncache-ttl 10800;
        edns-udp-size 1220;
        max-udp-size 1220; 
        # for service restart: allow_bulkhost_ddns = Refusal
        allow-transfer { !any; };
        transfer-format many-answers;
        max-journal-size 100000K;
};

# Worker threads: default

# Bulk Host Name Templates:
#       Four Octets:            "-$1-$2-$3-$4" (Default)
#       One Octet:              "-$4"
#       Three Octets:           "-$2-$3-$4"
#       Two Octets:             "-$3-$4"

# multi-master master selection: 30-5-120-20

include "/tmp/dhcp_updater.key";

include "/tmp/rndc.key";

controls {
        inet 127.0.0.1 port 953
        allow { 127.0.0.1; } keys { "rndc-key"; };
};

logging {
	 channel ib_syslog { 
		 syslog daemon; 
		 severity info; 
	};
	 category default { ib_syslog; };
};

# Below stanza's are added to ignore unsolicited EDNS client subnet options
server 0.0.0.0/0 { ignore-ecs-opt yes; };
server ::/0 { ignore-ecs-opt yes; };
acl all_dns_views_updater_keys {  key DHCP_UPDATER1; key DHCP_UPDATER4; key DHCP_UPDATER2; key DHCP_UPDATER3; key DHCP_UPDATER5; key DHCP_UPDATER_default; };

# internal
view "1" {  # internal
    match-clients { key DHCP_UPDATER1; !all_dns_views_updater_keys; *********/8; };
    match-destinations { any; };
    recursion no;
    additional-from-cache no;
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220; 
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "0.0.127.in-addr.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.0.0.127.in-addr.arpa.1";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.1";
    };
    zone "test.com" in { # test.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.test.com.1";
        notify yes;
    };
    zone "zone1.com" in { # zone1.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.zone1.com.1";
        notify yes;
    };
    zone "zone2.com" in { # zone2.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.zone2.com.1";
        notify yes;
    };
};
# another_test_view
view "4" {  # another_test_view
    match-clients { key DHCP_UPDATER4; !all_dns_views_updater_keys; *********/8; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220; 
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "." in {
        type hint;
        file "named.cache.4";
    };
    zone "0.0.127.in-addr.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.0.0.127.in-addr.arpa.4";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.4";
    };
    zone "test.com" in { # test.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.test.com.4";
        notify yes;
    };
    zone "sub.test.com" in { # sub.test.com
        type master;
    	database infoblox_zdb;
	    masterfile-format raw;
    	file "azd/db.sub.test.com.4";
        notify yes;
    };
};
# external
view "2" {  # external
    match-clients { key DHCP_UPDATER2; !all_dns_views_updater_keys; *********/24; !***********/16; !***********/23; key "TSIG_TEST_KEY"; key "TSIG_TEST_KEY_2"; };
    match-destinations { any; };
    recursion yes;
    additional-from-cache yes;
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220; 
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "." in {
        type hint;
        file "named.cache.2";
    };
    zone "0.0.127.in-addr.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.0.0.127.in-addr.arpa.2";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.2";
    };
    zone "zone1.com" in { # zone1.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.zone1.com.2";
        notify yes;
    };
    zone "zone2.com" in { # zone2.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.zone2.com.2";
        notify yes;
    };
    zone "zone1.pvt" in { # zone1.pvt
      type slave;
      masters { *********** key "DNS/TSIG_KEY@with_a_slash_sign_in_name"; };
      allow-update-forwarding { key DHCP_UPDATER2;  none; };
      masterfile-format raw;
      file "db.zone1.pvt.2";
      notify explicit;
    };
};
# test_view
view "3" {  # test_view
    match-clients { key DHCP_UPDATER3; !all_dns_views_updater_keys; *********/8; *********/8; *********/8; key "TSIG_TEST_KEY_2"; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220; 
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "." in {
        type hint;
        file "named.cache.3";
    };
    zone "0.0.127.in-addr.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.0.0.127.in-addr.arpa.3";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.3";
    };
    zone "test.com" in { # test.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.test.com.3";
        notify yes;
    };
    zone "sub.test.com" in { # sub.test.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.sub.test.com.3";
        notify yes;
    };
    zone "zone2.com" in { # zone2.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.zone2.com.3";
        notify yes;
    };
};
# another_default
view "5" {  # another_default
    match-clients { key DHCP_UPDATER5; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220; 
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "." in {
        type hint;
        file "named.cache.5";
    };
    zone "0.0.127.in-addr.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.0.0.127.in-addr.arpa.5";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa.5";
    };
    zone "test.com" in { # test.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.test.com.5";
        notify yes;
    };
};
# default
view "_default" {  # default
    match-clients { key DHCP_UPDATER_default; !all_dns_views_updater_keys; any; };
    match-destinations { any; };
    lame-ttl 600;
    max-cache-ttl 604800;
    max-ncache-ttl 10800;
    max-udp-size 1220; 
    edns-udp-size 1220;
    dnssec-validation yes;
    dnssec-accept-expired no;
    zone "." in {
       type hint;
       file "named.cache._default";
    };
    zone "0.0.127.in-addr.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.0.0.127.in-addr.arpa._default";
    };
    zone "*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa" in {
        type master;
        database infoblox_zdb;
        masterfile-format raw;
        file "azd/db.*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.ip6.arpa._default";
    };
    zone "zone1.com" in { # zone1.com
        type master;
    	database infoblox_zdb;
    	masterfile-format raw;
    	file "azd/db.zone1.com._default";
        notify yes;
    };
};
