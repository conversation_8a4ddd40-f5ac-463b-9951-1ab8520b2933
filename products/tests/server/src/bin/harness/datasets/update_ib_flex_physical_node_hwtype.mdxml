<MDXML>
    <STRUCTURE-TRANSFORM struct-name=".com.infoblox.one.physical_node">
        <MEMBER-VALUE-CHANGE member-name="hwtype" pre-xform-value="IB-FLEX-TYPE1" post-xform-value="IB-FLEX"/>
        <MEMBER-VALUE-CHANGE member-name="hwtype" pre-xform-value="IB-FLEX-TYPE2" post-xform-value="IB-FLEX"/>
        <MEMBER-VALUE-CHANGE member-name="hwtype" pre-xform-value="IB-FLEX-TYPE3" post-xform-value="IB-FLEX"/>
        <MEMBER-VALUE-CHANGE member-name="hwtype" pre-xform-value="IB-FLEX-TYPE4" post-xform-value="IB-FLEX"/>
        <MEMBER-VALUE-CHANGE member-name="hwtype" pre-xform-value="IB-FLEX-TYPE5" post-xform-value="IB-FLEX"/>
        <MEMBER-VALUE-CHANGE member-name="hwtype" pre-xform-value="IB-FLEX-TYPE6" post-xform-value="IB-FLEX"/>
        <MEMBER-VALUE-CHANGE member-name="hwtype" pre-xform-value="IB-FLEX-TYPE7" post-xform-value="IB-FLEX"/>
    </STRUCTURE-TRANSFORM>
</MDXML>
