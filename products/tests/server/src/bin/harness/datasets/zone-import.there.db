$TTL 28800

@ IN SOA ns.there. admin.there (4 10800 3600 2592000 900)

there.             IN  NS  ns.there.
ns.there.          IN  A   10.0.0.1

; Will create delegated zone with delegated TTL of 720.
del.there. 720     IN  NS  ns3.del.there.
ns3.del.there.     IN  A   ********
del.there. 720     IN  NS  del.there.
del.there.         IN  A   *******

; Glue record will use delegated TTL.
there.             IN  NS  ns1.del.there.
ns1.del.there.     IN  A   ********

; Will create delegated zone delegated TTL using zone default TTL.
; (Can't tell from AXFR if TTL was explicit.)
del4.there.        IN  NS  ns.del4.there.
ns.del4.there.     IN  A   ********

; Default TTL. Multiple servers.
many.mdel.there.   IN  AAAA  50::1
many.mdel.there.   IN  AAAA  50::2
mdel.there.        IN  NS    many.mdel.there.
many.mdel.there.   IN  A     ********
many.mdel.there.   IN  A     ********
