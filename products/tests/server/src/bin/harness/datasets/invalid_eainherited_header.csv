header-ipv6network,address*,cidr*,always_update_dns,disabled,network_view,EA-Test_M,EA-Test_M,EAInherited-INVALID-HEADER-Test_M
ipv6network,3144:13::,60,TRUE,FALSE,default,42,43,
ipv6network,3144:13:0:1::,64,TRUE,FALSE,default,42,43,INHERIT
ipv6network,3144:13:0:2::,64,TRUE,FALSE,default,42,43,OVERRIDE
ipv6network,3144:13:0:3::,64,TRUE,FALSE,default,42,55,OVERRIDE
ipv6network,3144:13:0:4::,64,TRUE,FALSE,default,,,DELETE
