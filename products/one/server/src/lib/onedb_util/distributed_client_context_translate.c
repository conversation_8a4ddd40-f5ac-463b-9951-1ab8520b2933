#include "infoblox/types.h"
#include "infoblox/one/distributedManager.h"
#include "infoblox/one/distributedManager_ClientContext.h"
#include "infoblox/one/one_controld_functions.h"
#include "../../../../../dns/server/include/infoblox/dns/dns_controld_functions.h"
#include "infoblox/ib_version.h"

/* Translate one_log_file_view_v11_t structure to one_log_file_view_v10_t.
 * This translation should have been introduced when the tcpdump log file
 * name format change was implemented but that didn't happen. This means that
 * that an 8.0.0 or 8.0.1 release will use the v10 message but support
 * the new file name format.
 */
static ib_return_t
tr_one_file_view_v11_v10(imempool* a_mempool,
                         const void* a_input, size_t a_input_len,
                         void** a_output, size_t* a_output_len,
                         translation_result_t* a_result, void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_file_view_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    // v11 and v10 are the same. v11 was introduced for translation of the m_param field.
    ITEST(sizeof(one_file_view_v11_t) == sizeof(one_file_view_v10_t), IERR_FAILURE);
    ICALL(translation_ctx_copy_cb(a_mempool, a_input, a_input_len, a_output, a_output_len, a_result, a_user_data));

    // Check if grid is running 8.x or upgrading from 8.x.
    ib_return_t ret = IERR_SUCCESS;
    char version[MAX_VERSION_NAME_SZ] = "";
    ICALL(rc_alternate_internal_version(version, sizeof(version)));
    // An upgrade is in progress or we wouldn't have gotten here. There ought
    // to be an alternate version. However, if one is not found allow the translation.
    // Allowing translation in this case is an issue only if upgrading from 8.0.0
    // or 8.0.1. This is acceptable as such upgrades will be rare and to have the
    // alternate version also missing will be very rare.

    /* This code is very similar to that in upgrade_version_checker.c. It is replicated
     * in part here because database independence in this context is preferred.
     *
     * Parse source version string. Don't consider parsing error as
     * fatal by catching IERR_FORMAT.
     */
    ib_version_t parsed_version = { { 0, 0}, 0, 0 };
    ret = ib_version_parse_string(version, &parsed_version);
    if (ret == IERR_FORMAT)
    {
      // Shouldn't happen but if we are here it has. Assume that
      // translation is needed.
      ILOG("Unable to parse version string '%s'", version);
    }
    else if (ret != IERR_SUCCESS)
      ICALL(ret);
    else {
      ib_bool_t suppress = FALSE;
      ib_version_t desired_ver = { { 8, 0}, 0, 0 };
      // When the alt version is 8.x the translation is not needed.
      ICALL(ib_version_operator_ge(&parsed_version, &desired_ver, &suppress));
      if (suppress)
        IRETURN(IERR_SUCCESS);
    }

    // Must be upgrading from pre-8.x.
    const one_file_view_v10_t * input = (const one_file_view_v10_t *)a_input;
    const char * tcpdumplog = "tcpdumpLog";
    if (strstr(input->m_param, tcpdumplog)) {
      one_file_view_v11_t * output = (one_file_view_v11_t *)*a_output;
      STRLCPY(output->m_param, tcpdumplog, sizeof(output->m_param));
    }

cleanup:
    ILEAVE;
}

static ib_return_t
tr_one_file_view_v12_v11(imempool* a_mempool,
                         const void* a_input, size_t a_input_len,
                         void** a_output, size_t* a_output_len,
                         translation_result_t* a_result, void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof( one_file_view_v12_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(one_file_view_v11_t);
    const one_file_view_v12_t * input = (const one_file_view_v12_t * ) a_input;
    one_file_view_v11_t * output;
    // size of m_destination_file_name and m_param is increased from 128 to 256 bytes.
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    output->m_start_line = input->m_start_line;
    output->m_requested_lines_count = input->m_requested_lines_count;
    // Changing the length from 256 bytes to 128 bytes.
    memcpy(output->m_destination_file_name, input->m_destination_file_name, sizeof(input->m_destination_file_name));
    memcpy(output->m_param, input->m_param, sizeof(input->m_param));
    output->m_file_selection = input->m_file_selection;
    memcpy(output->m_search_string, input->m_search_string, sizeof(output->m_search_string));
    output->m_is_search_mode = input->m_is_search_mode;

    *a_result = TRANSLATION_RESULT_SUCCESS;
    *a_output = output;
    *a_output_len = out_len;

cleanup:
        ILEAVE;
}

/* Translate one_log_file_view_v11_t structure to one_log_file_view_v10_t */
static ib_return_t
tr_one_log_file_view_v11_v10(imempool* a_mempool,
                                    const void* a_input, size_t a_input_len,
                                    void** a_output, size_t* a_output_len,
                                    translation_result_t* a_result, void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_log_file_view_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    const one_log_file_view_v11_t * input = (const one_log_file_view_v11_t * )a_input;
    if (input->m_file_selection == VIEW_DHCPV6_CFG)
    {
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        ICALL(translation_ctx_copy_cb(a_mempool, a_input, a_input_len, a_output, a_output_len, a_result, a_user_data));
    }

cleanup:
    ILEAVE;
}

/* Translate one_log_file_view_v12_t structure to one_log_file_view_v11_t */
static ib_return_t
tr_one_log_file_view_v12_v11(imempool* a_mempool,
                                    const void* a_input, size_t a_input_len,
                                    void** a_output, size_t* a_output_len,
                                    translation_result_t* a_result, void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_log_file_view_v12_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    const one_log_file_view_v12_t * input = (const one_log_file_view_v12_t * )a_input;
    if (input->m_file_selection == VIEW_DNS_STATS)
    {
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        ICALL(translation_ctx_copy_cb(a_mempool, a_input, a_input_len, a_output, a_output_len, a_result, a_user_data));
    }

cleanup:
    ILEAVE;
}

/* Translate one_log_file_view_v13_t structure to one_log_file_view_v12_t */
static ib_return_t
tr_one_log_file_view_v13_v12(imempool* a_mempool,
                                    const void* a_input, size_t a_input_len,
                                    void** a_output, size_t* a_output_len,
                                    translation_result_t* a_result, void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_log_file_view_v13_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    const one_log_file_view_v13_t * input = (const one_log_file_view_v13_t * )a_input;
    if (input->m_file_selection == VIEW_DNS_ACCEL_CACHE)
    {
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        ICALL(translation_ctx_copy_cb(a_mempool, a_input, a_input_len, a_output, a_output_len, a_result, a_user_data));
    }

cleanup:
    ILEAVE;
}

/* Translate one_log_file_view_v14_t structure to one_log_file_view_v13_t */
static ib_return_t
tr_one_log_file_view_v14_v13(imempool* a_mempool,
                                    const void* a_input, size_t a_input_len,
                                    void** a_output, size_t* a_output_len,
                                    translation_result_t* a_result, void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_log_file_view_v14_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    const one_log_file_view_v14_t * input = (const one_log_file_view_v14_t * )a_input;
    if (input->m_file_selection == VIEW_PTOPLOG)
    {
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        ICALL(translation_ctx_copy_cb(a_mempool, a_input, a_input_len, a_output, a_output_len, a_result, a_user_data));
    }

cleanup:
    ILEAVE;
}

/* Translate one_log_file_view_v15_t structure to one_log_file_view_v14_t */
static ib_return_t
tr_one_log_file_view_v15_v14(imempool* a_mempool,
                                    const void* a_input, size_t a_input_len,
                                    void** a_output, size_t* a_output_len,
                                    translation_result_t* a_result, void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_log_file_view_v15_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    const one_log_file_view_v15_t * input = (const one_log_file_view_v15_t * )a_input;
    if (input->m_file_selection == VIEW_DISCOVERY_CSV_ERRLOG)
    {
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        ICALL(translation_ctx_copy_cb(a_mempool, a_input, a_input_len, a_output, a_output_len, a_result, a_user_data));
    }

cleanup:
    ILEAVE;
}

/* Translate one_log_file_view_v16_t structure to one_log_file_view_v15_t */
static ib_return_t
tr_one_log_file_view_v16_v15(imempool* a_mempool,
                                    const void* a_input, size_t a_input_len,
                                    void** a_output, size_t* a_output_len,
                                    translation_result_t* a_result, void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_log_file_view_v16_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    const one_log_file_view_v16_t * input = (const one_log_file_view_v16_t * )a_input;
    if (input->m_file_selection == VIEW_FP_CPU_USAGE)
    {   
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        ICALL(translation_ctx_copy_cb(a_mempool, a_input, a_input_len, a_output, a_output_len, a_result, a_user_data));
    }

cleanup:
    ILEAVE;
}

/* Translate dhcp_lease_state_change_req_v11_t structure to dhcp_lease_state_change_req_v10_t */
static ib_return_t
tr_dhcp_lease_state_change_req_v11_v10(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(dhcp_lease_state_change_req_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    dhcp_lease_state_change_req_v10_t * output;
    size_t output_len = sizeof(dhcp_lease_state_change_req_v10_t);

    const dhcp_lease_state_change_req_v11_t * input = (const dhcp_lease_state_change_req_v11_t * )a_input;
    if (strlen(input->m_lease_ip) >= MAX_IPV4_ADDR_SZ)
    {
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        ITEST(sizeof(dhcp_lease_state_change_req_v11_t) != sizeof(dhcp_lease_state_change_req_v10_t), IERR_FAILURE);
        MEMPOOL_CALLOC(a_mempool, output_len, &output);
        memcpy(output->m_lease_state, input->m_lease_state, sizeof(output->m_lease_state));
        memcpy(output->m_dhcp_srvr_ip_addr, input->m_dhcp_srvr_ip_addr, sizeof(output->m_dhcp_srvr_ip_addr));
        memcpy(output->m_lease_ip, input->m_lease_ip, sizeof(output->m_lease_ip));
        memcpy(output->m_dhcp_updater_key, input->m_dhcp_updater_key, sizeof(output->m_dhcp_updater_key));
        *a_output_len = output_len;
        *a_output = output;
        *a_result = TRANSLATION_RESULT_SUCCESS;
    }

cleanup:
    ILEAVE;
}

/* Translate dhcp_lease_state_change_req_v12_t structure to dhcp_lease_state_change_req_v11_t */
static ib_return_t
tr_dhcp_lease_state_change_req_v12_v11(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(dhcp_lease_state_change_req_v12_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    dhcp_lease_state_change_req_v11_t * output;
    size_t output_len = sizeof(dhcp_lease_state_change_req_v11_t);

    const dhcp_lease_state_change_req_v12_t * input = (const dhcp_lease_state_change_req_v12_t * )a_input;
    ITEST(sizeof(dhcp_lease_state_change_req_v12_t) != sizeof(dhcp_lease_state_change_req_v11_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, output_len, &output);
    memcpy(output->m_lease_state, input->m_lease_state, sizeof(output->m_lease_state));
    memcpy(output->m_dhcp_srvr_ip_addr, input->m_dhcp_srvr_ip_addr, sizeof(output->m_dhcp_srvr_ip_addr));
    memcpy(output->m_lease_ip, input->m_lease_ip, sizeof(output->m_lease_ip));
    memcpy(output->m_dhcp_updater_key, input->m_dhcp_updater_key, sizeof(output->m_dhcp_updater_key));
    memcpy(output->m_duid, input->m_duid, sizeof(output->m_duid));
    *a_output_len = output_len;
    *a_output = output;
    *a_result = TRANSLATION_RESULT_SUCCESS;


cleanup:
    ILEAVE;
}

/* Translate dhcp_fo_state_change_req_v11_t structure to dhcp_fo_state_change_req_v10_t */
static ib_return_t
tr_dhcp_fo_state_change_req_v11_v10(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(dhcp_fo_state_change_req_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    dhcp_fo_state_change_req_v10_t * output;
    size_t output_len = sizeof(dhcp_fo_state_change_req_v10_t);

    const dhcp_fo_state_change_req_v11_t * input = (const dhcp_fo_state_change_req_v11_t * )a_input;
    ITEST(sizeof(dhcp_fo_state_change_req_v11_t) != sizeof(dhcp_fo_state_change_req_v10_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, output_len, &output);
    memcpy(output->m_partner_state, input->m_partner_state, sizeof(output->m_partner_state));
    memcpy(output->m_peer_ip_addr, input->m_peer_ip_addr, sizeof(output->m_peer_ip_addr));
    memcpy(output->m_failover_assoc_name, input->m_failover_assoc_name, sizeof(output->m_failover_assoc_name));
    memcpy(output->m_dhcp_updater_key, input->m_dhcp_updater_key, sizeof(output->m_dhcp_updater_key));
    *a_output_len = output_len;
    *a_output = output;
    *a_result = TRANSLATION_RESULT_SUCCESS;


cleanup:
    ILEAVE;
}



/* Return mapping value from ib_field_enum_t version 2 to version 1 */
static ib_bool_t
ib_ib_field_enum_t_v2_v1_mapping(int v2_enum_value, int * v1_enum_value)
{
    typedef struct ib_field_enum_v2_v1_mapping_t {
        int v2_enum_value;
        int v1_enum_value;
    } ib_field_enum_v2_v1_mapping_t;

    /* Define mapping between v2 and v1. -1 indicates no mapping. */
    static ib_field_enum_v2_v1_mapping_t mapping[] = {
            {IB_FIELD_ENUM_V2_FIELD_ENDS, IB_FIELD_ENUM_V1_FIELD_ENDS},
            {IB_FIELD_ENUM_V2_FIELD_EVENT, IB_FIELD_ENUM_V1_FIELD_EVENT},
            {IB_FIELD_ENUM_V2_FIELD_HOST_NAME, IB_FIELD_ENUM_V1_FIELD_HOST_NAME},
            {IB_FIELD_ENUM_V2_FIELD_IS_IPV4, -1}, //No mapping
            {IB_FIELD_ENUM_V2_FIELD_IP_ADDRESS, IB_FIELD_ENUM_V1_FIELD_IP_ADDRESS},
            {IB_FIELD_ENUM_V2_FIELD_MAC_ADDRESS, IB_FIELD_ENUM_V1_FIELD_MAC_ADDRESS},
            {IB_FIELD_ENUM_V2_FIELD_SERVER_HOST_NAME, IB_FIELD_ENUM_V1_FIELD_SERVER_HOST_NAME},
            {IB_FIELD_ENUM_V2_FIELD_SERVER_IP_ADDRESS, IB_FIELD_ENUM_V1_FIELD_SERVER_IP_ADDRESS},
            {IB_FIELD_ENUM_V2_FIELD_NETWORK_VIEW, IB_FIELD_ENUM_V1_FIELD_NETWORK_VIEW},
            {IB_FIELD_ENUM_V2_FIELD_STARTS, IB_FIELD_ENUM_V1_FIELD_STARTS},
            {IB_FIELD_ENUM_V2_FIELD_TIMESTAMP, IB_FIELD_ENUM_V1_FIELD_TIMESTAMP},
            {IB_FIELD_ENUM_V2_FIELD_USERNAME, IB_FIELD_ENUM_V1_FIELD_USERNAME},
            {IB_FIELD_ENUM_V2_FIELD_V6_DUID, -1}, //No mapping
            {IB_FIELD_ENUM_V2_FIELD_V6_IAID, -1}, //No mapping
            {IB_FIELD_ENUM_V2_FIELD_V6_PREFIX_BITS, -1}, //No mapping
            {IB_FIELD_ENUM_V2_FIELD_V6_PREFERRED_LIFETIME, -1}, //No mapping
            {IB_FIELD_ENUM_V2_FIELD_SLOG_LEVEL, IB_FIELD_ENUM_V1_FIELD_SLOG_LEVEL},
            {IB_FIELD_ENUM_V2_FIELD_SLOG_DAEMON, IB_FIELD_ENUM_V1_FIELD_SLOG_DAEMON},
            {IB_FIELD_ENUM_V2_FIELD_SLOG_MESSAGE, IB_FIELD_ENUM_V1_FIELD_SLOG_MESSAGE},
            {IB_FIELD_ENUM_V2_FIELD_SLOG_FACILITY, IB_FIELD_ENUM_V1_FIELD_SLOG_FACILITY},
            {IB_FIELD_ENUM_V2_FIELD_TYPE, IB_FIELD_ENUM_V1_FIELD_TYPE},
            {IB_FIELD_ENUM_V2_FIELD_FUNCTION_TYPE, IB_FIELD_ENUM_V1_FIELD_FUNCTION_TYPE},
            {IB_FIELD_ENUM_V2_FIELD_CLIENT, IB_FIELD_ENUM_V1_FIELD_CLIENT},
            {IB_FIELD_ENUM_V2_FIELD_SOURCE, IB_FIELD_ENUM_V1_FIELD_SOURCE},
            {IB_FIELD_ENUM_V2_FIELD_CONTAINER, IB_FIELD_ENUM_V1_FIELD_CONTAINER},
            {IB_FIELD_ENUM_V2_FIELD_FINGERPRINT, -1}, //No mapping
            {IB_FIELD_ENUM_V2_FIELD_IS_INVALID_MAC, -1}, //No mapping
            {IB_FIELD_ENUM_V2_FIELD_RESERVED, IB_FIELD_ENUM_V1_FIELD_RESERVED},
    };

    int entry_num = sizeof(mapping) / sizeof(ib_field_enum_v2_v1_mapping_t);
    int i;

    for (i = 0; i < entry_num; i++)
    {
        if ( mapping[i].v2_enum_value == v2_enum_value)
        {
            if (mapping[i].v1_enum_value == -1)
            {
                return FALSE;
            }
            *v1_enum_value = mapping[i].v1_enum_value;
            return TRUE;
        }
    }
    return FALSE;
}

/* Update an ib_search_request_t structure to apply the changes made in the
 * version 2 of the ib_field_enum_t enumeration.
 * The a_supported parameter will be set to FALSE if the update is not
 * supported.
 */
static ib_return_t
tr_ib_search_request_field_enum_v2_v1(const ib_search_request_v10_t* a_in_request, ib_search_request_v10_t* a_out_request, ib_bool_t* a_supported)
{
    IENTER;

    ITEST(a_in_request, IERR_ARG);
    ITEST(a_out_request, IERR_ARG);
    ITEST(a_supported, IERR_ARG);

    int i;

    *a_supported = ib_ib_field_enum_t_v2_v1_mapping((int)a_in_request->m_sort_field, (int *)&a_out_request->m_sort_field);
    if (*a_supported == FALSE)
    {
        goto cleanup;
    }
    for (i = 0; i < a_in_request->m_num_filter_fields; i++)
    {
        *a_supported = ib_ib_field_enum_t_v2_v1_mapping((int)a_in_request->m_filter_fields[i].m_field_enum, (int *)&a_out_request->m_filter_fields[i].m_field_enum);
        if (*a_supported == FALSE)
        {
            goto cleanup;
        }
    }
    for (i = 0; i < a_in_request->m_num_goto_fields; i++)
    {
        *a_supported = ib_ib_field_enum_t_v2_v1_mapping((int)a_in_request->m_goto_fields[i].m_field_enum, (int *)&a_out_request->m_goto_fields[i].m_field_enum);
        if (*a_supported == FALSE)
        {
            goto cleanup;
        }
    }

cleanup:
    ILEAVE;
}
/* Update an ib_search_request_t structure to apply the changes made in the
 * version 13 of the ib_search_field_t structure of the m_value field size support.
 */
static ib_return_t
tr_ib_search_request_field_m_value(ib_search_request_v13_t* input, ib_search_request_v10_t* output, ib_bool_t* a_supported)
{
    IENTER;
    ITEST(input, IERR_ARG);
    ITEST(output, IERR_ARG);
    int i;

    memcpy(&output->m_dest_file_name, &input->m_dest_file_name, sizeof(output->m_dest_file_name));
    memcpy(&output->m_src_file_name, &input->m_src_file_name, sizeof(output->m_src_file_name));
    output->m_limit=input->m_limit;
    output->m_sort_field=input->m_sort_field;
    output->m_sort_ascending=input->m_sort_ascending;
    output->m_paging_op=input->m_paging_op;
    output->m_desired_goto_context=input->m_desired_goto_context;
    output->m_num_filter_fields=input->m_num_filter_fields;
    output->m_num_goto_fields=input->m_num_goto_fields;
    for (i = 0; i < input->m_num_filter_fields; i++)
    {
	memcpy(&output->m_filter_fields[i], (ib_search_request_v10_t *)&input->m_filter_fields[i],sizeof(output->m_filter_fields[i]));
    }
    for (i = 0; i < input->m_num_goto_fields; i++)
    {
	memcpy(&output->m_goto_fields[i], (ib_search_request_v10_t *)&input->m_goto_fields[i],sizeof(output->m_goto_fields[i]));
    }

    *a_supported = ib_ib_field_enum_t_v2_v1_mapping((int)input->m_sort_field, (int *)&output->m_sort_field);
    if (*a_supported == FALSE)
    {
        goto cleanup;
    }
    for (i = 0; i < input->m_num_filter_fields; i++)
    {
        *a_supported = ib_ib_field_enum_t_v2_v1_mapping((int)input->m_filter_fields[i].m_field_enum, (int *)&output->m_filter_fields[i].m_field_enum);
        if (*a_supported == FALSE)
        {
            goto cleanup;
        }
    }
    for (i = 0; i < input->m_num_goto_fields; i++)
    {
        *a_supported = ib_ib_field_enum_t_v2_v1_mapping((int)input->m_goto_fields[i].m_field_enum, (int *)&output->m_goto_fields[i].m_field_enum);
        if (*a_supported == FALSE)
        {
            goto cleanup;
        }
    }


cleanup:
    ILEAVE;
}

/* Translate dhcp_lease_history_search_request_v13_t structure to dhcp_lease_history_search_request_v12_t */
static ib_return_t
tr_dhcp_lease_history_search_request_v13_v12(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(dhcp_lease_history_search_request_v13_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);
    ib_bool_t supported = TRUE;
    int i;


    size_t out_len =  sizeof(dhcp_lease_history_search_request_v12_t);

    const dhcp_lease_history_search_request_v13_t * input = (const dhcp_lease_history_search_request_v13_t * )a_input;
    dhcp_lease_history_search_request_v12_t * output;

   for (i = 0; i < (&input->m_search_request)->m_num_filter_fields; i++)
    {
        if(strlen((&input->m_search_request)->m_filter_fields[i].m_value)  >= MAX_SEARCH_VALUE_SZ)
        {
       	    *a_result = TRANSLATION_RESULT_UNSUPPORTED;
            goto cleanup;
        }
    }
    for (i = 0; i < (&input->m_search_request)->m_num_goto_fields; i++)
    {
        if(strlen((&input->m_search_request)->m_goto_fields[i].m_value)  >= MAX_SEARCH_VALUE_SZ)
        {
            *a_result = TRANSLATION_RESULT_UNSUPPORTED;
            goto cleanup;
        }
    }

    ITEST(sizeof(dhcp_lease_history_search_request_v13_t) != sizeof(dhcp_lease_history_search_request_v12_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    memcpy(output->m_entity, input->m_entity, sizeof(output->m_entity));
    memcpy(output->m_start_time, input->m_start_time, sizeof(output->m_start_time));
    memcpy(output->m_end_time, input->m_end_time, sizeof(output->m_end_time));
    memcpy(output->m_prev_obj_id, input->m_prev_obj_id, sizeof(output->m_prev_obj_id));
    ICALL(tr_ib_search_request_field_m_value((ib_search_request_v13_t *)&input->m_search_request, (ib_search_request_v10_t *)&output->m_search_request, &supported));
    if (supported == FALSE)
    {
      *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
      *a_result = TRANSLATION_RESULT_SUCCESS;
      *a_output = output;
      *a_output_len = out_len;
    }
cleanup:
    ILEAVE;

}

/* Translate dhcp_lease_history_search_request_v12_t structure to dhcp_lease_history_search_request_v11_t */
static ib_return_t
tr_dhcp_lease_history_search_request_v12_v11(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(dhcp_lease_history_search_request_v12_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(dhcp_lease_history_search_request_v11_t);

    MEMPOOL_MEMDUP(a_mempool, a_input, out_len, a_output);

    *a_output_len = out_len;
    *a_result = TRANSLATION_RESULT_SUCCESS;

cleanup:
    ILEAVE;
}

/* Translate dhcp_lease_history_search_request_v11_t structure to dhcp_lease_history_search_request_v10_t */
static ib_return_t
tr_dhcp_lease_history_search_request_v11_v10(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(dhcp_lease_history_search_request_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    ib_bool_t supported = TRUE;
    size_t out_len =  sizeof(dhcp_lease_history_search_request_v10_t);

    const dhcp_lease_history_search_request_v11_t * input = (const dhcp_lease_history_search_request_v11_t * )a_input;
    dhcp_lease_history_search_request_v10_t * output;

    ITEST(sizeof(dhcp_lease_history_search_request_v11_t) == sizeof(dhcp_lease_history_search_request_v10_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    *output = *(dhcp_lease_history_search_request_v10_t *)input;
    ICALL(tr_ib_search_request_field_enum_v2_v1(&input->m_search_request, &output->m_search_request, &supported));
    if (supported == FALSE)
    {
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        *a_result = TRANSLATION_RESULT_SUCCESS;
        *a_output = output;
        *a_output_len = out_len;
    }

cleanup:
    ILEAVE;
}
/* Translate one_file_search_request_v13_t structure to one_file_search_request_v12_t */
static ib_return_t
tr_one_file_search_request_v13_v12(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_file_search_request_v13_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);
    int i;

    ib_bool_t supported = TRUE;
    size_t out_len = sizeof(one_file_search_request_v12_t);

    const one_file_search_request_v13_t * input = (const one_file_search_request_v13_t * )a_input;
    one_file_search_request_v12_t * output;
    for (i = 0; i < ((&input->m_search_request)->m_num_filter_fields); i++)
    {
        if(strlen((&input->m_search_request)->m_filter_fields[i].m_value)  >= MAX_SEARCH_VALUE_SZ)
        {
            *a_result = TRANSLATION_RESULT_UNSUPPORTED;
            goto cleanup;
        }
    }
    ITEST(sizeof(one_file_search_request_v13_t) != sizeof(one_file_search_request_v12_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    output->m_start_offset=input->m_start_offset;
    memcpy(output->m_start_time, input->m_start_time, sizeof(output->m_start_time));
    memcpy(output->m_end_time, input->m_end_time, sizeof(output->m_end_time));
    output->m_file_selection=input->m_file_selection;
    ICALL(tr_ib_search_request_field_m_value((ib_search_request_v13_t *)&input->m_search_request, (ib_search_request_v10_t *)&output->m_search_request, &supported));
    if (supported == FALSE)
    {
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        *a_result = TRANSLATION_RESULT_SUCCESS;
        *a_output = output;
        *a_output_len = out_len;
    }
cleanup:
    ILEAVE;
}

/* Translate one_file_search_request_v11_t structure to one_file_search_request_v10_t */
static ib_return_t
tr_one_file_search_request_v11_v10(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_file_search_request_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    ib_bool_t supported = TRUE;
    size_t out_len = sizeof(one_file_search_request_v10_t);

    const one_file_search_request_v11_t * input = (const one_file_search_request_v11_t * )a_input;
    one_file_search_request_v10_t * output;

    ITEST(sizeof(one_file_search_request_v11_t) == sizeof(one_file_search_request_v10_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    *output = *(one_file_search_request_v10_t *)input;
    ICALL(tr_ib_search_request_field_enum_v2_v1(&input->m_search_request, &output->m_search_request, &supported));
    if (supported == FALSE)
    {
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        *a_result = TRANSLATION_RESULT_SUCCESS;
        *a_output = output;
        *a_output_len = out_len;
    }

cleanup:
    ILEAVE;
}

static ib_return_t
tr_DM_ONE_BLOXTOOLS_SYNC_TO_MASTER_v11_v10(imempool* a_mempool,
        const void* a_input, size_t a_input_len,
        void** a_output, size_t* a_output_len,
        translation_result_t* a_result,
        void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input == NULL, IERR_ARG);
    ITEST(a_input_len == 0, IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    /*
     * There is a constant conflict with 0x00000038 between NIOS 5.x and NIOS 6.x.
     * To avoid sending this conflicting value from NIOS 6.x to NIOS 5.x accidentally,
     * we intentionally increase its version number  from 10, which is used by NIOS 5.x,
     * to 11 in NIOS 6.x and return translation from 11 to V10 is unsupported.
     */
    *a_result = TRANSLATION_RESULT_UNSUPPORTED;

cleanup:
        ILEAVE;
}

static ib_return_t
tr_one_request_file_status_v11_v10(imempool* a_mempool,
        const void* a_input, size_t a_input_len,
        void** a_output, size_t* a_output_len,
        translation_result_t* a_result,
        void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_request_file_status_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(one_request_file_status_v10_t);

    const one_request_file_status_v11_t * input = (const one_request_file_status_v11_t * )a_input;
    one_request_file_status_v10_t * output;

    /*
     * time_t has different size definition in 32 bit and 64 bit machine.
     */
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    output->m_size_left = input->m_size_left;
    output->m_percentile = input->m_percentile;
    output->m_timestamp = (ib_int32_t)input->m_timestamp; /* convert from 64 bit to 32 bit */
    output->m_file_exists = input->m_file_exists;
    memcpy(output->m_file_name, input->m_file_name, sizeof(char) * MAX_FILE_NAME_SZ);
    memcpy(output->m_file_md5_sum, input->m_file_md5_sum, sizeof(char) * IUTIL_MD5_DIGEST_STRING_LENGTH);
    output->m_reboot_time = input->m_reboot_time;

    *a_result = TRANSLATION_RESULT_SUCCESS;
    *a_output = output;
    *a_output_len = out_len;

cleanup:
        ILEAVE;
}

static ib_return_t
tr_one_capture_traffic_status_v11_v10(imempool* a_mempool,
        const void* a_input, size_t a_input_len,
        void** a_output, size_t* a_output_len,
        translation_result_t* a_result,
        void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_capture_traffic_status_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(one_capture_traffic_status_v10_t);

    const one_capture_traffic_status_v11_t * input = (const one_capture_traffic_status_v11_t * )a_input;
    one_capture_traffic_status_v10_t * output;

    /*
     * time_t has different size definition in 32 bit and 64 bit machine.
     */
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    output->m_status = input->m_status;
    output->m_size = input->m_size;
    output->m_timestamp = (ib_int32_t)input->m_timestamp; /* convert from 64 bit to 32 bit */
    output->m_file_exists = input->m_file_exists;

    *a_result = TRANSLATION_RESULT_SUCCESS;
    *a_output = output;
    *a_output_len = out_len;

cleanup:
        ILEAVE;
}

static ib_return_t
tr_one_capture_traffic_status_v12_v11(imempool* a_mempool,
        const void* a_input, size_t a_input_len,
        void** a_output, size_t* a_output_len,
        translation_result_t* a_result,
        void* a_user_data)
{
    IENTER;
    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_capture_traffic_status_v12_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(one_capture_traffic_status_v11_t);

    const one_capture_traffic_status_v12_t * input = (const one_capture_traffic_status_v12_t * )a_input;
    one_capture_traffic_status_v11_t * output;

    /*
     * Removed file_transfer, vlan_id, interface, backward translation.
     */
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    output->m_status = input->m_status;
    output->m_size = input->m_size;
    output->m_timestamp = input->m_timestamp; 
    output->m_file_exists = input->m_file_exists;

    *a_result = TRANSLATION_RESULT_SUCCESS;
    *a_output = output;
    *a_output_len = out_len;

cleanup:
        ILEAVE;
}

/*one_restore_req_t from 11 to 12*/
static ib_return_t
tr_dm_one_restore_req_v12_v11(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_restore_req_v12_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(one_restore_req_v11_t);

    const one_restore_req_v12_t * input = (const one_restore_req_v12_t * )a_input;
    one_restore_req_v11_t * output;
    size_t output_len = sizeof(one_restore_req_v11_t);
    ITEST(sizeof(one_restore_req_v12_t) != sizeof(one_restore_req_v11_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, output_len, &output);
    memcpy(output->m_dir, input->m_dir, sizeof(output->m_dir));
    output->m_nios_data=input->m_nios_data;
    output->m_discovery_data=input->m_discovery_data;
    *a_output_len = out_len;
    *a_output = output;
    *a_result = TRANSLATION_RESULT_SUCCESS;
cleanup:
        ILEAVE;
}
static ib_return_t
tr_one_capture_traffic_status_v10_v11(imempool* a_mempool,
        const void* a_input, size_t a_input_len,
        void** a_output, size_t* a_output_len,
        translation_result_t* a_result,
        void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_capture_traffic_status_v10_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(one_capture_traffic_status_v11_t);

    const one_capture_traffic_status_v10_t * input = (const one_capture_traffic_status_v10_t * )a_input;
    one_capture_traffic_status_v11_t * output;

    /*
     * time_t has different size definition in 32 bit and 64 bit machine.
     */
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    output->m_status = input->m_status;
    output->m_size = input->m_size;
    output->m_timestamp = (time_t)input->m_timestamp; /* convert from 32 bit to 64 bit */
    output->m_file_exists = input->m_file_exists;

    *a_result = TRANSLATION_RESULT_SUCCESS;
    *a_output = output;
    *a_output_len = out_len;

cleanup:
        ILEAVE;
}


static ib_return_t
tr_one_capture_traffic_status_v11_v12(imempool* a_mempool,
        const void* a_input, size_t a_input_len,
        void** a_output, size_t* a_output_len,
        translation_result_t* a_result,
        void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_capture_traffic_status_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(one_capture_traffic_status_v12_t);

    const one_capture_traffic_status_v11_t * input = (const one_capture_traffic_status_v11_t * )a_input;
    one_capture_traffic_status_v12_t * output;

    /*
     * Added three new fields m_file_transfer, m_interface, m_vlan_id.
     */
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    output->m_status = input->m_status;
    output->m_size = input->m_size;
    output->m_timestamp = input->m_timestamp; 
    output->m_file_exists = input->m_file_exists;
    strlcpy (output->m_file_transfer,"UNKNOWN",sizeof(output->m_file_transfer));
    strlcpy (output->m_interface,"ALL",sizeof(output->m_interface));
    strlcpy (output->m_vlan_id,"",sizeof(output->m_vlan_id));

    *a_result = TRANSLATION_RESULT_SUCCESS;
    *a_output = output;
    *a_output_len = out_len;

cleanup:
        ILEAVE;
}

static ib_return_t
tr_one_traffic_capature_parameter_v11_v10(imempool* a_mempool,
        const void* a_input, size_t a_input_len,
        void** a_output, size_t* a_output_len,
        translation_result_t* a_result,
        void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_traffic_capature_parameter_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(one_traffic_capature_parameter_v10_t);

    const one_traffic_capature_parameter_v11_t * input = (const one_traffic_capature_parameter_v11_t * )a_input;
    one_traffic_capature_parameter_v10_t * output;

    ITEST(sizeof(one_traffic_capature_parameter_v11_t) != sizeof(one_traffic_capature_parameter_v10_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    output->m_traffic_type = input->m_traffic_type;
    output->m_time = input->m_time;
    output->m_free_space = input->m_free_space;
    output->m_error_code = input->m_error_code;

    *a_result = TRANSLATION_RESULT_SUCCESS;
    *a_output = output;
    *a_output_len = out_len;

cleanup:
        ILEAVE;
}

static ib_return_t
tr_test_ms_server_request_v3_v2(imempool* a_mempool,
        const void* a_input, size_t a_input_len,
        void** a_output, size_t* a_output_len,
        translation_result_t* a_result,
        void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(test_ms_server_request_v3_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(test_ms_server_request_v2_t);

    const test_ms_server_request_v3_t* input = (const test_ms_server_request_v3_t* )a_input;
    test_ms_server_request_v2_t* output;

    if (input->m_mode & CHECK_MS_AD_SITES)
    {
        *a_result = TRANSLATION_RESULT_UNSUPPORTED;
    }
    else
    {
        MEMPOOL_CALLOC(a_mempool, out_len, &output);
        memcpy(output->m_server, input->m_server, sizeof(output->m_server));
        memcpy(output->m_username, input->m_username, sizeof(output->m_username));
        memcpy(output->m_password, input->m_password, sizeof(output->m_password));
        memcpy(output->m_msoid, input->m_msoid, sizeof(output->m_msoid));
        output->m_mode = input->m_mode;
        *a_result = TRANSLATION_RESULT_SUCCESS;
        *a_output = output;
        *a_output_len = out_len;
    }

cleanup:
        ILEAVE;
}


static ib_return_t
tr_one_dup_ipv6_addr_rep_v10_v11(imempool* a_mempool,
        const void* a_input, size_t a_input_len,
        void** a_output, size_t* a_output_len,
        translation_result_t* a_result,
        void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_dup_ipv6_addr_rep_v10_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(one_dup_ipv6_addr_rep_v11_t);

    const one_dup_ipv6_addr_rep_v10_t * input = (const one_dup_ipv6_addr_rep_v10_t * )a_input;
    one_dup_ipv6_addr_rep_v11_t * output;

    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    output->m_error_case = (ib_uint16_t) (input->m_error_case ? 2 : 0); /* convert boolean to int (any number greater than 1) */
    memcpy(output->m_err_string, input->m_err_string, sizeof(output->m_err_string));

    *a_result = TRANSLATION_RESULT_SUCCESS;
    *a_output = output;
    *a_output_len = out_len;

cleanup:
        ILEAVE;
}
/* Translate dns_clear_sec_db_v11_t structure to dns_clear_sec_db_v10_t */
static
ib_return_t tr_dns_clear_sec_db_v11_v10(imempool* a_mempool,
        const void* a_input, size_t a_input_len,
        void** a_output, size_t* a_output_len,
        translation_result_t* a_result,
        void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(dns_clear_sec_db_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    const dns_clear_sec_db_v11_t *input = (const dns_clear_sec_db_v11_t *) a_input;
    dns_clear_sec_db_v10_t *output = NULL;
    size_t out_len = sizeof(dns_clear_sec_db_v10_t);

    MEMPOOL_CALLOC(a_mempool, out_len, &output);
    memcpy(output->m_zone, input->m_zone, sizeof(output->m_zone));
    output->m_remove_subzones = (input->m_remove_subzones == DNS_CLEAR_SEC_DB_SUBZONE_ALL);

    *a_result = TRANSLATION_RESULT_SUCCESS;
    *a_output = output;
    *a_output_len = out_len;

cleanup:
        ILEAVE;
}

/* Translate one_upload_file_v11_t structure to one_upload_file_v10_t */
static ib_return_t
tr_one_upload_file_v11_v10(imempool* a_mempool,
                                    const void* a_input, size_t a_input_len,
                                    void** a_output, size_t* a_output_len,
                                    translation_result_t* a_result, void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_upload_file_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    const one_upload_file_v11_t * input = (const one_upload_file_v11_t * )a_input;

    one_upload_file_v10_t * output = NULL;
    size_t output_len = sizeof(one_upload_file_v10_t);

    MEMPOOL_CALLOC(a_mempool, output_len, &output);
    memcpy(output->m_directory, input->m_directory, sizeof(output->m_directory));
    memcpy(output->m_parent_dir, input->m_parent_dir, sizeof(output->m_parent_dir));

    *a_output_len = output_len;
    *a_output = output;
    *a_result = TRANSLATION_RESULT_SUCCESS;

cleanup:
    ILEAVE;
}

/* one_backup_req_t from 11 to 12 */
static ib_return_t
tr_dm_one_backup_req_v12_v11(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_backup_req_v12_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    size_t out_len = sizeof(one_backup_req_v11_t);

    const one_backup_req_v12_t * input = (const one_backup_req_v12_t * )a_input;
    one_backup_req_v11_t * output;
    size_t output_len = sizeof(one_backup_req_v11_t);
    ITEST(sizeof(one_backup_req_v12_t) != sizeof(one_backup_req_v11_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, output_len, &output);
    memcpy(output->m_output_dir, input->m_output_dir, sizeof(output->m_output_dir));
    output->m_nios_data=input->m_nios_data;
    output->m_discovery_data=input->m_discovery_data;

    *a_output_len = out_len;
    *a_output = output;
    *a_result = TRANSLATION_RESULT_SUCCESS;

cleanup:
        ILEAVE;
}

/* dns_clear_cache_t from 11 to 10 */
static ib_return_t
tr_dm_clear_dns_cache_req_v11_v10(imempool* a_mempool,
                                  const void* a_input, size_t a_input_len,
                                  void** a_output, size_t* a_output_len,
                                  translation_result_t* a_result,
                                  void* a_user_data)
{
  IENTER;

  ITEST(a_mempool, IERR_ARG);
  ITEST(a_input, IERR_ARG);
  ITEST(a_input_len == sizeof(dns_clear_cache_v11_t), IERR_ARG);
  ITEST(a_output, IERR_ARG);
  ITEST(a_output_len, IERR_ARG);
  ITEST(a_result, IERR_ARG);

  const dns_clear_cache_v11_t *input = (const dns_clear_cache_v11_t*)a_input;
  dns_clear_cache_v10_t *output;
  size_t output_len = sizeof(one_backup_req_v11_t);

  ITEST(a_input_len != output_len, IERR_FAILURE);

  MEMPOOL_CALLOC(a_mempool, output_len, &output);
  memcpy(output->m_domain, input->m_domain, sizeof(output->m_domain));
  memcpy(output->m_view_name, input->m_view_name, sizeof(output->m_view_name));
  *a_output_len = output_len;
  *a_output = output;
  *a_result = TRANSLATION_RESULT_SUCCESS;

cleanup:
      ILEAVE;
}

/* Translate one_email_info_v11_t structure to one_email_info_v10_t */
static ib_return_t
tr_one_email_info_v11_v10(imempool* a_mempool,
                                    const void* a_input, size_t a_input_len,
                                    void** a_output, size_t* a_output_len,
                                    translation_result_t* a_result, void* a_user_data)
{
    IENTER;
    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_email_info_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    const one_email_info_v11_t * input = (const one_email_info_v11_t * )a_input;

    one_email_info_v10_t * output = NULL;
    size_t output_len = sizeof(one_email_info_v10_t);

    MEMPOOL_CALLOC(a_mempool, output_len, &output);
    memcpy(output->m_relay_server, input->m_relay_server, sizeof(output->m_relay_server));
    memcpy(output->m_email_addr, input->m_email_addr, sizeof(output->m_email_addr));
    memcpy(output->m_unit, input->m_unit, sizeof(output->m_unit));

    *a_output_len = output_len;
    *a_output = output;
    *a_result = TRANSLATION_RESULT_SUCCESS;

cleanup:
    ILEAVE;
}

/* Translate one_email_info_v12_t structure to one_email_info_v11_t */
static ib_return_t
tr_one_email_info_v12_v11(imempool* a_mempool,
                                    const void* a_input, size_t a_input_len,
                                    void** a_output, size_t* a_output_len,
                                    translation_result_t* a_result, void* a_user_data)
{
    IENTER;
    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_email_info_v12_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    const one_email_info_v12_t * input = (const one_email_info_v12_t * )a_input;

    one_email_info_v11_t * output = NULL;
    size_t output_len = sizeof(one_email_info_v11_t);

    MEMPOOL_CALLOC(a_mempool, output_len, &output);
    memcpy(output->m_relay_server, input->m_relay_server, sizeof(output->m_relay_server));
    memcpy(output->m_email_addr, input->m_email_addr, sizeof(output->m_email_addr));
    memcpy(output->m_from_addr, input->m_from_addr, sizeof(output->m_from_addr));
    memcpy(output->m_unit, input->m_unit, sizeof(output->m_unit));

    *a_output_len = output_len;
    *a_output = output;
    *a_result = TRANSLATION_RESULT_SUCCESS;

cleanup:
    ILEAVE;
}

/* Translate one_system_stats_v11_t structure to one_system_stats_v10_t */
static ib_return_t
tr_one_system_stats_v11_v10(imempool* a_mempool,
                                     const void* a_input, size_t a_input_len,
                                     void** a_output, size_t* a_output_len,
                                     translation_result_t* a_result,
                                     void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_system_stats_v11_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    one_system_stats_v10_t * output;
    size_t output_len = sizeof(one_system_stats_v10_t);

    const one_system_stats_v11_t * input = (const one_system_stats_v11_t * )a_input;
    ITEST(sizeof(one_system_stats_v11_t) != sizeof(one_system_stats_v10_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, output_len, &output);
    output->stats_type=input->stats_type;
    memcpy(output->m_destination_file_name, input->m_destination_file_name, sizeof(output->m_destination_file_name));
    *a_output_len = output_len;
    *a_output = output;
    *a_result = TRANSLATION_RESULT_SUCCESS;

cleanup:
    ILEAVE;
}

/* Translate one_system_stats_v12_t structure to one_system_stats_v11_t */
static ib_return_t
tr_one_system_stats_v12_v11(imempool* a_mempool,
                                      const void* a_input, size_t a_input_len,
                                      void** a_output, size_t* a_output_len,
                                      translation_result_t* a_result,
                                      void* a_user_data)
{
    IENTER;

    ITEST(a_mempool, IERR_ARG);
    ITEST(a_input, IERR_ARG);
    ITEST(a_input_len == sizeof(one_system_stats_v12_t), IERR_ARG);
    ITEST(a_output, IERR_ARG);
    ITEST(a_output_len, IERR_ARG);
    ITEST(a_result, IERR_ARG);

    one_system_stats_v11_t * output;
    size_t output_len = sizeof(one_system_stats_v11_t);

    const one_system_stats_v12_t * input = (const one_system_stats_v12_t * )a_input;
    ITEST(sizeof(one_system_stats_v12_t) != sizeof(one_system_stats_v11_t), IERR_FAILURE);
    MEMPOOL_CALLOC(a_mempool, output_len, &output);
    output->stats_type=input->stats_type;
    memcpy(output->m_destination_file_name, input->m_destination_file_name, sizeof(output->m_destination_file_name));
    output->m_top_live=input->m_top_live;
    *a_output_len = output_len;
    *a_output = output;
    *a_result = TRANSLATION_RESULT_SUCCESS;

cleanup:
    ILEAVE;
}

// static ib_return_t
// tr_dns_fqdn_dig_request_v1_v2(imempool* a_mempool,
//         const void* a_input, size_t a_input_len,
//         void** a_output, size_t* a_output_len,
//         translation_result_t* a_result,
//         void* a_user_data)
// {
//     IENTER;
//     ITEST(a_mempool, IERR_ARG);
//     ITEST(a_input, IERR_ARG);
//     ITEST(a_input_len == sizeof(dns_fqdn_dig_request_v1_t), IERR_ARG);
//     ITEST(a_output, IERR_ARG);
//     ITEST(a_output_len, IERR_ARG);
//     ITEST(a_result, IERR_ARG);

//     size_t out_len = sizeof(dns_fqdn_dig_request_v2_t);

//     const dns_fqdn_dig_request_v1_t * input = (const dns_fqdn_dig_request_v1_t * )a_input;
//     dns_fqdn_dig_request_v2_t * output;

//     /*
//      * Added two new fields m_time_out and m_tries
//      */
//     MEMPOOL_CALLOC(a_mempool, out_len, &output);
//     memcpy(output->m_fqdn, input->m_fqdn, sizeof(output->m_fqdn));
//     memcpy(output->m_name_server, input->m_name_server, sizeof(output->m_name_server));
//     memcpy(output->m_record_type, input->m_record_type, sizeof(output->m_record_type));
//     memcpy(output->m_source_address, input->m_source_address, sizeof(output->m_source_address));
//     output->m_recursive_query = input->m_recursive_query;
//     output->m_time_out = 5;
//     output->m_tries = 3;

//     *a_result = TRANSLATION_RESULT_SUCCESS;
//     *a_output = output;
//     *a_output_len = out_len;

// cleanup:
//         ILEAVE;
// }



/* Register all the existing translation callback functions */
ib_return_t
dm_client_context_register_translations_cb(dm_client_context_t* a_context)
{
    IENTER;

    ITEST(a_context, IERR_ARG);

    /* Translation callbacks for queries */
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_SUPPORT_FILES_TRANSFER, 12, 11, tr_one_file_view_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_SUPPORT_FILES_TRANSFER, 11, 10, tr_one_file_view_v11_v10));
    // The following copy translations are because multiple opcodes share the same request message with DM_ONE_SUPPORT_FILES_TRANSFER.
    // The opcode versions for all of these had to be changed along with DM_ONE_SUPPORT_FILES_TRANSFER.
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_FILE_DISPLAY_REQUEST, 12, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_FILE_DISPLAY_REQUEST, 11, 10, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_DOWNLOAD_GRID_NTP_KEY_FILE, 12, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_DOWNLOAD_GRID_NTP_KEY_FILE, 11, 10, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_SUPPORT_FILES_DOWNLOAD, 12, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_SUPPORT_FILES_DOWNLOAD, 11, 10, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_MERGE_LOG_DOWNLOAD, 12, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_MERGE_LOG_DOWNLOAD, 11, 10, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_TARBALL_FILES_DOWNLOAD, 12, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_TARBALL_FILES_DOWNLOAD, 11, 10, translation_ctx_copy_cb));
    // End of shared request message.
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 16, 15, tr_one_log_file_view_v16_v15));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 15, 14, tr_one_log_file_view_v15_v14));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 14, 13, tr_one_log_file_view_v14_v13));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 13, 12, tr_one_log_file_view_v13_v12));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 12, 11, tr_one_log_file_view_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 11, 10, tr_one_log_file_view_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_STATE_CHANGE_REQUEST, 11, 10, tr_dhcp_lease_state_change_req_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_STATE_CHANGE_REQUEST, 12, 11, tr_dhcp_lease_state_change_req_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_FO_STATE_CHANGE_REQUEST, 11, 10, tr_dhcp_fo_state_change_req_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_HISTORY_DOWNLOAD, 13, 12, tr_dhcp_lease_history_search_request_v13_v12));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_HISTORY_DOWNLOAD, 12, 11, tr_dhcp_lease_history_search_request_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_HISTORY_DOWNLOAD, 11, 10, tr_dhcp_lease_history_search_request_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_HISTORY_TRANSFER, 13, 12, tr_dhcp_lease_history_search_request_v13_v12));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_HISTORY_TRANSFER, 12, 11, tr_dhcp_lease_history_search_request_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_HISTORY_TRANSFER, 11, 10, tr_dhcp_lease_history_search_request_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_HISTORY_UNPACK, 13, 12, tr_dhcp_lease_history_search_request_v13_v12));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_HISTORY_UNPACK, 12, 11, tr_dhcp_lease_history_search_request_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DHCP_LEASE_HISTORY_UNPACK, 11, 10, tr_dhcp_lease_history_search_request_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_GET_FILE_REQUEST, 13, 12, tr_one_file_search_request_v13_v12));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_GET_FILE_REQUEST, 12, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_GET_FILE_REQUEST, 11, 10, tr_one_file_search_request_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_BLOXTOOLS_SYNC_TO_MASTER, 11, 10, tr_DM_ONE_BLOXTOOLS_SYNC_TO_MASTER_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_UPGRADE_GRID, 11, 10, tr_one_request_file_status_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_DOWNGRADE_REQUEST, 11, 10, tr_one_request_file_status_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_TRAFFIC_LOG_STATUS_REQUEST, 11, 10, tr_one_capture_traffic_status_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_TRAFFIC_LOG_STATUS_REQUEST, 12, 11, tr_one_capture_traffic_status_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_TEST_DUP_IPV6_ADDR, 11, 10, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_START_TRAFFIC_CAPTURER, 11, 10, tr_one_traffic_capature_parameter_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_STOP_TRAFFIC_CAPTURER, 11, 10, tr_one_traffic_capature_parameter_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_DNS_CLEAR_SEC_DB_REQUEST, 11, 10, tr_dns_clear_sec_db_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_TEST_MS_SERVER_REQUEST, 3, 2, tr_test_ms_server_request_v3_v2));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_IMPORT_TFTP_FILE_REQUEST, 11, 10, tr_one_upload_file_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_DB_DUMP, 12, 11, tr_dm_one_backup_req_v12_v11));

    /*Added for the translation of restore_req*/
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_RESTORE_REQUEST, 12, 11, tr_dm_one_restore_req_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_FORCED_RESTORE_REQUEST, 12, 11,tr_dm_one_restore_req_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_KEEP_GM_IP_FORCED_RESTORE_REQUEST, 12, 11, tr_dm_one_restore_req_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_CLEAR_DNS_CACHE_REQUEST, 11, 10, tr_dm_clear_dns_cache_req_v11_v10));

    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_SEND_TEST_EMAIL, 12, 11, tr_one_email_info_v12_v11));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_SEND_TEST_EMAIL, 11, 10, tr_one_email_info_v11_v10));

    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_GET_SYSTEM_STATISTICS_REQUEST, 11, 10, tr_one_system_stats_v11_v10));
    ICALL(dm_client_context_add_translation_cb_query(a_context, DM_ONE_GET_SYSTEM_STATISTICS_REQUEST, 12, 11, tr_one_system_stats_v12_v11));


    /* Translation callbacks for replies */
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_FILE_DISPLAY_REQUEST, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_FILE_DISPLAY_REQUEST, 11, 12, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 11, 12, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 12, 13, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 13, 14, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 14, 15, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_LOG_FILE_DISPLAY_REQUEST, 15, 16, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_DHCP_LEASE_STATE_CHANGE_REQUEST, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_DHCP_LEASE_STATE_CHANGE_REQUEST, 11, 12, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_DHCP_FO_STATE_CHANGE_REQUEST, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_DHCP_LEASE_HISTORY_TRANSFER, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_DHCP_LEASE_HISTORY_TRANSFER, 11, 12, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_DHCP_LEASE_HISTORY_TRANSFER, 12, 13, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_GET_FILE_REQUEST, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_GET_FILE_REQUEST, 11, 12, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_GET_FILE_REQUEST, 12, 13, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_UPGRADE_GRID, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_DOWNGRADE_REQUEST, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_TRAFFIC_LOG_STATUS_REQUEST, 10, 11, tr_one_capture_traffic_status_v10_v11));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_TRAFFIC_LOG_STATUS_REQUEST, 11, 12, tr_one_capture_traffic_status_v11_v12));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_TEST_DUP_IPV6_ADDR, 10, 11, tr_one_dup_ipv6_addr_rep_v10_v11));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_START_TRAFFIC_CAPTURER, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_STOP_TRAFFIC_CAPTURER, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_DNS_CLEAR_SEC_DB_REQUEST, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_TEST_MS_SERVER_REQUEST, 2, 3, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_IMPORT_TFTP_FILE_REQUEST, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_DB_DUMP, 11, 12, translation_ctx_copy_cb));


    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_RESTORE_REQUEST, 11, 12, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_FORCED_RESTORE_REQUEST, 11, 12, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_KEEP_GM_IP_FORCED_RESTORE_REQUEST, 11, 12, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_CLEAR_DNS_CACHE_REQUEST, 10, 11, translation_ctx_copy_cb));

    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_SEND_TEST_EMAIL, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_SEND_TEST_EMAIL, 11, 12, translation_ctx_copy_cb));

    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_GET_SYSTEM_STATISTICS_REQUEST, 10, 11, translation_ctx_copy_cb));
    ICALL(dm_client_context_add_translation_cb_reply(a_context, DM_ONE_GET_SYSTEM_STATISTICS_REQUEST, 11, 12, translation_ctx_copy_cb));
cleanup:
    ILEAVE;
}
